﻿using BlueTape.Aion.DataAccess.External.Abstractions;
using BlueTape.Aion.DataAccess.External.Constants;
using BlueTape.Aion.DataAccess.External.Options;
using BueTape.Aion.Infrastructure.Extensions;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using Polly;
using Polly.Extensions.Http;
using System.Net;

namespace BlueTape.Aion.DataAccess.External.DI;

public static class DependencyRegistrar
{
    public static void AddDataAccessExternalDependencies(this IServiceCollection services, IConfiguration configuration)
    {
        services.AddSingleton<IAionMemoryCache, AionMemoryCache>();
        services.AddTransient<IAionHttpClient, AionHttpClient>();
        services.AddScoped<IAionCredentialsManager, AionCredentialsManager>();

        services.Configure<ServicesOptions>(configuration.GetSection(ServicesOptions.SectionName));

        services.AddHttpClient(ClientConstants.AionAuthHttpClientName, (httpClient) =>
            {
                httpClient.BaseAddress = new Uri(configuration["Services:AionAuthServiceApi"]
                                                 ?? throw new VariableNullException(nameof(configuration)));
                httpClient.DefaultRequestHeaders.Add("Accept", "application/json");
            })
            .SetHandlerLifetime(TimeSpan.FromMinutes(5))
            .AddPolicyHandler((serviceProvider, request) => GetAionRetryPolicy(serviceProvider));

        services.AddHttpClient(ClientConstants.AionHttpClientName, (httpClient) =>
            {
                httpClient.BaseAddress = new Uri(configuration["Services:AionServiceApi"]
                                                 ?? throw new VariableNullException(nameof(configuration)));
                httpClient.DefaultRequestHeaders.Add("Accept", "application/json");
                httpClient.Timeout = TimeSpan.FromSeconds(70); // Set default timeout to 70 seconds
            })
            .SetHandlerLifetime(TimeSpan.FromMinutes(5));
    }

    private static AsyncPolicy<HttpResponseMessage> GetAionRetryPolicy(IServiceProvider serviceProvider)
    {
        return HttpPolicyExtensions
            .HandleTransientHttpError()
            .OrResult(msg => msg.StatusCode == HttpStatusCode.InternalServerError)
            .WaitAndRetryAsync(new[]
                {
                    TimeSpan.FromMilliseconds(50),
                    TimeSpan.FromMilliseconds(500),
                    TimeSpan.FromMilliseconds(1000)
                },
                (_, timespan, retryCount, _) =>
                {
                    var logger = serviceProvider.GetRequiredService<ILogger<IAionHttpClient>>();
                    logger?.LogWarning("Polly retried after {ElapsedTime} milliseconds for the {RetryCount}-th time.",
                        timespan.Milliseconds, retryCount);
                });
    }
}