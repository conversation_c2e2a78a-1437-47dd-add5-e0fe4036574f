{"id": "33112635-dbb4-4cc8-a34d-d1b5c9610505", "name": "beta", "values": [{"key": "PaymentRequestId", "value": "bcd769df-1934-4b9b-811e-ad362f4d0bd4", "type": "default", "enabled": true}, {"key": "CommandToManualExecuting", "value": "", "type": "default", "enabled": true}, {"key": "PaymentServiceUrl", "value": "https://api-beta.bluetape.com/paymentService", "type": "default", "enabled": true}, {"key": "transaction4", "value": "", "type": "any", "enabled": true}, {"key": "transaction3", "value": "", "type": "any", "enabled": true}, {"key": "transaction2", "value": "", "type": "any", "enabled": true}, {"key": "transaction1", "value": "", "type": "any", "enabled": true}, {"key": "TransactionToUpdate", "value": "", "type": "any", "enabled": true}, {"key": "x-api-key", "value": "0bdd1966-7ef1-464f-845d-ecb963a043f1", "type": "default", "enabled": true}], "_postman_variable_scope": "environment", "_postman_exported_at": "2024-03-08T12:49:11.628Z", "_postman_exported_using": "Postman/10.23.11"}