using System.Runtime.Serialization;
using System.Text.Json.Serialization;

namespace BlueTape.Aion.DataAccess.External.Models.InstantTransfer.Response;

[DataContract]
public class CreateInstantTransferResponse : BaseAionResponseModel
{
    [JsonPropertyName("instantTransferObj")]
    public InstantTransferObjectItemResponse? InstantTransfer { get; set; }
}

[DataContract]
public class InstantTransferObjectItemResponse
{
    [JsonPropertyName("paymentId")]
    public string? PaymentId { get; set; }

    [JsonPropertyName("aionSource")]
    public string? AionSource { get; set; }

    [JsonPropertyName("fromAccountNumber")]
    public string? FromAccountNumber { get; set; }

    [JsonPropertyName("amount")]
    public decimal Amount { get; set; }

    [JsonPropertyName("feeAmount")]
    public decimal FeeAmount { get; set; }
    
    [JsonPropertyName("contextIdentifier")]
    public string ContextIdentifier { get; set; }

    [JsonPropertyName("initiatedByUserName")]
    public string? InitiatedByUserName { get; set; }

    [JsonPropertyName("senderDescription")]
    public string? SenderDescription { get; set; }

    [JsonPropertyName("receiverDescription")]
    public string? ReceiverDescription { get; set; }

    [JsonPropertyName("createdAt")]
    public DateTime CreatedAt { get; set; }

    [JsonPropertyName("updatedAt")]
    public DateTime UpdatedAt { get; set; }

    [JsonPropertyName("status")]
    public string? Status { get; set; }

    [JsonPropertyName("providerStatus")]
    public string? ProviderStatus { get; set; }

    [JsonPropertyName("statusMessage")]
    public string? StatusMessage { get; set; }

    [JsonPropertyName("userNote")]
    public string? UserNote { get; set; }

    [JsonPropertyName("transactionType")]
    public string? TransactionType { get; set; }

    [JsonPropertyName("purpose")]
    public string? Purpose { get; set; }

    [JsonPropertyName("lastModifiedAt")]
    public DateTime LastModifiedAt { get; set; }

    [JsonPropertyName("rtpPaymentInfo")]
    public RtpPaymentInfo? RtpPaymentInfo { get; set; }
}