{"version": "2.0", "extensions": {"serviceBus": {"clientRetryOptions": {"mode": "exponential", "tryTimeout": "00:01:00", "delay": "00:00:00.80", "maxDelay": "00:01:00", "maxRetries": 3}, "prefetchCount": 0, "autoCompleteMessages": true, "maxAutoLockRenewalDuration": "00:00:00", "maxConcurrentCalls": 1, "maxConcurrentSessions": 1, "maxMessageBatchSize": 10, "maxBatchWaitTime": "00:00:30", "sessionIdleTimeout": "00:01:00", "enableCrossEntityTransactions": false}}, "logging": {"applicationInsights": {"samplingSettings": {"isEnabled": true, "excludedTypes": "Request"}, "enableLiveMetricsFilters": true}}}