<%#
Copyright 2008-2013 Concur Technologies, Inc.

Licensed under the Apache License, Version 2.0 (the "License"); you may
not use this file except in compliance with the License. You may obtain
a copy of the License at

  http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
License for the specific language governing permissions and limitations
under the License.
%>
<% language_tabs = current_page.data.language_tabs || [] %>
<% page_content = yield %>
<%
  if current_page.data.includes
    current_page.data.includes.each do |include|
      page_content += partial("includes/#{include}")
    end
  end
%>

<!doctype html>
<html>
  <head>
    <meta charset="utf-8">
    <meta content="IE=edge,chrome=1" http-equiv="X-UA-Compatible">
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
    <% if current_page.data.key?('meta') %>
      <% current_page.data.meta.each do |meta| %>
        <meta
          <% meta.each do |key, value| %>
            <%= "#{key}=\"#{value}\"" %>
          <% end %>
        >
      <% end %>
    <% end %>
    <title><%= current_page.data.title || "API Documentation" %></title>

    <style media="screen">
      <%= Rouge::Themes::MonokaiSublimeSlate.render(:scope => '.highlight') %>
    </style>
    <style media="print">
      * {
        transition:none!important;
      }
      <%= Rouge::Themes::Base16::Solarized.render(:scope => '.highlight') %>
    </style>
    <%= stylesheet_link_tag :screen, media: :screen %>
    <%= stylesheet_link_tag :print, media: :print %>
    <% if current_page.data.search %>
      <%= javascript_include_tag  "all" %>
    <% else %>
      <%= javascript_include_tag  "all_nosearch" %>
    <% end %>

    <% if current_page.data.code_clipboard %>
    <script>
      $(function() { setupCodeCopy(); });
    </script>
    <% end %>

    <%= favicon_tag 'images/favicon-32x32.png' %>
  </head>

  <body class="<%= page_classes %>" data-languages="<%=h language_tabs.map{ |lang| lang.is_a?(Hash) ? lang.keys.first : lang }.to_json %>">
    <a href="#" id="nav-button">
      <span>
        NAV
        <%= image_tag('navbar.png') %>
      </span>
    </a>
    <div class="toc-wrapper">
      <%= image_tag "logo.png", class: 'logo' %>
      <% if language_tabs.any? %>
        <div class="lang-selector">
          <% language_tabs.each do |lang| %>
            <% if lang.is_a? Hash %>
              <a href="#" data-language-name="<%= lang.keys.first %>"><%= lang.values.first %></a>
            <% else %>
              <a href="#" data-language-name="<%= lang %>"><%= lang %></a>
            <% end %>
          <% end %>
        </div>
      <% end %>
      <% if current_page.data.search %>
        <div class="search">
          <input type="text" class="search" id="input-search" placeholder="Search">
        </div>
        <ul class="search-results"></ul>
      <% end %>
      <ul id="toc" class="toc-list-h1">
        <% toc_data(page_content).each do |h1| %>
          <li>
            <a href="#<%= h1[:id] %>" class="toc-h1 toc-link" data-title="<%= h1[:title] %>"><%= h1[:content] %></a>
            <% if h1[:children].length > 0 %>
              <ul class="toc-list-h2">
                <% h1[:children].each do |h2| %>
                  <li>
                    <a href="#<%= h2[:id] %>" class="toc-h2 toc-link" data-title="<%= h2[:title] %>"><%= h2[:content] %></a>
                  </li>
                <% end %>
              </ul>
            <% end %>
          </li>
        <% end %>
      </ul>
      <% if current_page.data.toc_footers %>
        <ul class="toc-footer">
          <% current_page.data.toc_footers.each do |footer| %>
            <li><%= footer %></li>
          <% end %>
        </ul>
      <% end %>
    </div>
    <div class="page-wrapper">
      <div class="dark-box"></div>
      <div class="content">
        <%= page_content %>
      </div>
      <div class="dark-box">
        <% if language_tabs.any? %>
          <div class="lang-selector">
            <% language_tabs.each do |lang| %>
              <% if lang.is_a? Hash %>
                <a href="#" data-language-name="<%= lang.keys.first %>"><%= lang.values.first %></a>
              <% else %>
                <a href="#" data-language-name="<%= lang %>"><%= lang %></a>
              <% end %>
            <% end %>
          </div>
        <% end %>
      </div>
    </div>
  </body>
</html>
