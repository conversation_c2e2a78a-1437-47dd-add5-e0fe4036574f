@startuml Virtual Card Draw Issue Process (.NET) /2

title Virtual Card Draw Issue Process (.NET) /2

participant "VCard Service" as vs #SkyBlue
database "PostgreSQL" as pgsql #Cyan
participant "CBW" as cbw #LightGray
queue "Draw Events Queue" as dq #PaleVioletRed
participant "LFS" as lfs #SkyBlue
participant "LMS" as lms #SkyBlue

autonumber

== Virtual Card Scheduled Job ==

vs -> vs : Daily schedule
vs -> pgsql : Get active cards from Db
pgsql --> vs

loop Active cards
    vs -> cbw : Get actual data from CBW
    cbw --> vs
    alt Deactivate expired but not used cards
        vs -> cbw : Deactivate expired but not used card
        vs -> pgsql : Deactivate expired but not used card
    else Issue Draw for used cards
        vs -> cbw : Deactivate used card
        vs -> pgsql : Deactivate used card, save used amount
        vs -> dq : Place event\n""Draw.Issue"" with type ""virtualCard""
        dq -> lfs : Consume events
        lfs -> lms : Create and Start draw
        note top
            **No** merchant disbursement!
        end note
    else Active but not used cards
        vs --> vs : Skip to process
    end
end

@enduml