import { IsNotEmpty, Validate } from 'class-validator'
import i18n from 'i18next'

import { BaseValidationModel } from '@/lib/class-validator/base-validation-model'
import { LessOrEqualThanMaxDate } from '@/globals/decorators'

interface IConstructorProps {
  newExpirationDate: string | null
  maxExpirationDate: string
}

export class DrawApplicationEditAuthorizationDateData extends BaseValidationModel {
  constructor({ newExpirationDate, maxExpirationDate }: IConstructorProps) {
    super()
    this.newExpirationDate = newExpirationDate ?? ''
    this.maxExpirationDate = maxExpirationDate
  }

  @Validate(LessOrEqualThanMaxDate)
  @IsNotEmpty({
    message: i18n.t(
      'drawApplicationEditAuthorizationDateForm.isNewExpirationDateNotEmpty',
      {
        ns: 'validation',
      },
    ),
  })
  newExpirationDate: string

  maxExpirationDate: string
}
