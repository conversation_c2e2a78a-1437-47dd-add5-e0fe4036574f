@startuml Paying Over Text Static Architecture
title Paying Over Text Static Architecture (component diagram)

!include <awslib/AWSCommon>

' Uncomment the following line to create simplified view
!include <awslib/AWSSimplified>

!include <awslib/Compute/Lambda>
!include <awslib/ApplicationIntegration/SQS>
!include <awslib/Database/DocumentDBwithMongoDBcompatibility>
!include <awslib/ApplicationIntegration/APIGateway>

left to right direction
skinparam responseMessageBelowArrow true

component "UserInteractionService" as uis #SkyBlue
component "Provider" as prov #LightSeaGreen
component "Templates" as templ #LightSeaGreen
component "Linqpal" as linq #SkyBlue
component "Notification\nInvoicing" as notif #SkyBlue

APIGateway(agw, "AGW\nwith Lambda auth", "")
SQS(sqs, "SQS.fifo", "")
DocumentDBwithMongoDBcompatibility(mongo, "MongoDB", "")

prov -d- uis
prov .r. agw
agw .d. sqs
sqs .l. uis
uis -l- templ
uis -d- linq
uis -d- notif
linq -r- mongo
uis -r- mongo
notif -l- mongo

legend bottom
| Color | Meaning |
|<#LightSeaGreen>| 3rd party component |
|<#SkyBlue>| 1st party backend application |
endlegend

@enduml