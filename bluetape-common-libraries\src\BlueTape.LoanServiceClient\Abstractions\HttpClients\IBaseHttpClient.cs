﻿namespace BlueTape.LoanServiceClient.Abstractions.HttpClients;

public interface IBaseHttpClient
{
    HttpClient Client { get; }
    Task<TResult?> Get<TResult>(string url, CancellationToken ct) where TResult : class;
    Task<TResult?> Post<TResult, TPayload>(string url, TPayload payload, CancellationToken ct) where TResult : class;
    Task<TResult?> Post<TResult, TPayload>(string url, TPayload payload, IReadOnlyDictionary<string, string>? headers, CancellationToken ct) where TResult : class;
    Task<TResult?> PostAsync<TResult>(string url, object? payload, CancellationToken ct) where TResult : class;
    Task<TResult?> PostAsync<TResult>(string url, object? payload, IReadOnlyDictionary<string, string>? headers, CancellationToken ct) where TResult : class;
    Task<TResult?> PatchAsync<TResult>(string url, object? payload, CancellationToken ct) where TResult : class;
    Task<TResult?> PatchAsync<TResult>(string url, object? payload, IReadOnlyDictionary<string, string>? headers, CancellationToken ct) where TResult : class;
}
