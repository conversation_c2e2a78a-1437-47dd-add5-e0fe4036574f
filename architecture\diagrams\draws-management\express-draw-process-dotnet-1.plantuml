@startuml ATC/Express Draw Issue Process (.NET) /1

title ATC/Express Draw Draw Issue Process (.NET) /1

participant "OnBoarding Service" as obs #SkyBlue
participant "LFSASJ" as lfsasj #SkyBlue
queue "Draw Events Queue" as dq #PaleVioletRed
participant "Loan Flow Service" as lfs #SkyBlue
participant "1st parties" as other #LightGrey
participant "LMS" as lms #SkyBlue

autonumber

== Approve draw ==

obs --> obs : Draw approved
obs -> dq : Place event\n""Draw.Approved""\nwith type ""express""
dq -> lfs : Consume draw events
lfs -> other : Get necessary details
other --> lfs
lfs --> lfs : Validates request
lfs -> lms : Create draw + authorization
lms --> lms : Authorization\nperiod starts

@enduml