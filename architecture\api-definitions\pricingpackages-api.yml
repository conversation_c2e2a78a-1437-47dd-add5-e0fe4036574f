openapi: "3.0.0"
info:
  version: "0.0.1"
  title: "Pricing Packages API"
  description: |
    API definition of Merchant, Supplier Pricing Packages
servers:
  - url: TBD-Dev
    description: Development server
  - url: TBD-Prod
    description: Production server
paths:
  /pricingpackages:
    get:
      tags:
        - pricingPackages
      summary: Gets all pricing packages
      description: Gets all pricing packages
      operationId: getPricingPackages
      parameters: 
        - name: id
          description: The id of package
          in: query
          required: false
          schema:
            type: string
        - name: legacyId
          description: The legacy id of package
          in: query
          required: false
          schema:
            type: string
      responses:
        200:
          description: The packages
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/PricingPackage'
        404:
          description: Not found
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
  /pricingpackages/{id}:
    get:
      tags:
        - pricingPackages
      summary: Gets pricing package by id (for compatibility)
      description: Gets pricing package by id (for compatibility)
      operationId: getPricingPackageById
      parameters: 
        - name: id
          description: The id of package
          in: path
          required: true
          schema:
            type: string
      responses:
        200:
          description: The template
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/PricingPackage'
        404:
          description: Not found
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
  /pricingpackages/legacy/{id}:
    get:
      tags:
        - pricingPackages
      summary: Gets pricing package by legacy id (for compatibility)
      description: Gets pricing package by legacy id (for compatibility)
      operationId: getPricingPackageByLegacyId
      parameters: 
        - name: id
          description: The legacy id of package
          in: path
          required: true
          schema:
            type: string
      responses:
        200:
          description: The package
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/PricingPackage'
        404:
          description: Not found
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
  /admin/pricingpackages:
    post:
      tags:
        - pricingPackages Admin
      summary: Adds a new pricing package
      description: Adds a new pricing package
      operationId: createPricingPackage
      parameters:
        - name: userId
          description: The user added this package
          in: header
          required: true
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/CreateOrPatchPricingPackage"
      responses:
        201:
          description: The created package
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/PricingPackage'
        400:
          description: Bad request
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
  /admin/pricingpackages/{id}:
    patch:
      tags:
        - pricingPackages Admin
      summary: Updates pricing package (admin function)
      description: Updates pricing package (admin function) All fields are optional. Which field is sent, that field to update.
      operationId: updatePricingPackageById
      parameters:
        - name: id
          description: The id of package
          in: path
          required: true
          schema:
            type: string
        - name: userId
          description: Identifier of the user who updated the package
          example: 62c5e900cd65d31d3f25c34c
          in: header
          required: true
          schema:
            type: string
            nullable: false
      requestBody:
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/CreateOrPatchPricingPackage"
      responses:
        200:
          description: The patched package
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/PricingPackage'
        400:
          description: Bad request
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        404:
          description: Not found
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
components:
  schemas:
    ErrorResponse:
      type: object
      required:
        - statusCode
        - message
        - errorDescription
      properties:
        statusCode:
          description: HTTP status code corresponding to the error.
          type: integer
          example: 400
        message:
          description: A human readable error message.
          type: string
          example: Invalid model.
        errorDescription:
          description: Detailed information.
          type: string
    PricingPackage:
      allOf:
      - type: object
        properties:
          id:
            type: string
            example: bdafd605-fb56-4d32-9c9b-3b58c174dd79
          createdAt:
            type: string
            format: date-time
          createdBy:
            type: string
          updatedAt:
            type: string
            format: date-time
          updatedBy:
            type: string
      - $ref: "#/components/schemas/CreateOrPatchPricingPackage"
    CreateOrPatchPricingPackage:
      type: object
      properties:
        legacyId:
          type: string
        name:
          type: string
        title:
          type: string
        description:
          type: string
        status:
          type: string
          enum:
            - active
            - inactive
          example: active
          default: active
        isIndividual:
          type: boolean
        order:
          type: number
          format: int32
        merchant:
          type: number
        maxAmountReceived:
          type: number
        advanceRate:
          type: number
        finalPayment:
          type: number
        merchantRebate:
          type: number
        merchantFeeAfterRebate:
          type: number
        maxAmountReceivedAfterRebate:
          type: number
        customerFees30:
          type: number
        customerFees6090:
          type: string
  securitySchemes:
    ApiKey:
      type: apiKey
      in: header
      name: X-Api-Key
security:
  - ApiKey: []
