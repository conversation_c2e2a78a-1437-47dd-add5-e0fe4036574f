openapi: '3.0.0'
info:
  version: '1.0.0'
  title: 'Aion Integration API'
servers:
- url: https://bluetape.com
  description: TBD
paths:
  /api/ach/pull:
    post:
      tags:
        - ACH
      summary: Creates an ACH PULL transaction in Aion's system.
      description: Creates an ACH PULL transaction in Aion's system.
      operationId: createAchPull
      requestBody:
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/CreateAchPullRequest"
      responses: 
        200:
          description: Ach pull created
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/CreateAchPullResponse'
        400:
          description: Bad request
          content: 
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/ErrorItem'
  /api/ach/push:
    post:
      tags:
        - ACH
      summary: Creates an ACH PUSH transaction in Aion's system.
      description: Creates an ACH PUSH transaction in Aion's system.
      operationId: createAchPush
      requestBody:
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/CreateAchPushRequest"
      responses: 
        200:
          description: Ach pull created
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/CreateAchPushResponse'
        400:
          description: Bad request
          content: 
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/ErrorItem'
  /report:
    post:
      tags:
        - Report
      summary: Creates a report of transaction statuses.
      description: Creates a report of transaction statuses.
      operationId: createReport
      requestBody:
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/EmptyBody"
      responses: 
        200:
          description: The report was created.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/EmptyBody'
        400:
          description: Bad request
          content: 
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/ErrorItem'
  /api/internal:
    post:
      tags:
        - Internal
      summary: Creates an internal transfer between accounts.
      description: Creates an internal transfer between accounts.
      operationId: createInternalTransfer
      requestBody:
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/CreateInternalTransferRequest"
      responses: 
        200:
          description: The internal transfer was created.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/CreateInternalTransferResponse'
        400:
          description: Bad request
          content: 
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/ErrorItem'
components:
  schemas:
    CreateAchPullRequest:
      type: object
      required:
        - receiver
        - amount
      properties:
        receiver:
          $ref: '#/components/schemas/ReceiverDetails'
          description: The receiver of the transaction request. In a pull scenario means debtor.
        amount:
          type: number
          example: '5.00'
          format: decimal
          description: Amount of transaction in USD (no currency field)
          pattern: '[0-9]*\.[0-9]{2}'
          multipleOf: 0.01
        description:
          type: string
          description: A meaningful description, max. 10 chars.
          maxLength: 10
        addenda:
          type: array
          items:
            type: string
            maxLength: 80
          description: Array of notes, max. 80 chars (?)
    CreateAchPushRequest:
      type: object
      required:
        - receiver
        - amount
      properties:
        receiver:
          $ref: '#/components/schemas/ReceiverDetails'
          description: The receiver of the transaction request. In a pull scenario means debtor.
        amount:
          type: number
          example: '5.00'
          format: decimal
          description: Amount of transaction in USD (no currency field)
          pattern: '[0-9]*\.[0-9]{2}'
          multipleOf: 0.01
        description:
          type: string
          description: A meaningful description, max. 10 chars.
          maxLength: 10
        addenda:
          type: array
          items:
            type: string
            maxLength: 80
          description: Array of notes, max. 80 chars (?)
    ReceiverDetails:
      type: object
      required:
        - companyId
        - bankAccountId
      properties:
        companyId:
          type: string
          description: The company id
        bankAccountId:
          type: string
          description: The bank account id
    AccountDetails:
      type: object
      required: 
        - accountCode
      properties:
        accountCode:
          type: string
          enum:
            - GL
            - FBO
            - SERVICE
            - REVENUE
            - OP
            - FUNDING
            - TABAPAY
            - COLLECTION
        name:
          type: string
        description:
          type: string
          maxLength: 30
    CreateAchPullResponse:
      type: object
      properties:
        responseMessage:
          type: string
        result:
          type: boolean
        achObj:
          $ref: "#/components/schemas/Ach"
    CreateAchPushResponse:
      type: object
      properties:
        responseMessage:
          type: string
        result:
          type: boolean
        achObj:
          $ref: "#/components/schemas/Ach"
    Ach:
      type: object
      properties:
        id:
          type: string
        accountId:
          type: string
        amount:
          type: string
        counterpartyId:
          type: string
        description:
          type: string
        direction:
          type: string
        error:
          type: string
        secCode:
          type: string
        status:
          type: string
          enum:
            - created
            - pending
            - processing
            - sent
            - error
            - canceled
        counterpartyName:
          type: string
        counterpartyType:
          type: string
        email:
          type: boolean
        userNote:
          type: string
        sendEmail:
          type: boolean
        effectiveDate:
          type: string
        transferMethodId:
          type: string
        initiatedBy:
          type: string
        contextIdentifier:
          type: string
        addenda:
          type: array
          items:
            type: string
        createdAt:
          type: string
          format: date
        updatedAt:
          type: string
          format: date
        originator:
          $ref: "#/components/schemas/ParticipantAccount"
        receiver:
          $ref: "#/components/schemas/ParticipantAccount"
        referenceId:
          type: string
          description: Aion's internal id.
        paymentType:
          type: string
        postingCode:
          type: string
        posting:
          type: string
        reasonCode:
          type: string
        reasonData:
          type: string
        traceNumber:
          type: string
          description: External reference, counterparty can see it.
        transactionType:
          type: string
        serviceType:
          type: string
        wasReturned:
          type: boolean
        wasCorrected:
          type: boolean
        canceledAt:
          type: string
          format: date
        processedAt:
          type: string
          format: date
        completedAt:
          type: string
          format: date
        postedAt:
          type: string
          format: date
        rejectedAt:
          type: string
          format: date
        original:
          $ref: "#/components/schemas/Payment"
        previous:
          $ref: "#/components/schemas/Payment"
        accountNumber:
          type: string
    ParticipantAccount:
      type: object
      properties:
          accountNumber:
            type: string
          routingNumber:
            type: string
          name:
            type: string
          accountType:
            type: string
          identification:
            type: string
    Payment:
      type: object
      properties:
        paymentId:
          type: string
          description: Aion's internal id.
    EmptyBody:
      type: object
      nullable: true
    ErrorItem:
      type: object
      properties:
        errorType:
          type: string
          enum:
            - Default
            - ValidationError
            - BusinessLogicError
            - InternalError
        code:
          type: string
          nullable: true
        message:
          type: string
          nullable: true
        errorData:
          type: object
          nullable: true
    CreateInternalTransferRequest:
      type: object
      required:
        - originator
        - receiver
        - amount
      properties:
        originator:
          $ref: '#/components/schemas/AccountDetails'
        receiver:
          $ref: '#/components/schemas/AccountDetails'
        amount:
          type: number
          example: '5.00'
          format: decimal
          description: Amount of transaction in USD (no currency field)
          pattern: '[0-9]*\.[0-9]{2}'
          multipleOf: 0.01
        description:
          type: string
          description: A meaningful description, max. 30 chars. (UserNote)
          maxLength: 30
    CreateInternalTransferResponse:
      type: object
      properties:
        responseMessage:
          type: string
        result:
          type: boolean
        bookTransferObj:
          $ref: "#/components/schemas/Transfer"
    Transfer:
      type: object
      properties:
        objectId:
          type: string
        id:
          type: string
        amount:
          type: number
        createdAt:
          type: string
          format: date-time
        updatedAt:
          type: string
          format: date-time
        status:
          type: string
          enum:
            - pending
            - posted
        description:
          type: string
        senderDescription:
          type: string
        receiverDescription:
          type: string
        userNote:
          type: string
        statusMessage:
          type: string
        providerStatus:
          type: string
          enum:
            - pending
            - posted
        bankProvider:
          type: string
        transactionType:
          type: string
          enum:
            - push
        lastModifiedAt:
          type: string
          format: date-time
        railId:
          type: string
        rail:
          type: string
        proposedAt:
          type: string
          format: date-time
        currency:
          type: string
        traceNumber:
          type: string
        clientIdentifier:
          type: string
  securitySchemes:
    ApiKey:
      type: apiKey
      in: header
      name: AionAuth
security:
  - ApiKey: []