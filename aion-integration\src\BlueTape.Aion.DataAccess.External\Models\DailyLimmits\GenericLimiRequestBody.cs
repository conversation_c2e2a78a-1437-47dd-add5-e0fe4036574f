using System.Runtime.Serialization;
using System.Text.Json.Serialization;

namespace BlueTape.Aion.DataAccess.External.Models.DailyLimmits;

[DataContract]
public class GenericLimiRequestBody
{
    [JsonPropertyName("AccountNumber")]
    public string AccountNumber { get; set; } = null!;
    
    [JsonPropertyName("amount")]
    public decimal Amount { get; set; }

    [JsonPropertyName("TransferType")]
    public string TransferType { get; set; }
}