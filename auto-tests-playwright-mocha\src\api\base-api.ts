import {APIRequestContext, APIResponse, request} from '@playwright/test';

export class BaseAPI {

    static async getTokenID(emailAddress: string, password: string) {
        const apiContext: APIRequestContext = await request.newContext();
        const key = `${process.env.USER_KEY}`;
        const response: APIResponse = await apiContext.post("https://identitytoolkit.googleapis.com/v1/accounts:signInWithPassword?", {
            data: {
                "email": emailAddress,
                "password": password,
                "returnSecureToken": true,
            },
            params: {key: key}
        });
        if (response.status() === 200) {
            const bodyResponse: Buffer = await response.body();
            const jsonResponse = await this.convertToJson(bodyResponse);
            console.log(`\nidToken was successfully extracted.\n`);
            // console.log(`idToken was successfully extracted.\nidToken: ${jsonResponse.idToken}\n`);
            return jsonResponse.idToken;
        } else {
            throw new Error(`idToken wasn't extracted. Status code: ${response.status()} ${response.statusText()}`);
            // console.log(`idToken wasn't extracted. Status code: ${response.status()} ${response.statusText()}`);
            // return null;
        }
    };

    static async getAccountInfo(emailAddress: string, password: string) {
        const apiContext: APIRequestContext = await request.newContext();
        const key = `${process.env.USER_KEY}`;
        const response: APIResponse = await apiContext.post("https://identitytoolkit.googleapis.com/v1/accounts:signInWithPassword?", {
            data: {
                "email": emailAddress,
                "password": password,
                "returnSecureToken": true,
            },
            params: {key: key}
        });
        if (response.status() === 200) {
            const bodyResponse = await response.body();
            const jsonResponse = await this.convertToJson(bodyResponse);
            console.log(`\naccountInfo was successfully extracted.\n`);
            return jsonResponse;
        } else {
            throw new Error(`accountInfo wasn't extracted. Status code: ${response.status()} ${response.statusText()}`);
        }
    };

    static async getAuth(idToken: string) {
        const apiContext: APIRequestContext = await request.newContext();
        const response: APIResponse = await apiContext.post(`${process.env.USER_BACKEND_URL}/v2/user/web-auth`, {
            data: {"idToken": idToken}
        });
        if (response.status() === 200) {
            const bodyResponse = await response.body();
            const jsonResponse = await this.convertToJson(bodyResponse);
            console.log(`\nAuthorization was successful.\n`);
            //console.log(`Authorization was successful.\nSession: ${jsonResponse.session}\nChallenge: ${jsonResponse.challenge}\n`);
            return {
                "session": jsonResponse.session,
                "challenge": jsonResponse.challenge
            };
        } else {
            throw new Error(`Authorization wasn't successful. Status code: ${response.status()} ${response.statusText()}`);
            // console.log(`Authorization wasn't successful. Status code: ${response.status()} ${response.statusText()}`);
            // return null;
        }
    };

    static async googleSignUp(emailAddress: string, password: string) {
        const apiContext: APIRequestContext = await request.newContext();
        const response: APIResponse = await apiContext.post("https://identitytoolkit.googleapis.com/v1/accounts:signUp?", {
            data: {
                "email": emailAddress,
                "password": password,
                "returnSecureToken": true,
            },
            params: {key: `${process.env.USER_KEY}`}
        });
        if (response.status() === 200) {
            const bodyResponse: Buffer = await response.body();
            const jsonResponse = await this.convertToJson(bodyResponse);
            console.log(`\nGoogle account was successfully created.\n`);
            //console.log(`Google account was successfully created.\nGoogle idToken: ${jsonResponse.idToken}\n`);
            return jsonResponse.idToken;
        } else {
            throw new Error(`Google account wasn't created. Status code: ${response.status()} ${response.statusText()}`);
            // console.log(`Google account wasn't created. Status code: ${response.status()} ${response.statusText()}`);
            // return null;
        }
    };

    static async userSignUp(companyName: string, emailAddress: string, firstName: string, idToken: string, lastName: string, phoneNumber: string) {
        const apiContext: APIRequestContext = await request.newContext();
        const response: APIResponse = await apiContext.post(`${process.env.USER_BACKEND_URL}/v2/user/signup`, {
            data: {
                "company_name": companyName,
                "email": emailAddress,
                "firstName": firstName,
                "idToken": idToken,
                "invitation": "",
                "isBusiness": true,
                "lastName": lastName,
                "opt_for_marketing_messages": false,
                "phone": phoneNumber,
                "type": "password"
            }
        });
        if (response.status() === 200) {
            console.log(`\nUser account was created successfully.\n`);
        } else {
            throw new Error(`User account wasn't created. Status code: ${response.status()} ${response.statusText()}`);
            //console.log(`User account wasn't created. Status code: ${response.status()} ${response.statusText()}`);
        }
    };

    static async convertToJson(response: Buffer) {
        const bodyString: string = response.toString().replace(/(?:\r\n|\r|\n)/g, "").replace(/(?:\ )/g, "");
        return JSON.parse(bodyString);
    };
}

export function getCurrentDay() {
    const date: Date = new Date();
    const day: string = date.getDate().toString().padStart(2, '0');
    const month: string = (date.getMonth() + 1).toString().padStart(2, '0');
    const year: number = date.getFullYear();
    return `${year}-${month}-${day}`;
}
