@startuml Payment service
title Payment service static architecture (component diagram)
!include <awslib/AWSCommon>

' Uncomment the following line to create simplified view
!include <awslib/AWSSimplified>

!include <awslib/Compute/Lambda>
!include <awslib/ApplicationIntegration/SQS>
!include <awslib/ApplicationIntegration/Eventbridge>
!include <awslib/Storage/SimpleStorageServiceS3>
!include <awslib/Database/DocumentDBwithMongoDBcompatibility>

skinparam responseMessageBelowArrow true

component "Payment.Services\n[Financial] [Virtual Card]\n[Card Payment]" as ps #PaleTurquoise
component "Payment.Service API" as psapi #PaleTurquoise
rectangle "Payment.Integration.Service" as pis #line.dashed
queue "Operations Queue" as sqsOperations #HotPink
component "CBW" as cbw #LightGrey
component "Tabapay" as taba #LightGrey
database "  S3  " as s3 #HotPink
interface "1st parties" as first #Black
interface "1st parties" as first2 #Black
interface "Invoicing\nIntegration" as inv #Black
interface "Invoicing\nEventBridge" as ebInvoicing #HotPink
queue "Invoicing Queue" as sqsInvoicing #HotPink
component "Transaction Reports" as lambdaTransactionReports #Orange
component "Operation Checks" as lambdaOperationChecks #Orange
component "Loan Buybacks" as lambdaLoanBuybacks #Orange
database "Mongo" as db #LightSteelBlue
queue "TransactionReport" as sqsTrans #HotPink
queue "OperationChecks" as sqsOp #HotPink
queue "Loan Buybacks" as sqsLbb #HotPink

first -r-> sqsOperations
sqsOperations -r-> ps
pis -d-> cbw
pis -d-> taba
ps -r-> ebInvoicing
ps -d-> pis
ebInvoicing -r-> sqsInvoicing
sqsInvoicing -r-> inv
lambdaTransactionReports -d-> sqsTrans
sqsTrans -d-> ps
lambdaTransactionReports -l-> s3
lambdaLoanBuybacks -d-> sqsLbb
sqsLbb -d-> ps
lambdaOperationChecks -d-> sqsOp
sqsOp -d-> ps
psapi -d-> db : read
ps -d-> db : R/W
pis -d-> db : R/W
first2 -l-> psapi

@enduml