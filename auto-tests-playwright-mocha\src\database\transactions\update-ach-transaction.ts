import {client} from "../client";

export async function updateAchTranscation(operationID, transactionNumber) {
    const database = client.db(`${process.env.CI_ENVIRONMENT_URL == 'https://dev.bluetape.com' ? 'dev' : 'beta'}`);
    const collection = database.collection('transactions');

    const query = {"operation_id": `${operationID}`, "payment_method": "ach"};
    const update = {$set: {"metadata": {"transactionType": "OUT", "transactionNumber": `${transactionNumber}`}}};

    await collection.updateOne(query, update);
}
