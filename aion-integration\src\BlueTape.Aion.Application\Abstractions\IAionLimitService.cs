using BlueTape.Aion.DataAccess.External.Models.DailyLimmits;
using BlueTape.Integrations.Aion.Infrastructure.Enums;

namespace BlueTape.Aion.Application.Abstractions;

public interface IAionLimitService
{
    Task<AionAccountLimitResponse> GetAionAchPullLimit(decimal amount, AccountCodeType accountCodeType, string paymentSubscriptionType, CancellationToken ctx);
    Task<AionAccountLimitResponse> GetAionAchPushLimit(decimal amount, AccountCodeType accountCodeType, string paymentSubscriptionType, CancellationToken ctx);
    Task<AionAccountLimitResponse> GetAionWirePushLimit(decimal amount, AccountCodeType accountCodeType, string paymentSubscriptionType, CancellationToken ctx);
    Task<AionAccountLimitResponse> GetAionInstantPushLimit(decimal amount, AccountCodeType accountCodeType, string paymentSubscriptionType, CancellationToken ctx);
}