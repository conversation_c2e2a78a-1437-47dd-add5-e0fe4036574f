import {Page, expect} from '@playwright/test';
import {test} from '../../../test-utils';
import {PageManager} from '../../../../objects/pages/page-manager';

test.use({storageState: {cookies: [], origins: []}});
const constants = JSON.parse(JSON.stringify(require('../../../../constants/LMStestData.json')));

test.describe('Loan tests', async () => {
    let page: Page;
    
    test.beforeEach(async ({browser, userIdToken, userLoanIdToken}) => {

        page = await browser.newPage();
        await page.goto('/');
    });

    test.afterEach(async () => {
        await page.close();
    });

    test.skip('Loan List can be opened and tabs with Active and Previous Loans are displayed @loan', async () => {
        const pageManager = new PageManager(page);
        await pageManager.loginPage.login(`${constants.loanUsers[1].email}`, `${constants.loanUsers[1].password}`);

        await pageManager.sideMenu.openPaySubTab(pageManager.sideMenu.sideMenuSubTabs.pay.credit);
        await page.waitForLoadState('networkidle'); //waiter here for request to load
        await pageManager.creditList.tabs.previousLoans.click();
        await page.waitForLoadState('networkidle');

        const firstLoanAmount = await pageManager.creditList.getFirstLoanAmount();
        //let amount = await firstLoanAmount.textContent();
        await expect(pageManager.creditList.loanList.loanListElement,
            "List of Previous Loans is displayed").toBeVisible();
        //await expect(amount, "Remaining Paymnets should be equal to $0.00").toHaveValue(`$0.00`);
        //await pageManager.creditList.openFirstLoanDetails();
    });
});
