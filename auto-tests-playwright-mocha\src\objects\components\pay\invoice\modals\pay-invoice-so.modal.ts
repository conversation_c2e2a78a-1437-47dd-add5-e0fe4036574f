import {BasePage} from '../../../../base.page';

export class PayInvoiceSOModal extends BasePage {
    constructor(page){
        super(page);
    };

    buttons = {
        agreePay: this.page.locator('"Agree & Pay"'),
        payInvoice: this.page.locator('"Pay Invoice"'),
        close: this.page.locator('.css-901oao.r-1777fci'), // consider moving it later
    };
    
    label = { // change
        bankAccountLinkedSuccessfully: this.page.locator('"Bank Account linked successfully"'),
        cardLinkedSuccessfully: this.page.locator('"Card linked successfully"'),
        paymentRequestProcessing: this.page.locator('"Payment request is processing!"'),
        paymentIsProccessing: this.page.locator('[dir="auto"]:has-text("Payment is")'),
    };
}