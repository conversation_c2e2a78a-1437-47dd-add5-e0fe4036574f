﻿using System.Linq;
using AutoFixture;
using AutoFixture.Xunit2;
using BlueTape.Application.Tests.AutoFixture.Builders;

namespace BlueTape.Application.Tests.AutoFixture.Attributes;

public class AutoFixtureCustomAttribute() : AutoDataAttribute(() =>
{
    var fixture = new Fixture();

    fixture.Behaviors.OfType<ThrowingRecursionBehavior>().ToList().ForEach(b => fixture.Behaviors.Remove(b));
    fixture.Behaviors.Add(new OmitOnRecursionBehavior(2));
    fixture.Customizations.Add(new DateOnlyBuilder());
    fixture.Customizations.Add(new RandomNumericSequenceGenerator(1, 1000));

    return fixture;
});
