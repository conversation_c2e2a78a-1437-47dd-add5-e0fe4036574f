﻿using System.Runtime.Serialization;
using System.Text.Json.Serialization;

namespace BlueTape.Aion.DataAccess.External.Models.CreateCounterParty;

[DataContract]
public class CounterpartyObjectResponse
{
    [JsonPropertyName(("objectId"))]
    public string ObjectId { get; set; } = null!;
    
    [JsonPropertyName(("id"))]
    public string Id { get; set; } = null!;
    
    [JsonPropertyName(("type"))]
    public string Type { get; set; } = null!;
    
    [JsonPropertyName(("nameOnAccount"))]
    public string NameOnAccount { get; set; } = null!;
    
    [JsonPropertyName(("email"))]
    public string Email { get; set; } = null!;
    
    [JsonPropertyName(("createdAt"))]
    public DateTime CreatedAt { get; set; }
    
    [JsonPropertyName(("updatedAt"))]
    public DateTime UpdatedAt { get; set; }
    
    [JsonPropertyName(("transferMethodACH"))]
    public bool TransferMethodAch { get; set; }
    
    [JsonPropertyName(("TransferMethodWire"))]
    public bool TransferMethodWire { get; set; }
    
    [JsonPropertyName(("active"))]
    public bool Active { get; set; }
}