﻿using System.Runtime.Serialization;
using System.Text.Json.Serialization;

namespace BlueTape.Aion.DataAccess.External.Models.TransferMethod;

[DataContract]
public class BankDetail
{
    [JsonPropertyName("accountNumber")]
    public string AccountNumber { get; set; } = null!;
    
    [JsonPropertyName("routingNumber")]
    public string RoutingNumber { get; set; } = null!;
    
    [JsonPropertyName("accountType")]
    public string AccountType { get; set; } = null!;
    
    [JsonPropertyName("type")]
    public string Type { get; set; } = null!;
    
    [JsonPropertyName("addressOnAccount")]
    public AddressOnAccountRequest? AddressOnAccount { get; set; } = null!;
}