import {PutObjectCommand} from "@aws-sdk/client-s3";
import {s3} from "./aws-client";
import {readFile} from 'fs/promises';
import {basename} from 'path';
import * as dotenv from 'dotenv';

dotenv.config();

const uploadFile = async (bucketName: string, fileName: string, filePath: string) => {
    const fileContent = await readFile(filePath);
    const params = {
        Bucket: bucketName,
        Key: fileName,
        Body: fileContent
    };

    const response = await s3.send(new PutObjectCommand(params));
    if (response.$metadata.httpStatusCode === 200) {
        console.log(`File ${fileName} was successfully uploaded to AWS S3 ${bucketName}`);
    } else {
        console.log(`File ${fileName} wasn't uploaded to AWS S3 ${bucketName}. Status code: ${response.$metadata.httpStatusCode}`);
    }
};

export async function uploadTransactionReport(filePath: string) {
    const fileName = basename(filePath);
    const bucketName = `${process.env.BUCKETNAME_AWS}`;
    await uploadFile(bucketName, fileName, filePath);
};