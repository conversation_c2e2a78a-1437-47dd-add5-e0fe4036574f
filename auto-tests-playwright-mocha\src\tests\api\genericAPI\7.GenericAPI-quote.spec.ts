import {expect} from '@playwright/test';
import {test} from '../../test-utils';
import {
    createQuote,
    createProject,
    linkQuoteAndProject,
    unlinkQuoteAndProject,
    createCustomer
} from '../../../api/common/send-generic-api-request';

import {BaseTest} from '../../test-utils';

const error = JSON.parse(JSON.stringify(require('../../../constants/generic-api-errors.json')));

test.describe(`Quote tests @generic @API`, async () => {
    let customerId: string;
    let quoteId: string;
    let blueTapeId: string;

    test.beforeAll(async () => {
        customerId = `customerId${BaseTest.dateTimePrefix()}`;
        quoteId = `quoteId${BaseTest.dateTimePrefix()}`;
        await createCustomer(null, null, customerId);
    });

    test(`Create Quote`, async () => {
        const quoteData = {
            customerId,
            quoteId,
        };
        const response = await createQuote(quoteData);
        blueTapeId = response.data.blueTapeId;
        expect(response.status, `Status code 201`).toEqual(201);
        expect(response.data, `Response contains Object`).toEqual(expect.any(Object));
    });

    test(`Cannot Create Quote wuth non-unic Quote ID`, async () => {
        const quoteData = {
            customerId,
            quoteId,
        };
        const response = await createQuote(quoteData);
        expect(response.status, `Status code 400`).toEqual(400);
        expect(response.data, `Response contains Object`).toEqual(expect.any(Object));
        expect(response.data[0].code, `Error Code: ${error.quote.code.quoteAlreadyExist}`).toEqual(error.quote.code.quoteAlreadyExist);
        expect(response.data[0].reason, `Error Reason: Quote with id: "${blueTapeId}" already exist.`).toEqual(`Quote with id: "${blueTapeId}" already exist.`);
    });

    test(`Cannot create Quote with Total Amount = -1`, async () => {
        const quoteData = {
            customerId,
            totalAmount: -1,
        };
        const response = await createQuote(quoteData);
        expect(response.status, `Status code 400`).toEqual(400);
        expect(response.data, `Response contains Array`).toEqual(expect.any(Array));
        expect(response.data[0].code, `Error Code: ${error.quote.code.invalidDocumentData}`).toEqual(error.quote.code.invalidDocumentData);
        expect(response.data[0].reason, `Error Reason:${error.quote.reason.invalidTotalAmount}`).toEqual(error.quote.reason.invalidTotalAmount);
    });

    test(`Cannot create Quote with String in Total Amount`, async () => {
        const quoteData = {
            customerId,
            totalAmount: 'str',
        };
        const response = await createQuote(quoteData);
        expect(response.status, `Status code 400`).toEqual(400);
        expect(response.data, `Response contains Array`).toEqual(expect.any(Array));
        expect(response.data[0].code, `Error Code: ${error.quote.code.invalidDataFormat}`).toEqual(error.quote.code.invalidDataFormat);
        expect(response.data[0].reason, `Error Reason:${error.quote.reason.invalidDataFormat}`).toEqual(error.quote.reason.invalidDataFormat);
    });

    test(`Cannot create Quote with SubTotal Amount = -1`, async () => {
        const quoteData = {
            customerId,
            subTotalAmount: -1,
        };
        const response = await createQuote(quoteData);
        expect(response.status, `Status code 400`).toEqual(400);
        expect(response.data, `Response contains Array`).toEqual(expect.any(Array));
        expect(response.data[0].code, `Error Code: ${error.quote.code.invalidDocumentData}`).toEqual(error.quote.code.invalidDocumentData);
        expect(response.data[0].reason, `Error Reason:${error.quote.reason.invalidSubTotal}`).toEqual(error.quote.reason.invalidSubTotal);
    });

    test(`Cannot create Quote with String in SubTotal Amount`, async () => {
        const quoteData = {
            customerId,
            subTotalAmount: 'str',
        };
        const response = await createQuote(quoteData);
        expect(response.status, `Status code 400`).toEqual(400);
        expect(response.data, `Response contains Array`).toEqual(expect.any(Array));
        expect(response.data[0].code, `Error Code: ${error.quote.code.invalidDataFormat}`).toEqual(error.quote.code.invalidDataFormat);
        expect(response.data[0].reason, `Error Reason:${error.quote.reason.invalidDataFormat}`).toEqual(error.quote.reason.invalidDataFormat);
    });

    test(`Cannot create Quote with Tax Amount = null`, async () => {
        const quoteData = {
            customerId: customerId,
            taxAmount: null,
        };
        const response = await createQuote(quoteData);
        expect(response.status, `Status code 400`).toEqual(400);
        expect(response.data, `Response contains Object`).toEqual(expect.any(Object));
        expect(response.data[0].code, `Error Code: ${error.quote.code.invalidDataFormat}`).toEqual(error.quote.code.invalidDataFormat);
        expect(response.data[0].reason, `Error Reason:${error.quote.reason.invalidDataFormat}`).toEqual(error.quote.reason.invalidDataFormat);
    });

    test(`Cannot create Quote with Tax Amount = -1`, async () => {
        //fail
        const quoteData = {
            customerId: customerId,
            taxAmount: -1,
        };
        const response = await createQuote(quoteData);
        expect(response.status, `Status code 400`).toEqual(400);
        expect(response.data, `Response contains Object`).toEqual(expect.any(Object));
        expect(response.data[0].code, `Error Code: ${error.quote.code.invalidDataFormat}`).toEqual(error.quote.code.invalidDataFormat);
        expect(response.data[0].reason, `Error Reason:${error.quote.reason.invalidDataFormat}`).toEqual(error.quote.reason.invalidDataFormat);
    });

    test(`Cannot create Quote with Quote Data in the past`, async () => {
        //fail
        const previousDate = BaseTest.changeCurrentDate('minus', 20);
        const quoteData = {
            customerId: customerId,
            quoteDate: previousDate,
        };
        const response = await createQuote(quoteData);
        expect(response.status, `Status code 400`).toEqual(400);
        expect(response.data, `Response contains Object`).toEqual(expect.any(Object));
    });

    test(`Cannot create Quote with Quote Number = null`, async () => {
        const quoteData = {
            customerId: customerId,
            quoteNumber: null,
        };
        const response = await createQuote(quoteData);
        expect(response.status, `Status code 400`).toEqual(400);
        expect(response.data, `Response contains Object`).toEqual(expect.any(Object));
        expect(response.data[0].code, `Error Code: ${error.quote.code.invalidDataFormat}`).toEqual(error.quote.code.invalidDataFormat);
        expect(response.data[0].reason, `Error Reason:${error.quote.reason.nullInNumber}`).toEqual(error.quote.reason.nullInNumber);
    });

    test(`Cannot create Quote with Customer ID = null`, async () => {
        const quoteData = {
            customerId: null,
        };
        const response = await createQuote(quoteData);
        expect(response.status, `Status code 400`).toEqual(400);
        expect(response.data, `Response contains Object`).toEqual(expect.any(Object));
        expect(response.data[0].code, `Error Code: ${error.quote.code.invalidDataFormat}`).toEqual(error.quote.code.invalidDataFormat);
        expect(response.data[0].reason, `Error Reason:${error.quote.reason.invalidCustomerId}`).toEqual(error.quote.reason.invalidCustomerId);
    });

    test(`Cannot create Quote with Due Date in the past`, async () => {
        //fail
        const previousDate = BaseTest.changeCurrentDate('minus', 20);
        const quoteData = {
            customerId: customerId,
            dueDate: previousDate,
        };
        const response = await createQuote(quoteData);
        expect(response.status, `Status code 400`).toEqual(400);
        expect(response.data, `Response contains Object`).toEqual(expect.any(Object));
    });

    test(`Cannot create Quote with Expiration Date in the past`, async () => {
        const previousDate = BaseTest.changeCurrentDate('minus', 20);
        const quoteData = {
            customerId: customerId,
            expirationDate: previousDate,
        };
        const response = await createQuote(quoteData);
        expect(response.status, `Status code 400`).toEqual(400);
        expect(response.data, `Response contains Object`).toEqual(expect.any(Object));
        expect(response.data[0].code, `Error Code: ${error.quote.code.invalidDocumentData}`).toEqual(error.quote.code.invalidDocumentData);
        expect(response.data[0].reason, `Error Code: ${error.quote.reason.invalidExpirationDate}`).toEqual(error.quote.reason.invalidExpirationDate);
    });

    //LINK QUOTE WITH PROJECT

    test(`Link Quote with project`, async () => {
        const projectId = `projectId${BaseTest.dateTimePrefix()}`;
        const quoteData = {
            customerId,
            quoteId,
        };
        const projectData = {
            projectId,
        };
        await createQuote(quoteData);
        await createProject(projectData);
        const response = await linkQuoteAndProject(quoteId, projectId);
        expect(response.status, `Status code 201`).toEqual(201);
    });

    test(`Cannot link Quote and project with linked Quote`, async () => {
        const projectId = `projectId${BaseTest.dateTimePrefix()}`;
        const quoteData = {
            customerId,
            quoteId,
        };
        const projectData = {
            projectId,
        };
        await createQuote(quoteData);
        await createProject(projectData);
        const response = await linkQuoteAndProject(quoteId, projectId);
        expect(response.status, `Status code 400`).toEqual(400);
        expect(response.data, `Response contains Object`).toEqual(expect.any(Object));
        expect(response.data[0].code, `Error message: ${error.quote.code.quoteAlreadyLinked}`).toEqual(error.quote.code.quoteAlreadyLinked);
        expect(response.data[0].reason, `Error message: ${error.quote.reason.quoteAlreadyLinked}`).toEqual(error.quote.reason.quoteAlreadyLinked);
    });

    test(`Cannot link Quote and project with non existent project`, async () => {
        const projectId = `projectId${BaseTest.dateTimePrefix()}`;
        quoteId = `quoteId${BaseTest.dateTimePrefix()}`;
        const quoteData = {
            customerId,
            quoteId,
        };
        await createQuote(quoteData);
        const response = await linkQuoteAndProject(quoteId, projectId);
        expect(response.status, `Status code 400`).toEqual(400);
        expect(response.data, `Response contains Object`).toEqual(expect.any(Object));
        expect(response.data[0].code, `Error message: ${error.project.code.invalidId}`).toEqual(error.project.code.invalidId);
        expect(response.data[0].reason, `Error message: ${error.project.reason.invalidId}`).toEqual(error.project.reason.invalidId);
    });

    test(`Cannot link Quote and project with non existent Quote`, async () => {
        const projectId = `projectId${BaseTest.dateTimePrefix()}`;
        quoteId = `quoteId${BaseTest.dateTimePrefix()}`;
        const quoteData = {
            projectId,
        };
        await createProject(quoteData);
        const response = await linkQuoteAndProject(quoteId, projectId);
        expect(response.status, `Status code 400`).toEqual(400);
        expect(response.data, `Response contains Object`).toEqual(expect.any(Object));
        expect(response.data[0].code, `Error message: ${error.quote.code.quoteNonExistent}`).toEqual(error.quote.code.quoteNonExistent);
        expect(response.data[0].reason, `Error message: ${error.quote.reason.quoteNonExistent}`).toEqual(error.quote.reason.quoteNonExistent);
    });

    //UNLINK QUOTE AND PROJECT

    test(`Unlink quote and project`, async () => {
        const projectId = `projectId${BaseTest.dateTimePrefix()}`;
        quoteId = `quoteId${BaseTest.dateTimePrefix()}`;
        const quoteData = {
            customerId,
            quoteId,
        };
        const projectData = {
            projectId,
        };
        await createQuote(quoteData);
        await createProject(projectData);
        await linkQuoteAndProject(quoteId, projectId);
        const response = await unlinkQuoteAndProject(quoteId, projectId);
        expect(response.status, `Status code 200`).toEqual(200);
    });

    test(`Cannot unlink quote and project with Quote ID = null`, async () => {
        const projectId = `projectId${BaseTest.dateTimePrefix()}`;
        quoteId = `quoteId${BaseTest.dateTimePrefix()}`;
        const quoteData = {
            customerId,
            quoteId,
        };
        const projectData = {
            projectId,
        };
        await createQuote(quoteData);
        await createProject(projectData);
        await linkQuoteAndProject(quoteId, projectId);
        const response = await unlinkQuoteAndProject(null, projectId);
        expect(response.status, `Status code 400`).toEqual(400);
        expect(response.data, `Response contains Object`).toEqual(expect.any(Object));
        expect(response.data[0].code, `Error Code: ${error.quote.code.quoteNonExistent}`).toEqual(error.quote.code.quoteNonExistent);
        expect(response.data[0].reason, `Error Reason:${error.quote.reason.quoteNonExistent}`).toEqual(error.quote.reason.quoteNonExistent);
    });

    test(`Cannot unlink quote and project with project ID = null`, async () => {
        const projectId = `projectId${BaseTest.dateTimePrefix()}`;
        quoteId = `quoteId${BaseTest.dateTimePrefix()}`;
        const quoteData = {
            customerId,
            quoteId,
        };
        const projectData = {
            projectId,
        };
        await createQuote(quoteData);
        await createProject(projectData);
        await linkQuoteAndProject(quoteId, projectId);
        const response = await unlinkQuoteAndProject(quoteId, null);
        expect(response.status, `Status code 400`).toEqual(400);
        expect(response.data, `Response contains Object`).toEqual(expect.any(Object));
        expect(response.data[0].code, `Error message: ${error.project.code.invalidId}`).toEqual(error.project.code.invalidId);
        expect(response.data[0].reason, `Error message: ${error.project.reason.invalidId}`).toEqual(error.project.reason.invalidId);
    });
});
