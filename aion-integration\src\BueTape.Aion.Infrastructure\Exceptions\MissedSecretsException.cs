﻿using BlueTape.Common.ExceptionHandling.Exceptions.Base;
using System.Net;

namespace BueTape.Aion.Infrastructure.Exceptions;

public class MissedSecretsException : DomainException
{
    public MissedSecretsException()
        : base(string.Empty, HttpStatusCode.BadRequest) { }

    public MissedSecretsException(IEnumerable<string> missedSecrets)
    : base(string.Join(" ", missedSecrets), HttpStatusCode.BadRequest) { }

    public override string Code => ErrorCodes.MissedSecrets;
}
