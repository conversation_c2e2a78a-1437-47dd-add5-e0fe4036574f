# Authentication

BlueTape API uses API keys to authenticate requests. BlueTape team will share the keys.

All headers needs to be sent against our endpoints to identify who is sending request to us and distinguish what actions needs to be made, otherwise will get HTTP ```401``` Unauthorized response from our backend.

Header Key  | Description
----------- | -----------
```X-BlueTape-Key``` | Unique secret key to protect our API-s.
```X-Integration-AccountId``` | Unique identifier received to track you in our system.
