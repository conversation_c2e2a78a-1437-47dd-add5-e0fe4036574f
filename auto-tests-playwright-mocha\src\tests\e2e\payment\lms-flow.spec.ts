import {BaseTest, test} from '../../test-utils';
import {insertTestReport} from "../../../database/autoTests/AutoTestReportRepository";
import {Action, ITestReport} from "../../../database/autoTests/entities/AutoTestReport";
import {GenericIntegrationService} from "../../../services/genericIntegrationService";
import {paymentService} from "../../../services/paymentService";
import {CustomerService} from "../../../services/customerService";
import {BackOfficeClient} from "../../../api/back-office-decision-engine/backOfficeClient";
import { DrawApprovalsRepository } from '../../../database/drawApplication/drawApprovalsRepository';
import { OperationRepository } from '../../../database/operation/operationRepository';
import { InvoiceRepository } from '../../../database/invoices/invoiceRepository';
import { BackOfficeService } from '../../../services/backOfficeService';
import { SupplierService } from '../../../services/supplierService';
import { findLoans } from '../../../api/common/lms-send-request';
import envVarsManager from '../../../utils/env-vars-manager';
import { getCustomerNameByEmail, getCompanyIdByEmail } from '../../../database/customers/customer-searcher';
import { CompanyRepository } from '../../../database/companies/companyRepository';

// Define loan input interface for GitHub workflow parameters
interface LoanInputs {
    supplierEmail?: string;
    supplierPassword?: string;
    customerEmail?: string;
    customerPassword?: string;
    invoiceQuantity?: string;
    invoiceAmount?: string;
    environment?: string;
    loanType?: string;
}

interface EnvConfig {
    baseUrl: string;
    supplier: {
        email: string;
        password: string;
    };
    customer: {
        email: string;
        password: string;
        name: string;
    };
}

function getEnvConfig(): EnvConfig {
    // Parse CREATE_LOAN_INPUTS to get environment if available
    let loanEnvironment: string | undefined;
    try {
        if (process.env.CREATE_LOAN_INPUTS) {
            const loanInputs = JSON.parse(process.env.CREATE_LOAN_INPUTS);
            loanEnvironment = loanInputs.environment;
        }
    } catch (error) {
        console.error('Failed to parse CREATE_LOAN_INPUTS:', error);
    }
    
    // Get environment from GitHub Actions, loan inputs, or use default
    //const environment = loanEnvironment || process.env.TEST_ENVIRONMENT || 'beta';
    const environment = loanEnvironment || process.env.TEST_ENVIRONMENT || 'qa';
    
    // Load environment-specific configuration from .env.{environment} file
    try {
        // Clear any existing environment variables that might interfere
        // You might want to save and restore critical variables if needed
        
        // Using dotenv to load the environment file
        const dotenv = require('dotenv');
        const path = require('path');
        const fs = require('fs');
        
        // Determine the path to the .env.{environment} file
        const envFilePath = path.resolve(process.cwd(), `.env.${environment.toLowerCase()}`);
        
        if (fs.existsSync(envFilePath)) {
            // Override configuration
            const envConfig = dotenv.config({ 
                path: envFilePath,
                override: true // This ensures variables from this file override any existing ones
            });
            
            if (envConfig.error) {
                throw new Error(`Error loading .env.${environment.toLowerCase()}: ${envConfig.error.message}`);
            }
            
            console.log(`Successfully loaded environment variables from .env.${environment.toLowerCase()}`);
            
            // Debug: Print a few environment variables to confirm they were loaded correctly
            console.log(`Loaded environment: ${environment}`);
            console.log(`CI_ENVIRONMENT_URL: ${process.env.CI_ENVIRONMENT_URL}`);
            console.log(`USER_EMAIL: ${process.env.USER_EMAIL}`);
        } else {
            console.warn(`Environment file not found: ${envFilePath}`);
            console.log(`Using default .env file or existing environment variables.`);
        }
    } catch (error) {
        console.error(`Error loading environment variables: ${error.message}`);
    }
    
    // Create config object from environment variables
    const baseUrl = process.env.CI_ENVIRONMENT_URL || '';
    
    const envConfig: EnvConfig = {
        baseUrl: baseUrl.endsWith('/') ? baseUrl : `${baseUrl}/`,
        supplier: {
            email: process.env.SUPPLIER_EMAIL || process.env.IHC_USER_EMAIL || '',
            password: process.env.SUPPLIER_PASSPORD || process.env.IHC_USER_PASSWORD || '',
        },
        customer: {
            email: process.env.USER_CUSTOMEREMAIL || process.env.IHC_CUSTOMER_EMAIL || '',
            password: process.env.USER_CUSTOMERPASSWORD || process.env.IHC_CUSTOMER_PASSWORD || '',
            name: process.env.USER_CUSTOMER_COMPANYNAME || process.env.IHC_CUSTOMER_COMPANYNAME || '',
        }
    };
    
    return envConfig;
}

test.use({storageState: {cookies: [], origins: []}});

const testGroup = `@CreateLoanFlow Create loan via UI and approve`;

test.describe.parallel(testGroup, async () => {
    
    let testGroupId: string
    let resultPerTest: boolean
    let actionsPerTest: Action[]
    let report: ITestReport
    let _genericIntegrationService: GenericIntegrationService
    let _paymentService: paymentService
    let _supplierService: SupplierService
    let _customerService: CustomerService
    let _invoiceRepository: InvoiceRepository
    let _companyRepository: CompanyRepository
    let _operationRepository: OperationRepository
    let _backOfficeService: BackOfficeService
    let envConfig: EnvConfig
    
    test.beforeAll(async () => {
        _genericIntegrationService = new GenericIntegrationService()
        _supplierService = new SupplierService()
        _customerService = new CustomerService()
        _invoiceRepository = new InvoiceRepository()
        _companyRepository = new CompanyRepository()
        _operationRepository = new OperationRepository()
        _backOfficeService = new BackOfficeService(new DrawApprovalsRepository(), new BackOfficeClient())
        _paymentService = new paymentService(_invoiceRepository, _backOfficeService, new CustomerService())
        envConfig = getEnvConfig()
        resetReportToDefault()
    })
    
    function resetReportToDefault() {
        resultPerTest = true;
        testGroupId = process.env.TestGroupId || `payment-test-${Date.now()}`
        actionsPerTest = []
        
        report = {
            testGroup: testGroup,
            testGroupId: testGroupId,
        } as ITestReport
    }

    async function finalizeSingleTestReport() {
        report.createdAt = new Date()
        report.result = resultPerTest
        report.action = actionsPerTest;

        try {
            await insertTestReport(report);
        }
        catch (e) {
            console.log(e)
        }

        resetReportToDefault()
    }

    test.afterEach(async ({}, testInfo) => {
        if (testInfo.status !== 'passed') {
            actionsPerTest.push({ description: `Test failed with error: ${testInfo.error?.message}`});
            resultPerTest = false;
        }
        
        await finalizeSingleTestReport();
        // Reset the report without directly accessing _id
        resetReportToDefault();
    });

    // Define default values for local testing
    /*
    const defaultLoanInputs: LoanInputs = {
        supplierEmail: "<EMAIL>",
        supplierPassword: "Ss@22222222",
        customerEmail: "<EMAIL>",
        customerPassword: "Ss@22222222",
        invoiceQuantity: "1",
        environment: "beta"
    };
*/    const defaultLoanInputs: LoanInputs = {
        supplierEmail: "<EMAIL>",
        supplierPassword: "Ss@22222222",
        //customerEmail: "<EMAIL>",
        customerEmail: "<EMAIL>",
        customerPassword: "Ss@22222222",
        invoiceQuantity: "1",
        invoiceAmount: undefined,//undefined, // Can be set to specific value like "15000" or left undefined to use random amount
        environment: "qa",
        loanType: "DirectTerms",
        //loanType: "Regular"
        //loanType: "DirectTerms",
    };

    test("@CreateLoanFlow Create invoice via UI and create loan", async ({ browser, adminIdToken }, testInfo) => {
        report.testName = "@payments Create invoice via UI and create loan"
        
        // Store for metadata collection
        let collectedLoanIds: string[] = [];
        let collectedInvoiceIds: string[] = [];
        
        // Get timeout value from environment or use default
        const timeout = parseInt(process.env.TEST_TIMEOUT || '550000');
        test.setTimeout(timeout);
        
        // Parse CREATE_LOAN_INPUTS from GitHub workflow
        let loanInputs: LoanInputs;
        try {
            // Use CREATE_LOAN_INPUTS if available, otherwise use empty object
            const isLocalEnv = !process.env.GITHUB_ACTIONS;
            const providedInputs = process.env.CREATE_LOAN_INPUTS ? JSON.parse(process.env.CREATE_LOAN_INPUTS) : {};
            loanInputs = isLocalEnv 
                ? { ...defaultLoanInputs, ...providedInputs }
                : providedInputs;
                
            // Log the inputs being used
            actionsPerTest.push({ 
                description: `Using loan inputs: ${JSON.stringify(loanInputs)}`,
                data: loanInputs
            });
            
            // Validate required parameters
            const missingParams = [];
            if (!loanInputs.supplierEmail && !envConfig.supplier.email) missingParams.push('supplierEmail');
            if (!loanInputs.supplierPassword && !envConfig.supplier.password) missingParams.push('supplierPassword');
            if (!loanInputs.customerEmail && !envConfig.customer.email) missingParams.push('customerEmail');
            if (!loanInputs.customerPassword && !envConfig.customer.password) missingParams.push('customerPassword');
            
            if (missingParams.length > 0) {
                throw new Error(`Missing required test parameters: ${missingParams.join(', ')}`);
            }
            
        } catch (error) {
            actionsPerTest.push({ 
                description: `Failed to parse CREATE_LOAN_INPUTS: ${error.message}`,
                error: error
            });
            throw error;
        }
        
        // Use credentials from loan inputs if available, otherwise use environment config
        const supplierEmail = loanInputs.supplierEmail || envConfig.supplier.email;
        const supplierPassword = loanInputs.supplierPassword || envConfig.supplier.password;
        const customerEmail = loanInputs.customerEmail || envConfig.customer.email;
        const customerPassword = loanInputs.customerPassword || envConfig.customer.password;
        
        try {
            // Fetch customer name from MongoDB using email
            const customerName = await getCustomerNameByEmail(customerEmail);
            
            if (!customerName) {
                actionsPerTest.push({
                    description: `Warning: Could not find customer name for email: ${customerEmail}. Using email as customer name.`
                });
            }

            let invoiceNumber = "";            // Create invoice
            if (!loanInputs.loanType === null || loanInputs.loanType === "Regular") {
            const createInvoiceResponse = await _supplierService.addInvoice(
                envConfig.baseUrl,
                browser,
                supplierEmail,
                supplierPassword,
                customerName, // Use customer name if found, otherwise fallback to email
                actionsPerTest,
                loanInputs.invoiceAmount); // Use invoiceAmount if provided, otherwise use default random amount

            await _customerService.payInvoiceViaDraw(
                envConfig.baseUrl,
                browser,
                createInvoiceResponse.invoiceNumber,
                customerEmail,
                customerPassword,
                actionsPerTest);

                invoiceNumber = createInvoiceResponse.invoiceNumber;
            }
            else if (loanInputs.loanType === "DirectTerms") {
                invoiceNumber = await _customerService.createDirectTermsInvoice(
                envConfig.baseUrl,
                browser,
                customerEmail,
                customerPassword,
                actionsPerTest,
                loanInputs.invoiceAmount); // Use invoiceAmount if provided, otherwise use default random amount
            }

            // Get invoice details from MongoDB using invoice number
            const invoice = await _invoiceRepository.getInvoiceByNumber(invoiceNumber);
            if (!invoice) {
                const errorMsg = `Could not find invoice with number ${invoiceNumber} in the database`;
                actionsPerTest.push({ description: errorMsg });
                throw new Error(errorMsg);
            }
            
            // Extract the invoice ID for approval
            const invoiceId = invoice._id.toString();
            collectedInvoiceIds.push(invoiceId);
            
            actionsPerTest.push({ 
                description: `Retrieved invoice ID ${invoiceId} for invoice number ${invoiceNumber}`
            });

            if (loanInputs.loanType === "DirectTerms") {
                const supplierId = await _companyRepository.getCompanyIdByEmail(supplierEmail);

                if (!supplierId) {
                    const errorMsg = `Could not find company with email ${supplierEmail} in the database`;
                    actionsPerTest.push({ description: errorMsg });
                    throw new Error(errorMsg);
                }

                await _backOfficeService.attachSupplierDrawApprovalViaBackOffice(supplierId, invoiceId, browser, actionsPerTest, adminIdToken);
            }

            await _backOfficeService.approveDrawApprovalViaBackOffice(invoiceId, browser, actionsPerTest, adminIdToken);
            
            // Wait for loan to be created and processed
            await BaseTest.delayOperation(60000);
            
            // After approval and delay, find the loan ID
            const loans = await findLoans({
                payableId: invoiceId,
                detailed: true,
            });
            
            if (loans && loans.length > 0) {
                const loanId = loans[0].id;
                collectedLoanIds.push(loanId);
                
                actionsPerTest.push({ 
                    description: `Successfully created and processed loan: ${loanId} for invoice: ${invoiceId}`
                });
            }
            
            // Attach metadata to test result for the reporter to collect
            await testInfo.attach('metadata', {
                body: JSON.stringify({
                    loanIds: collectedLoanIds,
                    invoiceIds: collectedInvoiceIds
                }),
                contentType: 'application/json'
            });
            
        } catch (error) {
            console.error("Error in CreateLoanFlow test:", error);
            
            // Even in case of failure, try to collect any available IDs
            if (collectedInvoiceIds.length > 0 || collectedLoanIds.length > 0) {
                // Still attach any collected metadata
                await testInfo.attach('metadata', {
                    body: JSON.stringify({
                        loanIds: collectedLoanIds,
                        invoiceIds: collectedInvoiceIds,
                        error: error.message
                    }),
                    contentType: 'application/json'
                });
            }
            
            throw error;
        }
    });
});
