import { Lamb<PERSON> } from 'aws-sdk';
import { Browser } from '@playwright/test';

export class AwsService {
    private _lambda: Lambda;

    constructor() {
        this._lambda = new Lambda({
            accessKeyId: '********************',
            secretAccessKey: 'D/AzEsZkCWV7cz+olCL53MwgXEhEiToqI/U4gieP',
            region: 'us-west-1'
        });
    }

    async invokeLambda(functionName: string, invocationType: string = 'Event'): Promise<any> {
        const params = {
            FunctionName: functionName,
            InvocationType: invocationType,
        };

        try {
            const result = await this._lambda.invoke(params).promise();
            console.log(`Lambda invocation result: ${JSON.stringify(result, null, 2)}`);
            return result;
        } catch (error) {
            console.error(`Failed to invoke Lambda function ${functionName}:`, error);
            throw error;
        }
    }
}