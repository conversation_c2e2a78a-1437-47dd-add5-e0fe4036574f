
/**
 * Find messages from BLUETAP in Gmail. Messages are filtered by sender, receiver and subject
 * @param {Function} getMessages
 * @param {any[]} listOfMessagesId
 * @param {string[]} gmailSender
 * @param {string} messageReceiver
 * @param {string} messageSubject
 */
const findMessageFromBluetap = async (
    getMessages,
    listOfMessagesId,
    messageReceiver,
    messageSubject,
) => {
    const messages = listOfMessagesId.map((el) =>
        getMessages({
            userId: "me",
            id: el.id,
        }),
    );

    const response = await Promise.all(messages);

    const now = new Date();
    const minutesAgo = new Date(now.getTime() - 5 * 60 * 1000); // find messaged no more than 2 mins ago

    const necessaryMessage = response.find((element) => {
        const currentReceiver = element.data.payload.headers.find((el) => el.name === "To")
            .value;
        const currentSender = element.data.payload.headers.find((el) => el.name === "From") // TODO (For now it is stable noreply@)
            .value; 
        const currentSubject = element.data.payload.headers.find((el) => el.name === "Subject")
            .value;
        const dateString = element.data.payload.headers.find((el) => el.name === "Date")
            .value; 

        const date = new Date(dateString);

        // Check if the email meets the required parameters
        const receiversAreEqual = currentReceiver === messageReceiver;
        const subjectsAreEqual = currentSubject === messageSubject;

        // Check if the email was received in the last 2 minutes
        const isRecent = date >= minutesAgo;
        return receiversAreEqual && subjectsAreEqual && isRecent;
    });
    return necessaryMessage;
};

module.exports = findMessageFromBluetap;