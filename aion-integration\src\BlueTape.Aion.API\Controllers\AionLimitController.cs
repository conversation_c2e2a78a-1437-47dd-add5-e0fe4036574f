﻿using System.ComponentModel.DataAnnotations;
using BlueTape.Aion.API.Models.Errors;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using System.Diagnostics.CodeAnalysis;
using BlueTape.Aion.Application.Abstractions;
using BlueTape.Aion.DataAccess.External.Constants;
using BlueTape.Integrations.Aion;
using BlueTape.Integrations.Aion.Infrastructure.Enums;

namespace BlueTape.Aion.API.Controllers;

[ApiController]
[Route("/api/aion-limit")]
[ExcludeFromCodeCoverage]
[Authorize]
[ProducesResponseType(typeof(List<ErrorModel>), StatusCodes.Status400BadRequest)]
[ProducesResponseType(typeof(void), StatusCodes.Status401Unauthorized)]
[ProducesResponseType(typeof(List<ErrorModel>), StatusCodes.Status500InternalServerError)]
public class AionLimitController : ControllerBase
{
    private readonly IAionLimitService _aionLimitService;

    public AionLimitController(IAionLimitService aionLimitService)
    {
        _aionLimitService = aionLimitService;
    }

    [HttpGet("/ach-pull/account-code/{accountCodeType}")]
    [ProducesResponseType(typeof(AionTransferLimitModel), StatusCodes.Status200OK)]
    public async Task<IActionResult> GetAionAchPullLimit(
        [FromHeader(Name = ClientConstants.PaymentSubscriptionTypeHeader)][Required] string paymentSubscriptionType,
        [FromRoute][Required] AccountCodeType accountCodeType,
        CancellationToken ctx,
        [FromQuery] decimal amount = 0)
    {
        var limit = await _aionLimitService.GetAionAchPullLimit(amount, accountCodeType, paymentSubscriptionType, ctx);
        return Ok(new AionTransferLimitModel
        {
            Limit = limit.DailyAvailableACHDebitBalance
        });
    }
    
    [HttpGet("/ach-push/account-code/{accountCodeType}")]
    [ProducesResponseType(typeof(AionTransferLimitModel), StatusCodes.Status200OK)]
    public async Task<IActionResult> GetAionAchPushLimit(
        [FromHeader(Name = ClientConstants.PaymentSubscriptionTypeHeader)][Required] string paymentSubscriptionType,
        [FromRoute][Required] AccountCodeType accountCodeType,
        CancellationToken ctx,
        [FromQuery] decimal amount = 0)
    {
        var limit = await _aionLimitService.GetAionAchPushLimit(amount, accountCodeType, paymentSubscriptionType, ctx);
        return Ok(new AionTransferLimitModel
        {
            Limit = limit.DailyAvailableACHBalance
        });
    }
    
    [HttpGet("/wire-push/account-code/{accountCodeType}")]
    [ProducesResponseType(typeof(AionTransferLimitModel), StatusCodes.Status200OK)]
    public async Task<IActionResult> GetAionWirePushimit(
        [FromHeader(Name = ClientConstants.PaymentSubscriptionTypeHeader)][Required] string paymentSubscriptionType,
        [FromRoute][Required] AccountCodeType accountCodeType,
        CancellationToken ctx,
        [FromQuery] decimal amount = 0)
    {
        var limit = await _aionLimitService.GetAionWirePushLimit(amount, accountCodeType, paymentSubscriptionType, ctx);
        return Ok(new AionTransferLimitModel
        {
            Limit = limit.DailyAvailableWireBalance
        });
    }
    
    [HttpGet("/instant-push/account-code/{accountCodeType}")]
    [ProducesResponseType(typeof(AionTransferLimitModel), StatusCodes.Status200OK)]
    public async Task<IActionResult> GetAionInstantPushLimit(
        [FromHeader(Name = ClientConstants.PaymentSubscriptionTypeHeader)][Required] string paymentSubscriptionType,
        [FromRoute][Required] AccountCodeType accountCodeType,
        CancellationToken ctx,
        [FromQuery] decimal amount = 0)
    {
        var limit = await _aionLimitService.GetAionInstantPushLimit(amount, accountCodeType, paymentSubscriptionType, ctx);
        return Ok(new AionTransferLimitModel
        {
            Limit = limit.DailyAvailableInstantXferBalance
        });
    }
}