import {expect} from "@playwright/test";
import {test, BaseTest} from '../../test-utils';

test.use({storageState: {cookies: [], origins: []}});

test.describe("Projects tests", async () => {
    const address: string = BaseTest.constants.address.fullAddress;
    const street: string = BaseTest.constants.address.street;
    const state: string = BaseTest.constants.address.state;
    const city: string = BaseTest.constants.address.city;
    const zipCode: string = BaseTest.constants.address.zipCode;

    const currentDate: string = BaseTest.getDueDateInFuture(0);
    const dateInTwoDays: string = BaseTest.getDueDateInFuture(2);
    const contractValue = '1000';

    const firstName: string = BaseTest.dateTimePrefix() + 'firstName';
    const lastName: string = BaseTest.dateTimePrefix() + 'lastName';
    const phoneNumber: string = BaseTest.constants.user.cellPhoneNumber;

    const buttonShouldBeInactiveInfo = 'Button "next" should be inactive';


    test.afterEach(async ({page}) => {
        await page.close();
    });

    test(`Property Address. Click add new project. Property address window should be opened.`,
        async ({pageManager}) => {
            await addNewProject({pageManager});

            await expect(pageManager.projectPropertyAddress.input.streetAddress,
                `Property address window should be opened`).toBeVisible();
        });

    test(`Property Address. Click add new project. Fill street, state, zip code fields, city is empty. Button 'next' should be inactive.`,
        async ({pageManager}) => {
            await addNewProject({pageManager});

            await pageManager.projectPropertyAddress.fillRequiredFields(address, state, zipCode, '');

            await expect(pageManager.projectPropertyAddress.buttons.next, buttonShouldBeInactiveInfo)
                .toHaveAttribute('tabindex', '-1');
        });

    test(`Property Address. Click add new project. Fill street, state, city fields, zip code is empty. Button 'next' should be inactive.`,
        async ({pageManager}) => {
            await addNewProject({pageManager});

            await pageManager.projectPropertyAddress.fillRequiredFields(address, state, '', city);

            await expect(pageManager.projectPropertyAddress.buttons.next, buttonShouldBeInactiveInfo)
                .toHaveAttribute('tabindex', '-1');
        });

    test(`Property Address. Click add new project. Fill state, city fields, zip code, street address is empty. Button 'next' should be inactive.`,
        async ({pageManager}) => {
            await addNewProject({pageManager});

            await pageManager.projectPropertyAddress.fillRequiredFields('', state, zipCode, city);

            await expect(pageManager.projectPropertyAddress.buttons.next, buttonShouldBeInactiveInfo)
                .toHaveAttribute('tabindex', '-1');
        });

    test(`Property Address. Click add new project. Fill street address, city fields, zip code, state is empty. Button 'next' should be inactive.`,
        async ({pageManager}) => {
            await addNewProject({pageManager});

            await pageManager.projectPropertyAddress.fillRequiredFields(address, '', zipCode, city);

            await expect(pageManager.projectPropertyAddress.buttons.next, buttonShouldBeInactiveInfo)
                .toHaveAttribute('tabindex', '-1');
        });

    test(`Property Address. Click add new project. Fill street address, city fields, zip code, state. Button 'next' should be active.`,
        async ({pageManager}) => {
            await addNewProject({pageManager});

            await pageManager.projectPropertyAddress.fillRequiredFields(street, state, zipCode, city);

            await expect(pageManager.projectPropertyAddress.buttons.next, "Button 'next' should be active.")
                .toHaveAttribute('tabindex', '0');
        });

    test(`Property Address. Enter address and click on the address in list. Required fields should be filled. @flaky`,
        async ({pageManager}) => {
            await addNewProject({pageManager});

            await expect(async () => pageManager.projectPropertyAddress.fillAddressFieldAndChoose(street))
                .toPass({intervals: [5000, 5000, 5000]});

            await expect(async () => pageManager.projectPropertyAddress.input.streetAddress.click({delay: 2000}))
                .toPass({intervals: [5000, 5000, 5000]});

            await expect(pageManager.projectPropertyAddress.input.city).toHaveValue(city);

            await expect(pageManager.projectPropertyAddress.input.state).toHaveValue(state);

            await expect(pageManager.projectPropertyAddress.input.zipCode).toHaveValue(zipCode);
        });

    test(`Project Information. Enter address and click on the address in list. Required fields should be filled. Button 'next' should be inactive.`,
        async ({pageManager}) => {
            await addNewProject({pageManager});

            await expect(async () => pageManager.projectPropertyAddress.fillAddressFieldAndChoose(street))
                .toPass({intervals: [5000, 5000, 5000]});

            await expect(async () => pageManager.projectPropertyAddress.clickActiveNextButton())
                .toPass({intervals: [5000, 5000, 5000]});

            await pageManager.projectInformation.fillRequiredFields('1000', '11/13/2023', '11/25/2023');

            await expect(pageManager.projectPropertyAddress.buttons.next, buttonShouldBeInactiveInfo)
                .toHaveAttribute('tabindex', '-1');
        });

    test(`Project Information. Fill contract value, first day and expected last day on the job the role not chosen. Button 'next' should be inactive.`,
        async ({pageManager}) => {
            await addNewProject({pageManager});

            await expect(async () => pageManager.projectPropertyAddress.fillAddressFieldAndChoose(street))
                .toPass({intervals: [5000, 5000, 5000]});

            await pageManager.projectPropertyAddress.clickActiveNextButton();

            await pageManager.projectInformation.fillRequiredFields(contractValue, currentDate, dateInTwoDays);

            await expect(pageManager.projectPropertyAddress.buttons.next, buttonShouldBeInactiveInfo)
                .toHaveAttribute('tabindex', '-1');
        });

    test(`Project Information. Fill first day and expected last day on the job, role was chosen. Button 'next' should be inactive.`,
        async ({pageManager}) => {
            await addNewProject({pageManager});

            await expect(async () => pageManager.projectPropertyAddress.fillAddressFieldAndChoose(street))
                .toPass({intervals: [5000, 5000, 5000]});
            await pageManager.projectPropertyAddress.clickActiveNextButton();

            await pageManager.projectInformation.fillRequiredFields('', currentDate, dateInTwoDays);
            await pageManager.projectInformation.clickRoleAndSelect.primeContractor();

            await expect(pageManager.projectPropertyAddress.buttons.next, buttonShouldBeInactiveInfo)
                .toHaveAttribute('tabindex', '-1');
        });

    test(`Project Information. Fill contract value and expected last day on the job, role was chosen. Button 'next' should be inactive.`,
        async ({pageManager}) => {
            await addNewProject({pageManager});

            await expect(async () => pageManager.projectPropertyAddress.fillAddressFieldAndChoose(street))
                .toPass({intervals: [5000, 5000, 5000]});
            await pageManager.projectPropertyAddress.clickActiveNextButton();

            await pageManager.projectInformation.fillRequiredFields(contractValue, '', dateInTwoDays);
            await pageManager.projectInformation.clickRoleAndSelect.primeContractor();

            await expect(pageManager.projectPropertyAddress.buttons.next, buttonShouldBeInactiveInfo)
                .toHaveAttribute('tabindex', '-1');
        });

    test(`Project Information. Fill contract value and first day day on the job, role was chosen. Button 'next' should be inactive.`,
        async ({pageManager}) => {
            await addNewProject({pageManager});

            await expect(async () => pageManager.projectPropertyAddress.fillAddressFieldAndChoose(street))
                .toPass({intervals: [5000, 5000, 5000]});
            await pageManager.projectPropertyAddress.clickActiveNextButton();

            await pageManager.projectInformation.fillRequiredFields(contractValue, currentDate, '');
            await pageManager.projectInformation.clickRoleAndSelect.primeContractor();

            await expect(pageManager.projectPropertyAddress.buttons.next, buttonShouldBeInactiveInfo)
                .toHaveAttribute('tabindex', '-1');
        });

    test(`Project Type. Upload file. File should be visible.`,
        async ({pageManager}) => {
            await addNewProject({pageManager});

            await expect(async () => pageManager.projectPropertyAddress.fillAddressFieldAndChoose(street))
                .toPass({intervals: [5000, 5000, 5000]});
            await pageManager.projectPropertyAddress.clickActiveNextButton();

            await pageManager.projectInformation.clickRoleAndSelect.primeContractor();
            await pageManager.projectInformation.fillRequiredFields(contractValue, currentDate, dateInTwoDays);

            await pageManager.projectType.uploadMockFile();
            await expect(pageManager.projectType.elements.uploadedFileName, `Uploaded file is visible`)
                .toBeVisible();
        });

    test(`Project Type. Upload file and delete. File should not be visible.`,
        async ({pageManager}) => {
            await fullPathFromAddingNewProjectToUploadingMockFile({pageManager});
            await pageManager.projectType.buttons.deleteButton.click();

            await expect(pageManager.projectType.elements.uploadedFileName, `File should be not visible on the page`)
                .toBeVisible({visible: false});
        });

    test(`Project Property Owner. Individual's required fields were filled.`,
        async ({pageManager}) => {
            await fullPathFromAddingNewProjectToUploadingMockFile({pageManager});
            await pageManager.projectPropertyAddress.clickActiveNextButton();

            await pageManager.projectType.chooseFederalTypeWithoutBond();
            await pageManager.projectPropertyAddress.clickActiveNextButton();

            await pageManager.projectPropertyOwner.fillIndividualInfo(firstName, lastName, phoneNumber, address);

            await pageManager.projectPropertyAddress.clickActiveNextButton();

            await expect(pageManager.projects.elements.lastCreatedProject, `Project should be created`)
                .toContainText("51 W 51st St, New York, New York, 10019");
        });

    async function addNewProject({pageManager}) {
        await pageManager.sideMenu.sideMenuTabs.projects.click();
        await pageManager.projects.buttons.addNewProject.click();
    }

    async function fullPathFromAddingNewProjectToUploadingMockFile({pageManager}) {
        await addNewProject({pageManager});

        await expect(async () => pageManager.projectPropertyAddress.fillAddressFieldAndChoose(street))
            .toPass({intervals: [5000, 5000, 5000]});
        await pageManager.projectPropertyAddress.clickActiveNextButton();

        await pageManager.projectInformation.fillRequiredFields(contractValue, currentDate, dateInTwoDays);
        await pageManager.projectInformation.clickRoleAndSelect.primeContractor();

        await pageManager.projectType.uploadMockFile();
    }
});
