﻿using BlueTape.Aion.API.Models.Errors;
using BlueTape.Aion.API.Validators;
using BlueTape.Common.ExceptionHandling.Exceptions.Base;
using BlueTape.Utilities.Extensions;
using BueTape.Aion.Infrastructure;
using System.Collections.Concurrent;
using System.Diagnostics.CodeAnalysis;
using System.Net;

namespace BlueTape.Aion.API.Helpers;

[ExcludeFromCodeCoverage]
public sealed class ExceptionToResponseMapper : IExceptionToResponseMapper
{
    private static readonly ConcurrentDictionary<Type, string> Codes = new();
    public const string GenericError = "OOPS, Something went wrong";

    public (List<ErrorModel>, HttpStatusCode) Map(Exception exception)
    {
        Exception exceptionToHandle = exception;

        if (exception.InnerException is DomainException)
            exceptionToHandle = exception.InnerException;

        var (res, code) = exceptionToHandle switch
        {
            FluentValidationException fluentValidationException => (fluentValidationException.ErrorModels, HttpStatusCode.BadRequest),
            DomainException ex => (new List<ErrorModel>
            {
                new()
                {
                    ErrorType = ErrorCodes.AionErrors.ContainsKey(GetCode(ex))
                        ? ErrorType.AionError
                        : ErrorType.BusinessLogicError,
                    Code = GetCode(ex),
                    Reason = ex.Message,
                    ErrorData = ex.ErrorData
                }
            }, ex.HttpStatusCode),
            _ => (new List<ErrorModel>
            {
                new()
                {
                    ErrorType = ErrorType.InternalError,
                    Code = "error",
                    Reason = GenericError
                }
            }, HttpStatusCode.InternalServerError)
        };

        return (res, code);
    }

    private static string GetCode(Exception exception)
    {
        var type = exception.GetType();

        if (Codes.TryGetValue(type, out var code))
        {
            return code;
        }

        var exceptionCode = exception switch
        {
            DomainException ex when !string.IsNullOrWhiteSpace(ex.Code) => ex.Code,
            _ => type.Name.Underscore().Replace("_exception", string.Empty)
        };

        Codes.TryAdd(type, exceptionCode);

        return exceptionCode;
    }
}
