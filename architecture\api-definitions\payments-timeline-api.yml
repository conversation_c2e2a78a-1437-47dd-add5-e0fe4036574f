openapi: '3.0.0'
info:
  version: '0.0.1'
  title: 'LoanReceivables Payments Timeline API'
  description: | 
    API definition proposal for timeline of payments of a loan
servers:
- url: TBD-Dev
  description: Development server
- url: TBD-QA
  description: QA server
- url: TBD-Prod
  description: Production server
paths:
  /loanreceivables/payments:
    get:
      tags:
        - LoanReceivablesPayments
      summary: Gets loan's receivables and payments details as a timeline
      description: Gets loan's receivables and payments details as a timeline
      operationId: getLoanReceivabledPaymentsTimeline
      parameters:
        - name: loanId
          description: Identifier of the loan
          example: 6df30249-c7f0-4f17-b06f-c29b2fee0a62
          in: query
          required: true
          schema:
            type: string
            nullable: false
      responses:
        200:
          description: The ordered items for payments and receivables
          content:
            application/json:
              schema:
                type: array
                items:
                    $ref: '#/components/schemas/LoanReceivablesPaymentsTimelineItem'
        400:
          description: Invalid request
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        401:
          description: Unauthorized
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        404:
          description: Not found
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        500:
          description: Unexpected error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
components:
  schemas:
    LoanReceivablesPaymentsTimelineItem:
      type: object
      properties:
        loanReceivableId:
          type: string
          format: uuid
          description: The loan receivable id, if applicable
          nullable: true
        paymentId:
          type: string
          format: uuid
          description: The payment id, if applicable
          nullable: true
        itemType:
          type: string
          enum:
            - payment
            - installment
            - lateFee
            - extensionFee
          description: Type of timeline item. Installment contains loan fee.
          example: payment
        date:
          type: string
          format: date
          example: 2023-05-04
          description: Date of timeline item
        amount:
          type: number
          format: decimal
          example: 1000.00
          pattern: '[0-9]*\.[0-9]{2}'
          multipleOf: 0.01
          description: Amount of timeline item (payment amount or remaining unpaid receivable amount)
        dueStatus:
          type: string
          enum:
            - paid
            - processing
            - pastDue
            - dueNow
            - dueNext
            - failed
          description: A calculated due status of timeline item
          example: paid
    ErrorResponse:
      type: object
      required:
        - statusCode
        - message
        - errorDescription
      properties:
        statusCode:
          description: HTTP status code corresponding to the error.
          type: integer
          example: 400
        message:
          description: A human readable error message.
          type: string
          example: Invalid model.
        errorDescription:
          description: Detailed information.
          type: string
  securitySchemes:
    ApiKey:
      type: apiKey
      in: header
      name: X-Api-Key
security:
  - ApiKey: []