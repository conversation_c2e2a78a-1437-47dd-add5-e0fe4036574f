openapi: "3.0.0"
info:
  version: "0.0.1"
  title: "Virtual Cards API (as a part of Payment Domain)"
  description: |
    API definition of Virtual Cards internal functions
servers:
  - url: TBD-Dev
    description: Development server
  - url: TBD-Prod
    description: Production server
paths:
  /virtualcards:
    post:
      tags:
        - virtual cards
      summary: Creates a new virtual card for a draw request
      description: Creates a new virtual card for a draw request
      operationId: createVirtualCard
      parameters:
        - name: drawApprovalId
          description: Identifier of the draw approval
          example: 62c5e900cd65d31d3f25c34c
          in: header
          required: true
          schema:
            type: string
            nullable: false
      requestBody:
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/CreateVirtualCard"
      responses:
        201:
          description: The created virtual card
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/VirtualCard'
        400:
          description: Bad request
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
    get:
      tags:
        - virtual cards
      summary: Gets all virtual cards by various filters
      description: Gets all projects by various filters
      operationId: getProjects
      parameters:
        - name: id
          description: The virtual card id
          in: query
          required: false
          schema:
            type: string
        - name: companyId
          description: The company id
          in: query
          required: false
          schema:
            type: string
        - name: merchantId
          description: The merchant id
          in: query
          required: false
          schema:
            type: string
        - name: status
          description: The status
          in: query
          required: false
          schema: 
            type: string
            enum:
              - issuedAndActive
              - usedAndClosed
              - notUsedAndExpired
        - name: drawApprovalId
          description: The draw approval id
          in: query
          required: false
          schema: 
            type: string
        - name: cardExternalId
          description: The card's external id
          in: query
          required: false
          schema: 
            type: string
        - name: provider
          description: The virtual card provider
          in: query
          required: false
          schema: 
            type: string
            enum:
              - cbw
        - name: page
          description: The page number
          example: 2
          in: query
          required: false
          schema:
            type: number
        - name: items
          description: The items number on a page
          example: 50
          in: query
          required: false
          schema:
            type: number
      responses:
        200:
          description: The virtual cards
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/PagedVirtualCards'
        400:
          description: Bad request
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
  /virtualcards/{id}:
    get:
      tags:
        - virtual cards
      summary: Gets a virtual card by id (just for compatibility)
      description: Gets a virtual card by id (just for compatibility)
      operationId: getVirtualCardById
      parameters:
        - name: id
          description: The id of virtual card
          required: true
          in: path
          schema:
            type: string
      responses:
        200:
          description: The virtual card
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/VirtualCard'
        400:
          description: Bad request
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
  /virtualcards/{id}/status:
    patch: 
      tags:
      - virtual cards
      summary: Updates virtual card status
      description: Updates virtual card status
      operationId: updateVirtualCardStatusById
      parameters:
        - name: id
          description: The id of virtual card
          in: path
          required: true
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/PatchVirtualCardStatus"
      responses:
        200:
          description: The patched virtual card
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/VirtualCard'
        400:
          description: Bad request
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
components:
  schemas:
    ErrorResponse:
      type: object
      required:
        - statusCode
        - message
        - errorDescription
      properties:
        statusCode:
          description: HTTP status code corresponding to the error.
          type: integer
          example: 400
        message:
          description: A human readable error message.
          type: string
          example: Invalid model.
        errorDescription:
          description: Detailed information.
          type: string
    VirtualCard:
      allOf:
        - type: object
          required:
            - id
          properties:
            id:
              type: string
              format: guid
              description: Id of virtual card
              example: d3a5d889-47e4-4321-b87b-f816ce5e7b6e
            createdAt:
              type: string
              format: date-time
            updatedAt:
              type: string
              format: date-time
            status:
              type: string
              enum:
                - issuedAndActive
                - usedAndClosed
                - notUsedAndExpired
              default: issuedAndActive
        - $ref: '#/components/schemas/CreateVirtualCard'
        - type: object
          properties:
            usedAmount:
              type: number
    CreateVirtualCard:
      type: object
      properties:
        companyId:
          type: string
        merchantId:
          type: string
        drawApprovalId:
          type: string
        limit:
          type: number
        cardExternalId:
          type: string
        provider:
          type: string
          enum:
            - cbw
          default: cbw
        payablesIds:
          type: array
          items:
            type: string
    PagedVirtualCards:
      type: object
      properties:
        pageNumber:
          type: number
        pagesCount:
          type: number
        totalCount:
          type: number
        result:
          type: array
          items:
            $ref: "#/components/schemas/VirtualCard"
    PatchVirtualCardStatus:
      type: object
      properties:
        newStatus:
          type: string
          enum:
            - usedAndClosed
            - notUsedAndExpired
        usedAmount:
          type: number
  securitySchemes:
    ApiKey:
      type: apiKey
      in: header
      name: X-Api-Key
security:
  - ApiKey: []  
