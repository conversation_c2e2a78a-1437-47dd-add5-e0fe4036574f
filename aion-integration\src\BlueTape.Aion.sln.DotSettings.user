﻿<wpf:ResourceDictionary xml:space="preserve" xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml" xmlns:s="clr-namespace:System;assembly=mscorlib" xmlns:ss="urn:shemas-jetbrains-com:settings-storage-xaml" xmlns:wpf="http://schemas.microsoft.com/winfx/2006/xaml/presentation">
	<s:String x:Key="/Default/Environment/UnitTesting/UnitTestSessionStore/Sessions/=4705e22e_002D0127_002D4f40_002Dbb3a_002D89a1f1f3e16d/@EntryIndexedValue">&lt;SessionState ContinuousTestingMode="0" IsActive="True" Name="EnsureTransactionDoesNotExist_TransactionNotExists_InsertsNewTransaction" xmlns="urn:schemas-jetbrains-com:jetbrains-ut-session"&gt;&#xD;
  &lt;TestAncestor&gt;&#xD;
    &lt;TestId&gt;xUnit::97FE58C0-1FBC-4217-A60A-E2CC1AC427E1::net8.0::BlueTape.Application.Tests.Services.TransactionServiceTests.EnsureTransactionDoesNotExist_TransactionNotExists_InsertsNewTransaction&lt;/TestId&gt;&#xD;
    &lt;TestId&gt;xUnit::97FE58C0-1FBC-4217-A60A-E2CC1AC427E1::net8.0::BlueTape.Application.Tests.Services.TransactionServiceTests.EnsureTransactionDoesNotExist_TransactionExists_ThrowsArgumentOutOfRangeException&lt;/TestId&gt;&#xD;
  &lt;/TestAncestor&gt;&#xD;
&lt;/SessionState&gt;</s:String></wpf:ResourceDictionary>