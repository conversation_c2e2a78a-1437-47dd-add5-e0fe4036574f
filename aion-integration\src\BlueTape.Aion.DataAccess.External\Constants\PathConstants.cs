﻿namespace BlueTape.Aion.DataAccess.External.Constants;

public static class PathConstants
{
    public const string LoginPath = "api/login";
    public const string CreateAchTransferPath = "api/bb/createACHTransfer";
    public const string CreateWireTransferPath = "api/bb/createWire";
    public const string CreateInstantTransferPath = "api/bb/createInstantTransfer";
    public const string GetInternalTransfersPath = "api/bb/getInternalTransfers";
    public const string GetAchTransfersPath = "api/bb/getACHTransfers";
    public const string GetAchTransferReturnsPath = "api/bb/getACHTransferReturns";
    public const string CreateCounterPartyPath = "api/bb/createCounterparty";
    public const string AddTransferMethodPath = "api/bb/addTransferMethod";
    public const string GetAccountsPath = "api/bb/getAccounts";
    public const string GetTransferMethodsPath = "api/bb/getTransferMethods";
    public const string CreateInternalTransferPath = "api/bb/createInternalTransfer";
    public const string GetTransactionsPath = "/api/bb/getTransactions";
    public const string GetWireTransfersPath = "/api/bb/getWireTransfers";
    public const string GetInstantTransfersPath = "/api/bb/getInstantTransfers";
    public static string GetAchPullLimit = "api/bb/getDailyLimitsACHDebit";
    public static string GetAchPushLimit = "api/bb/getDailyLimitsACH";
    public static string GetDailyLimitsInstantTransfer = "api/bb/getDailyLimitsInstantTransfer";
    public static string GetDailyLimitsWire = "api/bb/getDailyLimitsWire";
}