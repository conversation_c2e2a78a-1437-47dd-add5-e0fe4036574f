import {client} from "../client";
import { ObjectId } from 'mongodb';

const collectionName = 'loanapplications'

export class LoanApplicationRepository {
    async getByInvoiceId(invoiceId: string) {

        try {
            await client.connect();

            const database = client.db(`${process.env.test_env}`);
            const collection = database.collection(collectionName);
            const query = {"invoiceDetails.invoiceId": invoiceId}

            return await collection.findOne(query)
        } catch (e) {
            console.error(e);
        } finally {
            await client.close();
        }
    }
}
