﻿using BlueTape.Common.ExceptionHandling.Exceptions.Base;
using BlueTape.SNS.SlackNotification.Models;
using System.Globalization;
using System.Net;
using System.Runtime.Serialization;

namespace BueTape.Aion.Infrastructure.Exceptions;

[Serializable]
public class AionTimeOutRequestException : DomainException
{
    public HttpStatusCode SourceHttpStatusCode { get; set; }
    public string? RequestPath { get; set; }

    public AionTimeOutRequestException(string requestPath, HttpStatusCode httpStatusCode, HttpStatusCode statusCode = HttpStatusCode.RequestTimeout)
        : base(BuildErrorMessage(), statusCode)
    {
        SourceHttpStatusCode = httpStatusCode;
        RequestPath = requestPath;
    }

    protected AionTimeOutRequestException(SerializationInfo info, StreamingContext context) : base(info, context)
    {
    }

    public override EventMessageBody GetSlackEventMessageBody(string source = "dotnet", string serviceName = "undefined", EventLevel eventLevel = EventLevel.Warning, int maxMessageLength = 400)
    {
        return new EventMessageBody
        {
            Message = $"ExceptionType: {GetType().Name} \n StatusCode: {HttpStatusCode} \n Message: " +
                      $"{Message.TruncateMessage(400)}",
            EventLevel = SourceHttpStatusCode is HttpStatusCode.OK ? EventLevel.Warning : EventLevel.Error,
            EventName = $"Error during request to external system. RequestPath: {RequestPath}",
            EventSource = source,
            ServiceName = serviceName,
            TimeStamp = DateTime.UtcNow.ToString(CultureInfo.InvariantCulture),
            AwsAccountId = Environment.GetEnvironmentVariable("LP_AWS_ACCOUNT") ??
                           "Not provided in service"
        };
    }

    private static string BuildErrorMessage()
    {
        return $"Aion service request was failed due-to timeout error";
    }

    public override string Code => ErrorCodes.AionTimeOutRequestError;
}