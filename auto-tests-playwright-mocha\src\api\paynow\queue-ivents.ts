import {sendPayNowApiRequest} from "./send-pay-now-request";

require('dotenv').config();

const queueEvents = "queueEvents";

export async function transactionStatusUpdateJob(blueTapeTransactionNumber: string, transactionStatus: string, externalTransactionNumber: string = "1221") {
    const body = {
        "blueTapeTransactionNumber": blueTapeTransactionNumber,
        "externalTransactionNumber": externalTransactionNumber,
        "externalTransactionStatus": transactionStatus,
        "errorCode": "",
        "statusMessage": "Some test message"
    };
    return await sendPayNowApiRequest('post', `${queueEvents}/transaction-status-update-job`, body);
}

/*
Functions required for status update after status was changed
*/

async function triggerPaymentScheduledJob() {
    await sendPayNowApiRequest(`post`, `${queueEvents}/payment-scheduled-job`);
}

export async function triggerCommandEventProcessorJob(commandId: string) {
    await sendPayNowApiRequest(`get`, `${queueEvents}/command-event-processor/${commandId}`);
}

export async function triggerPaymentScheduledJobWithDelay() {
    await triggerPaymentScheduledJob();
    setTimeout(triggerPaymentScheduledJob, 2000);
}
