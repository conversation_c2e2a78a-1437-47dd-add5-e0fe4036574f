using BlueTape.InvoiceService.Common.Enums;

namespace BlueTape.BackOffice.DecisionEngine.Application.Services.DrawApprovals.Models.Details;

public class DrawPayablesDetails
{
    public string Number { get; set; } = string.Empty;
    public DateOnly? DateOfUpload { get; set; }
    public DateOnly Date { get; set; }
    public DateOnly DueDate { get; set; }
    public string Status { get; set; } = string.Empty;
    public decimal MaterialSubTotal { get; set; }
    public decimal TaxAmount { get; set; }
    public decimal TotalAmount { get; set; }
    public string? Url { get; set; }
    public InvoiceType Type { get; set; }
    public bool? HasQuote { get; set; }
    public PayablesSupplierDetails SupplierDetails { get; set; } = new();
    public string? ShippingAddress { get; set; }
    public string? Attention { get; set; }
}
