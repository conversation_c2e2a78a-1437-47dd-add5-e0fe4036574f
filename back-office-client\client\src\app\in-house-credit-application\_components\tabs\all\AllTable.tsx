import type { ColumnsType } from 'antd/es/table'
import { produce } from 'immer'
import { useCallback, useMemo } from 'react'
import { useTranslation } from 'react-i18next'

import InHouseCreditApplicationTableFilter, {
  type IFilterState,
} from '@/app/in-house-credit-application/_components/tabs/components/InHouseCreditApplicationTableFilter'
import {
  useInHouseCreditApplicationFilterState,
  useInHouseCreditApplicationTableRowClick,
} from '@/app/in-house-credit-application/_components/tabs/hooks'
import { inHouseCreditApplicationTableColumns } from '@/app/in-house-credit-application/_components/tabs/utils'
import CentredSpinner from '@/components/common/CentredSpinner'
import Spacer from '@/components/common/Spacer'
import { StyledTable } from '@/components/common/Table'
import {
  useSearchParamsTableState,
  useTableSortingPagination,
} from '@/globals/hooks'
import { columnWithSorting, mapTableSorting } from '@/globals/utils'
import { useInHouseCreditApplicationListQuery } from '@/lib/redux/api/in-house-credit-application'
import type {
  IInHouseCreditApplicationListItem,
  IInHouseCreditApplicationListSortBy,
} from '@/lib/redux/api/in-house-credit-application/types'

interface IProps {
  isActive: boolean
  isTabsMounted: boolean
}

const AllTable = ({ isActive, isTabsMounted }: IProps): JSX.Element => {
  const { t } = useTranslation()

  const [filters, setListFilters] = useInHouseCreditApplicationFilterState(
    isActive,
    !isTabsMounted,
  )

  const [tableState, setTableState] =
    useSearchParamsTableState<IInHouseCreditApplicationListSortBy>(
      isActive,
      !isTabsMounted,
    )

  const { pagination, sorting, onTableChange, useTablePagination } =
    useTableSortingPagination(tableState, setTableState)

  const {
    isLoading,
    data: tableData,
    isFetching,
  } = useInHouseCreditApplicationListQuery({
    ...filters,
    ...pagination,
    ...sorting,
    sortOrder: mapTableSorting(sorting.sortOrder),
    refetchKey: 'all',
  })

  const tablePagination = useTablePagination(tableData?.totalCount ?? 0)

  const columns = useMemo<ColumnsType<IInHouseCreditApplicationListItem>>(
    () => [
      inHouseCreditApplicationTableColumns.supplierName(t),
      inHouseCreditApplicationTableColumns.businessName(t),
      inHouseCreditApplicationTableColumns.dba(t),
      inHouseCreditApplicationTableColumns.category(t),
      inHouseCreditApplicationTableColumns.applicantName(t),
      columnWithSorting(
        inHouseCreditApplicationTableColumns.decisionDate(t),
        sorting,
      ),
      columnWithSorting(
        inHouseCreditApplicationTableColumns.decisionMadeBy(t),
        sorting,
      ),
      inHouseCreditApplicationTableColumns.status(t),
      inHouseCreditApplicationTableColumns.automatedDecision(t),
      inHouseCreditApplicationTableColumns.viewLink(t),
    ],
    [t, sorting],
  )

  const onFilterChange = useCallback(
    (updater: (prevState: IFilterState) => IFilterState) => {
      setListFilters(updater)
      setTableState(
        produce((state) => {
          state.pageNumber = 1
        }),
      )
    },
    [setListFilters, setTableState],
  )

  const { onRowClick, rowClassName } =
    useInHouseCreditApplicationTableRowClick()

  if (isLoading) {
    return <CentredSpinner />
  }

  return (
    <>
      <Spacer height={28} />
      <InHouseCreditApplicationTableFilter
        onFilterChange={onFilterChange}
        defaultValue={filters}
        withSearchbar
        withAutomatedDecision
        withCategory
        withStatus
        withDecisionDateRange
      />
      <Spacer height={24} />
      <StyledTable<IInHouseCreditApplicationListItem>
        columns={columns}
        dataSource={tableData?.result}
        loading={isFetching}
        pagination={tablePagination}
        onChange={onTableChange}
        rowKey={(record) => record.id}
        scroll={{ x: 'max-content' }}
        onRow={(record) => ({
          onClick: onRowClick(record),
        })}
        rowClassName={rowClassName}
      />
    </>
  )
}

export default AllTable
