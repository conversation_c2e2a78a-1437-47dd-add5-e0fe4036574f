import axios from "axios";

export async function getSortAi() {
    const lastDayInThePreviousMonth = await getLastWeekdayInPreviousMonth();
    try {
        const url = `https://markets.newyorkfed.org/api/rates/secured/sofrai/search.json?startDate=${lastDayInThePreviousMonth}&endDate=${lastDayInThePreviousMonth}&type=rate`;
        const config = {
            headers: {
                accept: 'application/json',
            },
        };

        const response = await axios.get(url, config);
        return response;
    } catch (error) {
        console.error(error);
        throw error;
    }
}

async function getLastWeekdayInPreviousMonth() {
    const today: Date = new Date();
    const lastMonth: Date = new Date(today.getFullYear(), today.getMonth(), 0);

    while (lastMonth.getDay() === 0 || lastMonth.getDay() === 6) {
        lastMonth.setDate(lastMonth.getDate() - 1);
    }

    const year: number = lastMonth.getFullYear();
    const month: string = (lastMonth.getMonth() + 1).toString().padStart(2, '0');
    const day: string = lastMonth.getDate().toString().padStart(2, '0');

    return `${year}-${month}-${day}`;
}
  