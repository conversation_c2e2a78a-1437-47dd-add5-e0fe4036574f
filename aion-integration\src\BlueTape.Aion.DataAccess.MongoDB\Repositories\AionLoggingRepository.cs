﻿using BlueTape.Aion.DataAccess.MongoDB.Abstractions;
using BlueTape.Aion.DataAccess.MongoDB.Entities;
using BlueTape.MongoDB.Abstractions;
using BlueTape.MongoDB.Repositories;
using Microsoft.Extensions.Logging;

namespace BlueTape.Aion.DataAccess.MongoDB.Repositories;

public class AionLoggingRepository : LoggingRepository<AionLoggingEntity>, IAionLoggingRepository
{
    public AionLoggingRepository(
        IMongoDbContext context,
        ILogger<AionLoggingRepository> logger) : base(context, logger)
    {
    }
}