import {expect} from '@playwright/test';
import {BaseTest, test} from '../../test-utils';
import {
    addCustomerToCompany,
    sendGenericApiRequest
} from "../../../api/common/send-generic-api-request";
import {searchCustomerByObjectId} from "../../../database/customers/customer-searcher";
import {changePhoneNumberPatternToResponseVariant} from "../../../utils/string-operations";
import {
    generateObjectWithFieldsRequiredForAssertion, reasonStringParser
} from "../../../utils/genericAPI-utils";
import {wipeCustomerFields} from "../../../database/customers/customer-fields-wiper";
import {generateCompanyRequestBody} from './genericAPI-request-body-functions-and-storage'

/**
 * Imported files with repeating strings
 */
const constantsError = JSON.parse(JSON.stringify(require('../../../constants/generic-api-errors.json')));
const constants = JSON.parse(JSON.stringify(require('../../../constants/constants.json')));
const testMessages = JSON.parse(JSON.stringify(require('../../../constants/testMessages.json')));

/**
 * Just messages for more efficient reuse
 */
const badRequest = constantsError.common.badRequest;
const response201 = testMessages.genericApi['201'];
const response400 = testMessages.genericApi['400'];

const companyAlreadyExist = constantsError.company.code.companyAlreadyExist;
const companyWithIdAlreadyExist = constantsError.company.reason.companyWithIdAlreadyExist;

const errorMessageNonUniquePhoneOrEmail = constantsError.customer.code.errorMessageNonUnicPhoneOrEmail;
const customerWithPhoneOrEmailAlreadyExist = constantsError.customer.reason.errorWithPhoneOrEmailWhichAlreadyExist;
const customerWithIdAlreadyExist = constantsError.customer.reason.errorWithIdAlreadyExist;
const customerAlreadyExistCode = constantsError.customer.code.errorMessageNonUnicId;
const codeCustomerInvalidData = constantsError.customer.code.errorMessageInvalidData;

/**
 * @POST endpoint tests
 */

test.describe(`Company tests @genericR @API @positive @POST @company`, async () => {

    /**
     * @param currentDate - equal to source modified date in request body
     */
    let currentDate: string;

    /**
     * Values for company part of request
     */
    let uniqueBusinessPhone: string;
    let companyId: string;

    /**
     * Values for customer part of request
     */
    let uniqueCustomerPhone: string;
    let uniqueCustomerEmail: string;
    let uniqueCustomerId: string;
    const customerFirstName = constants["generic"].firstName;
    const customerLastName = constants["generic"].lastName;

    test.beforeEach(`Init values`, async () => {
        currentDate = new Date().toISOString();

        uniqueCustomerEmail = `sergei+genericapicustomer${BaseTest.dateTimePrefix()}@bluetape.com`;
        uniqueCustomerPhone = BaseTest.getCellPhoneNumber();
        uniqueCustomerId = BaseTest.getGUID();

        uniqueBusinessPhone = BaseTest.getCellPhoneNumber();
        companyId = BaseTest.getGUID();
    });

    test(`Create company with customer`, async () => {
        const companyRequestBody = await generateCompanyRequestBody({
            companyId: companyId,
            businessPhoneNumber: uniqueBusinessPhone,
            sourceModifiedDate: currentDate,
            customerFirstName: customerFirstName,
            customerLastName: customerLastName,
            customerCellPhoneNumber: uniqueCustomerPhone,
            customerEmailAddress: uniqueCustomerEmail,
            customerId: uniqueCustomerId,
            isCustomerUsed: true
        });

        const response = await sendGenericApiRequest('post', 'company', companyRequestBody);

        const createdCustomer = await response.data.customers[0];

        expect(await response.status, response201).toEqual(201);

        expect(await createdCustomer, `Response contains array with customer`)
            .toEqual(expect.any(Object));

        expect(typeof response.data["blueTapeCompanyId"]).toBe('string');

        const blueTapeCustomerId = await response.data.customers[0]["blueTapeCustomerId"];

        const DBObject = await searchCustomerByObjectId(blueTapeCustomerId);

        expect(DBObject).toEqual(expect.objectContaining(await generateObjectWithFieldsRequiredForAssertion(
            uniqueBusinessPhone,
            uniqueCustomerPhone,
            customerFirstName,
            customerLastName,
            uniqueCustomerEmail
        )));
    });

    test(`Create company with empty phone number of company and customer`, async () => {
        const companyRequestBody = await generateCompanyRequestBody({
            companyId: companyId,
            businessPhoneNumber: '',
            customerCellPhoneNumber: '',
            isCustomerUsed: true
        })

        const response = await sendGenericApiRequest('post', 'company', companyRequestBody);

        expect(await response.status, response201).toEqual(201);

        const blueTapeCustomerId = await response.data.customers[0]["blueTapeCustomerId"];

        const DBObject =
            await searchCustomerByObjectId(blueTapeCustomerId);

        expect(DBObject).toEqual(expect.objectContaining({
            'phone': '',
            'business_phone': ''
        }));

        await wipeCustomerFields(blueTapeCustomerId);
    })

    test(`Create company with empty email`, async () => {
        const body = await generateCompanyRequestBody({
            companyId: companyId,
            customerEmailAddress: '',
            isCustomerUsed: true
        });

        const response = await sendGenericApiRequest('post', 'company', body);

        expect(await response.status, response201).toEqual(201);

        const blueTapeCustomerId = await response.data.customers[0]["blueTapeCustomerId"];

        const DBObject =
            await searchCustomerByObjectId(blueTapeCustomerId);

        expect(DBObject).toEqual(expect.objectContaining({
            'email': ''
        }));

        await wipeCustomerFields(blueTapeCustomerId);
    });

    /**
     * Skipped because of "customer_company_change" error
     */
    test.skip(`Create company and add customer @skippedGeneric1`, async () => {
        const companyRequestBody = await generateCompanyRequestBody({
            companyId: companyId,
            isCustomerUsed: true
        });

        const newCustomerId = BaseTest.dateTimePrefix();

        const responseCompany = await sendGenericApiRequest('post', `company`, companyRequestBody);

        expect(await responseCompany.status, response201).toEqual(201);

        const DBObject =
            await searchCustomerByObjectId(await responseCompany.data.customers[0]["blueTapeCustomerId"]);

        const responseCustomer =
            await addCustomerToCompany(responseCompany.data.customers[0].blueTapeCustomerId, "665f014bfd39783ab3f335c0")
    })

    test.skip(`Create company without customer`, async () => {
        const companyRequestBody = await generateCompanyRequestBody({
            companyId: companyId,
            isCustomerUsed: true
        });

        const response = await sendGenericApiRequest('post', 'company', companyRequestBody);

        expect(await response.status, response201).toEqual(201);

        expect(await response.data.customers[0], `Response contains array with NO customer`)
            .toEqual(undefined);

        expect(typeof response.data["blueTapeCompanyId"]).toBe('string');

        const DBObject =
            await searchCustomerByObjectId(await response.data.customers[0]["blueTapeCustomerId"]);

        expect(DBObject).toEqual(expect.objectContaining(await generateObjectWithFieldsRequiredForAssertion(
            uniqueBusinessPhone,
            uniqueCustomerPhone,
            customerFirstName,
            customerLastName,
            uniqueCustomerEmail
        )));
    })
})

test.describe(`Company tests @genericR @API @negative @POST`, async () => {

    /**
     * @param currentDate - equal to source modified date in request body
     */
    let currentDate: string;

    const customerFirstName = constants["generic"].firstName;
    const customerLastName = constants["generic"].lastName;

    /**
     * Values for 'beforeAll', where account with valid data is created
     * for getting tests data
     */
    let uniqueCustomerPhone: string;
    let uniqueCustomerEmail: string;
    let uniqueCustomerId: string;
    let uniqueBusinessPhone: string;
    let uniqueCompanyId: string;

    /**
     * Values for 'beforeEach', where a valid data for tests is initialized
     */
    let customerPhone: string;
    let customerEmail: string;
    let customerId: string;
    let businessPhone: string;
    let companyId: string;

    /**
     * This will be used soon
     */
    let bodyForRequest: {};

    /**
     * todo describe
     */
    let blueTapeCompanyId: string;
    let blueTapeCustomerId: string;

    test.beforeAll(`Creating account with unique fields, getting required data`, async () => {
        currentDate = new Date().toISOString();

        uniqueCustomerEmail = `sergei+genericapicustomer${BaseTest.dateTimePrefix()}@bluetape.com`;
        uniqueCustomerPhone = BaseTest.getCellPhoneNumber();
        uniqueCustomerId = BaseTest.getGUID();

        uniqueBusinessPhone = BaseTest.getCellPhoneNumber();
        uniqueCompanyId = BaseTest.getGUID();

        const body = await generateCompanyRequestBody({
            companyId: uniqueCompanyId,
            businessPhoneNumber: uniqueBusinessPhone,
            sourceModifiedDate: currentDate,
            customerFirstName: customerFirstName,
            customerLastName: customerLastName,
            customerCellPhoneNumber: uniqueCustomerPhone,
            customerEmailAddress: uniqueCustomerEmail,
            customerId: uniqueCustomerId,
            isCustomerUsed: true
        });

        const response = await sendGenericApiRequest('post', 'company', body);

        blueTapeCustomerId = await response.data.customers[0]["blueTapeCustomerId"];

        expect(await response.status, `Response status code 201, generation is successful.`)
            .toEqual(201);
    });

    test.afterAll(async () => {
        await wipeCustomerFields(blueTapeCustomerId);
    });

    test.beforeEach(`Generate valid values for negative tests`, async () => {
        customerEmail = `sergei+genericapicustomer${BaseTest.dateTimePrefix()}@bluetape.com`;
        customerPhone = BaseTest.getCellPhoneNumber();
        customerId = BaseTest.getGUID();
        businessPhone = BaseTest.getCellPhoneNumber();
        companyId = BaseTest.getGUID();

        bodyForRequest = await generateCompanyRequestBody({
            companyId: companyId,
            businessPhoneNumber: businessPhone,
            sourceModifiedDate: currentDate,
            customerFirstName: customerFirstName,
            customerLastName: customerLastName,
            customerCellPhoneNumber: customerPhone,
            customerEmailAddress: customerEmail,
            customerId: customerId,
            isCustomerUsed: true
        })
    });

    test(`Cannot create Company with non-unique ID with customer`, async () => {
        const body = await generateCompanyRequestBody({
            companyId: uniqueCompanyId,
            isCustomerUsed: true
        });

        const response = await sendGenericApiRequest('post', 'company', body);

        expect(response.code).toEqual(badRequest);

        expect(response.response.status, response400)
            .toEqual(400);

        expect(response.response.data[0].code)
            .toEqual(companyAlreadyExist);

        expect(response.response.data[0].reason)
            .toEqual(await reasonStringParser(
                companyWithIdAlreadyExist,
                uniqueCompanyId, 'id'))
    });

    test(`Cannot create Company with non-unique customer phone`, async () => {
        const body = await generateCompanyRequestBody({
            companyId: companyId,
            customerCellPhoneNumber: uniqueCustomerPhone,
            isCustomerUsed: true
        });

        const response = await sendGenericApiRequest('post', 'company', body);

        expect(response.code).toEqual(badRequest);

        expect(response.response.status, response400)
            .toEqual(400);

        expect(response.response.data[0].code)
            .toEqual(errorMessageNonUniquePhoneOrEmail);

        expect(response.response.data[0].reason)
            .toContain(await reasonStringParser(
                customerWithPhoneOrEmailAlreadyExist,
                await changePhoneNumberPatternToResponseVariant(uniqueCustomerPhone),
                'phoneOrEmail'
            ));
    });

    test(`Cannot create Company with non-unique customer email`, async () => {
        const body = await generateCompanyRequestBody({
            companyId: companyId,
            customerEmailAddress: uniqueCustomerEmail,
            isCustomerUsed: true
        });

        const response = await sendGenericApiRequest('post', 'company', body);

        expect(response.code).toEqual(badRequest);

        expect(response.response.status, response400)
            .toEqual(400);

        expect(response.response.data[0].code)
            .toEqual(errorMessageNonUniquePhoneOrEmail);

        expect(response.response.data[0].reason)
            .toContain(await reasonStringParser(
                customerWithPhoneOrEmailAlreadyExist,
                uniqueCustomerEmail,
                'phoneOrEmail'
            ));
    });

    test(`Cannot create company with non-unique company id without customer`, async () => {
        const body = await generateCompanyRequestBody({
            companyId: uniqueCompanyId,
            isCustomerUsed: false
        });

        const response = await sendGenericApiRequest('post', 'company', body);

        expect(response.code)
            .toEqual(badRequest);

        expect(response.response.status, response400)
            .toEqual(400);

        expect(response.response.data[0].reason)
            .toEqual(await reasonStringParser(
                companyWithIdAlreadyExist,
                uniqueCompanyId,
                'id'
            ));

        expect(response.response.data[0].code)
            .toEqual(companyAlreadyExist);
    });

    test(`Cannot create company with non-unique customer id`, async () => {
        const body = await generateCompanyRequestBody({
            companyId: companyId,
            customerId: uniqueCustomerId,
            isCustomerUsed: true
        });

        const response = await sendGenericApiRequest('post', 'company', body);

        expect(response.code)
            .toEqual(badRequest);

        expect(response.response.status, response400)
            .toEqual(400);

        expect(response.response.data[0].reason)
            .toEqual(await reasonStringParser(
                customerWithIdAlreadyExist,
                uniqueCustomerId,
                'id'
            ));

        expect(response.response.data[0].code)
            .toEqual(customerAlreadyExistCode);
    });

    test(`Cannot create company with no company id`, async () => {
        const body = await generateCompanyRequestBody({
            companyId: '',
            isCustomerUsed: true
        });

        const response = await sendGenericApiRequest('post', 'company', body);

        expect(response.code)
            .toEqual(badRequest);

        expect(response.response.status, response400)
            .toEqual(400);

        expect(response.response.data[0].code)
            .toEqual(codeCustomerInvalidData);

        expect(response.response.data[0].reason)
            .toEqual('Integration connector can\'t be null or empty');
    });

    test(`Cannot create company with no first name`, async () => {
        const body = await generateCompanyRequestBody({
            companyId: companyId,
            customerFirstName: '',
            isCustomerUsed: true
        });

        const response = await sendGenericApiRequest('post', 'company', body);

        expect(response.code)
            .toEqual(badRequest);

        expect(response.response.status, response400)
            .toEqual(400);

        expect(response.response.data[0].code)
            .toEqual(codeCustomerInvalidData);

        expect(response.response.data[0].reason)
            .toEqual('First name could not be empty');
    });

    test(`Cannot create company with no last name`, async () => {
        const body = await generateCompanyRequestBody({
            companyId: companyId,
            customerLastName: '',
            isCustomerUsed: true
        });

        const response = await sendGenericApiRequest('post', 'company', body);

        expect(response.code)
            .toEqual(badRequest);

        expect(response.response.status, response400)
            .toEqual(400);

        expect(response.response.data[0].code)
            .toEqual(codeCustomerInvalidData);

        expect(response.response.data[0].reason)
            .toEqual('Last name could not be empty');
    });
})
