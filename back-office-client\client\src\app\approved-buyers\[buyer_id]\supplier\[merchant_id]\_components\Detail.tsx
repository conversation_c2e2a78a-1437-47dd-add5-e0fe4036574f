'use client'

import { useTranslation } from 'react-i18next'
import { useState } from 'react'

import EditInHouseCreditLimitModal from '../../../detailed/_components/modals/edit-in-house-credit-limit/EditInHouseCreditLimitModal'

import StatisticsPanelBlock from './StatisticsPanelBlock'
import SupplierTabs from './SupplierTabs'

import Spacer from '@/components/common/Spacer'
import StyledTitle from '@/components/common/typography/StyledTitle'
import BackLink from '@/components/common/BackLink'
import { AppRoutes } from '@/globals/routes'
import type { ILoanTemplate } from '@/lib/redux/api/admin/types'
import type { IApprovedBuyerSupplierItem } from '@/lib/redux/api/approved-buyers/types'
import { StyledButton } from '@/components/common/Button'

interface IProps {
  data: IApprovedBuyerSupplierItem
  companyId: string
  merchantId: string
  businessName: string
  loanTemplates?: Array<ILoanTemplate>
  backLinkHref?: string
  backLinkText?: string
}

const Detail = ({
  data,
  companyId,
  merchantId,
  businessName,
  loanTemplates,
  backLinkHref,
  backLinkText,
}: IProps): JSX.Element => {
  const { t } = useTranslation()
  const [isEditInHouseModalOpened, setIsEditInHouseCreditModalOpened] =
    useState(false)

  const openModal = () => {
    setIsEditInHouseCreditModalOpened(true)
  }

  const closeModal = () => {
    setIsEditInHouseCreditModalOpened(false)
  }

  const defaultBackLinkHref = backLinkHref ?? AppRoutes.drawApplication.main()
  const defaultBackLinkText =
    backLinkText ?? t('drawApplication.page.detailed.backToList')

  return (
    <>
      <BackLink href={defaultBackLinkHref}>{defaultBackLinkText}</BackLink>

      <Spacer height={12} />
      <StyledTitle level={2} data-testid="supplierDetailsTitle">
        {t('approvedBuyers.page.details.tabs.suppliers.details.label', {
          merchantName: data.merchantName ?? t('na'),
        })}
      </StyledTitle>

      <Spacer height={32} />

      <StatisticsPanelBlock
        data={{ creditLimit: data.currentCreditLimit, ...data.creditDetails }}
      />

      <Spacer height={30} />

      <StyledButton onClick={openModal}>
        {t('approvedBuyers.page.details.tabs.suppliers.editIHCLimit')}
      </StyledButton>

      <Spacer height={30} />
      <SupplierTabs
        companyId={companyId}
        merchantId={merchantId}
        businessName={businessName}
        loanTemplates={loanTemplates}
        supplierData={data}
      />

      <EditInHouseCreditLimitModal
        creditId={data.creditDetails.id}
        isOpened={isEditInHouseModalOpened}
        onClose={closeModal}
        currentLimit={data.currentCreditLimit ?? 0}
        merchantName={data.merchantName}
        merchantId={merchantId}
      />
    </>
  )
}

export default Detail
