using System.Runtime.Serialization;
using System.Text.Json.Serialization;

namespace BlueTape.Aion.DataAccess.External.Models.WireTransfer.Requests;

[DataContract]
public class WireObjRequest
{
    [JsonPropertyName("accountId")]
    public string AccountId { get; set; }

    [JsonPropertyName("accountNumber")]
    public string AccountNumber { get; set; }

    [JsonPropertyName("counterpartyId")]
    public string CounterpartyId { get; set; }

    [JsonPropertyName("transferMethodId")]
    public string TransferMethodId { get; set; }

    [JsonPropertyName("amount")]
    public string Amount { get; set; }

    [JsonPropertyName("purpose")]
    public string Purpose { get; set; }

    [JsonPropertyName("contextIdentifier")]
    public string ContextIdentifier { get; set; }

    [Json<PERSON>ropertyName("remittanceInfo")]
    public string RemittanceInfo { get; set; }
}