﻿using BlueTape.Common.ExceptionHandling.Exceptions.Base;
using System.Net;
using System.Runtime.Serialization;

namespace BueTape.Aion.Infrastructure.Exceptions;

public class InvalidPaymentSubscriptionType : DomainException
{
    public InvalidPaymentSubscriptionType(string paymentSubscription, HttpStatusCode statusCode = HttpStatusCode.InternalServerError) : base(BuildErrorMessage(paymentSubscription), statusCode)
    {
    }

    public InvalidPaymentSubscriptionType(SerializationInfo info, StreamingContext context) : base(info, context)
    {
    }

    private static string BuildErrorMessage(string paymentSubscription)
    {
        return $"Invalid payment subscription: {paymentSubscription}. Payment subscription does not exist.";
    }

    public override string Code => ErrorCodes.InvalidPaymentSubscriptionType;
}
