import {BaseTest, test} from "../../test-utils";
import {
    findRequiredPaymentRequest, getPaymentRequestExist,
    getRequiredPaymentRequestByDay,
    getRequiredPaymentRequestById, updatedTransactionsInfo
}
    from "../../../api/paynow/send-pay-now-request";
import {getCurrentDay} from "../../../api/base-api";
import {expect} from "@playwright/test";
import * as _ from 'lodash';
import {transactionStatusUpdateJob, triggerPaymentScheduledJobWithDelay} from "../../../api/paynow/queue-ivents";

const payNowData = JSON.parse(JSON.stringify(require('../../../constants/PayNow-data.json')));

test.describe(`Pay now API positive full path @paynow`, async () => {

    /**
    Each test depends on the execution of the previous ones
    A payment request is created that step by step changes the statuses of its transactions
    */

    const totalAmountString = Math.floor(Math.random() * (200 - 2) + 2).toString() + "." + Math.floor(Math.random() * 99).toString();
    const day = getCurrentDay();
    const sellerId = process.env.SELLER_ID;
    const payerId = process.env.PAYER_ID;
    const payeeId = process.env.PAYEE_ID;

    /**
    Data that tests receive from each other
    */

    let requiredPaymentId;
    let requiredPaymentRequest;
    let allPaymentRequests;
    let requiredPaymentRequestGettedById;
    let requiredPaymentRequestGettedByIdData;
    let paymentStatus;
    let transactions;

    /**
    Trigger payment job for transactions sync
    */

    test.afterEach(async () => {
        await triggerPaymentScheduledJobWithDelay();
    });

    /**
    Create invoice via UI
    Get all required data from response
    */

    test(`Invoice creating.`, async ({pageManager, supplierForPayNowPageManager}) => {
        const invoiceNumber = BaseTest.dateTimePrefix() + 'invoiceNumber';
        await pageManager.onBoardingPage.createInvoiceViaUI(invoiceNumber, totalAmountString);
        await supplierForPayNowPageManager.payModal.openInvoicesPage();
        await supplierForPayNowPageManager.invoicesList.clickOnFirstRequiredInvoice();
        await supplierForPayNowPageManager.invoicesDetails.payForLatestInvoice();

        allPaymentRequests = await getRequiredPaymentRequestByDay(day);
        requiredPaymentRequest = await findRequiredPaymentRequest(allPaymentRequests, +totalAmountString);
        requiredPaymentId = await requiredPaymentRequest.id;
        requiredPaymentRequestGettedById = await getRequiredPaymentRequestById(requiredPaymentId);
        requiredPaymentRequestGettedByIdData = await requiredPaymentRequestGettedById.data;
        paymentStatus = await requiredPaymentRequestGettedByIdData.status;
    });

    /**
    Deep check data of payment request
    because at the moment there is no way to quickly get an ID of payment request
    */

    test(`PaymentRequest is created right.`, async () => {
        expect(requiredPaymentRequestGettedById.status).toEqual(200);
        expect(requiredPaymentRequestGettedByIdData.id).toEqual(requiredPaymentId);
        expect(requiredPaymentRequestGettedByIdData.date).toEqual(day);
        expect(requiredPaymentRequestGettedByIdData.payeeId).toEqual(payeeId);
        expect(requiredPaymentRequestGettedByIdData.payerId).toEqual(payerId);
        expect(requiredPaymentRequestGettedByIdData.sellerId).toEqual(sellerId);
        expect(requiredPaymentRequestGettedByIdData.amount).toEqual(+totalAmountString);
        expect(paymentStatus).toEqual(payNowData.statuses.Processing);
        expect(requiredPaymentRequestGettedByIdData.flowTemplateCode).toEqual(payNowData.flowTemplateCode);
    });

    /**
    Check amount and status of transaction
    */

    test(`Check data of first transaction.`, async () => {
        transactions = await updatedTransactionsInfo(requiredPaymentId);
        const transaction = _.find(transactions, {sequenceNumber: 1});
        expect(transaction.amount).toEqual(+totalAmountString);
        expect(transaction.status).toEqual(payNowData.statuses.Processing);
    });

    /**
    Triggering a change in transaction status according to "happy path"
    */

    test(`Trigger first transaction by id, Processing=>Processed. Transaction must change status.`, async () => {
        transactions = await updatedTransactionsInfo(requiredPaymentId);
        const transactionNumber = await _.find(transactions, {sequenceNumber: 1}).transactionNumber;
        await transactionStatusUpdateJob(await transactionNumber, payNowData.externalStatuses.Sent);
        transactions = await updatedTransactionsInfo(requiredPaymentId);
        const transactionAfterUpdate = await _.find(transactions, {sequenceNumber: 1});
        expect(transactionAfterUpdate.status).toEqual(payNowData.statuses.Processed);
    });

    test(`Trigger first transaction by id, Processed=>Cleared. Transaction must change status.`, async () => {
        transactions = await updatedTransactionsInfo(requiredPaymentId);
        const transactionNumber = await _.find(transactions, {sequenceNumber: 1}).transactionNumber;
        await transactionStatusUpdateJob(await transactionNumber, payNowData.externalStatuses.Cleared);
        transactions = await updatedTransactionsInfo(requiredPaymentId);
        const transactionAfterUpdate = await _.find(transactions, {sequenceNumber: 1});
        expect(transactionAfterUpdate.status).toEqual(payNowData.statuses.Cleared);
    });

    /**
    Check amount and status of transaction
    */

    test(`Check data of second transaction.`, async () => {
        transactions = await updatedTransactionsInfo(requiredPaymentId);
        const transaction = _.find(transactions, {sequenceNumber: 2});
        expect(transaction.amount.toString()).toEqual((+totalAmountString - payNowData.fee).toFixed(2).toString());
        expect(transaction.status).toEqual(payNowData.statuses.Processing);
    });

    /**
   Triggering a change in transaction status according to "happy path"
    */

    test(`Trigger second transaction by id, Processing=>Processed. Transaction must change status.`, async () => {
        transactions = await updatedTransactionsInfo(requiredPaymentId);
        const transactionNumber = await _.find(transactions, {sequenceNumber: 2}).transactionNumber;
        await transactionStatusUpdateJob(await transactionNumber, payNowData.externalStatuses.Sent);
        transactions = await updatedTransactionsInfo(requiredPaymentId);
        const transactionAfterUpdate = await _.find(transactions, {sequenceNumber: 2});
        expect(transactionAfterUpdate.status).toEqual(payNowData.statuses.Processed);
    });

    test(`Trigger second transaction by id, Processed=>Cleared. Transaction must change status.`, async () => {
        transactions = await updatedTransactionsInfo(requiredPaymentId);
        const transactionNumber = await _.find(transactions, {sequenceNumber: 2}).transactionNumber;
        await transactionStatusUpdateJob(await transactionNumber, payNowData.externalStatuses.Cleared);
        transactions = await updatedTransactionsInfo(requiredPaymentId);
        const transactionAfterUpdate = _.find(transactions, {sequenceNumber: 2});
        expect(transactionAfterUpdate.status).toEqual(payNowData.statuses.Cleared);
    });

    /**
    Check amount and status of transaction
    */

    test(`Check data of third transaction.`, async () => {
        transactions = await updatedTransactionsInfo(requiredPaymentId);
        const transaction = _.find(transactions, {sequenceNumber: 3});
        expect(transaction.amount).toEqual(payNowData.fee);
        expect(transaction.status).toEqual(payNowData.statuses.Processing);
    });

    /**
   Triggering a change in transaction status according to "happy path"
   */

    test(`Trigger third transaction by id, Processing=>Processed. Transaction must change status.`, async () => {
        transactions = await updatedTransactionsInfo(requiredPaymentId);
        const transactionNumber = await _.find(transactions, {sequenceNumber: 3}).transactionNumber;
        await transactionStatusUpdateJob(await transactionNumber, payNowData.externalStatuses.Sent);
        transactions = await updatedTransactionsInfo(requiredPaymentId);
        const transactionAfterUpdate = _.find(transactions, {sequenceNumber: 3});
        expect(transactionAfterUpdate.status).toEqual(payNowData.statuses.Processed);
    });

    test(`Trigger third transaction by id, Processed=>Cleared. Transaction must change status.`, async () => {
        transactions = await updatedTransactionsInfo(requiredPaymentId);
        const transactionNumber = await _.find(transactions, {sequenceNumber: 3}).transactionNumber;
        await transactionStatusUpdateJob(await transactionNumber, payNowData.externalStatuses.Cleared);
        transactions = await updatedTransactionsInfo(requiredPaymentId);
        const transactionAfterUpdate = _.find(transactions, {sequenceNumber: 3});
        expect(transactionAfterUpdate.status).toEqual(payNowData.statuses.Cleared);
    });

    /**
    Check amount and status of transaction
    */

    test(`Check data of fourth transaction.`, async () => {
        transactions = await updatedTransactionsInfo(requiredPaymentId);
        const transaction = _.find(transactions, {sequenceNumber: 4});
        expect(transaction.amount.toString()).toEqual((+totalAmountString - payNowData.fee).toFixed(2));
        expect(transaction.status).toEqual(payNowData.statuses.Processing);
    });

    /**
   Triggering a change in transaction status according to "happy path"
   */

    test(`Trigger fourth transaction by id, Processing=>Processed. Transaction must change status.`, async () => {
        transactions = await updatedTransactionsInfo(requiredPaymentId);
        const transactionNumber = await _.find(transactions, {sequenceNumber: 4}).transactionNumber;
        await transactionStatusUpdateJob(await transactionNumber, payNowData.externalStatuses.Sent);
        transactions = await updatedTransactionsInfo(requiredPaymentId);
        const transactionAfterUpdate = _.find(transactions, {sequenceNumber: 4});
        expect(transactionAfterUpdate.status).toEqual(payNowData.statuses.Processed);
    });

    test(`Trigger fourth transaction by id, Processed=>Cleared. Transaction must change status.`, async () => {
        transactions = await updatedTransactionsInfo(requiredPaymentId);
        const transactionNumber = await _.find(transactions, {sequenceNumber: 4}).transactionNumber;
        await transactionStatusUpdateJob(await transactionNumber, payNowData.externalStatuses.Cleared);
        transactions = await updatedTransactionsInfo(requiredPaymentId);
        const transactionAfterUpdate = _.find(transactions, {sequenceNumber: 4});
        expect(transactionAfterUpdate.status).toEqual(payNowData.statuses.Cleared);
    });

    /**
   Existence check of payment request by specific endpoint
   */

    test(`Is payment request exist for payable.`, async () => {
        const payableInfo = await getRequiredPaymentRequestById(requiredPaymentId);
        const payableId = await payableInfo.data.paymentRequestPayables[0].payableId;
        const response = await getPaymentRequestExist(payableId);
        expect(await response.status).toEqual(200);
    });
});
