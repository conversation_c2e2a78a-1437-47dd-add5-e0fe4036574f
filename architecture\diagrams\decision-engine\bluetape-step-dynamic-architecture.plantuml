@startuml

title BlueTape step

participant "Previous Step" as ps #LightGray
participant "BT" as bt #LightGray
participant "OnBoarding\nService" as onbs #SkyBlue
participant "Company\nService" as cs #SkyBlue
participant "Loan\nService" as ls #SkyBlue
database "Mongo" as db #SkyBlue
participant "Next step" as next

autonumber

ps -> bt

== Event Wrapper ==
bt -> onbs : Check loan application
bt -> onbs : Create draft if not exists
bt -> onbs : Updating progress
bt -> onbs : Save previous outputs

== Collect loan info step ==

bt -> ls: Get business outstanding amount
bt -> onbs: Get failed applications count
bt -> ls: Get late loans count

== Check existing company step ==

bt -> onbs: If draft not normalized, fail step
bt -> cs: Get company and owners by ein and ssn hash matches

@enduml