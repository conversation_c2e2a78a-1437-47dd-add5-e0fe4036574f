﻿using BlueTape.Aion.DataAccess.External.Abstractions;
using BlueTape.Aion.DataAccess.External.Constants;
using BlueTape.Aion.DataAccess.External.Models.Login;
using BlueTape.Common.ExceptionHandling.Exceptions;
using BlueTape.Integrations.Aion.Infrastructure.Enums;
using Microsoft.Extensions.Configuration;

namespace BlueTape.Aion.DataAccess.External;

public class AionCredentialsManager(IConfiguration configuration) : IAionCredentialsManager
{
    public LoginRequestModel? GetAionCredentials(PaymentSubscriptionType paymentSubscription)
    {
        var aionCredentialsSecrets = GetAionCredentialSecrets(paymentSubscription);

        var userId = configuration[aionCredentialsSecrets.UserIdSecret] ??
                     throw new VariableNullException(nameof(ClientConstants.AionUserId));
        var password = configuration[aionCredentialsSecrets.PasswordSecret] ??
                       throw new VariableNullException(nameof(ClientConstants.AionPassword));
        var apiKey = configuration[aionCredentialsSecrets.ApiKeySecret] ??
                     throw new VariableNullException(nameof(ClientConstants.AionExternalApiKey));

        return new LoginRequestModel
        {
            UserId = userId,
            Password = password,
            ApiKey = apiKey
        };

    }

    private static AionCredentialSecretsModel GetAionCredentialSecrets(PaymentSubscriptionType paymentSubscription)
    {
        var subscriptionNumber = (int)paymentSubscription;
        if (subscriptionNumber == 1) return new AionCredentialSecretsModel()
        {
            ApiKeySecret = ClientConstants.AionExternalApiKey,
            PasswordSecret = ClientConstants.AionPassword,
            UserIdSecret = ClientConstants.AionUserId
        };

        return new AionCredentialSecretsModel()
        {
            ApiKeySecret = $"{ClientConstants.AionExternalApiKey}{subscriptionNumber}",
            PasswordSecret = $"{ClientConstants.AionPassword}{subscriptionNumber}",
            UserIdSecret = $"{ClientConstants.AionUserId}{subscriptionNumber}",
        };
    }
}
