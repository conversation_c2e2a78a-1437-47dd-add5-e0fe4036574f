openapi: "3.0.0"
info:
  version: "0.0.1"
  title: "Projects API (as a part of Invoices Service)"
  description: |
    API definition of Projects functions
servers:
  - url: TBD-Dev
    description: Development server
  - url: TBD-Prod
    description: Production server
paths:
  /projects:
    post:
      tags:
        - projects
      summary: Creates a new project
      description: Creates a new project
      operationId: createProject
      parameters:
        - name: companyId
          description: Identifier of the company
          example: 62c5e900cd65d31d3f25c34c
          in: header
          required: true
          schema:
            type: string
            nullable: false
      requestBody:
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/CreateProject"
      responses:
        201:
          description: The project was created
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Project'
        400:
          description: Bad request
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
    get:
      tags:
        - projects
      summary: Gets all projects by various filters
      description: Gets all projects by various filters
      operationId: getProjects
      parameters:
        - name: id
          description: The project id
          in: query
          required: false
          schema:
            type: string
        - name: companyId
          description: The company id
          in: query
          required: false
          schema:
            type: string
        - name: status
          description: The status
          in: query
          required: false
          schema: 
            type: string
            enum:
              - incomplete
              - inreview
              - approved
              - rejected
        - name: detailed
          description: Detailed view
          in: query
          required: false
          schema:
            type: boolean
            default: false
        - name: jobIdOrAddress
          description: Search in job id or address field
          in: query
          required: false
          schema:
            type: string
        - name: jobEndDateFrom
          description: The job end date from filter
          in: query
          required: false
          schema:
            type: string
            format: date
          example: 2020-01-01
        - name: jobEndDateTo
          description: The job end date to filter
          in: query
          required: false
          schema:
            type: string
            format: date
          example: 2029-12-31
        - name: pageNumber
          description: The page number
          in: query
          required: false
          schema:
            type: number
            format: int64
          example: 5
        - name: pageSize
          description: The page size
          in: query
          required: false
          schema:
            type: number
            format: int64
          example: 50
        - name: sortBy
          description: Sort by
          in: query
          required: false
          schema:
            type: string
            enum:
              - contractValue
        - name: sortOrder
          description: The sort order to use, default asc
          in: query
          required: false
          schema:
            type: string
            enum:
              - asc
              - desc
      responses:
        200:
          description: The projects
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/PagedProjects'
        400:
          description: Bad request
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
  /projects/{id}:
    get:
      tags:
        - projects
      summary: Gets a project by id (just for compatibility)
      description: Gets a project by id (just for compatibility)
      operationId: getProjectById
      parameters:
        - name: id
          description: The id of project
          in: path
          required: true
          schema:
            type: string
        - name: detailed
          description: Detailed view
          in: query
          required: false
          schema:
            type: boolean
            default: false
      responses:
        200:
          description: The project
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Project'
        400:
          description: Bad request
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
    patch:
      tags:
        - projects
      summary: Updates a project by id
      description: Updates a project by id
      operationId: updateProjectById
      parameters:
        - name: id
          description: The id of project
          in: path
          required: true
          schema:
            type: string
        - name: companyId
          description: Identifier of the company
          example: 62c5e900cd65d31d3f25c34c
          in: header
          required: true
          schema:
            type: string
            nullable: false
      requestBody:
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/UpdateProject"
      responses:
        200:
          description: The project
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Project'
        400:
          description: Bad request
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
  /admin/projects/{id}/status:
    patch: 
      tags:
      - projects Admin
      summary: Updates project approval status (admin function)
      description: Updates project approval status (admin function)
      operationId: updateProjectStatusById
      parameters:
        - name: id
          description: The id of project
          in: path
          required: true
          schema:
            type: string
        - name: userId
          description: Identifier of the user who updated the status
          example: 62c5e900cd65d31d3f25c34c
          in: header
          required: true
          schema:
            type: string
            nullable: false
      requestBody:
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/PatchProjectStatus"
      responses:
        200:
          description: The patched project
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Project'
        400:
          description: Bad request
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
  /admin/projects/{id}/contractdetails:
    patch: 
      tags:
      - projects Admin
      summary: Updates project's approved contact value and other details (admin function)
      description: Updates project's approved contact value and other details (admin function) All fields are optional. Which field is sent, that field to update.
      operationId: updateProjectContractDetailsById
      parameters:
        - name: id
          description: The id of project
          in: path
          required: true
          schema:
            type: string
        - name: userId
          description: Identifier of the user who updated the contract details
          example: 62c5e900cd65d31d3f25c34c
          in: header
          required: true
          schema:
            type: string
            nullable: false
      requestBody:
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/PatchProjectContractDetails"
      responses:
        200:
          description: The patched project
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Project'
        400:
          description: Bad request
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
components:
  schemas:
    ErrorResponse:
      type: object
      required:
        - statusCode
        - message
        - errorDescription
      properties:
        statusCode:
          description: HTTP status code corresponding to the error.
          type: integer
          example: 400
        message:
          description: A human readable error message.
          type: string
          example: Invalid model.
        errorDescription:
          description: Detailed information.
          type: string
    Project:
      allOf:
        - type: object
          required:
            - id
          properties:
            id:
              type: string
              format: guid
              description: Id of project
              example: d3a5d889-47e4-4321-b87b-f816ce5e7b6e
            createdAt:
              type: string
              format: date-time
            updatedAt:
              type: string
              format: date-time
            companyId:
              type: string
            status:
              type: string
              enum:
                - incomplete
                - inreview
                - approved
                - rejected
              default: incomplete
              example: incomplete
            statusCode:
              type: string
            statusNote:
              type: string
            approvedAt:
              type: string
              format: date-time
              nullable: true
            approvedBy:
              type: string
              nullable: true
            rejectedAt:
              type: string
              format: date-time
              nullable: true
            rejectedBy:
              type: string
              nullable: true
        - $ref: '#/components/schemas/CreateProject'
        - type: object
          properties:
            creditDetails:
              $ref: '#/components/schemas/CreditDetails'
    UpdateProject:
      allOf:
        - $ref: '#/components/schemas/CreateProject'
    CreateProject:
      type: object
      properties:
        name:
          type: string
        isDeactivated:
          type: boolean
        address:
          type: string
        requestedValue:
          type: number
        contractValue:
          type: number
        startDate:
          type: string
          format: date
        endDate:
          type: string
          format: date
        jobId:
          type: string
        jobAddress:
          $ref: "#/components/schemas/JobAddress"
        role:
          type: string
          enum:
            - primeOrGeneralContractor
            - subContractor
            - subContractorForAnotherSubContractor
            - materialSupplier
            - propertyDeveloper
            - owner
            - tenant
        primeContractorDetails:
          $ref: "#/components/schemas/PrimeContractorDetails"
          nullable: true
        type:
          type: string
          enum:
            - private
            - public
            - federal
        privateProjectDetails:
          $ref: "#/components/schemas/PrivateProjectDetails"
          nullable: true
        publicProjectDetails:
          $ref: "#/components/schemas/PublicProjectDetails"
          nullable: true
        federalProjectDetails:
          $ref: "#/components/schemas/FederalProjectDetails"
          nullable: true
        individualOwners:
          type: array
          items:
            $ref: "#/components/schemas/IndividualOwner"
        businessOwners:
          type: array
          items:
            $ref: "#/components/schemas/BusinessOwner"
    PagedProjects:
      type: object
      properties:
        pageNumber:
          type: number
        pagesCount:
          type: number
        totalCount:
          type: number
        result:
          type: array
          items:
            $ref: "#/components/schemas/Project"
    JobAddress:
      type: object
      properties:
        address:
          type: string
        unitNo:
          type: string
        state:
          type: string
        zipCode:
          type: string
        city:
          type: string
        lotNumber:
          type: string
          nullable: true
        blockNumber:
          type: string
          nullable: true
        fileUrl:
          type: string
          nullable: true
    PrimeContractorDetails:
      type: object
      properties:
        businessName:
          type: string
        firstName:
          type: string
        lastName:
          type: string
        businessPhoneNumber:
          type: string
        businessEmail:
          type: string
        businessAddress:
          type: string
        state:
          type: string
        city:
          type: string
        zipCode:
          type: string
    PrivateProjectDetails:
      type: object
      properties:
        privateProjectType:
          type: string
          enum: 
            - commercial
            - residential
        builtFor:
          $ref: "#/components/schemas/BuiltFor"
    PublicProjectDetails:
      type: object
      properties:        
        description:  
          type: string
        hasBond:
          type: boolean
        isBondRequired:
          type: boolean
        fileUrl:
          type: string
          nullable: true
    FederalProjectDetails:
      type: object
      properties:
        hasBond:
          type: boolean
        fileUrl:
          type: string
          nullable: true
    IndividualOwner:
      type: object
      properties:
        firstName:
          type: string
        lastName:
          type: string
        phone:
          type: string
        homeAddress:
          type: string
        state:
          type: string
        city:
          type: string
        zipCode:
          type: string
    BusinessOwner:
      type: object
      properties:
        businessName:
          type: string
        firstName:
          type: string
        lastName:
          type: string
        businessPhoneNumber:
          type: string
        businessAddress:
          type: string
        state:
          type: string
        city:
          type: string
        zipCode:
          type: string
    BuiltFor:
      type: object
      properties:
        buildForType:
          type: string
          enum:
            - spec
            - propertyOwnerNewBuilding
            - propertyOwnerRenovation
            - other
        notes:
          type: string
    PatchProjectStatus:
      type: object
      properties:
        newStatus:
          type: string
          enum:
            - approved
            - rejected
          example: approved
          description: The project approval status
        contractValue:
          type: number
          format: decimal
          example: 500000.00
          pattern: '[0-9]*\.[0-9]{2}'
          multipleOf: 0.01
          description: The contract value, the project spending limit
        endDate:
          type: string
          format: date
          example: 2024-04-24
          description: The end date of project
        note:
          type: string
          description: Note by backoffice user
    CreditDetails:
      type: object
      description: Details of contract value, the spending amount. Filled only if detailed view requested.
      properties:
        numberOfDraws:
          type: number
          format: int32
          example: 5
          description: Number of attached draws to the project
        numberOfPayables:
          type: number
          format: int32
          example: 8
          description: Number of invoices (payables) attached to the project
        availableCredit:
          type: number
          format: decimal
          example: 10000.00
          pattern: '[0-9]*\.[0-9]{2}'
          multipleOf: 0.01
          description: Available spending amount. Contract amount minus used amount. One of these fields is calculated (see contractValue)
        usedCredit:
          type: number
          format: decimal
          example: 4555.00
          pattern: '[0-9]*\.[0-9]{2}'
          multipleOf: 0.01
          description: Used spending amount. Sum of the attached invoices. One of these fields is calculated (see contractValue)
        usedCreditPercentage:
          type: number
          format: decimal
          example: 45.55
          pattern: '[0-9]*\.[0-9]{2}'
          multipleOf: 0.01
          description: The ratio of contract value and the used amount. Calculated field
    PatchProjectContractDetails:
      type: object
      properties:
        contractValue:
          type: number
          format: decimal
          example: 500000.00
          pattern: '[0-9]*\.[0-9]{2}'
          multipleOf: 0.01
          description: The new contract value, the new spending limit
        endDate:
          type: string
          format: date
          example: 2024-04-24
          description: The new end date of project
  securitySchemes:
    ApiKey:
      type: apiKey
      in: header
      name: X-Api-Key
security:
  - ApiKey: []  
