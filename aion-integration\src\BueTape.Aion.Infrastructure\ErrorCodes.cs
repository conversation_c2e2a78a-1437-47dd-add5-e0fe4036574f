﻿using System.Diagnostics.CodeAnalysis;

namespace BueTape.Aion.Infrastructure;

[ExcludeFromCodeCoverage]
public static class ErrorCodes
{
    public static readonly string ProvidedServiceNull = "di_configuration_does_not_exist";
    public static readonly string RequestNull = "request_is_null";
    public static readonly string ResponseNull = "response_is_null";
    public static readonly string AionUnexpectedRequestError = "aion_unexpected_request_error";
    public static readonly string AionRequestError = "aion_request_error";
    public static readonly string AionDailyLimitRequestError = "aion_daily_limit_request_error";
    public static readonly string AionLoginRequestError = "aion_login_request_error";
    public static readonly string AionTimeOutRequestError = "aion_time_out_request_error";
    public static readonly string AionTooManyRequestError = "aion_too_many_request_error";
    public static readonly string CompanyDoesNotExist = "company_does_not_exist";
    public static readonly string BankAccountDoesNotExist = "bankaccount_does_not_exist";
    public static readonly string ParameterRequired = "parameter_required";
    public static readonly string UnableToDecryptParameter = "unable_to_decrypt";
    public static readonly string AionBankAccountDoesNotExist = "aion_bankaccount_does_not_exist";
    public static readonly string ParameterNotExist = "parameter_not_exist";
    public static readonly string MissedSecrets = "missed_secrets";
    public static readonly string InvalidPaymentSubscriptionType = "invalid_payment_subscription_type";
    public static readonly string AionAccountDoesNotExist = "aion_account_does_not_exist";
    public static readonly string CompanyDoesNotHaveName = "bankaccount_does_not_have_name";

    public static readonly IReadOnlyDictionary<string, string> Errors = new Dictionary<string, string>
    {
        {ProvidedServiceNull, "DI configuration was not set up correctly"},
        {RequestNull, "Unable to process null request"},
        {ResponseNull, "Unable to process null response"},
        {AionUnexpectedRequestError, "Aion respond with error for incoming request, details provided in error data"},
        {CompanyDoesNotExist, "Company with id provided in request does not exist"},
        {BankAccountDoesNotExist, "BankAccount with id provided in request does not exist"},
        {ParameterRequired, "Parameter required but was not provided."},
        {UnableToDecryptParameter, "Can not decrypt parameter"},
        {AionBankAccountDoesNotExist, "Unable to find bankaccount in aion system, by AccountNumber and Routing number from origination BankAccountId"},
        {ParameterNotExist, "Parameter not exist"},
        {AionTimeOutRequestError, "Aion request was failed due to timeout exception"},
        {MissedSecrets, "Some of the necessary secrets are missed on provided environment"},
        {AionTooManyRequestError, "Request was failed due to too many request. Normally it is 30 requests per minute for all endpoints"},
        {AionAccountDoesNotExist, "Internal aion account does not exist"},
        {AionLoginRequestError, "Unable to login"},
        {AionRequestError, "Aion request failed due to business logic conditions"},
        {AionDailyLimitRequestError, "Aion request failed due to daily limit reached"},
    };

    public static readonly IReadOnlyDictionary<string, string> AionErrors = new Dictionary<string, string>
    {
        {AionRequestError, "Aion respond with error for incoming request, details provided in error data"},
        {AionDailyLimitRequestError, "Aion request failed due to daily limit reached"},
    };
}