﻿using System.Diagnostics.CodeAnalysis;
using BlueTape.Company.API.DataAccess;
using BlueTape.DataAccess.MongoDB.Abstractions;
using BlueTape.DataAccess.MongoDB.Mappers;
using BlueTape.DataAccess.MongoDB.Repositories;
using BlueTape.Document.DataAccess.EF.Abstractions;
using BlueTape.Document.DataAccess.EF.Repositories;
using Microsoft.Extensions.DependencyInjection;

namespace BlueTape.DataAccess.MongoDB.DI;

[ExcludeFromCodeCoverage]
public static class DependencyRegistrar
{
    public static void AddMongoDataAccessDependencies(this IServiceCollection services)
    {
        services.AddAutoMapper(typeof(MongoDbProfile));
        services.AddScoped<CompanyDbContext>();

        services.AddTransient<ICompanyRepository, CompanyRepository>();
        services.AddTransient<IBankAccountRepository, BankAccountRepository>();
        services.AddTransient<ICustomerRepository, CustomerRepository>();
        services.AddTransient<ILoanApplicationRepository, LoanApplicationRepository>();
        services.AddTransient<IAccountStatusChangeAuditRepository, AccountStatusChangeAuditRepository>();
        services.AddTransient<IUserRepository, UserRepository>();
        services.AddTransient<IUserRoleRepository, UserRoleRepository>();
        services.AddTransient<IDraftRepository, DraftRepository>();
    }
}