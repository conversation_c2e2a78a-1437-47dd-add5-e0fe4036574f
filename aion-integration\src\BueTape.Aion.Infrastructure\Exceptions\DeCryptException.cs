﻿using BlueTape.Common.ExceptionHandling.Exceptions.Base;
using System.Net;
using System.Runtime.Serialization;

namespace BueTape.Aion.Infrastructure.Exceptions;

[Serializable]
public class DeCryptException : DomainException
{
    public DeCryptException(string message, HttpStatusCode statusCode = HttpStatusCode.BadRequest) : base(message, statusCode)
    {
    }

    protected DeCryptException(SerializationInfo info, StreamingContext context) : base(info, context)
    {
    }

    public override string Code => ErrorCodes.UnableToDecryptParameter;
}