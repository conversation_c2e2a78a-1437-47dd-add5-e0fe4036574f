variable "environment" {
  type    = string
}

variable "application_name" {
  type = map(any)
}

variable "key_vault_id" {
  type    = string
}

variable "resource_group_name" {
  type    = string
}

variable "resource_group_location" {
  type    = string
}

variable "external_transaction_queue_name" {
  default = "aionExternalTransactionQueueName"
}

variable "external_transaction_queue_connection" {
  default = "aionExternalTransactionQueueConnection"
}

variable "internal_transaction_queue_name" {
  default = "aionInternalTransactionQueueName"
}

variable "internal_transaction_queue_connection" {
  default = "aionInternalTransactionQueueConnection"
}
