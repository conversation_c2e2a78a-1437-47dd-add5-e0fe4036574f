﻿import {client} from "../client";
import TestReport, {ITestReport} from "./entities/AutoTestReport";

const collectionName = 'auto-test-reports'; // Collection name

export async function insertTestReport(testReport: ITestReport) {
    try {
        await client.connect();
        
        const database = client.db(`${process.env.test_env}`);
        const collection = database.collection(collectionName);
        await collection.insertOne(testReport as any);
    } catch (e) {
        console.error(e);
    } finally {
        await client.close();
    }
}