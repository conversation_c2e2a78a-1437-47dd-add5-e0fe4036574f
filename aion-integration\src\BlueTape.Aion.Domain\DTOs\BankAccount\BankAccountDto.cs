﻿namespace BlueTape.Aion.Domain.DTOs.BankAccount;

public class BankAccountDto
{
    public string BlueTapeBankAccountId { get; set; } = null!;
    public string? Name { get; set; }
    public BankAccountAionSettingsDto? AionSettings { get; set; }
    public string? AccountType { get; set; }
    public string? PaymentMethodType { get; set; }
    public string? RoutingNumber { get; set; }
    public BankAccountNumberDto? AccountNumber { get; set; }
    public BankAccountIdentityDto? Identity { get; set; }
}