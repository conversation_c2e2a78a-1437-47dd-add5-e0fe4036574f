import sendUserRequest from '../common/send-user-request';

export async function getInvoices(session:string, challenge:string, invoiceNumber:string){
    const endpoint = `v1/invoices/invoice?currentDate=03-01-2023&status=EXCEPT_DRAFT&type=invoice&duration=0&search=${invoiceNumber}`;
    try {
        const response = await sendUserRequest('get', endpoint, session, challenge);
        const bodyString = await response.toString();
        const bodyJSON = JSON.parse(bodyString);
        return bodyJSON;
    } catch (error) {
        console.log(error);
        return error;
    }
}

export async function getInvoiceStatus(session:string, challenge:string, invoiceNumber:string) {
    const response = await getInvoices(session, challenge, invoiceNumber);
    return response.invoices[0]?.status;
}
