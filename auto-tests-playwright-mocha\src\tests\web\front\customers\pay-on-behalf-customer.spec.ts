import {expect} from '@playwright/test';
import {test, BaseTest} from '../../../test-utils';
import {BaseAPI} from '../../../../api/base-api';
import {deleteUser, getUserSub} from "../../../../api/admin";
import {createCustomer, getInvoiceStatus} from "../../../../api/user";
import {setTransactionNumber} from '../../../../database/transactions/set-transaction-number';
import {deleteTransactionReport, writeTransactionReport} from '../../../../utils/transaction-report-handler';
import {uploadTransactionReport} from '../../../../api/aws-s3/upload-file';
import {waitForStatusToChange} from '../../../../utils/waiters';
import {PageManager} from '../../../../objects/pages/page-manager';


test.describe('Customers tests', async () => {
    let email: string;
    let firstName: string;
    let lastName: string;
    let businessName: string;
    let cellPhoneNumber: string;
    let invoiceNumber: string;

    let userAuth: { session: any; challenge: any; };

    test.beforeEach(async ({userIdToken}) => {
        email = `automation_user+${BaseTest.dateTimePrefix()}@bluetape.com`;
        firstName = BaseTest.dateTimePrefix() + 'firstName';
        lastName = BaseTest.dateTimePrefix() + 'lastName';
        businessName = BaseTest.dateTimePrefix() + 'businessName';
        cellPhoneNumber = BaseTest.getCellPhoneNumber();
        invoiceNumber = BaseTest.dateTimePrefix() + 'invoiceNumber';

        const googleIDToken = await BaseAPI.googleSignUp(email, BaseTest.constants.password);
        await BaseAPI.userSignUp(businessName, email, firstName, googleIDToken, lastName, cellPhoneNumber);

        userAuth = await BaseAPI.getAuth(await userIdToken);
        await createCustomer(userAuth.session, userAuth.challenge, email, firstName, lastName, businessName, cellPhoneNumber);
    });

    test.afterEach(async ({page, adminIdToken}) => {
        const userSub = await getUserSub(adminIdToken, email);
        await deleteUser(adminIdToken, userSub);
        await deleteTransactionReport();
        await page.close();
    });

    test('Pay on behalf customer feature. @smoke', async ({pageManager, page}) => {
        // test skipped due to problem with https://devapi.bluetape.com/beta/v1/company/add-payment-card 
        // Status Code:500
        test.slow();
        await goToCustomersAddNewPaymentMethod(pageManager);
        await pageManager.sideMenu.sideMenuSubTabs.sales.receivables.click();
        await pageManager.receivablesInvoices.rowElements.clickDotsDropDown(businessName);
        await pageManager.receivablesInvoices.dropDowns.collectPayment.click();
        await pageManager.payInvoiceSOModal.buttons.payInvoice.click();
        await expect(pageManager.payInvoiceSOModal.label.paymentIsProccessing).toBeVisible();
        await pageManager.payInvoiceSOModal.buttons.close.click();
        await pageManager.receivablesInvoices.rowElements.clickOnRow(businessName);

        const transactionNumber = await pageManager.customerDetailsModal.labels.transactionNumber.textContent();
        const transactionInfo = await setTransactionNumber(transactionNumber);
        const transactionReportPath = await writeTransactionReport(transactionInfo?.transactionNumber, transactionInfo?.operationID);
        await uploadTransactionReport(transactionReportPath);

        await waitForStatusToChange({
            func: getInvoiceStatus,
            args: [userAuth.session, userAuth.challenge, invoiceNumber]
        }, "COLLECTED");
        await page.reload();
        await expect(await pageManager.receivablesInvoices.rowElements.getStatus(businessName)).toHaveText("Collected");
    });

    async function goToCustomersAddNewPaymentMethod(pageManager: PageManager) {
        await pageManager.sideMenu.openSalesSubTab(pageManager.sideMenu.sideMenuSubTabs.sales.customers);
        await pageManager.customersList.clickOnCustomerRow(businessName); // change it
        await pageManager.customerDetailsModal.buttons.addPaymentMethod.click();
        await pageManager.addNewCardModal.buttons.addCustomerCard.click();
        await pageManager.addNewCardModal.fillUpCardNumber(BaseTest.constants.cardDetails.cardNumber, BaseTest.constants.cardDetails.expirationDate, BaseTest.constants.cardDetails.securityCode);
        await pageManager.addNewCardModal.fillUpNameOnCard(firstName, lastName);
        await pageManager.addNewCardModal.fillUpBillingAddress(BaseTest.constants.address.street, BaseTest.constants.address.city, BaseTest.constants.address.state, BaseTest.constants.address.zipCode);
        await pageManager.customerDetailsModal.buttons.addInvoice.click();
        await pageManager.addInvoiceModal.fillUpAddInvoice(invoiceNumber, '500', '5');
    }
});
