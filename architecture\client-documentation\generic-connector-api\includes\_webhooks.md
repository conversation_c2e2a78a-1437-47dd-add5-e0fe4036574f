# Webhooks

## Approve a quote

> Example endpoint on your side

```xml
POST /webhook/quote/approval
```

BlueTape sends quote approvals to this endpoint.

### HTTP Request
`POST /webhook/quote/approval`

**Request parameters**

> Quote Appoval Object

```json
{
  "quoteId": "a0b9cedc32ce",
  "result": "Approved",
  "creditInfo": {
    "limit": 1000,
    "balance": 5000,
    "pastDueAmount": 450
  }
}
```

| Name | Description | Required | Type |
| ---- | ----------- | -------- | ---- |
| quoteId | Unique identifier of the quote to be approved. | true | string |
| result | Approval result. Enum: Approved, Rejected  | true | string <enum> |
| creditInfo | The company credit information. | true | credit info object |

**Responses**

<aside class="success">
Please send HTTP 200 OK when parsing successfully our request.
</aside>

## Pay an invoice

> Example endpoint on your side

```xml
POST /webhook/invoice/payment
```

BlueTape sends invoice payments to this endpoint.

### HTTP Request
`POST /webhook/invoice/payment`

**Request parameters**

> Invoice Payment Object

```json
{
  "invoiceId": "6c8f90e43e13",
  "amount": 1100,
  "fee": 0,
  "paymentMethod": "card",
  "status": "Paid"
}
```

| Name | Description | Required | Type |
| ---- | ----------- | -------- | ---- |
| invoiceId | Unique identifier of the invoice. | true | string |
| amount | Paid amount. | true | decimal |
| fee | Fee amount. | true | decimal |
| paymentMethod | The applied payment method. Can be card, ach and loan. | true | string |
| status | Invoice payment status. Enum: Paid, PartiallyPaid | true | string <enum> |

**Responses**

<aside class="success">
Please send HTTP 200 OK when parsing successfully our request.
</aside>

| Code | Description |
| ---- | ----------- |
| 200 | Successful operation |
| 400 | Invalid request |