@startuml

title Trade Credit Components Draw Repayment I.\n(not all components are shown)

participant "GetDue Func" as getdue #LightGray
participant "LMS" as lms #LightGray
queue "dueDrawsQueue" as ddqueue #LightSalmon
participant "ProcessDue Func" as procdue #LightGray
queue "Payment Operations" as opsqs #LightSalmon
participant "Payment\nService" as pserv #SkyBlue
participant "Aion" as aion #Orange

autonumber

== Draw repayment ==

getdue --> getdue : Scheduled
getdue -> lms : Get due draws and amounts
lms --> getdue
getdue -> ddqueue : Place draw ids and amounts
ddqueue -> procdue : Read due draws
procdue -> opsqs : Convert to Payment requests
opsqs -> pserv : Read payment requests
pserv -> aion : ACH

@enduml