﻿using System.Runtime.Serialization;
using System.Text.Json.Serialization;

namespace BlueTape.Aion.DataAccess.External.Models.CreateCounterParty;

[DataContract]
public class CounterpartyRequest
{
    [JsonPropertyName("type")]
    public string Type { get; set; } = null!;
    
    [JsonPropertyName("nameOnAccount")]
    public string NameOnAccount { get; set; } = null!;
    
    [JsonPropertyName("email")]
    public string Email { get; set; } = null!;
}