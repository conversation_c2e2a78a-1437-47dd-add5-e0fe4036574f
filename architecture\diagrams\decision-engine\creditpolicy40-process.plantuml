@startuml

title CreditPolicy 4.0 Process Diagram

participant "Draft" as draft #LightGrey
participant "Initialization" as init #LightSalmon
participant "Steps" as steps #LightSalmon
participant "OnBoarding\nService" as obs #SkyBlue
participant "Company\nService" as cs #SkyBlue
participant "Loan\nService" as ls #SkyBlue
participant "3rd\nparty\nproxies" as third #LightGrey

autonumber

== Initialization ==

draft -> init
init -> obs : Create\nCredit Application
init -> steps : Pass\nCreditApplicationId

== Steps processing ==

loop Steps processing
    steps -> obs : Create step
    steps --> steps : Read policy configuration
    steps -> third : Read external data
    steps -> obs : Read internal data
    steps -> cs : Read internal data
    steps -> ls : Read internal data
    steps -> obs : Update auth. details with latest values
    steps --> steps : Evaluate rules
    steps -> obs : Write results to step

    alt #pink If result Hard Fail
        steps --> steps : Process stopped
    else #LightGreen If pass or Soft Fail
        steps --> steps : go to next step
    end
end


@enduml