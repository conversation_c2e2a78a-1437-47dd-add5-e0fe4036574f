# Define the storage account
resource "azurerm_storage_account" "aionstorage" {
  name                     = "aiontransactions${var.environment}"
  resource_group_name      = var.resource_group_name
  location                 = var.resource_group_location
  account_tier             = "Standard"
  account_replication_type = "LRS"
}

# Set the storage account connection string in Key Vault
resource "azurerm_key_vault_secret" "example" {
  name         = var.aion_storage_connection
  value        = azurerm_storage_account.aionstorage.primary_connection_string
  key_vault_id = var.key_vault_id
}