﻿using AutoFixture;
using BlueTape.Aion.Application.Abstractions;
using BlueTape.Aion.Application.Models.InternalTransfer;
using BlueTape.Aion.Application.Service;
using BlueTape.Aion.DataAccess.External.Abstractions;
using BlueTape.Aion.DataAccess.External.Models.InternalTransfer;
using BlueTape.Integrations.Aion.Infrastructure.Enums;
using BueTape.Aion.Infrastructure.Exceptions;
using BueTape.Aion.Infrastructure.Options;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Options;
using Moq;
using System.Collections.Generic;
using System.Threading.Tasks;
using BlueTape.Aion.DataAccess.MongoDB.Abstractions;

namespace BlueTape.Application.Tests.Services;

public class InternalTransferServiceTests
{
    private readonly Mock<IAionHttpClient> _aionHttpClientServiceMock = new();
    private readonly Mock<IOptions<AionInternalTransferOptions>> _reportOptionsMock = new();
    private readonly Mock<ITransactionService> _transactionServiceMock = new();
    private readonly Mock<IConfiguration> _configurationMock = new();
    private readonly Mock<IAionLoggingService> _aionLoggingServiceMock = new();

    private AionInternalTransferOptions _reportOptions = new();
    private Fixture _fixture = new();
    private string _gLSecret;
    private string _opSecet;

    private void VerifyNoOtherCalls()
    {
        _aionHttpClientServiceMock.VerifyNoOtherCalls();
        _configurationMock.VerifyNoOtherCalls();
    }

    private IInternalTransferService GetService()
    {
        _gLSecret = _fixture.Create<string>();
        _opSecet = _fixture.Create<string>();

        _reportOptions = new AionInternalTransferOptions
        {
            AccountNumberSecretNames = new Dictionary<AccountCodeType, string>
            {
                {AccountCodeType.GL, _gLSecret},
                {AccountCodeType.OP, _opSecet},
            }
        };

        _reportOptionsMock
            .SetupGet(opt => opt.Value)
            .Returns(_reportOptions);

        return new InternalTransferService(
            _aionHttpClientServiceMock.Object,
            _reportOptionsMock.Object,
            _transactionServiceMock.Object,
            _configurationMock.Object,
            _aionLoggingServiceMock.Object
        );
    }

    [Fact]
    public async Task CreateInternalTransferAsync_OriginatorThrows_ParameterNotExistException()
    {
        var data = _fixture.Create<CreateInternal>();
        data.Originator.AccountCode = AccountCodeType.FUNDING;
        data.Receiver.AccountCode = AccountCodeType.GL;

        var service = GetService();
        await service.CreateInternalTransferAsync(data, "SUBSCRIPTION1", default)
            .ShouldThrowAsync<ParameterNotExistException>();

        VerifyNoOtherCalls();
    }

    [Fact]
    public async Task CreateInternalTransferAsync_ReceiverThrows_ParameterNotExistException()
    {
        var data = _fixture.Create<CreateInternal>();
        data.Originator.AccountCode = AccountCodeType.GL;
        data.Receiver.AccountCode = AccountCodeType.FUNDING;

        var service = GetService();
        await service.CreateInternalTransferAsync(data, "SUBSCRIPTION1", default)
            .ShouldThrowAsync<ParameterNotExistException>();

        VerifyNoOtherCalls();
    }

    [Fact]
    public async Task CreateInternalTransferAsync_ReceiverAccountThrows_ParameterNotExistException()
    {
        var data = _fixture.Create<CreateInternal>();
        data.Originator.AccountCode = AccountCodeType.GL;
        data.Receiver.AccountCode = AccountCodeType.OP;

        var originalAccountNumber = _fixture.Create<string>();
        var receiverAccountNumber = _fixture.Create<string>();

        _configurationMock.Setup(x => x[It.Is<string>(y => y.Equals(_gLSecret))])
            .Returns(originalAccountNumber);

        receiverAccountNumber = string.Empty;
        _configurationMock.Setup(x => x[It.Is<string>(y => y.Equals(_opSecet))])
            .Returns(receiverAccountNumber);

        var service = GetService();
        await service.CreateInternalTransferAsync(data, "SUBSCRIPTION1", default)
            .ShouldThrowAsync<ParameterNotExistException>();

        _configurationMock.Verify(x => x[It.IsAny<string>()], Times.Exactly(2));

        VerifyNoOtherCalls();
    }

    [Fact]
    public async Task CreateInternalTransferAsync_OriginalAccountThrows_ParameterNotExistException()
    {
        var data = _fixture.Create<CreateInternal>();
        data.Originator.AccountCode = AccountCodeType.GL;
        data.Receiver.AccountCode = AccountCodeType.OP;

        var originalAccountNumber = _fixture.Create<string>();
        var receiverAccountNumber = _fixture.Create<string>();

        originalAccountNumber = string.Empty;
        _configurationMock.Setup(x => x[It.Is<string>(y => y.Equals(_gLSecret))])
            .Returns(originalAccountNumber);

        _configurationMock.Setup(x => x[It.Is<string>(y => y.Equals(_opSecet))])
            .Returns(receiverAccountNumber);

        var service = GetService();
        await service.CreateInternalTransferAsync(data, "SUBSCRIPTION1", default)
            .ShouldThrowAsync<ParameterNotExistException>();

        _configurationMock.Verify(x => x[It.IsAny<string>()], Times.Exactly(2));

        VerifyNoOtherCalls();
    }

    [Fact]
    public async Task CreateInternalTransferAsync_Execute_Success()
    {
        var data = _fixture.Create<CreateInternal>();
        data.Originator.AccountCode = AccountCodeType.GL;
        data.Receiver.AccountCode = AccountCodeType.OP;

        var originalAccountNumber = _fixture.Create<string>();
        var receiverAccountNumber = _fixture.Create<string>();

        var response = _fixture.Create<InternalTransferResponse>();

        _configurationMock.Setup(x => x[It.Is<string>(y => y.Equals(_gLSecret))])
            .Returns(originalAccountNumber);

        _configurationMock.Setup(x => x[It.Is<string>(y => y.Equals(_opSecet))])
            .Returns(receiverAccountNumber);

        _aionHttpClientServiceMock.Setup(x =>
                x.CreateInternalTransfer(It.IsAny<InternalTransferRequest>(), PaymentSubscriptionType.SUBSCRIPTION1, default))
            .ReturnsAsync(response);

        var service = GetService();
        var result = await service.CreateInternalTransferAsync(data, "SUBSCRIPTION1", default);
        result.ShouldBeEquivalentTo(response.BookTransferObj);

        _configurationMock.Verify(x => x[It.IsAny<string>()], Times.Exactly(2));
        _aionHttpClientServiceMock.Verify(x =>
            x.CreateInternalTransfer(It.IsAny<InternalTransferRequest>(), PaymentSubscriptionType.SUBSCRIPTION1, default), Times.Once);

        VerifyNoOtherCalls();
    }
}