import {expect} from '@playwright/test';
import {test} from '../../test-utils';
import {sendLMSRequest, createLoan, deleteLoan} from '../../../api/common/lms-send-request';

const constants = JSON.parse(JSON.stringify(require('../../../constants/LMStestData.json')));

test.describe(`Change Log @LMS @API`, async () => {

    let loanId: string;

    test.beforeAll(async () => {
        loanId = await createLoan();
    });

    test.afterAll(async () => {
        const response = await deleteLoan(loanId);

        expect(response.status, `Status code 200`)
            .toEqual(200);
    });

    test(`Check Change Log by LoanID. @lms`, async () => {
        const response = await sendLMSRequest('get', `ChangeLogs?loanId=${loanId}`);

        expect(response.status, `Status code 200`)
            .toEqual(200);

        expect(response.data)
            .toEqual(expect.any(Array));
    });

    /**
     * Negative Tests
     */

    test(`Getting empty log by invalid loan Id. @lms`, async () => {
        const response = await sendLMSRequest('get', `ChangeLogs?loanId=${constants.invalidLoanIDs.id}`);

        expect(response.status, `Status code 200`)
            .toEqual(200);

        expect(response.data, `Received answer contains an empty array`)
            .toEqual([]);
    });
});
