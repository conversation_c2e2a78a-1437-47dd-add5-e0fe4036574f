import sendAdminRequest from '../common/send-admin-request';

export async function deleteBankConnection(bearerToken: string, accountId: string, companyId: string) {
    const endpoint = `delete-finicity-account?accountId=${accountId}&companyId=${companyId}`;
    await sendAdminRequest('get', endpoint, bearerToken);
}

export async function deleteAllBankConnection(bearerToken: string, accountIds: string, companyIds: string) {
    try {
        for (let i = 0; i < accountIds.length; i++) {
            await deleteBankConnection(bearerToken, accountIds[i], companyIds[i]);
        }
    } catch (error) {
        console.log(error);
        return error;
    }
}
