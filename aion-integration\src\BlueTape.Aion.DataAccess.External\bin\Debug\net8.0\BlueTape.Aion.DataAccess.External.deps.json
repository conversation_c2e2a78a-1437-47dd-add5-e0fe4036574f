{"runtimeTarget": {"name": ".NETCoreApp,Version=v8.0", "signature": ""}, "compilationOptions": {}, "targets": {".NETCoreApp,Version=v8.0": {"BlueTape.Aion.DataAccess.External/1.0.0": {"dependencies": {"BlueTape.Aion.DataAccess.MongoDB": "1.0.0", "Portable.BouncyCastle": "1.9.0", "System.IdentityModel.Tokens.Jwt": "8.0.2"}, "runtime": {"BlueTape.Aion.DataAccess.External.dll": {}}}, "AutoMapper/12.0.1": {"dependencies": {"Microsoft.CSharp": "4.7.0"}, "runtime": {"lib/netstandard2.1/AutoMapper.dll": {"assemblyVersion": "1*******", "fileVersion": "12.0.1.0"}}}, "AutoMapper.Extensions.Microsoft.DependencyInjection/12.0.1": {"dependencies": {"AutoMapper": "12.0.1", "Microsoft.Extensions.Options": "8.0.1"}, "runtime": {"lib/netstandard2.1/AutoMapper.Extensions.Microsoft.DependencyInjection.dll": {"assemblyVersion": "1*******", "fileVersion": "12.0.1.0"}}}, "AWSSDK.Core/3.7.302.6": {"runtime": {"lib/net8.0/AWSSDK.Core.dll": {"assemblyVersion": "*******", "fileVersion": "3.7.302.6"}}}, "AWSSDK.KeyManagementService/3.7.300.46": {"dependencies": {"AWSSDK.Core": "3.7.302.6"}, "runtime": {"lib/net8.0/AWSSDK.KeyManagementService.dll": {"assemblyVersion": "*******", "fileVersion": "3.7.300.46"}}}, "AWSSDK.SecretsManager/3.7.302.21": {"dependencies": {"AWSSDK.Core": "3.7.302.6"}, "runtime": {"lib/net8.0/AWSSDK.SecretsManager.dll": {"assemblyVersion": "*******", "fileVersion": "3.7.302.21"}}}, "AWSSDK.SecretsManager.Caching/1.0.6": {"dependencies": {"AWSSDK.SecretsManager": "3.7.302.21", "Microsoft.Extensions.Caching.Memory": "7.0.0"}, "runtime": {"lib/netstandard2.0/Amazon.SecretsManager.Extensions.Caching.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "AWSSDK.SecurityToken/3.7.300.47": {"dependencies": {"AWSSDK.Core": "3.7.302.6"}, "runtime": {"lib/net8.0/AWSSDK.SecurityToken.dll": {"assemblyVersion": "*******", "fileVersion": "3.7.300.47"}}}, "AWSSDK.SimpleNotificationService/**********": {"dependencies": {"AWSSDK.Core": "3.7.302.6"}, "runtime": {"lib/netcoreapp3.1/AWSSDK.SimpleNotificationService.dll": {"assemblyVersion": "*******", "fileVersion": "**********"}}}, "Azure.Core/1.36.0": {"dependencies": {"Microsoft.Bcl.AsyncInterfaces": "1.1.1", "System.Diagnostics.DiagnosticSource": "6.0.1", "System.Memory.Data": "1.0.2", "System.Numerics.Vectors": "4.5.0", "System.Text.Encodings.Web": "4.7.2", "System.Text.Json": "4.7.2", "System.Threading.Tasks.Extensions": "4.5.4"}, "runtime": {"lib/net6.0/Azure.Core.dll": {"assemblyVersion": "1.36.0.0", "fileVersion": "1.3600.23.56006"}}}, "Azure.Identity/1.10.4": {"dependencies": {"Azure.Core": "1.36.0", "Microsoft.Identity.Client": "4.56.0", "Microsoft.Identity.Client.Extensions.Msal": "4.56.0", "System.Memory": "4.5.5", "System.Security.Cryptography.ProtectedData": "4.7.0", "System.Text.Json": "4.7.2", "System.Threading.Tasks.Extensions": "4.5.4"}, "runtime": {"lib/netstandard2.0/Azure.Identity.dll": {"assemblyVersion": "1.10.4.0", "fileVersion": "1.1000.423.56303"}}}, "Azure.Security.KeyVault.Keys/4.5.0": {"dependencies": {"Azure.Core": "1.36.0", "System.Memory": "4.5.5", "System.Text.Json": "4.7.2", "System.Threading.Tasks.Extensions": "4.5.4"}, "runtime": {"lib/netstandard2.0/Azure.Security.KeyVault.Keys.dll": {"assemblyVersion": "4.5.0.0", "fileVersion": "4.500.23.16403"}}}, "Azure.Security.KeyVault.Secrets/4.5.0": {"dependencies": {"Azure.Core": "1.36.0", "System.Memory": "4.5.5", "System.Text.Json": "4.7.2", "System.Threading.Tasks.Extensions": "4.5.4"}, "runtime": {"lib/netstandard2.0/Azure.Security.KeyVault.Secrets.dll": {"assemblyVersion": "4.5.0.0", "fileVersion": "4.500.23.16403"}}}, "BlueTape.AzureKeyVault/1.0.3": {"dependencies": {"Azure.Identity": "1.10.4", "Azure.Security.KeyVault.Keys": "4.5.0", "Azure.Security.KeyVault.Secrets": "4.5.0", "BlueTape.Utilities": "1.3.11", "Microsoft.Extensions.Logging.Abstractions": "8.0.0"}, "runtime": {"lib/net6.0/BlueTape.AzureKeyVault.dll": {"assemblyVersion": "1.0.3.0", "fileVersion": "1.0.3.0"}}}, "BlueTape.Common.ExceptionHandling/1.0.6": {"dependencies": {"BlueTape.SNS": "1.0.0"}, "runtime": {"lib/net6.0/BlueTape.Common.ExceptionHandling.dll": {"assemblyVersion": "1.0.6.0", "fileVersion": "1.0.6.0"}}}, "BlueTape.Common.Extensions/1.1.0": {"runtime": {"lib/net6.0/BlueTape.Common.Extensions.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "BlueTape.Integrations.Aion.Infrastructure/1.0.20": {"runtime": {"lib/net6.0/BlueTape.Integrations.Aion.Infrastructure.dll": {"assemblyVersion": "1.0.20.0", "fileVersion": "1.0.20.0"}}}, "BlueTape.MongoDB/1.1.30": {"dependencies": {"BlueTape.AzureKeyVault": "1.0.3", "BlueTape.Utilities": "1.3.11", "MongoDB.Driver": "2.25.0"}, "runtime": {"lib/net6.0/BlueTape.MongoDB.dll": {"assemblyVersion": "1.1.6.0", "fileVersion": "1.1.6.0"}}}, "BlueTape.OBS/1.1.21": {"runtime": {"lib/net6.0/BlueTape.OBS.dll": {"assemblyVersion": "1.1.21.0", "fileVersion": "1.1.21.0"}}}, "BlueTape.SNS/1.0.0": {"dependencies": {"AWSSDK.SimpleNotificationService": "**********", "Microsoft.Extensions.DependencyInjection": "7.0.0", "Serilog.AspNetCore": "6.0.1"}, "runtime": {"lib/net6.0/BlueTape.SNS.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "BlueTape.Utilities/1.3.11": {"dependencies": {"AWSSDK.KeyManagementService": "3.7.300.46", "AWSSDK.SecretsManager": "3.7.302.21", "AWSSDK.SecretsManager.Caching": "1.0.6", "AWSSDK.SecurityToken": "3.7.300.47", "BlueTape.Common.Extensions": "1.1.0", "BlueTape.OBS": "1.1.21", "Microsoft.AspNetCore.Http.Abstractions": "2.2.0", "Microsoft.Extensions.DependencyInjection.Abstractions": "8.0.0", "Microsoft.Extensions.Hosting.Abstractions": "6.0.0", "Microsoft.Extensions.Http.Polly": "6.0.9", "Microsoft.Extensions.Options": "8.0.1", "MongoDB.Bson": "2.25.0", "Polly": "7.2.3", "Serilog": "2.12.0", "Serilog.AspNetCore": "6.0.1", "Serilog.Enrichers.GlobalLogContext": "2.1.0", "Serilog.Sinks.Console": "4.1.0", "Serilog.Sinks.Logz.Io": "7.1.0"}, "runtime": {"lib/net6.0/BlueTape.Utilities.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "DnsClient/1.6.1": {"dependencies": {"Microsoft.Win32.Registry": "5.0.0"}, "runtime": {"lib/net5.0/DnsClient.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Elastic.CommonSchema/1.5.3": {"dependencies": {"System.Text.Json": "4.7.2"}, "runtime": {"lib/netstandard2.1/Elastic.CommonSchema.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Elastic.CommonSchema.Serilog/1.5.3": {"dependencies": {"Elastic.CommonSchema": "1.5.3", "Microsoft.AspNetCore.Http.Abstractions": "2.2.0", "Serilog": "2.12.0"}, "runtime": {"lib/netstandard2.1/Elastic.CommonSchema.Serilog.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Microsoft.AspNetCore.Http.Abstractions/2.2.0": {"dependencies": {"Microsoft.AspNetCore.Http.Features": "2.2.0", "System.Text.Encodings.Web": "4.7.2"}}, "Microsoft.AspNetCore.Http.Features/2.2.0": {"dependencies": {"Microsoft.Extensions.Primitives": "8.0.0"}}, "Microsoft.Bcl.AsyncInterfaces/1.1.1": {"runtime": {"lib/netstandard2.1/Microsoft.Bcl.AsyncInterfaces.dll": {"assemblyVersion": "*******", "fileVersion": "4.700.20.21406"}}}, "Microsoft.CSharp/4.7.0": {}, "Microsoft.Extensions.Caching.Abstractions/7.0.0": {"dependencies": {"Microsoft.Extensions.Primitives": "8.0.0"}}, "Microsoft.Extensions.Caching.Memory/7.0.0": {"dependencies": {"Microsoft.Extensions.Caching.Abstractions": "7.0.0", "Microsoft.Extensions.DependencyInjection.Abstractions": "8.0.0", "Microsoft.Extensions.Logging.Abstractions": "8.0.0", "Microsoft.Extensions.Options": "8.0.1", "Microsoft.Extensions.Primitives": "8.0.0"}}, "Microsoft.Extensions.Configuration/2.0.0": {"dependencies": {"Microsoft.Extensions.Configuration.Abstractions": "6.0.0"}}, "Microsoft.Extensions.Configuration.Abstractions/6.0.0": {"dependencies": {"Microsoft.Extensions.Primitives": "8.0.0"}}, "Microsoft.Extensions.Configuration.Binder/2.0.0": {"dependencies": {"Microsoft.Extensions.Configuration": "2.0.0"}}, "Microsoft.Extensions.DependencyInjection/7.0.0": {"dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "8.0.0"}}, "Microsoft.Extensions.DependencyInjection.Abstractions/8.0.0": {}, "Microsoft.Extensions.DependencyModel/3.0.0": {"dependencies": {"System.Text.Json": "4.7.2"}, "runtime": {"lib/netstandard2.0/Microsoft.Extensions.DependencyModel.dll": {"assemblyVersion": "*******", "fileVersion": "3.0.19.46305"}}}, "Microsoft.Extensions.FileProviders.Abstractions/6.0.0": {"dependencies": {"Microsoft.Extensions.Primitives": "8.0.0"}}, "Microsoft.Extensions.Hosting.Abstractions/6.0.0": {"dependencies": {"Microsoft.Extensions.Configuration.Abstractions": "6.0.0", "Microsoft.Extensions.DependencyInjection.Abstractions": "8.0.0", "Microsoft.Extensions.FileProviders.Abstractions": "6.0.0"}}, "Microsoft.Extensions.Http/6.0.0": {"dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "8.0.0", "Microsoft.Extensions.Logging": "6.0.0", "Microsoft.Extensions.Logging.Abstractions": "8.0.0", "Microsoft.Extensions.Options": "8.0.1"}}, "Microsoft.Extensions.Http.Polly/6.0.9": {"dependencies": {"Microsoft.Extensions.Http": "6.0.0", "Polly": "7.2.3", "Polly.Extensions.Http": "3.0.0"}, "runtime": {"lib/netstandard2.0/Microsoft.Extensions.Http.Polly.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.922.41926"}}}, "Microsoft.Extensions.Logging/6.0.0": {"dependencies": {"Microsoft.Extensions.DependencyInjection": "7.0.0", "Microsoft.Extensions.DependencyInjection.Abstractions": "8.0.0", "Microsoft.Extensions.Logging.Abstractions": "8.0.0", "Microsoft.Extensions.Options": "8.0.1", "System.Diagnostics.DiagnosticSource": "6.0.1"}}, "Microsoft.Extensions.Logging.Abstractions/8.0.0": {"dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "8.0.0"}}, "Microsoft.Extensions.Options/8.0.1": {"dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "8.0.0", "Microsoft.Extensions.Primitives": "8.0.0"}, "runtime": {"lib/net8.0/Microsoft.Extensions.Options.dll": {"assemblyVersion": "8.0.0.0", "fileVersion": "8.0.123.58001"}}}, "Microsoft.Extensions.Options.ConfigurationExtensions/2.0.0": {"dependencies": {"Microsoft.Extensions.Configuration.Abstractions": "6.0.0", "Microsoft.Extensions.Configuration.Binder": "2.0.0", "Microsoft.Extensions.DependencyInjection.Abstractions": "8.0.0", "Microsoft.Extensions.Options": "8.0.1"}}, "Microsoft.Extensions.Primitives/8.0.0": {}, "Microsoft.Identity.Client/4.56.0": {"dependencies": {"Microsoft.IdentityModel.Abstractions": "8.0.2"}, "runtime": {"lib/net6.0/Microsoft.Identity.Client.dll": {"assemblyVersion": "4.56.0.0", "fileVersion": "4.56.0.0"}}}, "Microsoft.Identity.Client.Extensions.Msal/4.56.0": {"dependencies": {"Microsoft.Identity.Client": "4.56.0", "System.IO.FileSystem.AccessControl": "5.0.0", "System.Security.Cryptography.ProtectedData": "4.7.0"}, "runtime": {"lib/netstandard2.0/Microsoft.Identity.Client.Extensions.Msal.dll": {"assemblyVersion": "4.56.0.0", "fileVersion": "4.56.0.0"}}}, "Microsoft.IdentityModel.Abstractions/8.0.2": {"runtime": {"lib/net8.0/Microsoft.IdentityModel.Abstractions.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.2.50822"}}}, "Microsoft.IdentityModel.JsonWebTokens/8.0.2": {"dependencies": {"Microsoft.IdentityModel.Tokens": "8.0.2"}, "runtime": {"lib/net8.0/Microsoft.IdentityModel.JsonWebTokens.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.2.50822"}}}, "Microsoft.IdentityModel.Logging/8.0.2": {"dependencies": {"Microsoft.IdentityModel.Abstractions": "8.0.2"}, "runtime": {"lib/net8.0/Microsoft.IdentityModel.Logging.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.2.50822"}}}, "Microsoft.IdentityModel.Tokens/8.0.2": {"dependencies": {"Microsoft.IdentityModel.Logging": "8.0.2"}, "runtime": {"lib/net8.0/Microsoft.IdentityModel.Tokens.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.2.50822"}}}, "Microsoft.NETCore.Platforms/5.0.0": {}, "Microsoft.Win32.Registry/5.0.0": {"dependencies": {"System.Security.AccessControl": "5.0.0", "System.Security.Principal.Windows": "5.0.0"}}, "MongoDB.Bson/2.25.0": {"dependencies": {"System.Memory": "4.5.5", "System.Runtime.CompilerServices.Unsafe": "6.0.0"}, "runtime": {"lib/netstandard2.1/MongoDB.Bson.dll": {"assemblyVersion": "********", "fileVersion": "********"}}}, "MongoDB.Driver/2.25.0": {"dependencies": {"Microsoft.Extensions.Logging.Abstractions": "8.0.0", "MongoDB.Bson": "2.25.0", "MongoDB.Driver.Core": "2.25.0", "MongoDB.Libmongocrypt": "1.8.2"}, "runtime": {"lib/netstandard2.1/MongoDB.Driver.dll": {"assemblyVersion": "********", "fileVersion": "********"}}}, "MongoDB.Driver.Core/2.25.0": {"dependencies": {"AWSSDK.SecurityToken": "3.7.300.47", "DnsClient": "1.6.1", "Microsoft.Extensions.Logging.Abstractions": "8.0.0", "MongoDB.Bson": "2.25.0", "MongoDB.Libmongocrypt": "1.8.2", "SharpCompress": "0.30.1", "Snappier": "1.0.0", "System.Buffers": "4.5.1", "ZstdSharp.Port": "0.7.3"}, "runtime": {"lib/netstandard2.1/MongoDB.Driver.Core.dll": {"assemblyVersion": "********", "fileVersion": "********"}}}, "MongoDB.Libmongocrypt/1.8.2": {"runtime": {"lib/netstandard2.1/MongoDB.Libmongocrypt.dll": {"assemblyVersion": "1.8.2.0", "fileVersion": "1.8.2.0"}}, "runtimeTargets": {"runtimes/linux/native/libmongocrypt.so": {"rid": "linux", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/osx/native/libmongocrypt.dylib": {"rid": "osx", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/win/native/mongocrypt.dll": {"rid": "win", "assetType": "native", "fileVersion": "0.0.0.0"}}}, "Newtonsoft.Json/13.0.1": {"runtime": {"lib/netstandard2.0/Newtonsoft.Json.dll": {"assemblyVersion": "1*******", "fileVersion": "13.0.1.25517"}}}, "Polly/7.2.3": {"runtime": {"lib/netstandard2.0/Polly.dll": {"assemblyVersion": "7.0.0.0", "fileVersion": "7.2.3.0"}}}, "Polly.Extensions.Http/3.0.0": {"dependencies": {"Polly": "7.2.3"}, "runtime": {"lib/netstandard2.0/Polly.Extensions.Http.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Portable.BouncyCastle/1.9.0": {"runtime": {"lib/netstandard2.0/BouncyCastle.Crypto.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Serilog/2.12.0": {"runtime": {"lib/net6.0/Serilog.dll": {"assemblyVersion": "*******", "fileVersion": "********"}}}, "Serilog.AspNetCore/6.0.1": {"dependencies": {"Microsoft.Extensions.DependencyInjection": "7.0.0", "Microsoft.Extensions.Logging": "6.0.0", "Serilog": "2.12.0", "Serilog.Extensions.Hosting": "5.0.1", "Serilog.Formatting.Compact": "1.1.0", "Serilog.Settings.Configuration": "3.3.0", "Serilog.Sinks.Console": "4.1.0", "Serilog.Sinks.Debug": "2.0.0", "Serilog.Sinks.File": "5.0.0"}, "runtime": {"lib/net5.0/Serilog.AspNetCore.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Serilog.Enrichers.GlobalLogContext/2.1.0": {"dependencies": {"Serilog": "2.12.0"}, "runtime": {"lib/net5.0/Serilog.Enrichers.GlobalLogContext.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Serilog.Extensions.Hosting/5.0.1": {"dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "8.0.0", "Microsoft.Extensions.Hosting.Abstractions": "6.0.0", "Microsoft.Extensions.Logging.Abstractions": "8.0.0", "Serilog": "2.12.0", "Serilog.Extensions.Logging": "3.1.0"}, "runtime": {"lib/netstandard2.1/Serilog.Extensions.Hosting.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Serilog.Extensions.Logging/3.1.0": {"dependencies": {"Microsoft.Extensions.Logging": "6.0.0", "Serilog": "2.12.0"}, "runtime": {"lib/netstandard2.0/Serilog.Extensions.Logging.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Serilog.Formatting.Compact/1.1.0": {"dependencies": {"Serilog": "2.12.0"}, "runtime": {"lib/netstandard2.0/Serilog.Formatting.Compact.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Serilog.Settings.Configuration/3.3.0": {"dependencies": {"Microsoft.Extensions.DependencyModel": "3.0.0", "Microsoft.Extensions.Options.ConfigurationExtensions": "2.0.0", "Serilog": "2.12.0"}, "runtime": {"lib/netstandard2.0/Serilog.Settings.Configuration.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Serilog.Sinks.Console/4.1.0": {"dependencies": {"Serilog": "2.12.0"}, "runtime": {"lib/net5.0/Serilog.Sinks.Console.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Serilog.Sinks.Debug/2.0.0": {"dependencies": {"Serilog": "2.12.0"}, "runtime": {"lib/netstandard2.1/Serilog.Sinks.Debug.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Serilog.Sinks.File/5.0.0": {"dependencies": {"Serilog": "2.12.0"}, "runtime": {"lib/net5.0/Serilog.Sinks.File.dll": {"assemblyVersion": "5.0.0.0", "fileVersion": "5.0.0.0"}}}, "Serilog.Sinks.Http/8.0.0": {"dependencies": {"Microsoft.Extensions.Configuration.Abstractions": "6.0.0", "Serilog.Sinks.File": "5.0.0"}, "runtime": {"lib/netstandard2.1/Serilog.Sinks.Http.dll": {"assemblyVersion": "8.0.0.0", "fileVersion": "8.0.0.0"}}}, "Serilog.Sinks.Logz.Io/7.1.0": {"dependencies": {"Elastic.CommonSchema.Serilog": "1.5.3", "Newtonsoft.Json": "13.0.1", "Serilog.Sinks.Http": "8.0.0", "Serilog.Sinks.PeriodicBatching": "3.1.0"}, "runtime": {"lib/net6.0/Serilog.Sinks.Logz.Io.dll": {"assemblyVersion": "7.0.0.0", "fileVersion": "7.0.0.0"}}}, "Serilog.Sinks.PeriodicBatching/3.1.0": {"dependencies": {"Serilog": "2.12.0"}, "runtime": {"lib/netstandard2.1/Serilog.Sinks.PeriodicBatching.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "SharpCompress/0.30.1": {"runtime": {"lib/net5.0/SharpCompress.dll": {"assemblyVersion": "0.30.1.0", "fileVersion": "0.30.1.0"}}}, "Snappier/1.0.0": {"runtime": {"lib/net5.0/Snappier.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "System.Buffers/4.5.1": {}, "System.Diagnostics.DiagnosticSource/6.0.1": {"dependencies": {"System.Runtime.CompilerServices.Unsafe": "6.0.0"}}, "System.IdentityModel.Tokens.Jwt/8.0.2": {"dependencies": {"Microsoft.IdentityModel.JsonWebTokens": "8.0.2", "Microsoft.IdentityModel.Tokens": "8.0.2"}, "runtime": {"lib/net8.0/System.IdentityModel.Tokens.Jwt.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.2.50822"}}}, "System.IO.FileSystem.AccessControl/5.0.0": {"dependencies": {"System.Security.AccessControl": "5.0.0", "System.Security.Principal.Windows": "5.0.0"}}, "System.Memory/4.5.5": {}, "System.Memory.Data/1.0.2": {"dependencies": {"System.Text.Encodings.Web": "4.7.2", "System.Text.Json": "4.7.2"}, "runtime": {"lib/netstandard2.0/System.Memory.Data.dll": {"assemblyVersion": "*******", "fileVersion": "1.0.221.20802"}}}, "System.Numerics.Vectors/4.5.0": {}, "System.Runtime.CompilerServices.Unsafe/6.0.0": {}, "System.Security.AccessControl/5.0.0": {"dependencies": {"Microsoft.NETCore.Platforms": "5.0.0", "System.Security.Principal.Windows": "5.0.0"}}, "System.Security.Cryptography.ProtectedData/4.7.0": {"runtime": {"lib/netstandard2.0/System.Security.Cryptography.ProtectedData.dll": {"assemblyVersion": "*******", "fileVersion": "4.700.19.56404"}}, "runtimeTargets": {"runtimes/win/lib/netstandard2.0/System.Security.Cryptography.ProtectedData.dll": {"rid": "win", "assetType": "runtime", "assemblyVersion": "*******", "fileVersion": "4.700.19.56404"}}}, "System.Security.Principal.Windows/5.0.0": {}, "System.Text.Encodings.Web/4.7.2": {}, "System.Text.Json/4.7.2": {}, "System.Threading.Tasks.Extensions/4.5.4": {}, "ZstdSharp.Port/0.7.3": {"runtime": {"lib/net7.0/ZstdSharp.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "BlueTape.Aion.DataAccess.MongoDB/1.0.0": {"dependencies": {"AutoMapper": "12.0.1", "AutoMapper.Extensions.Microsoft.DependencyInjection": "12.0.1", "BlueTape.Aion.Domain": "1.0.0", "BlueTape.MongoDB": "1.1.30", "BueTape.Aion.Infrastructure": "1.0.0"}, "runtime": {"BlueTape.Aion.DataAccess.MongoDB.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "BlueTape.Aion.Domain/1.0.0": {"dependencies": {"BlueTape.Integrations.Aion.Infrastructure": "1.0.20", "BlueTape.MongoDB": "1.1.30"}, "runtime": {"BlueTape.Aion.Domain.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "BueTape.Aion.Infrastructure/1.0.0": {"dependencies": {"BlueTape.Aion.Domain": "1.0.0", "BlueTape.Common.ExceptionHandling": "1.0.6", "BlueTape.Integrations.Aion.Infrastructure": "1.0.20", "BlueTape.SNS": "1.0.0"}, "runtime": {"BueTape.Aion.Infrastructure.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}}}, "libraries": {"BlueTape.Aion.DataAccess.External/1.0.0": {"type": "project", "serviceable": false, "sha512": ""}, "AutoMapper/12.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-hvV62vl6Hp/WfQ24yzo3Co9+OPl8wH8hApwVtgWpiAynVJkUcs7xvehnSftawL8Pe8FrPffBRM3hwzLQqWDNjA==", "path": "automapper/12.0.1", "hashPath": "automapper.12.0.1.nupkg.sha512"}, "AutoMapper.Extensions.Microsoft.DependencyInjection/12.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-+g/K+Vpe3gGMKGzjslMOdqNlkikScDjWfVvmWTayrDHaG/n2pPmFBMa+jKX1r/h6BDGFdkyRjAuhFE3ykW+r1g==", "path": "automapper.extensions.microsoft.dependencyinjection/12.0.1", "hashPath": "automapper.extensions.microsoft.dependencyinjection.12.0.1.nupkg.sha512"}, "AWSSDK.Core/3.7.302.6": {"type": "package", "serviceable": true, "sha512": "sha512-eOPMHT3YSLQg5wXm+UL8Qoka17byK5l/4qxfuE/S32IoQ34gkTDQ1yI9pQPk8LEpeV/W0BE2np+XuhdDQ3wE8g==", "path": "awssdk.core/3.7.302.6", "hashPath": "awssdk.core.3.7.302.6.nupkg.sha512"}, "AWSSDK.KeyManagementService/3.7.300.46": {"type": "package", "serviceable": true, "sha512": "sha512-6J3FlWpFvEhYinkmabY8i9w66lgvmjlZG5oRHazqyiyatjMOYsA76Ynj+T6gtHC5iB1BrBtEFOPO1rq36sILfA==", "path": "awssdk.keymanagementservice/3.7.300.46", "hashPath": "awssdk.keymanagementservice.3.7.300.46.nupkg.sha512"}, "AWSSDK.SecretsManager/3.7.302.21": {"type": "package", "serviceable": true, "sha512": "sha512-Ns8mQtXUWAhp32xJTeftmAklzQeu0Tful0BTZjQg5EeyYE+qUDQgCrY31UBUr1PVw2sInmT5BBzoyhic9OjAwA==", "path": "awssdk.secretsmanager/3.7.302.21", "hashPath": "awssdk.secretsmanager.3.7.302.21.nupkg.sha512"}, "AWSSDK.SecretsManager.Caching/1.0.6": {"type": "package", "serviceable": true, "sha512": "sha512-N982+rohMJ/w8ywyN6hgnSgw2cpqj4MJDizz+b93gudQSEzR3lCTzHGN3AQl+ngMH4yTG+DfKgmL7QajvvYyKQ==", "path": "awssdk.secretsmanager.caching/1.0.6", "hashPath": "awssdk.secretsmanager.caching.1.0.6.nupkg.sha512"}, "AWSSDK.SecurityToken/3.7.300.47": {"type": "package", "serviceable": true, "sha512": "sha512-wBcBC0axwf+qQTVMPUblDxIpMtbp04z9ElYeW6BSjeAO7spvJXhEz7yhikdTn8YaeWW+K6U9h6AIDQpJlhH2vQ==", "path": "awssdk.securitytoken/3.7.300.47", "hashPath": "awssdk.securitytoken.3.7.300.47.nupkg.sha512"}, "AWSSDK.SimpleNotificationService/**********": {"type": "package", "serviceable": true, "sha512": "sha512-RqBqzwh9dGCxmfaUGvdHyYqBzd4RyCGUdd8w8zo4nxCH/3acKDbQriiFRgYvSOkOunIKXNMR45aBbQXeReuVMw==", "path": "awssdk.simplenotificationservice/**********", "hashPath": "awssdk.simplenotificationservice.**********.nupkg.sha512"}, "Azure.Core/1.36.0": {"type": "package", "serviceable": true, "sha512": "sha512-vwqFZdHS4dzPlI7FFRkPx9ctA+aGGeRev3gnzG8lntWvKMmBhAmulABi1O9CEvS3/jzYt7yA+0pqVdxkpAd7dQ==", "path": "azure.core/1.36.0", "hashPath": "azure.core.1.36.0.nupkg.sha512"}, "Azure.Identity/1.10.4": {"type": "package", "serviceable": true, "sha512": "sha512-hSvisZy9sld0Gik1X94od3+rRXCx+AKgi+iLH6fFdlnRZRePn7RtrqUGSsORiH2h8H2sc4NLTrnuUte1WL+QuQ==", "path": "azure.identity/1.10.4", "hashPath": "azure.identity.1.10.4.nupkg.sha512"}, "Azure.Security.KeyVault.Keys/4.5.0": {"type": "package", "serviceable": true, "sha512": "sha512-HnW9kjhRzQkfJE4ISl63cWVa6qLe3FM1MxoxNvNFtDUeT5iMBEg0YgGbcx2YgEiYaazIvSgZyjBr4L3Ur3+m7g==", "path": "azure.security.keyvault.keys/4.5.0", "hashPath": "azure.security.keyvault.keys.4.5.0.nupkg.sha512"}, "Azure.Security.KeyVault.Secrets/4.5.0": {"type": "package", "serviceable": true, "sha512": "sha512-ztr26Ai4K7AZGuw68/ffLDn+2G3WL0myjC7nY1RYkxPMnsplTPEH+Ke4RGxvSkg4kC7bJ9NwdlqpEwfDX0qhdw==", "path": "azure.security.keyvault.secrets/4.5.0", "hashPath": "azure.security.keyvault.secrets.4.5.0.nupkg.sha512"}, "BlueTape.AzureKeyVault/1.0.3": {"type": "package", "serviceable": true, "sha512": "sha512-On4RZI41X71GSmrYnv3p1eN6ullTWF9L8SsZ2NC/tQsQ/Upe0X1kcJE7rgvrT6G1RToKyY2n+OAGwfYZH8uOlQ==", "path": "bluetape.azurekeyvault/1.0.3", "hashPath": "bluetape.azurekeyvault.1.0.3.nupkg.sha512"}, "BlueTape.Common.ExceptionHandling/1.0.6": {"type": "package", "serviceable": true, "sha512": "sha512-K2Q88TeDQM5SbD7LdnwHrGiUq/OOC744+OuYlR4gwK7TvsE+j678vpBlzJDpWKhrHa3HGcfsAEsn0GClwXa/Bw==", "path": "bluetape.common.exceptionhandling/1.0.6", "hashPath": "bluetape.common.exceptionhandling.1.0.6.nupkg.sha512"}, "BlueTape.Common.Extensions/1.1.0": {"type": "package", "serviceable": true, "sha512": "sha512-P4mQRSipiN2qp+5ETfnBdZz564U1AWder867Zj/4SV+mZTS+SxqML0H+sW2W7PADp9iUgdngoNzqYsj+UxGheA==", "path": "bluetape.common.extensions/1.1.0", "hashPath": "bluetape.common.extensions.1.1.0.nupkg.sha512"}, "BlueTape.Integrations.Aion.Infrastructure/1.0.20": {"type": "package", "serviceable": true, "sha512": "sha512-9pw8l+3DKjlwHR84+LaDaBDib+WoKpQ7ooPEbFypRk7a71MosF35RMUrZ7NaJXnE+yM1faPvRBIxVT+IWK/bCA==", "path": "bluetape.integrations.aion.infrastructure/1.0.20", "hashPath": "bluetape.integrations.aion.infrastructure.1.0.20.nupkg.sha512"}, "BlueTape.MongoDB/1.1.30": {"type": "package", "serviceable": true, "sha512": "sha512-2nH+xqx9JZ05WDzha/tOER8d8t9TtXf+TMmLXd/0OC43mCOawBrl3LDRKKMRFXlFAf2RhpM2Ggzrz6gDrtt2dw==", "path": "bluetape.mongodb/1.1.30", "hashPath": "bluetape.mongodb.1.1.30.nupkg.sha512"}, "BlueTape.OBS/1.1.21": {"type": "package", "serviceable": true, "sha512": "sha512-qTJQW5sjnE6kopm2aRTW7qYGK5wWMpj+UeSwSJxFQzcamNxSMsN8Yu2uYJcnbRnshYJUP8jhjbwDUNpdM79msw==", "path": "bluetape.obs/1.1.21", "hashPath": "bluetape.obs.1.1.21.nupkg.sha512"}, "BlueTape.SNS/1.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-jxYIFk1V+PZ67t4m1jKoKNtnx3e8JXPPvtJ8l51XQDrFp8IBab0zOZeSyvN6SlJs6/GrsB8VKWlyJ5divaZ+fQ==", "path": "bluetape.sns/1.0.0", "hashPath": "bluetape.sns.1.0.0.nupkg.sha512"}, "BlueTape.Utilities/1.3.11": {"type": "package", "serviceable": true, "sha512": "sha512-ftdtNNURUpofPw1DoldMfKwIXzZDVvl1JhSlxvFwAGJGFfNKLeaU6y7DU04e2TxLHn3pb5YHg3jVuiYTyxkbWw==", "path": "bluetape.utilities/1.3.11", "hashPath": "bluetape.utilities.1.3.11.nupkg.sha512"}, "DnsClient/1.6.1": {"type": "package", "serviceable": true, "sha512": "sha512-4H/f2uYJOZ+YObZjpY9ABrKZI+JNw3uizp6oMzTXwDw6F+2qIPhpRl/1t68O/6e98+vqNiYGu+lswmwdYUy3gg==", "path": "dnsclient/1.6.1", "hashPath": "dnsclient.1.6.1.nupkg.sha512"}, "Elastic.CommonSchema/1.5.3": {"type": "package", "serviceable": true, "sha512": "sha512-JgwhfThYY/s17asUiBCUVqnZtDdGTWO/2hTPG01QDfw2+T6kfwskrj5eh6XpBZsOh6r9SpBL95vSsU+q44i7Zg==", "path": "elastic.commonschema/1.5.3", "hashPath": "elastic.commonschema.1.5.3.nupkg.sha512"}, "Elastic.CommonSchema.Serilog/1.5.3": {"type": "package", "serviceable": true, "sha512": "sha512-bp2qHOWmN15fTKUecFMt7oCra68I1cm3yAEmwPcLuz4v2pQ5YxC8nVtyCTSSibquUS/ZPH5JInjlmuywV3UoyQ==", "path": "elastic.commonschema.serilog/1.5.3", "hashPath": "elastic.commonschema.serilog.1.5.3.nupkg.sha512"}, "Microsoft.AspNetCore.Http.Abstractions/2.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-Nxs7Z1q3f1STfLYKJSVXCs1iBl+Ya6E8o4Oy1bCxJ/rNI44E/0f6tbsrVqAWfB7jlnJfyaAtIalBVxPKUPQb4Q==", "path": "microsoft.aspnetcore.http.abstractions/2.2.0", "hashPath": "microsoft.aspnetcore.http.abstractions.2.2.0.nupkg.sha512"}, "Microsoft.AspNetCore.Http.Features/2.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-ziFz5zH8f33En4dX81LW84I6XrYXKf9jg6aM39cM+LffN9KJahViKZ61dGMSO2gd3e+qe5yBRwsesvyqlZaSMg==", "path": "microsoft.aspnetcore.http.features/2.2.0", "hashPath": "microsoft.aspnetcore.http.features.2.2.0.nupkg.sha512"}, "Microsoft.Bcl.AsyncInterfaces/1.1.1": {"type": "package", "serviceable": true, "sha512": "sha512-yuvf07qFWFqtK3P/MRkEKLhn5r2UbSpVueRziSqj0yJQIKFwG1pq9mOayK3zE5qZCTs0CbrwL9M6R8VwqyGy2w==", "path": "microsoft.bcl.asyncinterfaces/1.1.1", "hashPath": "microsoft.bcl.asyncinterfaces.1.1.1.nupkg.sha512"}, "Microsoft.CSharp/4.7.0": {"type": "package", "serviceable": true, "sha512": "sha512-pTj+D3uJWyN3My70i2Hqo+OXixq3Os2D1nJ2x92FFo6sk8fYS1m1WLNTs0Dc1uPaViH0YvEEwvzddQ7y4rhXmA==", "path": "microsoft.csharp/4.7.0", "hashPath": "microsoft.csharp.4.7.0.nupkg.sha512"}, "Microsoft.Extensions.Caching.Abstractions/7.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-IeimUd0TNbhB4ded3AbgBLQv2SnsiVugDyGV1MvspQFVlA07nDC7Zul7kcwH5jWN3JiTcp/ySE83AIJo8yfKjg==", "path": "microsoft.extensions.caching.abstractions/7.0.0", "hashPath": "microsoft.extensions.caching.abstractions.7.0.0.nupkg.sha512"}, "Microsoft.Extensions.Caching.Memory/7.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-xpidBs2KCE2gw1JrD0quHE72kvCaI3xFql5/Peb2GRtUuZX+dYPoK/NTdVMiM67Svym0M0Df9A3xyU0FbMQhHw==", "path": "microsoft.extensions.caching.memory/7.0.0", "hashPath": "microsoft.extensions.caching.memory.7.0.0.nupkg.sha512"}, "Microsoft.Extensions.Configuration/2.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-SsI4RqI8EH00+cYO96tbftlh87sNUv1eeyuBU1XZdQkG0RrHAOjWgl7P0FoLeTSMXJpOnfweeOWj2d1/5H3FxA==", "path": "microsoft.extensions.configuration/2.0.0", "hashPath": "microsoft.extensions.configuration.2.0.0.nupkg.sha512"}, "Microsoft.Extensions.Configuration.Abstractions/6.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-qWzV9o+ZRWq+pGm+1dF+R7qTgTYoXvbyowRoBxQJGfqTpqDun2eteerjRQhq5PQ/14S+lqto3Ft4gYaRyl4rdQ==", "path": "microsoft.extensions.configuration.abstractions/6.0.0", "hashPath": "microsoft.extensions.configuration.abstractions.6.0.0.nupkg.sha512"}, "Microsoft.Extensions.Configuration.Binder/2.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-IznHHzGUtrdpuQqIUdmzF6TYPcsYHONhHh3o9dGp39sX/9Zfmt476UnhvU0UhXgJnXXAikt/MpN6AuSLCCMdEQ==", "path": "microsoft.extensions.configuration.binder/2.0.0", "hashPath": "microsoft.extensions.configuration.binder.2.0.0.nupkg.sha512"}, "Microsoft.Extensions.DependencyInjection/7.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-elNeOmkeX3eDVG6pYVeV82p29hr+UKDaBhrZyWvWLw/EVZSYEkZlQdkp0V39k/Xehs2Qa0mvoCvkVj3eQxNQ1Q==", "path": "microsoft.extensions.dependencyinjection/7.0.0", "hashPath": "microsoft.extensions.dependencyinjection.7.0.0.nupkg.sha512"}, "Microsoft.Extensions.DependencyInjection.Abstractions/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-cjWrLkJXK0rs4zofsK4bSdg+jhDLTaxrkXu4gS6Y7MAlCvRyNNgwY/lJi5RDlQOnSZweHqoyvgvbdvQsRIW+hg==", "path": "microsoft.extensions.dependencyinjection.abstractions/8.0.0", "hashPath": "microsoft.extensions.dependencyinjection.abstractions.8.0.0.nupkg.sha512"}, "Microsoft.Extensions.DependencyModel/3.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-Iaectmzg9Dc4ZbKX/FurrRjgO/I8rTumL5UU+Uube6vZuGetcnXoIgTA94RthFWePhdMVm8MMhVFJZdbzMsdyQ==", "path": "microsoft.extensions.dependencymodel/3.0.0", "hashPath": "microsoft.extensions.dependencymodel.3.0.0.nupkg.sha512"}, "Microsoft.Extensions.FileProviders.Abstractions/6.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-0pd4/fho0gC12rQswaGQxbU34jOS1TPS8lZPpkFCH68ppQjHNHYle9iRuHeev1LhrJ94YPvzcRd8UmIuFk23Qw==", "path": "microsoft.extensions.fileproviders.abstractions/6.0.0", "hashPath": "microsoft.extensions.fileproviders.abstractions.6.0.0.nupkg.sha512"}, "Microsoft.Extensions.Hosting.Abstractions/6.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-GcT5l2CYXL6Sa27KCSh0TixsRfADUgth+ojQSD5EkzisZxmGFh7CwzkcYuGwvmXLjr27uWRNrJ2vuuEjMhU05Q==", "path": "microsoft.extensions.hosting.abstractions/6.0.0", "hashPath": "microsoft.extensions.hosting.abstractions.6.0.0.nupkg.sha512"}, "Microsoft.Extensions.Http/6.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-15+pa2G0bAMHbHewaQIdr/y6ag2H3yh4rd9hTXavtWDzQBkvpe2RMqFg8BxDpcQWssmjmBApGPcw93QRz6YcMg==", "path": "microsoft.extensions.http/6.0.0", "hashPath": "microsoft.extensions.http.6.0.0.nupkg.sha512"}, "Microsoft.Extensions.Http.Polly/6.0.9": {"type": "package", "serviceable": true, "sha512": "sha512-+tQeERLaSPA+G//SlIZ5pyv/jAmkn1xnMMOvFu3Bag3EJxwV4D9iEkHD2TaNiJOoFZ/VROUB76/H7n/75e9Dow==", "path": "microsoft.extensions.http.polly/6.0.9", "hashPath": "microsoft.extensions.http.polly.6.0.9.nupkg.sha512"}, "Microsoft.Extensions.Logging/6.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-eIbyj40QDg1NDz0HBW0S5f3wrLVnKWnDJ/JtZ+yJDFnDj90VoPuoPmFkeaXrtu+0cKm5GRAwoDf+dBWXK0TUdg==", "path": "microsoft.extensions.logging/6.0.0", "hashPath": "microsoft.extensions.logging.6.0.0.nupkg.sha512"}, "Microsoft.Extensions.Logging.Abstractions/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-arDBqTgFCyS0EvRV7O3MZturChstm50OJ0y9bDJvAcmEPJm0FFpFyjU/JLYyStNGGey081DvnQYlncNX5SJJGA==", "path": "microsoft.extensions.logging.abstractions/8.0.0", "hashPath": "microsoft.extensions.logging.abstractions.8.0.0.nupkg.sha512"}, "Microsoft.Extensions.Options/8.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-wmpp+BSU3oGifaev6Z9rrlwHoITLFfpVOSbgBrOXjkbJSCXnZVCsoRGE5c3fJFI4VlNgnNkNlI9y+5jC4fmOEA==", "path": "microsoft.extensions.options/8.0.1", "hashPath": "microsoft.extensions.options.8.0.1.nupkg.sha512"}, "Microsoft.Extensions.Options.ConfigurationExtensions/2.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-Y/lGICwO27fCkQRK3tZseVzFjZaxfGmui990E67sB4MuiPzdJHnJDS/BeYWrHShSSBgCl4KyKRx4ux686fftPg==", "path": "microsoft.extensions.options.configurationextensions/2.0.0", "hashPath": "microsoft.extensions.options.configurationextensions.2.0.0.nupkg.sha512"}, "Microsoft.Extensions.Primitives/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-bXJEZrW9ny8vjMF1JV253WeLhpEVzFo1lyaZu1vQ4ZxWUlVvknZ/+ftFgVheLubb4eZPSwwxBeqS1JkCOjxd8g==", "path": "microsoft.extensions.primitives/8.0.0", "hashPath": "microsoft.extensions.primitives.8.0.0.nupkg.sha512"}, "Microsoft.Identity.Client/4.56.0": {"type": "package", "serviceable": true, "sha512": "sha512-rr4zbidvHy9r4NvOAs5hdd964Ao2A0pAeFBJKR95u1CJAVzbd1p6tPTXUZ+5ld0cfThiVSGvz6UHwY6JjraTpA==", "path": "microsoft.identity.client/4.56.0", "hashPath": "microsoft.identity.client.4.56.0.nupkg.sha512"}, "Microsoft.Identity.Client.Extensions.Msal/4.56.0": {"type": "package", "serviceable": true, "sha512": "sha512-H12YAzEGK55vZ+QpxUzozhW8ZZtgPDuWvgA0JbdIR9UhMUplj29JhIgE2imuH8W2Nw9D8JKygR1uxRFtpSNcrg==", "path": "microsoft.identity.client.extensions.msal/4.56.0", "hashPath": "microsoft.identity.client.extensions.msal.4.56.0.nupkg.sha512"}, "Microsoft.IdentityModel.Abstractions/8.0.2": {"type": "package", "serviceable": true, "sha512": "sha512-m73Bun0l0jL8rceWZ9TMD4hwQCjDIaRT1s5RMN7TBDpXu8Ea8KcRndo45btW9gG0i/USmHLCmOBIITvTA4Y6PA==", "path": "microsoft.identitymodel.abstractions/8.0.2", "hashPath": "microsoft.identitymodel.abstractions.8.0.2.nupkg.sha512"}, "Microsoft.IdentityModel.JsonWebTokens/8.0.2": {"type": "package", "serviceable": true, "sha512": "sha512-6CVWMfXrQPMUaqlsMfG8OjtyTIKvtgiQCFOJ2YhSZo1UDaAWweVN7jGSrz59Ez0Y8lh260WE5V2b0Oe9NlVlyw==", "path": "microsoft.identitymodel.jsonwebtokens/8.0.2", "hashPath": "microsoft.identitymodel.jsonwebtokens.8.0.2.nupkg.sha512"}, "Microsoft.IdentityModel.Logging/8.0.2": {"type": "package", "serviceable": true, "sha512": "sha512-iKUyFKCQgc8rcEqyIJGLOIqqxemG7bgraqS9n5J6RPoZZH7dwxmJd3aFYmxXuAnfznJuaE1DQX5U46Cqvb+BOg==", "path": "microsoft.identitymodel.logging/8.0.2", "hashPath": "microsoft.identitymodel.logging.8.0.2.nupkg.sha512"}, "Microsoft.IdentityModel.Tokens/8.0.2": {"type": "package", "serviceable": true, "sha512": "sha512-X58KyDBpGJZcCfmSgbkxJLLxd04eMFVaJlMEbRCyWL1X44n6kMxRyK6UTS1zgi5DHikeyiZj8bi7+p0kfPepLg==", "path": "microsoft.identitymodel.tokens/8.0.2", "hashPath": "microsoft.identitymodel.tokens.8.0.2.nupkg.sha512"}, "Microsoft.NETCore.Platforms/5.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-VyPlqzH2wavqquTcYpkIIAQ6WdenuKoFN0BdYBbCWsclXacSOHNQn66Gt4z5NBqEYW0FAPm5rlvki9ZiCij5xQ==", "path": "microsoft.netcore.platforms/5.0.0", "hashPath": "microsoft.netcore.platforms.5.0.0.nupkg.sha512"}, "Microsoft.Win32.Registry/5.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-dDoKi0PnDz31yAyETfRntsLArTlVAVzUzCIvvEDsDsucrl33Dl8pIJG06ePTJTI3tGpeyHS9Cq7Foc/s4EeKcg==", "path": "microsoft.win32.registry/5.0.0", "hashPath": "microsoft.win32.registry.5.0.0.nupkg.sha512"}, "MongoDB.Bson/2.25.0": {"type": "package", "serviceable": true, "sha512": "sha512-xQx/qtC2nu9oGiyNqAwfiDpUMweLi0nID677cyKykpwmj5AVMrnd//UwmcmuX95178DeY6rf7cjmA613TQXPiA==", "path": "mongodb.bson/2.25.0", "hashPath": "mongodb.bson.2.25.0.nupkg.sha512"}, "MongoDB.Driver/2.25.0": {"type": "package", "serviceable": true, "sha512": "sha512-dMqnZTV6MuvoEI4yFtSvKJdAoN6NeyAEvG8aoxnrLIVd7bR84QxLgpsM1nhK17qkOcIx/IrpMIfrvp5iMnYGBg==", "path": "mongodb.driver/2.25.0", "hashPath": "mongodb.driver.2.25.0.nupkg.sha512"}, "MongoDB.Driver.Core/2.25.0": {"type": "package", "serviceable": true, "sha512": "sha512-oN4nLgO5HQEThTg/zqeoHqaO2+q64DBVb4r7BvhaFb0p0TM9jZKnCKvh1EA8d9E9swIz0CgvMrvL1mPyRCZzag==", "path": "mongodb.driver.core/2.25.0", "hashPath": "mongodb.driver.core.2.25.0.nupkg.sha512"}, "MongoDB.Libmongocrypt/1.8.2": {"type": "package", "serviceable": true, "sha512": "sha512-z/8JCULSHM1+mzkau0ivIkU9kIn8JEFFSkmYTSaMaWMMHt96JjUtMKuXxeGNGSnHZ5290ZPKIlQfjoWFk2sKog==", "path": "mongodb.libmongocrypt/1.8.2", "hashPath": "mongodb.libmongocrypt.1.8.2.nupkg.sha512"}, "Newtonsoft.Json/13.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-ppPFpBcvxdsfUonNcvITKqLl3bqxWbDCZIzDWHzjpdAHRFfZe0Dw9HmA0+za13IdyrgJwpkDTDA9fHaxOrt20A==", "path": "newtonsoft.json/13.0.1", "hashPath": "newtonsoft.json.13.0.1.nupkg.sha512"}, "Polly/7.2.3": {"type": "package", "serviceable": true, "sha512": "sha512-DeCY0OFbNdNxsjntr1gTXHJ5pKUwYzp04Er2LLeN3g6pWhffsGuKVfMBLe1lw7x76HrPkLxKEFxBlpRxS2nDEQ==", "path": "polly/7.2.3", "hashPath": "polly.7.2.3.nupkg.sha512"}, "Polly.Extensions.Http/3.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-drrG+hB3pYFY7w1c3BD+lSGYvH2oIclH8GRSehgfyP5kjnFnHKQuuBhuHLv+PWyFuaTDyk/vfRpnxOzd11+J8g==", "path": "polly.extensions.http/3.0.0", "hashPath": "polly.extensions.http.3.0.0.nupkg.sha512"}, "Portable.BouncyCastle/1.9.0": {"type": "package", "serviceable": true, "sha512": "sha512-eZZBCABzVOek+id9Xy04HhmgykF0wZg9wpByzrWN7q8qEI0Qen9b7tfd7w8VA3dOeesumMG7C5ZPy0jk7PSRHw==", "path": "portable.bouncycastle/1.9.0", "hashPath": "portable.bouncycastle.1.9.0.nupkg.sha512"}, "Serilog/2.12.0": {"type": "package", "serviceable": true, "sha512": "sha512-xaiJLIdu6rYMKfQMYUZgTy8YK7SMZjB4Yk50C/u//Z4OsvxkUfSPJy4nknfvwAC34yr13q7kcyh4grbwhSxyZg==", "path": "serilog/2.12.0", "hashPath": "serilog.2.12.0.nupkg.sha512"}, "Serilog.AspNetCore/6.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-5XW90k62V7G9I0D/j9Iz+NyRBB6/SnoFpHUPeLnV40gONV2vs2A/ewWi91QVjQmyHBfzFeqIrkvE/DJMZ0alTg==", "path": "serilog.aspnetcore/6.0.1", "hashPath": "serilog.aspnetcore.6.0.1.nupkg.sha512"}, "Serilog.Enrichers.GlobalLogContext/2.1.0": {"type": "package", "serviceable": true, "sha512": "sha512-BtSFymdnkHWYpxJzLAQ7J03pdnkIlQ3kQLswBRz37j2XllEp5cnmCOOTqdw7Hyh8TrHQ1nJSZZ+HTjV4AqTwhA==", "path": "serilog.enrichers.globallogcontext/2.1.0", "hashPath": "serilog.enrichers.globallogcontext.2.1.0.nupkg.sha512"}, "Serilog.Extensions.Hosting/5.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-o0VUyt3npAqOJaZ6CiWLFeLYs3CYJwfcAqaUqprzsmj7qYIvorcn8cZLVR8AQX6vzX7gee2bD0sQeA17iO2/Aw==", "path": "serilog.extensions.hosting/5.0.1", "hashPath": "serilog.extensions.hosting.5.0.1.nupkg.sha512"}, "Serilog.Extensions.Logging/3.1.0": {"type": "package", "serviceable": true, "sha512": "sha512-IWfem7wfrFbB3iw1OikqPFNPEzfayvDuN4WP7Ue1AVFskalMByeWk3QbtUXQR34SBkv1EbZ3AySHda/ErDgpcg==", "path": "serilog.extensions.logging/3.1.0", "hashPath": "serilog.extensions.logging.3.1.0.nupkg.sha512"}, "Serilog.Formatting.Compact/1.1.0": {"type": "package", "serviceable": true, "sha512": "sha512-pNroKVjo+rDqlxNG5PXkRLpfSCuDOBY0ri6jp9PLe505ljqwhwZz8ospy2vWhQlFu5GkIesh3FcDs4n7sWZODA==", "path": "serilog.formatting.compact/1.1.0", "hashPath": "serilog.formatting.compact.1.1.0.nupkg.sha512"}, "Serilog.Settings.Configuration/3.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-7GNudISZwqaT902hqEL2OFGTZeUFWfnrNLupJkOqeF41AR3GjcxX+Hwb30xb8gG2/CDXsCMVfF8o0+8KY0fJNg==", "path": "serilog.settings.configuration/3.3.0", "hashPath": "serilog.settings.configuration.3.3.0.nupkg.sha512"}, "Serilog.Sinks.Console/4.1.0": {"type": "package", "serviceable": true, "sha512": "sha512-K6N5q+5fetjnJPvCmkWOpJ/V8IEIoMIB1s86OzBrbxwTyHxdx3pmz4H+8+O/Dc/ftUX12DM1aynx/dDowkwzqg==", "path": "serilog.sinks.console/4.1.0", "hashPath": "serilog.sinks.console.4.1.0.nupkg.sha512"}, "Serilog.Sinks.Debug/2.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-Y6g3OBJ4JzTyyw16fDqtFcQ41qQAydnEvEqmXjhwhgjsnG/FaJ8GUqF5ldsC/bVkK8KYmqrPhDO+tm4dF6xx4A==", "path": "serilog.sinks.debug/2.0.0", "hashPath": "serilog.sinks.debug.2.0.0.nupkg.sha512"}, "Serilog.Sinks.File/5.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-uwV5hdhWPwUH1szhO8PJpFiahqXmzPzJT/sOijH/kFgUx+cyoDTMM8MHD0adw9+Iem6itoibbUXHYslzXsLEAg==", "path": "serilog.sinks.file/5.0.0", "hashPath": "serilog.sinks.file.5.0.0.nupkg.sha512"}, "Serilog.Sinks.Http/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-eHyl2/93Roymf2eudPl/6Eeu2GQ93Ucy4GM1UPF0jyd7CIW8r+Bk5ohdbjjyjB9TQSpP2ovOuj6ltf9DjoWHtg==", "path": "serilog.sinks.http/8.0.0", "hashPath": "serilog.sinks.http.8.0.0.nupkg.sha512"}, "Serilog.Sinks.Logz.Io/7.1.0": {"type": "package", "serviceable": true, "sha512": "sha512-uZN5FWMvpk0m/6u7PiKShODbUvfxQZGzK+D91BaxV1ePVJSE0xaqJwCh7rp8/mhPp2C0UPmXOt1hAF306Fzn1Q==", "path": "serilog.sinks.logz.io/7.1.0", "hashPath": "serilog.sinks.logz.io.7.1.0.nupkg.sha512"}, "Serilog.Sinks.PeriodicBatching/3.1.0": {"type": "package", "serviceable": true, "sha512": "sha512-NDWR7m3PalVlGEq3rzoktrXikjFMLmpwF0HI4sowo8YDdU+gqPlTHlDQiOGxHfB0sTfjPA9JjA7ctKG9zqjGkw==", "path": "serilog.sinks.periodicbatching/3.1.0", "hashPath": "serilog.sinks.periodicbatching.3.1.0.nupkg.sha512"}, "SharpCompress/0.30.1": {"type": "package", "serviceable": true, "sha512": "sha512-XqD4TpfyYGa7QTPzaGlMVbcecKnXy4YmYLDWrU+JIj7IuRNl7DH2END+Ll7ekWIY8o3dAMWLFDE1xdhfIWD1nw==", "path": "sharpcompress/0.30.1", "hashPath": "sharpcompress.0.30.1.nupkg.sha512"}, "Snappier/1.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-rFtK2KEI9hIe8gtx3a0YDXdHOpedIf9wYCEYtBEmtlyiWVX3XlCNV03JrmmAi/Cdfn7dxK+k0sjjcLv4fpHnqA==", "path": "snappier/1.0.0", "hashPath": "snappier.1.0.0.nupkg.sha512"}, "System.Buffers/4.5.1": {"type": "package", "serviceable": true, "sha512": "sha512-Rw7ijyl1qqRS0YQD/WycNst8hUUMgrMH4FCn1nNm27M4VxchZ1js3fVjQaANHO5f3sN4isvP4a+Met9Y4YomAg==", "path": "system.buffers/4.5.1", "hashPath": "system.buffers.4.5.1.nupkg.sha512"}, "System.Diagnostics.DiagnosticSource/6.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-KiLYDu2k2J82Q9BJpWiuQqCkFjRBWVq4jDzKKWawVi9KWzyD0XG3cmfX0vqTQlL14Wi9EufJrbL0+KCLTbqWiQ==", "path": "system.diagnostics.diagnosticsource/6.0.1", "hashPath": "system.diagnostics.diagnosticsource.6.0.1.nupkg.sha512"}, "System.IdentityModel.Tokens.Jwt/8.0.2": {"type": "package", "serviceable": true, "sha512": "sha512-jbfANr2qEmrfEtK3L7tOnkCW5/y2YiF6ISSRhRBgIZL+W2ZbEVHFNTNV8QOKeNU6gedQnhpdU2IvB0YB3nNMjw==", "path": "system.identitymodel.tokens.jwt/8.0.2", "hashPath": "system.identitymodel.tokens.jwt.8.0.2.nupkg.sha512"}, "System.IO.FileSystem.AccessControl/5.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-SxHB3nuNrpptVk+vZ/F+7OHEpoHUIKKMl02bUmYHQr1r+glbZQxs7pRtsf4ENO29TVm2TH3AEeep2fJcy92oYw==", "path": "system.io.filesystem.accesscontrol/5.0.0", "hashPath": "system.io.filesystem.accesscontrol.5.0.0.nupkg.sha512"}, "System.Memory/4.5.5": {"type": "package", "serviceable": true, "sha512": "sha512-XIWiDvKPXaTveaB7HVganDlOCRoj03l+jrwNvcge/t8vhGYKvqV+dMv6G4SAX2NoNmN0wZfVPTAlFwZcZvVOUw==", "path": "system.memory/4.5.5", "hashPath": "system.memory.4.5.5.nupkg.sha512"}, "System.Memory.Data/1.0.2": {"type": "package", "serviceable": true, "sha512": "sha512-JGkzeqgBsiZwKJZ1IxPNsDFZDhUvuEdX8L8BDC8N3KOj+6zMcNU28CNN59TpZE/VJYy9cP+5M+sbxtWJx3/xtw==", "path": "system.memory.data/1.0.2", "hashPath": "system.memory.data.1.0.2.nupkg.sha512"}, "System.Numerics.Vectors/4.5.0": {"type": "package", "serviceable": true, "sha512": "sha512-QQTlPTl06J/iiDbJCiepZ4H//BVraReU4O4EoRw1U02H5TLUIT7xn3GnDp9AXPSlJUDyFs4uWjWafNX6WrAojQ==", "path": "system.numerics.vectors/4.5.0", "hashPath": "system.numerics.vectors.4.5.0.nupkg.sha512"}, "System.Runtime.CompilerServices.Unsafe/6.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-/iUeP3tq1S0XdNNoMz5C9twLSrM/TH+qElHkXWaPvuNOt+99G75NrV0OS2EqHx5wMN7popYjpc8oTjC1y16DLg==", "path": "system.runtime.compilerservices.unsafe/6.0.0", "hashPath": "system.runtime.compilerservices.unsafe.6.0.0.nupkg.sha512"}, "System.Security.AccessControl/5.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-dagJ1mHZO3Ani8GH0PHpPEe/oYO+rVdbQjvjJkBRNQkX4t0r1iaeGn8+/ybkSLEan3/slM0t59SVdHzuHf2jmw==", "path": "system.security.accesscontrol/5.0.0", "hashPath": "system.security.accesscontrol.5.0.0.nupkg.sha512"}, "System.Security.Cryptography.ProtectedData/4.7.0": {"type": "package", "serviceable": true, "sha512": "sha512-ehYW0m9ptxpGWvE4zgqongBVWpSDU/JCFD4K7krxkQwSz/sFQjEXCUqpvencjy6DYDbn7Ig09R8GFffu8TtneQ==", "path": "system.security.cryptography.protecteddata/4.7.0", "hashPath": "system.security.cryptography.protecteddata.4.7.0.nupkg.sha512"}, "System.Security.Principal.Windows/5.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-t0MGLukB5WAVU9bO3MGzvlGnyJPgUlcwerXn1kzBRjwLKixT96XV0Uza41W49gVd8zEMFu9vQEFlv0IOrytICA==", "path": "system.security.principal.windows/5.0.0", "hashPath": "system.security.principal.windows.5.0.0.nupkg.sha512"}, "System.Text.Encodings.Web/4.7.2": {"type": "package", "serviceable": true, "sha512": "sha512-iTUgB/WtrZ1sWZs84F2hwyQhiRH6QNjQv2DkwrH+WP6RoFga2Q1m3f9/Q7FG8cck8AdHitQkmkXSY8qylcDmuA==", "path": "system.text.encodings.web/4.7.2", "hashPath": "system.text.encodings.web.4.7.2.nupkg.sha512"}, "System.Text.Json/4.7.2": {"type": "package", "serviceable": true, "sha512": "sha512-TcMd95wcrubm9nHvJEQs70rC0H/8omiSGGpU4FQ/ZA1URIqD4pjmFJh2Mfv1yH1eHgJDWTi2hMDXwTET+zOOyg==", "path": "system.text.json/4.7.2", "hashPath": "system.text.json.4.7.2.nupkg.sha512"}, "System.Threading.Tasks.Extensions/4.5.4": {"type": "package", "serviceable": true, "sha512": "sha512-zteT+G8xuGu6mS+mzDzYXbzS7rd3K6Fjb9RiZlYlJPam2/hU7JCBZBVEcywNuR+oZ1ncTvc/cq0faRr3P01OVg==", "path": "system.threading.tasks.extensions/4.5.4", "hashPath": "system.threading.tasks.extensions.4.5.4.nupkg.sha512"}, "ZstdSharp.Port/0.7.3": {"type": "package", "serviceable": true, "sha512": "sha512-U9Ix4l4cl58Kzz1rJzj5hoVTjmbx1qGMwzAcbv1j/d3NzrFaESIurQyg+ow4mivCgkE3S413y+U9k4WdnEIkRA==", "path": "zstdsharp.port/0.7.3", "hashPath": "zstdsharp.port.0.7.3.nupkg.sha512"}, "BlueTape.Aion.DataAccess.MongoDB/1.0.0": {"type": "project", "serviceable": false, "sha512": ""}, "BlueTape.Aion.Domain/1.0.0": {"type": "project", "serviceable": false, "sha512": ""}, "BueTape.Aion.Infrastructure/1.0.0": {"type": "project", "serviceable": false, "sha512": ""}}}