import { BaseTest } from '../../../tests/test-utils';
import {BasePage} from '../../base.page';

export class UnifiedApplication extends BasePage {
    constructor(page){
        super(page);
    };

    containers = {
        businessAddressStreetContainer: this.page.locator('[data-testid="business_address.address"]'),
        usersAddressStreetContainer: this.page.locator('[data-testid="user_address.address"]'),
        ownerAddressStreetContainer: this.page.locator('[data-testid="owner_address.address"]'),
        entityAddressStreetContainer: this.page.locator('[data-testid="entity_address.address"]'),
    };

    tiles = {
        continueApp: this.page.locator('_react=[testID="CreditStatusCard"]'),
    };

    buttons = {
        start: this.page.locator('"Start"'),
        next: this.page.locator('[data-testid="ApplicationNextButton"]'),
        skip: this.page.locator('[data-testid="ApplicationSkipButton"]'),
        submit: this.page.locator('[data-testid="ApplicationSubmitButton"]'),
        addBankAccount: this.page.locator('"Add Account"'),
        inviteContinueSigner: this.page.locator('"Invite & Continue"'),
        agreeAndContinue: this.page.locator('[data-testid="agree"]'),
        agreeAndSubmit: this.page.locator('[data-testid="ApplicationSubmitButton"]'),
        agreeAndPay: this.page.locator('//div[text()="Agree & Pay"]'),
        thirtyDaysPayment: this.page.locator('"30 days"'),
        sixtyDaysPayment: this.page.locator('"60 days"'),
        ninetyDaysPayment: this.page.locator('"90 days"'),
        continue: this.page.locator('//div[text()="Continue"]'),
        okayThanks: this.page.locator('"Okay, Thanks"')
    };

    businessDetails = {
        businessCategory: this.page.locator('[data-testid="BusinessCategoryInput"]'),
        businessName: this.page.locator('[data-testid="BusinessNameInput"] input'),
        businessPhoneNumber: this.page.locator('[data-testid="BusinessPhoneInput"] input'),
        businessStreetAddress: this.page.locator('input[data-testid="business_address.address"]'),
        businessZipCode: this.page.locator('[data-testid="business_address.zip"] input'),
        businessCity: this.page.locator('[data-testid="business_address.city"] input'),
        businessState: this.page.locator('[data-testid="business_address.state"] input'),
        businessStartDate: this.page.locator('[data-testid="BusinessStartDateInput"] input'),
        businessType: this.page.locator('[data-testid="BusinessTypeInput"]'),
        businessTypeItem: this.page.locator('[data-testid="BusinessTypeInput-0"]'),
        businessStreetList: this.containers.businessAddressStreetContainer.locator('[class="css-1dbjc4n r-1awozwy r-14lw9ot r-1loqt21 r-18u37iz r-10paoce r-1otgn73 r-1i6wzkk r-lrvibr"]'),
        businessPercentage: this.page.locator('[data-testid="OwnershipPercentage"]'),

        taxId: this.page.locator('[data-testid="businessInfo_ein"] input'),
        annualRevenue: this.page.locator('[data-testid="finance_revenue"] input'),
        debt: this.page.locator('input[inputmode="numeric"]'),
        significantShareholderYes: this.page.locator('[data-testid="businessOwner_isOwner.Yes"]'),
        significantShareholderNo: this.page.locator('[data-testid="businessOwner_isOwner.No"]'),
        indivialOwnership: this.page.locator('[data-testid="radioButton.Individual"]'),
        anotherEntityOwnership: this.page.locator('[data-testid="radioButton.Entity"]'),
        noOwnership: this.page.locator('[data-testid="radioButton.No"]'),

        ownersFirstName: this.page.locator('[data-testid="OwnerFirstname"] input'),
        ownersLastName: this.page.locator('[data-testid="OwnerLastname"] input'),
        ownersPhone: this.page.locator('[data-testid="ownerPhone"] input'),
        ownersEmail: this.page.locator('[data-testid="OwnerEmail"] input'),
        ownersStreetAddress: this.page.locator('input[data-testid="owner_address.address"]'),
        ownersZipCode: this.page.locator('[data-testid="owner_address.zip"] input'),
        ownersCity: this.page.locator('[data-testid="owner_address.city"] input'),
        ownersState: this.page.locator('[data-testid="owner_address.state"] input'),
        ownersStreetList: this.containers.ownerAddressStreetContainer.locator('[class="css-1dbjc4n r-1awozwy r-14lw9ot r-1loqt21 r-18u37iz r-10paoce r-1otgn73 r-1i6wzkk r-lrvibr"]'),
        ownersDateOfBirth: this.page.locator('[data-testid="owner_birthdate"] input'),
        ownersSocialSecurityNumber: this.page.locator('[data-testid="owner_ssn"] input'),

        entityName: this.page.locator('[data-testid="EntityName"] input'),
        entityTaxId: this.page.locator('[data-testid="ownerEntity_taxid"] input'),
        entityStreetAddress: this.page.locator('[data-testid="entity_address.address"]'),
        entityStreetList: this.containers.entityAddressStreetContainer.locator('[class="css-1dbjc4n r-1awozwy r-14lw9ot r-1loqt21 r-18u37iz r-10paoce r-1otgn73 r-1i6wzkk r-lrvibr"]'),
        entityZipCode: this.page.locator('[data-testid="entity_address.zip"] input'),
        entityCity: this.page.locator('[data-testid="entity_address.city"] input'),
        entityState: this.page.locator('[data-testid="entity_address.state"] input'),
    };

    authorizedSignerDetails = {
        authorizedSignerYes: this.page.locator('[data-testid="businessOwner_isAuthorized.Yes"]'),
        authorizedSignerNo: this.page.locator('[data-testid="businessOwner_isAuthorized.No"]'),
        signerFirstName: this.page.locator('[data-testid="invitee_first_name"] input'),
        signerLastName: this.page.locator('[data-testid="invitee_lasr_name"] input'),
        signerPhone: this.page.locator('[data-testid="invitee_phone"] input'),
        signerEmail: this.page.locator('[data-testid="invitee_email"] input'),
    };

    usersDetais = {
        usersStreetAddress: this.page.locator('input[data-testid="user_address.address"]'),
        usersZipCode: this.page.locator('[data-testid="user_address.zip"] input'),
        usersCity: this.page.locator('[data-testid="user_address.city"] input'),
        usersState: this.page.locator('[data-testid="user_address.state"] input'),
        userDateOfBirth: this.page.locator('[data-testid="user_birthdate"] input'),
        userSocialSecurityNumber: this.page.locator('[data-testid="businessOwner_ssn"] input'),
        usersStreetList: this.containers.usersAddressStreetContainer.locator('[class="css-1dbjc4n r-1awozwy r-14lw9ot r-1loqt21 r-18u37iz r-10paoce r-1otgn73 r-1i6wzkk r-lrvibr"]'),
    };

    bankInformation = {
        nameOnBankAccount: this.page.locator('[data-testid="BankHolderName"] input'),
        accountNumber: this.page.locator('[data-testid="BankAccountNumber"] input'),
        routingNumber: this.page.locator('[data-testid="BankRoutingNumber"] input'),
        searchYourBank: this.page.locator('[data-testid="bank_details_search"]'),
        searchYourBankList: this.page.locator('[class="css-1dbjc4n r-1awozwy r-14lw9ot r-1loqt21 r-18u37iz r-10paoce r-1otgn73 r-1i6wzkk r-lrvibr"]'),
        enterBankManually: this.page.locator('"Enter bank details manually"'),
        businessCheckingType: this.page.locator('"Business checking"'),
        bankErpField: this.page.locator('[data-testid="BankErpInput"]'),
        bankErpItem: this.page.locator('[data-testid="BankErpInput-16"]'),
        bankErpClose: this.page.locator('[data-testid="ApplicationTitle"]'),
        enterManuallyBtn: this.page.locator('[data-testid="BankConfirmationGoBack"]'),
        bankTile: this.page.locator('[class="css-1dbjc4n r-1xfd6ze r-1loqt21 r-18u37iz r-1otgn73 r-1i6wzkk r-lrvibr"]'),
    };

    applicationDetails = {
        filledSections: this.page.locator('"You have filled in the following section of the application"'),
        applicationSubmitted: this.page.locator('"Application Submitted" >> nth=1'), //TODO: investigate locator
        okayBtn: this.page.locator('"Okay, Thanks" >>nth=1'), //TODO: investigate locator
    };

    chooseEnteringBankAccountModal = {
        closeModal: this.page.locator('[data-testid="close"]'),
        enterBankDetailsManually: this.page.locator('[data-testid="BankConfirmationGoBack"]'),
        logInToBankAccount: this.page.locator('[data-testid="BankConfirmationContinue"]'),
    };

    async fillUpBusinessAddress(businessStreetAddress){
        await this.businessDetails.businessStreetAddress.click();
        await this.businessDetails.businessStreetAddress.fill(businessStreetAddress);
        await this.waitForAddressToAppear(this.businessDetails.businessStreetList, businessStreetAddress);
        await this.businessDetails.businessStreetList.first().click();
    };

    async fillUpBusinessDetailsForBusinessOwner(businessStartDate, taxId, annualRevenue, businessPercentage){
        await this.buttons.next.click();
        await this.businessDetails.businessStartDate.fill(businessStartDate);
        await this.buttons.next.click();
        await this.businessDetails.businessType.click();
        await this.businessDetails.businessTypeItem.click();
        await this.buttons.next.click();
        await this.businessDetails.taxId.fill(taxId);
        await this.buttons.next.click();
        await this.businessDetails.annualRevenue.fill(annualRevenue);        
        await this.buttons.next.click();
        await this.businessDetails.significantShareholderYes.click();
        await this.buttons.next.click();
        await this.businessDetails.businessPercentage.fill(businessPercentage);
        await this.buttons.next.click();
    };

    async fillUpCustomerDetailsForBusinessOwner(businessStartDate, taxId, annualRevenue, businessPercentage){
        await this.buttons.next.click();
        await this.businessDetails.businessStartDate.fill(businessStartDate);
        await this.buttons.next.click();
        await this.businessDetails.businessType.click();
        await this.businessDetails.businessTypeItem.click();
        await this.buttons.next.click();
        await this.businessDetails.taxId.fill(taxId);
        await this.buttons.next.click();
        await this.businessDetails.annualRevenue.fill(annualRevenue);        
        await this.buttons.next.click();
        await this.businessDetails.debt.fill('10000');  
        await this.buttons.next.click();
        //
        await this.businessDetails.significantShareholderYes.click();
        await this.buttons.next.click();
        await this.businessDetails.businessPercentage.fill(businessPercentage);
        await this.buttons.next.click();
    };

    async fillUpBusinessDetailsForNoBusinessOwner(response, businessStartDate, taxId, annualRevenue, ownersStreetAddress){
        await this.buttons.next.click();
        await this.businessDetails.businessStartDate.fill(businessStartDate);
        await this.buttons.next.click();
        await this.businessDetails.businessType.click();
        await this.businessDetails.businessTypeItem.click();
        await this.buttons.next.click();
        await this.businessDetails.taxId.fill(taxId);
        await this.buttons.next.click();
        await this.businessDetails.annualRevenue.fill(annualRevenue);        
        await this.buttons.next.click();
        await this.businessDetails.significantShareholderNo.click();
        await this.buttons.next.click();

        switch(response) {
            case 'yes,individual':
                await this.businessDetails.indivialOwnership.click();
                await this.buttons.next.click();
                await this.businessDetails.ownersFirstName.fill('test');
                await this.businessDetails.ownersLastName.fill('test');
                await this.buttons.next.click();
                await this.businessDetails.ownersPhone.fill('************');
                await this.buttons.next.click();
                await this.businessDetails.ownersEmail.fill('<EMAIL>');
                await this.buttons.next.click();
                await this.businessDetails.ownersStreetAddress.click();
                await this.businessDetails.ownersStreetAddress.fill(ownersStreetAddress);
                await this.waitForAddressToAppear(this.businessDetails.ownersStreetList, ownersStreetAddress);
                await this.businessDetails.ownersStreetList.first().click();
                await this.buttons.next.click();
                await this.businessDetails.ownersDateOfBirth.fill('12021999');
                await this.buttons.next.click();
                await this.businessDetails.ownersSocialSecurityNumber.fill('*********');
                await this.buttons.next.click();
                break;
            case 'yes,another entity':
                await this.businessDetails.anotherEntityOwnership.click();
                await this.buttons.next.click();
                await this.businessDetails.entityName.fill('test');
                await this.buttons.next.click();
                await this.businessDetails.entityTaxId.fill('*********');
                await this.buttons.next.click();
                await this.businessDetails.entityStreetAddress.click();
                await this.businessDetails.entityStreetAddress.fill(ownersStreetAddress);
                await this.waitForAddressToAppear(this.businessDetails.entityStreetList, ownersStreetAddress);
                await this.businessDetails.entityStreetList.first().click();
                await this.buttons.next.click();
                break;
            case 'no':
                await this.businessDetails.noOwnership.click();
                await this.buttons.next.click();
            default:
                //todo add exception
        }
    };

    async fillAuthorizedSignerDetails(response, usersStreetAddress, userDateOfBirth, userSocialSecurityNumber){
        switch(response) {
            case 'yes':
                await this.authorizedSignerDetails.authorizedSignerYes.click();
                await this.buttons.next.click();
                await this.fillUpUsersDetails(usersStreetAddress, userDateOfBirth, userSocialSecurityNumber);
                break;
            case 'no':
                await this.authorizedSignerDetails.authorizedSignerNo.click();
                await this.buttons.next.click();
                await this.authorizedSignerDetails.signerFirstName.fill('test');
                await this.authorizedSignerDetails.signerLastName.fill('test');
                await this.authorizedSignerDetails.signerPhone.fill('************');
                await this.authorizedSignerDetails.signerEmail.fill('<EMAIL>');
                await this.buttons.inviteContinueSigner.click();
                await this.fillUpUsersDetails(usersStreetAddress, userDateOfBirth, userSocialSecurityNumber);
                break;
        }
    };

    async fillUpUsersDetails(usersStreetAddress, userDateOfBirth, userSocialSecurityNumber){
        await this.usersDetais.usersStreetAddress.click();
        await this.usersDetais.usersStreetAddress.fill(usersStreetAddress);
        await this.waitForAddressToAppear(this.usersDetais.usersStreetList, usersStreetAddress);
        await this.usersDetais.usersStreetList.first().click();
        await this.buttons.next.click();
        await this.usersDetais.userDateOfBirth.fill(userDateOfBirth);
        await this.buttons.next.click();
        await this.usersDetais.userSocialSecurityNumber.fill(userSocialSecurityNumber);
        await this.buttons.next.click();
    };

    async fillUpBankInformation(bankName, accountName, accountNumber, routingNumber){
        await this.page.waitForLoadState('networkidle');
        await this.bankInformation.searchYourBank.fill(bankName);
        await this.page.waitForTimeout(2000);
        await this.bankInformation.searchYourBankList.first().click();
        await this.bankInformation.enterBankManually.click();
        await this.chooseEnteringBankAccountModal.enterBankDetailsManually.click();
        await this.bankInformation.nameOnBankAccount.fill(accountName);
        await this.bankInformation.accountNumber.fill(accountNumber);
        await this.bankInformation.routingNumber.fill(routingNumber);
        await this.buttons.addBankAccount.click();
        await this.bankInformation.bankTile.isVisible();
        await this.page.waitForTimeout(5000);
        await this.buttons.next.click({delay: 500});
    };

    async completeUnifiedApp(){
        await this.page.waitForLoadState('networkidle');
        await BaseTest.clickWithDelay(this.tiles.continueApp);
        await this.businessDetails.businessCategory.click();
        await this.page.locator(`"General Contractor"`).click();
        await BaseTest.clickWithDelay(this.buttons.next);
        await this.page.waitForLoadState('networkidle');
        await BaseTest.clickWithDelay(this.buttons.next);
        await this.page.waitForLoadState('networkidle');
        await BaseTest.clickWithDelay(this.buttons.next);
    };

    async waitForAddressToAppear(element, address, retries = 20){ // fix the string that we pass to this method
        for(let i = 0; i < retries; i++){
            if(await element.first().textContent() == address){
                break;
            } else{
                this.page.waitForTimeout(500);
            } 
        }
    };

    async agreeAndPayWithLoan(){
        await this.page.waitForLoadState('networkidle');
        await this.buttons.agreeAndContinue.click();
        await this.buttons.agreeAndSubmit.click();
        await this.page.waitForLoadState('networkidle');
        await BaseTest.clickWithDelay(this.buttons.ninetyDaysPayment);
        await this.page.waitForLoadState('networkidle');
        await this.buttons.continue.click();
        await this.page.waitForTimeout(3000);
        await BaseTest.clickWithDelay(this.buttons.agreeAndPay);
    };
}