using BlueTape.CompanyService.Common.Functions.AccountStatus;
using BlueTape.CompanyService.Common.Functions.AccountStatus.Constants.AccountStatusChangeEvents;
using BlueTape.ServiceBusMessaging.Attributes;
using BlueTape.Services.DecisionEngine.BusinessLogic.Abstractions.Services;
using BlueTape.Services.DecisionEngine.BusinessLogic.Abstractions.Steps.Base;
using BlueTape.Services.DecisionEngine.BusinessLogic.Constants;
using BlueTape.Services.DecisionEngine.BusinessLogic.Models.Credit;
using BlueTape.Services.DecisionEngine.BusinessLogic.Models.CreditApplication;
using BlueTape.Services.DecisionEngine.BusinessLogic.Services;
using BlueTape.Services.DecisionEngine.Infrastructure.Exceptions;
using BlueTape.SNS.SlackNotification.Models;
using BlueTape.Utilities.Providers;
using System.Net;

namespace BlueTape.Services.DecisionEngine.BusinessLogic.Tests.Services;

public class StepHandlingServiceTests
{
    private readonly IAccountStatusChangeService _mockAccountStatusChangeService = Substitute.For<IAccountStatusChangeService>();
    private readonly ILoanService _mockLoanService = Substitute.For<ILoanService>();
    private readonly IOnBoardingService _mockOnBoardingService = Substitute.For<IOnBoardingService>();
    private readonly IErrorNotificationService _mockErrorNotificationService = Substitute.For<IErrorNotificationService>();
    private readonly IDateProvider _mockDateProvider = Substitute.For<IDateProvider>();
    private readonly StepHandlingService _service;

    private const string StepStatus = "HardFail";
    private const string ErrorMessage = "TestErrorMessage";
    private const string ExecutionId = "ExecutionId";
    private const string CreditApplicationId = "creditApplicationId";
    private readonly string _stepName = StepsImplementationConstants.CashFlowReviewStep;

    public StepHandlingServiceTests()
    {
        _mockDateProvider.CurrentDateTime.Returns(DateTime.UtcNow);
        _service = new StepHandlingService(
            _mockAccountStatusChangeService,
            _mockLoanService,
            _mockDateProvider,
            _mockOnBoardingService,
            _mockErrorNotificationService);
    }

    [Fact]
    public async Task HandleRefreshStatus_SendsMessage_WhenStepStatusIsFail()
    {
        var ct = CancellationToken.None;

        var creditApplication = new CreditApplicationModel()
        {
            Id = CreditApplicationId,
            CompanyId = "TestCompanyId"
        };
        var credit = new CreditModel()
        {
            Id = Guid.NewGuid(),
            CreditApplicationId = CreditApplicationId
        };
        _mockOnBoardingService.GetCreditApplicationById(CreditApplicationId, ct).Returns(creditApplication);
        _mockLoanService.GetCreditsByCompanyId(creditApplication.CompanyId, Arg.Any<bool>(), ct).Returns(new List<CreditModel>
        {
            credit
        });

        await _service.HandleRefreshStatus(StepStatus, CreditApplicationId, _stepName, _stepName, ct);

        await _mockAccountStatusChangeService.Received(1).SendMessage(
            Arg.Is<ServiceBusMessageBt<ChangeAccountStatusModel>>(msg =>
                msg.MessageBody.EventType == OnHoldEvents.CompanyAnnualRevenueFails &&
                msg.MessageBody.Details.CompanyId == creditApplication.CompanyId &&
                msg.MessageBody.Details.Id == credit.Id.ToString()
            ),
            ct);
    }

    [Fact]
    public async Task HandleStatus_UpdatesCreditApplication_WhenHardFail()
    {
        var input = Substitute.For<IStepInputBase>();
        input.ExecutionId.Returns(ExecutionId);
        input.CreditApplicationId.Returns(CreditApplicationId);

        var ct = CancellationToken.None;

        await _service.HandleStatus(input, StepStatus, _stepName, ct);

        await _mockOnBoardingService.Received(1).PatchCreditApplication(
            input.CreditApplicationId,
            Arg.Is<UpdateCreditApplicationModel>(model =>
                model.NewStatus == "Processed" &&
                model.AutomatedDecisionResult == StepStatus &&
                model.UpdatedBy == _stepName &&
                model.ExecutionId == input.ExecutionId
            ),
            ct);
    }

    [Fact]
    public async Task HandleException_NotifiesErrorService()
    {
        var stepInput = Substitute.For<IStepInputBase>();
        stepInput.ExecutionId.Returns(ExecutionId);
        stepInput.CreditApplicationId.Returns(CreditApplicationId);

        await _service.HandleException(stepInput, _stepName, ErrorMessage, default);

        await _mockErrorNotificationService.Received(1).Notify(
            Arg.Is<EventMessageBody>(msg =>
                msg.Message == ErrorMessage &&
                msg.EventLevel == EventLevel.Error &&
                msg.EventName == "Internal Error" &&
                msg.EventSource.Contains("ExecutionType")
            ),
            default);
    }

    [Fact]
    public async Task HandleThirdPartyException_ProcessesExceptionAndNotifies()
    {
        var stepInput = Substitute.For<IStepInputBase>();
        stepInput.ExecutionId.Returns(ExecutionId);
        stepInput.CreditApplicationId.Returns(CreditApplicationId);
        var ex = new HttpClientRequestException("TestExceptionMessage", HttpStatusCode.BadRequest, "TestService");

        await _service.HandleThirdPartyException(stepInput, _stepName, ex, default);

        await _mockErrorNotificationService.Received(1).Notify(
            Arg.Is<EventMessageBody>(msg =>
                msg.Message == ex.Message &&
                msg.EventName == $"{ex.ServiceName} external request error"
            ),
            default);
    }

    [Fact]
    public async Task HandleThirdPartyException_WithScheduledSteps_ProcessesExceptionAndNotifies()
    {
        var stepInput = Substitute.For<IStepInputBase>();
        stepInput.ExecutionId.Returns(ExecutionId);
        stepInput.ScheduledUpdate = "ScheduledUpdate";
        stepInput.CreditApplicationId.Returns(CreditApplicationId);
        var ex = new HttpClientRequestException("TestExceptionMessage", HttpStatusCode.BadRequest, "TestService");

        await _service.HandleThirdPartyException(stepInput, _stepName, ex, default);

        await _mockErrorNotificationService.Received(1).Notify(
            Arg.Is<EventMessageBody>(msg =>
                msg.Message == ex.Message &&
                msg.EventName == $"{ex.ServiceName} external request error"
            ),
            default);

        await _mockOnBoardingService.Received(1).GetDecisionEngineStepsByExecutionId(ExecutionId, default);
    }
}