{"Version": 1, "WorkspaceRootPath": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\decision-engine\\src\\", "Documents": [{"AbsoluteMoniker": "D:0:0:{6F6DEF34-8616-4981-9F27-2F0140406372}|BlueTape.Services.DecisionEngine.BusinessLogic\\BlueTape.Services.DecisionEngine.BusinessLogic.csproj|c:\\users\\<USER>\\source\\repos\\bluetape\\decision-engine\\src\\bluetape.services.decisionengine.businesslogic\\steps\\drawapproval\\preliminarystep.cs||{A6C744A8-0E4A-4FC6-886A-************}", "RelativeMoniker": "D:0:0:{6F6DEF34-8616-4981-9F27-2F0140406372}|BlueTape.Services.DecisionEngine.BusinessLogic\\BlueTape.Services.DecisionEngine.BusinessLogic.csproj|solutionrelative:bluetape.services.decisionengine.businesslogic\\steps\\drawapproval\\preliminarystep.cs||{A6C744A8-0E4A-4FC6-886A-************}"}, {"AbsoluteMoniker": "D:0:0:{6F6DEF34-8616-4981-9F27-2F0140406372}|BlueTape.Services.DecisionEngine.BusinessLogic\\BlueTape.Services.DecisionEngine.BusinessLogic.csproj|c:\\users\\<USER>\\source\\repos\\bluetape\\decision-engine\\src\\bluetape.services.decisionengine.businesslogic\\steps\\drawapproval\\approveissuedrawstep.cs||{A6C744A8-0E4A-4FC6-886A-************}", "RelativeMoniker": "D:0:0:{6F6DEF34-8616-4981-9F27-2F0140406372}|BlueTape.Services.DecisionEngine.BusinessLogic\\BlueTape.Services.DecisionEngine.BusinessLogic.csproj|solutionrelative:bluetape.services.decisionengine.businesslogic\\steps\\drawapproval\\approveissuedrawstep.cs||{A6C744A8-0E4A-4FC6-886A-************}"}, {"AbsoluteMoniker": "D:0:0:{6F6DEF34-8616-4981-9F27-2F0140406372}|BlueTape.Services.DecisionEngine.BusinessLogic\\BlueTape.Services.DecisionEngine.BusinessLogic.csproj|c:\\users\\<USER>\\source\\repos\\bluetape\\decision-engine\\src\\bluetape.services.decisionengine.businesslogic\\services\\onboardingservice.cs||{A6C744A8-0E4A-4FC6-886A-************}", "RelativeMoniker": "D:0:0:{6F6DEF34-8616-4981-9F27-2F0140406372}|BlueTape.Services.DecisionEngine.BusinessLogic\\BlueTape.Services.DecisionEngine.BusinessLogic.csproj|solutionrelative:bluetape.services.decisionengine.businesslogic\\services\\onboardingservice.cs||{A6C744A8-0E4A-4FC6-886A-************}"}, {"AbsoluteMoniker": "D:0:0:{682BCDC0-7D44-4535-BBAA-49A59C3CD7A5}|BlueTape.Services.DecisionEngine.DataAccess.External\\BlueTape.Services.DecisionEngine.DataAccess.External.csproj|c:\\users\\<USER>\\source\\repos\\bluetape\\decision-engine\\src\\bluetape.services.decisionengine.dataaccess.external\\externalservices\\onboardingexternalservice.cs||{A6C744A8-0E4A-4FC6-886A-************}", "RelativeMoniker": "D:0:0:{682BCDC0-7D44-4535-BBAA-49A59C3CD7A5}|BlueTape.Services.DecisionEngine.DataAccess.External\\BlueTape.Services.DecisionEngine.DataAccess.External.csproj|solutionrelative:bluetape.services.decisionengine.dataaccess.external\\externalservices\\onboardingexternalservice.cs||{A6C744A8-0E4A-4FC6-886A-************}"}, {"AbsoluteMoniker": "D:0:0:{6F6DEF34-8616-4981-9F27-2F0140406372}|BlueTape.Services.DecisionEngine.BusinessLogic\\BlueTape.Services.DecisionEngine.BusinessLogic.csproj|c:\\users\\<USER>\\source\\repos\\bluetape\\decision-engine\\src\\bluetape.services.decisionengine.businesslogic\\steps\\decisionengine\\cashflowreviewstep.cs||{A6C744A8-0E4A-4FC6-886A-************}", "RelativeMoniker": "D:0:0:{6F6DEF34-8616-4981-9F27-2F0140406372}|BlueTape.Services.DecisionEngine.BusinessLogic\\BlueTape.Services.DecisionEngine.BusinessLogic.csproj|solutionrelative:bluetape.services.decisionengine.businesslogic\\steps\\decisionengine\\cashflowreviewstep.cs||{A6C744A8-0E4A-4FC6-886A-************}"}, {"AbsoluteMoniker": "D:0:0:{6F6DEF34-8616-4981-9F27-2F0140406372}|BlueTape.Services.DecisionEngine.BusinessLogic\\BlueTape.Services.DecisionEngine.BusinessLogic.csproj|c:\\users\\<USER>\\source\\repos\\bluetape\\decision-engine\\src\\bluetape.services.decisionengine.businesslogic\\steps\\decisionengine\\cashflowreportstep.cs||{A6C744A8-0E4A-4FC6-886A-************}", "RelativeMoniker": "D:0:0:{6F6DEF34-8616-4981-9F27-2F0140406372}|BlueTape.Services.DecisionEngine.BusinessLogic\\BlueTape.Services.DecisionEngine.BusinessLogic.csproj|solutionrelative:bluetape.services.decisionengine.businesslogic\\steps\\decisionengine\\cashflowreportstep.cs||{A6C744A8-0E4A-4FC6-886A-************}"}, {"AbsoluteMoniker": "D:0:0:{6F6DEF34-8616-4981-9F27-2F0140406372}|BlueTape.Services.DecisionEngine.BusinessLogic\\BlueTape.Services.DecisionEngine.BusinessLogic.csproj|c:\\users\\<USER>\\source\\repos\\bluetape\\decision-engine\\src\\bluetape.services.decisionengine.businesslogic\\services\\cashflowservice.cs||{A6C744A8-0E4A-4FC6-886A-************}", "RelativeMoniker": "D:0:0:{6F6DEF34-8616-4981-9F27-2F0140406372}|BlueTape.Services.DecisionEngine.BusinessLogic\\BlueTape.Services.DecisionEngine.BusinessLogic.csproj|solutionrelative:bluetape.services.decisionengine.businesslogic\\services\\cashflowservice.cs||{A6C744A8-0E4A-4FC6-886A-************}"}, {"AbsoluteMoniker": "D:0:0:{682BCDC0-7D44-4535-BBAA-49A59C3CD7A5}|BlueTape.Services.DecisionEngine.DataAccess.External\\BlueTape.Services.DecisionEngine.DataAccess.External.csproj|c:\\users\\<USER>\\source\\repos\\bluetape\\decision-engine\\src\\bluetape.services.decisionengine.dataaccess.external\\externalservices\\plaidintegrationexternalservice.cs||{A6C744A8-0E4A-4FC6-886A-************}", "RelativeMoniker": "D:0:0:{682BCDC0-7D44-4535-BBAA-49A59C3CD7A5}|BlueTape.Services.DecisionEngine.DataAccess.External\\BlueTape.Services.DecisionEngine.DataAccess.External.csproj|solutionrelative:bluetape.services.decisionengine.dataaccess.external\\externalservices\\plaidintegrationexternalservice.cs||{A6C744A8-0E4A-4FC6-886A-************}"}, {"AbsoluteMoniker": "D:0:0:{596DBD41-4E1E-40B1-AD5F-79BFDE40B614}|BlueTape.Services.DecisionEngine.BusinessLogic.Tests\\BlueTape.Services.DecisionEngine.BusinessLogic.Tests.csproj|c:\\users\\<USER>\\source\\repos\\bluetape\\decision-engine\\src\\bluetape.services.decisionengine.businesslogic.tests\\steps\\drawapproval\\approveissuedrawsteptests.cs||{A6C744A8-0E4A-4FC6-886A-************}", "RelativeMoniker": "D:0:0:{596DBD41-4E1E-40B1-AD5F-79BFDE40B614}|BlueTape.Services.DecisionEngine.BusinessLogic.Tests\\BlueTape.Services.DecisionEngine.BusinessLogic.Tests.csproj|solutionrelative:bluetape.services.decisionengine.businesslogic.tests\\steps\\drawapproval\\approveissuedrawsteptests.cs||{A6C744A8-0E4A-4FC6-886A-************}"}, {"AbsoluteMoniker": "D:0:0:{27554536-94AA-4184-A2DB-2CB09B00DE3A}|lambdas\\DrawApproval\\BlueTape.Services.DrawApproval.ApproveIssueDrawStep\\BlueTape.Services.DrawApproval.ApproveIssueDrawStep.csproj|c:\\users\\<USER>\\source\\repos\\bluetape\\decision-engine\\src\\lambdas\\drawapproval\\bluetape.services.drawapproval.approveissuedrawstep\\function.cs||{A6C744A8-0E4A-4FC6-886A-************}", "RelativeMoniker": "D:0:0:{27554536-94AA-4184-A2DB-2CB09B00DE3A}|lambdas\\DrawApproval\\BlueTape.Services.DrawApproval.ApproveIssueDrawStep\\BlueTape.Services.DrawApproval.ApproveIssueDrawStep.csproj|solutionrelative:lambdas\\drawapproval\\bluetape.services.drawapproval.approveissuedrawstep\\function.cs||{A6C744A8-0E4A-4FC6-886A-************}"}, {"AbsoluteMoniker": "D:0:0:{6F6DEF34-8616-4981-9F27-2F0140406372}|BlueTape.Services.DecisionEngine.BusinessLogic\\BlueTape.Services.DecisionEngine.BusinessLogic.csproj|c:\\users\\<USER>\\source\\repos\\bluetape\\decision-engine\\src\\bluetape.services.decisionengine.businesslogic\\di\\dependencyregistrar.cs||{A6C744A8-0E4A-4FC6-886A-************}", "RelativeMoniker": "D:0:0:{6F6DEF34-8616-4981-9F27-2F0140406372}|BlueTape.Services.DecisionEngine.BusinessLogic\\BlueTape.Services.DecisionEngine.BusinessLogic.csproj|solutionrelative:bluetape.services.decisionengine.businesslogic\\di\\dependencyregistrar.cs||{A6C744A8-0E4A-4FC6-886A-************}"}, {"AbsoluteMoniker": "D:0:0:{6F6DEF34-8616-4981-9F27-2F0140406372}|BlueTape.Services.DecisionEngine.BusinessLogic\\BlueTape.Services.DecisionEngine.BusinessLogic.csproj|c:\\users\\<USER>\\source\\repos\\bluetape\\decision-engine\\src\\bluetape.services.decisionengine.businesslogic\\abstractions\\steps\\drawapproval\\iapproveissuedrawstep.cs||{A6C744A8-0E4A-4FC6-886A-************}", "RelativeMoniker": "D:0:0:{6F6DEF34-8616-4981-9F27-2F0140406372}|BlueTape.Services.DecisionEngine.BusinessLogic\\BlueTape.Services.DecisionEngine.BusinessLogic.csproj|solutionrelative:bluetape.services.decisionengine.businesslogic\\abstractions\\steps\\drawapproval\\iapproveissuedrawstep.cs||{A6C744A8-0E4A-4FC6-886A-************}"}, {"AbsoluteMoniker": "D:0:0:{6F6DEF34-8616-4981-9F27-2F0140406372}|BlueTape.Services.DecisionEngine.BusinessLogic\\BlueTape.Services.DecisionEngine.BusinessLogic.csproj|c:\\users\\<USER>\\source\\repos\\bluetape\\decision-engine\\src\\bluetape.services.decisionengine.businesslogic\\steps\\drawapproval\\initializationstep.cs||{A6C744A8-0E4A-4FC6-886A-************}", "RelativeMoniker": "D:0:0:{6F6DEF34-8616-4981-9F27-2F0140406372}|BlueTape.Services.DecisionEngine.BusinessLogic\\BlueTape.Services.DecisionEngine.BusinessLogic.csproj|solutionrelative:bluetape.services.decisionengine.businesslogic\\steps\\drawapproval\\initializationstep.cs||{A6C744A8-0E4A-4FC6-886A-************}"}, {"AbsoluteMoniker": "D:0:0:{6F6DEF34-8616-4981-9F27-2F0140406372}|BlueTape.Services.DecisionEngine.BusinessLogic\\BlueTape.Services.DecisionEngine.BusinessLogic.csproj|c:\\users\\<USER>\\source\\repos\\bluetape\\decision-engine\\src\\bluetape.services.decisionengine.businesslogic\\abstractions\\services\\ionboardingservice.cs||{A6C744A8-0E4A-4FC6-886A-************}", "RelativeMoniker": "D:0:0:{6F6DEF34-8616-4981-9F27-2F0140406372}|BlueTape.Services.DecisionEngine.BusinessLogic\\BlueTape.Services.DecisionEngine.BusinessLogic.csproj|solutionrelative:bluetape.services.decisionengine.businesslogic\\abstractions\\services\\ionboardingservice.cs||{A6C744A8-0E4A-4FC6-886A-************}"}, {"AbsoluteMoniker": "D:0:0:{6F6DEF34-8616-4981-9F27-2F0140406372}|BlueTape.Services.DecisionEngine.BusinessLogic\\BlueTape.Services.DecisionEngine.BusinessLogic.csproj|c:\\users\\<USER>\\source\\repos\\bluetape\\decision-engine\\src\\bluetape.services.decisionengine.businesslogic\\abstractions\\services\\iautomatedapprovalservice.cs||{A6C744A8-0E4A-4FC6-886A-************}", "RelativeMoniker": "D:0:0:{6F6DEF34-8616-4981-9F27-2F0140406372}|BlueTape.Services.DecisionEngine.BusinessLogic\\BlueTape.Services.DecisionEngine.BusinessLogic.csproj|solutionrelative:bluetape.services.decisionengine.businesslogic\\abstractions\\services\\iautomatedapprovalservice.cs||{A6C744A8-0E4A-4FC6-886A-************}"}, {"AbsoluteMoniker": "D:0:0:{07ADD9E0-88A8-4F8F-83A1-06ECFDEFD017}|lambdas\\DrawApproval\\BlueTape.Services.DrawApproval.InitializationStep\\BlueTape.Services.DrawApproval.InitializationStep.csproj|c:\\users\\<USER>\\source\\repos\\bluetape\\decision-engine\\src\\lambdas\\drawapproval\\bluetape.services.drawapproval.initializationstep\\function.cs||{A6C744A8-0E4A-4FC6-886A-************}", "RelativeMoniker": "D:0:0:{07ADD9E0-88A8-4F8F-83A1-06ECFDEFD017}|lambdas\\DrawApproval\\BlueTape.Services.DrawApproval.InitializationStep\\BlueTape.Services.DrawApproval.InitializationStep.csproj|solutionrelative:lambdas\\drawapproval\\bluetape.services.drawapproval.initializationstep\\function.cs||{A6C744A8-0E4A-4FC6-886A-************}"}, {"AbsoluteMoniker": "D:0:0:{6F6DEF34-8616-4981-9F27-2F0140406372}|BlueTape.Services.DecisionEngine.BusinessLogic\\BlueTape.Services.DecisionEngine.BusinessLogic.csproj|c:\\users\\<USER>\\source\\repos\\bluetape\\decision-engine\\src\\bluetape.services.decisionengine.businesslogic\\mapping\\onboardingmodelsprofile.cs||{A6C744A8-0E4A-4FC6-886A-************}", "RelativeMoniker": "D:0:0:{6F6DEF34-8616-4981-9F27-2F0140406372}|BlueTape.Services.DecisionEngine.BusinessLogic\\BlueTape.Services.DecisionEngine.BusinessLogic.csproj|solutionrelative:bluetape.services.decisionengine.businesslogic\\mapping\\onboardingmodelsprofile.cs||{A6C744A8-0E4A-4FC6-886A-************}"}, {"AbsoluteMoniker": "D:0:0:{02691FF6-A7BA-4181-85F5-7A3AEB2738B8}|BlueTape.Services.DecisionEngine\\BlueTape.Services.DecisionEngine.csproj|c:\\users\\<USER>\\source\\repos\\bluetape\\decision-engine\\src\\bluetape.services.decisionengine\\controllers\\testingcontroller.cs||{A6C744A8-0E4A-4FC6-886A-************}", "RelativeMoniker": "D:0:0:{02691FF6-A7BA-4181-85F5-7A3AEB2738B8}|BlueTape.Services.DecisionEngine\\BlueTape.Services.DecisionEngine.csproj|solutionrelative:bluetape.services.decisionengine\\controllers\\testingcontroller.cs||{A6C744A8-0E4A-4FC6-886A-************}"}, {"AbsoluteMoniker": "D:0:0:{A2FE74E1-B743-11D0-AE1A-00A0C90FFFC3}|<MiscFiles>|C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\MSBuild\\Current\\Bin\\Roslyn\\Microsoft.Managed.Core.targets||{FA3CD31E-987B-443A-9B81-186104E8DAC1}|"}, {"AbsoluteMoniker": "D:0:0:{A2FE74E1-B743-11D0-AE1A-00A0C90FFFC3}|<MiscFiles>|C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\MSBuild\\Current\\Bin\\amd64\\Microsoft.Common.CurrentVersion.targets||{FA3CD31E-987B-443A-9B81-186104E8DAC1}|"}, {"AbsoluteMoniker": "D:0:0:{6F6DEF34-8616-4981-9F27-2F0140406372}|BlueTape.Services.DecisionEngine.BusinessLogic\\BlueTape.Services.DecisionEngine.BusinessLogic.csproj|c:\\users\\<USER>\\source\\repos\\bluetape\\decision-engine\\src\\bluetape.services.decisionengine.businesslogic\\models\\stepinputs\\drawapproval\\nosupplierbankaccountnumber.cs||{A6C744A8-0E4A-4FC6-886A-************}", "RelativeMoniker": "D:0:0:{6F6DEF34-8616-4981-9F27-2F0140406372}|BlueTape.Services.DecisionEngine.BusinessLogic\\BlueTape.Services.DecisionEngine.BusinessLogic.csproj|solutionrelative:bluetape.services.decisionengine.businesslogic\\models\\stepinputs\\drawapproval\\nosupplierbankaccountnumber.cs||{A6C744A8-0E4A-4FC6-886A-************}"}, {"AbsoluteMoniker": "D:0:0:{2DE6360C-B153-4AC9-9A07-73FFCCD4BB5E}|BlueTape.Services.DecisionEngine.Domain\\BlueTape.Services.DecisionEngine.Domain.csproj|c:\\users\\<USER>\\source\\repos\\bluetape\\decision-engine\\src\\bluetape.services.decisionengine.domain\\models\\drawapprovals\\createdrawapproval.cs||{8B382828-6202-11D1-8870-0000F87579D2}", "RelativeMoniker": "D:0:0:{2DE6360C-B153-4AC9-9A07-73FFCCD4BB5E}|BlueTape.Services.DecisionEngine.Domain\\BlueTape.Services.DecisionEngine.Domain.csproj|solutionrelative:bluetape.services.decisionengine.domain\\models\\drawapprovals\\createdrawapproval.cs||{8B382828-6202-11D1-8870-0000F87579D2}"}, {"AbsoluteMoniker": "D:0:0:{6F6DEF34-8616-4981-9F27-2F0140406372}|BlueTape.Services.DecisionEngine.BusinessLogic\\BlueTape.Services.DecisionEngine.BusinessLogic.csproj|c:\\users\\<USER>\\source\\repos\\bluetape\\decision-engine\\src\\bluetape.services.decisionengine.businesslogic\\abstractions\\steps\\drawapproval\\iinitializationstep.cs||{A6C744A8-0E4A-4FC6-886A-************}", "RelativeMoniker": "D:0:0:{6F6DEF34-8616-4981-9F27-2F0140406372}|BlueTape.Services.DecisionEngine.BusinessLogic\\BlueTape.Services.DecisionEngine.BusinessLogic.csproj|solutionrelative:bluetape.services.decisionengine.businesslogic\\abstractions\\steps\\drawapproval\\iinitializationstep.cs||{A6C744A8-0E4A-4FC6-886A-************}"}, {"AbsoluteMoniker": "D:0:0:{6F6DEF34-8616-4981-9F27-2F0140406372}|BlueTape.Services.DecisionEngine.BusinessLogic\\BlueTape.Services.DecisionEngine.BusinessLogic.csproj|c:\\users\\<USER>\\source\\repos\\bluetape\\decision-engine\\src\\bluetape.services.decisionengine.businesslogic\\models\\stepinputs\\drawapproval\\drawapprovalstepinputbase.cs||{A6C744A8-0E4A-4FC6-886A-************}", "RelativeMoniker": "D:0:0:{6F6DEF34-8616-4981-9F27-2F0140406372}|BlueTape.Services.DecisionEngine.BusinessLogic\\BlueTape.Services.DecisionEngine.BusinessLogic.csproj|solutionrelative:bluetape.services.decisionengine.businesslogic\\models\\stepinputs\\drawapproval\\drawapprovalstepinputbase.cs||{A6C744A8-0E4A-4FC6-886A-************}"}, {"AbsoluteMoniker": "D:0:0:{6F6DEF34-8616-4981-9F27-2F0140406372}|BlueTape.Services.DecisionEngine.BusinessLogic\\BlueTape.Services.DecisionEngine.BusinessLogic.csproj|c:\\users\\<USER>\\source\\repos\\bluetape\\decision-engine\\src\\bluetape.services.decisionengine.businesslogic\\models\\stepinputs\\drawapproval\\inputpayableitem.cs||{A6C744A8-0E4A-4FC6-886A-************}", "RelativeMoniker": "D:0:0:{6F6DEF34-8616-4981-9F27-2F0140406372}|BlueTape.Services.DecisionEngine.BusinessLogic\\BlueTape.Services.DecisionEngine.BusinessLogic.csproj|solutionrelative:bluetape.services.decisionengine.businesslogic\\models\\stepinputs\\drawapproval\\inputpayableitem.cs||{A6C744A8-0E4A-4FC6-886A-************}"}, {"AbsoluteMoniker": "D:0:0:{6F6DEF34-8616-4981-9F27-2F0140406372}|BlueTape.Services.DecisionEngine.BusinessLogic\\BlueTape.Services.DecisionEngine.BusinessLogic.csproj|c:\\users\\<USER>\\source\\repos\\bluetape\\decision-engine\\src\\bluetape.services.decisionengine.businesslogic\\models\\stepinputs\\drawapproval\\nosupplierdetails.cs||{A6C744A8-0E4A-4FC6-886A-************}", "RelativeMoniker": "D:0:0:{6F6DEF34-8616-4981-9F27-2F0140406372}|BlueTape.Services.DecisionEngine.BusinessLogic\\BlueTape.Services.DecisionEngine.BusinessLogic.csproj|solutionrelative:bluetape.services.decisionengine.businesslogic\\models\\stepinputs\\drawapproval\\nosupplierdetails.cs||{A6C744A8-0E4A-4FC6-886A-************}"}, {"AbsoluteMoniker": "D:0:0:{6F6DEF34-8616-4981-9F27-2F0140406372}|BlueTape.Services.DecisionEngine.BusinessLogic\\BlueTape.Services.DecisionEngine.BusinessLogic.csproj|c:\\users\\<USER>\\source\\repos\\bluetape\\decision-engine\\src\\bluetape.services.decisionengine.businesslogic\\models\\stepinputs\\drawapproval\\nosupplierbankdetails.cs||{A6C744A8-0E4A-4FC6-886A-************}", "RelativeMoniker": "D:0:0:{6F6DEF34-8616-4981-9F27-2F0140406372}|BlueTape.Services.DecisionEngine.BusinessLogic\\BlueTape.Services.DecisionEngine.BusinessLogic.csproj|solutionrelative:bluetape.services.decisionengine.businesslogic\\models\\stepinputs\\drawapproval\\nosupplierbankdetails.cs||{A6C744A8-0E4A-4FC6-886A-************}"}, {"AbsoluteMoniker": "D:0:0:{6F6DEF34-8616-4981-9F27-2F0140406372}|BlueTape.Services.DecisionEngine.BusinessLogic\\BlueTape.Services.DecisionEngine.BusinessLogic.csproj|c:\\users\\<USER>\\source\\repos\\bluetape\\decision-engine\\src\\bluetape.services.decisionengine.businesslogic\\models\\stepinputs\\drawapproval\\nosupplieraddress.cs||{A6C744A8-0E4A-4FC6-886A-************}", "RelativeMoniker": "D:0:0:{6F6DEF34-8616-4981-9F27-2F0140406372}|BlueTape.Services.DecisionEngine.BusinessLogic\\BlueTape.Services.DecisionEngine.BusinessLogic.csproj|solutionrelative:bluetape.services.decisionengine.businesslogic\\models\\stepinputs\\drawapproval\\nosupplieraddress.cs||{A6C744A8-0E4A-4FC6-886A-************}"}, {"AbsoluteMoniker": "D:0:0:{21BDAC41-9D08-4CB3-ADDF-7E1795FDF1AC}|lambdas\\DrawApproval\\BlueTape.Services.DrawApproval.PreliminaryStep\\BlueTape.Services.DrawApproval.PreliminaryStep.csproj|c:\\users\\<USER>\\source\\repos\\bluetape\\decision-engine\\src\\lambdas\\drawapproval\\bluetape.services.drawapproval.preliminarystep\\function.cs||{A6C744A8-0E4A-4FC6-886A-************}", "RelativeMoniker": "D:0:0:{21BDAC41-9D08-4CB3-ADDF-7E1795FDF1AC}|lambdas\\DrawApproval\\BlueTape.Services.DrawApproval.PreliminaryStep\\BlueTape.Services.DrawApproval.PreliminaryStep.csproj|solutionrelative:lambdas\\drawapproval\\bluetape.services.drawapproval.preliminarystep\\function.cs||{A6C744A8-0E4A-4FC6-886A-************}"}, {"AbsoluteMoniker": "D:0:0:{596DBD41-4E1E-40B1-AD5F-79BFDE40B614}|BlueTape.Services.DecisionEngine.BusinessLogic.Tests\\BlueTape.Services.DecisionEngine.BusinessLogic.Tests.csproj|c:\\users\\<USER>\\source\\repos\\bluetape\\decision-engine\\src\\bluetape.services.decisionengine.businesslogic.tests\\bluetape.services.decisionengine.businesslogic.tests.csproj||{FA3CD31E-987B-443A-9B81-186104E8DAC1}|", "RelativeMoniker": "D:0:0:{596DBD41-4E1E-40B1-AD5F-79BFDE40B614}|BlueTape.Services.DecisionEngine.BusinessLogic.Tests\\BlueTape.Services.DecisionEngine.BusinessLogic.Tests.csproj|solutionrelative:bluetape.services.decisionengine.businesslogic.tests\\bluetape.services.decisionengine.businesslogic.tests.csproj||{FA3CD31E-987B-443A-9B81-186104E8DAC1}|"}, {"AbsoluteMoniker": "D:0:0:{6F6DEF34-8616-4981-9F27-2F0140406372}|BlueTape.Services.DecisionEngine.BusinessLogic\\BlueTape.Services.DecisionEngine.BusinessLogic.csproj|c:\\users\\<USER>\\source\\repos\\bluetape\\decision-engine\\src\\bluetape.services.decisionengine.businesslogic\\abstractions\\services\\iloanservice.cs||{A6C744A8-0E4A-4FC6-886A-************}", "RelativeMoniker": "D:0:0:{6F6DEF34-8616-4981-9F27-2F0140406372}|BlueTape.Services.DecisionEngine.BusinessLogic\\BlueTape.Services.DecisionEngine.BusinessLogic.csproj|solutionrelative:bluetape.services.decisionengine.businesslogic\\abstractions\\services\\iloanservice.cs||{A6C744A8-0E4A-4FC6-886A-************}"}, {"AbsoluteMoniker": "D:0:0:{F01541C9-7C7D-4A39-976B-C1AAEDE07F3C}|lambdas\\DecisionEngine\\BlueTape.Services.CashFlowReviewStep\\BlueTape.Services.CashFlowReviewStep.csproj|c:\\users\\<USER>\\source\\repos\\bluetape\\decision-engine\\src\\lambdas\\decisionengine\\bluetape.services.cashflowreviewstep\\function.cs||{A6C744A8-0E4A-4FC6-886A-************}", "RelativeMoniker": "D:0:0:{F01541C9-7C7D-4A39-976B-C1AAEDE07F3C}|lambdas\\DecisionEngine\\BlueTape.Services.CashFlowReviewStep\\BlueTape.Services.CashFlowReviewStep.csproj|solutionrelative:lambdas\\decisionengine\\bluetape.services.cashflowreviewstep\\function.cs||{A6C744A8-0E4A-4FC6-886A-************}"}, {"AbsoluteMoniker": "D:0:0:{6F6DEF34-8616-4981-9F27-2F0140406372}|BlueTape.Services.DecisionEngine.BusinessLogic\\BlueTape.Services.DecisionEngine.BusinessLogic.csproj|c:\\users\\<USER>\\source\\repos\\bluetape\\decision-engine\\src\\bluetape.services.decisionengine.businesslogic\\abstractions\\steps\\decisionengine\\icashflowreviewstep.cs||{A6C744A8-0E4A-4FC6-886A-************}", "RelativeMoniker": "D:0:0:{6F6DEF34-8616-4981-9F27-2F0140406372}|BlueTape.Services.DecisionEngine.BusinessLogic\\BlueTape.Services.DecisionEngine.BusinessLogic.csproj|solutionrelative:bluetape.services.decisionengine.businesslogic\\abstractions\\steps\\decisionengine\\icashflowreviewstep.cs||{A6C744A8-0E4A-4FC6-886A-************}"}, {"AbsoluteMoniker": "D:0:0:{6F6DEF34-8616-4981-9F27-2F0140406372}|BlueTape.Services.DecisionEngine.BusinessLogic\\BlueTape.Services.DecisionEngine.BusinessLogic.csproj|c:\\users\\<USER>\\source\\repos\\bluetape\\decision-engine\\src\\bluetape.services.decisionengine.businesslogic\\constants\\accountstatusconstants.cs||{A6C744A8-0E4A-4FC6-886A-************}", "RelativeMoniker": "D:0:0:{6F6DEF34-8616-4981-9F27-2F0140406372}|BlueTape.Services.DecisionEngine.BusinessLogic\\BlueTape.Services.DecisionEngine.BusinessLogic.csproj|solutionrelative:bluetape.services.decisionengine.businesslogic\\constants\\accountstatusconstants.cs||{A6C744A8-0E4A-4FC6-886A-************}"}, {"AbsoluteMoniker": "D:0:0:{6F6DEF34-8616-4981-9F27-2F0140406372}|BlueTape.Services.DecisionEngine.BusinessLogic\\BlueTape.Services.DecisionEngine.BusinessLogic.csproj|c:\\users\\<USER>\\source\\repos\\bluetape\\decision-engine\\src\\bluetape.services.decisionengine.businesslogic\\models\\bvi\\bviresult.cs||{A6C744A8-0E4A-4FC6-886A-************}", "RelativeMoniker": "D:0:0:{6F6DEF34-8616-4981-9F27-2F0140406372}|BlueTape.Services.DecisionEngine.BusinessLogic\\BlueTape.Services.DecisionEngine.BusinessLogic.csproj|solutionrelative:bluetape.services.decisionengine.businesslogic\\models\\bvi\\bviresult.cs||{A6C744A8-0E4A-4FC6-886A-************}"}, {"AbsoluteMoniker": "D:0:0:{6F6DEF34-8616-4981-9F27-2F0140406372}|BlueTape.Services.DecisionEngine.BusinessLogic\\BlueTape.Services.DecisionEngine.BusinessLogic.csproj|c:\\users\\<USER>\\source\\repos\\bluetape\\decision-engine\\src\\bluetape.services.decisionengine.businesslogic\\models\\company\\companymodel.cs||{A6C744A8-0E4A-4FC6-886A-************}", "RelativeMoniker": "D:0:0:{6F6DEF34-8616-4981-9F27-2F0140406372}|BlueTape.Services.DecisionEngine.BusinessLogic\\BlueTape.Services.DecisionEngine.BusinessLogic.csproj|solutionrelative:bluetape.services.decisionengine.businesslogic\\models\\company\\companymodel.cs||{A6C744A8-0E4A-4FC6-886A-************}"}, {"AbsoluteMoniker": "D:0:0:{596DBD41-4E1E-40B1-AD5F-79BFDE40B614}|BlueTape.Services.DecisionEngine.BusinessLogic.Tests\\BlueTape.Services.DecisionEngine.BusinessLogic.Tests.csproj|c:\\users\\<USER>\\source\\repos\\bluetape\\decision-engine\\src\\bluetape.services.decisionengine.businesslogic.tests\\services\\emailnotificationservicetests.cs||{A6C744A8-0E4A-4FC6-886A-************}", "RelativeMoniker": "D:0:0:{596DBD41-4E1E-40B1-AD5F-79BFDE40B614}|BlueTape.Services.DecisionEngine.BusinessLogic.Tests\\BlueTape.Services.DecisionEngine.BusinessLogic.Tests.csproj|solutionrelative:bluetape.services.decisionengine.businesslogic.tests\\services\\emailnotificationservicetests.cs||{A6C744A8-0E4A-4FC6-886A-************}"}, {"AbsoluteMoniker": "D:0:0:{6F6DEF34-8616-4981-9F27-2F0140406372}|BlueTape.Services.DecisionEngine.BusinessLogic\\BlueTape.Services.DecisionEngine.BusinessLogic.csproj|c:\\users\\<USER>\\source\\repos\\bluetape\\decision-engine\\src\\bluetape.services.decisionengine.businesslogic\\steps\\drawapproval\\internalreviewstep.cs||{A6C744A8-0E4A-4FC6-886A-************}", "RelativeMoniker": "D:0:0:{6F6DEF34-8616-4981-9F27-2F0140406372}|BlueTape.Services.DecisionEngine.BusinessLogic\\BlueTape.Services.DecisionEngine.BusinessLogic.csproj|solutionrelative:bluetape.services.decisionengine.businesslogic\\steps\\drawapproval\\internalreviewstep.cs||{A6C744A8-0E4A-4FC6-886A-************}"}, {"AbsoluteMoniker": "D:0:0:{6F6DEF34-8616-4981-9F27-2F0140406372}|BlueTape.Services.DecisionEngine.BusinessLogic\\BlueTape.Services.DecisionEngine.BusinessLogic.csproj|c:\\users\\<USER>\\source\\repos\\bluetape\\decision-engine\\src\\bluetape.services.decisionengine.businesslogic\\steps\\drawapproval\\drawapprovalgenericstep.cs||{A6C744A8-0E4A-4FC6-886A-************}", "RelativeMoniker": "D:0:0:{6F6DEF34-8616-4981-9F27-2F0140406372}|BlueTape.Services.DecisionEngine.BusinessLogic\\BlueTape.Services.DecisionEngine.BusinessLogic.csproj|solutionrelative:bluetape.services.decisionengine.businesslogic\\steps\\drawapproval\\drawapprovalgenericstep.cs||{A6C744A8-0E4A-4FC6-886A-************}"}, {"AbsoluteMoniker": "D:0:0:{6F6DEF34-8616-4981-9F27-2F0140406372}|BlueTape.Services.DecisionEngine.BusinessLogic\\BlueTape.Services.DecisionEngine.BusinessLogic.csproj|c:\\users\\<USER>\\source\\repos\\bluetape\\decision-engine\\src\\bluetape.services.decisionengine.businesslogic\\services\\stephandlingservice.cs||{A6C744A8-0E4A-4FC6-886A-************}", "RelativeMoniker": "D:0:0:{6F6DEF34-8616-4981-9F27-2F0140406372}|BlueTape.Services.DecisionEngine.BusinessLogic\\BlueTape.Services.DecisionEngine.BusinessLogic.csproj|solutionrelative:bluetape.services.decisionengine.businesslogic\\services\\stephandlingservice.cs||{A6C744A8-0E4A-4FC6-886A-************}"}, {"AbsoluteMoniker": "D:0:0:{596DBD41-4E1E-40B1-AD5F-79BFDE40B614}|BlueTape.Services.DecisionEngine.BusinessLogic.Tests\\BlueTape.Services.DecisionEngine.BusinessLogic.Tests.csproj|c:\\users\\<USER>\\source\\repos\\bluetape\\decision-engine\\src\\bluetape.services.decisionengine.businesslogic.tests\\steps\\drawapproval\\preliminarysteptests.cs||{A6C744A8-0E4A-4FC6-886A-************}", "RelativeMoniker": "D:0:0:{596DBD41-4E1E-40B1-AD5F-79BFDE40B614}|BlueTape.Services.DecisionEngine.BusinessLogic.Tests\\BlueTape.Services.DecisionEngine.BusinessLogic.Tests.csproj|solutionrelative:bluetape.services.decisionengine.businesslogic.tests\\steps\\drawapproval\\preliminarysteptests.cs||{A6C744A8-0E4A-4FC6-886A-************}"}, {"AbsoluteMoniker": "D:0:0:{682BCDC0-7D44-4535-BBAA-49A59C3CD7A5}|BlueTape.Services.DecisionEngine.DataAccess.External\\BlueTape.Services.DecisionEngine.DataAccess.External.csproj|c:\\users\\<USER>\\source\\repos\\bluetape\\decision-engine\\src\\bluetape.services.decisionengine.dataaccess.external\\httpclients\\lexisnexisintegrationservicehttpclient.cs||{A6C744A8-0E4A-4FC6-886A-************}", "RelativeMoniker": "D:0:0:{682BCDC0-7D44-4535-BBAA-49A59C3CD7A5}|BlueTape.Services.DecisionEngine.DataAccess.External\\BlueTape.Services.DecisionEngine.DataAccess.External.csproj|solutionrelative:bluetape.services.decisionengine.dataaccess.external\\httpclients\\lexisnexisintegrationservicehttpclient.cs||{A6C744A8-0E4A-4FC6-886A-************}"}, {"AbsoluteMoniker": "D:0:0:{682BCDC0-7D44-4535-BBAA-49A59C3CD7A5}|BlueTape.Services.DecisionEngine.DataAccess.External\\BlueTape.Services.DecisionEngine.DataAccess.External.csproj|c:\\users\\<USER>\\source\\repos\\bluetape\\decision-engine\\src\\bluetape.services.decisionengine.dataaccess.external\\httpclients\\plaidintegrationservicehttpclient.cs||{A6C744A8-0E4A-4FC6-886A-************}", "RelativeMoniker": "D:0:0:{682BCDC0-7D44-4535-BBAA-49A59C3CD7A5}|BlueTape.Services.DecisionEngine.DataAccess.External\\BlueTape.Services.DecisionEngine.DataAccess.External.csproj|solutionrelative:bluetape.services.decisionengine.dataaccess.external\\httpclients\\plaidintegrationservicehttpclient.cs||{A6C744A8-0E4A-4FC6-886A-************}"}, {"AbsoluteMoniker": "D:0:0:{6F6DEF34-8616-4981-9F27-2F0140406372}|BlueTape.Services.DecisionEngine.BusinessLogic\\BlueTape.Services.DecisionEngine.BusinessLogic.csproj|c:\\users\\<USER>\\source\\repos\\bluetape\\decision-engine\\src\\bluetape.services.decisionengine.businesslogic\\abstractions\\steps\\base\\idrawapprovalstepimplementationbase.cs||{A6C744A8-0E4A-4FC6-886A-************}", "RelativeMoniker": "D:0:0:{6F6DEF34-8616-4981-9F27-2F0140406372}|BlueTape.Services.DecisionEngine.BusinessLogic\\BlueTape.Services.DecisionEngine.BusinessLogic.csproj|solutionrelative:bluetape.services.decisionengine.businesslogic\\abstractions\\steps\\base\\idrawapprovalstepimplementationbase.cs||{A6C744A8-0E4A-4FC6-886A-************}"}, {"AbsoluteMoniker": "D:0:0:{6F6DEF34-8616-4981-9F27-2F0140406372}|BlueTape.Services.DecisionEngine.BusinessLogic\\BlueTape.Services.DecisionEngine.BusinessLogic.csproj|c:\\users\\<USER>\\source\\repos\\bluetape\\decision-engine\\src\\bluetape.services.decisionengine.businesslogic\\services\\accountstatuschangeservice.cs||{A6C744A8-0E4A-4FC6-886A-************}", "RelativeMoniker": "D:0:0:{6F6DEF34-8616-4981-9F27-2F0140406372}|BlueTape.Services.DecisionEngine.BusinessLogic\\BlueTape.Services.DecisionEngine.BusinessLogic.csproj|solutionrelative:bluetape.services.decisionengine.businesslogic\\services\\accountstatuschangeservice.cs||{A6C744A8-0E4A-4FC6-886A-************}"}, {"AbsoluteMoniker": "D:0:0:{6F6DEF34-8616-4981-9F27-2F0140406372}|BlueTape.Services.DecisionEngine.BusinessLogic\\BlueTape.Services.DecisionEngine.BusinessLogic.csproj|c:\\users\\<USER>\\source\\repos\\bluetape\\decision-engine\\src\\bluetape.services.decisionengine.businesslogic\\services\\automatedapprovalservice.cs||{A6C744A8-0E4A-4FC6-886A-************}", "RelativeMoniker": "D:0:0:{6F6DEF34-8616-4981-9F27-2F0140406372}|BlueTape.Services.DecisionEngine.BusinessLogic\\BlueTape.Services.DecisionEngine.BusinessLogic.csproj|solutionrelative:bluetape.services.decisionengine.businesslogic\\services\\automatedapprovalservice.cs||{A6C744A8-0E4A-4FC6-886A-************}"}, {"AbsoluteMoniker": "D:0:0:{6F6DEF34-8616-4981-9F27-2F0140406372}|BlueTape.Services.DecisionEngine.BusinessLogic\\BlueTape.Services.DecisionEngine.BusinessLogic.csproj|c:\\users\\<USER>\\source\\repos\\bluetape\\decision-engine\\src\\bluetape.services.decisionengine.businesslogic\\services\\invoiceservice.cs||{A6C744A8-0E4A-4FC6-886A-************}", "RelativeMoniker": "D:0:0:{6F6DEF34-8616-4981-9F27-2F0140406372}|BlueTape.Services.DecisionEngine.BusinessLogic\\BlueTape.Services.DecisionEngine.BusinessLogic.csproj|solutionrelative:bluetape.services.decisionengine.businesslogic\\services\\invoiceservice.cs||{A6C744A8-0E4A-4FC6-886A-************}"}, {"AbsoluteMoniker": "D:0:0:{6F6DEF34-8616-4981-9F27-2F0140406372}|BlueTape.Services.DecisionEngine.BusinessLogic\\BlueTape.Services.DecisionEngine.BusinessLogic.csproj|c:\\users\\<USER>\\source\\repos\\bluetape\\decision-engine\\src\\bluetape.services.decisionengine.businesslogic\\services\\experianservice.cs||{A6C744A8-0E4A-4FC6-886A-************}", "RelativeMoniker": "D:0:0:{6F6DEF34-8616-4981-9F27-2F0140406372}|BlueTape.Services.DecisionEngine.BusinessLogic\\BlueTape.Services.DecisionEngine.BusinessLogic.csproj|solutionrelative:bluetape.services.decisionengine.businesslogic\\services\\experianservice.cs||{A6C744A8-0E4A-4FC6-886A-************}"}, {"AbsoluteMoniker": "D:0:0:{6F6DEF34-8616-4981-9F27-2F0140406372}|BlueTape.Services.DecisionEngine.BusinessLogic\\BlueTape.Services.DecisionEngine.BusinessLogic.csproj|c:\\users\\<USER>\\source\\repos\\bluetape\\decision-engine\\src\\bluetape.services.decisionengine.businesslogic\\services\\emailnotificationservice.cs||{A6C744A8-0E4A-4FC6-886A-************}", "RelativeMoniker": "D:0:0:{6F6DEF34-8616-4981-9F27-2F0140406372}|BlueTape.Services.DecisionEngine.BusinessLogic\\BlueTape.Services.DecisionEngine.BusinessLogic.csproj|solutionrelative:bluetape.services.decisionengine.businesslogic\\services\\emailnotificationservice.cs||{A6C744A8-0E4A-4FC6-886A-************}"}, {"AbsoluteMoniker": "D:0:0:{6F6DEF34-8616-4981-9F27-2F0140406372}|BlueTape.Services.DecisionEngine.BusinessLogic\\BlueTape.Services.DecisionEngine.BusinessLogic.csproj|c:\\users\\<USER>\\source\\repos\\bluetape\\decision-engine\\src\\bluetape.services.decisionengine.businesslogic\\services\\connectornotificationservice.cs||{A6C744A8-0E4A-4FC6-886A-************}", "RelativeMoniker": "D:0:0:{6F6DEF34-8616-4981-9F27-2F0140406372}|BlueTape.Services.DecisionEngine.BusinessLogic\\BlueTape.Services.DecisionEngine.BusinessLogic.csproj|solutionrelative:bluetape.services.decisionengine.businesslogic\\services\\connectornotificationservice.cs||{A6C744A8-0E4A-4FC6-886A-************}"}, {"AbsoluteMoniker": "D:0:0:{6F6DEF34-8616-4981-9F27-2F0140406372}|BlueTape.Services.DecisionEngine.BusinessLogic\\BlueTape.Services.DecisionEngine.BusinessLogic.csproj|c:\\users\\<USER>\\source\\repos\\bluetape\\decision-engine\\src\\bluetape.services.decisionengine.businesslogic\\services\\companyservice.cs||{A6C744A8-0E4A-4FC6-886A-************}", "RelativeMoniker": "D:0:0:{6F6DEF34-8616-4981-9F27-2F0140406372}|BlueTape.Services.DecisionEngine.BusinessLogic\\BlueTape.Services.DecisionEngine.BusinessLogic.csproj|solutionrelative:bluetape.services.decisionengine.businesslogic\\services\\companyservice.cs||{A6C744A8-0E4A-4FC6-886A-************}"}, {"AbsoluteMoniker": "D:0:0:{21BDAC41-9D08-4CB3-ADDF-7E1795FDF1AC}|lambdas\\DrawApproval\\BlueTape.Services.DrawApproval.PreliminaryStep\\BlueTape.Services.DrawApproval.PreliminaryStep.csproj|c:\\users\\<USER>\\source\\repos\\bluetape\\decision-engine\\src\\lambdas\\drawapproval\\bluetape.services.drawapproval.preliminarystep\\appsettings.prod.json||{90A6B3A7-C1A3-4009-A288-E2FF89E96FA0}", "RelativeMoniker": "D:0:0:{21BDAC41-9D08-4CB3-ADDF-7E1795FDF1AC}|lambdas\\DrawApproval\\BlueTape.Services.DrawApproval.PreliminaryStep\\BlueTape.Services.DrawApproval.PreliminaryStep.csproj|solutionrelative:lambdas\\drawapproval\\bluetape.services.drawapproval.preliminarystep\\appsettings.prod.json||{90A6B3A7-C1A3-4009-A288-E2FF89E96FA0}"}, {"AbsoluteMoniker": "D:0:0:{8F181A7A-F37E-43D8-B687-F16EC9711CC0}|lambdas\\DecisionEngine\\BlueTape.Services.CashFlowReportStep\\BlueTape.Services.CashFlowReportStep.csproj|c:\\users\\<USER>\\source\\repos\\bluetape\\decision-engine\\src\\lambdas\\decisionengine\\bluetape.services.cashflowreportstep\\function.cs||{A6C744A8-0E4A-4FC6-886A-************}", "RelativeMoniker": "D:0:0:{8F181A7A-F37E-43D8-B687-F16EC9711CC0}|lambdas\\DecisionEngine\\BlueTape.Services.CashFlowReportStep\\BlueTape.Services.CashFlowReportStep.csproj|solutionrelative:lambdas\\decisionengine\\bluetape.services.cashflowreportstep\\function.cs||{A6C744A8-0E4A-4FC6-886A-************}"}, {"AbsoluteMoniker": "D:0:0:{21BDAC41-9D08-4CB3-ADDF-7E1795FDF1AC}|lambdas\\DrawApproval\\BlueTape.Services.DrawApproval.PreliminaryStep\\BlueTape.Services.DrawApproval.PreliminaryStep.csproj|c:\\users\\<USER>\\source\\repos\\bluetape\\decision-engine\\src\\lambdas\\drawapproval\\bluetape.services.drawapproval.preliminarystep\\appsettings.development.json||{90A6B3A7-C1A3-4009-A288-E2FF89E96FA0}", "RelativeMoniker": "D:0:0:{21BDAC41-9D08-4CB3-ADDF-7E1795FDF1AC}|lambdas\\DrawApproval\\BlueTape.Services.DrawApproval.PreliminaryStep\\BlueTape.Services.DrawApproval.PreliminaryStep.csproj|solutionrelative:lambdas\\drawapproval\\bluetape.services.drawapproval.preliminarystep\\appsettings.development.json||{90A6B3A7-C1A3-4009-A288-E2FF89E96FA0}"}, {"AbsoluteMoniker": "D:0:0:{E0ECE16A-EB81-4E79-9208-89290911DF93}|lambdas\\DecisionEngine\\BlueTape.Services.DecisionEngine.InitializationStep\\BlueTape.Services.DecisionEngine.InitializationStep.csproj|c:\\users\\<USER>\\source\\repos\\bluetape\\decision-engine\\src\\lambdas\\decisionengine\\bluetape.services.decisionengine.initializationstep\\function.cs||{A6C744A8-0E4A-4FC6-886A-************}", "RelativeMoniker": "D:0:0:{E0ECE16A-EB81-4E79-9208-89290911DF93}|lambdas\\DecisionEngine\\BlueTape.Services.DecisionEngine.InitializationStep\\BlueTape.Services.DecisionEngine.InitializationStep.csproj|solutionrelative:lambdas\\decisionengine\\bluetape.services.decisionengine.initializationstep\\function.cs||{A6C744A8-0E4A-4FC6-886A-************}"}, {"AbsoluteMoniker": "D:0:0:{6F6DEF34-8616-4981-9F27-2F0140406372}|BlueTape.Services.DecisionEngine.BusinessLogic\\BlueTape.Services.DecisionEngine.BusinessLogic.csproj|c:\\users\\<USER>\\source\\repos\\bluetape\\decision-engine\\src\\bluetape.services.decisionengine.businesslogic\\steps\\decisionengine\\initializationstep.cs||{A6C744A8-0E4A-4FC6-886A-************}", "RelativeMoniker": "D:0:0:{6F6DEF34-8616-4981-9F27-2F0140406372}|BlueTape.Services.DecisionEngine.BusinessLogic\\BlueTape.Services.DecisionEngine.BusinessLogic.csproj|solutionrelative:bluetape.services.decisionengine.businesslogic\\steps\\decisionengine\\initializationstep.cs||{A6C744A8-0E4A-4FC6-886A-************}"}, {"AbsoluteMoniker": "D:0:0:{02691FF6-A7BA-4181-85F5-7A3AEB2738B8}|BlueTape.Services.DecisionEngine\\BlueTape.Services.DecisionEngine.csproj|c:\\users\\<USER>\\source\\repos\\bluetape\\decision-engine\\src\\bluetape.services.decisionengine\\appsettings.development.json||{90A6B3A7-C1A3-4009-A288-E2FF89E96FA0}", "RelativeMoniker": "D:0:0:{02691FF6-A7BA-4181-85F5-7A3AEB2738B8}|BlueTape.Services.DecisionEngine\\BlueTape.Services.DecisionEngine.csproj|solutionrelative:bluetape.services.decisionengine\\appsettings.development.json||{90A6B3A7-C1A3-4009-A288-E2FF89E96FA0}"}, {"AbsoluteMoniker": "D:0:0:{02691FF6-A7BA-4181-85F5-7A3AEB2738B8}|BlueTape.Services.DecisionEngine\\BlueTape.Services.DecisionEngine.csproj|c:\\users\\<USER>\\source\\repos\\bluetape\\decision-engine\\src\\bluetape.services.decisionengine\\appsettings.json||{90A6B3A7-C1A3-4009-A288-E2FF89E96FA0}", "RelativeMoniker": "D:0:0:{02691FF6-A7BA-4181-85F5-7A3AEB2738B8}|BlueTape.Services.DecisionEngine\\BlueTape.Services.DecisionEngine.csproj|solutionrelative:bluetape.services.decisionengine\\appsettings.json||{90A6B3A7-C1A3-4009-A288-E2FF89E96FA0}"}, {"AbsoluteMoniker": "D:0:0:{07ADD9E0-88A8-4F8F-83A1-06ECFDEFD017}|lambdas\\DrawApproval\\BlueTape.Services.DrawApproval.InitializationStep\\BlueTape.Services.DrawApproval.InitializationStep.csproj|c:\\users\\<USER>\\source\\repos\\bluetape\\decision-engine\\src\\lambdas\\drawapproval\\bluetape.services.drawapproval.initializationstep\\aws-lambda-tools-defaults.json||{90A6B3A7-C1A3-4009-A288-E2FF89E96FA0}", "RelativeMoniker": "D:0:0:{07ADD9E0-88A8-4F8F-83A1-06ECFDEFD017}|lambdas\\DrawApproval\\BlueTape.Services.DrawApproval.InitializationStep\\BlueTape.Services.DrawApproval.InitializationStep.csproj|solutionrelative:lambdas\\drawapproval\\bluetape.services.drawapproval.initializationstep\\aws-lambda-tools-defaults.json||{90A6B3A7-C1A3-4009-A288-E2FF89E96FA0}"}, {"AbsoluteMoniker": "D:0:0:{07ADD9E0-88A8-4F8F-83A1-06ECFDEFD017}|lambdas\\DrawApproval\\BlueTape.Services.DrawApproval.InitializationStep\\BlueTape.Services.DrawApproval.InitializationStep.csproj|c:\\users\\<USER>\\source\\repos\\bluetape\\decision-engine\\src\\lambdas\\drawapproval\\bluetape.services.drawapproval.initializationstep\\bluetape.services.drawapproval.initializationstep.csproj||{FA3CD31E-987B-443A-9B81-186104E8DAC1}|", "RelativeMoniker": "D:0:0:{07ADD9E0-88A8-4F8F-83A1-06ECFDEFD017}|lambdas\\DrawApproval\\BlueTape.Services.DrawApproval.InitializationStep\\BlueTape.Services.DrawApproval.InitializationStep.csproj|solutionrelative:lambdas\\drawapproval\\bluetape.services.drawapproval.initializationstep\\bluetape.services.drawapproval.initializationstep.csproj||{FA3CD31E-987B-443A-9B81-186104E8DAC1}|"}, {"AbsoluteMoniker": "D:0:0:{6F6DEF34-8616-4981-9F27-2F0140406372}|BlueTape.Services.DecisionEngine.BusinessLogic\\BlueTape.Services.DecisionEngine.BusinessLogic.csproj|c:\\users\\<USER>\\source\\repos\\bluetape\\decision-engine\\src\\bluetape.services.decisionengine.businesslogic\\constants\\experianconstants.cs||{A6C744A8-0E4A-4FC6-886A-************}", "RelativeMoniker": "D:0:0:{6F6DEF34-8616-4981-9F27-2F0140406372}|BlueTape.Services.DecisionEngine.BusinessLogic\\BlueTape.Services.DecisionEngine.BusinessLogic.csproj|solutionrelative:bluetape.services.decisionengine.businesslogic\\constants\\experianconstants.cs||{A6C744A8-0E4A-4FC6-886A-************}"}, {"AbsoluteMoniker": "D:0:0:{682BCDC0-7D44-4535-BBAA-49A59C3CD7A5}|BlueTape.Services.DecisionEngine.DataAccess.External\\BlueTape.Services.DecisionEngine.DataAccess.External.csproj|c:\\users\\<USER>\\source\\repos\\bluetape\\decision-engine\\src\\bluetape.services.decisionengine.dataaccess.external\\httpclients\\onboardingservicehttpclient.cs||{A6C744A8-0E4A-4FC6-886A-************}", "RelativeMoniker": "D:0:0:{682BCDC0-7D44-4535-BBAA-49A59C3CD7A5}|BlueTape.Services.DecisionEngine.DataAccess.External\\BlueTape.Services.DecisionEngine.DataAccess.External.csproj|solutionrelative:bluetape.services.decisionengine.dataaccess.external\\httpclients\\onboardingservicehttpclient.cs||{A6C744A8-0E4A-4FC6-886A-************}"}, {"AbsoluteMoniker": "D:0:0:{6F6DEF34-8616-4981-9F27-2F0140406372}|BlueTape.Services.DecisionEngine.BusinessLogic\\BlueTape.Services.DecisionEngine.BusinessLogic.csproj|c:\\users\\<USER>\\source\\repos\\bluetape\\decision-engine\\src\\bluetape.services.decisionengine.businesslogic\\infrastructure\\retrievedatadynamicallystrategies\\experian\\retrievejudgmentsdata.cs||{A6C744A8-0E4A-4FC6-886A-************}", "RelativeMoniker": "D:0:0:{6F6DEF34-8616-4981-9F27-2F0140406372}|BlueTape.Services.DecisionEngine.BusinessLogic\\BlueTape.Services.DecisionEngine.BusinessLogic.csproj|solutionrelative:bluetape.services.decisionengine.businesslogic\\infrastructure\\retrievedatadynamicallystrategies\\experian\\retrievejudgmentsdata.cs||{A6C744A8-0E4A-4FC6-886A-************}"}, {"AbsoluteMoniker": "D:0:0:{6F6DEF34-8616-4981-9F27-2F0140406372}|BlueTape.Services.DecisionEngine.BusinessLogic\\BlueTape.Services.DecisionEngine.BusinessLogic.csproj|c:\\users\\<USER>\\source\\repos\\bluetape\\decision-engine\\src\\bluetape.services.decisionengine.businesslogic\\steps\\drawapproval\\riskassessmentstep.cs||{A6C744A8-0E4A-4FC6-886A-************}", "RelativeMoniker": "D:0:0:{6F6DEF34-8616-4981-9F27-2F0140406372}|BlueTape.Services.DecisionEngine.BusinessLogic\\BlueTape.Services.DecisionEngine.BusinessLogic.csproj|solutionrelative:bluetape.services.decisionengine.businesslogic\\steps\\drawapproval\\riskassessmentstep.cs||{A6C744A8-0E4A-4FC6-886A-************}"}, {"AbsoluteMoniker": "D:0:0:{56DB2F3F-1CC9-4544-9CEF-4A7C04591E33}|lambdas\\DrawApproval\\BlueTape.Services.DrawApproval.RiskAssessmentStep\\BlueTape.Services.DrawApproval.RiskAssessmentStep.csproj|c:\\users\\<USER>\\source\\repos\\bluetape\\decision-engine\\src\\lambdas\\drawapproval\\bluetape.services.drawapproval.riskassessmentstep\\appsettings.beta.json||{90A6B3A7-C1A3-4009-A288-E2FF89E96FA0}", "RelativeMoniker": "D:0:0:{56DB2F3F-1CC9-4544-9CEF-4A7C04591E33}|lambdas\\DrawApproval\\BlueTape.Services.DrawApproval.RiskAssessmentStep\\BlueTape.Services.DrawApproval.RiskAssessmentStep.csproj|solutionrelative:lambdas\\drawapproval\\bluetape.services.drawapproval.riskassessmentstep\\appsettings.beta.json||{90A6B3A7-C1A3-4009-A288-E2FF89E96FA0}"}, {"AbsoluteMoniker": "D:0:0:{56DB2F3F-1CC9-4544-9CEF-4A7C04591E33}|lambdas\\DrawApproval\\BlueTape.Services.DrawApproval.RiskAssessmentStep\\BlueTape.Services.DrawApproval.RiskAssessmentStep.csproj|c:\\users\\<USER>\\source\\repos\\bluetape\\decision-engine\\src\\lambdas\\drawapproval\\bluetape.services.drawapproval.riskassessmentstep\\appsettings.json||{90A6B3A7-C1A3-4009-A288-E2FF89E96FA0}", "RelativeMoniker": "D:0:0:{56DB2F3F-1CC9-4544-9CEF-4A7C04591E33}|lambdas\\DrawApproval\\BlueTape.Services.DrawApproval.RiskAssessmentStep\\BlueTape.Services.DrawApproval.RiskAssessmentStep.csproj|solutionrelative:lambdas\\drawapproval\\bluetape.services.drawapproval.riskassessmentstep\\appsettings.json||{90A6B3A7-C1A3-4009-A288-E2FF89E96FA0}"}, {"AbsoluteMoniker": "D:0:0:{56DB2F3F-1CC9-4544-9CEF-4A7C04591E33}|lambdas\\DrawApproval\\BlueTape.Services.DrawApproval.RiskAssessmentStep\\BlueTape.Services.DrawApproval.RiskAssessmentStep.csproj|c:\\users\\<USER>\\source\\repos\\bluetape\\decision-engine\\src\\lambdas\\drawapproval\\bluetape.services.drawapproval.riskassessmentstep\\function.cs||{A6C744A8-0E4A-4FC6-886A-************}", "RelativeMoniker": "D:0:0:{56DB2F3F-1CC9-4544-9CEF-4A7C04591E33}|lambdas\\DrawApproval\\BlueTape.Services.DrawApproval.RiskAssessmentStep\\BlueTape.Services.DrawApproval.RiskAssessmentStep.csproj|solutionrelative:lambdas\\drawapproval\\bluetape.services.drawapproval.riskassessmentstep\\function.cs||{A6C744A8-0E4A-4FC6-886A-************}"}, {"AbsoluteMoniker": "D:0:0:{02691FF6-A7BA-4181-85F5-7A3AEB2738B8}|BlueTape.Services.DecisionEngine\\BlueTape.Services.DecisionEngine.csproj|c:\\users\\<USER>\\source\\repos\\bluetape\\decision-engine\\src\\bluetape.services.decisionengine\\properties\\launchsettings.json||{90A6B3A7-C1A3-4009-A288-E2FF89E96FA0}", "RelativeMoniker": "D:0:0:{02691FF6-A7BA-4181-85F5-7A3AEB2738B8}|BlueTape.Services.DecisionEngine\\BlueTape.Services.DecisionEngine.csproj|solutionrelative:bluetape.services.decisionengine\\properties\\launchsettings.json||{90A6B3A7-C1A3-4009-A288-E2FF89E96FA0}"}, {"AbsoluteMoniker": "D:0:0:{02691FF6-A7BA-4181-85F5-7A3AEB2738B8}|BlueTape.Services.DecisionEngine\\BlueTape.Services.DecisionEngine.csproj|c:\\users\\<USER>\\source\\repos\\bluetape\\decision-engine\\src\\bluetape.services.decisionengine\\constants\\loggerconstants.cs||{A6C744A8-0E4A-4FC6-886A-************}", "RelativeMoniker": "D:0:0:{02691FF6-A7BA-4181-85F5-7A3AEB2738B8}|BlueTape.Services.DecisionEngine\\BlueTape.Services.DecisionEngine.csproj|solutionrelative:bluetape.services.decisionengine\\constants\\loggerconstants.cs||{A6C744A8-0E4A-4FC6-886A-************}"}, {"AbsoluteMoniker": "D:0:0:{02691FF6-A7BA-4181-85F5-7A3AEB2738B8}|BlueTape.Services.DecisionEngine\\BlueTape.Services.DecisionEngine.csproj|c:\\users\\<USER>\\source\\repos\\bluetape\\decision-engine\\src\\bluetape.services.decisionengine\\program.cs||{A6C744A8-0E4A-4FC6-886A-************}", "RelativeMoniker": "D:0:0:{02691FF6-A7BA-4181-85F5-7A3AEB2738B8}|BlueTape.Services.DecisionEngine\\BlueTape.Services.DecisionEngine.csproj|solutionrelative:bluetape.services.decisionengine\\program.cs||{A6C744A8-0E4A-4FC6-886A-************}"}, {"AbsoluteMoniker": "D:0:0:{6F6DEF34-8616-4981-9F27-2F0140406372}|BlueTape.Services.DecisionEngine.BusinessLogic\\BlueTape.Services.DecisionEngine.BusinessLogic.csproj|c:\\users\\<USER>\\source\\repos\\bluetape\\decision-engine\\src\\bluetape.services.decisionengine.businesslogic\\extensions\\drawapprovaltypeextensions.cs||{A6C744A8-0E4A-4FC6-886A-************}", "RelativeMoniker": "D:0:0:{6F6DEF34-8616-4981-9F27-2F0140406372}|BlueTape.Services.DecisionEngine.BusinessLogic\\BlueTape.Services.DecisionEngine.BusinessLogic.csproj|solutionrelative:bluetape.services.decisionengine.businesslogic\\extensions\\drawapprovaltypeextensions.cs||{A6C744A8-0E4A-4FC6-886A-************}"}, {"AbsoluteMoniker": "D:0:0:{6F6DEF34-8616-4981-9F27-2F0140406372}|BlueTape.Services.DecisionEngine.BusinessLogic\\BlueTape.Services.DecisionEngine.BusinessLogic.csproj|c:\\users\\<USER>\\source\\repos\\bluetape\\decision-engine\\src\\bluetape.services.decisionengine.businesslogic\\extensions\\statusnoteextensions.cs||{A6C744A8-0E4A-4FC6-886A-************}", "RelativeMoniker": "D:0:0:{6F6DEF34-8616-4981-9F27-2F0140406372}|BlueTape.Services.DecisionEngine.BusinessLogic\\BlueTape.Services.DecisionEngine.BusinessLogic.csproj|solutionrelative:bluetape.services.decisionengine.businesslogic\\extensions\\statusnoteextensions.cs||{A6C744A8-0E4A-4FC6-886A-************}"}, {"AbsoluteMoniker": "D:0:0:{6F6DEF34-8616-4981-9F27-2F0140406372}|BlueTape.Services.DecisionEngine.BusinessLogic\\BlueTape.Services.DecisionEngine.BusinessLogic.csproj|c:\\users\\<USER>\\source\\repos\\bluetape\\decision-engine\\src\\bluetape.services.decisionengine.businesslogic\\extensions\\creditapplicationtypeextensions.cs||{A6C744A8-0E4A-4FC6-886A-************}", "RelativeMoniker": "D:0:0:{6F6DEF34-8616-4981-9F27-2F0140406372}|BlueTape.Services.DecisionEngine.BusinessLogic\\BlueTape.Services.DecisionEngine.BusinessLogic.csproj|solutionrelative:bluetape.services.decisionengine.businesslogic\\extensions\\creditapplicationtypeextensions.cs||{A6C744A8-0E4A-4FC6-886A-************}"}, {"AbsoluteMoniker": "D:0:0:{A2FE74E1-B743-11D0-AE1A-00A0C90FFFC3}|<MiscFiles>|C:\\Users\\<USER>\\AppData\\Local\\JetBrains\\Shared\\vAny\\DecompilerCache\\decompiler\\e8721e7d2fb14803b749d9015294b54d1b200\\1c\\5ec765de\\InHouseCreditStatus.cs||{A6C744A8-0E4A-4FC6-886A-************}"}, {"AbsoluteMoniker": "D:0:0:{6F6DEF34-8616-4981-9F27-2F0140406372}|BlueTape.Services.DecisionEngine.BusinessLogic\\BlueTape.Services.DecisionEngine.BusinessLogic.csproj|c:\\users\\<USER>\\source\\repos\\bluetape\\decision-engine\\src\\bluetape.services.decisionengine.businesslogic\\infrastructure\\retrievedatadynamicallystrategies\\experian\\retrievebankruptciesdata.cs||{A6C744A8-0E4A-4FC6-886A-************}", "RelativeMoniker": "D:0:0:{6F6DEF34-8616-4981-9F27-2F0140406372}|BlueTape.Services.DecisionEngine.BusinessLogic\\BlueTape.Services.DecisionEngine.BusinessLogic.csproj|solutionrelative:bluetape.services.decisionengine.businesslogic\\infrastructure\\retrievedatadynamicallystrategies\\experian\\retrievebankruptciesdata.cs||{A6C744A8-0E4A-4FC6-886A-************}"}, {"AbsoluteMoniker": "D:0:0:{596DBD41-4E1E-40B1-AD5F-79BFDE40B614}|BlueTape.Services.DecisionEngine.BusinessLogic.Tests\\BlueTape.Services.DecisionEngine.BusinessLogic.Tests.csproj|c:\\users\\<USER>\\source\\repos\\bluetape\\decision-engine\\src\\bluetape.services.decisionengine.businesslogic.tests\\testdata\\drawapprovaltestdata.cs||{A6C744A8-0E4A-4FC6-886A-************}", "RelativeMoniker": "D:0:0:{596DBD41-4E1E-40B1-AD5F-79BFDE40B614}|BlueTape.Services.DecisionEngine.BusinessLogic.Tests\\BlueTape.Services.DecisionEngine.BusinessLogic.Tests.csproj|solutionrelative:bluetape.services.decisionengine.businesslogic.tests\\testdata\\drawapprovaltestdata.cs||{A6C744A8-0E4A-4FC6-886A-************}"}, {"AbsoluteMoniker": "D:0:0:{682BCDC0-7D44-4535-BBAA-49A59C3CD7A5}|BlueTape.Services.DecisionEngine.DataAccess.External\\BlueTape.Services.DecisionEngine.DataAccess.External.csproj|c:\\users\\<USER>\\source\\repos\\bluetape\\decision-engine\\src\\bluetape.services.decisionengine.dataaccess.external\\externalservices\\experianintegrationexternalservice.cs||{A6C744A8-0E4A-4FC6-886A-************}", "RelativeMoniker": "D:0:0:{682BCDC0-7D44-4535-BBAA-49A59C3CD7A5}|BlueTape.Services.DecisionEngine.DataAccess.External\\BlueTape.Services.DecisionEngine.DataAccess.External.csproj|solutionrelative:bluetape.services.decisionengine.dataaccess.external\\externalservices\\experianintegrationexternalservice.cs||{A6C744A8-0E4A-4FC6-886A-************}"}, {"AbsoluteMoniker": "D:0:0:{6F6DEF34-8616-4981-9F27-2F0140406372}|BlueTape.Services.DecisionEngine.BusinessLogic\\BlueTape.Services.DecisionEngine.BusinessLogic.csproj|c:\\users\\<USER>\\source\\repos\\bluetape\\decision-engine\\src\\bluetape.services.decisionengine.businesslogic\\infrastructure\\retrievedatadynamicallystrategies\\experian\\retrievecreditstatusdata.cs||{A6C744A8-0E4A-4FC6-886A-************}", "RelativeMoniker": "D:0:0:{6F6DEF34-8616-4981-9F27-2F0140406372}|BlueTape.Services.DecisionEngine.BusinessLogic\\BlueTape.Services.DecisionEngine.BusinessLogic.csproj|solutionrelative:bluetape.services.decisionengine.businesslogic\\infrastructure\\retrievedatadynamicallystrategies\\experian\\retrievecreditstatusdata.cs||{A6C744A8-0E4A-4FC6-886A-************}"}, {"AbsoluteMoniker": "D:0:0:{6F6DEF34-8616-4981-9F27-2F0140406372}|BlueTape.Services.DecisionEngine.BusinessLogic\\BlueTape.Services.DecisionEngine.BusinessLogic.csproj|c:\\users\\<USER>\\source\\repos\\bluetape\\decision-engine\\src\\bluetape.services.decisionengine.businesslogic\\infrastructure\\retrievedatadynamicallystrategies\\lexisnexis\\retrievebusinessinstantiddata.cs||{A6C744A8-0E4A-4FC6-886A-************}", "RelativeMoniker": "D:0:0:{6F6DEF34-8616-4981-9F27-2F0140406372}|BlueTape.Services.DecisionEngine.BusinessLogic\\BlueTape.Services.DecisionEngine.BusinessLogic.csproj|solutionrelative:bluetape.services.decisionengine.businesslogic\\infrastructure\\retrievedatadynamicallystrategies\\lexisnexis\\retrievebusinessinstantiddata.cs||{A6C744A8-0E4A-4FC6-886A-************}"}, {"AbsoluteMoniker": "D:0:0:{6F6DEF34-8616-4981-9F27-2F0140406372}|BlueTape.Services.DecisionEngine.BusinessLogic\\BlueTape.Services.DecisionEngine.BusinessLogic.csproj|c:\\users\\<USER>\\source\\repos\\bluetape\\decision-engine\\src\\bluetape.services.decisionengine.businesslogic\\services\\lexisnexisservice.cs||{A6C744A8-0E4A-4FC6-886A-************}", "RelativeMoniker": "D:0:0:{6F6DEF34-8616-4981-9F27-2F0140406372}|BlueTape.Services.DecisionEngine.BusinessLogic\\BlueTape.Services.DecisionEngine.BusinessLogic.csproj|solutionrelative:bluetape.services.decisionengine.businesslogic\\services\\lexisnexisservice.cs||{A6C744A8-0E4A-4FC6-886A-************}"}, {"AbsoluteMoniker": "D:0:0:{682BCDC0-7D44-4535-BBAA-49A59C3CD7A5}|BlueTape.Services.DecisionEngine.DataAccess.External\\BlueTape.Services.DecisionEngine.DataAccess.External.csproj|c:\\users\\<USER>\\source\\repos\\bluetape\\decision-engine\\src\\bluetape.services.decisionengine.dataaccess.external\\externalservices\\lexisnexisintegrationexternalservice.cs||{A6C744A8-0E4A-4FC6-886A-************}", "RelativeMoniker": "D:0:0:{682BCDC0-7D44-4535-BBAA-49A59C3CD7A5}|BlueTape.Services.DecisionEngine.DataAccess.External\\BlueTape.Services.DecisionEngine.DataAccess.External.csproj|solutionrelative:bluetape.services.decisionengine.dataaccess.external\\externalservices\\lexisnexisintegrationexternalservice.cs||{A6C744A8-0E4A-4FC6-886A-************}"}, {"AbsoluteMoniker": "D:0:0:{682BCDC0-7D44-4535-BBAA-49A59C3CD7A5}|BlueTape.Services.DecisionEngine.DataAccess.External\\BlueTape.Services.DecisionEngine.DataAccess.External.csproj|c:\\users\\<USER>\\source\\repos\\bluetape\\decision-engine\\src\\bluetape.services.decisionengine.dataaccess.external\\externalservices\\loanexternalservice.cs||{A6C744A8-0E4A-4FC6-886A-************}", "RelativeMoniker": "D:0:0:{682BCDC0-7D44-4535-BBAA-49A59C3CD7A5}|BlueTape.Services.DecisionEngine.DataAccess.External\\BlueTape.Services.DecisionEngine.DataAccess.External.csproj|solutionrelative:bluetape.services.decisionengine.dataaccess.external\\externalservices\\loanexternalservice.cs||{A6C744A8-0E4A-4FC6-886A-************}"}, {"AbsoluteMoniker": "D:0:0:{6F6DEF34-8616-4981-9F27-2F0140406372}|BlueTape.Services.DecisionEngine.BusinessLogic\\BlueTape.Services.DecisionEngine.BusinessLogic.csproj|c:\\users\\<USER>\\source\\repos\\bluetape\\decision-engine\\src\\bluetape.services.decisionengine.businesslogic\\steps\\decisionengine\\kycstep.cs||{A6C744A8-0E4A-4FC6-886A-************}", "RelativeMoniker": "D:0:0:{6F6DEF34-8616-4981-9F27-2F0140406372}|BlueTape.Services.DecisionEngine.BusinessLogic\\BlueTape.Services.DecisionEngine.BusinessLogic.csproj|solutionrelative:bluetape.services.decisionengine.businesslogic\\steps\\decisionengine\\kycstep.cs||{A6C744A8-0E4A-4FC6-886A-************}"}, {"AbsoluteMoniker": "D:0:0:{6F6DEF34-8616-4981-9F27-2F0140406372}|BlueTape.Services.DecisionEngine.BusinessLogic\\BlueTape.Services.DecisionEngine.BusinessLogic.csproj|c:\\users\\<USER>\\source\\repos\\bluetape\\decision-engine\\src\\bluetape.services.decisionengine.businesslogic\\mapping\\mappers\\draftmapper.cs||{A6C744A8-0E4A-4FC6-886A-************}", "RelativeMoniker": "D:0:0:{6F6DEF34-8616-4981-9F27-2F0140406372}|BlueTape.Services.DecisionEngine.BusinessLogic\\BlueTape.Services.DecisionEngine.BusinessLogic.csproj|solutionrelative:bluetape.services.decisionengine.businesslogic\\mapping\\mappers\\draftmapper.cs||{A6C744A8-0E4A-4FC6-886A-************}"}, {"AbsoluteMoniker": "D:0:0:{6F6DEF34-8616-4981-9F27-2F0140406372}|BlueTape.Services.DecisionEngine.BusinessLogic\\BlueTape.Services.DecisionEngine.BusinessLogic.csproj|c:\\users\\<USER>\\source\\repos\\bluetape\\decision-engine\\src\\bluetape.services.decisionengine.businesslogic\\extensions\\draftextensions.cs||{A6C744A8-0E4A-4FC6-886A-************}", "RelativeMoniker": "D:0:0:{6F6DEF34-8616-4981-9F27-2F0140406372}|BlueTape.Services.DecisionEngine.BusinessLogic\\BlueTape.Services.DecisionEngine.BusinessLogic.csproj|solutionrelative:bluetape.services.decisionengine.businesslogic\\extensions\\draftextensions.cs||{A6C744A8-0E4A-4FC6-886A-************}"}, {"AbsoluteMoniker": "D:0:0:{6F6DEF34-8616-4981-9F27-2F0140406372}|BlueTape.Services.DecisionEngine.BusinessLogic\\BlueTape.Services.DecisionEngine.BusinessLogic.csproj|c:\\users\\<USER>\\source\\repos\\bluetape\\decision-engine\\src\\bluetape.services.decisionengine.businesslogic\\infrastructure\\retrievedatadynamicallystrategies\\lexisnexis\\retrievefrauddata.cs||{A6C744A8-0E4A-4FC6-886A-************}", "RelativeMoniker": "D:0:0:{6F6DEF34-8616-4981-9F27-2F0140406372}|BlueTape.Services.DecisionEngine.BusinessLogic\\BlueTape.Services.DecisionEngine.BusinessLogic.csproj|solutionrelative:bluetape.services.decisionengine.businesslogic\\infrastructure\\retrievedatadynamicallystrategies\\lexisnexis\\retrievefrauddata.cs||{A6C744A8-0E4A-4FC6-886A-************}"}, {"AbsoluteMoniker": "D:0:0:{682BCDC0-7D44-4535-BBAA-49A59C3CD7A5}|BlueTape.Services.DecisionEngine.DataAccess.External\\BlueTape.Services.DecisionEngine.DataAccess.External.csproj|c:\\users\\<USER>\\source\\repos\\bluetape\\decision-engine\\src\\bluetape.services.decisionengine.dataaccess.external\\extensions\\dependencyregistrar.cs||{A6C744A8-0E4A-4FC6-886A-************}", "RelativeMoniker": "D:0:0:{682BCDC0-7D44-4535-BBAA-49A59C3CD7A5}|BlueTape.Services.DecisionEngine.DataAccess.External\\BlueTape.Services.DecisionEngine.DataAccess.External.csproj|solutionrelative:bluetape.services.decisionengine.dataaccess.external\\extensions\\dependencyregistrar.cs||{A6C744A8-0E4A-4FC6-886A-************}"}, {"AbsoluteMoniker": "D:0:0:{F6C75605-5251-438F-B6DB-1C33EE6854D0}|lambdas\\DrawApproval\\BlueTape.Services.DrawApproval.InternalReviewStep\\BlueTape.Services.DrawApproval.InternalReviewStep.csproj|c:\\users\\<USER>\\source\\repos\\bluetape\\decision-engine\\src\\lambdas\\drawapproval\\bluetape.services.drawapproval.internalreviewstep\\function.cs||{A6C744A8-0E4A-4FC6-886A-************}", "RelativeMoniker": "D:0:0:{F6C75605-5251-438F-B6DB-1C33EE6854D0}|lambdas\\DrawApproval\\BlueTape.Services.DrawApproval.InternalReviewStep\\BlueTape.Services.DrawApproval.InternalReviewStep.csproj|solutionrelative:lambdas\\drawapproval\\bluetape.services.drawapproval.internalreviewstep\\function.cs||{A6C744A8-0E4A-4FC6-886A-************}"}, {"AbsoluteMoniker": "D:0:0:{6F6DEF34-8616-4981-9F27-2F0140406372}|BlueTape.Services.DecisionEngine.BusinessLogic\\BlueTape.Services.DecisionEngine.BusinessLogic.csproj|c:\\users\\<USER>\\source\\repos\\bluetape\\decision-engine\\src\\bluetape.services.decisionengine.businesslogic\\steps\\decisionengine\\bluetapestep.cs||{A6C744A8-0E4A-4FC6-886A-************}", "RelativeMoniker": "D:0:0:{6F6DEF34-8616-4981-9F27-2F0140406372}|BlueTape.Services.DecisionEngine.BusinessLogic\\BlueTape.Services.DecisionEngine.BusinessLogic.csproj|solutionrelative:bluetape.services.decisionengine.businesslogic\\steps\\decisionengine\\bluetapestep.cs||{A6C744A8-0E4A-4FC6-886A-************}"}, {"AbsoluteMoniker": "D:0:0:{6F6DEF34-8616-4981-9F27-2F0140406372}|BlueTape.Services.DecisionEngine.BusinessLogic\\BlueTape.Services.DecisionEngine.BusinessLogic.csproj|c:\\users\\<USER>\\source\\repos\\bluetape\\decision-engine\\src\\bluetape.services.decisionengine.businesslogic\\steps\\decisionengine\\preliminarystep.cs||{A6C744A8-0E4A-4FC6-886A-************}", "RelativeMoniker": "D:0:0:{6F6DEF34-8616-4981-9F27-2F0140406372}|BlueTape.Services.DecisionEngine.BusinessLogic\\BlueTape.Services.DecisionEngine.BusinessLogic.csproj|solutionrelative:bluetape.services.decisionengine.businesslogic\\steps\\decisionengine\\preliminarystep.cs||{A6C744A8-0E4A-4FC6-886A-************}"}, {"AbsoluteMoniker": "D:0:0:{6F6DEF34-8616-4981-9F27-2F0140406372}|BlueTape.Services.DecisionEngine.BusinessLogic\\BlueTape.Services.DecisionEngine.BusinessLogic.csproj|c:\\users\\<USER>\\source\\repos\\bluetape\\decision-engine\\src\\bluetape.services.decisionengine.businesslogic\\steps\\decisionengine\\bankstatementvalidationstep.cs||{A6C744A8-0E4A-4FC6-886A-************}", "RelativeMoniker": "D:0:0:{6F6DEF34-8616-4981-9F27-2F0140406372}|BlueTape.Services.DecisionEngine.BusinessLogic\\BlueTape.Services.DecisionEngine.BusinessLogic.csproj|solutionrelative:bluetape.services.decisionengine.businesslogic\\steps\\decisionengine\\bankstatementvalidationstep.cs||{A6C744A8-0E4A-4FC6-886A-************}"}, {"AbsoluteMoniker": "D:0:0:{6F6DEF34-8616-4981-9F27-2F0140406372}|BlueTape.Services.DecisionEngine.BusinessLogic\\BlueTape.Services.DecisionEngine.BusinessLogic.csproj|c:\\users\\<USER>\\source\\repos\\bluetape\\decision-engine\\src\\bluetape.services.decisionengine.businesslogic\\steps\\decisionengine\\bankaccountverificationstep.cs||{A6C744A8-0E4A-4FC6-886A-************}", "RelativeMoniker": "D:0:0:{6F6DEF34-8616-4981-9F27-2F0140406372}|BlueTape.Services.DecisionEngine.BusinessLogic\\BlueTape.Services.DecisionEngine.BusinessLogic.csproj|solutionrelative:bluetape.services.decisionengine.businesslogic\\steps\\decisionengine\\bankaccountverificationstep.cs||{A6C744A8-0E4A-4FC6-886A-************}"}, {"AbsoluteMoniker": "D:0:0:{6F6DEF34-8616-4981-9F27-2F0140406372}|BlueTape.Services.DecisionEngine.BusinessLogic\\BlueTape.Services.DecisionEngine.BusinessLogic.csproj|c:\\users\\<USER>\\source\\repos\\bluetape\\decision-engine\\src\\bluetape.services.decisionengine.businesslogic\\steps\\decisionengine\\affordabilityassessmentstep.cs||{A6C744A8-0E4A-4FC6-886A-************}", "RelativeMoniker": "D:0:0:{6F6DEF34-8616-4981-9F27-2F0140406372}|BlueTape.Services.DecisionEngine.BusinessLogic\\BlueTape.Services.DecisionEngine.BusinessLogic.csproj|solutionrelative:bluetape.services.decisionengine.businesslogic\\steps\\decisionengine\\affordabilityassessmentstep.cs||{A6C744A8-0E4A-4FC6-886A-************}"}, {"AbsoluteMoniker": "D:0:0:{6F6DEF34-8616-4981-9F27-2F0140406372}|BlueTape.Services.DecisionEngine.BusinessLogic\\BlueTape.Services.DecisionEngine.BusinessLogic.csproj|c:\\users\\<USER>\\source\\repos\\bluetape\\decision-engine\\src\\bluetape.services.decisionengine.businesslogic\\abstractions\\steps\\base\\istepimplementationbase.cs||{A6C744A8-0E4A-4FC6-886A-************}", "RelativeMoniker": "D:0:0:{6F6DEF34-8616-4981-9F27-2F0140406372}|BlueTape.Services.DecisionEngine.BusinessLogic\\BlueTape.Services.DecisionEngine.BusinessLogic.csproj|solutionrelative:bluetape.services.decisionengine.businesslogic\\abstractions\\steps\\base\\istepimplementationbase.cs||{A6C744A8-0E4A-4FC6-886A-************}"}, {"AbsoluteMoniker": "D:0:0:{596DBD41-4E1E-40B1-AD5F-79BFDE40B614}|BlueTape.Services.DecisionEngine.BusinessLogic.Tests\\BlueTape.Services.DecisionEngine.BusinessLogic.Tests.csproj|c:\\users\\<USER>\\source\\repos\\bluetape\\decision-engine\\src\\bluetape.services.decisionengine.businesslogic.tests\\services\\loanservicetests.cs||{A6C744A8-0E4A-4FC6-886A-************}", "RelativeMoniker": "D:0:0:{596DBD41-4E1E-40B1-AD5F-79BFDE40B614}|BlueTape.Services.DecisionEngine.BusinessLogic.Tests\\BlueTape.Services.DecisionEngine.BusinessLogic.Tests.csproj|solutionrelative:bluetape.services.decisionengine.businesslogic.tests\\services\\loanservicetests.cs||{A6C744A8-0E4A-4FC6-886A-************}"}, {"AbsoluteMoniker": "D:0:0:{6F6DEF34-8616-4981-9F27-2F0140406372}|BlueTape.Services.DecisionEngine.BusinessLogic\\BlueTape.Services.DecisionEngine.BusinessLogic.csproj|c:\\users\\<USER>\\source\\repos\\bluetape\\decision-engine\\src\\bluetape.services.decisionengine.businesslogic\\services\\loanservice.cs||{A6C744A8-0E4A-4FC6-886A-************}", "RelativeMoniker": "D:0:0:{6F6DEF34-8616-4981-9F27-2F0140406372}|BlueTape.Services.DecisionEngine.BusinessLogic\\BlueTape.Services.DecisionEngine.BusinessLogic.csproj|solutionrelative:bluetape.services.decisionengine.businesslogic\\services\\loanservice.cs||{A6C744A8-0E4A-4FC6-886A-************}"}, {"AbsoluteMoniker": "D:0:0:{7A5C33E7-DDBE-4705-80D1-649FCBE338FA}|lambdas\\DecisionEngine\\BlueTape.Services.BlueTapeStep\\BlueTape.Services.BlueTapeStep.csproj|c:\\users\\<USER>\\source\\repos\\bluetape\\decision-engine\\src\\lambdas\\decisionengine\\bluetape.services.bluetapestep\\function.cs||{A6C744A8-0E4A-4FC6-886A-************}", "RelativeMoniker": "D:0:0:{7A5C33E7-DDBE-4705-80D1-649FCBE338FA}|lambdas\\DecisionEngine\\BlueTape.Services.BlueTapeStep\\BlueTape.Services.BlueTapeStep.csproj|solutionrelative:lambdas\\decisionengine\\bluetape.services.bluetapestep\\function.cs||{A6C744A8-0E4A-4FC6-886A-************}"}, {"AbsoluteMoniker": "D:0:0:{7A5C33E7-DDBE-4705-80D1-649FCBE338FA}|lambdas\\DecisionEngine\\BlueTape.Services.BlueTapeStep\\BlueTape.Services.BlueTapeStep.csproj|c:\\users\\<USER>\\source\\repos\\bluetape\\decision-engine\\src\\lambdas\\decisionengine\\bluetape.services.bluetapestep\\bluetape.services.bluetapestep.csproj||{FA3CD31E-987B-443A-9B81-186104E8DAC1}|", "RelativeMoniker": "D:0:0:{7A5C33E7-DDBE-4705-80D1-649FCBE338FA}|lambdas\\DecisionEngine\\BlueTape.Services.BlueTapeStep\\BlueTape.Services.BlueTapeStep.csproj|solutionrelative:lambdas\\decisionengine\\bluetape.services.bluetapestep\\bluetape.services.bluetapestep.csproj||{FA3CD31E-987B-443A-9B81-186104E8DAC1}|"}, {"AbsoluteMoniker": "D:0:0:{572D93E2-6325-437C-B9AE-6D7B57C001D0}|lambdas\\DecisionEngine\\BlueTape.Services.DecisionEngine.PreliminaryStep\\BlueTape.Services.DecisionEngine.PreliminaryStep.csproj|c:\\users\\<USER>\\source\\repos\\bluetape\\decision-engine\\src\\lambdas\\decisionengine\\bluetape.services.decisionengine.preliminarystep\\function.cs||{A6C744A8-0E4A-4FC6-886A-************}", "RelativeMoniker": "D:0:0:{572D93E2-6325-437C-B9AE-6D7B57C001D0}|lambdas\\DecisionEngine\\BlueTape.Services.DecisionEngine.PreliminaryStep\\BlueTape.Services.DecisionEngine.PreliminaryStep.csproj|solutionrelative:lambdas\\decisionengine\\bluetape.services.decisionengine.preliminarystep\\function.cs||{A6C744A8-0E4A-4FC6-886A-************}"}, {"AbsoluteMoniker": "D:0:0:{682BCDC0-7D44-4535-BBAA-49A59C3CD7A5}|BlueTape.Services.DecisionEngine.DataAccess.External\\BlueTape.Services.DecisionEngine.DataAccess.External.csproj|c:\\users\\<USER>\\source\\repos\\bluetape\\decision-engine\\src\\bluetape.services.decisionengine.dataaccess.external\\externalservices\\companyexternalservice.cs||{A6C744A8-0E4A-4FC6-886A-************}", "RelativeMoniker": "D:0:0:{682BCDC0-7D44-4535-BBAA-49A59C3CD7A5}|BlueTape.Services.DecisionEngine.DataAccess.External\\BlueTape.Services.DecisionEngine.DataAccess.External.csproj|solutionrelative:bluetape.services.decisionengine.dataaccess.external\\externalservices\\companyexternalservice.cs||{A6C744A8-0E4A-4FC6-886A-************}"}, {"AbsoluteMoniker": "D:0:0:{682BCDC0-7D44-4535-BBAA-49A59C3CD7A5}|BlueTape.Services.DecisionEngine.DataAccess.External\\BlueTape.Services.DecisionEngine.DataAccess.External.csproj|c:\\users\\<USER>\\source\\repos\\bluetape\\decision-engine\\src\\bluetape.services.decisionengine.dataaccess.external\\abstractions\\externalservices\\icompanyexternalservice.cs||{A6C744A8-0E4A-4FC6-886A-************}", "RelativeMoniker": "D:0:0:{682BCDC0-7D44-4535-BBAA-49A59C3CD7A5}|BlueTape.Services.DecisionEngine.DataAccess.External\\BlueTape.Services.DecisionEngine.DataAccess.External.csproj|solutionrelative:bluetape.services.decisionengine.dataaccess.external\\abstractions\\externalservices\\icompanyexternalservice.cs||{A6C744A8-0E4A-4FC6-886A-************}"}, {"AbsoluteMoniker": "D:0:0:{********-3B51-486D-9C7A-7404812DC75E}|lambdas\\DecisionEngine\\BlueTape.Services.BankAccountVerificationStep\\BlueTape.Services.BankAccountVerificationStep.csproj|c:\\users\\<USER>\\source\\repos\\bluetape\\decision-engine\\src\\lambdas\\decisionengine\\bluetape.services.bankaccountverificationstep\\function.cs||{A6C744A8-0E4A-4FC6-886A-************}", "RelativeMoniker": "D:0:0:{********-3B51-486D-9C7A-7404812DC75E}|lambdas\\DecisionEngine\\BlueTape.Services.BankAccountVerificationStep\\BlueTape.Services.BankAccountVerificationStep.csproj|solutionrelative:lambdas\\decisionengine\\bluetape.services.bankaccountverificationstep\\function.cs||{A6C744A8-0E4A-4FC6-886A-************}"}, {"AbsoluteMoniker": "D:0:0:{682BCDC0-7D44-4535-BBAA-49A59C3CD7A5}|BlueTape.Services.DecisionEngine.DataAccess.External\\BlueTape.Services.DecisionEngine.DataAccess.External.csproj|c:\\users\\<USER>\\source\\repos\\bluetape\\decision-engine\\src\\bluetape.services.decisionengine.dataaccess.external\\constants\\lexisnexisserviceconstants.cs||{A6C744A8-0E4A-4FC6-886A-************}", "RelativeMoniker": "D:0:0:{682BCDC0-7D44-4535-BBAA-49A59C3CD7A5}|BlueTape.Services.DecisionEngine.DataAccess.External\\BlueTape.Services.DecisionEngine.DataAccess.External.csproj|solutionrelative:bluetape.services.decisionengine.dataaccess.external\\constants\\lexisnexisserviceconstants.cs||{A6C744A8-0E4A-4FC6-886A-************}"}, {"AbsoluteMoniker": "D:0:0:{56DB2F3F-1CC9-4544-9CEF-4A7C04591E33}|lambdas\\DrawApproval\\BlueTape.Services.DrawApproval.RiskAssessmentStep\\BlueTape.Services.DrawApproval.RiskAssessmentStep.csproj|c:\\users\\<USER>\\source\\repos\\bluetape\\decision-engine\\src\\lambdas\\drawapproval\\bluetape.services.drawapproval.riskassessmentstep\\appsettings.development.json||{90A6B3A7-C1A3-4009-A288-E2FF89E96FA0}", "RelativeMoniker": "D:0:0:{56DB2F3F-1CC9-4544-9CEF-4A7C04591E33}|lambdas\\DrawApproval\\BlueTape.Services.DrawApproval.RiskAssessmentStep\\BlueTape.Services.DrawApproval.RiskAssessmentStep.csproj|solutionrelative:lambdas\\drawapproval\\bluetape.services.drawapproval.riskassessmentstep\\appsettings.development.json||{90A6B3A7-C1A3-4009-A288-E2FF89E96FA0}"}, {"AbsoluteMoniker": "D:0:0:{F6C75605-5251-438F-B6DB-1C33EE6854D0}|lambdas\\DrawApproval\\BlueTape.Services.DrawApproval.InternalReviewStep\\BlueTape.Services.DrawApproval.InternalReviewStep.csproj|c:\\users\\<USER>\\source\\repos\\bluetape\\decision-engine\\src\\lambdas\\drawapproval\\bluetape.services.drawapproval.internalreviewstep\\appsettings.development.json||{90A6B3A7-C1A3-4009-A288-E2FF89E96FA0}", "RelativeMoniker": "D:0:0:{F6C75605-5251-438F-B6DB-1C33EE6854D0}|lambdas\\DrawApproval\\BlueTape.Services.DrawApproval.InternalReviewStep\\BlueTape.Services.DrawApproval.InternalReviewStep.csproj|solutionrelative:lambdas\\drawapproval\\bluetape.services.drawapproval.internalreviewstep\\appsettings.development.json||{90A6B3A7-C1A3-4009-A288-E2FF89E96FA0}"}, {"AbsoluteMoniker": "D:0:0:{07ADD9E0-88A8-4F8F-83A1-06ECFDEFD017}|lambdas\\DrawApproval\\BlueTape.Services.DrawApproval.InitializationStep\\BlueTape.Services.DrawApproval.InitializationStep.csproj|c:\\users\\<USER>\\source\\repos\\bluetape\\decision-engine\\src\\lambdas\\drawapproval\\bluetape.services.drawapproval.initializationstep\\appsettings.development.json||{90A6B3A7-C1A3-4009-A288-E2FF89E96FA0}", "RelativeMoniker": "D:0:0:{07ADD9E0-88A8-4F8F-83A1-06ECFDEFD017}|lambdas\\DrawApproval\\BlueTape.Services.DrawApproval.InitializationStep\\BlueTape.Services.DrawApproval.InitializationStep.csproj|solutionrelative:lambdas\\drawapproval\\bluetape.services.drawapproval.initializationstep\\appsettings.development.json||{90A6B3A7-C1A3-4009-A288-E2FF89E96FA0}"}, {"AbsoluteMoniker": "D:0:0:{572D93E2-6325-437C-B9AE-6D7B57C001D0}|lambdas\\DecisionEngine\\BlueTape.Services.DecisionEngine.PreliminaryStep\\BlueTape.Services.DecisionEngine.PreliminaryStep.csproj|c:\\users\\<USER>\\source\\repos\\bluetape\\decision-engine\\src\\lambdas\\decisionengine\\bluetape.services.decisionengine.preliminarystep\\appsettings.development.json||{90A6B3A7-C1A3-4009-A288-E2FF89E96FA0}", "RelativeMoniker": "D:0:0:{572D93E2-6325-437C-B9AE-6D7B57C001D0}|lambdas\\DecisionEngine\\BlueTape.Services.DecisionEngine.PreliminaryStep\\BlueTape.Services.DecisionEngine.PreliminaryStep.csproj|solutionrelative:lambdas\\decisionengine\\bluetape.services.decisionengine.preliminarystep\\appsettings.development.json||{90A6B3A7-C1A3-4009-A288-E2FF89E96FA0}"}, {"AbsoluteMoniker": "D:0:0:{E9BB4820-A7FF-49A8-B804-EDE0FD8E1B03}|lambdas\\DecisionEngine\\BlueTape.Services.DecisionEngine.KycStep\\BlueTape.Services.DecisionEngine.KycStep.csproj|c:\\users\\<USER>\\source\\repos\\bluetape\\decision-engine\\src\\lambdas\\decisionengine\\bluetape.services.decisionengine.kycstep\\appsettings.development.json||{90A6B3A7-C1A3-4009-A288-E2FF89E96FA0}", "RelativeMoniker": "D:0:0:{E9BB4820-A7FF-49A8-B804-EDE0FD8E1B03}|lambdas\\DecisionEngine\\BlueTape.Services.DecisionEngine.KycStep\\BlueTape.Services.DecisionEngine.KycStep.csproj|solutionrelative:lambdas\\decisionengine\\bluetape.services.decisionengine.kycstep\\appsettings.development.json||{90A6B3A7-C1A3-4009-A288-E2FF89E96FA0}"}, {"AbsoluteMoniker": "D:0:0:{FFBB677B-9816-4BE5-89DE-A54511DE13DF}|lambdas\\DecisionEngine\\BlueTape.Services.DecisionEngine.KybStep\\BlueTape.Services.DecisionEngine.KybStep.csproj|c:\\users\\<USER>\\source\\repos\\bluetape\\decision-engine\\src\\lambdas\\decisionengine\\bluetape.services.decisionengine.kybstep\\appsettings.development.json||{90A6B3A7-C1A3-4009-A288-E2FF89E96FA0}", "RelativeMoniker": "D:0:0:{FFBB677B-9816-4BE5-89DE-A54511DE13DF}|lambdas\\DecisionEngine\\BlueTape.Services.DecisionEngine.KybStep\\BlueTape.Services.DecisionEngine.KybStep.csproj|solutionrelative:lambdas\\decisionengine\\bluetape.services.decisionengine.kybstep\\appsettings.development.json||{90A6B3A7-C1A3-4009-A288-E2FF89E96FA0}"}, {"AbsoluteMoniker": "D:0:0:{E0ECE16A-EB81-4E79-9208-89290911DF93}|lambdas\\DecisionEngine\\BlueTape.Services.DecisionEngine.InitializationStep\\BlueTape.Services.DecisionEngine.InitializationStep.csproj|c:\\users\\<USER>\\source\\repos\\bluetape\\decision-engine\\src\\lambdas\\decisionengine\\bluetape.services.decisionengine.initializationstep\\appsettings.development.json||{90A6B3A7-C1A3-4009-A288-E2FF89E96FA0}", "RelativeMoniker": "D:0:0:{E0ECE16A-EB81-4E79-9208-89290911DF93}|lambdas\\DecisionEngine\\BlueTape.Services.DecisionEngine.InitializationStep\\BlueTape.Services.DecisionEngine.InitializationStep.csproj|solutionrelative:lambdas\\decisionengine\\bluetape.services.decisionengine.initializationstep\\appsettings.development.json||{90A6B3A7-C1A3-4009-A288-E2FF89E96FA0}"}, {"AbsoluteMoniker": "D:0:0:{A6762E82-F86A-49E3-A8EE-AD8C8C8D6833}|lambdas\\DecisionEngine\\BlueTape.Services.CreditRatingCoOwnersStep\\BlueTape.Services.CreditRatingCoOwnersStep.csproj|c:\\users\\<USER>\\source\\repos\\bluetape\\decision-engine\\src\\lambdas\\decisionengine\\bluetape.services.creditratingcoownersstep\\appsettings.development.json||{90A6B3A7-C1A3-4009-A288-E2FF89E96FA0}", "RelativeMoniker": "D:0:0:{A6762E82-F86A-49E3-A8EE-AD8C8C8D6833}|lambdas\\DecisionEngine\\BlueTape.Services.CreditRatingCoOwnersStep\\BlueTape.Services.CreditRatingCoOwnersStep.csproj|solutionrelative:lambdas\\decisionengine\\bluetape.services.creditratingcoownersstep\\appsettings.development.json||{90A6B3A7-C1A3-4009-A288-E2FF89E96FA0}"}, {"AbsoluteMoniker": "D:0:0:{E71877E9-5A9C-40D4-8BCE-093A33878994}|lambdas\\DecisionEngine\\BlueTape.Services.CreditRatingBusinessStep\\BlueTape.Services.CreditRatingBusinessStep.csproj|c:\\users\\<USER>\\source\\repos\\bluetape\\decision-engine\\src\\lambdas\\decisionengine\\bluetape.services.creditratingbusinessstep\\appsettings.development.json||{90A6B3A7-C1A3-4009-A288-E2FF89E96FA0}", "RelativeMoniker": "D:0:0:{E71877E9-5A9C-40D4-8BCE-093A33878994}|lambdas\\DecisionEngine\\BlueTape.Services.CreditRatingBusinessStep\\BlueTape.Services.CreditRatingBusinessStep.csproj|solutionrelative:lambdas\\decisionengine\\bluetape.services.creditratingbusinessstep\\appsettings.development.json||{90A6B3A7-C1A3-4009-A288-E2FF89E96FA0}"}, {"AbsoluteMoniker": "D:0:0:{7A5C33E7-DDBE-4705-80D1-649FCBE338FA}|lambdas\\DecisionEngine\\BlueTape.Services.BlueTapeStep\\BlueTape.Services.BlueTapeStep.csproj|c:\\users\\<USER>\\source\\repos\\bluetape\\decision-engine\\src\\lambdas\\decisionengine\\bluetape.services.bluetapestep\\appsettings.development.json||{90A6B3A7-C1A3-4009-A288-E2FF89E96FA0}", "RelativeMoniker": "D:0:0:{7A5C33E7-DDBE-4705-80D1-649FCBE338FA}|lambdas\\DecisionEngine\\BlueTape.Services.BlueTapeStep\\BlueTape.Services.BlueTapeStep.csproj|solutionrelative:lambdas\\decisionengine\\bluetape.services.bluetapestep\\appsettings.development.json||{90A6B3A7-C1A3-4009-A288-E2FF89E96FA0}"}, {"AbsoluteMoniker": "D:0:0:{CCA6F8B3-1DD9-4F4A-BCE8-645C4B636EDD}|lambdas\\DecisionEngine\\BlueTape.Services.AffordabilityAssessmentStep\\BlueTape.Services.AffordabilityAssessmentStep.csproj|c:\\users\\<USER>\\source\\repos\\bluetape\\decision-engine\\src\\lambdas\\decisionengine\\bluetape.services.affordabilityassessmentstep\\appsettings.development.json||{90A6B3A7-C1A3-4009-A288-E2FF89E96FA0}", "RelativeMoniker": "D:0:0:{CCA6F8B3-1DD9-4F4A-BCE8-645C4B636EDD}|lambdas\\DecisionEngine\\BlueTape.Services.AffordabilityAssessmentStep\\BlueTape.Services.AffordabilityAssessmentStep.csproj|solutionrelative:lambdas\\decisionengine\\bluetape.services.affordabilityassessmentstep\\appsettings.development.json||{90A6B3A7-C1A3-4009-A288-E2FF89E96FA0}"}, {"AbsoluteMoniker": "D:0:0:{********-3B51-486D-9C7A-7404812DC75E}|lambdas\\DecisionEngine\\BlueTape.Services.BankAccountVerificationStep\\BlueTape.Services.BankAccountVerificationStep.csproj|c:\\users\\<USER>\\source\\repos\\bluetape\\decision-engine\\src\\lambdas\\decisionengine\\bluetape.services.bankaccountverificationstep\\appsettings.development.json||{90A6B3A7-C1A3-4009-A288-E2FF89E96FA0}", "RelativeMoniker": "D:0:0:{********-3B51-486D-9C7A-7404812DC75E}|lambdas\\DecisionEngine\\BlueTape.Services.BankAccountVerificationStep\\BlueTape.Services.BankAccountVerificationStep.csproj|solutionrelative:lambdas\\decisionengine\\bluetape.services.bankaccountverificationstep\\appsettings.development.json||{90A6B3A7-C1A3-4009-A288-E2FF89E96FA0}"}, {"AbsoluteMoniker": "D:0:0:{E9BB4820-A7FF-49A8-B804-EDE0FD8E1B03}|lambdas\\DecisionEngine\\BlueTape.Services.DecisionEngine.KycStep\\BlueTape.Services.DecisionEngine.KycStep.csproj|c:\\users\\<USER>\\source\\repos\\bluetape\\decision-engine\\src\\lambdas\\decisionengine\\bluetape.services.decisionengine.kycstep\\function.cs||{A6C744A8-0E4A-4FC6-886A-************}", "RelativeMoniker": "D:0:0:{E9BB4820-A7FF-49A8-B804-EDE0FD8E1B03}|lambdas\\DecisionEngine\\BlueTape.Services.DecisionEngine.KycStep\\BlueTape.Services.DecisionEngine.KycStep.csproj|solutionrelative:lambdas\\decisionengine\\bluetape.services.decisionengine.kycstep\\function.cs||{A6C744A8-0E4A-4FC6-886A-************}"}, {"AbsoluteMoniker": "D:0:0:{6F6DEF34-8616-4981-9F27-2F0140406372}|BlueTape.Services.DecisionEngine.BusinessLogic\\BlueTape.Services.DecisionEngine.BusinessLogic.csproj|c:\\users\\<USER>\\source\\repos\\bluetape\\decision-engine\\src\\bluetape.services.decisionengine.businesslogic\\steps\\decisionengine\\kybstep.cs||{A6C744A8-0E4A-4FC6-886A-************}", "RelativeMoniker": "D:0:0:{6F6DEF34-8616-4981-9F27-2F0140406372}|BlueTape.Services.DecisionEngine.BusinessLogic\\BlueTape.Services.DecisionEngine.BusinessLogic.csproj|solutionrelative:bluetape.services.decisionengine.businesslogic\\steps\\decisionengine\\kybstep.cs||{A6C744A8-0E4A-4FC6-886A-************}"}, {"AbsoluteMoniker": "D:0:0:{6F6DEF34-8616-4981-9F27-2F0140406372}|BlueTape.Services.DecisionEngine.BusinessLogic\\BlueTape.Services.DecisionEngine.BusinessLogic.csproj|c:\\users\\<USER>\\source\\repos\\bluetape\\decision-engine\\src\\bluetape.services.decisionengine.businesslogic\\models\\cipher\\ciphermodel.cs||{A6C744A8-0E4A-4FC6-886A-************}", "RelativeMoniker": "D:0:0:{6F6DEF34-8616-4981-9F27-2F0140406372}|BlueTape.Services.DecisionEngine.BusinessLogic\\BlueTape.Services.DecisionEngine.BusinessLogic.csproj|solutionrelative:bluetape.services.decisionengine.businesslogic\\models\\cipher\\ciphermodel.cs||{A6C744A8-0E4A-4FC6-886A-************}"}], "DocumentGroupContainers": [{"Orientation": 0, "VerticalTabListWidth": 256, "DocumentGroups": [{"DockedWidth": 200, "SelectedChildIndex": 1, "Children": [{"$type": "Document", "DocumentIndex": 4, "Title": "CashFlowReviewStep.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\decision-engine\\src\\BlueTape.Services.DecisionEngine.BusinessLogic\\Steps\\DecisionEngine\\CashFlowReviewStep.cs", "RelativeDocumentMoniker": "BlueTape.Services.DecisionEngine.BusinessLogic\\Steps\\DecisionEngine\\CashFlowReviewStep.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\decision-engine\\src\\BlueTape.Services.DecisionEngine.BusinessLogic\\Steps\\DecisionEngine\\CashFlowReviewStep.cs", "RelativeToolTip": "BlueTape.Services.DecisionEngine.BusinessLogic\\Steps\\DecisionEngine\\CashFlowReviewStep.cs", "ViewState": "AgIAAGgAAAAAAAAAAAAWwIAAAAA8AAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-04-23T13:45:11.724Z"}, {"$type": "Document", "DocumentIndex": 0, "Title": "PreliminaryStep.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\decision-engine\\src\\BlueTape.Services.DecisionEngine.BusinessLogic\\Steps\\DrawApproval\\PreliminaryStep.cs", "RelativeDocumentMoniker": "BlueTape.Services.DecisionEngine.BusinessLogic\\Steps\\DrawApproval\\PreliminaryStep.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\decision-engine\\src\\BlueTape.Services.DecisionEngine.BusinessLogic\\Steps\\DrawApproval\\PreliminaryStep.cs", "RelativeToolTip": "BlueTape.Services.DecisionEngine.BusinessLogic\\Steps\\DrawApproval\\PreliminaryStep.cs", "ViewState": "AgIAAIAAAAAAAAAAAAAIwJEAAAAbAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-03-20T07:03:04.088Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 3, "Title": "OnBoardingExternalService.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\decision-engine\\src\\BlueTape.Services.DecisionEngine.DataAccess.External\\ExternalServices\\OnBoardingExternalService.cs", "RelativeDocumentMoniker": "BlueTape.Services.DecisionEngine.DataAccess.External\\ExternalServices\\OnBoardingExternalService.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\decision-engine\\src\\BlueTape.Services.DecisionEngine.DataAccess.External\\ExternalServices\\OnBoardingExternalService.cs", "RelativeToolTip": "BlueTape.Services.DecisionEngine.DataAccess.External\\ExternalServices\\OnBoardingExternalService.cs", "ViewState": "AgIAAE8CAAAAAAAAAAAmwFoCAAA0AAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2024-06-10T10:36:45.568Z"}, {"$type": "Document", "DocumentIndex": 10, "Title": "DependencyRegistrar.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\decision-engine\\src\\BlueTape.Services.DecisionEngine.BusinessLogic\\DI\\DependencyRegistrar.cs", "RelativeDocumentMoniker": "BlueTape.Services.DecisionEngine.BusinessLogic\\DI\\DependencyRegistrar.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\decision-engine\\src\\BlueTape.Services.DecisionEngine.BusinessLogic\\DI\\DependencyRegistrar.cs", "RelativeToolTip": "BlueTape.Services.DecisionEngine.BusinessLogic\\DI\\DependencyRegistrar.cs", "ViewState": "AgIAAFEAAAAAAAAAAAAwwGIAAABCAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2024-06-24T15:40:45.783Z"}, {"$type": "Document", "DocumentIndex": 1, "Title": "ApproveIssueDrawStep.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\decision-engine\\src\\BlueTape.Services.DecisionEngine.BusinessLogic\\Steps\\DrawApproval\\ApproveIssueDrawStep.cs", "RelativeDocumentMoniker": "BlueTape.Services.DecisionEngine.BusinessLogic\\Steps\\DrawApproval\\ApproveIssueDrawStep.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\decision-engine\\src\\BlueTape.Services.DecisionEngine.BusinessLogic\\Steps\\DrawApproval\\ApproveIssueDrawStep.cs", "RelativeToolTip": "BlueTape.Services.DecisionEngine.BusinessLogic\\Steps\\DrawApproval\\ApproveIssueDrawStep.cs", "ViewState": "AgIAAAABAAAAAAAAAAAuwAMBAAAcAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-04T09:21:47.275Z"}, {"$type": "Document", "DocumentIndex": 5, "Title": "CashFlowReportStep.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\decision-engine\\src\\BlueTape.Services.DecisionEngine.BusinessLogic\\Steps\\DecisionEngine\\CashFlowReportStep.cs", "RelativeDocumentMoniker": "BlueTape.Services.DecisionEngine.BusinessLogic\\Steps\\DecisionEngine\\CashFlowReportStep.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\decision-engine\\src\\BlueTape.Services.DecisionEngine.BusinessLogic\\Steps\\DecisionEngine\\CashFlowReportStep.cs", "RelativeToolTip": "BlueTape.Services.DecisionEngine.BusinessLogic\\Steps\\DecisionEngine\\CashFlowReportStep.cs", "ViewState": "AgIAAHEAAAAAAAAAAAAWwIMAAAA5AAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2024-06-13T07:17:28.986Z"}, {"$type": "Document", "DocumentIndex": 2, "Title": "OnBoardingService.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\decision-engine\\src\\BlueTape.Services.DecisionEngine.BusinessLogic\\Services\\OnBoardingService.cs", "RelativeDocumentMoniker": "BlueTape.Services.DecisionEngine.BusinessLogic\\Services\\OnBoardingService.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\decision-engine\\src\\BlueTape.Services.DecisionEngine.BusinessLogic\\Services\\OnBoardingService.cs", "RelativeToolTip": "BlueTape.Services.DecisionEngine.BusinessLogic\\Services\\OnBoardingService.cs", "ViewState": "AgIAAOEAAAAAAAAAAAD4v/QAAAAsAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2024-06-10T12:03:04.84Z"}, {"$type": "Document", "DocumentIndex": 7, "Title": "PlaidIntegrationExternalService.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\decision-engine\\src\\BlueTape.Services.DecisionEngine.DataAccess.External\\ExternalServices\\PlaidIntegrationExternalService.cs", "RelativeDocumentMoniker": "BlueTape.Services.DecisionEngine.DataAccess.External\\ExternalServices\\PlaidIntegrationExternalService.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\decision-engine\\src\\BlueTape.Services.DecisionEngine.DataAccess.External\\ExternalServices\\PlaidIntegrationExternalService.cs", "RelativeToolTip": "BlueTape.Services.DecisionEngine.DataAccess.External\\ExternalServices\\PlaidIntegrationExternalService.cs", "ViewState": "AgIAACQAAAAAAAAAAAAWwDMAAABJAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-03-19T11:45:55.871Z"}, {"$type": "Document", "DocumentIndex": 6, "Title": "CashFlowService.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\decision-engine\\src\\BlueTape.Services.DecisionEngine.BusinessLogic\\Services\\CashFlowService.cs", "RelativeDocumentMoniker": "BlueTape.Services.DecisionEngine.BusinessLogic\\Services\\CashFlowService.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\decision-engine\\src\\BlueTape.Services.DecisionEngine.BusinessLogic\\Services\\CashFlowService.cs", "RelativeToolTip": "BlueTape.Services.DecisionEngine.BusinessLogic\\Services\\CashFlowService.cs", "ViewState": "AgIAABsAAAAAAAAAAAArwB0AAAAnAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-12T10:34:11.683Z"}, {"$type": "Document", "DocumentIndex": 8, "Title": "ApproveIssueDrawStepTests.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\decision-engine\\src\\BlueTape.Services.DecisionEngine.BusinessLogic.Tests\\Steps\\DrawApproval\\ApproveIssueDrawStepTests.cs", "RelativeDocumentMoniker": "BlueTape.Services.DecisionEngine.BusinessLogic.Tests\\Steps\\DrawApproval\\ApproveIssueDrawStepTests.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\decision-engine\\src\\BlueTape.Services.DecisionEngine.BusinessLogic.Tests\\Steps\\DrawApproval\\ApproveIssueDrawStepTests.cs", "RelativeToolTip": "BlueTape.Services.DecisionEngine.BusinessLogic.Tests\\Steps\\DrawApproval\\ApproveIssueDrawStepTests.cs", "ViewState": "AgIAACMAAAAAAAAAAAAowDMAAAAfAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2024-12-11T15:11:19.388Z"}, {"$type": "Document", "DocumentIndex": 11, "Title": "IApproveIssueDrawStep.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\decision-engine\\src\\BlueTape.Services.DecisionEngine.BusinessLogic\\Abstractions\\Steps\\DrawApproval\\IApproveIssueDrawStep.cs", "RelativeDocumentMoniker": "BlueTape.Services.DecisionEngine.BusinessLogic\\Abstractions\\Steps\\DrawApproval\\IApproveIssueDrawStep.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\decision-engine\\src\\BlueTape.Services.DecisionEngine.BusinessLogic\\Abstractions\\Steps\\DrawApproval\\IApproveIssueDrawStep.cs", "RelativeToolTip": "BlueTape.Services.DecisionEngine.BusinessLogic\\Abstractions\\Steps\\DrawApproval\\IApproveIssueDrawStep.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAUAAAAaAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-04T09:20:58.096Z"}, {"$type": "Document", "DocumentIndex": 13, "Title": "IOnBoardingService.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\decision-engine\\src\\BlueTape.Services.DecisionEngine.BusinessLogic\\Abstractions\\Services\\IOnBoardingService.cs", "RelativeDocumentMoniker": "BlueTape.Services.DecisionEngine.BusinessLogic\\Abstractions\\Services\\IOnBoardingService.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\decision-engine\\src\\BlueTape.Services.DecisionEngine.BusinessLogic\\Abstractions\\Services\\IOnBoardingService.cs", "RelativeToolTip": "BlueTape.Services.DecisionEngine.BusinessLogic\\Abstractions\\Services\\IOnBoardingService.cs", "ViewState": "AgIAADwAAAAAAAAAAAAewE0AAAAdAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2024-05-28T09:00:40.685Z"}, {"$type": "Document", "DocumentIndex": 14, "Title": "IAutomatedApprovalService.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\decision-engine\\src\\BlueTape.Services.DecisionEngine.BusinessLogic\\Abstractions\\Services\\IAutomatedApprovalService.cs", "RelativeDocumentMoniker": "BlueTape.Services.DecisionEngine.BusinessLogic\\Abstractions\\Services\\IAutomatedApprovalService.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\decision-engine\\src\\BlueTape.Services.DecisionEngine.BusinessLogic\\Abstractions\\Services\\IAutomatedApprovalService.cs", "RelativeToolTip": "BlueTape.Services.DecisionEngine.BusinessLogic\\Abstractions\\Services\\IAutomatedApprovalService.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAABUAAAABAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-03T13:51:22.361Z"}, {"$type": "Document", "DocumentIndex": 9, "Title": "Function.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\decision-engine\\src\\lambdas\\DrawApproval\\BlueTape.Services.DrawApproval.ApproveIssueDrawStep\\Function.cs", "RelativeDocumentMoniker": "lambdas\\DrawApproval\\BlueTape.Services.DrawApproval.ApproveIssueDrawStep\\Function.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\decision-engine\\src\\lambdas\\DrawApproval\\BlueTape.Services.DrawApproval.ApproveIssueDrawStep\\Function.cs", "RelativeToolTip": "lambdas\\DrawApproval\\BlueTape.Services.DrawApproval.ApproveIssueDrawStep\\Function.cs", "ViewState": "AgIAAAQAAAAAAAAAAAAwwBUAAAAMAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-02-12T14:23:40.084Z"}, {"$type": "Document", "DocumentIndex": 18, "Title": "Microsoft.Managed.Core.targets", "DocumentMoniker": "C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\MSBuild\\Current\\Bin\\Roslyn\\Microsoft.Managed.Core.targets", "RelativeDocumentMoniker": "..\\..\\..\\..\\..\\..\\..\\Program Files\\Microsoft Visual Studio\\2022\\Community\\MSBuild\\Current\\Bin\\Roslyn\\Microsoft.Managed.Core.targets", "ToolTip": "C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\MSBuild\\Current\\Bin\\Roslyn\\Microsoft.Managed.Core.targets", "RelativeToolTip": "..\\..\\..\\..\\..\\..\\..\\Program Files\\Microsoft Visual Studio\\2022\\Community\\MSBuild\\Current\\Bin\\Roslyn\\Microsoft.Managed.Core.targets", "ViewState": "AgIAAKwAAAAAAAAAAAAAAL4AAAAEAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.003801|", "WhenOpened": "2024-12-11T15:32:59.254Z"}, {"$type": "Document", "DocumentIndex": 17, "Title": "TestingController.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\decision-engine\\src\\BlueTape.Services.DecisionEngine\\Controllers\\TestingController.cs", "RelativeDocumentMoniker": "BlueTape.Services.DecisionEngine\\Controllers\\TestingController.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\decision-engine\\src\\BlueTape.Services.DecisionEngine\\Controllers\\TestingController.cs", "RelativeToolTip": "BlueTape.Services.DecisionEngine\\Controllers\\TestingController.cs", "ViewState": "AgIAADEAAAAAAAAAAADgv20AAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2024-06-12T07:03:15.603Z"}, {"$type": "Document", "DocumentIndex": 12, "Title": "InitializationStep.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\decision-engine\\src\\BlueTape.Services.DecisionEngine.BusinessLogic\\Steps\\DrawApproval\\InitializationStep.cs", "RelativeDocumentMoniker": "BlueTape.Services.DecisionEngine.BusinessLogic\\Steps\\DrawApproval\\InitializationStep.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\decision-engine\\src\\BlueTape.Services.DecisionEngine.BusinessLogic\\Steps\\DrawApproval\\InitializationStep.cs", "RelativeToolTip": "BlueTape.Services.DecisionEngine.BusinessLogic\\Steps\\DrawApproval\\InitializationStep.cs", "ViewState": "AgIAAI0AAAAAAAAAAAAwwKYAAAAKAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-05-14T07:25:31.847Z"}, {"$type": "Document", "DocumentIndex": 19, "Title": "Microsoft.Common.CurrentVersion.targets", "DocumentMoniker": "C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\MSBuild\\Current\\Bin\\amd64\\Microsoft.Common.CurrentVersion.targets", "RelativeDocumentMoniker": "..\\..\\..\\..\\..\\..\\..\\Program Files\\Microsoft Visual Studio\\2022\\Community\\MSBuild\\Current\\Bin\\amd64\\Microsoft.Common.CurrentVersion.targets", "ToolTip": "C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\MSBuild\\Current\\Bin\\amd64\\Microsoft.Common.CurrentVersion.targets", "RelativeToolTip": "..\\..\\..\\..\\..\\..\\..\\Program Files\\Microsoft Visual Studio\\2022\\Community\\MSBuild\\Current\\Bin\\amd64\\Microsoft.Common.CurrentVersion.targets", "ViewState": "AgIAAC4XAAAAAAAAAAAAAEAXAAAEAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.003801|", "WhenOpened": "2024-12-11T15:31:43.656Z"}, {"$type": "Document", "DocumentIndex": 16, "Title": "OnBoardingModelsProfile.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\decision-engine\\src\\BlueTape.Services.DecisionEngine.BusinessLogic\\Mapping\\OnBoardingModelsProfile.cs", "RelativeDocumentMoniker": "BlueTape.Services.DecisionEngine.BusinessLogic\\Mapping\\OnBoardingModelsProfile.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\decision-engine\\src\\BlueTape.Services.DecisionEngine.BusinessLogic\\Mapping\\OnBoardingModelsProfile.cs", "RelativeToolTip": "BlueTape.Services.DecisionEngine.BusinessLogic\\Mapping\\OnBoardingModelsProfile.cs", "ViewState": "AgIAAGsAAAAAAAAAAAAmwHUAAAAdAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-05-14T10:15:49.636Z"}, {"$type": "Document", "DocumentIndex": 15, "Title": "Function.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\decision-engine\\src\\lambdas\\DrawApproval\\BlueTape.Services.DrawApproval.InitializationStep\\Function.cs", "RelativeDocumentMoniker": "lambdas\\DrawApproval\\BlueTape.Services.DrawApproval.InitializationStep\\Function.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\decision-engine\\src\\lambdas\\DrawApproval\\BlueTape.Services.DrawApproval.InitializationStep\\Function.cs", "RelativeToolTip": "lambdas\\DrawApproval\\BlueTape.Services.DrawApproval.InitializationStep\\Function.cs", "ViewState": "AgIAAAkAAAAAAAAAAAAtwCAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2024-06-11T12:45:15.069Z"}, {"$type": "Document", "DocumentIndex": 20, "Title": "NoSupplierBankAccountNumber.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\decision-engine\\src\\BlueTape.Services.DecisionEngine.BusinessLogic\\Models\\StepInputs\\DrawApproval\\NoSupplierBankAccountNumber.cs", "RelativeDocumentMoniker": "BlueTape.Services.DecisionEngine.BusinessLogic\\Models\\StepInputs\\DrawApproval\\NoSupplierBankAccountNumber.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\decision-engine\\src\\BlueTape.Services.DecisionEngine.BusinessLogic\\Models\\StepInputs\\DrawApproval\\NoSupplierBankAccountNumber.cs", "RelativeToolTip": "BlueTape.Services.DecisionEngine.BusinessLogic\\Models\\StepInputs\\DrawApproval\\NoSupplierBankAccountNumber.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-05-14T07:49:49.042Z"}, {"$type": "Document", "DocumentIndex": 22, "Title": "IInitializationStep.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\decision-engine\\src\\BlueTape.Services.DecisionEngine.BusinessLogic\\Abstractions\\Steps\\DrawApproval\\IInitializationStep.cs", "RelativeDocumentMoniker": "BlueTape.Services.DecisionEngine.BusinessLogic\\Abstractions\\Steps\\DrawApproval\\IInitializationStep.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\decision-engine\\src\\BlueTape.Services.DecisionEngine.BusinessLogic\\Abstractions\\Steps\\DrawApproval\\IInitializationStep.cs", "RelativeToolTip": "BlueTape.Services.DecisionEngine.BusinessLogic\\Abstractions\\Steps\\DrawApproval\\IInitializationStep.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAgAAAArAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-05-14T07:51:07.965Z"}, {"$type": "Document", "DocumentIndex": 21, "Title": "CreateDrawApproval.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\decision-engine\\src\\BlueTape.Services.DecisionEngine.Domain\\Models\\DrawApprovals\\CreateDrawApproval.cs", "RelativeDocumentMoniker": "BlueTape.Services.DecisionEngine.Domain\\Models\\DrawApprovals\\CreateDrawApproval.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\decision-engine\\src\\BlueTape.Services.DecisionEngine.Domain\\Models\\DrawApprovals\\CreateDrawApproval.cs", "RelativeToolTip": "BlueTape.Services.DecisionEngine.Domain\\Models\\DrawApprovals\\CreateDrawApproval.cs", "ViewState": "AgIAAAUAAAAAAAAAAAAQwA8AAAAqAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-05-14T07:51:51.833Z"}, {"$type": "Document", "DocumentIndex": 23, "Title": "DrawApprovalStepInputBase.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\decision-engine\\src\\BlueTape.Services.DecisionEngine.BusinessLogic\\Models\\StepInputs\\DrawApproval\\DrawApprovalStepInputBase.cs", "RelativeDocumentMoniker": "BlueTape.Services.DecisionEngine.BusinessLogic\\Models\\StepInputs\\DrawApproval\\DrawApprovalStepInputBase.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\decision-engine\\src\\BlueTape.Services.DecisionEngine.BusinessLogic\\Models\\StepInputs\\DrawApproval\\DrawApprovalStepInputBase.cs", "RelativeToolTip": "BlueTape.Services.DecisionEngine.BusinessLogic\\Models\\StepInputs\\DrawApproval\\DrawApprovalStepInputBase.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAoAAAABAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-05-14T07:49:53.002Z"}, {"$type": "Document", "DocumentIndex": 24, "Title": "InputPayableItem.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\decision-engine\\src\\BlueTape.Services.DecisionEngine.BusinessLogic\\Models\\StepInputs\\DrawApproval\\InputPayableItem.cs", "RelativeDocumentMoniker": "BlueTape.Services.DecisionEngine.BusinessLogic\\Models\\StepInputs\\DrawApproval\\InputPayableItem.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\decision-engine\\src\\BlueTape.Services.DecisionEngine.BusinessLogic\\Models\\StepInputs\\DrawApproval\\InputPayableItem.cs", "RelativeToolTip": "BlueTape.Services.DecisionEngine.BusinessLogic\\Models\\StepInputs\\DrawApproval\\InputPayableItem.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-05-14T07:49:50.793Z"}, {"$type": "Document", "DocumentIndex": 26, "Title": "NoSupplierBankDetails.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\decision-engine\\src\\BlueTape.Services.DecisionEngine.BusinessLogic\\Models\\StepInputs\\DrawApproval\\NoSupplierBankDetails.cs", "RelativeDocumentMoniker": "BlueTape.Services.DecisionEngine.BusinessLogic\\Models\\StepInputs\\DrawApproval\\NoSupplierBankDetails.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\decision-engine\\src\\BlueTape.Services.DecisionEngine.BusinessLogic\\Models\\StepInputs\\DrawApproval\\NoSupplierBankDetails.cs", "RelativeToolTip": "BlueTape.Services.DecisionEngine.BusinessLogic\\Models\\StepInputs\\DrawApproval\\NoSupplierBankDetails.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-05-14T07:49:49.614Z"}, {"$type": "Document", "DocumentIndex": 27, "Title": "NoSupplierAddress.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\decision-engine\\src\\BlueTape.Services.DecisionEngine.BusinessLogic\\Models\\StepInputs\\DrawApproval\\NoSupplierAddress.cs", "RelativeDocumentMoniker": "BlueTape.Services.DecisionEngine.BusinessLogic\\Models\\StepInputs\\DrawApproval\\NoSupplierAddress.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\decision-engine\\src\\BlueTape.Services.DecisionEngine.BusinessLogic\\Models\\StepInputs\\DrawApproval\\NoSupplierAddress.cs", "RelativeToolTip": "BlueTape.Services.DecisionEngine.BusinessLogic\\Models\\StepInputs\\DrawApproval\\NoSupplierAddress.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-05-14T07:49:48.284Z"}, {"$type": "Document", "DocumentIndex": 25, "Title": "NoSupplierDetails.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\decision-engine\\src\\BlueTape.Services.DecisionEngine.BusinessLogic\\Models\\StepInputs\\DrawApproval\\NoSupplierDetails.cs", "RelativeDocumentMoniker": "BlueTape.Services.DecisionEngine.BusinessLogic\\Models\\StepInputs\\DrawApproval\\NoSupplierDetails.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\decision-engine\\src\\BlueTape.Services.DecisionEngine.BusinessLogic\\Models\\StepInputs\\DrawApproval\\NoSupplierDetails.cs", "RelativeToolTip": "BlueTape.Services.DecisionEngine.BusinessLogic\\Models\\StepInputs\\DrawApproval\\NoSupplierDetails.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAIAAAANAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-05-14T07:49:14.815Z"}, {"$type": "Document", "DocumentIndex": 30, "Title": "ILoanService.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\decision-engine\\src\\BlueTape.Services.DecisionEngine.BusinessLogic\\Abstractions\\Services\\ILoanService.cs", "RelativeDocumentMoniker": "BlueTape.Services.DecisionEngine.BusinessLogic\\Abstractions\\Services\\ILoanService.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\decision-engine\\src\\BlueTape.Services.DecisionEngine.BusinessLogic\\Abstractions\\Services\\ILoanService.cs", "RelativeToolTip": "BlueTape.Services.DecisionEngine.BusinessLogic\\Abstractions\\Services\\ILoanService.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAoAAABMAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2024-06-12T15:49:21.32Z"}, {"$type": "Document", "DocumentIndex": 29, "Title": "BlueTape.Services.DecisionEngine.BusinessLogic.Tests.csproj", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\decision-engine\\src\\BlueTape.Services.DecisionEngine.BusinessLogic.Tests\\BlueTape.Services.DecisionEngine.BusinessLogic.Tests.csproj", "RelativeDocumentMoniker": "BlueTape.Services.DecisionEngine.BusinessLogic.Tests\\BlueTape.Services.DecisionEngine.BusinessLogic.Tests.csproj", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\decision-engine\\src\\BlueTape.Services.DecisionEngine.BusinessLogic.Tests\\BlueTape.Services.DecisionEngine.BusinessLogic.Tests.csproj", "RelativeToolTip": "BlueTape.Services.DecisionEngine.BusinessLogic.Tests\\BlueTape.Services.DecisionEngine.BusinessLogic.Tests.csproj", "ViewState": "AgIAAAAAAAAAAAAAAAAAAB8AAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000758|", "WhenOpened": "2025-03-21T12:11:23.54Z"}, {"$type": "Document", "DocumentIndex": 28, "Title": "Function.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\decision-engine\\src\\lambdas\\DrawApproval\\BlueTape.Services.DrawApproval.PreliminaryStep\\Function.cs", "RelativeDocumentMoniker": "lambdas\\DrawApproval\\BlueTape.Services.DrawApproval.PreliminaryStep\\Function.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\decision-engine\\src\\lambdas\\DrawApproval\\BlueTape.Services.DrawApproval.PreliminaryStep\\Function.cs", "RelativeToolTip": "lambdas\\DrawApproval\\BlueTape.Services.DrawApproval.PreliminaryStep\\Function.cs", "ViewState": "AgIAABAAAAAAAAAAAAAUwBQAAABDAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-03-20T06:56:10.832Z"}, {"$type": "Document", "DocumentIndex": 32, "Title": "ICashFlowReviewStep.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\decision-engine\\src\\BlueTape.Services.DecisionEngine.BusinessLogic\\Abstractions\\Steps\\DecisionEngine\\ICashFlowReviewStep.cs", "RelativeDocumentMoniker": "BlueTape.Services.DecisionEngine.BusinessLogic\\Abstractions\\Steps\\DecisionEngine\\ICashFlowReviewStep.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\decision-engine\\src\\BlueTape.Services.DecisionEngine.BusinessLogic\\Abstractions\\Steps\\DecisionEngine\\ICashFlowReviewStep.cs", "RelativeToolTip": "BlueTape.Services.DecisionEngine.BusinessLogic\\Abstractions\\Steps\\DecisionEngine\\ICashFlowReviewStep.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAqwAYAAAAZAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-04-23T13:43:47.749Z"}, {"$type": "Document", "DocumentIndex": 31, "Title": "Function.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\decision-engine\\src\\lambdas\\DecisionEngine\\BlueTape.Services.CashFlowReviewStep\\Function.cs", "RelativeDocumentMoniker": "lambdas\\DecisionEngine\\BlueTape.Services.CashFlowReviewStep\\Function.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\decision-engine\\src\\lambdas\\DecisionEngine\\BlueTape.Services.CashFlowReviewStep\\Function.cs", "RelativeToolTip": "lambdas\\DecisionEngine\\BlueTape.Services.CashFlowReviewStep\\Function.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAABcAAAAxAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-04-23T13:44:59.794Z"}, {"$type": "Document", "DocumentIndex": 33, "Title": "AccountStatusConstants.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\decision-engine\\src\\BlueTape.Services.DecisionEngine.BusinessLogic\\Constants\\AccountStatusConstants.cs", "RelativeDocumentMoniker": "BlueTape.Services.DecisionEngine.BusinessLogic\\Constants\\AccountStatusConstants.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\decision-engine\\src\\BlueTape.Services.DecisionEngine.BusinessLogic\\Constants\\AccountStatusConstants.cs", "RelativeToolTip": "BlueTape.Services.DecisionEngine.BusinessLogic\\Constants\\AccountStatusConstants.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAkAAAAnAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-04-23T13:43:51.542Z"}, {"$type": "Document", "DocumentIndex": 36, "Title": "EmailNotificationServiceTests.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\decision-engine\\src\\BlueTape.Services.DecisionEngine.BusinessLogic.Tests\\Services\\EmailNotificationServiceTests.cs", "RelativeDocumentMoniker": "BlueTape.Services.DecisionEngine.BusinessLogic.Tests\\Services\\EmailNotificationServiceTests.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\decision-engine\\src\\BlueTape.Services.DecisionEngine.BusinessLogic.Tests\\Services\\EmailNotificationServiceTests.cs", "RelativeToolTip": "BlueTape.Services.DecisionEngine.BusinessLogic.Tests\\Services\\EmailNotificationServiceTests.cs", "ViewState": "AgIAAEoAAAAAAAAAAAAcwGIAAAAYAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-03-21T12:10:04.884Z"}, {"$type": "Document", "DocumentIndex": 35, "Title": "CompanyModel.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\decision-engine\\src\\BlueTape.Services.DecisionEngine.BusinessLogic\\Models\\Company\\CompanyModel.cs", "RelativeDocumentMoniker": "BlueTape.Services.DecisionEngine.BusinessLogic\\Models\\Company\\CompanyModel.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\decision-engine\\src\\BlueTape.Services.DecisionEngine.BusinessLogic\\Models\\Company\\CompanyModel.cs", "RelativeToolTip": "BlueTape.Services.DecisionEngine.BusinessLogic\\Models\\Company\\CompanyModel.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAwAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-04-11T07:35:32.428Z"}, {"$type": "Document", "DocumentIndex": 34, "Title": "BviResult.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\decision-engine\\src\\BlueTape.Services.DecisionEngine.BusinessLogic\\Models\\BVI\\BviResult.cs", "RelativeDocumentMoniker": "BlueTape.Services.DecisionEngine.BusinessLogic\\Models\\BVI\\BviResult.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\decision-engine\\src\\BlueTape.Services.DecisionEngine.BusinessLogic\\Models\\BVI\\BviResult.cs", "RelativeToolTip": "BlueTape.Services.DecisionEngine.BusinessLogic\\Models\\BVI\\BviResult.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAYAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-04-11T07:36:52.097Z"}, {"$type": "Document", "DocumentIndex": 39, "Title": "StepHandlingService.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\decision-engine\\src\\BlueTape.Services.DecisionEngine.BusinessLogic\\Services\\StepHandlingService.cs", "RelativeDocumentMoniker": "BlueTape.Services.DecisionEngine.BusinessLogic\\Services\\StepHandlingService.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\decision-engine\\src\\BlueTape.Services.DecisionEngine.BusinessLogic\\Services\\StepHandlingService.cs", "RelativeToolTip": "BlueTape.Services.DecisionEngine.BusinessLogic\\Services\\StepHandlingService.cs", "ViewState": "AgIAAJQAAAAAAAAAAAAswKIAAAA1AAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-03-20T06:55:41.638Z"}, {"$type": "Document", "DocumentIndex": 38, "Title": "DrawApprovalGenericStep.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\decision-engine\\src\\BlueTape.Services.DecisionEngine.BusinessLogic\\Steps\\DrawApproval\\DrawApprovalGenericStep.cs", "RelativeDocumentMoniker": "BlueTape.Services.DecisionEngine.BusinessLogic\\Steps\\DrawApproval\\DrawApprovalGenericStep.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\decision-engine\\src\\BlueTape.Services.DecisionEngine.BusinessLogic\\Steps\\DrawApproval\\DrawApprovalGenericStep.cs", "RelativeToolTip": "BlueTape.Services.DecisionEngine.BusinessLogic\\Steps\\DrawApproval\\DrawApprovalGenericStep.cs", "ViewState": "AgIAAF8AAAAAAAAAAAAkwHAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2024-12-11T15:56:41.331Z"}, {"$type": "Document", "DocumentIndex": 37, "Title": "InternalReviewStep.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\decision-engine\\src\\BlueTape.Services.DecisionEngine.BusinessLogic\\Steps\\DrawApproval\\InternalReviewStep.cs", "RelativeDocumentMoniker": "BlueTape.Services.DecisionEngine.BusinessLogic\\Steps\\DrawApproval\\InternalReviewStep.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\decision-engine\\src\\BlueTape.Services.DecisionEngine.BusinessLogic\\Steps\\DrawApproval\\InternalReviewStep.cs", "RelativeToolTip": "BlueTape.Services.DecisionEngine.BusinessLogic\\Steps\\DrawApproval\\InternalReviewStep.cs", "ViewState": "AgIAAG4AAAAAAAAAAADwv3YAAAAfAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2024-06-13T08:19:27.08Z"}, {"$type": "Document", "DocumentIndex": 40, "Title": "PreliminaryStepTests.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\decision-engine\\src\\BlueTape.Services.DecisionEngine.BusinessLogic.Tests\\Steps\\DrawApproval\\PreliminaryStepTests.cs", "RelativeDocumentMoniker": "BlueTape.Services.DecisionEngine.BusinessLogic.Tests\\Steps\\DrawApproval\\PreliminaryStepTests.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\decision-engine\\src\\BlueTape.Services.DecisionEngine.BusinessLogic.Tests\\Steps\\DrawApproval\\PreliminaryStepTests.cs", "RelativeToolTip": "BlueTape.Services.DecisionEngine.BusinessLogic.Tests\\Steps\\DrawApproval\\PreliminaryStepTests.cs", "ViewState": "AgIAACoBAAAAAAAAAIAwwDEBAAAIAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2024-10-04T13:05:04.27Z"}, {"$type": "Document", "DocumentIndex": 41, "Title": "LexisNexisIntegrationServiceHttpClient.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\decision-engine\\src\\BlueTape.Services.DecisionEngine.DataAccess.External\\HttpClients\\LexisNexisIntegrationServiceHttpClient.cs", "RelativeDocumentMoniker": "BlueTape.Services.DecisionEngine.DataAccess.External\\HttpClients\\LexisNexisIntegrationServiceHttpClient.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\decision-engine\\src\\BlueTape.Services.DecisionEngine.DataAccess.External\\HttpClients\\LexisNexisIntegrationServiceHttpClient.cs", "RelativeToolTip": "BlueTape.Services.DecisionEngine.DataAccess.External\\HttpClients\\LexisNexisIntegrationServiceHttpClient.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAABYAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2024-06-24T14:42:38.483Z"}, {"$type": "Document", "DocumentIndex": 42, "Title": "PlaidIntegrationServiceHttpClient.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\decision-engine\\src\\BlueTape.Services.DecisionEngine.DataAccess.External\\HttpClients\\PlaidIntegrationServiceHttpClient.cs", "RelativeDocumentMoniker": "BlueTape.Services.DecisionEngine.DataAccess.External\\HttpClients\\PlaidIntegrationServiceHttpClient.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\decision-engine\\src\\BlueTape.Services.DecisionEngine.DataAccess.External\\HttpClients\\PlaidIntegrationServiceHttpClient.cs", "RelativeToolTip": "BlueTape.Services.DecisionEngine.DataAccess.External\\HttpClients\\PlaidIntegrationServiceHttpClient.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-03-21T10:33:35.953Z"}, {"$type": "Document", "DocumentIndex": 43, "Title": "IDrawApprovalStepImplementationBase.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\decision-engine\\src\\BlueTape.Services.DecisionEngine.BusinessLogic\\Abstractions\\Steps\\Base\\IDrawApprovalStepImplementationBase.cs", "RelativeDocumentMoniker": "BlueTape.Services.DecisionEngine.BusinessLogic\\Abstractions\\Steps\\Base\\IDrawApprovalStepImplementationBase.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\decision-engine\\src\\BlueTape.Services.DecisionEngine.BusinessLogic\\Abstractions\\Steps\\Base\\IDrawApprovalStepImplementationBase.cs", "RelativeToolTip": "BlueTape.Services.DecisionEngine.BusinessLogic\\Abstractions\\Steps\\Base\\IDrawApprovalStepImplementationBase.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAQAAAAUAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-03-21T06:37:54.902Z"}, {"$type": "Document", "DocumentIndex": 45, "Title": "AutomatedApprovalService.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\decision-engine\\src\\BlueTape.Services.DecisionEngine.BusinessLogic\\Services\\AutomatedApprovalService.cs", "RelativeDocumentMoniker": "BlueTape.Services.DecisionEngine.BusinessLogic\\Services\\AutomatedApprovalService.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\decision-engine\\src\\BlueTape.Services.DecisionEngine.BusinessLogic\\Services\\AutomatedApprovalService.cs", "RelativeToolTip": "BlueTape.Services.DecisionEngine.BusinessLogic\\Services\\AutomatedApprovalService.cs", "ViewState": "AgIAAHMAAAAAAAAAAAAqwIgAAAAeAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2024-12-11T15:01:54.006Z"}, {"$type": "Document", "DocumentIndex": 46, "Title": "InvoiceService.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\decision-engine\\src\\BlueTape.Services.DecisionEngine.BusinessLogic\\Services\\InvoiceService.cs", "RelativeDocumentMoniker": "BlueTape.Services.DecisionEngine.BusinessLogic\\Services\\InvoiceService.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\decision-engine\\src\\BlueTape.Services.DecisionEngine.BusinessLogic\\Services\\InvoiceService.cs", "RelativeToolTip": "BlueTape.Services.DecisionEngine.BusinessLogic\\Services\\InvoiceService.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-03-20T06:58:12.372Z"}, {"$type": "Document", "DocumentIndex": 48, "Title": "EmailNotificationService.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\decision-engine\\src\\BlueTape.Services.DecisionEngine.BusinessLogic\\Services\\EmailNotificationService.cs", "RelativeDocumentMoniker": "BlueTape.Services.DecisionEngine.BusinessLogic\\Services\\EmailNotificationService.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\decision-engine\\src\\BlueTape.Services.DecisionEngine.BusinessLogic\\Services\\EmailNotificationService.cs", "RelativeToolTip": "BlueTape.Services.DecisionEngine.BusinessLogic\\Services\\EmailNotificationService.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-03-20T06:58:09.061Z"}, {"$type": "Document", "DocumentIndex": 49, "Title": "ConnectorNotificationService.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\decision-engine\\src\\BlueTape.Services.DecisionEngine.BusinessLogic\\Services\\ConnectorNotificationService.cs", "RelativeDocumentMoniker": "BlueTape.Services.DecisionEngine.BusinessLogic\\Services\\ConnectorNotificationService.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\decision-engine\\src\\BlueTape.Services.DecisionEngine.BusinessLogic\\Services\\ConnectorNotificationService.cs", "RelativeToolTip": "BlueTape.Services.DecisionEngine.BusinessLogic\\Services\\ConnectorNotificationService.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-03-20T06:58:08.017Z"}, {"$type": "Document", "DocumentIndex": 44, "Title": "AccountStatusChangeService.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\decision-engine\\src\\BlueTape.Services.DecisionEngine.BusinessLogic\\Services\\AccountStatusChangeService.cs", "RelativeDocumentMoniker": "BlueTape.Services.DecisionEngine.BusinessLogic\\Services\\AccountStatusChangeService.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\decision-engine\\src\\BlueTape.Services.DecisionEngine.BusinessLogic\\Services\\AccountStatusChangeService.cs", "RelativeToolTip": "BlueTape.Services.DecisionEngine.BusinessLogic\\Services\\AccountStatusChangeService.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAqwBAAAAAFAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-01-13T12:12:38.907Z"}, {"$type": "Document", "DocumentIndex": 47, "Title": "ExperianService.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\decision-engine\\src\\BlueTape.Services.DecisionEngine.BusinessLogic\\Services\\ExperianService.cs", "RelativeDocumentMoniker": "BlueTape.Services.DecisionEngine.BusinessLogic\\Services\\ExperianService.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\decision-engine\\src\\BlueTape.Services.DecisionEngine.BusinessLogic\\Services\\ExperianService.cs", "RelativeToolTip": "BlueTape.Services.DecisionEngine.BusinessLogic\\Services\\ExperianService.cs", "ViewState": "AgIAAAoAAAAAAAAAAAAswBQAAAABAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2024-06-25T07:39:21.449Z"}, {"$type": "Document", "DocumentIndex": 51, "Title": "appsettings.prod.json", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\decision-engine\\src\\lambdas\\DrawApproval\\BlueTape.Services.DrawApproval.PreliminaryStep\\appsettings.prod.json", "RelativeDocumentMoniker": "lambdas\\DrawApproval\\BlueTape.Services.DrawApproval.PreliminaryStep\\appsettings.prod.json", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\decision-engine\\src\\lambdas\\DrawApproval\\BlueTape.Services.DrawApproval.PreliminaryStep\\appsettings.prod.json", "RelativeToolTip": "lambdas\\DrawApproval\\BlueTape.Services.DrawApproval.PreliminaryStep\\appsettings.prod.json", "ViewState": "AgIAAAAAAAAAAAAAAAAWwBAAAAAhAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.001642|", "WhenOpened": "2025-03-13T14:51:44.999Z"}, {"$type": "Document", "DocumentIndex": 50, "Title": "CompanyService.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\decision-engine\\src\\BlueTape.Services.DecisionEngine.BusinessLogic\\Services\\CompanyService.cs", "RelativeDocumentMoniker": "BlueTape.Services.DecisionEngine.BusinessLogic\\Services\\CompanyService.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\decision-engine\\src\\BlueTape.Services.DecisionEngine.BusinessLogic\\Services\\CompanyService.cs", "RelativeToolTip": "BlueTape.Services.DecisionEngine.BusinessLogic\\Services\\CompanyService.cs", "ViewState": "AgIAAA0AAAAAAAAAAAAvwCEAAABgAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2024-05-30T12:09:00.703Z"}, {"$type": "Document", "DocumentIndex": 52, "Title": "Function.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\decision-engine\\src\\lambdas\\DecisionEngine\\BlueTape.Services.CashFlowReportStep\\Function.cs", "RelativeDocumentMoniker": "lambdas\\DecisionEngine\\BlueTape.Services.CashFlowReportStep\\Function.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\decision-engine\\src\\lambdas\\DecisionEngine\\BlueTape.Services.CashFlowReportStep\\Function.cs", "RelativeToolTip": "lambdas\\DecisionEngine\\BlueTape.Services.CashFlowReportStep\\Function.cs", "ViewState": "AgIAAA4AAAAAAAAAAAAYwBkAAAAxAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2024-06-13T07:17:17.507Z"}, {"$type": "Document", "DocumentIndex": 54, "Title": "Function.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\decision-engine\\src\\lambdas\\DecisionEngine\\BlueTape.Services.DecisionEngine.InitializationStep\\Function.cs", "RelativeDocumentMoniker": "lambdas\\DecisionEngine\\BlueTape.Services.DecisionEngine.InitializationStep\\Function.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\decision-engine\\src\\lambdas\\DecisionEngine\\BlueTape.Services.DecisionEngine.InitializationStep\\Function.cs", "RelativeToolTip": "lambdas\\DecisionEngine\\BlueTape.Services.DecisionEngine.InitializationStep\\Function.cs", "ViewState": "AgIAABAAAAAAAAAAAAA5wBIAAAA1AAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2024-05-28T08:55:51.845Z"}, {"$type": "Document", "DocumentIndex": 53, "Title": "appsettings.Development.json", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\decision-engine\\src\\lambdas\\DrawApproval\\BlueTape.Services.DrawApproval.PreliminaryStep\\appsettings.Development.json", "RelativeDocumentMoniker": "lambdas\\DrawApproval\\BlueTape.Services.DrawApproval.PreliminaryStep\\appsettings.Development.json", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\decision-engine\\src\\lambdas\\DrawApproval\\BlueTape.Services.DrawApproval.PreliminaryStep\\appsettings.Development.json", "RelativeToolTip": "lambdas\\DrawApproval\\BlueTape.Services.DrawApproval.PreliminaryStep\\appsettings.Development.json", "ViewState": "AgIAAA0AAAAAAAAAAADwvxoAAAAfAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.001642|", "WhenOpened": "2024-05-29T10:08:45.59Z"}, {"$type": "Document", "DocumentIndex": 60, "Title": "ExperianConstants.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\decision-engine\\src\\BlueTape.Services.DecisionEngine.BusinessLogic\\Constants\\ExperianConstants.cs", "RelativeDocumentMoniker": "BlueTape.Services.DecisionEngine.BusinessLogic\\Constants\\ExperianConstants.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\decision-engine\\src\\BlueTape.Services.DecisionEngine.BusinessLogic\\Constants\\ExperianConstants.cs", "RelativeToolTip": "BlueTape.Services.DecisionEngine.BusinessLogic\\Constants\\ExperianConstants.cs", "ViewState": "AgIAAAoAAAAAAAAAAAAYwBMAAAAYAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-01-28T15:48:21.965Z"}, {"$type": "Document", "DocumentIndex": 55, "Title": "InitializationStep.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\decision-engine\\src\\BlueTape.Services.DecisionEngine.BusinessLogic\\Steps\\DecisionEngine\\InitializationStep.cs", "RelativeDocumentMoniker": "BlueTape.Services.DecisionEngine.BusinessLogic\\Steps\\DecisionEngine\\InitializationStep.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\decision-engine\\src\\BlueTape.Services.DecisionEngine.BusinessLogic\\Steps\\DecisionEngine\\InitializationStep.cs", "RelativeToolTip": "BlueTape.Services.DecisionEngine.BusinessLogic\\Steps\\DecisionEngine\\InitializationStep.cs", "ViewState": "AgIAAF8AAAAAAAAAAAAewHIAAABQAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2024-05-28T08:56:45.87Z"}, {"$type": "Document", "DocumentIndex": 56, "Title": "appsettings.Development.json", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\decision-engine\\src\\BlueTape.Services.DecisionEngine\\appsettings.Development.json", "RelativeDocumentMoniker": "BlueTape.Services.DecisionEngine\\appsettings.Development.json", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\decision-engine\\src\\BlueTape.Services.DecisionEngine\\appsettings.Development.json", "RelativeToolTip": "BlueTape.Services.DecisionEngine\\appsettings.Development.json", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.001642|", "WhenOpened": "2024-12-11T16:19:25.803Z"}, {"$type": "Document", "DocumentIndex": 58, "Title": "aws-lambda-tools-defaults.json", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\decision-engine\\src\\lambdas\\DrawApproval\\BlueTape.Services.DrawApproval.InitializationStep\\aws-lambda-tools-defaults.json", "RelativeDocumentMoniker": "lambdas\\DrawApproval\\BlueTape.Services.DrawApproval.InitializationStep\\aws-lambda-tools-defaults.json", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\decision-engine\\src\\lambdas\\DrawApproval\\BlueTape.Services.DrawApproval.InitializationStep\\aws-lambda-tools-defaults.json", "RelativeToolTip": "lambdas\\DrawApproval\\BlueTape.Services.DrawApproval.InitializationStep\\aws-lambda-tools-defaults.json", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.001642|", "WhenOpened": "2025-02-06T15:43:15.458Z"}, {"$type": "Document", "DocumentIndex": 59, "Title": "BlueTape.Services.DrawApproval.InitializationStep.csproj", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\decision-engine\\src\\lambdas\\DrawApproval\\BlueTape.Services.DrawApproval.InitializationStep\\BlueTape.Services.DrawApproval.InitializationStep.csproj", "RelativeDocumentMoniker": "lambdas\\DrawApproval\\BlueTape.Services.DrawApproval.InitializationStep\\BlueTape.Services.DrawApproval.InitializationStep.csproj", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\decision-engine\\src\\lambdas\\DrawApproval\\BlueTape.Services.DrawApproval.InitializationStep\\BlueTape.Services.DrawApproval.InitializationStep.csproj", "RelativeToolTip": "lambdas\\DrawApproval\\BlueTape.Services.DrawApproval.InitializationStep\\BlueTape.Services.DrawApproval.InitializationStep.csproj", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000758|", "WhenOpened": "2024-06-11T12:45:11.807Z"}, {"$type": "Document", "DocumentIndex": 57, "Title": "appsettings.json", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\decision-engine\\src\\BlueTape.Services.DecisionEngine\\appsettings.json", "RelativeDocumentMoniker": "BlueTape.Services.DecisionEngine\\appsettings.json", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\decision-engine\\src\\BlueTape.Services.DecisionEngine\\appsettings.json", "RelativeToolTip": "BlueTape.Services.DecisionEngine\\appsettings.json", "ViewState": "AgIAAGsAAAAAAAAAAABRwC8AAAAJAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.001642|", "WhenOpened": "2024-05-29T10:08:46.585Z"}, {"$type": "Document", "DocumentIndex": 61, "Title": "OnBoardingServiceHttpClient.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\decision-engine\\src\\BlueTape.Services.DecisionEngine.DataAccess.External\\HttpClients\\OnBoardingServiceHttpClient.cs", "RelativeDocumentMoniker": "BlueTape.Services.DecisionEngine.DataAccess.External\\HttpClients\\OnBoardingServiceHttpClient.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\decision-engine\\src\\BlueTape.Services.DecisionEngine.DataAccess.External\\HttpClients\\OnBoardingServiceHttpClient.cs", "RelativeToolTip": "BlueTape.Services.DecisionEngine.DataAccess.External\\HttpClients\\OnBoardingServiceHttpClient.cs", "ViewState": "AgIAAAIAAAAAAAAAAAAjwAcAAAA6AAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2024-06-10T10:36:32.085Z"}, {"$type": "Document", "DocumentIndex": 65, "Title": "appsettings.json", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\decision-engine\\src\\lambdas\\DrawApproval\\BlueTape.Services.DrawApproval.RiskAssessmentStep\\appsettings.json", "RelativeDocumentMoniker": "lambdas\\DrawApproval\\BlueTape.Services.DrawApproval.RiskAssessmentStep\\appsettings.json", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\decision-engine\\src\\lambdas\\DrawApproval\\BlueTape.Services.DrawApproval.RiskAssessmentStep\\appsettings.json", "RelativeToolTip": "lambdas\\DrawApproval\\BlueTape.Services.DrawApproval.RiskAssessmentStep\\appsettings.json", "ViewState": "AgIAAAAAAAAAAAAAAIBJwBgAAAAbAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.001642|", "WhenOpened": "2024-12-11T16:19:39.936Z"}, {"$type": "Document", "DocumentIndex": 62, "Title": "RetrieveJudgmentsData.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\decision-engine\\src\\BlueTape.Services.DecisionEngine.BusinessLogic\\Infrastructure\\RetrieveDataDynamicallyStrategies\\Experian\\RetrieveJudgmentsData.cs", "RelativeDocumentMoniker": "BlueTape.Services.DecisionEngine.BusinessLogic\\Infrastructure\\RetrieveDataDynamicallyStrategies\\Experian\\RetrieveJudgmentsData.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\decision-engine\\src\\BlueTape.Services.DecisionEngine.BusinessLogic\\Infrastructure\\RetrieveDataDynamicallyStrategies\\Experian\\RetrieveJudgmentsData.cs", "RelativeToolTip": "BlueTape.Services.DecisionEngine.BusinessLogic\\Infrastructure\\RetrieveDataDynamicallyStrategies\\Experian\\RetrieveJudgmentsData.cs", "ViewState": "AgIAACYAAAAAAAAAAADwvzcAAAAFAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2024-06-25T07:39:34.779Z"}, {"$type": "Document", "DocumentIndex": 63, "Title": "RiskAssessmentStep.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\decision-engine\\src\\BlueTape.Services.DecisionEngine.BusinessLogic\\Steps\\DrawApproval\\RiskAssessmentStep.cs", "RelativeDocumentMoniker": "BlueTape.Services.DecisionEngine.BusinessLogic\\Steps\\DrawApproval\\RiskAssessmentStep.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\decision-engine\\src\\BlueTape.Services.DecisionEngine.BusinessLogic\\Steps\\DrawApproval\\RiskAssessmentStep.cs", "RelativeToolTip": "BlueTape.Services.DecisionEngine.BusinessLogic\\Steps\\DrawApproval\\RiskAssessmentStep.cs", "ViewState": "AgIAAJsAAAAAAAAAAAAwwLgAAAAsAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2024-06-18T14:09:13.811Z"}, {"$type": "Document", "DocumentIndex": 66, "Title": "Function.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\decision-engine\\src\\lambdas\\DrawApproval\\BlueTape.Services.DrawApproval.RiskAssessmentStep\\Function.cs", "RelativeDocumentMoniker": "lambdas\\DrawApproval\\BlueTape.Services.DrawApproval.RiskAssessmentStep\\Function.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\decision-engine\\src\\lambdas\\DrawApproval\\BlueTape.Services.DrawApproval.RiskAssessmentStep\\Function.cs", "RelativeToolTip": "lambdas\\DrawApproval\\BlueTape.Services.DrawApproval.RiskAssessmentStep\\Function.cs", "ViewState": "AgIAAAAAAAAAAAAAAIBJwBgAAAAzAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2024-06-18T14:09:03.016Z"}, {"$type": "Document", "DocumentIndex": 64, "Title": "appsettings.beta.json", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\decision-engine\\src\\lambdas\\DrawApproval\\BlueTape.Services.DrawApproval.RiskAssessmentStep\\appsettings.beta.json", "RelativeDocumentMoniker": "lambdas\\DrawApproval\\BlueTape.Services.DrawApproval.RiskAssessmentStep\\appsettings.beta.json", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\decision-engine\\src\\lambdas\\DrawApproval\\BlueTape.Services.DrawApproval.RiskAssessmentStep\\appsettings.beta.json", "RelativeToolTip": "lambdas\\DrawApproval\\BlueTape.Services.DrawApproval.RiskAssessmentStep\\appsettings.beta.json", "ViewState": "AgIAAAAAAAAAAAAAAAAAABQAAAAlAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.001642|", "WhenOpened": "2024-12-11T16:23:37.873Z"}, {"$type": "Document", "DocumentIndex": 67, "Title": "launchSettings.json", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\decision-engine\\src\\BlueTape.Services.DecisionEngine\\Properties\\launchSettings.json", "RelativeDocumentMoniker": "BlueTape.Services.DecisionEngine\\Properties\\launchSettings.json", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\decision-engine\\src\\BlueTape.Services.DecisionEngine\\Properties\\launchSettings.json", "RelativeToolTip": "BlueTape.Services.DecisionEngine\\Properties\\launchSettings.json", "ViewState": "AgIAAAAAAAAAAAAAAAAAAA0AAAAgAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.001642|", "WhenOpened": "2024-12-11T15:25:39.048Z"}, {"$type": "Document", "DocumentIndex": 68, "Title": "LoggerConstants.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\decision-engine\\src\\BlueTape.Services.DecisionEngine\\Constants\\LoggerConstants.cs", "RelativeDocumentMoniker": "BlueTape.Services.DecisionEngine\\Constants\\LoggerConstants.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\decision-engine\\src\\BlueTape.Services.DecisionEngine\\Constants\\LoggerConstants.cs", "RelativeToolTip": "BlueTape.Services.DecisionEngine\\Constants\\LoggerConstants.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2024-12-11T15:26:35.047Z"}, {"$type": "Document", "DocumentIndex": 69, "Title": "Program.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\decision-engine\\src\\BlueTape.Services.DecisionEngine\\Program.cs", "RelativeDocumentMoniker": "BlueTape.Services.DecisionEngine\\Program.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\decision-engine\\src\\BlueTape.Services.DecisionEngine\\Program.cs", "RelativeToolTip": "BlueTape.Services.DecisionEngine\\Program.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2024-12-11T15:26:33.627Z"}, {"$type": "Document", "DocumentIndex": 70, "Title": "DrawApprovalTypeExtensions.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\decision-engine\\src\\BlueTape.Services.DecisionEngine.BusinessLogic\\Extensions\\DrawApprovalTypeExtensions.cs", "RelativeDocumentMoniker": "BlueTape.Services.DecisionEngine.BusinessLogic\\Extensions\\DrawApprovalTypeExtensions.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\decision-engine\\src\\BlueTape.Services.DecisionEngine.BusinessLogic\\Extensions\\DrawApprovalTypeExtensions.cs", "RelativeToolTip": "BlueTape.Services.DecisionEngine.BusinessLogic\\Extensions\\DrawApprovalTypeExtensions.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2024-12-11T14:58:56.045Z"}, {"$type": "Document", "DocumentIndex": 71, "Title": "StatusNoteExtensions.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\decision-engine\\src\\BlueTape.Services.DecisionEngine.BusinessLogic\\Extensions\\StatusNoteExtensions.cs", "RelativeDocumentMoniker": "BlueTape.Services.DecisionEngine.BusinessLogic\\Extensions\\StatusNoteExtensions.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\decision-engine\\src\\BlueTape.Services.DecisionEngine.BusinessLogic\\Extensions\\StatusNoteExtensions.cs", "RelativeToolTip": "BlueTape.Services.DecisionEngine.BusinessLogic\\Extensions\\StatusNoteExtensions.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2024-12-11T14:58:55.42Z"}, {"$type": "Document", "DocumentIndex": 72, "Title": "CreditApplicationTypeExtensions.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\decision-engine\\src\\BlueTape.Services.DecisionEngine.BusinessLogic\\Extensions\\CreditApplicationTypeExtensions.cs", "RelativeDocumentMoniker": "BlueTape.Services.DecisionEngine.BusinessLogic\\Extensions\\CreditApplicationTypeExtensions.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\decision-engine\\src\\BlueTape.Services.DecisionEngine.BusinessLogic\\Extensions\\CreditApplicationTypeExtensions.cs", "RelativeToolTip": "BlueTape.Services.DecisionEngine.BusinessLogic\\Extensions\\CreditApplicationTypeExtensions.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAA8AAAA6AAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2024-11-13T10:46:12.347Z"}, {"$type": "Document", "DocumentIndex": 73, "Title": "InHouseCreditStatus.cs", "DocumentMoniker": "C:\\Users\\<USER>\\AppData\\Local\\JetBrains\\Shared\\vAny\\DecompilerCache\\decompiler\\e8721e7d2fb14803b749d9015294b54d1b200\\1c\\5ec765de\\InHouseCreditStatus.cs", "RelativeDocumentMoniker": "..\\..\\..\\..\\..\\AppData\\Local\\JetBrains\\Shared\\vAny\\DecompilerCache\\decompiler\\e8721e7d2fb14803b749d9015294b54d1b200\\1c\\5ec765de\\InHouseCreditStatus.cs", "ToolTip": "C:\\Users\\<USER>\\AppData\\Local\\JetBrains\\Shared\\vAny\\DecompilerCache\\decompiler\\e8721e7d2fb14803b749d9015294b54d1b200\\1c\\5ec765de\\InHouseCreditStatus.cs", "RelativeToolTip": "..\\..\\..\\..\\..\\AppData\\Local\\JetBrains\\Shared\\vAny\\DecompilerCache\\decompiler\\e8721e7d2fb14803b749d9015294b54d1b200\\1c\\5ec765de\\InHouseCreditStatus.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAABQAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2024-10-04T13:06:25.243Z"}, {"$type": "Document", "DocumentIndex": 74, "Title": "RetrieveBankruptciesData.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\decision-engine\\src\\BlueTape.Services.DecisionEngine.BusinessLogic\\Infrastructure\\RetrieveDataDynamicallyStrategies\\Experian\\RetrieveBankruptciesData.cs", "RelativeDocumentMoniker": "BlueTape.Services.DecisionEngine.BusinessLogic\\Infrastructure\\RetrieveDataDynamicallyStrategies\\Experian\\RetrieveBankruptciesData.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\decision-engine\\src\\BlueTape.Services.DecisionEngine.BusinessLogic\\Infrastructure\\RetrieveDataDynamicallyStrategies\\Experian\\RetrieveBankruptciesData.cs", "RelativeToolTip": "BlueTape.Services.DecisionEngine.BusinessLogic\\Infrastructure\\RetrieveDataDynamicallyStrategies\\Experian\\RetrieveBankruptciesData.cs", "ViewState": "AgIAADQAAAAAAAAAAADgv0cAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2024-06-25T07:39:28.837Z"}, {"$type": "Document", "DocumentIndex": 75, "Title": "DrawApprovalTestData.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\decision-engine\\src\\BlueTape.Services.DecisionEngine.BusinessLogic.Tests\\TestData\\DrawApprovalTestData.cs", "RelativeDocumentMoniker": "BlueTape.Services.DecisionEngine.BusinessLogic.Tests\\TestData\\DrawApprovalTestData.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\decision-engine\\src\\BlueTape.Services.DecisionEngine.BusinessLogic.Tests\\TestData\\DrawApprovalTestData.cs", "RelativeToolTip": "BlueTape.Services.DecisionEngine.BusinessLogic.Tests\\TestData\\DrawApprovalTestData.cs", "ViewState": "AgIAAAcBAAAAAAAAAAAqwBkBAAAiAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2024-10-04T13:05:15.084Z"}, {"$type": "Document", "DocumentIndex": 77, "Title": "RetrieveCreditStatusData.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\decision-engine\\src\\BlueTape.Services.DecisionEngine.BusinessLogic\\Infrastructure\\RetrieveDataDynamicallyStrategies\\Experian\\RetrieveCreditStatusData.cs", "RelativeDocumentMoniker": "BlueTape.Services.DecisionEngine.BusinessLogic\\Infrastructure\\RetrieveDataDynamicallyStrategies\\Experian\\RetrieveCreditStatusData.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\decision-engine\\src\\BlueTape.Services.DecisionEngine.BusinessLogic\\Infrastructure\\RetrieveDataDynamicallyStrategies\\Experian\\RetrieveCreditStatusData.cs", "RelativeToolTip": "BlueTape.Services.DecisionEngine.BusinessLogic\\Infrastructure\\RetrieveDataDynamicallyStrategies\\Experian\\RetrieveCreditStatusData.cs", "ViewState": "AgIAABQAAAAAAAAAAAAIwBYAAAABAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2024-06-25T07:39:37.297Z"}, {"$type": "Document", "DocumentIndex": 76, "Title": "ExperianIntegrationExternalService.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\decision-engine\\src\\BlueTape.Services.DecisionEngine.DataAccess.External\\ExternalServices\\ExperianIntegrationExternalService.cs", "RelativeDocumentMoniker": "BlueTape.Services.DecisionEngine.DataAccess.External\\ExternalServices\\ExperianIntegrationExternalService.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\decision-engine\\src\\BlueTape.Services.DecisionEngine.DataAccess.External\\ExternalServices\\ExperianIntegrationExternalService.cs", "RelativeToolTip": "BlueTape.Services.DecisionEngine.DataAccess.External\\ExternalServices\\ExperianIntegrationExternalService.cs", "ViewState": "AgIAACQAAAAAAAAAAAAYwBIAAAABAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2024-06-25T07:38:53.692Z"}, {"$type": "Document", "DocumentIndex": 80, "Title": "LexisNexisIntegrationExternalService.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\decision-engine\\src\\BlueTape.Services.DecisionEngine.DataAccess.External\\ExternalServices\\LexisNexisIntegrationExternalService.cs", "RelativeDocumentMoniker": "BlueTape.Services.DecisionEngine.DataAccess.External\\ExternalServices\\LexisNexisIntegrationExternalService.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\decision-engine\\src\\BlueTape.Services.DecisionEngine.DataAccess.External\\ExternalServices\\LexisNexisIntegrationExternalService.cs", "RelativeToolTip": "BlueTape.Services.DecisionEngine.DataAccess.External\\ExternalServices\\LexisNexisIntegrationExternalService.cs", "ViewState": "AgIAABgAAAAAAAAAAAAiwB0AAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2024-06-24T14:42:56.397Z"}, {"$type": "Document", "DocumentIndex": 78, "Title": "RetrieveBusinessInstantIdData.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\decision-engine\\src\\BlueTape.Services.DecisionEngine.BusinessLogic\\Infrastructure\\RetrieveDataDynamicallyStrategies\\LexisNexis\\RetrieveBusinessInstantIdData.cs", "RelativeDocumentMoniker": "BlueTape.Services.DecisionEngine.BusinessLogic\\Infrastructure\\RetrieveDataDynamicallyStrategies\\LexisNexis\\RetrieveBusinessInstantIdData.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\decision-engine\\src\\BlueTape.Services.DecisionEngine.BusinessLogic\\Infrastructure\\RetrieveDataDynamicallyStrategies\\LexisNexis\\RetrieveBusinessInstantIdData.cs", "RelativeToolTip": "BlueTape.Services.DecisionEngine.BusinessLogic\\Infrastructure\\RetrieveDataDynamicallyStrategies\\LexisNexis\\RetrieveBusinessInstantIdData.cs", "ViewState": "AgIAAI4AAAAAAAAAAAAcwFQAAAANAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2024-06-24T14:48:19.38Z"}, {"$type": "Document", "DocumentIndex": 82, "Title": "KycStep.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\decision-engine\\src\\BlueTape.Services.DecisionEngine.BusinessLogic\\Steps\\DecisionEngine\\KycStep.cs", "RelativeDocumentMoniker": "BlueTape.Services.DecisionEngine.BusinessLogic\\Steps\\DecisionEngine\\KycStep.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\decision-engine\\src\\BlueTape.Services.DecisionEngine.BusinessLogic\\Steps\\DecisionEngine\\KycStep.cs", "RelativeToolTip": "BlueTape.Services.DecisionEngine.BusinessLogic\\Steps\\DecisionEngine\\KycStep.cs", "ViewState": "AgIAAA8AAAAAAAAAAAAcwFgAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2024-05-29T09:50:34.673Z"}, {"$type": "Document", "DocumentIndex": 81, "Title": "LoanExternalService.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\decision-engine\\src\\BlueTape.Services.DecisionEngine.DataAccess.External\\ExternalServices\\LoanExternalService.cs", "RelativeDocumentMoniker": "BlueTape.Services.DecisionEngine.DataAccess.External\\ExternalServices\\LoanExternalService.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\decision-engine\\src\\BlueTape.Services.DecisionEngine.DataAccess.External\\ExternalServices\\LoanExternalService.cs", "RelativeToolTip": "BlueTape.Services.DecisionEngine.DataAccess.External\\ExternalServices\\LoanExternalService.cs", "ViewState": "AgIAACQAAAAAAAAAAAAmwE4AAAA1AAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2024-06-10T10:38:09.077Z"}, {"$type": "Document", "DocumentIndex": 79, "Title": "LexisNexisService.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\decision-engine\\src\\BlueTape.Services.DecisionEngine.BusinessLogic\\Services\\LexisNexisService.cs", "RelativeDocumentMoniker": "BlueTape.Services.DecisionEngine.BusinessLogic\\Services\\LexisNexisService.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\decision-engine\\src\\BlueTape.Services.DecisionEngine.BusinessLogic\\Services\\LexisNexisService.cs", "RelativeToolTip": "BlueTape.Services.DecisionEngine.BusinessLogic\\Services\\LexisNexisService.cs", "ViewState": "AgIAABEAAAAAAAAAAAAswBcAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2024-06-24T14:44:10.32Z"}, {"$type": "Document", "DocumentIndex": 84, "Title": "DraftExtensions.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\decision-engine\\src\\BlueTape.Services.DecisionEngine.BusinessLogic\\Extensions\\DraftExtensions.cs", "RelativeDocumentMoniker": "BlueTape.Services.DecisionEngine.BusinessLogic\\Extensions\\DraftExtensions.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\decision-engine\\src\\BlueTape.Services.DecisionEngine.BusinessLogic\\Extensions\\DraftExtensions.cs", "RelativeToolTip": "BlueTape.Services.DecisionEngine.BusinessLogic\\Extensions\\DraftExtensions.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAcAAAA8AAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2024-06-24T14:46:19.715Z"}, {"$type": "Document", "DocumentIndex": 85, "Title": "RetrieveFraudData.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\decision-engine\\src\\BlueTape.Services.DecisionEngine.BusinessLogic\\Infrastructure\\RetrieveDataDynamicallyStrategies\\LexisNexis\\RetrieveFraudData.cs", "RelativeDocumentMoniker": "BlueTape.Services.DecisionEngine.BusinessLogic\\Infrastructure\\RetrieveDataDynamicallyStrategies\\LexisNexis\\RetrieveFraudData.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\decision-engine\\src\\BlueTape.Services.DecisionEngine.BusinessLogic\\Infrastructure\\RetrieveDataDynamicallyStrategies\\LexisNexis\\RetrieveFraudData.cs", "RelativeToolTip": "BlueTape.Services.DecisionEngine.BusinessLogic\\Infrastructure\\RetrieveDataDynamicallyStrategies\\LexisNexis\\RetrieveFraudData.cs", "ViewState": "AgIAAC8AAAAAAAAAAAAtwD8AAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2024-06-24T14:44:19.623Z"}, {"$type": "Document", "DocumentIndex": 83, "Title": "DraftMapper.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\decision-engine\\src\\BlueTape.Services.DecisionEngine.BusinessLogic\\Mapping\\Mappers\\DraftMapper.cs", "RelativeDocumentMoniker": "BlueTape.Services.DecisionEngine.BusinessLogic\\Mapping\\Mappers\\DraftMapper.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\decision-engine\\src\\BlueTape.Services.DecisionEngine.BusinessLogic\\Mapping\\Mappers\\DraftMapper.cs", "RelativeToolTip": "BlueTape.Services.DecisionEngine.BusinessLogic\\Mapping\\Mappers\\DraftMapper.cs", "ViewState": "AgIAAFQAAAAAAAAAAIBpwGoAAAAgAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2024-05-28T09:05:21.725Z"}, {"$type": "Document", "DocumentIndex": 86, "Title": "DependencyRegistrar.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\decision-engine\\src\\BlueTape.Services.DecisionEngine.DataAccess.External\\Extensions\\DependencyRegistrar.cs", "RelativeDocumentMoniker": "BlueTape.Services.DecisionEngine.DataAccess.External\\Extensions\\DependencyRegistrar.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\decision-engine\\src\\BlueTape.Services.DecisionEngine.DataAccess.External\\Extensions\\DependencyRegistrar.cs", "RelativeToolTip": "BlueTape.Services.DecisionEngine.DataAccess.External\\Extensions\\DependencyRegistrar.cs", "ViewState": "AgIAABUAAAAAAAAAAAApwB8AAAAjAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2024-06-24T14:42:55.714Z"}, {"$type": "Document", "DocumentIndex": 90, "Title": "BankStatementValidationStep.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\decision-engine\\src\\BlueTape.Services.DecisionEngine.BusinessLogic\\Steps\\DecisionEngine\\BankStatementValidationStep.cs", "RelativeDocumentMoniker": "BlueTape.Services.DecisionEngine.BusinessLogic\\Steps\\DecisionEngine\\BankStatementValidationStep.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\decision-engine\\src\\BlueTape.Services.DecisionEngine.BusinessLogic\\Steps\\DecisionEngine\\BankStatementValidationStep.cs", "RelativeToolTip": "BlueTape.Services.DecisionEngine.BusinessLogic\\Steps\\DecisionEngine\\BankStatementValidationStep.cs", "ViewState": "AgIAADEAAAAAAAAAAAAQwEEAAAAcAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2024-06-13T08:26:47.07Z"}, {"$type": "Document", "DocumentIndex": 87, "Title": "Function.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\decision-engine\\src\\lambdas\\DrawApproval\\BlueTape.Services.DrawApproval.InternalReviewStep\\Function.cs", "RelativeDocumentMoniker": "lambdas\\DrawApproval\\BlueTape.Services.DrawApproval.InternalReviewStep\\Function.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\decision-engine\\src\\lambdas\\DrawApproval\\BlueTape.Services.DrawApproval.InternalReviewStep\\Function.cs", "RelativeToolTip": "lambdas\\DrawApproval\\BlueTape.Services.DrawApproval.InternalReviewStep\\Function.cs", "ViewState": "AgIAAAAAAAAAAAAAAOBvwBkAAAAyAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2024-06-12T14:56:50.761Z"}, {"$type": "Document", "DocumentIndex": 88, "Title": "BlueTapeStep.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\decision-engine\\src\\BlueTape.Services.DecisionEngine.BusinessLogic\\Steps\\DecisionEngine\\BlueTapeStep.cs", "RelativeDocumentMoniker": "BlueTape.Services.DecisionEngine.BusinessLogic\\Steps\\DecisionEngine\\BlueTapeStep.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\decision-engine\\src\\BlueTape.Services.DecisionEngine.BusinessLogic\\Steps\\DecisionEngine\\BlueTapeStep.cs", "RelativeToolTip": "BlueTape.Services.DecisionEngine.BusinessLogic\\Steps\\DecisionEngine\\BlueTapeStep.cs", "ViewState": "AgIAAGMAAAAAAAAAAAAkwHwAAAAbAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2024-05-31T07:27:42.397Z"}, {"$type": "Document", "DocumentIndex": 89, "Title": "PreliminaryStep.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\decision-engine\\src\\BlueTape.Services.DecisionEngine.BusinessLogic\\Steps\\DecisionEngine\\PreliminaryStep.cs", "RelativeDocumentMoniker": "BlueTape.Services.DecisionEngine.BusinessLogic\\Steps\\DecisionEngine\\PreliminaryStep.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\decision-engine\\src\\BlueTape.Services.DecisionEngine.BusinessLogic\\Steps\\DecisionEngine\\PreliminaryStep.cs", "RelativeToolTip": "BlueTape.Services.DecisionEngine.BusinessLogic\\Steps\\DecisionEngine\\PreliminaryStep.cs", "ViewState": "AgIAADwAAAAAAAAAAIBZwEIAAAABAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2024-05-31T07:25:27.068Z"}, {"$type": "Document", "DocumentIndex": 92, "Title": "AffordabilityAssessmentStep.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\decision-engine\\src\\BlueTape.Services.DecisionEngine.BusinessLogic\\Steps\\DecisionEngine\\AffordabilityAssessmentStep.cs", "RelativeDocumentMoniker": "BlueTape.Services.DecisionEngine.BusinessLogic\\Steps\\DecisionEngine\\AffordabilityAssessmentStep.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\decision-engine\\src\\BlueTape.Services.DecisionEngine.BusinessLogic\\Steps\\DecisionEngine\\AffordabilityAssessmentStep.cs", "RelativeToolTip": "BlueTape.Services.DecisionEngine.BusinessLogic\\Steps\\DecisionEngine\\AffordabilityAssessmentStep.cs", "ViewState": "AgIAAJ4AAAAAAAAAAAAswAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2024-06-13T08:25:48.333Z"}, {"$type": "Document", "DocumentIndex": 91, "Title": "BankAccountVerificationStep.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\decision-engine\\src\\BlueTape.Services.DecisionEngine.BusinessLogic\\Steps\\DecisionEngine\\BankAccountVerificationStep.cs", "RelativeDocumentMoniker": "BlueTape.Services.DecisionEngine.BusinessLogic\\Steps\\DecisionEngine\\BankAccountVerificationStep.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\decision-engine\\src\\BlueTape.Services.DecisionEngine.BusinessLogic\\Steps\\DecisionEngine\\BankAccountVerificationStep.cs", "RelativeToolTip": "BlueTape.Services.DecisionEngine.BusinessLogic\\Steps\\DecisionEngine\\BankAccountVerificationStep.cs", "ViewState": "AgIAAEUAAAAAAAAAAAAmwC4AAAAXAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2024-05-30T11:46:38.806Z"}, {"$type": "Document", "DocumentIndex": 94, "Title": "LoanServiceTests.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\decision-engine\\src\\BlueTape.Services.DecisionEngine.BusinessLogic.Tests\\Services\\LoanServiceTests.cs", "RelativeDocumentMoniker": "BlueTape.Services.DecisionEngine.BusinessLogic.Tests\\Services\\LoanServiceTests.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\decision-engine\\src\\BlueTape.Services.DecisionEngine.BusinessLogic.Tests\\Services\\LoanServiceTests.cs", "RelativeToolTip": "BlueTape.Services.DecisionEngine.BusinessLogic.Tests\\Services\\LoanServiceTests.cs", "ViewState": "AgIAAAkAAAAAAAAAAAAgwB8AAAAoAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2024-06-12T15:49:14.219Z"}, {"$type": "Document", "DocumentIndex": 93, "Title": "IStepImplementationBase.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\decision-engine\\src\\BlueTape.Services.DecisionEngine.BusinessLogic\\Abstractions\\Steps\\Base\\IStepImplementationBase.cs", "RelativeDocumentMoniker": "BlueTape.Services.DecisionEngine.BusinessLogic\\Abstractions\\Steps\\Base\\IStepImplementationBase.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\decision-engine\\src\\BlueTape.Services.DecisionEngine.BusinessLogic\\Abstractions\\Steps\\Base\\IStepImplementationBase.cs", "RelativeToolTip": "BlueTape.Services.DecisionEngine.BusinessLogic\\Abstractions\\Steps\\Base\\IStepImplementationBase.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAQAAAAVAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2024-05-30T11:46:23.07Z"}, {"$type": "Document", "DocumentIndex": 95, "Title": "LoanService.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\decision-engine\\src\\BlueTape.Services.DecisionEngine.BusinessLogic\\Services\\LoanService.cs", "RelativeDocumentMoniker": "BlueTape.Services.DecisionEngine.BusinessLogic\\Services\\LoanService.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\decision-engine\\src\\BlueTape.Services.DecisionEngine.BusinessLogic\\Services\\LoanService.cs", "RelativeToolTip": "BlueTape.Services.DecisionEngine.BusinessLogic\\Services\\LoanService.cs", "ViewState": "AgIAAAwAAAAAAAAAAAAWwBUAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2024-06-10T10:38:03.005Z"}, {"$type": "Document", "DocumentIndex": 96, "Title": "Function.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\decision-engine\\src\\lambdas\\DecisionEngine\\BlueTape.Services.BlueTapeStep\\Function.cs", "RelativeDocumentMoniker": "lambdas\\DecisionEngine\\BlueTape.Services.BlueTapeStep\\Function.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\decision-engine\\src\\lambdas\\DecisionEngine\\BlueTape.Services.BlueTapeStep\\Function.cs", "RelativeToolTip": "lambdas\\DecisionEngine\\BlueTape.Services.BlueTapeStep\\Function.cs", "ViewState": "AgIAAAAAAAAAAAAAACBzwCEAAAArAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2024-05-31T07:27:37.261Z"}, {"$type": "Document", "DocumentIndex": 97, "Title": "BlueTape.Services.BlueTapeStep.csproj", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\decision-engine\\src\\lambdas\\DecisionEngine\\BlueTape.Services.BlueTapeStep\\BlueTape.Services.BlueTapeStep.csproj", "RelativeDocumentMoniker": "lambdas\\DecisionEngine\\BlueTape.Services.BlueTapeStep\\BlueTape.Services.BlueTapeStep.csproj", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\decision-engine\\src\\lambdas\\DecisionEngine\\BlueTape.Services.BlueTapeStep\\BlueTape.Services.BlueTapeStep.csproj", "RelativeToolTip": "lambdas\\DecisionEngine\\BlueTape.Services.BlueTapeStep\\BlueTape.Services.BlueTapeStep.csproj", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000758|", "WhenOpened": "2024-05-31T07:27:34.176Z"}, {"$type": "Document", "DocumentIndex": 98, "Title": "Function.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\decision-engine\\src\\lambdas\\DecisionEngine\\BlueTape.Services.DecisionEngine.PreliminaryStep\\Function.cs", "RelativeDocumentMoniker": "lambdas\\DecisionEngine\\BlueTape.Services.DecisionEngine.PreliminaryStep\\Function.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\decision-engine\\src\\lambdas\\DecisionEngine\\BlueTape.Services.DecisionEngine.PreliminaryStep\\Function.cs", "RelativeToolTip": "lambdas\\DecisionEngine\\BlueTape.Services.DecisionEngine.PreliminaryStep\\Function.cs", "ViewState": "AgIAAAAAAAAAAAAAAIBpwBwAAAAvAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2024-05-31T07:25:20.174Z"}, {"$type": "Document", "DocumentIndex": 99, "Title": "CompanyExternalService.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\decision-engine\\src\\BlueTape.Services.DecisionEngine.DataAccess.External\\ExternalServices\\CompanyExternalService.cs", "RelativeDocumentMoniker": "BlueTape.Services.DecisionEngine.DataAccess.External\\ExternalServices\\CompanyExternalService.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\decision-engine\\src\\BlueTape.Services.DecisionEngine.DataAccess.External\\ExternalServices\\CompanyExternalService.cs", "RelativeToolTip": "BlueTape.Services.DecisionEngine.DataAccess.External\\ExternalServices\\CompanyExternalService.cs", "ViewState": "AgIAABkAAAAAAAAAAAAjwDAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2024-05-30T12:11:08.089Z"}, {"$type": "Document", "DocumentIndex": 100, "Title": "ICompanyExternalService.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\decision-engine\\src\\BlueTape.Services.DecisionEngine.DataAccess.External\\Abstractions\\ExternalServices\\ICompanyExternalService.cs", "RelativeDocumentMoniker": "BlueTape.Services.DecisionEngine.DataAccess.External\\Abstractions\\ExternalServices\\ICompanyExternalService.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\decision-engine\\src\\BlueTape.Services.DecisionEngine.DataAccess.External\\Abstractions\\ExternalServices\\ICompanyExternalService.cs", "RelativeToolTip": "BlueTape.Services.DecisionEngine.DataAccess.External\\Abstractions\\ExternalServices\\ICompanyExternalService.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAA0AAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2024-05-30T12:11:03.008Z"}, {"$type": "Document", "DocumentIndex": 101, "Title": "Function.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\decision-engine\\src\\lambdas\\DecisionEngine\\BlueTape.Services.BankAccountVerificationStep\\Function.cs", "RelativeDocumentMoniker": "lambdas\\DecisionEngine\\BlueTape.Services.BankAccountVerificationStep\\Function.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\decision-engine\\src\\lambdas\\DecisionEngine\\BlueTape.Services.BankAccountVerificationStep\\Function.cs", "RelativeToolTip": "lambdas\\DecisionEngine\\BlueTape.Services.BankAccountVerificationStep\\Function.cs", "ViewState": "AgIAAAAAAAAAAAAAAOBvwCIAAAA5AAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2024-05-30T11:45:56.498Z"}, {"$type": "Document", "DocumentIndex": 102, "Title": "LexisNexisServiceConstants.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\decision-engine\\src\\BlueTape.Services.DecisionEngine.DataAccess.External\\Constants\\LexisNexisServiceConstants.cs", "RelativeDocumentMoniker": "BlueTape.Services.DecisionEngine.DataAccess.External\\Constants\\LexisNexisServiceConstants.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\decision-engine\\src\\BlueTape.Services.DecisionEngine.DataAccess.External\\Constants\\LexisNexisServiceConstants.cs", "RelativeToolTip": "BlueTape.Services.DecisionEngine.DataAccess.External\\Constants\\LexisNexisServiceConstants.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAA4AAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2024-05-29T10:08:47.417Z"}, {"$type": "Document", "DocumentIndex": 103, "Title": "appsettings.Development.json", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\decision-engine\\src\\lambdas\\DrawApproval\\BlueTape.Services.DrawApproval.RiskAssessmentStep\\appsettings.Development.json", "RelativeDocumentMoniker": "lambdas\\DrawApproval\\BlueTape.Services.DrawApproval.RiskAssessmentStep\\appsettings.Development.json", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\decision-engine\\src\\lambdas\\DrawApproval\\BlueTape.Services.DrawApproval.RiskAssessmentStep\\appsettings.Development.json", "RelativeToolTip": "lambdas\\DrawApproval\\BlueTape.Services.DrawApproval.RiskAssessmentStep\\appsettings.Development.json", "ViewState": "AgIAABwAAAAAAAAAAFB2wDEAAAAEAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.001642|", "WhenOpened": "2024-05-29T10:08:46.078Z"}, {"$type": "Document", "DocumentIndex": 104, "Title": "appsettings.Development.json", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\decision-engine\\src\\lambdas\\DrawApproval\\BlueTape.Services.DrawApproval.InternalReviewStep\\appsettings.Development.json", "RelativeDocumentMoniker": "lambdas\\DrawApproval\\BlueTape.Services.DrawApproval.InternalReviewStep\\appsettings.Development.json", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\decision-engine\\src\\lambdas\\DrawApproval\\BlueTape.Services.DrawApproval.InternalReviewStep\\appsettings.Development.json", "RelativeToolTip": "lambdas\\DrawApproval\\BlueTape.Services.DrawApproval.InternalReviewStep\\appsettings.Development.json", "ViewState": "AgIAAEQAAAAAAAAAAFB2wFkAAAAOAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.001642|", "WhenOpened": "2024-05-29T10:08:45.11Z"}, {"$type": "Document", "DocumentIndex": 105, "Title": "appsettings.Development.json", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\decision-engine\\src\\lambdas\\DrawApproval\\BlueTape.Services.DrawApproval.InitializationStep\\appsettings.Development.json", "RelativeDocumentMoniker": "lambdas\\DrawApproval\\BlueTape.Services.DrawApproval.InitializationStep\\appsettings.Development.json", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\decision-engine\\src\\lambdas\\DrawApproval\\BlueTape.Services.DrawApproval.InitializationStep\\appsettings.Development.json", "RelativeToolTip": "lambdas\\DrawApproval\\BlueTape.Services.DrawApproval.InitializationStep\\appsettings.Development.json", "ViewState": "AgIAAAAAAAAAAAAAAEB1wBQAAAAiAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.001642|", "WhenOpened": "2024-05-29T10:08:44.474Z"}, {"$type": "Document", "DocumentIndex": 106, "Title": "appsettings.Development.json", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\decision-engine\\src\\lambdas\\DecisionEngine\\BlueTape.Services.DecisionEngine.PreliminaryStep\\appsettings.Development.json", "RelativeDocumentMoniker": "lambdas\\DecisionEngine\\BlueTape.Services.DecisionEngine.PreliminaryStep\\appsettings.Development.json", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\decision-engine\\src\\lambdas\\DecisionEngine\\BlueTape.Services.DecisionEngine.PreliminaryStep\\appsettings.Development.json", "RelativeToolTip": "lambdas\\DecisionEngine\\BlueTape.Services.DecisionEngine.PreliminaryStep\\appsettings.Development.json", "ViewState": "AgIAAEYAAAAAAAAAAEB1wFoAAAAEAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.001642|", "WhenOpened": "2024-05-29T10:08:43.954Z"}, {"$type": "Document", "DocumentIndex": 107, "Title": "appsettings.Development.json", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\decision-engine\\src\\lambdas\\DecisionEngine\\BlueTape.Services.DecisionEngine.KycStep\\appsettings.Development.json", "RelativeDocumentMoniker": "lambdas\\DecisionEngine\\BlueTape.Services.DecisionEngine.KycStep\\appsettings.Development.json", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\decision-engine\\src\\lambdas\\DecisionEngine\\BlueTape.Services.DecisionEngine.KycStep\\appsettings.Development.json", "RelativeToolTip": "lambdas\\DecisionEngine\\BlueTape.Services.DecisionEngine.KycStep\\appsettings.Development.json", "ViewState": "AgIAAIUAAAAAAAAAACBzwJcAAAAHAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.001642|", "WhenOpened": "2024-05-29T10:08:43.473Z"}, {"$type": "Document", "DocumentIndex": 108, "Title": "appsettings.Development.json", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\decision-engine\\src\\lambdas\\DecisionEngine\\BlueTape.Services.DecisionEngine.KybStep\\appsettings.Development.json", "RelativeDocumentMoniker": "lambdas\\DecisionEngine\\BlueTape.Services.DecisionEngine.KybStep\\appsettings.Development.json", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\decision-engine\\src\\lambdas\\DecisionEngine\\BlueTape.Services.DecisionEngine.KybStep\\appsettings.Development.json", "RelativeToolTip": "lambdas\\DecisionEngine\\BlueTape.Services.DecisionEngine.KybStep\\appsettings.Development.json", "ViewState": "AgIAACoAAAAAAAAAADB0wD0AAAAiAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.001642|", "WhenOpened": "2024-05-29T10:08:42.956Z"}, {"$type": "Document", "DocumentIndex": 109, "Title": "appsettings.Development.json", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\decision-engine\\src\\lambdas\\DecisionEngine\\BlueTape.Services.DecisionEngine.InitializationStep\\appsettings.Development.json", "RelativeDocumentMoniker": "lambdas\\DecisionEngine\\BlueTape.Services.DecisionEngine.InitializationStep\\appsettings.Development.json", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\decision-engine\\src\\lambdas\\DecisionEngine\\BlueTape.Services.DecisionEngine.InitializationStep\\appsettings.Development.json", "RelativeToolTip": "lambdas\\DecisionEngine\\BlueTape.Services.DecisionEngine.InitializationStep\\appsettings.Development.json", "ViewState": "AgIAAAAAAAAAAAAAAEB1wBQAAAAiAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.001642|", "WhenOpened": "2024-05-29T10:08:42.413Z"}, {"$type": "Document", "DocumentIndex": 110, "Title": "appsettings.Development.json", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\decision-engine\\src\\lambdas\\DecisionEngine\\BlueTape.Services.CreditRatingCoOwnersStep\\appsettings.Development.json", "RelativeDocumentMoniker": "lambdas\\DecisionEngine\\BlueTape.Services.CreditRatingCoOwnersStep\\appsettings.Development.json", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\decision-engine\\src\\lambdas\\DecisionEngine\\BlueTape.Services.CreditRatingCoOwnersStep\\appsettings.Development.json", "RelativeToolTip": "lambdas\\DecisionEngine\\BlueTape.Services.CreditRatingCoOwnersStep\\appsettings.Development.json", "ViewState": "AgIAADMAAAAAAAAAACBzwEUAAAAWAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.001642|", "WhenOpened": "2024-05-29T10:08:41.892Z"}, {"$type": "Document", "DocumentIndex": 111, "Title": "appsettings.Development.json", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\decision-engine\\src\\lambdas\\DecisionEngine\\BlueTape.Services.CreditRatingBusinessStep\\appsettings.Development.json", "RelativeDocumentMoniker": "lambdas\\DecisionEngine\\BlueTape.Services.CreditRatingBusinessStep\\appsettings.Development.json", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\decision-engine\\src\\lambdas\\DecisionEngine\\BlueTape.Services.CreditRatingBusinessStep\\appsettings.Development.json", "RelativeToolTip": "lambdas\\DecisionEngine\\BlueTape.Services.CreditRatingBusinessStep\\appsettings.Development.json", "ViewState": "AgIAAF0AAAAAAAAAACBzwG8AAAAGAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.001642|", "WhenOpened": "2024-05-29T10:08:41.319Z"}, {"$type": "Document", "DocumentIndex": 112, "Title": "appsettings.Development.json", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\decision-engine\\src\\lambdas\\DecisionEngine\\BlueTape.Services.BlueTapeStep\\appsettings.Development.json", "RelativeDocumentMoniker": "lambdas\\DecisionEngine\\BlueTape.Services.BlueTapeStep\\appsettings.Development.json", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\decision-engine\\src\\lambdas\\DecisionEngine\\BlueTape.Services.BlueTapeStep\\appsettings.Development.json", "RelativeToolTip": "lambdas\\DecisionEngine\\BlueTape.Services.BlueTapeStep\\appsettings.Development.json", "ViewState": "AgIAACEAAAAAAAAAAEB1wDUAAAAWAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.001642|", "WhenOpened": "2024-05-29T10:08:40.329Z"}, {"$type": "Document", "DocumentIndex": 113, "Title": "appsettings.Development.json", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\decision-engine\\src\\lambdas\\DecisionEngine\\BlueTape.Services.AffordabilityAssessmentStep\\appsettings.Development.json", "RelativeDocumentMoniker": "lambdas\\DecisionEngine\\BlueTape.Services.AffordabilityAssessmentStep\\appsettings.Development.json", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\decision-engine\\src\\lambdas\\DecisionEngine\\BlueTape.Services.AffordabilityAssessmentStep\\appsettings.Development.json", "RelativeToolTip": "lambdas\\DecisionEngine\\BlueTape.Services.AffordabilityAssessmentStep\\appsettings.Development.json", "ViewState": "AgIAAEoAAAAAAAAAAAAAwG0AAAAJAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.001642|", "WhenOpened": "2024-05-29T10:08:38.526Z"}, {"$type": "Document", "DocumentIndex": 114, "Title": "appsettings.Development.json", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\decision-engine\\src\\lambdas\\DecisionEngine\\BlueTape.Services.BankAccountVerificationStep\\appsettings.Development.json", "RelativeDocumentMoniker": "lambdas\\DecisionEngine\\BlueTape.Services.BankAccountVerificationStep\\appsettings.Development.json", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\decision-engine\\src\\lambdas\\DecisionEngine\\BlueTape.Services.BankAccountVerificationStep\\appsettings.Development.json", "RelativeToolTip": "lambdas\\DecisionEngine\\BlueTape.Services.BankAccountVerificationStep\\appsettings.Development.json", "ViewState": "AgIAAAAAAAAAAAAAAABhwBQAAAAiAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.001642|", "WhenOpened": "2024-05-29T10:08:14.429Z"}, {"$type": "Document", "DocumentIndex": 115, "Title": "Function.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\decision-engine\\src\\lambdas\\DecisionEngine\\BlueTape.Services.DecisionEngine.KycStep\\Function.cs", "RelativeDocumentMoniker": "lambdas\\DecisionEngine\\BlueTape.Services.DecisionEngine.KycStep\\Function.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\decision-engine\\src\\lambdas\\DecisionEngine\\BlueTape.Services.DecisionEngine.KycStep\\Function.cs", "RelativeToolTip": "lambdas\\DecisionEngine\\BlueTape.Services.DecisionEngine.KycStep\\Function.cs", "ViewState": "AgIAABEAAAAAAAAAAAAxwBkAAAA5AAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2024-05-29T09:50:29.99Z"}, {"$type": "Document", "DocumentIndex": 116, "Title": "KybStep.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\decision-engine\\src\\BlueTape.Services.DecisionEngine.BusinessLogic\\Steps\\DecisionEngine\\KybStep.cs", "RelativeDocumentMoniker": "BlueTape.Services.DecisionEngine.BusinessLogic\\Steps\\DecisionEngine\\KybStep.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\decision-engine\\src\\BlueTape.Services.DecisionEngine.BusinessLogic\\Steps\\DecisionEngine\\KybStep.cs", "RelativeToolTip": "BlueTape.Services.DecisionEngine.BusinessLogic\\Steps\\DecisionEngine\\KybStep.cs", "ViewState": "AgIAAD8AAAAAAAAAACBzwBsAAAAlAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2024-05-29T09:47:53.952Z"}, {"$type": "Document", "DocumentIndex": 117, "Title": "CipherModel.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\decision-engine\\src\\BlueTape.Services.DecisionEngine.BusinessLogic\\Models\\Cipher\\CipherModel.cs", "RelativeDocumentMoniker": "BlueTape.Services.DecisionEngine.BusinessLogic\\Models\\Cipher\\CipherModel.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\decision-engine\\src\\BlueTape.Services.DecisionEngine.BusinessLogic\\Models\\Cipher\\CipherModel.cs", "RelativeToolTip": "BlueTape.Services.DecisionEngine.BusinessLogic\\Models\\Cipher\\CipherModel.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAA8AAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2024-05-28T12:44:52.384Z"}]}]}]}