import { client } from "../client";
import { ObjectId } from 'mongodb';

const collectionName = 'companies';

export class CompanyRepository {

    async getCompanyById(companyId: string) {
        try {
            await client.connect();

            const database = client.db(`${process.env.test_env}`);
            const collection = database.collection(collectionName);
            
            const result = await collection
                .findOne({ _id: new ObjectId(companyId) });
                
            return result || null;
        } catch (e) {
            console.error(`Error retrieving company with ID ${companyId}:`, e);
            return null;
        } finally {
            await client.close();
        }
    }
    
    async getCompanyIdByEmail(email: string): Promise<string> {
        try {
            await client.connect();

            // Step 1: Find the user by email in users collection
            const database = client.db(`${process.env.test_env}`);
            const usersCollection = database.collection('users');
            /*
            // Case insensitive regex search for email or login
            const user = await usersCollection.findOne({
                $or: [
                    { email: { $regex: new RegExp(`^${email}$`, 'i') } },
                    { login: { $regex: new RegExp(`^${email}$`, 'i') } }
                ]
            });
*/
            const user = await usersCollection.findOne({
                $or: [
                    { email: email },
                    { login: email }
                ]
            });
            
            if (!user || !user.sub) {
                console.error(`No user found with email ${email} or missing sub field`);
                return '';
            }
            
            // Step 2: Get the user's sub ID
            const userSub = user.sub;
            
            // Step 3: Find the user role in the userroles collection using the sub
            const userRolesCollection = database.collection('userroles');
            const userRole = await userRolesCollection.findOne({ sub: userSub });
            
            if (!userRole || !userRole.company_id) {
                console.error(`No user role found for sub ${userSub} or missing companyId`);
                return '';
            }
            
            // Step 4: Return the company ID from the user role
            return userRole.company_id.toString();
        } catch (error) {
            console.error(`Error retrieving company ID for email ${email}:`, error);
            return '';
        } finally {
            await client.close();
        }
    }
}