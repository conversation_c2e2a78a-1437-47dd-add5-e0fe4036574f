import {BasePage} from '../../../../base.page';
import {Page} from '@playwright/test';
import {Locator} from '@playwright/test';

export class FirstPlatypusBankModal extends BasePage {
    constructor(page){
        super(page);
    };

    containers = {
        newPage: null as Page | null 
    };


    inputFields = {
        userName: null as Locator | null,
        password: null as Locator | null,
    };

    buttons = {
        getCode: null as Locator | null,
        submitCode: null as Locator | null,
        plaidChecking: null as Locator | null,
        plaidSaving: null as Locator | null,
        submitAccounts: null as Locator | null,
        acceptTermsAndConditions: null as Locator | null,
        submitConfirmation: null as Locator | null,
        signIn: null as Locator | null,
    };

    async initializeLocators() {
        this.containers.newPage = await this.page.context().waitForEvent('page');
        this.inputFields.userName = this.containers.newPage.getByPlaceholder('Username');
        this.inputFields.password = this.containers.newPage.locator('[id="password"]');
        this.buttons.getCode = this.containers.newPage.locator('[id="submit-device"]'),
        this.buttons.submitCode = this.containers.newPage.locator('[id="submit-code"]'),
        this.buttons.plaidChecking = this.containers.newPage.locator('[for="account_0"]'),
        this.buttons.plaidSaving = this.containers.newPage.locator('[for="account_1"]'),
        this.buttons.submitAccounts = this.containers.newPage.locator('[id="submit-accounts"]'),
        this.buttons.acceptTermsAndConditions = this.containers.newPage.locator('[id="terms"]'),
        this.buttons.submitConfirmation = this.containers.newPage.locator('[id="submit-confirmation"]'),
        this.buttons.signIn = this.containers.newPage.locator('button[id="submit-credentials"]');
    };

    async passPlatybusBank(userName: string, password: string){
        await this.inputFields.userName?.fill(userName);
        await this.inputFields.password?.fill(password);
        await this.buttons.signIn?.click();
        await this.buttons.getCode?.click();
        await this.buttons.submitCode?.click();
        await this.buttons.plaidChecking?.click();
        await this.buttons.submitAccounts?.click();
        await this.buttons.acceptTermsAndConditions?.click();
        await this.buttons.submitConfirmation?.click();
    };
}