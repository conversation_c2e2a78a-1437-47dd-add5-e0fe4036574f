﻿using System.Runtime.Serialization;
using System.Text.Json.Serialization;

namespace BlueTape.Aion.DataAccess.External.Models.CreateAchTransfer.Response;

[DataContract]
public class AchResponseObj
{
    [JsonPropertyName("id")]
    public string Id { get; set; } = null!;

    [JsonPropertyName("accountId")]
    public string AccountId { get; set; } = null!;

    [JsonPropertyName("amount")]
    public string Amount { get; set; } = null!;
    
    [JsonPropertyName("feeAmount")]
    public decimal FeeAmount { get; set; }

    [JsonPropertyName("counterpartyId")]
    public string CounterpartyId { get; set; } = null!;

    [JsonPropertyName("description")]
    public string Description { get; set; } = null!;

    [JsonPropertyName("direction")]
    public string Direction { get; set; } = null!;

    [JsonPropertyName("error")]
    public string Error { get; set; } = null!;

    [JsonPropertyName("secCode")]
    public string SecCode { get; set; } = null!;

    [JsonPropertyName("status")]
    public string Status { get; set; } = null!;
    
    [JsonPropertyName("statusMessage")]
    public string StatusMessage { get; set; } = null!;

    [JsonPropertyName("counterpartyName")]
    public string CounterpartyName { get; set; } = null!;

    [JsonPropertyName("counterpartyType")]
    public string CounterpartyType { get; set; } = null!;

    [JsonPropertyName("email")]
    public string Email { get; set; } = null!;

    [JsonPropertyName("userNote")]
    public string UserNote { get; set; } = null!;

    [JsonPropertyName("sendEmail")]
    public bool? SendEmail { get; set; }

    [JsonPropertyName("effectiveDate")]
    public string EffectiveDate { get; set; } = null!;

    [JsonPropertyName("transferMethodId")]
    public string TransferMethodId { get; set; } = null!;

    [JsonPropertyName("initiatedBy")]
    public string InitiatedBy { get; set; } = null!;

    [JsonPropertyName("contextIdentifier")]
    public string ContextIdentifier { get; set; } = null!;

    [JsonPropertyName("addenda")]
    public List<string> Addenda { get; set; } = null!;

    [JsonPropertyName("createdAt")]
    public string CreatedAt { get; set; } = null!;

    [JsonPropertyName("updatedAt")]
    public string UpdatedAt { get; set; } = null!;

    [JsonPropertyName("originator")]
    public OriginatorResponse OriginatorResponse { get; set; } = null!;

    [JsonPropertyName("receiver")]
    public ReceiverResponse Receiver { get; set; } = null!;

    [JsonPropertyName("referenceId")]
    public string ReferenceId { get; set; } = null!;

    [JsonPropertyName("paymentType")]
    public string PaymentType { get; set; } = null!;

    [JsonPropertyName("postingCode")]
    public string PostingCode { get; set; } = null!;

    [JsonPropertyName("posting")]
    public string Posting { get; set; } = null!;

    [JsonPropertyName("reasonCode")]
    public string ReasonCode { get; set; } = null!;

    [JsonPropertyName("reasonData")]
    public string ReasonData { get; set; } = null!;

    [JsonPropertyName("traceNumber")]
    public string TraceNumber { get; set; } = null!;

    [JsonPropertyName("transactionType")]
    public string TransactionType { get; set; } = null!;

    [JsonPropertyName("serviceType")]
    public string ServiceType { get; set; } = null!;

    [JsonPropertyName("wasReturned")]
    public bool? WasReturned { get; set; }

    [JsonPropertyName("wasCorrected")]
    public bool? WasCorrected { get; set; }

    [JsonPropertyName("canceledAt")]
    public string CanceledAt { get; set; } = null!;

    [JsonPropertyName("processedAt")]
    public string ProcessedAt { get; set; } = null!;

    [JsonPropertyName("completedAt")]
    public string CompletedAt { get; set; } = null!;

    [JsonPropertyName("postedAt")]
    public string PostedAt { get; set; } = null!;

    [JsonPropertyName("rejectedAt")]
    public string RejectedAt { get; set; } = null!;
    
    [JsonPropertyName("original")]
    public OriginalResponse Original{ get; set; } = null!;

    [JsonPropertyName("previous")]
    public PreviousResponse Previous { get; set; } = null!;
    
    [JsonPropertyName("accountNumber")]
    public string AccountNumber { get; set; } = null!;
}