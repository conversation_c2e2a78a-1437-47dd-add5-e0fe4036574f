using System.Runtime.Serialization;
using System.Text.Json.Serialization;

namespace BlueTape.Aion.Application.Models.Instant;

[DataContract]
public class RtpPaymentInfoModel
{
    [JsonPropertyName("rtpPaymentId")]
    public string? RtpPaymentId { get; set; }

    [JsonPropertyName("originalPaymentId")]
    public string? OriginalPaymentId { get; set; }

    [JsonPropertyName("referenceId")]
    public string? ReferenceId { get; set; }

    [JsonPropertyName("accountNumber")]
    public string? AccountNumber { get; set; }

    [JsonPropertyName("clientIdentifier")]
    public string? ClientIdentifier { get; set; }

    [JsonPropertyName("amount")]
    public decimal Amount { get; set; }

    [JsonPropertyName("direction")]
    public string? Direction { get; set; }

    [JsonPropertyName("status")]
    public string? Status { get; set; }

    [JsonPropertyName("paymentType")]
    public string? PaymentType { get; set; }

    [JsonPropertyName("source")]
    public string? Source { get; set; }

    [JsonPropertyName("debtor")]
    public DebtorModel? Debtor { get; set; }

    [JsonPropertyName("creditor")]
    public CreditorModel? Creditor { get; set; }

    [JsonPropertyName("network")]
    public NetworkModel? Network { get; set; }
}