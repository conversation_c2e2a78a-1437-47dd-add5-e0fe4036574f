using System.Text.Json.Serialization;

namespace BlueTape.Aion.DataAccess.External.Models.InstantTransfer.Response;

public class Creditor
{
    [JsonPropertyName("routingNumber")]
    public string? RoutingNumber { get; set; }

    [JsonPropertyName("accountNumber")]
    public string? AccountNumber { get; set; }

    [JsonPropertyName("name")]
    public string? Name { get; set; }

    [JsonPropertyName("addressStreetName")]
    public string? AddressStreetName { get; set; }

    [JsonPropertyName("addressLine")]
    public string? AddressLine { get; set; }

    [JsonPropertyName("addressCity")]
    public string? AddressCity { get; set; }

    [Json<PERSON>ropertyName("addressState")]
    public string? AddressState { get; set; }

    [JsonPropertyName("addressPostalCode")]
    public string? AddressPostalCode { get; set; }

    [Json<PERSON>ropertyName("addressCountry")]
    public string? AddressCountry { get; set; }
}