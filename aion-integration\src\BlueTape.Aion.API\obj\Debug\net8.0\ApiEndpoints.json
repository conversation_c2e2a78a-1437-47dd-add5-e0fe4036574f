[{"ContainingType": "BlueTape.Aion.API.Controllers.AionLimitController", "Method": "GetAionAchPullLimit", "RelativePath": "ach-pull/account-code/{accountCodeType}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "X-PAYMENT-SUBSCRIPTION-TYPE", "Type": "System.String", "IsRequired": false}, {"Name": "accountCodeType", "Type": "System.String", "IsRequired": true}, {"Name": "amount", "Type": "System.Decimal", "IsRequired": false}], "ReturnTypes": [{"Type": "System.Collections.Generic.List`1[[BlueTape.Aion.API.Models.Errors.ErrorModel, BlueTape.Aion.API, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 400}, {"Type": "System.Void", "MediaTypes": [], "StatusCode": 401}, {"Type": "System.Collections.Generic.List`1[[BlueTape.Aion.API.Models.Errors.ErrorModel, BlueTape.Aion.API, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 500}, {"Type": "BlueTape.Integrations.Aion.AionTransferLimitModel", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "BlueTape.Aion.API.Controllers.AionLimitController", "Method": "GetAionAchPushLimit", "RelativePath": "ach-push/account-code/{accountCodeType}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "X-PAYMENT-SUBSCRIPTION-TYPE", "Type": "System.String", "IsRequired": false}, {"Name": "accountCodeType", "Type": "System.String", "IsRequired": true}, {"Name": "amount", "Type": "System.Decimal", "IsRequired": false}], "ReturnTypes": [{"Type": "System.Collections.Generic.List`1[[BlueTape.Aion.API.Models.Errors.ErrorModel, BlueTape.Aion.API, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 400}, {"Type": "System.Void", "MediaTypes": [], "StatusCode": 401}, {"Type": "System.Collections.Generic.List`1[[BlueTape.Aion.API.Models.Errors.ErrorModel, BlueTape.Aion.API, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 500}, {"Type": "BlueTape.Integrations.Aion.AionTransferLimitModel", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "BlueTape.Aion.API.Controllers.AccountController", "Method": "GetAccounts", "RelativePath": "api/account", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "System.Collections.Generic.List`1[[BlueTape.Aion.API.Models.Errors.ErrorModel, BlueTape.Aion.API, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 400}, {"Type": "System.Void", "MediaTypes": [], "StatusCode": 401}, {"Type": "System.Collections.Generic.List`1[[BlueTape.Aion.API.Models.Errors.ErrorModel, BlueTape.Aion.API, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 500}]}, {"ContainingType": "BlueTape.Aion.API.Controllers.AccountController", "Method": "GetAccountBalance", "RelativePath": "api/account/account-code/{accountCodeType}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "PaymentRequestId", "Type": "System.String", "IsRequired": false}, {"Name": "X-PAYMENT-SUBSCRIPTION-TYPE", "Type": "System.String", "IsRequired": false}, {"Name": "accountCodeType", "Type": "System.String", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Collections.Generic.List`1[[BlueTape.Aion.API.Models.Errors.ErrorModel, BlueTape.Aion.API, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 400}, {"Type": "System.Void", "MediaTypes": [], "StatusCode": 401}, {"Type": "System.Collections.Generic.List`1[[BlueTape.Aion.API.Models.Errors.ErrorModel, BlueTape.Aion.API, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 500}, {"Type": "System.String", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "BlueTape.Aion.API.Controllers.AchController", "Method": "CreateAchPull", "RelativePath": "api/ach/pull", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "PaymentRequestId", "Type": "System.String", "IsRequired": false}, {"Name": "X-PAYMENT-SUBSCRIPTION-TYPE", "Type": "System.String", "IsRequired": false}, {"Name": "achModel", "Type": "BlueTape.Integrations.Aion.Ach.CreateAchTransfer.CreateAchModel", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Collections.Generic.List`1[[BlueTape.Aion.API.Models.Errors.ErrorModel, BlueTape.Aion.API, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 400}, {"Type": "System.Void", "MediaTypes": [], "StatusCode": 401}, {"Type": "System.Collections.Generic.List`1[[BlueTape.Aion.API.Models.Errors.ErrorModel, BlueTape.Aion.API, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 500}, {"Type": "BlueTape.Aion.Application.Models.Ach.Pull.Response.CreateAchResponseModel", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "BlueTape.Aion.API.Controllers.AchController", "Method": "Create<PERSON><PERSON><PERSON>ush", "RelativePath": "api/ach/push", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "PaymentRequestId", "Type": "System.String", "IsRequired": false}, {"Name": "X-PAYMENT-SUBSCRIPTION-TYPE", "Type": "System.String", "IsRequired": false}, {"Name": "achModel", "Type": "BlueTape.Integrations.Aion.Ach.CreateAchTransfer.CreateAchModel", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Collections.Generic.List`1[[BlueTape.Aion.API.Models.Errors.ErrorModel, BlueTape.Aion.API, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 400}, {"Type": "System.Void", "MediaTypes": [], "StatusCode": 401}, {"Type": "System.Collections.Generic.List`1[[BlueTape.Aion.API.Models.Errors.ErrorModel, BlueTape.Aion.API, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 500}, {"Type": "BlueTape.Aion.Application.Models.Ach.Pull.Response.CreateAchResponseModel", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "BlueTape.Aion.API.Controllers.AchController", "Method": "CreateInstantPush", "RelativePath": "api/ach/push/instant", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "PaymentRequestId", "Type": "System.String", "IsRequired": false}, {"Name": "X-PAYMENT-SUBSCRIPTION-TYPE", "Type": "System.String", "IsRequired": false}, {"Name": "achModel", "Type": "BlueTape.Integrations.Aion.Ach.CreateAchTransfer.CreateAchModel", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Collections.Generic.List`1[[BlueTape.Aion.API.Models.Errors.ErrorModel, BlueTape.Aion.API, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 400}, {"Type": "System.Void", "MediaTypes": [], "StatusCode": 401}, {"Type": "System.Collections.Generic.List`1[[BlueTape.Aion.API.Models.Errors.ErrorModel, BlueTape.Aion.API, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 500}, {"Type": "BlueTape.Aion.Application.Models.Ach.Pull.Response.CreateAchResponseModel", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "BlueTape.Aion.API.Controllers.AchController", "Method": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "RelativePath": "api/ach/push/wire", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "PaymentRequestId", "Type": "System.String", "IsRequired": false}, {"Name": "X-PAYMENT-SUBSCRIPTION-TYPE", "Type": "System.String", "IsRequired": false}, {"Name": "achModel", "Type": "BlueTape.Integrations.Aion.Ach.CreateAchTransfer.CreateAchModel", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Collections.Generic.List`1[[BlueTape.Aion.API.Models.Errors.ErrorModel, BlueTape.Aion.API, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 400}, {"Type": "System.Void", "MediaTypes": [], "StatusCode": 401}, {"Type": "System.Collections.Generic.List`1[[BlueTape.Aion.API.Models.Errors.ErrorModel, BlueTape.Aion.API, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 500}, {"Type": "BlueTape.Aion.Application.Models.Ach.Pull.Response.CreateAchResponseModel", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "BlueTape.Aion.API.Controllers.InternalController", "Method": "CreateInternalTransfer", "RelativePath": "api/internal", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "PaymentRequestId", "Type": "System.String", "IsRequired": false}, {"Name": "X-PAYMENT-SUBSCRIPTION-TYPE", "Type": "System.String", "IsRequired": false}, {"Name": "createInternalModel", "Type": "BlueTape.Integrations.Aion.Internal.CreateInternalModel", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Collections.Generic.List`1[[BlueTape.Aion.API.Models.Errors.ErrorModel, BlueTape.Aion.API, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 400}, {"Type": "System.Void", "MediaTypes": [], "StatusCode": 401}, {"Type": "System.Collections.Generic.List`1[[BlueTape.Aion.API.Models.Errors.ErrorModel, BlueTape.Aion.API, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 500}, {"Type": "System.Object", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "BlueTape.Aion.API.Controllers.TransactionController", "Method": "GetTransactions", "RelativePath": "api/transactions", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "AccountCodeType", "Type": "System.String", "IsRequired": false}, {"Name": "PageSize", "Type": "System.Int32", "IsRequired": false}, {"Name": "PageNumber", "Type": "System.Int32", "IsRequired": false}], "ReturnTypes": [{"Type": "System.Collections.Generic.List`1[[BlueTape.Aion.API.Models.Errors.ErrorModel, BlueTape.Aion.API, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 400}, {"Type": "System.Void", "MediaTypes": [], "StatusCode": 401}, {"Type": "System.Collections.Generic.List`1[[BlueTape.Aion.API.Models.Errors.ErrorModel, BlueTape.Aion.API, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 500}, {"Type": "BlueTape.Utilities.Models.PaginatedResponse`1[[BlueTape.Aion.DataAccess.External.Models.Transactions.TransactionListObj, BlueTape.Aion.DataAccess.External, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "BlueTape.Aion.API.Controllers.TransactionController", "Method": "CreateExternalTransaction", "RelativePath": "api/transactions/external/transactionType/{transactionType}/paymentMethodType/{paymentMethodType}", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "PaymentRequestId", "Type": "System.String", "IsRequired": false}, {"Name": "X-PAYMENT-SUBSCRIPTION-TYPE", "Type": "System.String", "IsRequired": false}, {"Name": "achModel", "Type": "BlueTape.Integrations.Aion.Ach.CreateAchTransfer.CreateAchModel", "IsRequired": true}, {"Name": "transactionType", "Type": "System.String", "IsRequired": true}, {"Name": "paymentMethodType", "Type": "System.String", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Collections.Generic.List`1[[BlueTape.Aion.API.Models.Errors.ErrorModel, BlueTape.Aion.API, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 400}, {"Type": "System.Void", "MediaTypes": [], "StatusCode": 401}, {"Type": "System.Collections.Generic.List`1[[BlueTape.Aion.API.Models.Errors.ErrorModel, BlueTape.Aion.API, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 500}, {"Type": "BlueTape.Integrations.Aion.BlueTapeTransactionResponseModel", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "BlueTape.Aion.API.Controllers.TransactionController", "Method": "CreateInternalTransfer", "RelativePath": "api/transactions/internal", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "PaymentRequestId", "Type": "System.String", "IsRequired": false}, {"Name": "X-PAYMENT-SUBSCRIPTION-TYPE", "Type": "System.String", "IsRequired": false}, {"Name": "createInternalModel", "Type": "BlueTape.Integrations.Aion.Internal.CreateInternalModel", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Collections.Generic.List`1[[BlueTape.Aion.API.Models.Errors.ErrorModel, BlueTape.Aion.API, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 400}, {"Type": "System.Void", "MediaTypes": [], "StatusCode": 401}, {"Type": "System.Collections.Generic.List`1[[BlueTape.Aion.API.Models.Errors.ErrorModel, BlueTape.Aion.API, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 500}, {"Type": "BlueTape.Integrations.Aion.BlueTapeTransactionResponseModel", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "BlueTape.Aion.API.Controllers.AchController", "Method": "RunDailyAchProcessing", "RelativePath": "dailyAchProcessing/run", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "amount", "Type": "System.Nullable`1[[System.Decimal, System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "transactionsLimitPerCall", "Type": "System.Nullable`1[[System.Int32, System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}], "ReturnTypes": [{"Type": "System.Collections.Generic.List`1[[BlueTape.Aion.API.Models.Errors.ErrorModel, BlueTape.Aion.API, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 400}, {"Type": "System.Void", "MediaTypes": [], "StatusCode": 401}, {"Type": "System.Collections.Generic.List`1[[BlueTape.Aion.API.Models.Errors.ErrorModel, BlueTape.Aion.API, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 500}, {"Type": "System.Void", "MediaTypes": [], "StatusCode": 200}]}, {"ContainingType": "BlueTape.Aion.API.Controllers.HelperController", "Method": "GetErrorCodes", "RelativePath": "errorCodes", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "System.Collections.Generic.List`1[[BlueTape.Aion.API.Models.Errors.ErrorModel, BlueTape.Aion.API, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 400}, {"Type": "System.Void", "MediaTypes": [], "StatusCode": 401}, {"Type": "System.Collections.Generic.List`1[[BlueTape.Aion.API.Models.Errors.ErrorModel, BlueTape.Aion.API, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 500}]}, {"ContainingType": "BlueTape.Aion.API.Controllers.HelperController", "Method": "Git", "RelativePath": "git", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "System.Collections.Generic.List`1[[BlueTape.Aion.API.Models.Errors.ErrorModel, BlueTape.Aion.API, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 400}, {"Type": "System.Void", "MediaTypes": [], "StatusCode": 401}, {"Type": "System.Collections.Generic.List`1[[BlueTape.Aion.API.Models.Errors.ErrorModel, BlueTape.Aion.API, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 500}]}, {"ContainingType": "BlueTape.Aion.API.Controllers.HelperController", "Method": "HealthCheck", "RelativePath": "health", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "System.Collections.Generic.List`1[[BlueTape.Aion.API.Models.Errors.ErrorModel, BlueTape.Aion.API, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 400}, {"Type": "System.Void", "MediaTypes": [], "StatusCode": 401}, {"Type": "System.Collections.Generic.List`1[[BlueTape.Aion.API.Models.Errors.ErrorModel, BlueTape.Aion.API, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 500}]}, {"ContainingType": "BlueTape.Aion.API.Controllers.HelperController", "Method": "GetAllAionTransactions", "RelativePath": "helper/azure-storage/transactions/get-all", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "System.Collections.Generic.List`1[[BlueTape.Aion.API.Models.Errors.ErrorModel, BlueTape.Aion.API, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 400}, {"Type": "System.Void", "MediaTypes": [], "StatusCode": 401}, {"Type": "System.Collections.Generic.List`1[[BlueTape.Aion.API.Models.Errors.ErrorModel, BlueTape.Aion.API, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 500}]}, {"ContainingType": "BlueTape.Aion.API.Controllers.AionLimitController", "Method": "GetAionInstantPushLimit", "RelativePath": "instant-push/account-code/{accountCodeType}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "X-PAYMENT-SUBSCRIPTION-TYPE", "Type": "System.String", "IsRequired": false}, {"Name": "accountCodeType", "Type": "System.String", "IsRequired": true}, {"Name": "amount", "Type": "System.Decimal", "IsRequired": false}], "ReturnTypes": [{"Type": "System.Collections.Generic.List`1[[BlueTape.Aion.API.Models.Errors.ErrorModel, BlueTape.Aion.API, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 400}, {"Type": "System.Void", "MediaTypes": [], "StatusCode": 401}, {"Type": "System.Collections.Generic.List`1[[BlueTape.Aion.API.Models.Errors.ErrorModel, BlueTape.Aion.API, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 500}, {"Type": "BlueTape.Integrations.Aion.AionTransferLimitModel", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "BlueTape.Aion.API.Controllers.AchController", "Method": "GetReport", "RelativePath": "report/run", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "System.Collections.Generic.List`1[[BlueTape.Aion.API.Models.Errors.ErrorModel, BlueTape.Aion.API, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 400}, {"Type": "System.Void", "MediaTypes": [], "StatusCode": 401}, {"Type": "System.Collections.Generic.List`1[[BlueTape.Aion.API.Models.Errors.ErrorModel, BlueTape.Aion.API, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 500}, {"Type": "System.Void", "MediaTypes": [], "StatusCode": 200}]}, {"ContainingType": "BlueTape.Aion.API.Controllers.AionLimitController", "Method": "GetAionWirePushimit", "RelativePath": "wire-push/account-code/{accountCodeType}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "X-PAYMENT-SUBSCRIPTION-TYPE", "Type": "System.String", "IsRequired": false}, {"Name": "accountCodeType", "Type": "System.String", "IsRequired": true}, {"Name": "amount", "Type": "System.Decimal", "IsRequired": false}], "ReturnTypes": [{"Type": "System.Collections.Generic.List`1[[BlueTape.Aion.API.Models.Errors.ErrorModel, BlueTape.Aion.API, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 400}, {"Type": "System.Void", "MediaTypes": [], "StatusCode": 401}, {"Type": "System.Collections.Generic.List`1[[BlueTape.Aion.API.Models.Errors.ErrorModel, BlueTape.Aion.API, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 500}, {"Type": "BlueTape.Integrations.Aion.AionTransferLimitModel", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}]