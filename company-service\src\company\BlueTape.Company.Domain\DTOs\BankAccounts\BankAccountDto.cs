﻿using BlueTape.Company.Domain.DTOs.BankAccounts.Giact;

namespace BlueTape.Company.Domain.DTOs.BankAccounts;

public class BankAccountDto
{
    public string Id { get; set; } = null!;

    public string? AccountHolderName { get; set; }

    public string? AccountName { get; set; }

    public string? AccountType { get; set; }
    public DateTime CreatedAt { get; set; }
    public DateTime UpdatedAt { get; set; }
    public string? CreatedBy { get; set; }

    public bool? FinicityHistorySyncDone { get; set; }

    public bool? IsDeactivated { get; set; }

    public bool? isManualEntry { get; set; }

    public bool? IsPrimary { get; set; }
    public bool IsPrimaryForCredit { get; set; }
    public bool? IsPrimaryForIHCAutoPay { get; set; }

    public string? Name { get; set; }

    public string? PaymentMethodType { get; set; }

    public string? RoutingNumber { get; set; }

    public string? Status { get; set; }
    public bool? IncludeInCashFlow { get; set; }
    public string? ThirdPartyId { get; set; }

    public string? VoidedCheck { get; set; }
    public AccountNumberDto? AccountNumber { get; set; }

    public BillingAddressDto? BillingAddress { get; set; }
    public int Version { get; set; }
    public BankAccountPlaidDto? Plaid { get; set; }
    public BankAccountGiactDto? Giact { get; set; }
    public BankAccountAionSettingsDto? AionSettings { get; set; }

    public string? Network { get; set; }
    public string? Type { get; set; }
    public object? Response { get; set; }

    public bool? IsRegulated { get; set; }

    public BankAccountIdentityDto? Identity { get; set; }
}
