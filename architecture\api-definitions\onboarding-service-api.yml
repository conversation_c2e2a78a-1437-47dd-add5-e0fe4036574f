openapi: "3.0.0"
info:
  version: "0.0.1"
  title: "OnBoarding Private Service API"
  description: |
    API definition of OnBoarding Private Service
servers:
  - url: TBD-Dev
    description: Development server
  - url: TBD-Prod
    description: Production server
paths:
  /creditapplications:
    get:
      tags:
        - creditApplications
      summary: Gets credit applications by different filters
      description: Gets credit applications by different filters
      operationId: getCreditApplications
      parameters: 
        - name: id
          description: The credit application id
          in: query
          required: false
          schema:
            type: string
        - name: companyId
          description: The company id
          in: query
          required: false
          schema:
            type: string
        - name: einHash
          description: The EIN hash
          in: query
          required: false
          schema:
            type: string
        - name: status
          description: The status of credit application
          in: query
          required: false
          schema:
            type: string
            enum:
              - new
              - processing
              - processed
              - approved
              - rejected
              - canceled
              - sentback
        - name: name
          description: The business name, dba, or applicant name
          example: Acme inc
          in: query
          required: false
          schema:
            type: string
        - name: merchantName
          description: The merchant name
          in: query
          required: false
          schema:
            type: string
        - name: category
          description: The business category
          example: Contractor
          in: query
          required: false
          schema:
            type: string
            enum:
              - subContractor
              - generalContractor
              - dealerRetailerSupplier
              - manufacturerDistributor
              - architectInteriorDesigner
              - engineerConsultant
              - developerPropertyOwner
              - other
        - name: automatedDecision
          description: The automated decision
          example: passed
          in: query
          required: false
          schema:
            type: string
            enum:
              - passed
              - softFailed
              - hardFailed
        - name: appDateFrom
          description: The application date from date
          in: query
          required: false
          schema:
            type: string
            format: date
        - name: appDateTo
          description: The application date to date
          in: query
          required: false
          schema:
            type: string
            format: date
        - name: page
          description: The page number
          example: 2
          in: query
          required: false
          schema:
            type: number
        - name: items
          description: The items number on a page
          example: 50
          in: query
          required: false
          schema:
            type: number
      responses:
        200:
          description: The credit applications
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/PagedCreditApplication'
        400:
          description: Bad request
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
    post:
      tags:
        - creditApplications
      summary: Creates a credit application.
      description: Creates a credit application.
      operationId: createCreditApplication
      requestBody:
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/CreateCreditApplicationRequest"
      responses:
        201:
          description: The created credit application
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/CreditApplication'
        400:
          description: Bad request
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
  /creditapplications/{id}:
    get:
      tags:
        - creditApplications
      summary: Gets a credit application by id (just for compatibility)
      description: Gets a credit application by id  (just for compatibility)
      operationId: getCreditApplicationById
      parameters: 
        - name: id
          description: The credit application id
          in: path
          required: true
          schema:
            type: string
      responses:
        200:
          description: The credit application
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/CreditApplication'
        400:
          description: Bad request
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
    patch:
      tags:
        - creditApplications
      summary: Update a credit application by id (internal status change)
      description: Update a credit application by id (internal status change)
      operationId: updateCreditApplicationById
      parameters: 
        - name: id
          description: The credit application id
          in: path
          required: true
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/UpdateCreditApplicationRequest"
      responses:
        200:
          description: The updated credit application
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/CreditApplication'
        400:
          description: Bad request
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'                
  /admin/creditapplications/{id}:
    patch:
      tags:
        - creditApplications Admin
      summary: Update a credit application by id (admin function, final approval)
      description: Update a credit application by id  (admin function, final approval)
      operationId: updateCreditApplicationByIdAdmin
      parameters: 
        - name: id
          description: The credit application id
          in: path
          required: true
          schema:
            type: string
        - name: userId
          description: Identifier of the user who approved or rejected
          example: 62c5e900cd65d31d3f25c34c
          in: header
          required: true
          schema:
            type: string
            nullable: false
      requestBody:
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/UpdateCreditApplicationAdminRequest"
      responses:
        200:
          description: The updated credit application
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/CreditApplication'
        400:
          description: Bad request
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
  /creditapplications/{id}/report:
    get:
      tags:
        - creditApplications report
      summary: Gets a credit application's report.
      description: Gets a credit application's report.
      operationId: getCreditApplicationReportById
      parameters: 
        - name: id
          description: The credit application id
          in: path
          required: true
          schema:
            type: string
        - name: stepName
          description: The step's name
          in: query
          required: false
          schema: 
            type: string
            enum:
              - Initialization
              - Preliminary
              - KYB
              - KYC
              - BusinessCreditRating
              - CoOwnersCreditRating
              - BankAccountVerification
              - BankStatementValidation
              - CashFlow
              - AffordabilityAssessment
              - BlueTape
      responses:
        200:
          description: The report
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/CreditApplicationReportItem'
        404:
          description: Not found
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
    post:
      tags:
        - creditApplications report
      summary: Creates a credit application report.
      description: Creates a credit application report.
      operationId: createCreditApplicationReport
      parameters: 
        - name: id
          description: The credit application id
          in: path
          required: true
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/EmptyBody"
      responses:
        200:
          description: The report
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/CreditApplicationReportItem'
        404:
          description: Not found
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
  /creditapplications/{id}/notes:
    get:
      tags:
        - creditApplications notes
      summary: Gets a credit application's notes.
      description: Gets a credit application's notes.
      operationId: getCreditApplicationNotesById
      parameters: 
        - name: id
          description: The credit application id
          in: path
          required: true
          schema:
            type: string
      responses:
        200:
          description: The notes
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/CreditApplicationNote'
        404:
          description: Not found
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
    post:
      tags:
        - creditApplications notes
      summary: Adds a credit application note.
      description: Adds a credit application note.
      operationId: createCreditApplicationNote
      parameters: 
        - name: id
          description: The credit application id
          in: path
          required: true
          schema:
            type: string
        - name: userId
          description: Identifier of the user who created this note
          example: 62c5e900cd65d31d3f25c34c
          in: header
          required: true
          schema:
            type: string
            nullable: false
      requestBody: 
        content: 
          application/json:
            schema:
              $ref: "#/components/schemas/CreateCreditApplicationNoteRequest"
      responses:
        200:
          description: The note
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/CreditApplicationNote'
        404:
          description: Not found
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
  /drafts:
    get:
      tags:
        - drafts
      parameters: 
        - name: id
          description: The draft id
          in: query
          required: false
          schema:
            type: string
        - name: creditApplicationId
          description: The credit application id
          in: query
          required: false
          schema:
            type: string
        - name: companyId
          description: The company id
          in: query
          required: false
          schema:
            type: string
      summary: Gets drafts
      description: Gets drafts
      operationId: getDrafts
      responses:
        200:
          description: Array of drafts
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: "#/components/schemas/Draft"
        400:
          description: Bad request
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
  /drafts/{id}:
    get:
      tags:
        - drafts
      parameters:
        - name: id
          description: The draft id
          in: path
          required: true
          schema:
            type: string
      summary: Gets draft by id (just for compatibility)
      description: Gets draft by id (just for compatibility)
      operationId: getDraftById
      responses:
        200:
          description: The draft
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/Draft"
        400:
          description: Bad request
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
  /accountauthorizations:
    get:
      tags:
        - accountAuthorizations
      summary: Gets account authorizations by different criterias
      description: Gets account authorizations by different criterias
      operationId: getAccountAuthorizations
      parameters:
        - name: id
          description: The account authorization id
          in: query
          required: false
          schema:
            type: string
        - name: companyId
          description: The company id
          in: query
          required: false
          schema:
            type: string
        - name: einHash
          description: The EIN hash
          in: query
          required: false
          schema:
            type: string
      responses:
        200:
          description: The account authorization
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: "#/components/schemas/AccountAuthorization"
        400:
          description: Bad request
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
    post:
      tags:
        - accountAuthorizations
      summary: Creates account authorization
      description: Creates account authorization
      operationId: createAccountAuthorization
      requestBody:
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/CreateAccountAuthorizationRequest"
      responses:
        201:
          description: The created authorization object
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/AccountAuthorization'
        400:
          description: Bad request
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
  /accountauthorizations/{id}:
    get:
      tags:
        - accountAuthorizations
      summary: Gets account authorization by id (just for compatibility)
      description: Gets account authorization by id (just for compatibility)
      operationId: getAccountAuthorizationById
      parameters:
        - name: id
          description: The account authorization id
          in: path
          required: true
          schema:
            type: string
      responses:
        200:
          description: The account authorization
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/AccountAuthorization"
        400:
          description: Bad request
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
    put:
      tags:
        - accountAuthorizations
      summary: Replaces account authorization by id
      description: Replace account authorization by id
      operationId: replaceAccountAuthorizationById
      parameters:
        - name: id
          description: The account authorization id
          in: path
          required: true
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/UpdateAccountAuthorizationRequest"
      responses:
        200:
          description: The updated authorization object
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/AccountAuthorization'
        400:
          description: Bad request
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
    patch:
      tags:
        - accountAuthorizations
      summary: Updates parts of account authorization by id
      description: Updates parts of account authorization by id
      operationId: updateAccountAuthorizationById
      parameters:
        - name: id
          description: The account authorization id
          in: path
          required: true
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/UpdateAccountAuthorizationRequest"
      responses:
        200:
          description: The updated authorization object
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/AccountAuthorization'
        400:
          description: Bad request
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
  /accountauthorizations/getByEinList:
    post:
      tags:
        - accountAuthorizations
      summary: Gets account authorizations by ein list
      description: Gets account authorizations by ein list
      operationId: getAccountAuthorizationByEinList
      requestBody:
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/AccountAuthorizationGetByEinListRequest"
      responses:
        200:
          description: The list of authorizations
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/AccountAuthorization'
        400:
          description: Bad request
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
  /CreditApplicationAuthorizationDetails/{creditApplicationId}:
    get:
      tags:
        - creditApplicationAuthorizations
      summary: Gets a credit application authorization details
      description: Gets a credit application authorization details
      operationId: getCreditApplicationsAuthorization
      parameters:
        - name: creditApplicationId
          description: The credit application id
          in: path
          required: true
          schema:
            type: string
      responses:
        200:
          description: The credit application authorization details
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/CreditApplicationAuthorization'
        404:
          description: Not found
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
  /decisionenginesteps:
    get:
      tags:
        - decisionEngineSteps
      summary: Gets decision engine steps by various filters
      description: Gets decision engine steps by various filters
      operationId: getDecisionEngineStepByFilters
      parameters:
        - name: id
          description: The step id
          in: query
          required: false
          schema:
            type: string
        - name: accountAuthorizationId
          description: The account authorization id
          in: query
          required: false
          schema:
            type: string
        - name: creditApplicationId
          description: The credit application id
          in: query
          required: false
          schema:
            type: string
        - name: drawApprovalId
          description: The draw approval id
          in: query
          required: false
          schema:
            type: string
        - name: executionId
          description: The execution id
          in: query
          required: false
          schema:
            type: string
        - name: type
          description: The type
          in: query
          required: false
          schema:
            type: string
            enum:
              - creditApplication
              - scheduledUpdate
              - drawApproval
        - name: status
          description: The status
          in: query
          required: false
          schema:
            type: string
            enum:
            - started
            - passed
            - softFailed
            - hardFailed
            - sentBack
      responses:
        200:
          description: The list of decision engine steps
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/DecisionEngineStep'
        400:
          description: Bad request
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
    post:
      tags:
        - decisionEngineSteps
      summary: Creates decision engine step
      description: Creates decision engine step
      operationId: createDecisionEngineStep
      requestBody:
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/CreateDecisionEngineStepRequest"
      responses:
        201:
          description: The created decision engine step
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/DecisionEngineStep'
        400:
          description: Bad request
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
  /decisionenginesteps/{id}:
    get:
      tags:
        - decisionEngineSteps
      summary: Gets decision engine step by id (for compatibility)
      description: Gets decision engine step by id (for compatibility)
      operationId: getDecisionEngineStepById
      parameters:
        - name: id
          description: The step id
          in: path
          required: true
          schema:
            type: string
      responses:
        200:
          description: The step
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/DecisionEngineStep"
        400:
          description: Bad request
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
    patch:
      tags:
        - decisionEngineSteps
      summary: Updates decision engine step
      description: Updates decision engine step
      operationId: updateDecisionEngineStepById
      parameters:
        - name: id
          description: The step id
          in: path
          required: true
          schema:
            type: string
      requestBody:
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/UpdateDecisionEngineStepRequest"
      responses:
        200:
          description: The updated step
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/DecisionEngineStep'
        400:
          description: Bad request
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
  /decisionenginesteps/creditapplication/{id}:
    get:
      tags:
        - decisionEngineSteps
      summary: Gets decision engine steps by credit application id
      description: Gets decision engine steps by credit application id
      operationId: getDecisionEngineStepByCreditApplicationId
      parameters:
        - name: id
          description: The credit application id
          in: path
          required: true
          schema:
            type: string
      responses:
        200:
          description: The list of decision engine steps
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/DecisionEngineStep'    
        404:
          description: Not found
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
  /decisionenginesteps/drawapproval/{id}:
    get:
      tags:
        - decisionEngineSteps
      summary: Gets decision engine steps by draw approval id
      description: Gets decision engine steps by draw approval id
      operationId: getDecisionEngineStepByDrawApprovalId
      parameters:
        - name: id
          description: The draw approval id
          in: path
          required: true
          schema:
            type: string
      responses:
        200:
          description: The list of decision engine steps
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/DecisionEngineStep'    
        404:
          description: Not found
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
  /decisionenginesteps/bviresults:
    get:
      tags:
        - decisionEngineSteps BVI results
      summary: Gets decision engine step BVI results by various filters
      description: Gets decision engine step BVI results by various filters
      operationId: getDecisionEngineStepBVIResults
      parameters:
        - name: id
          description: The decision engine step id
          in: query
          required: false
          schema:
            type: string
        - name: creditApplicationId
          description: The credit application id
          in: query
          required: false
          schema:
            type: string
        - name: executionId
          description: The execution id
          in: query
          required: false
          schema:
            type: string
        - name: integrationLogId
          description: The integration log id
          in: query
          required: false
          schema:
            type: string
        - name: stepId
          description: The step id
          in: query
          required: false
          schema:
            type: string
      responses:
        200:
          description: The list of decision engine steps BVI result
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/DecisionEngineStepBVI'    
        404:
          description: Not found
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
    post:
      tags:
        - decisionEngineSteps BVI results
      summary: Adds decision engine step BVI
      description: Adds decision engine step BVI
      operationId: createDecisionEngineStepBVI
      requestBody:
        content:
          application/json:
            schema:
              type: array
              items:
                $ref: "#/components/schemas/CreateDecisionEngineStepBVI"
      responses:
        201:
          description: The created list of decision engine steps BVI result
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/DecisionEngineStepBVI'    
        400:
          description: Bad request
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
  /drawapprovals:
    get:
      tags:
        - drawApprovals
      summary: Gets draw approvals by different criterias
      description: Gets draw approvals by different criterias
      operationId: getDrawApprovals
      parameters:
        - name: id
          description: The draw approval id
          in: query
          required: false
          schema:
            type: string
        - name: companyId
          description: The company id
          in: query
          required: false
          schema:
            type: string
        - name: einHash
          description: The EIN hash
          in: query
          required: false
          schema:
            type: string
        - name: creditId
          description: The credit id
          in: query
          required: false
          schema:
            type: string
        - name: status
          description: The status
          in: query
          required: false
          schema:
            type: string
            enum:
              - new
              - processing
              - processed
              - authorized
              - approved
              - rejected
              - canceled
              - sentback
        - name: statusCode
          description: The status code
          in: query
          required: false
          schema:
            type: string
        - name: type
          description: The draw type
          in: query
          required: false
          schema:
            type: string
            enum:
              - regular
              - virtualCard
              - noSupplier
              - express
        - name: paymentPlanId
          description: The payment plan id
          in: query
          required: false
          schema:
            type: string
        - name: automatedDecisionResult
          description: The automated decision result
          in: query
          required: false
          schema:
            type: string
            enum:
              - passed
              - softFailed
              - hardFailed
        - name: accountStatus
          description: The account status
          in: query
          required: false
          schema:
            type: string
            enum:
              - incomplete
              - underReview
              - underStipulation
              - goodStandingConnected
              - goodStandingManual
              - pastDue
              - onHold
              - inCollection
              - inactive
              - potentiallyFraud
              - closed
        - name: appDateFrom
          description: The application date from date
          in: query
          required: false
          schema:
            type: string
            format: date
        - name: appDateTo
          description: The application date to date
          in: query
          required: false
          schema:
            type: string
            format: date
        - name: typeOrMerchantOrInvoiceNumber
          description: Search for type, merchant name or invoice number
          in: query
          required: false
          schema:
            type: string
        - name: page
          description: The page number
          example: 2
          in: query
          required: false
          schema:
            type: number
        - name: items
          description: The items number on a page
          example: 50
          in: query
          required: false
          schema:
            type: number
      responses:
        200:
          description: The draw approval
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/PagedDrawApproval"
        400:
          description: Bad request
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
    post:
      tags:
          - drawApprovals
      summary: Creates a draw approval
      description: Creates a draw approval
      operationId: createDrawApproval
      requestBody:
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/CreateDrawApprovalRequest"
      responses:
        201:
          description: The created approval object
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/DrawApproval'
        400:
          description: Bad request
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
  /drawapprovals/{id}:
    get:
      tags:
        - drawApprovals
      summary: Gets draw approvals by id (just for compatibility)
      description: Gets draw approvals by id (just for compatibility)
      operationId: getDrawApprovalById
      parameters:
        - name: id
          description: The draw approval id
          in: path
          required: true
          schema:
            type: string
      responses:
        200:
          description: The draw approval
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/DrawApproval"
        400:
          description: Bad request
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
    put:
      tags:
        - drawApprovals
      summary: Replaces draw approval by id
      description: Replace draw approval by id
      operationId: replaceDrawApprovalById
      parameters:
        - name: id
          description: The draw approval id
          in: path
          required: true
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/UpdateDrawApprovalRequest"
      responses:
        200:
          description: The updated approval object
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/DrawApproval'
        400:
          description: Bad request
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
    patch:
      tags:
        - drawApprovals
      summary: Updates draw approval by id (internal use)
      description: Updates draw approval by id (internal use)
      operationId: updateDrawApprovalById
      parameters:
        - name: id
          description: The draw approval id
          in: path
          required: true
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/UpdateDrawApprovalInternal"
      responses:
        200:
          description: The updated approval object
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/DrawApproval'
        400:
          description: Bad request
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
  /drawapprovals/{id}/details:
    patch:
      tags:
        - drawApprovals
      summary: Updates details section of draw approval by id (possibly duplicate)
      description: Updates details section of draw approval by id (possibly duplicate)
      operationId: updateDrawApprovalDetailsById
      parameters:
        - name: id
          description: The draw approval id
          in: path
          required: true
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/PatchDrawDetailsRequest"
      responses:
        200:
          description: The updated approval object
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/DrawApproval'
        400:
          description: Bad request
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'           
  /admin/drawapprovals/{id}/status:
    patch:
      tags:
        - drawApprovals Admin
      summary: Updates status of draw approval by id (admin function, final approval)
      description: Updates status of draw approval by id (admin function, final approval)
      operationId: updateDrawApprovalStatusByIdAdmin
      parameters:
        - name: id
          description: The draw approval id
          in: path
          required: true
          schema:
            type: string
        - name: userId
          description: Identifier of the user who updated the status
          example: 62c5e900cd65d31d3f25c34c
          in: header
          required: true
          schema:
            type: string
            nullable: false
      requestBody:
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/PatchDrawStatusAdminRequest"
      responses:
        200:
          description: The updated approval object
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/DrawApproval'
        400:
          description: Bad request
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
  /admin/drawapprovals/{id}/paymentplan:
    patch:
      tags:
        - drawApprovals Admin
      summary: Change payment plan, switch to nosuppier only (admin function)
      description: Change payment plan, switch to nosuppier only (admin function)
      operationId: updatePaymentPlanByIdAdmin
      parameters:
        - name: id
          description: The draw approval id
          in: path
          required: true
          schema:
            type: string
        - name: userId
          description: Identifier of the user who updated the paymentplan id
          example: 62c5e900cd65d31d3f25c34c
          in: header
          required: true
          schema:
            type: string
            nullable: false
      requestBody:
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/PatchDrawPaymentPlanAdminRequest"
      responses:
        200:
          description: The updated approval object
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/DrawApproval'
        400:
          description: Bad request
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
components:
  schemas:
    CreditApplication:
      type: object
      properties:
        id:
          type: string
        createdAt:
          type: string
          format: date-time
        createdBy:
          type: string
        updatedAt:
          type: string
          format: date-time
        updatedBy:
          type: string
        companyId:
          type: string
        einHash:
          type: string
        draftId:
          type: string
        applicationDate:
          type: string
          format: date
        status:
          type: string
          enum:
            - new
            - processing
            - processed
            - approved
            - rejected
            - canceled
            - sentback
        lastStatusChangedAt:
          type: string
          format: date-time
        lastStatusChangedBy:
          type: string
        creditLimit:
          type: number
          nullable: true
        approvedCreditLimit:
          type: number
          nullable: true
        businessName:
          type: string
        businessDba:
          type: string
        businessCategory:
          type: string
          enum:
            - subContractor
            - generalContractor
            - dealerRetailerSupplier
            - manufacturerDistributor
            - architectInteriorDesigner
            - engineerConsultant
            - developerPropertyOwner
            - other
        applicantName:
          type: string
        requestedAmount:
          type: number
        purchaseType:
          type: string
          enum:
            - inventory
            - project
            - both
        revenueFallPercentage:
          type: number
        automatedDecisionResult:
          type: string
          enum:
            - passed
            - softFailed
            - hardFailed
        approvedAt:
          type: string
          format: date-time
          nullable: true
        approvedBy:
          type: string
          nullable: true
        rejectedAt:
          type: string
          format: date-time
          nullable: true
        rejectedBy:
          type: string
          nullable: true
        canceledAt:
          type: string
          format: date-time
          nullable: true
        canceledBy:
          type: string
          nullable: true
        statusCode:
          type: string
        statusNote:
          type: string
    UpdateCreditApplicationRequest:
      type: object
      properties:
        newStatus:
          type: string
          enum:
            - processing
            - processed
            - sentback
        creditLimit:
          type: number
          nullable: true
    UpdateCreditApplicationAdminRequest:
      type: object
      properties:
        newStatus:
          type: string
          enum:
            - approved
            - rejected
            - canceled
        approvedCreditLimit:
          type: number
          nullable: true
        purchaseType:
          type: string
          enum:
            - inventory
            - project
            - both
        revenueFallPercentage:
          type: number
        code:
          type: string
        note:
          type: string
    CreateCreditApplicationRequest:
      type: object
      required: 
        - companyId
        - einHash
        - draftId
        - applicationDate
      properties:
        companyId:
          type: string
        einHash:
          type: string
        draftId:
          type: string
        applicationDate:
          type: string
          format: date
    AccountAuthorization:
      type: object
      properties:
        id:
          type: string
        createdAt:
          type: string
          format: date-time
        createdBy:
          type: string
        updatedAt:
          type: string
          format: date-time
        updatedBy:
          type: string
        companyId:
          type: string
        einHash:
          type: string
        isSentBack:
          type: boolean
        businessDetails:
          $ref: "#/components/schemas/BusinessDetails"
        ownersDetails:
          type: array
          items:
            oneOf:
              - $ref: "#/components/schemas/OwnersDetailsIndividual"
              - $ref: "#/components/schemas/OwnersDetailsEntity"
        bankAccountsDetails:
          type: array
          items:
            $ref: "#/components/schemas/BankAccountDetails"
        creditDetails:
          $ref: "#/components/schemas/CreditDetails"
    CreditApplicationAuthorization:
      allOf:
        - type: object
          required:
            - creditApplicationId
          properties:
            creditApplicationId:
              type: string
              format: guid
              description: Id of credit application
              example: d3a5d889-47e4-4321-b87b-f816ce5e7b6e
        - $ref: '#/components/schemas/AccountAuthorization'
    BusinessDetails:
      type: object
      properties:
        lastEINRejectionDate:
          type: string
          format: date
          nullable: true
        loansLastDefaultedDate:
          type: string
          format: date
          nullable: true
        businessStartDate:
          type: string
          format: date
          nullable: true
        BRICodes:
          type: array
          items:
            type: string
          nullable: true
        BVI:
          type: string
          nullable: true
        reliabilityCode:
          type: string
          nullable: true
        firstReportedTradeLineDate:
          type: string
          format: date
          nullable: true
        bankruptcyIndicator:
          type: boolean
        lastBankruptcyDate:
          type: string
          format: date
          nullable: true
        judgmentIndicator:
          type: string
        lastJudgmentDate:
          type: string
          format: date
          nullable: true
        judgmentBalance:
          type: number
          nullable: true
        lienIndicator:
          type: boolean
        lastLienDate:
          type: string
          format: date
          nullable: true
        lienBalance:
          type: number
          nullable: true
        DBT60PlusPercentage:
          type: number
          nullable: true
        DBT60PlusAmount:
          type: number
          nullable: true
        companyIncome:
          type: number
          nullable: true
        pastDueAmount:
          type: number
          nullable: true
        annualRevenue:
          type: number
          nullable: true
        inquiriesDuringLast6Months:
          type: number
          nullable: true
        tradeLinesPercentage:
          type: number
          nullable: true
        totalTradeLines:
          type: number
          nullable: true
        revenueVariancePercentage:
          type: number
          nullable: true
        DTI2Value:
          type: number
          nullable: true
        businessOutstandingBalance:
          type: number
          nullable: true
        totalAcceptableDebtAmount:
          type: number
          nullable: true
        revenueByCustomer:
          type: number
        debtByCustomer:
          type: number
        currentDbt:
          type: number
          nullable: true
          description: Debt Estimate from Experian
        loanDebt:
          type: number
          nullable: true
          description: Debt Estimate for Loan Calculation
        debtAdjustor:
          type: number
          description: Exists in decisionEngineSteps as well
          default: 1.2
        acceptablePercentRevenue:
          type: number
          description: Exists in decisionEngineSteps as well
          default: 15
    OwnersDetailsIndividual:
      type: object
      properties:
        identifier:
          type: string
        type: 
          type: string
          enum:
            - individual
            - entity
        percentOwned:
          type: number
        phone:
          type: string
        email:
          type: string
        birthday:
          type: string
          format: date
        ssnHash:
          type: string
        firstName:
          type: string
        lastName:
          type: string
        address:
          $ref: "#/components/schemas/Address"
        isPrincipal:
          type: boolean
        isAuthorizedRepresentative:
          type: boolean
        B2ELinkIndex:
          type: string
          nullable: true
        fraudpointScore:
          type: string
          nullable: true
        emailRiskScore:
          type: string
          nullable: true
        lastSSNRejectionDate:
          type: string
          format: date
          nullable: true
        CRICodes:
          type: array
          items:
            type: string
          nullable: true
        CVI:
          type: string
          nullable: true
        FICOScore:
          type: string
          nullable: true
        lastPersonalBankruptcyDate:
          type: string
          format: date
          nullable: true
    OwnersDetailsEntity:
      type: object
      properties:
        identifier:
          type: string
        type: 
          type: string
          enum:
            - individual
            - entity
        percentOwned:
          type: number
        phone:
          type: string
        email:
          type: string
        address:
          $ref: "#/components/schemas/Address"
        isPrincipal:
          type: boolean
        isAuthorizedRepresentative:
          type: boolean
        entityName:
          type: string
        einHash:
          type: string
        BRICodes:
          type: array
          items:
            type: string
          nullable: true
        BVI:
          type: string
          nullable: true
        reliabilityCode:
          type: string
          nullable: true
        firstReportedTradeLineDate:
          type: string
          format: date
          nullable: true
        bankruptcyIndicator:
          type: boolean
        lastBankruptcyDate:
          type: string
          format: date
          nullable: true
        judgmentIndicator:
          type: string
        lastJudgmentDate:
          type: string
          format: date
          nullable: true
        judgmentBalance:
          type: number
          nullable: true
        lienIndicator:
          type: boolean
        lastLienDate:
          type: string
          format: date
          nullable: true
        lienBalance:
          type: number
          nullable: true
        inquiriesDuringLast6Months:
          type: number
          nullable: true
        tradeLinesPercentage:
          type: number
          nullable: true
        DBT60PlusPercentage:
          type: number
          nullable: true
        DBT60PlusAmount:
          type: number
          nullable: true
        businessOutstandingBalance:
          type: number
          nullable: true
        lastEINRejectionDate:
          type: string
          format: date
          nullable: true
    CreateAccountAuthorizationRequest:
      type: object
      required: 
        - companyId
        - einHash
      properties:
        companyId:
          type: string
        einHash:
          type: string
    UpdateAccountAuthorizationRequest:
      type: object
      properties:
        updatedBy:
          type: string
        businessDetails:
          $ref: "#/components/schemas/BusinessDetails"
        ownersDetails:
          type: array
          items:
            oneOf:
              - $ref: "#/components/schemas/OwnersDetailsIndividual"
              - $ref: "#/components/schemas/OwnersDetailsEntity"
        bankAccountsDetails:
          type: array
          items:
            $ref: "#/components/schemas/BankAccountDetails"
        creditDetails:
          $ref: "#/components/schemas/CreditDetails"
    BankAccountDetails:
      type: object
      properties:
        identifier:
          type: string
        name:
          type: string
        id:
          type: string
        type:
          type: string
          enum:
            - checking
            - saving
        companyNameScore:
          type: number
          example: '0.4'
          format: decimal
          multipleOf: 0.1
        personalNameScore:
          type: number
          example: '0.4'
          format: decimal
          multipleOf: 0.1
        companyAddressScore:
          type: number
          example: '0.4'
          format: decimal
          multipleOf: 0.1
        personalAddressScore:
          type: number
          example: '0.4'
          format: decimal
          multipleOf: 0.1
        nameScore:
          type: number
          example: '0.4'
          format: decimal
          multipleOf: 0.1
        addressScore:
          type: number
          example: '0.4'
          format: decimal
          multipleOf: 0.1
    CreateDecisionEngineStepRequest:
      type: object
      properties:
        createdBy:
          type: string
        executionType:
          type: string
          enum:
            - creditApplication
            - scheduledUpdate
            - drawApproval
        executionId:
          type: string
        creditApplicationId:
          type: string
          nullable: true
        drawApprovalId:
          type: string
          nullable: true
        step:
          type: string
    DecisionEngineStep:
      type: object
      properties:
        id:
          type: string
        executionType:
          type: string
          enum:
            - creditApplication
            - scheduledUpdate
            - drawApproval
        createdAt:
          type: string
          format: date-time
        createdBy:
          type: string
        updatedAt:
          type: string
          format: date-time
        updatedBy:
          type: string
        executionId:
          type: string
        accountAuthorizationDetailsId:
          type: string
          nullable: true
        creditApplicationId:
          type: string
          nullable: true
        drawApprovalId:
          type: string
          nullable: true
        step:
          type: string
        previousStep:
          type: string
          nullable: true
        policyVersion:
          type: string
        status:
          type: string
          enum:
            - started
            - passed
            - softFailed
            - hardFailed
        results:
          type: array
          items:
            $ref: "#/components/schemas/StepResult"
    StepResult:
      type: object
      properties:
        code:
          type: string
        comparisonSource:
          type: string
        comparisonJustification:
          type: string
          enum:
            - byValue
            - sourceNotFound
        comparisonValue:
          type: string
          nullable: true
        thresholdValue:
          type: string
          nullable: true
        result:
          type: string
          enum:
            - pass
            - softFail
            - hardFail
    UpdateDecisionEngineStepRequest:
      type: object
      properties:
        updatedBy:
          type: string
        newStatus:
          type: string
          enum:
            - started
            - passed
            - softFailed
            - hardFailed
        policyVersion:
          type: string
        results:
          type: array
          items:
            $ref: "#/components/schemas/StepResult"
    Draft:
      type: object
      properties:
        id:
          type: string
        createdAt:
          type: string
          format: date-time
        updatedAt:
          type: string
          format: date-time
        sub:
          type: string
        companyId:
          type: string
        type:
          type: string
          enum:
            - general_application
        data:
          $ref: "#/components/schemas/DraftData"
        current:
          type: string
        filled:
          type: array
          items:
            type: string
    DraftData:
      type: object
      properties:
        businessInfo:
          $ref: "#/components/schemas/DraftGroup"
        businessOwner:
          $ref: "#/components/schemas/DraftGroup"
        finance:
          $ref: "#/components/schemas/DraftGroup"
        bank:
          $ref: "#/components/schemas/DraftGroup"
        coOwnerInfo:
          $ref: "#/components/schemas/DraftGroup"
    DraftGroup:
      type: object
      properties:
        group:
          type: string
        title:
          type: string
        items:
          type: array
          items:
            $ref: "#/components/schemas/DraftGroupItem"
    DraftGroupItem:
      type: object
      properties:
        identifier:
          type: string
        title:
          type: string
        filled:
          type: boolean
        content:
          oneOf:
            - type: string
            - $ref: '#/components/schemas/LegalName'
            - $ref: '#/components/schemas/Address'
            - $ref: '#/components/schemas/BusinessType'
            - $ref: '#/components/schemas/EncryptedData'
            - $ref: '#/components/schemas/BankDetails'
            - $ref: '#/components/schemas/OwnerDetails'
    Address:
      type: object
      properties:
        address:
          type: string
        city:
          type: string
        state:
          type: string
        zip:
          type: string
    BusinessType:
      type: object
      properties:
        selectedType:
          type: string
        other:
          type: string
    LegalName:
      type: object
      properties:
        legalName:
          type: string
        dba:
          type: string
    BankDetails:
      type: object
      properties:
        id:
          type: string
        name:
          type: string
        voidedCheck:
          type: string
        accountHolderName:
          type: string
        routingNumber:
          type: string
        accountNumber:
          type: string
        accountName:
          type: string
        paymentMethodType:
          type: string
        accountType:
          type: string
        isPrimary:
          type: boolean
        status:
          type: string
        isManualEntry:
          type: boolean
        isDeactivated:
          type: boolean
    OwnerDetails:
      type: object
      properties:
        type:
          type: string
        percentOwned:
          type: number
        address:
          type: string
        city:
          type: string
        state:
          type: string
        zip:
          type: string
        phone:
          type: string
        email:
          type: string
        firstName:
          type: string
        lastName:
          type: string
        birthday:
          type: string
        ssn:
          type: string
        entityName:
          type: string
        ein:
          $ref: "#/components/schemas/EncryptedData"
    EncryptedData:
      type: object
      properties:
        cipher:
          type: string
        display:
          type: string
        hash:
          type: string
    ErrorResponse:
      type: object
      required:
        - statusCode
        - message
        - errorDescription
      properties:
        statusCode:
          description: HTTP status code corresponding to the error.
          type: integer
          example: 400
        message:
          description: A human readable error message.
          type: string
          example: Invalid model.
        errorDescription:
          description: Detailed information.
          type: string
    AuthorizationRefreshGetByExpirationRequest:
      type: object
      properties:
        step:
          type: string
        expirationDays:
          type: number
          format: int32
        accountAuthorizationDetailsIds:
          type: array
          items:
            type: string
    AccountAuthorizationRefreshDetails:
      type: object
      properties:
        id:
          type: string
        createdAt:
          type: string
          format: date-time
        createdBy:
          type: string
        updatedAt:
          type: string
          format: date-time
        updatedBy:
          type: string
        step:
          type: string
        accountAuthorizationDetailsId:
          type: string
        creditApplicationId:
          type: string
        lastRefreshType:
          type: string
          enum:
            - creditApplication
            - scheduledUpdate
            - drawAuthorization
        lastRefreshResult:
          type: string
          enum:
            - passed
            - softFailed
            - hardFailed
    AccountAuthorizationGetByEinListRequest:
      type: object
      properties:
        einHashes:
         type: array
         items:
           type: string
    DrawApproval:
      type: object
      properties:
        id:
          type: string
        createdAt:
          type: string
          format: date-time
        createdBy:
          type: string
        updatedAt:
          type: string
          format: date-time
        updatedBy:
          type: string
        companyId:
          type: string
        einHash:
          type: string
        creditId:
          type: string
        applicationDate:
          type: string
          format: date
        paymentPlanId:
          type: string
        type:
          type: string
          enum:
            - regular
            - virtualCard
            - noSupplier
            - express
        virtualCardId:
          type: string
          nullable: true
        merchantId:
          type: string
        merchantName:
          type: string
          description: Redundant field to help filtering
        drawAmount:
          type: number
        drawAmountRiskLevel:
          type: string
          enum:
            - level1
            - level2
            - level3
            - level4
        projectId:
          type: string
          nullable: true
        automatedDecisionResult:
          type: string
          enum:
            - passed
            - softFailed
            - hardFailed
        status:
          type: string
          enum:
            - new
            - processing
            - processed
            - authorized
            - approved
            - rejected
            - canceled
            - sentback
          default: new
        lastStatusChangedAt:
          type: string
          format: date-time
        lastStatusChangedBy:
          type: string
        approvedAt:
          type: string
          format: date-time
          nullable: true
        approvedBy:
          type: string
          nullable: true
        rejectedAt:
          type: string
          format: date-time
          nullable: true
        rejectedBy:
          type: string
          nullable: true
        canceledAt:
          type: string
          format: date-time
          nullable: true
        canceledBy:
          type: string
          nullable: true
        statusCode:
          type: string
          nullable: true
        statusNote:
          type: string
          nullable: true
        drawDetails:
          $ref: "#/components/schemas/DrawsDetails"
        payables:
          type: array
          items:
            $ref: "#/components/schemas/PayableItem"
    PayableItem:
      type: object
      properties:
        id:
          type: string
        amount:
          type: number
    CreateDrawApprovalRequest:
      type: object
      properties:
        companyId:
          type: string
        einHash:
          type: string
        creditId:
          type: string
        paymentPlanId:
          type: string
        drawAmount:
          type: number
        projectId:
          type: string
          nullable: true
        payables:
          type: array
          items:
            $ref: "#/components/schemas/PayableItem"
    UpdateDrawApprovalRequest:
      type: object
      properties:
        updatedAt:
          type: string
          format: date-time
        updatedBy:
          type: string
        companyId:
          type: string
        einHash:
          type: string
        creditId:
          type: string
        paymentPlanId:
          type: string
        type:
          type: string
          enum:
            - regular
            - virtualcard
            - noSupplier
            - express
        drawAmount:
          type: number
        projectId:
          type: string
        status:
          type: string
          enum:
            - new
            - processing
            - processed
            - approved
            - rejected
            - canceled
            - sentback
        lastStatusChangedAt:
          type: string
          format: date-time
        lastStatusChangedBy:
          type: string
        approvedAt:
          type: string
          format: date-time
          nullable: true
        approvedBy:
          type: string
          nullable: true
        rejectedAt:
          type: string
          format: date-time
          nullable: true
        rejectedBy:
          type: string
          nullable: true
        drawDetails:
          $ref: "#/components/schemas/DrawsDetails"
        invoiceIds:
          type: array
          items:
            type: string
    UpdateDrawApprovalInternal:
      type: object
      properties:
        updatedAt:
          type: string
          format: date-time
        updatedBy:
          type: string
        companyId:
          type: string
        einHash:
          type: string
        creditId:
          type: string
        paymentPlanId:
          type: string
        type:
          type: string
          enum:
            - regular
            - virtualcard
            - noSupplier
            - express
        virtualCardId:
          type: string
        merchantId:
          type: string
        merchantName:
          type: string
        drawAmount:
          type: number
        drawAmountRiskLevel:
          type: string
          enum:
            - level1
            - level2
            - level3
            - level4
        projectId:
          type: string
        automatedDecisionResult:
          type: string
          enum:
            - passed
            - softFailed
            - hardFailed
        status:
          type: string
          enum:
            - new
            - processing
            - processed
            - approved
            - rejected
            - canceled
            - sentback
        lastStatusChangedAt:
          type: string
          format: date-time
        lastStatusChangedBy:
          type: string
        approvedAt:
          type: string
          format: date-time
          nullable: true
        approvedBy:
          type: string
          nullable: true
        rejectedAt:
          type: string
          format: date-time
          nullable: true
        rejectedBy:
          type: string
          nullable: true
        drawDetails:
          $ref: "#/components/schemas/DrawsDetails"
    DrawsDetails:
      type: object
      properties:
        LoansLastDefaultedDate:
          type: string
          format: date
          nullable: true
        MaxPastDueDays:
          type: number
          format: integer
          nullable: true
        CurrentCreditLimitPercentage:
          type: number
          nullable: true
        CreditPurchaseType:
          type: string
          enum:
            - inventory
            - project
            - both
          nullable: true
        CreditAvailableBalance:
          type: number
          nullable: true
        ProjectAvailableBalance:
          type: number
          nullable: true
        ProjectContractValue:
          type: number
          nullable: true
        ProjectEndDate:
          type: string
          format: date
          nullable: true
        OutstandingBalance:
          type: number
          nullable: true
        AccountStatus:
          type: string
          enum:
            - incomplete
            - underReview
            - underStipulation
            - goodStandingConnected
            - goodStandingManual
            - pastDue
            - onHold
            - inCollection
            - inactive
            - potentiallyFraud
            - closed
          nullable: true
        ProjectApprovalStatus:
          type: string
          enum:
            - incomplete
            - inreview
            - approved
            - rejected
          nullable: true
    PatchDrawDetailsRequest:
      type: object
      properties:
        newStatus:
          type: string
          enum:
            - new
            - processing
            - processed
        drawsDetails:
          $ref: "#/components/schemas/DrawsDetails"
    PatchDrawStatusAdminRequest:
      type: object
      properties: 
        newStatus:
          type: string
          enum:
            - approved
            - rejected
            - canceled
            - sentback
        code:
          type: string
        note:
          type: string
    CreditDetails:
      type: object
      properties:
        LastActivityDate:
          type: string
          format: date
    CreditApplicationNote:
      allOf:
        - type: object
          required:
            - id
          properties:
            id:
              type: string
              format: guid
              description: Id of note
              example: d3a5d889-47e4-4321-b87b-f816ce5e7b6e
            createdAt:
              type: string
              format: date-time
            createdBy:
              type: string
        - $ref: '#/components/schemas/CreateCreditApplicationNoteRequest'
    CreateCreditApplicationNoteRequest:
      type: object
      properties:
        note:
          type: string
    PagedCreditApplication:
      type: object
      properties:
        pageNumber:
          type: number
        pagesCount:
          type: number
        totalCount:
          type: number
        result:
          type: array
          items:
            $ref: '#/components/schemas/CreditApplication'
    EmptyBody:
      type: object
      nullable: true
    CreditApplicationReportItem:
      type: object
      properties:
        stepName:
          type: string
          enum:
            - Initialization
            - Preliminary
            - KYB
            - KYC
            - BusinessCreditRating
            - CoOwnersCreditRating
            - BankAccountVerification
            - BankStatementValidation
            - CashFlow
            - AffordabilityAssessment
            - BlueTape
        creditApplicationId:
          type: string
        companyId:
          type: string
        stepInput:
          type: string
        stepOutput:
          type: string
        bviResults:
          type: array
          items:
            $ref: '#/components/schemas/BVIResultsItem'
        accountAuthorizationDetails:
          type: string
    BVIResultsItem:
      type: object
      properties:
        integrationSource:
          type: string
        responseJsonString:
          type: string
    CreateDecisionEngineStepBVI:
      type: object
      properties:
        decisionEngineStepId:
          type: string
        integrationLogId:
          type: string
        integrationSource:
          type: string
        creditApplicationId:
          type: string
        executionId:
          type: string      
    DecisionEngineStepBVI:
      allOf:
        - type: object
          required:
            - id
          properties:
            id:
              type: string
              format: guid
            createdAt:
              type: string
              format: date-time
        - $ref: '#/components/schemas/CreateDecisionEngineStepBVI'
    PagedDrawApproval:
      type: object
      properties:
        pageNumber:
          type: number
        pagesCount:
          type: number
        totalCount:
          type: number
        result:
          type: array
          items:
            $ref: '#/components/schemas/DrawApproval'
    PatchDrawPaymentPlanAdminRequest:
      type: object
      properties:
        newPaymentPlanId:
          type: string
  securitySchemes:
    ApiKey:
      type: apiKey
      in: header
      name: X-Api-Key
security:
  - ApiKey: []
