.dev:
  extends: .test:base
  variables:
    CI_ENVIRONMENT_URL: $CI_ENVIRONMENT_URL
    CI_ENVIRONMENT_BACKEND_URL: $DEV_CI_ENVIRONMENT_BACKEND_URL
    ADMIN_BACKEND_URL: $DEV_BACKEND_URL
    ADMIN_EMAIL: $DEV_ADMIN_EMAIL
    ADMIN_PASSWORD: $DEV_ADMIN_PASSWORD
    USER_EMAIL: $DEV_USER_EMAIL
    USER_PASSWORD: $DEV_USER_PASSWORD
    USER_FIRSTNAME: $DEV_USER_FIRSTNAME
    USER_LASTNAME: $DEV_USER_LASTNAME
    USER_BACKEND_URL: $DEV_USER_BACKEND_URL
    USER_DOMAIN: $DEV_USER_DOMAIN
    USER_KEY: $BETA_USER_KEY
    MONGODB_URI: $MONGODB_URI
    ACCESS_KEY_AWS: $ACCESS_KEY_AWS
    SECRET_ACCESS_KEY_AWS: $SECRET_ACCESS_KEY_AWS
    BUCKETNAME_AWS: $BETA_BUCKETNAME_AWS

  environment:
    name: development
    url: https://dev.bluetape.com
