﻿using BlueTape.Aion.Domain.DTOs.BankAccount;
using BlueTape.Aion.Domain.DTOs.Company;
using BlueTape.Integrations.Aion.Infrastructure.Enums;

namespace BlueTape.Aion.Application.Abstractions;

public interface IBankAccountService
{
    Task<BankAccountDto> SynBankAccountWithAionAsync(string receiverBankAccountId, CompanyDto companyDto,
        string paymentSubscriptionType, AionPaymentMethodType paymentMethodType, CancellationToken ctx);
}