using BlueTape.Company.Domain.DTOs.BankAccounts.Giact;

namespace BlueTape.Company.Domain.DTOs.BankAccounts;

public class CreateBankAccountDto
{
    public string? Id { get; set; }
    public string? AccountHolderName { get; set; }
    public string? AccountName { get; set; }
    public string? AccountType { get; set; }
    public bool? FinicityHistorySyncDone { get; set; }
    public bool? IsDeactivated { get; set; }
    public bool? IsManualEntry { get; set; }
    public bool? IsRegulated { get; set; }
    public bool? IsPrimary { get; set; }
    public bool IsPrimaryForCredit { get; set; }
    public bool? IsPrimaryForIHCAutoPay { get; set; }
    public string? Name { get; set; }
    public string? PaymentMethodType { get; set; }
    public string? RoutingNumber { get; set; }
    public string? Status { get; set; }
    public string? ThirdPartyId { get; set; }
    public string? VoidedCheck { get; set; }
    public string AccountNumberCipher { get; set; } = null!;
    public string? AccountNumber { get; set; }
    public bool? IncludeInCashFlow { get; set; }
    public BillingAddressDto? BillingAddress { get; set; }
    public int Version { get; set; }
    public BankAccountPlaidDto? Plaid { get; set; }
    public BankAccountGiactDto? Giact { get; set; }
}