import {BasePage} from "../../../base.page";
import {expect} from "@playwright/test";

export class ProjectPropertyAddress extends BasePage {
    constructor(page) {
        super(page);
    };

    containers = {
        streetAddressContainer: this.page.locator('_react=[key=".0"]'),
    };

    dropdowns = {
        addressDropdown: this.page.locator('div').filter({hasText: /^51 West 51st Street, New York, NY, USA$/}).first(),
        requiredCityVariant: this.page.getByText('51 West 51st Street, New York, NY, USA'),
    };

    buttons = {
        next: this.page.locator('_react=[testID="ProjectNextButton"]'),
        back: this.page.locator('_react=[testID="ProjectBackButton"]'),
    };

    input = {
        streetAddress: this.page.getByTestId('streetAddress').first(),
        unit: this.page.locator('_react=l[testID="unitNumber"] >> _react=TextInput'),
        // state: this.page.locator('_react=l[testID="state"] >> _react=TextInput'),
        state: this.page.locator('//div[@class="css-1dbjc4n r-1x0uki6"]').locator('//input'),
        zipCode: this.page.locator('_react=l[testID="zip"] >> _react=TextInput'),
        city: this.page.locator('_react=l[testID="city"] >> _react=TextInput'),
        house: this.page.locator('_react=l[testID="houseNumber"] >> _react=TextInput'),
        subdivision: this.page.locator('_react=l[testID="subdivision"] >> _react=TextInput'),
        lotNumber: this.page.locator('_react=l[testID="lotNumber"] >> _react=TextInput'),
        block: this.page.locator('_react=l[testID="blockNum"] >> _react=TextInput'),
        jobId: this.page.locator('_react=l[testID="jobId"] >> _react=TextInput'),
    };

    elements = {
        firstAddressInAddressList: this.containers.streetAddressContainer.locator('_react=t[key=".$0"]'),
    };

    async fillRequiredFields(address: string, state: string, zipCode: string, city: string) {
        await this.input.streetAddress.type(address, {
            delay: 25,
        });
        await this.input.zipCode.fill(zipCode);
        await this.input.state.fill(state);
        await this.input.city.fill(city);
    };

    async fillAddressFieldAndChoose(address: string) {
        await this.input.streetAddress.click();
        await this.input.streetAddress.first().type(address);
        await this.dropdowns.requiredCityVariant.click({
            timeout: 5000
        });
    }

    async clickActiveNextButton() {
        await expect(this.buttons.next).toHaveAttribute('tabindex', '0');
        await this.buttons.next.click({
            timeout: 3000
        });
    }
}
