﻿<Project Sdk="Microsoft.NET.Sdk.Web">

    <PropertyGroup>
        <TargetFramework>net8.0</TargetFramework>
        <ImplicitUsings>enable</ImplicitUsings>
        <Nullable>enable</Nullable>
        <GenerateRuntimeConfigurationFiles>true</GenerateRuntimeConfigurationFiles>
        <AWSProjectType>Lambda</AWSProjectType>
        <CopyLocalLockFileAssemblies>true</CopyLocalLockFileAssemblies>
        <PublishReadyToRun>true</PublishReadyToRun>
        <DockerComposeProjectPath>../../docker-compose.dcproj</DockerComposeProjectPath>
        <UserSecretsId>aacf65bc-54a3-4102-90d3-4bbaf420d4d0</UserSecretsId>
    </PropertyGroup>

    <PropertyGroup Condition=" '$(RunConfiguration)' == 'BlueTape.Company.API DEV' " />
    <PropertyGroup Condition=" '$(RunConfiguration)' == 'BlueTape.Company.API BETA' " />
    <PropertyGroup Condition=" '$(RunConfiguration)' == 'BlueTape.Company.API QA' " />
    <ItemGroup>
      <None Remove="Constants\" />
    </ItemGroup>
    <ItemGroup>
        <None Update="appsettings.dev.json">
            <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
            <DependentUpon>appsettings.json</DependentUpon>
        </None>
        <None Update="appsettings.prod.json">
            <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
            <DependentUpon>appsettings.json</DependentUpon>
        </None>
        <None Update="appsettings.qa.json">
            <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
            <DependentUpon>appsettings.json</DependentUpon>
        </None>
        <None Update="appsettings.beta.json">
            <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
            <DependentUpon>appsettings.json</DependentUpon>
        </None>
        <None Update="appsettings.json">
            <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
        </None>
    </ItemGroup>
    
    <ItemGroup>
        <PackageReference Include="BlueTape.CompanyService" Version="1.3.2" />
        <PackageReference Include="BlueTape.Logging" Version="1.0.0" />
        <PackageReference Include="Azure.Identity" Version="1.11.3" />
        <PackageReference Include="Azure.Security.KeyVault.Secrets" Version="4.5.0" />
        <PackageReference Include="Azure.Extensions.AspNetCore.Configuration.Secrets" Version="1.3.0" />
        <PackageReference Include="Microsoft.ApplicationInsights.AspNetCore" Version="2.22.0" />
        <PackageReference Include="Swashbuckle.AspNetCore.SwaggerUI" Version="6.5.0" />
    </ItemGroup>
    
    <ItemGroup>
        <PackageReference Include="Serilog.Sinks.ApplicationInsights" Version="4.0.0" />
        <PackageReference Include="Swashbuckle.AspNetCore.SwaggerGen" Version="6.5.0" />
        <PackageReference Include="Amazon.Lambda.AspNetCoreServer.Hosting" Version="1.6.1" />
        <PackageReference Include="NSwag.AspNetCore" Version="14.0.0" />
        <PackageReference Include="Microsoft.NETCore.Targets" Version="6.0.0-preview.4.21253.7" />
        <PackageReference Include="AWSSDK.Extensions.NETCore.Setup" Version="3.7.300" />
        <PackageReference Include="AWSSDK.S3" Version="3.7.305" />
        <PackageReference Include="Microsoft.EntityFrameworkCore.Design" Version="7.0.4">
            <PrivateAssets>all</PrivateAssets>
            <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
        </PackageReference>
        <PackageReference Include="FluentValidation" Version="11.9.0" />
        <PackageReference Include="FluentValidation.AspNetCore" Version="11.0.2" />
        <PackageReference Include="FluentValidation.DependencyInjectionExtensions" Version="11.0.2" />
    </ItemGroup>
    
    <ItemGroup>
      <ProjectReference Include="..\cashflow\BlueTape.CashFlow.Application\BlueTape.CashFlow.Application.csproj" />
      <ProjectReference Include="..\company\BlueTape.Company.Application\BlueTape.Company.Application.csproj" />
      <ProjectReference Include="..\document\BlueTape.Document.Application\BlueTape.Document.Application.csproj" />
    </ItemGroup>
</Project>
