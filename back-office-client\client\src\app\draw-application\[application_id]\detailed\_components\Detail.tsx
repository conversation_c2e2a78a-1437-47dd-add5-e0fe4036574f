'use client'

import { useTranslation } from 'react-i18next'
import { Spin } from 'antd'

import DetailContent from './DetailContent'

import Spacer from '@/components/common/Spacer'
import StyledTitle from '@/components/common/typography/StyledTitle'
import type { IDrawApplicationDetailedResponse } from '@/lib/redux/api/draw-application/types'
import BackLink from '@/components/common/BackLink'
import { AppRoutes } from '@/globals/routes'
import {
  useDrawApplicationCompanyDetailsQuery,
  useDrawApplicationWithDrawDetailsQuery,
} from '@/lib/redux/api/draw-application'

interface IProps {
  data: IDrawApplicationDetailedResponse
  backLinkHref?: string
  backLinkText?: string
}

const Detail = ({ data, backLinkHref, backLinkText }: IProps): JSX.Element => {
  const { t } = useTranslation()

  const { data: companyDetails, isLoading: isCompanyDetailsLoading } =
    useDrawApplicationCompanyDetailsQuery(
      { companyId: data.companyId },
      { skip: !data.companyId },
    )

  const { data: drawsDetails, isLoading: isDrawDetailsLoading } =
    useDrawApplicationWithDrawDetailsQuery({ applicationId: data.id })

  const defaultBackLinkHref = backLinkHref ?? AppRoutes.drawApplication.main()
  const defaultBackLinkText =
    backLinkText ?? t('drawApplication.page.detailed.backToList')

  const isLoading = isCompanyDetailsLoading || isDrawDetailsLoading

  return (
    <>
      <BackLink href={defaultBackLinkHref}>{defaultBackLinkText}</BackLink>

      <Spacer height={12} />
      <StyledTitle level={2} data-testid="drawApplicationDetailedTitle">
        {data.businessName}
      </StyledTitle>
      <Spin spinning={isLoading} delay={0} size="large">
        <DetailContent
          data={data}
          companyDetails={companyDetails}
          drawDetails={drawsDetails?.result[0]?.drawDetails}
        />
      </Spin>
    </>
  )
}

export default Detail
