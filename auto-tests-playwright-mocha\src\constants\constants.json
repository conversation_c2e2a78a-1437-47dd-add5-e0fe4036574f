{"password": "Aa!11111111", "user": {"usersRoles": ["Sub-contractor", "General Contractor", "Dealer / Retailer / Supplier", "Manufacturer / Distributor", "Architect / Interior Designer", "Engineer / Consultant", "Developer / Property Owner"], "cellPhoneNumber": "(*************", "betaCompanyName": "AutomationBeta", "devCompanyName": "AutomationDev", "qaCompanyName": "AutomationQA", "socialSecurityNumber": "*********"}, "linkTypes": {"invitationLink": "Invitation", "verificationLink": "Verification"}, "invoice": {"invoiceAmount": "434"}, "address": {"fullAddress": "51 West 51st Street, New York, NY, USA", "street": "51 W 51st St", "city": "New York", "state": "New York", "zipCode": "10019"}, "cardDetails": {"cardNumber": "****************", "expirationDate": "12 / 42", "securityCode": "123"}, "bankAccount": {"bankName": "Bank Of America", "routingNumber": "*********", "accountNumber": "*****************"}, "generic": {"firstName": "AutoCustomerFirst", "lastName": "AutoCustomerLast", "invalidCellPhoneNumber": "***********", "invalidEmail": "katsiaryna+genericapicustomerbluetape.com"}, "aion": {"amount": 10, "validDescription": "Payment", "validAddenda": ["payment", "string"], "invalidDescriptionWirhMoreThanTenSymbols": "description", "invalidAddendaWithMoreThanEightySymbols": ["**********", "**********", "**********", "**********", "**********", "**********", "**********", "**********", "1"], "roundingAmounts": {"down": 10.454, "averageValue": 10.455, "up": 10.456}, "invalidCompanyId": "************", "invalidBankAccountId": "************"}, "plaid": {"userName": "user_good", "password": "password_good"}}