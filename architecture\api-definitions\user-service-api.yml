openapi: "3.0.0"
info:
  version: "0.0.1"
  title: "User Service API"
  description: |
    API definition of User service
servers:
  - url: TBD-Dev
    description: Development server
  - url: TBD-Prod
    description: Production server
paths:
  /users:
    get:
      tags:
        - users
      parameters: 
        - name: id
          description: The user id
          in: query
          required: false
          schema:
            type: string
        - name: sub
          description: The sub
          in: query
          required: false
          schema:
            type: string
      summary: Gets users
      description: Gets users
      operationId: getUsers
      responses:
        200:
          description: The users
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: "#/components/schemas/User"
  /users/{id}:
    get:
      tags:
        - users
      parameters: 
        - name: id
          description: The user id
          in: path
          required: true
          schema:
            type: string
      summary: Gets user by id
      description: Gets user by id
      operationId: getUserById
      responses:
        200:
          description: The user
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/User"
  /userRoles:
    get:
      tags:
        - userRoles
      parameters: 
        - name: companyId
          description: The company id
          in: query
          required: false
          schema:
            type: string
        - name: role
          description: The role
          in: query
          required: false
          schema:
            type: string
      summary: Gets user roles
      description: Gets user roles
      operationId: getUserRoles
      responses:
        200:
          description: The user roles
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: "#/components/schemas/UserRole"
components:
  schemas:
    User:
      type: object
      properties:
        id:
          type: string
        sub:
          type: string
        login:
          type: string
        email:
          type: string
        phone:
          type: string
        hubspotId:
          type: string
        hubspotLastSyncDate:
          type: string
          format: date-time
        settings:
          $ref: "#/components/schemas/UserSettings"
        addresses:
          type: array
          items:
            $ref: "#/components/schemas/UserAddress"
    UserSettings:
      type: object
      properties:
        ip:
          type: string
        ip_based_city:
          type: string
        ip_based_state:
          type: string
        ua:
          type: string
        conversion:
          type: object
        opt_for_marketing_messages:
          type: boolean
    UserAddress:
      type: object
      properties:
        address:
          type: string
        city:
          type: string
        state:
          type: string
        zip:
          type: string
    UserRole:
      type: object
      properties:
        id:
          type: string
        sub:
          type: string
        role:
          type: string
        companyId:
          type: string
        status:
          type: string
    ErrorResponse:
      type: object
      required:
        - statusCode
        - message
        - errorDescription
      properties:
        statusCode:
          description: HTTP status code corresponding to the error.
          type: integer
          example: 400
        message:
          description: A human readable error message.
          type: string
          example: Invalid model.
        errorDescription:
          description: Detailed information.
          type: string
  securitySchemes:
    ApiKey:
      type: apiKey
      in: header
      name: X-Api-Key
security:
  - ApiKey: []
