global:
  aionService:
    role:
      dev: arn:aws:iam::961958443244:role/LinqpalServiceRoleForLambdaExecutionInVPC
      qa: arn:aws:iam::961958443244:role/LinqpalServiceRoleForLambdaExecutionInVPC
      beta: arn:aws:iam::961958443244:role/LinqpalServiceRoleForLambdaExecutionInVPC
      prod: arn:aws:iam::162372929763:role/LinqpalServiceRoleForLambdaExecutionInVPC
    deploymentBucket:
      dev: dev.uw1.linqpal-serverlessdeployment
      qa: qa.uw1.linqpal-serverlessdeployment
      beta: beta.uw1.linqpal-serverlessdeployment
      prod: prod.uw1.linqpal-serverlessdeployment
    vpcGroup:
      dev:
        - sg-07c6e0d751378cf83
      qa:
        - sg-07c6e0d751378cf83
      beta:
        - sg-07c6e0d751378cf83
      prod:
        - sg-06199535b0aeef5cb
    vpcSubnet:
      dev:
        - subnet-07dc54c360ec07f33
        - subnet-0d2e9d3286fbd8e82
      qa:
        - subnet-07dc54c360ec07f33
        - subnet-0d2e9d3286fbd8e82
      beta:
        - subnet-07dc54c360ec07f33
        - subnet-0d2e9d3286fbd8e82
      prod:
        - subnet-0b5d07663f51186ea
        - subnet-039103dc94c77e9ca
    domainName:
      dev: dev-api.bluetape.com
      qa: qa-api.bluetape.com
      beta: beta-api.bluetape.com
      prod: api.bluetape.com
    warmup:
      dev: false
      qa: true
      beta: true
      prod: true
    lambdaInsights:
      defaultLambdaInsights: true #enables Lambda Insights for all your functions, if
      attachPolicy: false #explicitly disable auto attachment Managed Policy.
      lambdaInsightsVersion: 14 #specify the Layer Version
        
  customDomain:
    domainName: ${self:custom.aionService.domainName.${env:STAGE}}
    basePath: aionService
    stage: ${env:STAGE}
        
  serverless-offline:
    httpPort: 5000
  warmup:
    day:
      enabled: ${self:custom.aionService.warmup.${env:STAGE}}
      prewarmup: true
      events:
        - schedule: cron(0/5 8-17 ? * MON-FRI *)
      concurrency: 3
    night:
      enabled: ${self:custom.aionService.warmup.${env:STAGE}}
      prewarmup: true
      events:
        - schedule: cron(0/5 0-7 ? * MON-FRI *)
        - schedule: cron(0/5 23 ? * MON-FRI *)
        - schedule: cron(0/5 * ? * SAT-SUN *)
      concurrency: 1
  scheduleEnabled:
    prod: true
    beta: false
    dev: true
    qa: false
  transactionStatusReportScheduleRate:
    prod: rate(1 hour)
    beta: rate(1 hour)
    qa: rate(1 hour)
    dev: rate(1 hour)
  prune:
    automatic: true
    includeLayers: true
    number: 5
