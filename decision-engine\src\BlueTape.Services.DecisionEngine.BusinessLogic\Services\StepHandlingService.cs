using BlueTape.CompanyService.Common.Functions.AccountStatus;
using BlueTape.OBS.Enums;
using BlueTape.ServiceBusMessaging.Attributes;
using BlueTape.Services.DecisionEngine.BusinessLogic.Abstractions.Services;
using BlueTape.Services.DecisionEngine.BusinessLogic.Abstractions.Steps.Base;
using BlueTape.Services.DecisionEngine.BusinessLogic.Constants;
using BlueTape.Services.DecisionEngine.BusinessLogic.Extensions;
using BlueTape.Services.DecisionEngine.BusinessLogic.Models.Credit;
using BlueTape.Services.DecisionEngine.BusinessLogic.Models.CreditApplication;
using BlueTape.Services.DecisionEngine.BusinessLogic.Models.DecisionEngineStep;
using BlueTape.Services.DecisionEngine.Infrastructure.Exceptions;
using BlueTape.Services.DecisionEngine.PolicyRulesFramework.Domain.Enums;
using BlueTape.SNS.SlackNotification.Models;
using BlueTape.Utilities.Providers;
using System.Globalization;
using TinyHelpers.Extensions;

namespace BlueTape.Services.DecisionEngine.BusinessLogic.Services;

public class StepHandlingService : IStepHandlingService
{
    private readonly IAccountStatusChangeService _accountStatusChangeService;
    private readonly ILoanService _loanService;
    private readonly IOnBoardingService _onBoardingService;
    private readonly IErrorNotificationService _errorNotificationService;
    private readonly IDateProvider _dateProvider;
    public StepHandlingService(IAccountStatusChangeService accountStatusChangeService, ILoanService loanService, IDateProvider dateProvider, IOnBoardingService onBoardingService, IErrorNotificationService errorNotificationService)
    {
        _accountStatusChangeService = accountStatusChangeService;
        _loanService = loanService;
        _onBoardingService = onBoardingService;
        _errorNotificationService = errorNotificationService;
        _dateProvider = dateProvider;
    }

    public async Task HandleRefreshStatus(string stepStatus, string creditApplicationId, string stepNameWithExecutionId, string stepName, CancellationToken ct)
    {
        var eventName = GetRefreshEventName(stepName, stepStatus);
        if (string.IsNullOrEmpty(eventName)) return;

        var creditApp = await _onBoardingService.GetCreditApplicationById(creditApplicationId, ct);
        var credit = (await _loanService.GetCreditsByCompanyId(creditApp!.CompanyId!, false, ct))!
            .FirstOrDefault(x => x.CreditApplicationId == creditApplicationId);
        await _accountStatusChangeService.SendMessage(new ServiceBusMessageBt<ChangeAccountStatusModel>(new ChangeAccountStatusModel
        {
            CreatedBy = stepNameWithExecutionId,
            CreatedAt = _dateProvider.CurrentDateTime,
            EventType = eventName!,
            Details = new ChangeAccountStatusDetailsModel
            {
                Id = credit?.Id.ToString() ?? string.Empty,
                CompanyId = creditApp.CompanyId!
            }
        }), ct);
    }

    public async Task HandleRefreshStatus(string stepStatus, CreditModel? credit, string companyId, string stepNameWithExecutionId, string stepName, CancellationToken ct)
    {
        var eventName = GetRefreshEventName(stepName, stepStatus);
        if (string.IsNullOrEmpty(eventName)) return;

        await _accountStatusChangeService.SendMessage(new ServiceBusMessageBt<ChangeAccountStatusModel>(new ChangeAccountStatusModel
        {
            CreatedBy = stepNameWithExecutionId,
            CreatedAt = _dateProvider.CurrentDateTime,
            EventType = eventName!,
            Details = new ChangeAccountStatusDetailsModel
            {
                Id = credit?.Id.ToString() ?? string.Empty,
                CompanyId = companyId
            }
        }), ct);
    }

    private static string? GetRefreshEventName(string stepName, string stepStatus)
    {
        string eventName;
        if (stepStatus.Equals(ComparisonResult.HardFail.ToString()) || stepStatus.Equals(ComparisonResult.SoftFail.ToString())) AccountStatusConstants.EventsStatusesDictionary.TryGetValue(stepName, out eventName);
        else AccountStatusConstants.GoodStandingEventsStatusesDictionary.TryGetValue(stepName, out eventName);

        return eventName;
    }

    public async Task HandleStatus(IStepInputBase input, string stepStatus, string stepName, CancellationToken ct)
    {
        if (stepStatus.Equals(ComparisonResult.HardFail.ToString()))
        {
            var updateModel = new UpdateCreditApplicationModel()
            {
                NewStatus = CreditApplicationStatus.Processed.ToString(),
                AutomatedDecisionResult = ComparisonResult.HardFail.ToString(),
                UpdatedBy = stepName,
                ExecutionId = input.ExecutionId,
                ShouldIgnoreCaching = false
            };

            await _onBoardingService.PatchCreditApplication(input.CreditApplicationId, updateModel, ct);
        }
    }

    public async Task HandleException(IStepInputBase stepInput, string stepName, string errorMessage, CancellationToken ct)
    {
        await PatchCreditApplicationOnError(stepInput, stepName, null, ct);
        await PatchScheduledStepOnError(stepInput, stepName, ct);

        var executionType = string.IsNullOrEmpty(stepInput.ScheduledUpdate) ? DecisionEngineStepConstants.CreditApplication : DecisionEngineStepConstants.Scheduled;
        var source = $"TraceId: {stepInput.ExecutionId} CreditApplicationId: {stepInput.CreditApplicationId} ExecutionType: {executionType}";
        await _errorNotificationService.Notify(new EventMessageBody
        {
            Message = $"{errorMessage}",
            EventLevel = EventLevel.Error,
            EventName = "Internal Error",
            EventSource = source,
            ServiceName = SlackNotificationsConstants.DeProjectValue,
            TimeStamp = _dateProvider.CurrentDateTime.ToString(CultureInfo.InvariantCulture),
            AwsAccountId = Environment.GetEnvironmentVariable(SlackNotificationsConstants.AwsAccountId) ??
                           "Not provided in service"
        }, ct);
    }

    public async Task HandleThirdPartyException(IStepInputBase stepInput, string stepName, HttpClientRequestException ex, CancellationToken ct)
    {
        await PatchCreditApplicationOnError(stepInput, stepName, ex.GetStatusNoteFromException(), ct);
        await PatchScheduledStepOnError(stepInput, stepName, ct);

        var executionType = string.IsNullOrEmpty(stepInput.ScheduledUpdate) ? DecisionEngineStepConstants.CreditApplication : DecisionEngineStepConstants.Scheduled;
        var source = $"TraceId: {stepInput.ExecutionId} CreditApplicationId: {stepInput.CreditApplicationId} ExecutionType: {executionType}";
        await _errorNotificationService.Notify(new EventMessageBody
        {
            Message = $"{ex.Message}",
            EventLevel = EventLevel.Error,
            EventName = $"{ex.ServiceName} external request error",
            EventSource = source,
            ServiceName = SlackNotificationsConstants.DeProjectValue,
            TimeStamp = _dateProvider.CurrentDateTime.ToString(CultureInfo.InvariantCulture),
            AwsAccountId = Environment.GetEnvironmentVariable(SlackNotificationsConstants.AwsAccountId) ??
                           "Not provided in service"
        }, ct);
    }

    private async Task PatchCreditApplicationOnError(IStepInputBase stepInput, string stepName, string? statusNote, CancellationToken ct)
    {
        if (!string.IsNullOrEmpty(stepInput.ScheduledUpdate)) return;
        var executionId = stepInput.ExecutionId;
        var stepNameWithExecutionId = $"{executionId}:{stepName}";
        var updateModel = new UpdateCreditApplicationModel
        {
            NewStatus = "ExecutionFailed",
            AutomatedDecisionResult = null,
            UpdatedBy = stepNameWithExecutionId,
            ExecutionId = executionId,
            StatusNote = statusNote ?? "Decision Engine: issue occurred while processing. Please, try later and notify Engineering Team!",
            ShouldIgnoreCaching = false
        };
        await _onBoardingService.PatchCreditApplication(stepInput.CreditApplicationId, updateModel, ct);
    }

    private async Task PatchScheduledStepOnError(IStepInputBase stepInput, string stepName, CancellationToken ct)
    {
        if (string.IsNullOrEmpty(stepInput.ScheduledUpdate)) return;
        var stepNameWithExecutionId = $"{stepInput.ExecutionId}:{stepName}";

        var decisionSteps = await _onBoardingService.GetDecisionEngineStepsByExecutionId(stepInput.ExecutionId, ct);
        if (decisionSteps == null) return;

        var startedScheduledSteps = decisionSteps.Where(x => string.Equals(x.ExecutionType, "Scheduled", StringComparison.InvariantCultureIgnoreCase) &&
            string.Equals(x.Status, "started", StringComparison.InvariantCultureIgnoreCase));

        if (!startedScheduledSteps.Any()) return;

        await startedScheduledSteps.ForEachAsync(async x =>
        {
            await _onBoardingService.PatchDecisionEngineStep(x.Id!, new UpdateDecisionEngineStepModel()
            {
                NewStatus = "ExecutionFailed",
                UpdatedBy = stepNameWithExecutionId
            }, ct);
        });
    }
}