import {Browser, expect} from "@playwright/test";
import {Action} from "../database/autoTests/entities/AutoTestReport";
import {getPaymentRequestExist, getRequiredPaymentRequestById} from '../api/paynow/send-pay-now-request';
import {triggerPaymentScheduledJobWithDelay, transactionStatusUpdateJob} from '../api/paynow/queue-ivents';
import {DrawApprovalsRepository} from "../database/drawApplication/drawApprovalsRepository";
import {BackOfficeClient} from "../api/back-office-decision-engine/backOfficeClient";
import {ApproveDrawApplicationRequest, AttachSupplierToDrawApplicationRequest} from "../api/back-office-decision-engine/requests/approveDrawApplicationRequest";
import {DrawApprovalStatusEnum} from "../api/back-office-decision-engine/drawApprovalStatusEnum";
import {BaseTest} from "../tests/test-utils";
import {Decimal128} from "mongodb";

export class BackOfficeService 
{
    private _drawApprovalsRepository: DrawApprovalsRepository
    private _backOfficeClient: BackOfficeClient;
    
    constructor(drawApprovalsRepository: DrawApprovalsRepository, backOfficeClient: BackOfficeClient) {
        this._drawApprovalsRepository = drawApprovalsRepository
        this._backOfficeClient = backOfficeClient;
    }

async approveDrawApprovalViaBackOffice(invoiceId: string, browser: Browser, actionsPerTest: Action[], adminIdToken: any)
{
    actionsPerTest.push({description: `Verify that drawApproval existing and its status is Processed. Other statuses can not be approved`})
    
    let drawApproval;
    let attempts = 0;
    const maxAttempts = 4;
    
    while (attempts < maxAttempts) {
        drawApproval = await this._drawApprovalsRepository.getDrawApprovalByInvoiceId(invoiceId);
        
        if (drawApproval?.status === DrawApprovalStatusEnum[DrawApprovalStatusEnum.Processed]) {
            break;
        }
        
        if (attempts === maxAttempts - 1) {
            break;
        }
        
        await BaseTest.delayOperation(5000); // 5 seconds delay
        attempts++;
    }

    expect(drawApproval, `Unable to find draw Approval for invoice: ${invoiceId}`).not.toBeUndefined();
    expect(drawApproval, `Unable to find draw Approval for invoice: ${invoiceId}`).not.toBeNull();
    expect(drawApproval.status, `Expect draw status is Processed, but it is: ${drawApproval.status}`)
        .toEqual(DrawApprovalStatusEnum[DrawApprovalStatusEnum.Processed]);
    
    const approvalRequest :ApproveDrawApplicationRequest = {
        approvedCreditLimit: parseFloat(drawApproval.drawAmount.toString()),
        code: "",
        note: "Auto test approval",
        debtInvestor: "Arcadia",
        newStatus: DrawApprovalStatusEnum[DrawApprovalStatusEnum.Approved]
    }

    actionsPerTest.push({description: `Try to approve draw application by id: ${drawApproval._id.toString()}`})
    const approvalResponse = await this._backOfficeClient
        .approveDrawApplication(drawApproval._id.toString(), approvalRequest, adminIdToken)
    expect(approvalResponse.status, 'Expected draw approval response status code 200')
        .toEqual(200);

    await BaseTest.delayOperation(10000)

    actionsPerTest.push({description: `Get DrawApproval from database by invoiceId, and verify that new status is Approved`})
    drawApproval = await this._drawApprovalsRepository.getDrawApprovalByInvoiceId(invoiceId)
    expect(drawApproval, `Unable to find draw Approval for invoice: ${invoiceId}`).toBeDefined()
    expect(drawApproval.status, "Expect draw status is pending").toEqual(DrawApprovalStatusEnum[DrawApprovalStatusEnum.Approved])
}

async attachSupplierDrawApprovalViaBackOffice(supplierId: string, invoiceId: string, browser: Browser, actionsPerTest: Action[], adminIdToken: any)
{
    actionsPerTest.push({description: `Verify that drawApproval existing and its status is Processed. Other statuses can not be approved`})
    
    let drawApproval;
    let attempts = 0;
    const maxAttempts = 4;
    
    while (attempts < maxAttempts) {
        drawApproval = await this._drawApprovalsRepository.getDrawApprovalByInvoiceId(invoiceId);
        
        if (drawApproval?.status === DrawApprovalStatusEnum[DrawApprovalStatusEnum.Processed]) {
            break;
        }
        
        if (attempts === maxAttempts - 1) {
            break;
        }
        
        await BaseTest.delayOperation(5000); // 5 seconds delay
        attempts++;
    }

    expect(drawApproval, `Unable to find draw Approval for invoice: ${invoiceId}`).not.toBeUndefined();
    expect(drawApproval, `Unable to find draw Approval for invoice: ${invoiceId}`).not.toBeNull();
    expect(drawApproval.status, `Expect draw status is Processed, but it is: ${drawApproval.status}`)
        .toEqual(DrawApprovalStatusEnum[DrawApprovalStatusEnum.Processed]);
    
    const approvalRequest :AttachSupplierToDrawApplicationRequest = {
        supplierId: supplierId
    }

    actionsPerTest.push({description: `Try to attach supplier to the draw application by id: ${drawApproval._id.toString()}`})
    const approvalResponse = await this._backOfficeClient
        .attachSupplierToDrawApplication(drawApproval._id.toString(), approvalRequest, adminIdToken)
    expect(approvalResponse.status, 'Expected draw approval response status code 200')
        .toEqual(200);
}
}