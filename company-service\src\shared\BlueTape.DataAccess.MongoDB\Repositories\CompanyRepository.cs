﻿using AutoMapper;
using BlueTape.Company.API.DataAccess;
using BlueTape.Company.Domain.DTOs.Companies;
using BlueTape.Company.Domain.DTOs.GuestSuppliers;
using BlueTape.CompanyService.Common.Enums;
using BlueTape.CompanyService.Companies;
using BlueTape.CompanyService.Enums;
using BlueTape.CompanyService.GuestSuppliers;
using BlueTape.DataAccess.MongoDB.Abstractions;
using BlueTape.DataAccess.MongoDB.Entities.Company;
using BlueTape.DataAccess.MongoDB.Extensions;
using BlueTape.DataAccess.MongoDB.Pipelines;
using BlueTape.Utilities.Enums;
using MongoDB.Bson;
using MongoDB.Driver;
using System.Diagnostics.CodeAnalysis;
using BlueTape.Common.Exceptions.Companies;

namespace BlueTape.DataAccess.MongoDB.Repositories;

[ExcludeFromCodeCoverage]
public class CompanyRepository(CompanyDbContext dbContext, IMapper mapper) : ICompanyRepository
{
    private readonly CompanyDbContext _dbContext = dbContext ?? throw new ArgumentNullException(nameof(dbContext));
    private readonly IMapper _mapper = mapper ?? throw new ArgumentNullException(nameof(mapper));

    public async Task<CompanyDto?> GetByCompanyId(string companyId, CancellationToken cancellationToken)
    {
        var company = await _dbContext.Companies
            .Find(x => x.BlueTapeCompanyId.Equals(companyId))
            .FirstOrDefaultAsync(cancellationToken);

        return _mapper.Map<CompanyDto>(company);
    }

    public async Task<CompanyDto?> GetByBankAccountId(string bankAccountId, CancellationToken cancellationToken)
    {
        var company = await _dbContext.Companies
            .Find(x => x.BankAccounts.Contains(ObjectId.Parse(bankAccountId)))
            .FirstOrDefaultAsync(cancellationToken);

        return _mapper.Map<CompanyDto>(company);
    }

    public async Task<CompanyDto[]?> GetByCompanyIds(string[] companyIds, CancellationToken cancellationToken)
    {
        var expression = Builders<CompanyEntity>.Filter;
        var filterDefinition = expression.In(x => x.BlueTapeCompanyId, companyIds);
        var filteredPipeline = BuildFilteredLookupQuery(filterDefinition);

        var companies =
            await (await _dbContext.Companies.AggregateAsync(filteredPipeline, cancellationToken: cancellationToken))
                .ToListAsync(cancellationToken);

        return _mapper.Map<CompanyDto[]>(companies);
    }

    public async Task<ResultWithPaginationDto<CompanyDto>> GetByFiltersWithPagination(CompanyQueryPaginated query,
        CancellationToken cancellationToken)
    {
        var pageSize = query.PageSize <= 0 ? 1 : query.PageSize;
        var pageNumber = query.PageNumber <= 0 ? 1 : query.PageNumber;

        var filter = BuildFilterDefinition(query);
        var filteredPipeline = BuildFilteredLookupQuery(filter);
        var sortDefinition = BuildSortDefinition(query);

        var countPipeline = filteredPipeline.Count();
        var countResult =
            await _dbContext.Companies.AggregateAsync(countPipeline, cancellationToken: cancellationToken);
        var count = (await countResult.FirstOrDefaultAsync(cancellationToken))?.Count ?? 0;
        var pipeline = filteredPipeline.Sort(sortDefinition).Skip((pageNumber - 1) * pageSize).Limit(pageSize);

        var paginatedCompaniesResult =
            await _dbContext.Companies.AggregateAsync(pipeline, cancellationToken: cancellationToken);
        var result = await paginatedCompaniesResult.ToListAsync(cancellationToken);

        return new()
        {
            PageNumber = pageNumber,
            TotalCount = count,
            PagesCount = (long)Math.Ceiling(count / (decimal)pageSize),
            Result = mapper.Map<List<CompanyDto>>(result)
        };
    }

    public async Task<ResultWithPaginationDto<GuestSupplierDto>> GetGuestSupplierByFiltersWithPagination(
        GuestSupplierQueryPaginated query, CancellationToken cancellationToken)
    {
        var pageSize = query.PageSize <= 0 ? 1 : query.PageSize;
        var pageNumber = query.PageNumber <= 0 ? 1 : query.PageNumber;

        var filter = BuildFilterDefinition(query);
        var lookupOwnerPipeline = BuildGuestSupplierPipeline();
        var filteredPipeline = lookupOwnerPipeline.Match(filter);

        var countPipeline = filteredPipeline.Count();
        var countResult =
            await _dbContext.Companies.AggregateAsync(countPipeline, cancellationToken: cancellationToken);
        var count = (await countResult.FirstOrDefaultAsync(cancellationToken))?.Count ?? 0;

        var sorter = BuildSortDefinition(query);
        var pipeline = filteredPipeline.Sort(sorter)
            .Skip((pageNumber - 1) * pageSize).Limit(pageSize);
        var paginatedCompaniesResult =
            await _dbContext.Companies.AggregateAsync(pipeline, cancellationToken: cancellationToken);
        var result = await paginatedCompaniesResult.ToListAsync(cancellationToken);

        return new()
        {
            PageNumber = pageNumber,
            TotalCount = count,
            PagesCount = (long)Math.Ceiling(count / (decimal)pageSize),
            Result = mapper.Map<List<GuestSupplierDto>>(result)
        };
    }

    public async Task<ResultWithPaginationDto<GuestSupplierInvoiceDto>> GetGuestSuppliersInvoicesAsync(
        string companyId,
        GuestSupplierInvoiceQueryPaginated query,
        CancellationToken cancellationToken)
    {
        var invoices = await _dbContext.Invoice.Find(x =>
                x.CompanyId.Equals(companyId)
                && (!query.DueDateFrom.HasValue || x.InvoiceDueDate >= query.DueDateFrom)
                && (!query.DueDateTo.HasValue || x.InvoiceDueDate <= query.DueDateTo))
            .ToListAsync(cancellationToken);

        var payerIds = invoices.Select(x => x.PayerId)
            .Where(x => !string.IsNullOrWhiteSpace(x)).Distinct().ToArray();
        var companies = (await _dbContext.Companies.Find(x => payerIds.Contains(x.BlueTapeCompanyId))
            .ToListAsync(cancellationToken)).ToDictionary(x => x.BlueTapeCompanyId, x => x.Name ?? x.LegalName);

        var result = invoices.Select(x =>
        {
            var dto = new GuestSupplierInvoiceDto
            {
                InvoiceNumber = x.InvoiceNumber,
                DueDate = x.InvoiceDueDate,
                Amount = x.TotalAmount,
                Status = x.Status,
                BusinessName = companies.TryGetValue(x.PayerId, out var company) ? company : null
            };
            return dto;
        }).ToArray();

        if (!string.IsNullOrEmpty(query.Search))
        {
            result = result.Where(x =>
                    (x.InvoiceNumber != null && x.InvoiceNumber.Contains(query.Search))
                    || (x.BusinessName != null && x.BusinessName.Contains(query.Search)))
                .ToArray();
        }

        result = SortByQuery(result, query);
        var total = result.Length;
        result = result.Skip(query.PageSize * (query.PageNumber - 1)).Take(query.PageSize).ToArray();

        return new()
        {
            PageNumber = query.PageNumber,
            TotalCount = total,
            PagesCount = (long)Math.Ceiling(total / (decimal)query.PageSize),
            Result = result
        };
    }

    public Task<List<string>> GetAllIds(CancellationToken ct)
        => _dbContext.Companies.Find(_ => true)
            .Project(x => x.BlueTapeCompanyId)
            .ToListAsync(ct);

    public async Task AddBankAccountIds(string companyId, IEnumerable<string> bankAccountIds, CancellationToken cancellationToken)
    {
        var company = await _dbContext.Companies
            .Find(x => x.BlueTapeCompanyId.Equals(companyId))
            .FirstOrDefaultAsync(cancellationToken);

        var ids = bankAccountIds.Select(ObjectId.Parse).ToList();

        company.BankAccounts.AddRange(ids);

        var filter = Builders<CompanyEntity>.Filter.Where(x => x.BlueTapeCompanyId == companyId);
        await _dbContext.Companies.UpdateOneAsync(filter, BuildUpdateGeneralDefinitions(company.BankAccounts),
            cancellationToken: cancellationToken);
    }

    public Task UpdateAionSettingsAsync(CompanyDto company, CancellationToken cancellationToken)
    {
        var filter = Builders<CompanyEntity>
            .Filter.Where(x => x.BlueTapeCompanyId == company.BlueTapeCompanyId);

        var aionSettings = _mapper.Map<CompanyAionSettingsEntity>(company.AionSettings);

        var update = Builders<CompanyEntity>.Update
            .Set(inv => inv.AionSettings, aionSettings);

        return _dbContext.GetCollection<CompanyEntity>()
            .UpdateOneAsync(filter, update, cancellationToken: cancellationToken);
    }

    public Task UpdatePublicIdentifierAsync(string companyId, string companyIdentifier, CancellationToken cancellationToken)
    {
        var filter = Builders<CompanyEntity>
            .Filter.Where(x => x.BlueTapeCompanyId == companyId);

        if (string.IsNullOrEmpty(companyIdentifier)) return Task.CompletedTask;

        var updateBuilder = new UpdateDefinitionBuilder<CompanyEntity>();
        var updateDefinition = updateBuilder
            .Set(x => x.UpdatedAt, DateTime.UtcNow)
            .Set(x => x.PublicIdentifier, companyIdentifier);

        return _dbContext.Companies.UpdateOneAsync(filter, updateDefinition, cancellationToken: cancellationToken);
    }

    public async Task<CompanyDto?> CreateCompany(CreateCompanyDto companyDto, CancellationToken cancellationToken)
    {
        ArgumentNullException.ThrowIfNull(companyDto);

        // Map CreateCompanyDto to CompanyEntity
        var companyEntity = _mapper.Map<CompanyEntity>(companyDto);

        // Assign additional values (e.g., BlueTapeCompanyId and timestamps)
        companyEntity.BlueTapeCompanyId = ObjectId.GenerateNewId().ToString(); // Generate a unique ID
        companyEntity.CreatedAt = DateTime.UtcNow;
        companyEntity.UpdatedAt = DateTime.UtcNow;

        // Insert the entity into the database
        await _dbContext.Companies.InsertOneAsync(companyEntity, cancellationToken: cancellationToken);

        // Map the inserted CompanyEntity back to CompanyDto
        var createdCompany = _mapper.Map<CompanyDto>(companyEntity);

        return createdCompany;
    }

    public async Task<CompanyDto?> UpdateCompany(
        string blueTapeCompanyId,
        UpdateCompanyDto companyUpdateDto,
        CancellationToken cancellationToken)
    {
        ArgumentNullException.ThrowIfNull(companyUpdateDto);

        var existingCompany = await _dbContext.Companies
            .Find(x => x.BlueTapeCompanyId == blueTapeCompanyId)
            .FirstOrDefaultAsync(cancellationToken);

        if (existingCompany == null)
            throw new CompanyDoesNotExistException(blueTapeCompanyId);

        var mappedEntity = _mapper.Map<CompanyEntity>(companyUpdateDto);

        var filter = Builders<CompanyEntity>.Filter
            .Where(x => x.BlueTapeCompanyId == blueTapeCompanyId);

        var update = Builders<CompanyEntity>.Update
            .Set(x => x.UpdatedAt, DateTime.UtcNow);

        // Basic properties
        if (mappedEntity.Type.HasValue)
            update = update.Set(x => x.Type, mappedEntity.Type);
        if (mappedEntity.Status.HasValue)
            update = update.Set(x => x.Status, mappedEntity.Status);
        if (mappedEntity.IsGuest.HasValue)
            update = update.Set(x => x.IsGuest, mappedEntity.IsGuest);
        if (!string.IsNullOrEmpty(mappedEntity.Name))
            update = update.Set(x => x.Name, mappedEntity.Name);
        if (!string.IsNullOrEmpty(mappedEntity.Entity))
            update = update.Set(x => x.Entity, mappedEntity.Entity);
        if (!string.IsNullOrEmpty(mappedEntity.LegalName))
            update = update.Set(x => x.LegalName, mappedEntity.LegalName);
        if (!string.IsNullOrEmpty(mappedEntity.ContactName))
            update = update.Set(x => x.ContactName, mappedEntity.ContactName);
        if (!string.IsNullOrEmpty(mappedEntity.Website))
            update = update.Set(x => x.Website, mappedEntity.Website);
        if (!string.IsNullOrEmpty(mappedEntity.Phone))
            update = update.Set(x => x.Phone, mappedEntity.Phone);
        if (!string.IsNullOrEmpty(mappedEntity.Email))
            update = update.Set(x => x.Email, mappedEntity.Email);
        if (mappedEntity.BankAccounts is { Count: > 0 })
            update = update.Set(x => x.BankAccounts, mappedEntity.BankAccounts);
        if (mappedEntity.Address != null)
            update = UpdateAddressSettings(update, existingCompany, mappedEntity.Address);
        if (mappedEntity.Credit != null)
            update = UpdateCompanyCredit(update, existingCompany, mappedEntity.Credit);
        if (mappedEntity.Settings != null)
            update = UpdateCompanySettings(update, existingCompany, mappedEntity.Settings);

        var updateResult = await _dbContext.Companies
            .UpdateOneAsync(filter, update, cancellationToken: cancellationToken);

        if (updateResult.MatchedCount == 0)
            throw new InvalidOperationException($"No company was updated with ID {blueTapeCompanyId}");

        var updatedCompany = await _dbContext.Companies
            .Find(x => x.BlueTapeCompanyId == blueTapeCompanyId)
            .FirstOrDefaultAsync(cancellationToken);

        return _mapper.Map<CompanyDto>(updatedCompany);
    }

    private static UpdateDefinition<CompanyEntity> UpdateCompanyCredit(
        UpdateDefinition<CompanyEntity> update,
        CompanyEntity existingCompany,
        CompanyCreditEntity mappedCredit)
    {
        if (existingCompany.Credit == null)
        {
            update = update.Set(x => x.Credit, mappedCredit);
            return update;
        }
        if (mappedCredit.Limit.HasValue)
            update = update.Set(x => x.Credit!.Limit, mappedCredit.Limit);

        return update;
    }

    private static UpdateDefinition<CompanyEntity> UpdateCompanySettings(
        UpdateDefinition<CompanyEntity> update,
        CompanyEntity existingCompany,
        CompanySettingsEntity mappedSettings)
    {
        if (existingCompany.Settings == null)
        {
            update = update.Set(x => x.Settings, mappedSettings);
            return update;
        }

        if (!string.IsNullOrWhiteSpace(mappedSettings.CardPricingPackageId))
            update = update.Set(x => x.Settings!.CardPricingPackageId, mappedSettings.CardPricingPackageId);
        if (!string.IsNullOrWhiteSpace(mappedSettings.LoanPricingPackageId))
            update = update.Set(x => x.Settings!.LoanPricingPackageId, mappedSettings.LoanPricingPackageId);
        if (mappedSettings.OnBoardingType is { Length: > 0 })
            update = update.Set(x => x.Settings!.OnBoardingType, mappedSettings.OnBoardingType);
        if (mappedSettings.ApproveRead.HasValue)
            update = update.Set(x => x.Settings!.ApproveRead, mappedSettings.ApproveRead);
        if (mappedSettings.Onboarded.HasValue)
            update = update.Set(x => x.Settings!.Onboarded, mappedSettings.Onboarded);
        if (!string.IsNullOrWhiteSpace(mappedSettings.InvitedBy))
            update = update.Set(x => x.Settings!.InvitedBy, mappedSettings.InvitedBy);
        if (mappedSettings.LoanPlans is { Length: > 0 })
            update = update.Set(x => x.Settings!.LoanPlans, mappedSettings.LoanPlans);
        if (mappedSettings.InvoiceLoanPlans is { Length: > 0 })
            update = update.Set(x => x.Settings!.InvoiceLoanPlans, mappedSettings.InvoiceLoanPlans);
        if (!string.IsNullOrWhiteSpace(mappedSettings.BusinessType))
            update = update.Set(x => x.Settings!.BusinessType, mappedSettings.BusinessType);
        if (mappedSettings.AcceptAchPayment.HasValue)
            update = update.Set(x => x.Settings!.AcceptAchPayment, mappedSettings.AcceptAchPayment);
        if (mappedSettings.AchDelayDisabled.HasValue)
            update = update.Set(x => x.Settings!.AchDelayDisabled, mappedSettings.AchDelayDisabled);
        if (!string.IsNullOrWhiteSpace(mappedSettings.OCRModelId))
            update = update.Set(x => x.Settings!.OCRModelId, mappedSettings.OCRModelId);
        if (mappedSettings.CanEditAuthorization.HasValue)
            update = update.Set(x => x.Settings!.CanEditAuthorization, mappedSettings.CanEditAuthorization);
        if (mappedSettings.CanPostTransactions.HasValue)
            update = update.Set(x => x.Settings!.CanPostTransactions, mappedSettings.CanPostTransactions);
        if (mappedSettings.CanUploadInvoice.HasValue)
            update = update.Set(x => x.Settings!.CanUploadInvoice, mappedSettings.CanUploadInvoice);
        if (mappedSettings.DueDay.HasValue)
            update = update.Set(x => x.Settings!.DueDay, mappedSettings.DueDay);
        if (mappedSettings.SendFinalPaymentWhenLoanIsPaid.HasValue)
            update = update.Set(x => x.Settings!.SendFinalPaymentWhenLoanIsPaid, mappedSettings.SendFinalPaymentWhenLoanIsPaid);
        if (mappedSettings.SupplierCanPay.HasValue)
            update = update.Set(x => x.Settings!.SupplierCanPay, mappedSettings.SupplierCanPay);
        if (mappedSettings.TutorialViewed.HasValue)
            update = update.Set(x => x.Settings!.TutorialViewed, mappedSettings.TutorialViewed);
        if (mappedSettings.WelcomeViewed.HasValue)
            update = update.Set(x => x.Settings!.WelcomeViewed, mappedSettings.WelcomeViewed);
        if (mappedSettings.DefaultDebtInvestorTradeCredit.HasValue)
            update = update.Set(x => x.Settings!.DefaultDebtInvestorTradeCredit, mappedSettings.DefaultDebtInvestorTradeCredit);
        if (mappedSettings.ArAdvance != null)
            update = UpdateArAdvanceSettings(update, existingCompany, mappedSettings.ArAdvance);
        if (mappedSettings.Repayment != null)
            update = UpdateRepaymentSettings(update, existingCompany, mappedSettings.Repayment);
        if (mappedSettings.Email != null)
            update = UpdateEmailSettings(update, existingCompany, mappedSettings.Email);
        if (mappedSettings.AutomatedDrawApproval != null)
            update = UpdateAutomatedDrawApprovalSettings(update, existingCompany, mappedSettings.AutomatedDrawApproval);
        if (mappedSettings.DepositDetails != null)
            update = UpdateDepositSettings(update, existingCompany, mappedSettings.DepositDetails);
        if (mappedSettings.DownPaymentDetails != null)
            update = UpdateDownPaymentSettings(update, existingCompany, mappedSettings.DownPaymentDetails);
        if (mappedSettings.DirectTerms != null)
            update = UpdateDirectTermsSettings(update, existingCompany, mappedSettings.DirectTerms);
        if (mappedSettings.InHouseCredit != null)
            update = UpdateInHouseCreditSettings(update, existingCompany, mappedSettings.InHouseCredit);

        return update;
    }

    private static UpdateDefinition<CompanyEntity> UpdateArAdvanceSettings(
        UpdateDefinition<CompanyEntity> update,
        CompanyEntity existingCompany,
        ArAdvanceEntity arAdvance)
    {
        if (existingCompany.Settings?.ArAdvance == null)
        {
            update = update.Set(x => x.Settings!.ArAdvance, arAdvance);
            return update;
        }

        if (arAdvance.IsEnabled.HasValue)
            update = update.Set(x => x.Settings!.ArAdvance!.IsEnabled, arAdvance.IsEnabled);
        if (arAdvance.MerchantLimit.HasValue)
            update = update.Set(x => x.Settings!.ArAdvance!.MerchantLimit, arAdvance.MerchantLimit);
        if (arAdvance.DefaultCustomerLimit.HasValue)
            update = update.Set(x => x.Settings!.ArAdvance!.DefaultCustomerLimit, arAdvance.DefaultCustomerLimit);
        if (!string.IsNullOrWhiteSpace(arAdvance.DefaultFactoringTerm))
            update = update.Set(x => x.Settings!.ArAdvance!.DefaultFactoringTerm, arAdvance.DefaultFactoringTerm);
        if (!string.IsNullOrWhiteSpace(arAdvance.DefaultSupplierPackage))
            update = update.Set(x => x.Settings!.ArAdvance!.DefaultSupplierPackage, arAdvance.DefaultSupplierPackage);
        update = update.Set(x => x.Settings!.ArAdvance!.DefaultDebtInvestor, arAdvance.DefaultDebtInvestor);

        return update;
    }

    private static UpdateDefinition<CompanyEntity> UpdateRepaymentSettings(
        UpdateDefinition<CompanyEntity> update,
        CompanyEntity existingCompany,
        RepaymentEntity repayment)
    {
        if (existingCompany.Settings?.Repayment == null)
        {
            update = update.Set(x => x.Settings!.Repayment, repayment);
            return update;
        }

        if (repayment.Repays.HasValue)
            update = update.Set(x => x.Settings!.Repayment!.Repays, repayment.Repays);
        if (!string.IsNullOrWhiteSpace(repayment.NameOnAccount))
            update = update.Set(x => x.Settings!.Repayment!.NameOnAccount, repayment.NameOnAccount);
        if (!string.IsNullOrWhiteSpace(repayment.RoutingNumber))
            update = update.Set(x => x.Settings!.Repayment!.RoutingNumber, repayment.RoutingNumber);
        if (!string.IsNullOrWhiteSpace(repayment.AccountNumber))
            update = update.Set(x => x.Settings!.Repayment!.AccountNumber, repayment.AccountNumber);
        if (repayment.AutoApprove.HasValue)
            update = update.Set(x => x.Settings!.Repayment!.AutoApprove, repayment.AutoApprove);
        if (!string.IsNullOrWhiteSpace(repayment.PaymentPlan))
            update = update.Set(x => x.Settings!.Repayment!.PaymentPlan, repayment.PaymentPlan);
        if (repayment.MaxInvoiceAmount.HasValue)
            update = update.Set(x => x.Settings!.Repayment!.MaxInvoiceAmount, repayment.MaxInvoiceAmount);
        if (repayment.AutoApproveType.HasValue)
            update = update.Set(x => x.Settings!.Repayment!.AutoApproveType, repayment.AutoApproveType);

        return update;
    }

    private static UpdateDefinition<CompanyEntity> UpdateEmailSettings(
        UpdateDefinition<CompanyEntity> update,
        CompanyEntity existingCompany,
        EmailConfigurationEntity email)
    {
        if (existingCompany.Settings?.Email == null)
        {
            update = update.Set(x => x.Settings!.Email, email);
            return update;
        }

        // Update individual email configuration properties
        if (!string.IsNullOrWhiteSpace(email.SenderEmail))
            update = update.Set(x => x.Settings!.Email!.SenderEmail, email.SenderEmail);
        if (!string.IsNullOrWhiteSpace(email.SendInvitationTemplate))
            update = update.Set(x => x.Settings!.Email!.SendInvitationTemplate, email.SendInvitationTemplate);
        if (!string.IsNullOrWhiteSpace(email.SendInvoiceTemplate))
            update = update.Set(x => x.Settings!.Email!.SendInvoiceTemplate, email.SendInvoiceTemplate);

        return update;
    }

    private static UpdateDefinition<CompanyEntity> UpdateAutomatedDrawApprovalSettings(
        UpdateDefinition<CompanyEntity> update,
        CompanyEntity existingCompany,
        AutomatedDrawApprovalEntity automatedDrawApproval)
    {
        if (existingCompany.Settings?.AutomatedDrawApproval == null)
        {
            automatedDrawApproval.LastUpdatedAt = DateTime.UtcNow;
            update = update.Set(x => x.Settings!.AutomatedDrawApproval, automatedDrawApproval);
            return update;
        }

        if (automatedDrawApproval.IsEnabled.HasValue)
            update = update.Set(x => x.Settings!.AutomatedDrawApproval!.IsEnabled, automatedDrawApproval.IsEnabled);
        if (automatedDrawApproval.DrawLimit.HasValue)
            update = update.Set(x => x.Settings!.AutomatedDrawApproval!.DrawLimit, automatedDrawApproval.DrawLimit);
        if (automatedDrawApproval.DailyAmountLimit.HasValue)
            update = update.Set(x => x.Settings!.AutomatedDrawApproval!.DailyAmountLimit, automatedDrawApproval.DailyAmountLimit);
        if (automatedDrawApproval.WeeklyAmountLimit.HasValue)
            update = update.Set(x => x.Settings!.AutomatedDrawApproval!.WeeklyAmountLimit, automatedDrawApproval.WeeklyAmountLimit);
        if (automatedDrawApproval.CreditLimitPercentage.HasValue)
            update = update.Set(x => x.Settings!.AutomatedDrawApproval!.CreditLimitPercentage, automatedDrawApproval.CreditLimitPercentage);
        if (!string.IsNullOrWhiteSpace(automatedDrawApproval.LastUpdatedBy))
            update = update.Set(x => x.Settings!.AutomatedDrawApproval!.LastUpdatedBy, automatedDrawApproval.LastUpdatedBy);
        update = update.Set(x => x.Settings!.AutomatedDrawApproval!.LastUpdatedAt, DateTime.UtcNow);

        return update;
    }

    private static UpdateDefinition<CompanyEntity> UpdateDepositSettings(
        UpdateDefinition<CompanyEntity> update,
        CompanyEntity existingCompany,
        DepositDetailsEntity depositDetails)
    {
        if (existingCompany.Settings?.DepositDetails == null)
        {
            update = update.Set(x => x.Settings!.DepositDetails, depositDetails);
            return update;
        }

        if (existingCompany.Settings!.DepositDetails.IsSecured != depositDetails.IsSecured)
            update = update.Set(x => x.Settings!.DepositDetails!.IsSecured, depositDetails.IsSecured);
        if (depositDetails.IsDepositPaid.HasValue)
            update = update.Set(x => x.Settings!.DepositDetails!.IsDepositPaid, depositDetails.IsDepositPaid);
        if (depositDetails.DepositAmount.HasValue)
            update = update.Set(x => x.Settings!.DepositDetails!.DepositAmount, depositDetails.DepositAmount);

        return update;
    }

    private static UpdateDefinition<CompanyEntity> UpdateDownPaymentSettings(
        UpdateDefinition<CompanyEntity> update,
        CompanyEntity existingCompany,
        DownPaymentDetailsEntity downPaymentDetails)
    {
        if (existingCompany.Settings?.DownPaymentDetails == null)
        {
            update = update.Set(x => x.Settings!.DownPaymentDetails, downPaymentDetails);
            return update;
        }

        update = update.Set(x => x.Settings!.DownPaymentDetails!.IsRequired, downPaymentDetails.IsRequired);
        
        if (downPaymentDetails.DownPaymentPercentage.HasValue)
            update = update.Set(x => x.Settings!.DownPaymentDetails!.DownPaymentPercentage, downPaymentDetails.DownPaymentPercentage);
        
        if (downPaymentDetails.ExpireDays.HasValue)
            update = update.Set(x => x.Settings!.DownPaymentDetails!.ExpireDays, downPaymentDetails.ExpireDays);

        return update;
    }

    private static UpdateDefinition<CompanyEntity> UpdateDirectTermsSettings(
        UpdateDefinition<CompanyEntity> update,
        CompanyEntity existingCompany,
        DirectTermsEntity directTerms)
    {
        if (existingCompany.Settings?.DirectTerms == null)
        {
            update = update.Set(x => x.Settings!.DirectTerms, directTerms);
            return update;
        }

        if (directTerms.LoanPlans is { Length: > 0 })
            update = update.Set(x => x.Settings!.DirectTerms!.LoanPlans, directTerms.LoanPlans);

        return update;
    }

    private static UpdateDefinition<CompanyEntity> UpdateInHouseCreditSettings(
        UpdateDefinition<CompanyEntity> update,
        CompanyEntity existingCompany,
        CompanyInHouseCreditEntity mappedSettingsInHouseCredit)
    {
        if (existingCompany.Settings?.InHouseCredit == null)
        {
            update = update.Set(x => x.Settings!.InHouseCredit, mappedSettingsInHouseCredit);
            return update;
        }
        if (mappedSettingsInHouseCredit.IsAutoPayRequired.HasValue)
            update = update.Set(x => x.Settings!.InHouseCredit!.IsAutoPayRequired, mappedSettingsInHouseCredit.IsAutoPayRequired);
        if (mappedSettingsInHouseCredit.IsAutoPayEnabledByCompanyUser.HasValue)
            update = update.Set(x => x.Settings!.InHouseCredit!.IsAutoPayEnabledByCompanyUser, mappedSettingsInHouseCredit.IsAutoPayEnabledByCompanyUser);
        return update;
    }

    private static UpdateDefinition<CompanyEntity> UpdateAddressSettings(
        UpdateDefinition<CompanyEntity> update,
        CompanyEntity existingCompany,
        AddressEntity address)
    {
        if (existingCompany.Address == null)
        {
            address.CreatedAt = DateTime.UtcNow;
            update = update.Set(x => x.Address, address);
            return update;
        }

        if (!string.IsNullOrWhiteSpace(address.Address))
            update = update.Set(x => x.Address!.Address, address.Address);
        if (!string.IsNullOrWhiteSpace(address.UnitNumber))
            update = update.Set(x => x.Address!.UnitNumber, address.UnitNumber);
        if (!string.IsNullOrWhiteSpace(address.City))
            update = update.Set(x => x.Address!.City, address.City);
        if (!string.IsNullOrWhiteSpace(address.State))
            update = update.Set(x => x.Address!.State, address.State);
        if (!string.IsNullOrWhiteSpace(address.Zip))
            update = update.Set(x => x.Address!.Zip, address.Zip);
        if (!string.IsNullOrWhiteSpace(address.Phone))
            update = update.Set(x => x.Address!.Phone, address.Phone);

        update = update.Set(x => x.Address!.UpdatedAt, DateTime.UtcNow);

        return update;
    }

    private static UpdateDefinition<CompanyEntity>? BuildUpdateGeneralDefinitions(List<ObjectId> bankAccountIds)
    {
        var updateDefinitions = new List<UpdateDefinition<CompanyEntity>>
        {
            Builders<CompanyEntity>.Update.Set(s => s.UpdatedAt, DateTime.UtcNow)
        };

        updateDefinitions
            .AppendSet(s => s.BankAccounts, bankAccountIds);

        return Builders<CompanyEntity>.Update.Combine(updateDefinitions);
    }

    private static PipelineDefinition<CompanyEntity, CompanyEntity> BuildFilteredLookupQuery(
        FilterDefinition<CompanyEntity> filterDefinition)
    {
        var lookupOwnerPipeline = BuildOwnerUserPipeline();

        return lookupOwnerPipeline.Match(filterDefinition);
    }

    private static PipelineDefinition<CompanyEntity, CompanyEntity> BuildOwnerUserPipeline()
    {
        var updatedPipeline = new EmptyPipelineDefinition<CompanyEntity>()
                .AppendStage(CompanyPipelines.LookupOwnerUserRoles)
                .AppendStage(CompanyPipelines.UnwindUserRole)
                .AppendStage(CompanyPipelines.LookupUser)
                .AppendStage(CompanyPipelines.AddOwnerField)
                .AppendStage(CompanyPipelines.AddOwnerContactNameField)
            ;

        return updatedPipeline;
    }

    private static PipelineDefinition<CompanyEntity, CompanyEntity> BuildGuestSupplierPipeline()
    {
        var updatedPipeline = new EmptyPipelineDefinition<CompanyEntity>()
            .AppendStage(CompanyPipelines.LookupOwnerUserRoles)
            .AppendStage(CompanyPipelines.LookupInvoicesWithAggregation)
            .AppendStage(CompanyPipelines.UnwindUserRole)
            .AppendStage(CompanyPipelines.LookupUser)
            .AppendStage(CompanyPipelines.AddOwnerField)
            .AppendStage(CompanyPipelines.AddOwnerContactNameField)
            .AppendStage(CompanyPipelines.CalculateInvoiceCount)
            .AppendStage(CompanyPipelines.CalculateInvoiceAmount);
        return updatedPipeline;
    }

    private static SortDefinition<CompanyEntity> BuildSortDefinition(CompanyQueryPaginated query)
    {
        var sortBuilder = Builders<CompanyEntity>.Sort;
        var sortDefinition = sortBuilder.Descending(x => x.CreatedAt);

        if (!query.SortBy.HasValue) return sortDefinition;


        var field = query.SortBy switch
        {
            AccountSortingParameter.businessName => "legalName",
            AccountSortingParameter.dba => "name",
            AccountSortingParameter.category => "settings.onboardingType",
            AccountSortingParameter.contactName => "owner.contactName",
            AccountSortingParameter.createdAt => "createdAt",
            _ => null
        };

        if (!string.IsNullOrEmpty(field))
        {
            sortDefinition = query.SortOrder == SortOrderType.asc
                ? sortBuilder.Ascending(field)
                : sortBuilder.Descending(field);
        }

        return sortDefinition;
    }

    private static SortDefinition<CompanyEntity> BuildSortDefinition(GuestSupplierQueryPaginated query)
    {
        var builder = Builders<CompanyEntity>.Sort;
        var sortOrder = GetSortOrderType(query.SortOrder);

        return query.SortBy switch
        {
            var sortBy when string.Equals(sortBy, nameof(GuestSupplierDto.BusinessName),
                    StringComparison.OrdinalIgnoreCase)
                => sortOrder == SortOrderType.asc
                    ? builder.Ascending(x => x.Name)
                    : builder.Descending(x => x.Name),

            var sortBy when string.Equals(sortBy, nameof(GuestSupplierDto.ContactName),
                    StringComparison.OrdinalIgnoreCase)
                => sortOrder == SortOrderType.asc
                    ? builder.Ascending(x => x.Owner!.ContactName)
                    : builder.Descending(x => x.Owner!.ContactName),

            _ => builder.Descending(x => x.CreatedAt)
        };
    }

    private static FilterDefinition<CompanyEntity> BuildFilterDefinition(CompanyQueryPaginated query)
    {
        var expression = Builders<CompanyEntity>.Filter;
        var filter = expression.Empty;
        var nameRegex = query.Name.BuildRegexDefinition();
        var searchRegex = query.Search.BuildRegexDefinition();

        if (!string.IsNullOrEmpty(query.Id))
            filter &= expression.Eq(x => x.BlueTapeCompanyId, query.Id);
        if (!string.IsNullOrEmpty(query.Name))
        {
            filter &= expression.Or(
                expression.Regex(x => x.Name, nameRegex),
                expression.Regex(x => x.LegalName, nameRegex),
                expression.Regex(x => x.Owner.ContactName, nameRegex)
            );
        }

        if (!string.IsNullOrEmpty(query.Search))
        {
            filter &= expression.Or(
                expression.Regex(x => x.Name, searchRegex),
                expression.Regex(x => x.LegalName, searchRegex),
                expression.Regex(x => x.Email, searchRegex),
                expression.Regex(x => x.Phone, searchRegex),
                expression.Regex(x => x.ContactName, searchRegex),
                expression.Regex(x => x.Owner.ContactName, searchRegex),
                expression.Regex(x => x.Owner.Email, searchRegex),
                expression.Regex(x => x.Owner.Phone, searchRegex)
            );
        }

        if (query.Type.HasValue)
        {
            // For a long time suppliers were with status: approved, which meant they have approved GetPaid application.
            // only suppliers have approved status, but no-one alive knows why it was done this way
            filter &= expression.Or(
                expression.Eq(x => x.Type, query.Type.Value),
                expression.Eq(x => x.Status, CompanyStatusEnum.Approved)
            );
        }

        if (query.IsGuest.HasValue)
            filter &= expression.Eq(x => x.IsGuest, query.IsGuest);

        //Some fields from filter are not present in mongodb
        return filter;
    }

    private static FilterDefinition<CompanyEntity> BuildFilterDefinition(GuestSupplierQueryPaginated query)
    {
        var expression = Builders<CompanyEntity>.Filter;
        var filter = expression.Empty;
        var nameRegex = query.Search.BuildRegexDefinition();

        if (!string.IsNullOrEmpty(query.Search))
        {
            filter &= expression.Or(
                expression.Regex(x => x.Name, nameRegex),
                expression.Regex(x => x.LegalName, nameRegex),
                expression.Regex(x => x.Owner!.ContactName, nameRegex),
                expression.Regex(x => x.Phone, nameRegex),
                expression.Regex(x => x.Email, nameRegex)
            );
        }

        filter &= expression.Eq(x => x.Type, CompanyTypeEnum.Supplier);
        filter &= expression.Eq(x => x.IsGuest, true);
        return filter;
    }

    private static GuestSupplierInvoiceDto[] SortByQuery(GuestSupplierInvoiceDto[] companies,
        GuestSupplierInvoiceQueryPaginated query)
    {
        var sortOrder = GetSortOrderType(query.SortOrder);

        var companiesSort = query.SortBy switch
        {
            var sortBy when string.Equals(sortBy, nameof(GuestSupplierInvoiceDto.BusinessName),
                    StringComparison.OrdinalIgnoreCase)
                => sortOrder == SortOrderType.asc
                    ? companies.OrderBy(x => x.BusinessName)
                    : companies.OrderByDescending(x => x.BusinessName),

            var sortBy when string.Equals(sortBy, nameof(GuestSupplierInvoiceDto.Amount),
                    StringComparison.OrdinalIgnoreCase)
                => sortOrder == SortOrderType.asc
                    ? companies.OrderBy(x => x.Amount)
                    : companies.OrderByDescending(x => x.Amount),

            var sortBy when string.Equals(sortBy, nameof(GuestSupplierInvoiceDto.DueDate),
                    StringComparison.OrdinalIgnoreCase)
                => sortOrder == SortOrderType.asc
                    ? companies.OrderBy(x => x.DueDate)
                    : companies.OrderByDescending(x => x.DueDate),

            var sortBy when string.Equals(sortBy, nameof(GuestSupplierInvoiceDto.InvoiceNumber),
                    StringComparison.OrdinalIgnoreCase)
                => sortOrder == SortOrderType.asc
                    ? companies.OrderBy(x => x.InvoiceNumber)
                    : companies.OrderByDescending(x => x.InvoiceNumber),

            _ => companies.OrderBy(x => x.BusinessName)
        };

        return companiesSort.ToArray();
    }

    private static SortOrderType GetSortOrderType(string? sortOrder)
    {
        var sortOrderType = sortOrder is null
                            || string.Equals(sortOrder, SortOrderType.asc.ToString(),
                                StringComparison.InvariantCultureIgnoreCase)
            ? SortOrderType.asc
            : SortOrderType.desc;
        return sortOrderType;
    }
}