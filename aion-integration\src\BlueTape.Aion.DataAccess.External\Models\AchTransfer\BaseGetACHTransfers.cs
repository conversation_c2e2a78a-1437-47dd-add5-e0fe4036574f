﻿using System.Runtime.Serialization;
using System.Text.Json.Serialization;

namespace BlueTape.Aion.DataAccess.External.Models.AchTransfer;

[DataContract]
public class BaseGetACHTransfers
{
    [JsonPropertyName("page")]
    public int Page { get; set; }

    [JsonPropertyName("size")]
    public int Size { get; set; }

    [JsonPropertyName("fromDate")]
    public string FromDate { get; set; } = null!;

    [JsonPropertyName("toDate")]
    public string? ToDate { get; set; }
}