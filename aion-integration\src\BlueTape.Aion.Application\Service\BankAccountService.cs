﻿using BlueTape.Aion.Application.Abstractions;
using BlueTape.Aion.Application.Constants;
using BlueTape.Aion.Application.Extensions;
using BlueTape.Aion.DataAccess.External.Abstractions;
using BlueTape.Aion.DataAccess.External.Models.TransferMethod;
using BlueTape.Aion.DataAccess.MongoDB.Abstractions;
using BlueTape.Aion.Domain.Constants;
using BlueTape.Aion.Domain.DTOs.BankAccount;
using BlueTape.Aion.Domain.DTOs.Company;
using BlueTape.Aion.Domain.Extensions;
using BlueTape.CompanyClient.Abstractions;
using BlueTape.Integrations.Aion.Infrastructure.Enums;
using BlueTape.Services.Utilities.AWS;
using BueTape.Aion.Infrastructure.Exceptions;
using BueTape.Aion.Infrastructure.Extensions;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;

namespace BlueTape.Aion.Application.Service;

public class BankAccountService : IBankAccountService
{
    private readonly IAionHttpClient _aionHttpClient;
    private readonly IBankAccountRepository _bankAccountRepository;
    private readonly ILogger<BankAccountService> _logger;
    private readonly IKmsDecryptorService _kmsDecryptorService;
    private readonly IConfiguration _configuration;
    private readonly ICompanyHttpClient _companyHttpClient;

    public BankAccountService(
        IAionHttpClient aionHttpClient,
        IBankAccountRepository bankAccountRepository,
        ILogger<BankAccountService> logger,
        IKmsDecryptorService kmsDecryptorService,
        IConfiguration configuration,
        ICompanyHttpClient companyHttpClient)
    {
        _aionHttpClient = aionHttpClient;
        _bankAccountRepository = bankAccountRepository;
        _logger = logger;
        _kmsDecryptorService = kmsDecryptorService;
        _configuration = configuration;
        _companyHttpClient = companyHttpClient;
    }

    public async Task<BankAccountDto> SynBankAccountWithAionAsync(string receiverBankAccountId, CompanyDto companyDto,
        string paymentSubscriptionType, AionPaymentMethodType paymentMethodType, CancellationToken ctx)
    {
        _logger.LogInformation("Start preprocessing AchPull receiverBankAccountId: {receiverBankAccountId}", receiverBankAccountId);

        var paymentSubscription = paymentSubscriptionType.ParseToPaymentSubscription();

        ArgumentNullException.ThrowIfNull(companyDto);
        ArgumentNullException.ThrowIfNull(companyDto.AionSettings);
        if (string.IsNullOrEmpty(companyDto.AionSettings.GetCounterPartyObjectId(paymentSubscription)))
            throw new ParameterRequiredException(nameof(companyDto.AionSettings.CounterPartyObjectId));

        var bankAccount = await _bankAccountRepository.GetByBankAccountIdAsync(receiverBankAccountId, ctx);

        if (bankAccount is null) throw new BankAccountDoesNotExistException(receiverBankAccountId);
        if (string.IsNullOrEmpty(bankAccount.AccountType))
            throw new ParameterRequiredException(nameof(bankAccount.AccountType));
        if (string.IsNullOrEmpty(bankAccount.RoutingNumber))
            throw new ParameterRequiredException(nameof(bankAccount.RoutingNumber));
        if (bankAccount.AccountNumber is null)
            throw new ParameterRequiredException(nameof(bankAccount.AccountNumber));
        if (string.IsNullOrEmpty(bankAccount.AccountNumber.Cipher))
            throw new ParameterRequiredException(nameof(bankAccount.AccountNumber.Cipher));

        var transferMethodId = bankAccount?.AionSettings?.GetTransferMethodId(paymentSubscription, paymentMethodType);

        if (bankAccount?.AionSettings is null || string.IsNullOrEmpty(transferMethodId))
        {
            _logger.LogInformation("Receiver aion settings does not exist. Start creation process");

            var aionOriginatorAccountId = _configuration[AionConstants.AionCollectionAccountId] ??
                                          throw new VariableNullException(nameof(AionConstants.AionCollectionAccountId));

            var decryptionResult = await _kmsDecryptorService.DecryptSingleAsync
                (CryptoConstants.AccountNumberKeyId, bankAccount.AccountNumber.Cipher, ctx);

            var routingNumber = bankAccount.RoutingNumber;
            
            if (Environment.GetEnvironmentVariable(EnvironmentConstants.Environment)!.IsEnvironmentNotProd())
            {
                routingNumber = "*********";
            }
            
            var bankData = new AddTransferMethodRequest
            {
                AccountId = aionOriginatorAccountId,
                CounterpartyId = companyDto.AionSettings.GetCounterPartyObjectId(paymentSubscription),
                NickName = bankAccount.Name ?? string.Empty,
                BankDetail = new BankDetail
                {
                    Type = GetBankAccountType(paymentMethodType),
                    AccountType = bankAccount.AccountType,
                    AccountNumber = decryptionResult.DecryptedText,
                    RoutingNumber = routingNumber
                }
            };
            
            if (paymentMethodType is AionPaymentMethodType.WIRE or AionPaymentMethodType.INSTANT)
            {
                if (bankAccount.Identity is null || string.IsNullOrEmpty(bankAccount.Identity.AddressLine1))
                {
                    var bankAddress = await _companyHttpClient.GetBankAccountIdentityAsync(bankAccount.BlueTapeBankAccountId, ctx);
                
                    bankData.BankDetail.AddressOnAccount = new AddressOnAccountRequest()
                    {
                        PostalCode = bankAddress?.PostalCode ?? string.Empty,
                        City = bankAddress?.City ?? string.Empty,
                        CountryCode = bankAddress?.CountryCode ?? string.Empty,
                        CountrySubDivisionCode = bankAddress?.CountrySubDivisionCode ?? string.Empty,
                        AddressLine1 = bankAddress?.AddressLine1 ?? string.Empty,
                        AddressLine2 = bankAddress?.AddressLine2 ?? string.Empty
                    };
                }
                else
                {
                    bankData.BankDetail.AddressOnAccount = new AddressOnAccountRequest()
                    {
                        AddressLine1 = bankAccount.Identity.AddressLine1 ?? string.Empty,
                        AddressLine2 = string.Empty,
                        City = bankAccount.Identity.City ?? string.Empty,
                        CountryCode = bankAccount.Identity.CountryCode ?? string.Empty,
                        CountrySubDivisionCode = bankAccount.Identity.CountrySubDivisionCode ?? string.Empty,
                        PostalCode = bankAccount.Identity.PostalCode ?? string.Empty
                    };
                }
                
            }
            
            var transferMethodResponse = await _aionHttpClient.AddTransferMethod(bankData, paymentSubscription, ctx);

            bankAccount.AionSettings = GetAccountAionSettingsDtoV2(transferMethodResponse, paymentMethodType, paymentSubscription);

            await _bankAccountRepository.UpdateAionSettingsAsync(bankAccount, ctx);
        }

        _logger.LogInformation(
            "Finish preprocessing AchPull receiverBankAccountId: {receiverBankAccountId}", receiverBankAccountId);

        return bankAccount;
    }
    
    private string GetBankAccountType (AionPaymentMethodType accountType)
    {
        return accountType switch
        {
            AionPaymentMethodType.ACH => "ACH",
            AionPaymentMethodType.WIRE => "Wire",
            AionPaymentMethodType.INSTANT => "Instant",
            _ => throw new ArgumentOutOfRangeException(nameof(accountType), accountType, null)
        };
    }
    
    private static BankAccountAionSettingsDto GetAccountAionSettingsDtoV2(
        AddTransferMethodResponse transferMethodResponse, 
        AionPaymentMethodType accountType, 
        PaymentSubscriptionType paymentSubscription)
    {
        return (paymentSubscription, accountType) switch
        {
            (PaymentSubscriptionType.SUBSCRIPTION1, AionPaymentMethodType.ACH) => new BankAccountAionSettingsDto
            {
                TransferMethodId = transferMethodResponse.TransferMethod.Id
            },
            (PaymentSubscriptionType.SUBSCRIPTION2, AionPaymentMethodType.ACH) => new BankAccountAionSettingsDto
            {
                TransferMethodId2 = transferMethodResponse.TransferMethod.Id
            },
            (PaymentSubscriptionType.SUBSCRIPTION3, AionPaymentMethodType.ACH) => new BankAccountAionSettingsDto
            {
                TransferMethodId3 = transferMethodResponse.TransferMethod.Id
            },
            (PaymentSubscriptionType.SUBSCRIPTION1, AionPaymentMethodType.WIRE) => new BankAccountAionSettingsDto
            {
                WireTransferMethodId = transferMethodResponse.TransferMethod.Id
            },
            (PaymentSubscriptionType.SUBSCRIPTION2, AionPaymentMethodType.WIRE) => new BankAccountAionSettingsDto
            {
                WireTransferMethodId2 = transferMethodResponse.TransferMethod.Id
            },
            (PaymentSubscriptionType.SUBSCRIPTION3, AionPaymentMethodType.WIRE) => new BankAccountAionSettingsDto
            {
                WireTransferMethodId3 = transferMethodResponse.TransferMethod.Id
            },
            (PaymentSubscriptionType.SUBSCRIPTION1, AionPaymentMethodType.INSTANT) => new BankAccountAionSettingsDto
            {
                InstantTransferMethodId = transferMethodResponse.TransferMethod.Id
            },
            (PaymentSubscriptionType.SUBSCRIPTION2, AionPaymentMethodType.INSTANT) => new BankAccountAionSettingsDto
            {
                InstantTransferMethodId2 = transferMethodResponse.TransferMethod.Id
            },
            (PaymentSubscriptionType.SUBSCRIPTION3, AionPaymentMethodType.INSTANT) => new BankAccountAionSettingsDto
            {
                InstantTransferMethodId3 = transferMethodResponse.TransferMethod.Id
            },
            _ => throw new ArgumentOutOfRangeException(nameof(paymentSubscription), paymentSubscription, null)
        };
    }
}
