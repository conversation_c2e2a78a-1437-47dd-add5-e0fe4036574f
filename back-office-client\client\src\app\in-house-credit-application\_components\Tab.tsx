'use client'

import { useEffect, useMemo, useState } from 'react'
import { useTranslation } from 'react-i18next'

import AllTable from '@/app/in-house-credit-application/_components/tabs/all/AllTable'
import ApprovedTable from '@/app/in-house-credit-application/_components/tabs/approved/ApprovedTable'
import CancelledTable from '@/app/in-house-credit-application/_components/tabs/cancelled/CancelledTable'
import InReviewTable from '@/app/in-house-credit-application/_components/tabs/in-review/InReviewTable'
import RejectedTable from '@/app/in-house-credit-application/_components/tabs/rejected/RejectedTable'
import SentBackTable from '@/app/in-house-credit-application/_components/tabs/sent-back/SentBackTable'
import StyledTabs from '@/components/common/StyledTabs'
import { useTabsState, type TabItemType } from '@/globals/hooks'

const tabKeys = [
  'inReview',
  'approved',
  'rejected',
  'cancelled',
  'sentBack',
  'all',
] as const

type TabKeys = (typeof tabKeys)[number]

const Tab = (): JSX.Element => {
  const { t } = useTranslation()

  const [activeTab, onChange] = useTabsState(tabKeys, 'inReview', 'tab', true)

  const [isTabsMounted, setIsTabsMounted] = useState(false)

  useEffect(() => setIsTabsMounted(true), [])

  const items = useMemo(
    () =>
      [
        {
          key: 'inReview',
          label: t('inHouseCreditApplication.status.inReview'),
          children: (
            <InReviewTable
              isActive={activeTab === 'inReview'}
              isTabsMounted={isTabsMounted}
            />
          ),
        },
        {
          key: 'approved',
          label: t('inHouseCreditApplication.status.approved'),
          children: (
            <ApprovedTable
              isActive={activeTab === 'approved'}
              isTabsMounted={isTabsMounted}
            />
          ),
        },
        {
          key: 'sentBack',
          label: t('inHouseCreditApplication.status.sentBack'),
          children: (
            <SentBackTable
              isActive={activeTab === 'sentBack'}
              isTabsMounted={isTabsMounted}
            />
          ),
        },
        {
          key: 'rejected',
          label: t('inHouseCreditApplication.status.rejected'),
          children: (
            <RejectedTable
              isActive={activeTab === 'rejected'}
              isTabsMounted={isTabsMounted}
            />
          ),
        },
        {
          key: 'cancelled',
          label: t('inHouseCreditApplication.status.cancelled'),
          children: (
            <CancelledTable
              isActive={activeTab === 'cancelled'}
              isTabsMounted={isTabsMounted}
            />
          ),
        },
        {
          key: 'all',
          label: t('all'),
          children: (
            <AllTable
              isActive={activeTab === 'all'}
              isTabsMounted={isTabsMounted}
            />
          ),
        },
      ] as const satisfies TabItemType<TabKeys>[],
    [activeTab, isTabsMounted, t],
  )

  return <StyledTabs activeKey={activeTab} onChange={onChange} items={items} />
}

export default Tab
