﻿using AutoMapper;
using BlueTape.Common.Enums;
using BlueTape.Common.Exceptions.Companies;
using BlueTape.Company.Application.Abstractions.Processor;
using BlueTape.Company.Application.Abstractions.Service;
using BlueTape.Company.Domain.DTOs.BankAccounts;
using BlueTape.Company.Domain.DTOs.BankAccounts.Giact;
using BlueTape.Company.Domain.DTOs.Companies;
using BlueTape.Company.Domain.DTOs.Drafts;
using BlueTape.CompanyService.BankAccounts;
using BlueTape.CompanyService.Common.Functions.AccountStatus;
using BlueTape.CompanyService.Common.Functions.AccountStatus.Constants.AccountStatusChangeEvents;
using BlueTape.CompanyService.Common.Senders;
using BlueTape.DataAccess.MongoDB.Abstractions;
using BlueTape.Document.DataAccess.EF.Abstractions;
using BlueTape.Domain.Entities.Documents;
using BlueTape.Plaid.Client.Abstractions.ExternalServices;
using BlueTape.Plaid.Client.Models;
using BlueTape.ServiceBusMessaging.Attributes;
using BlueTape.Utilities.Security;
using System.Text;
using CompanyDoesNotExistException = BlueTape.Common.Exceptions.Companies.CompanyDoesNotExistException;

namespace BlueTape.Company.Application.Services;

public class BankAccountService(
    IBankAccountRepository bankAccountRepository,
    ICompanyRepository companyRepository,
    IBankAccountProcessor bankAccountProcessor,
    IEfBankAccountRepository efBankAccountRepository,
    IEfCompanyRepository efCompanyRepository,
    IEfCompanyNoteRepository efCompanyNoteRepository,
    IPlaidApiProxy plaidApiProxy,
    IKmsEncryptionService kmsEncryptionService,
    IMapper mapper,
    IAccountStatusChangeQueueSender statusChangeQueueSender,
    IDraftRepository draftRepository)
    : IBankAccountService
{
    public Task<BankAccountDto?> GetByBankAccountId(string bankAccountId, CancellationToken cancellationToken)
        => bankAccountRepository.GetByBankAccountId(bankAccountId, cancellationToken);

    public async Task<List<BankAccountDto>> GetBankAccountsByQuery(BankAccountQuery query,
        CancellationToken ct)
    {
        var result = new List<BankAccountDto>();
        if (!string.IsNullOrWhiteSpace(query.CompanyId))
        {
            var bankAccounts = await bankAccountRepository.GetByCompanyId(query.CompanyId, ct);
            result.AddRange(bankAccounts);
        }

        if (!string.IsNullOrWhiteSpace(query.Id) && result.TrueForAll(b => b.Id != query.Id))
        {
            var bankAccount = await bankAccountRepository.GetByBankAccountId(query.Id, ct);
            if (bankAccount != null)
            {
                result.Add(bankAccount);
            }
        }

        if (!string.IsNullOrWhiteSpace(query.PlaidAccountId))
        {
            var bankAccount =
                await bankAccountRepository.GetByPlaidAccountIds([query.PlaidAccountId], ct);
            if (bankAccount != null)
            {
                var existingIds = result.Select(b => b.Id).ToHashSet();
                result.AddRange(bankAccount.Where(b => !existingIds.Contains(b.Id)));
            }
        }

        // show deactivated is false by default
        if (query.ShowDeactivated == null || !query.ShowDeactivated.Value)
        {
            result = result.Where(b => b.IsDeactivated is false or null).ToList();
        }

        return result;
    }

    public async Task<List<BankAccountDtoV2>> GetBankAccountsByQueryV2(BankAccountQuery query, CancellationToken ct)
    {
        var result = new List<BankAccountDtoV2>();
        if (!string.IsNullOrWhiteSpace(query.CompanyId) && Guid.TryParse(query.CompanyId, out var companyId))
        {
            try
            {
                var bankAccounts = await GetByCompanyIdV2(companyId, ct);
                result.AddRange(bankAccounts);
            }
            catch (Common.Exceptions.Companies.CompanyDoesNotExistException)
            {
                // Company does not exist, it is ok
            }
        }

        if (!string.IsNullOrWhiteSpace(query.Id)
            && Guid.TryParse(query.Id, out var bankAccountId)
            && result.TrueForAll(b => b.BlueTapeGuidBankAccountId != bankAccountId))
        {
            var bankAccount = await GetByBankAccountIdV2(bankAccountId, ct);
            if (bankAccount != null)
            {
                result.Add(bankAccount);
            }
        }

        if (!string.IsNullOrWhiteSpace(query.PlaidAccountId))
        {
            var bankAccount =
                await bankAccountRepository.GetByPlaidAccountIds([query.PlaidAccountId], ct);
            if (bankAccount != null)
            {
                var existingIds = result.Select(b => b.Id).ToHashSet();
                var mongoBankAccounts = bankAccount.Where(b => !existingIds.Contains(b.Id)).ToArray();

                var efBankAccounts = await efBankAccountRepository
                    .GetByLegacyIds(mongoBankAccounts.Select(x => x.Id).ToArray(), ct);

                var bankAccountsV2 = mongoBankAccounts.Select(ba =>
                {
                    var bankAccountV2 = mapper.Map<BankAccountDtoV2>(ba);
                    bankAccountV2.BlueTapeGuidBankAccountId =
                        efBankAccounts.Find(y => bankAccountV2.Id.Equals(y.LegacyId))?.Id;
                    return bankAccountV2;
                });
                result.AddRange(bankAccountsV2);
            }
        }
        return result;
    }

    public async Task<IEnumerable<BankAccountDto>> GetByCompanyId(string companyId, bool showDeactivated, CancellationToken cancellationToken)
    {
        var company = await companyRepository.GetByCompanyId(companyId, cancellationToken) ?? throw new CompanyDoesNotExistException(companyId);
        var bankAccountIds = company.BankAccounts;
        return await bankAccountRepository.GetByBankAccountIds(bankAccountIds, showDeactivated, cancellationToken) ?? [];
    }

    public Task<List<BankAccountDto>?> GetByBankAccountIds(string[] bankAccountIds, CancellationToken cancellationToken)
        => bankAccountRepository.GetByBankAccountIds(bankAccountIds, true, cancellationToken);

    public Task<List<BankAccountDto>?> GetByBankAccountAndPlaidIds(string[] ids, bool showDeactivated, CancellationToken cancellationToken)
        => bankAccountRepository.GetByBankAccountAndPlaidIds(ids, showDeactivated, cancellationToken);

    public async Task AddMany(string companyId, List<CreateBankAccountDto> bankAccountDtos,
        CancellationToken cancellationToken)
    {
        bankAccountDtos = bankAccountDtos.Select(x =>
        {
            if (x.Plaid != null)
            {
                x.Plaid.Status = PlaidBankAccountStatus.Active.ToString().ToLower();
            }

            return x;
        }).ToList();

        var processedBankAccounts = await bankAccountProcessor
            .PreProcessAddMany(companyId, bankAccountDtos, cancellationToken);

        if (processedBankAccounts == null || processedBankAccounts.Length == 0)
            throw new BankAccountAddException("PreProcessAddMany return 0 bank accounts");

        var newBankAccounts = processedBankAccounts
            .Where(x => string.IsNullOrEmpty(x.Id))
            .ToList();

        if (newBankAccounts.Count != 0)
        {
            var addedBankAccounts = await bankAccountRepository.AddMany(newBankAccounts, cancellationToken);

            await companyRepository.AddBankAccountIds(
                companyId,
                addedBankAccounts.Select(x => x.Id).ToArray(),
                cancellationToken);
        }

        var bankAccountsToUpdate = processedBankAccounts
            .Where(x => !string.IsNullOrEmpty(x.Id))
            .ToList();

        if (bankAccountsToUpdate.Count != 0)
        {
            foreach (var item in bankAccountsToUpdate)
            {
                if (item.Plaid != null)
                {
                    await bankAccountRepository.UpdatePlaidConfigAsync(item, cancellationToken);
                }
            }
        }
    }

    public async Task<BankAccountDto> Add(string companyId, string? userId, CreateBankAccountDto bankAccountDto, CancellationToken cancellationToken)
    {
        var bankAccountsToCreate = await bankAccountProcessor
            .PreProcessAdd(companyId, bankAccountDto, cancellationToken);

        if (bankAccountsToCreate.Plaid != null)
        {
            bankAccountsToCreate.Plaid.Status = PlaidBankAccountStatus.Active.ToString().ToLower();
        }

        if (bankAccountsToCreate.Giact is { Accounts.Count: > 0 })
        {
            bankAccountsToCreate.Giact.Accounts[0].Id = bankAccountsToCreate.Id;
            await bankAccountRepository.UpdateGiactAsync(bankAccountsToCreate, cancellationToken);
        }

        if (!string.IsNullOrEmpty(bankAccountsToCreate.Id))
        {
            if (bankAccountsToCreate.Plaid != null)
            {
                await bankAccountRepository.UpdatePlaidConfigAsync(bankAccountsToCreate, cancellationToken);
            }

            return bankAccountsToCreate;
        }

        bankAccountsToCreate.CreatedBy = userId;
        var bankAccount = await bankAccountRepository.Add(bankAccountsToCreate, cancellationToken);

        await companyRepository.AddBankAccountIds(
            companyId,
            new[] { bankAccount.Id },
            cancellationToken);

        return bankAccount;
    }

    public async Task UpdatePlaidBankAccountStatus(string itemId, string accountId, PlaidBankAccountStatus status, CancellationToken cancellationToken)
    {
        var bankAccounts = await bankAccountRepository.GetByPlaidData(itemId, cancellationToken);

        if (bankAccounts is null)
            throw new BankDoesNotExistException("Unable to find bankAccount by itemId");

        if (!string.IsNullOrEmpty(accountId))
        {
            bankAccounts = bankAccounts.Where(x =>
                x.Plaid != null &&
                !string.IsNullOrEmpty(x.Plaid.AccountId) &&
                x.Plaid.AccountId.Equals(accountId)).ToList();
        }

        if (bankAccounts.Count == 0)
            throw new BankDoesNotExistException("Unable to find bankAccount by itemId");

        var companyDto = await companyRepository.GetByBankAccountId(bankAccounts[0].Id, cancellationToken);

        var messages = new List<ServiceBusMessageBt<ChangeAccountStatusModel>>();

        foreach (var bankAccount in bankAccounts)
        {
            await bankAccountRepository.UpdatePlaidBankAccountStatusAsync(bankAccount, status, cancellationToken);

            if (companyDto is not null)
            {
                var body = new ChangeAccountStatusModel
                {
                    CreatedBy = "CompanyService:UpdatePlaidBankAccountStatus",
                    CreatedAt = DateTime.UtcNow,
                    Details = new ChangeAccountStatusDetailsModel
                    {
                        CompanyId = companyDto.BlueTapeCompanyId,
                        Id = bankAccount.Id,
                        Status = status.ToString()
                    },
                    EventType = status == PlaidBankAccountStatus.Active ? GoodStandingEvents.BankAccountConnected : OnHoldEvents.BankAccountDisconnected
                };

                messages.Add(new ServiceBusMessageBt<ChangeAccountStatusModel>(body));
            }
        }

        await statusChangeQueueSender.SendMessages(messages, cancellationToken);
    }

    public async Task PatchCashFlowInclusion(string accountId, PatchBankAccountCashFlowDto patchDto, CancellationToken cancellationToken)
    {
        var bankAccount = await bankAccountRepository.GetByBankAccountId(accountId, cancellationToken);

        if (bankAccount is null) throw new BankDoesNotExistException("Unable to find bankAccount by itemId");

        await bankAccountRepository.UpdatePlaidBankAccountInclusionAsync(bankAccount, patchDto.IncludeInCashFlow, cancellationToken);
    }

    public async Task PatchSettings(string accountId, PatchBankAccountSettingsDto patchModel, CancellationToken cancellationToken)
    {
        var bankAccount = await bankAccountRepository.GetByBankAccountId(accountId, cancellationToken);
        if (bankAccount is null) throw new BankDoesNotExistException("Unable to find bankAccount by itemId");

        await bankAccountRepository.UpdateSettingsAsync(bankAccount, patchModel, cancellationToken);
    }

    public async Task<BankAccountDtoV2?> GetByBankAccountIdV2(Guid bankAccountId, CancellationToken ct)
    {
        var efBankAccount = await efBankAccountRepository.GetById(bankAccountId, ct);
        if (efBankAccount is null || string.IsNullOrEmpty(efBankAccount.LegacyId)) return null;

        var bankAccountDto = await bankAccountRepository.GetByBankAccountId(efBankAccount.LegacyId, ct);
        if (bankAccountDto is null) return null;

        var result = mapper.Map<BankAccountDtoV2>(bankAccountDto);
        result.BlueTapeGuidBankAccountId = bankAccountId;
        return result;
    }

    public async Task<IEnumerable<BankAccountDtoV2>> GetByCompanyIdV2(Guid companyId, CancellationToken ct)
    {
        var efCompany = await efCompanyRepository.GetById(companyId, ct);
        if (efCompany == null || string.IsNullOrEmpty(efCompany.LegacyId)) throw new CompanyDoesNotExistException(companyId.ToString());

        var mongoBankAccounts = (await bankAccountRepository.GetByCompanyId(efCompany.LegacyId, ct)).ToArray();
        var efBankAccounts = await efBankAccountRepository.GetByLegacyIds(mongoBankAccounts.Select(x => x.Id).ToArray(), ct);

        return mongoBankAccounts.Select(bankAccount =>
        {
            var bankAccountV2 = mapper.Map<BankAccountDtoV2>(bankAccount);
            bankAccountV2.BlueTapeGuidBankAccountId =
                efBankAccounts.Find(y => bankAccountV2.Id.Equals(y.LegacyId))?.Id;
            return bankAccountV2;
        }).ToList();
    }

    public async Task<List<BankAccountDtoV2>?> GetByBankAccountIdsV2(Guid[] bankAccountIds, CancellationToken ct)
    {
        var efBankAccounts = await efBankAccountRepository.GetByIds(bankAccountIds, ct);
        if (efBankAccounts == null || efBankAccounts.Count == 0) return new List<BankAccountDtoV2>();

        var mongoIds = efBankAccounts
            .Where(x => !string.IsNullOrEmpty(x.LegacyId))
            .Select(x => x.LegacyId)
            .ToArray();

        var mongoBankAccounts = await bankAccountRepository.GetByBankAccountIds(mongoIds!, true, ct);
        if (mongoBankAccounts == null || mongoBankAccounts.Count == 0) return new List<BankAccountDtoV2>();

        return mongoBankAccounts.Select(bankAccount =>
        {
            var bankAccountV2 = mapper.Map<BankAccountDtoV2>(bankAccount);
            bankAccountV2.BlueTapeGuidBankAccountId =
                efBankAccounts.Find(y => bankAccountV2.Id.Equals(y.LegacyId))?.Id;
            return bankAccountV2;
        }).ToList();
    }

    public async Task<List<MigrateResultDto>> TryMigrateLegacyBankAccountIdsAsync(string[] companyIds, CancellationToken ct)
    {
        var result = new List<MigrateResultDto>();
        var mongoBankAccounts = await GetByBankAccountIds(companyIds, ct);
        if (mongoBankAccounts == null || mongoBankAccounts.Count == 0) return result;

        foreach (var bankAccountDto in mongoBankAccounts)
        {
            var temp = await efBankAccountRepository.TryMigrateLegacyBankAccountIdAsync(bankAccountDto.Id, ct);
            result.Add(new MigrateResultDto
            {
                LegacyId = temp.LegacyId!,
                NewId = temp.Id
            });
        }

        return result;
    }

    public async Task UpdateAionSettings(Guid bankAccountId, BankAccountAionSettingsDto bankAccountAionSettingsDto, CancellationToken ct)
    {
        var efCompany = await efBankAccountRepository.GetById(bankAccountId, ct);
        if (efCompany == null || string.IsNullOrEmpty(efCompany.LegacyId)) throw new BankDoesNotExistException(bankAccountId.ToString());

        var mongoBankAccount = await bankAccountRepository.GetByBankAccountId(efCompany.LegacyId, ct);
        if (mongoBankAccount is null) throw new BankDoesNotExistException(efCompany.LegacyId);

        mongoBankAccount.AionSettings = bankAccountAionSettingsDto;
        await bankAccountRepository.UpdateAionSettingsAsync(mongoBankAccount, ct);
    }

    public async Task UpdateGiactInfo(string accountId, BankAccountGiactDto bankAccountGiactDto, CancellationToken ct)
    {
        var mongoBankAccount = await bankAccountRepository.GetByBankAccountId(accountId, ct);
        if (mongoBankAccount is null) throw new BankDoesNotExistException(accountId);

        mongoBankAccount.Giact = bankAccountGiactDto;
        await bankAccountRepository.UpdateGiactAsync(mongoBankAccount, ct);
    }

    public async Task<BankAccountIdentityDto?> GetBankAccountIdentityById(string accountId, CancellationToken ct)
    {
        var mongoBankAccount = await bankAccountRepository.GetByBankAccountId(accountId, ct);
        if (mongoBankAccount is null) throw new BankDoesNotExistException(accountId);

        var bankAccountIdentity = mongoBankAccount.Identity;

        if (bankAccountIdentity == null)
        {
            var company = await companyRepository.GetByBankAccountId(accountId, ct);

            // Try to get identity from Plaid API first
            try
            {
                var plaidIdentity = await TryGetPlaidIdentity(mongoBankAccount, company, ct);
                if (plaidIdentity != null)
                {
                    mongoBankAccount.Identity = plaidIdentity;
                    bankAccountIdentity = (await bankAccountRepository.UpdatePlaidIdentityAsync(mongoBankAccount, ct))?.Identity;
                    return bankAccountIdentity;
                }
            }
            catch (Exception)
            {
            }

            // First fallback: Try to get address from company address
            if (company?.Address != null)
            {
                var companyAddress = company.Address;
                if (!string.IsNullOrEmpty(companyAddress.Address) && !string.IsNullOrEmpty(companyAddress.City))
                {
                    mongoBankAccount.Identity = new BankAccountIdentityDto()
                    {
                        PostalCode = companyAddress.Zip,
                        City = companyAddress.City,
                        CountryCode = "US",
                        CountrySubDivisionCode = companyAddress.State,
                        AddressLine1 = companyAddress.Address,
                    };

                    bankAccountIdentity = (await bankAccountRepository.UpdatePlaidIdentityAsync(mongoBankAccount, ct))?.Identity;
                    return bankAccountIdentity;
                }
            }

            // Second fallback: Try to get address from drafts collection
            if (company != null)
            {
                var draftAddress = await TryGetDraftAddress(company.BlueTapeCompanyId, ct);
                if (draftAddress != null)
                {
                    mongoBankAccount.Identity = draftAddress;
                    bankAccountIdentity = (await bankAccountRepository.UpdatePlaidIdentityAsync(mongoBankAccount, ct))?.Identity;
                    return bankAccountIdentity;
                }
            }

            // If all fallbacks fail, throw the original exception
            throw new PlaidIdentityNotFoundException(accountId);
        }

        return bankAccountIdentity;
    }

    private async Task<BankAccountIdentityDto?> TryGetPlaidIdentity(BankAccountDto mongoBankAccount, CompanyDto? company, CancellationToken ct)
    {
        var accessToken = mongoBankAccount.Plaid?.AccessToken?.Cipher;
        if (string.IsNullOrEmpty(accessToken))
            return null;

        var encryptedToken = await kmsEncryptionService.Decrypt(Convert.FromBase64String(accessToken));
        var encodedToken = Encoding.UTF8.GetString(encryptedToken);

        var plaidIdentityDto = await plaidApiProxy.GetIdentityByAccessToken(new BasicGetDataEntity()
        {
            AccessToken = encodedToken,
            CompanyId = company?.BlueTapeCompanyId,
            UserId = company?.BlueTapeCompanyId
        }, ct);

        if (plaidIdentityDto == null)
            return null;

        var address = plaidIdentityDto.Accounts?.FirstOrDefault()?
            .Owners?.FirstOrDefault()?.Addresses?.FirstOrDefault(x => x.Primary)?
            .Data;

        if (address == null)
            return null;

        return new BankAccountIdentityDto()
        {
            PostalCode = address.PostalCode,
            City = address.City,
            CountryCode = address.Country,
            CountrySubDivisionCode = address.Region,
            AddressLine1 = address.Street,
        };
    }

    private async Task<BankAccountIdentityDto?> TryGetDraftAddress(string companyId, CancellationToken ct)
    {
        try
        {
            var draft = await draftRepository.GetByCompanyIdAsync(companyId, ct);
            if (draft?.Data?.BusinessInfo?.Items == null)
                return null;

            var businessAddressItem = draft.Data.BusinessInfo.Items
                .FirstOrDefault(item => item.Identifier == "businessAddress" && item.Filled == true);

            if (businessAddressItem?.Content == null)
                return null;

            BusinessAddressContentDto? addressContent = null;

            if (businessAddressItem.Content is Dictionary<string, object> dict)
            {
                try
                {
                    addressContent = new BusinessAddressContentDto
                    {
                        Address = dict.TryGetValue("address", out var addr) ? addr?.ToString() : null,
                        City = dict.TryGetValue("city", out var city) ? city?.ToString() : null,
                        State = dict.TryGetValue("state", out var state) ? state?.ToString() : null,
                        Zip = dict.TryGetValue("zip", out var zip) ? zip?.ToString() : null
                    };
                }
                catch (Exception ex)
                {
                }
            }
            else
            {
                // Fallback: try to serialize and deserialize the object
                try
                {
                    var jsonString = System.Text.Json.JsonSerializer.Serialize(businessAddressItem.Content);
                    addressContent = System.Text.Json.JsonSerializer.Deserialize<BusinessAddressContentDto>(jsonString);
                }
                catch (Exception ex)
                {
                }
            }

            if (addressContent == null || string.IsNullOrEmpty(addressContent.Address) || string.IsNullOrEmpty(addressContent.City))
                return null;

            return new BankAccountIdentityDto()
            {
                PostalCode = addressContent.Zip,
                City = addressContent.City,
                CountryCode = "US", // Default to US, could be made configurable
                CountrySubDivisionCode = addressContent.State,
                AddressLine1 = addressContent.Address,
            };
        }
        catch (Exception)
        {
            // If any error occurs while accessing drafts, return null to continue with normal flow
            return null;
        }
    }

    public async Task<BankAccountDto?> UpdateBankAccountNumberModel(string accountId,
        string userId,
        UpdateBankAccountNumberDto patchDto,
        CancellationToken ct)
    {
        // bankaccounts' routingNumber and accountNumber are immutable.
        // we internally deactivates the old and creates a new one.
        // All the Plaid connections are lost - BUT this functionality is for Guest Suppliers,
        // which has not Plaid connection.

        var mongoBankAccount = await bankAccountRepository.GetByBankAccountId(accountId, ct);
        if (mongoBankAccount is null) throw new BankDoesNotExistException(accountId);
        if (patchDto.BankDetails == null)
        {
            return mongoBankAccount;
        }

        var companyDto = await companyRepository.GetByBankAccountId(mongoBankAccount.Id, ct);
        if (companyDto is null) throw new CompanyDoesNotExistException(string.Empty);

        var newAccount = mapper.Map<BankAccountDto>(mongoBankAccount);
        newAccount.Id = null!;
        newAccount.RoutingNumber = patchDto.BankDetails.RoutingNumber;
        newAccount.AccountNumber = patchDto.BankDetails.AccountNumber;
        newAccount.Plaid = null;
        newAccount.Giact = null;
        newAccount.UpdatedAt = DateTime.UtcNow;
        newAccount.CreatedAt = DateTime.UtcNow;
        mongoBankAccount.AccountType = patchDto.BankDetails.AccountType;
        var addedBankAccount = await bankAccountRepository.Add(mongoBankAccount, ct);

        addedBankAccount.Giact = new BankAccountGiactDto
        {
            Accounts =
            [
                new GiactAccountDto
                {
                    Id = addedBankAccount.Id,
                    AccountNumberDisplay = patchDto.BankDetails?.AccountNumber?.Display ?? string.Empty,
                    Response = patchDto.GiactVerificationResult
                }
            ]
        };

        await bankAccountRepository.UpdateGiactAsync(addedBankAccount, ct);
        await bankAccountRepository.DeactivateAsync(accountId, ct);

        await companyRepository.AddBankAccountIds(
            companyDto.BlueTapeCompanyId,
            [addedBankAccount.Id],
            ct);

        var efCompany = await efCompanyRepository.GetByLegacyId(companyDto.BlueTapeCompanyId, ct);
        if (efCompany != null)
        {
            var note = new CompanyNoteEntity
            {
                CompanyId = efCompany.Id,
                CreatedBy = userId,
                CreatedAt = DateTime.UtcNow,
                Note = $"Bank Account details were updated by {userId}"
            };
            await efCompanyNoteRepository.Add(note, ct);
        }

        return addedBankAccount;
    }
}