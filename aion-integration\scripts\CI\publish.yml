.publishing:base:
  stage: publish
  variables:
    GIT_DEPTH: "0"
  script:
    - apt-get update
    - apt-get -y install zip
    - dotnet tool restore
    - dotnet lambda package -pl src/BlueTape.Aion.API/ -o publish/aionApi-$CI_PIPELINE_ID.zip
    - dotnet lambda package -pl src/BlueTape.Reports.Lambda/ -o publish/lambda-aion-report-$CI_PIPELINE_ID.zip
  
  artifacts:
    name: $CI_JOB_NAME
    paths:
      - publish/aionApi-$CI_PIPELINE_ID.zip
      - publish/lambda-aion-report-$CI_PIPELINE_ID.zip
    expire_in: 1 week

publishing:api:
  extends: .publishing:base
  only:
    !reference [ "building:api", only ]
  when: manual

publishing:prod:
  extends: .publishing:base
  only:
    !reference [ "building:prod", only ]
  when: manual
