import { IsNotEmpty, ValidateIf } from 'class-validator'
import i18n from 'i18next'

import { BaseValidationModel } from '@/lib/class-validator/base-validation-model'
import config from '@/globals/config'

export class DrawApplicationRejectData extends BaseValidationModel {
  constructor(code: any, note: any) {
    super()
    this.code = code
    this.note = note
  }

  @IsNotEmpty({
    message: i18n.t('drawApplicationRejectForm.isReasonNotEmpty', {
      ns: 'validation',
    }),
  })
  code: string

  @ValidateIf((o) => o.code === config.DRAW_REJECTION_OTHER_CODE)
  @IsNotEmpty({
    message: i18n.t('drawApplicationRejectForm.isReasonNotEmpty', {
      ns: 'validation',
    }),
  })
  note: string | undefined
}
