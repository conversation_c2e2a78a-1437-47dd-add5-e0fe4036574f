import {BasePage} from '../../../base.page';

export class CreditList extends BasePage {
    constructor(page){
        super(page);
    };

    containers = {
        creditList: this.page.locator('_react=[name="list"] >> _react=ScrollView'), //TODO: improve locators
        penaltyInfo: this.page.locator('_react=ea'),
    };

    tabs = {
        activeLoans: this.page.locator('"Active Loans"'),
        previousLoans: this.page.locator('"Previous Loans"'),
    };

    loanList = {
        loanListElement: this.containers.creditList.locator('_react=[loan]'), // TODO: SELECTOR
        loanElementByInvoiceId: (invoiceId) => this.containers.creditList.locator(`_react=[loan.invoices.0._id="${invoiceId}"]`),
        loanAmount: this.page.locator('.css-901oao.r-etduwe.r-ubezar.r-1kfrs79'),
    };

    buttons = {
        getPrequalified: this.page.locator('[data-testid="getPrequalified"]'),
    };

    async openFirstLoanDetails(){
        await super.clickFistOption(this.loanList.loanListElement);
    };

    async getFirstLoanAmount(){
        await super.getFistOption(this.loanList.loanAmount);
    };
}