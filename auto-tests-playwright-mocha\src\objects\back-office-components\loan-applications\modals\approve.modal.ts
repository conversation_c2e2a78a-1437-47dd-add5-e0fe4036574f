import { Page } from '@playwright/test';
import {BasePage} from '../../../base.page';

export class ApproveModal extends BasePage {
    constructor(page: Page) {
        super(page);
    };

    containers = {
        modal: this.page.locator('.modal-content'),
    };

    buttons = {
        approve: this.containers.modal.locator('button:has-text("Approve")'),
    };

    fields = {
        inputCreditLimit: this.containers.modal.locator('_react=[label="Credit Limit"] >> _react=CurrencyInput'),
    };

    async fillCreditLimitAndClickApprove(creditLimit: string) {
        await this.fields.inputCreditLimit.fill(creditLimit);
        await this.buttons.approve.click();
    };
}
