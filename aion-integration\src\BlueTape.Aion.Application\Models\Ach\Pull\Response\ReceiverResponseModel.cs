﻿using System.Runtime.Serialization;
using System.Text.Json.Serialization;

namespace BlueTape.Aion.Application.Models.Ach.Pull.Response;

[DataContract]
public class ReceiverResponseModel
{
    [JsonPropertyName("accountNumber")]
    public string AccountNumber { get; set; } = null!;

    [JsonPropertyName("routingNumber")]
    public string RoutingNumber { get; set; } = null!;

    [JsonPropertyName("name")]
    public string Name { get; set; } = null!;

    [JsonPropertyName("accountType")]
    public string AccountType { get; set; } = null!;

    [JsonPropertyName("identification")]
    public string Identification { get; set; } = null!;
}