using BlueTape.Aion.DataAccess.MongoDB.Abstractions;
using BlueTape.Aion.DataAccess.MongoDB.Entities.UserRole;
using BlueTape.MongoDB.Abstractions;
using MongoDB.Driver;

namespace BlueTape.Aion.DataAccess.MongoDB.Repositories;
public class UserRoleRepository(IMongoDbContext dbContext) : IUserRoleRepository
{
    public Task<List<UserRoleEntity>?> GetByCompanyId(string companyId, CancellationToken ct)
    {
        return dbContext.GetCollection<UserRoleEntity>()
            .Find(x => x.CompanyId == companyId)
            .ToListAsync(ct);
    }
}
