[{"ContainingType": "BlueTape.Company.API.Controllers.AccountController", "Method": "UpdateAccountStatusM", "RelativePath": "account/companies", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "model", "Type": "BlueTape.CompanyService.Common.Functions.AccountStatus.ChangeAccountStatusModel", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Collections.Generic.List`1[[BlueTape.Company.API.Models.Errors.ErrorResponse, BlueTape.Company.API, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 400}, {"Type": "System.Collections.Generic.List`1[[BlueTape.Company.API.Models.Errors.ErrorResponse, BlueTape.Company.API, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 401}, {"Type": "System.Void", "MediaTypes": [], "StatusCode": 404}, {"Type": "System.Collections.Generic.List`1[[BlueTape.Company.API.Models.Errors.ErrorResponse, BlueTape.Company.API, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 500}, {"Type": "System.Void", "MediaTypes": [], "StatusCode": 200}]}, {"ContainingType": "BlueTape.Company.API.Controllers.AccountController", "Method": "UpdateAccountStatusManually", "RelativePath": "account/companies/{id}", "HttpMethod": "PATCH", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.String", "IsRequired": true}, {"Name": "userId", "Type": "System.String", "IsRequired": false}, {"Name": "changeAccountStatusManuallyModel", "Type": "BlueTape.Company.Application.Models.AccountStatus.ChangeAccountStatusManuallyModel", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Collections.Generic.List`1[[BlueTape.Company.API.Models.Errors.ErrorResponse, BlueTape.Company.API, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 400}, {"Type": "System.Collections.Generic.List`1[[BlueTape.Company.API.Models.Errors.ErrorResponse, BlueTape.Company.API, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 401}, {"Type": "System.Void", "MediaTypes": [], "StatusCode": 404}, {"Type": "System.Collections.Generic.List`1[[BlueTape.Company.API.Models.Errors.ErrorResponse, BlueTape.Company.API, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 500}, {"Type": "System.Void", "MediaTypes": [], "StatusCode": 200}]}, {"ContainingType": "BlueTape.Company.API.Controllers.AccountController", "Method": "GetCompanyAccountStatusByLegacyId", "RelativePath": "account/companies/{legacyCompanyId}/account-status", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "legacyCompanyId", "Type": "System.String", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Collections.Generic.List`1[[BlueTape.Company.API.Models.Errors.ErrorResponse, BlueTape.Company.API, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 400}, {"Type": "System.Collections.Generic.List`1[[BlueTape.Company.API.Models.Errors.ErrorResponse, BlueTape.Company.API, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 401}, {"Type": "System.Void", "MediaTypes": [], "StatusCode": 404}, {"Type": "System.Collections.Generic.List`1[[BlueTape.Company.API.Models.Errors.ErrorResponse, BlueTape.Company.API, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 500}, {"Type": "System.Nullable`1[[BlueTape.CompanyService.Common.Enums.AccountStatusEnum, BlueTape.CompanyService.Common, Version=1.1.21.0, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "BlueTape.Company.API.Controllers.AdminController", "Method": "UpdateManualAccountStatus", "RelativePath": "admin/companies/{id}/status", "HttpMethod": "PATCH", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.String", "IsRequired": true}, {"Name": "userId", "Type": "System.String", "IsRequired": false}, {"Name": "updateManualAccountStatusModel", "Type": "BlueTape.Company.API.Models.UpdateManualAccountStatusModel", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Collections.Generic.List`1[[BlueTape.Company.API.Models.Errors.ErrorResponse, BlueTape.Company.API, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 400}, {"Type": "System.Collections.Generic.List`1[[BlueTape.Company.API.Models.Errors.ErrorResponse, BlueTape.Company.API, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 401}, {"Type": "System.Void", "MediaTypes": [], "StatusCode": 404}, {"Type": "System.Collections.Generic.List`1[[BlueTape.Company.API.Models.Errors.ErrorResponse, BlueTape.Company.API, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 500}, {"Type": "System.Void", "MediaTypes": [], "StatusCode": 200}]}, {"ContainingType": "BlueTape.Company.API.Controllers.AdminController", "Method": "DeleteManualAccountStatus", "RelativePath": "admin/companies/{id}/status/delete", "HttpMethod": "PATCH", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.String", "IsRequired": true}, {"Name": "userId", "Type": "System.String", "IsRequired": false}, {"Name": "model", "Type": "BlueTape.Company.API.Models.NoteModel", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Collections.Generic.List`1[[BlueTape.Company.API.Models.Errors.ErrorResponse, BlueTape.Company.API, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 400}, {"Type": "System.Collections.Generic.List`1[[BlueTape.Company.API.Models.Errors.ErrorResponse, BlueTape.Company.API, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 401}, {"Type": "System.Void", "MediaTypes": [], "StatusCode": 404}, {"Type": "System.Collections.Generic.List`1[[BlueTape.Company.API.Models.Errors.ErrorResponse, BlueTape.Company.API, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 500}, {"Type": "System.Void", "MediaTypes": [], "StatusCode": 200}]}, {"ContainingType": "BlueTape.Company.API.Controllers.BankAccountController", "Method": "GetByQuery", "RelativePath": "bank-account", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "Id", "Type": "System.String", "IsRequired": false}, {"Name": "CompanyId", "Type": "System.String", "IsRequired": false}, {"Name": "PlaidAccountId", "Type": "System.String", "IsRequired": false}, {"Name": "ShowDeactivated", "Type": "System.Nullable`1[[<PERSON><PERSON>Boolean, System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}], "ReturnTypes": [{"Type": "System.Collections.Generic.List`1[[BlueTape.Company.API.Models.Errors.ErrorResponse, BlueTape.Company.API, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 400}, {"Type": "System.Collections.Generic.List`1[[BlueTape.Company.API.Models.Errors.ErrorResponse, BlueTape.Company.API, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 401}, {"Type": "System.Void", "MediaTypes": [], "StatusCode": 404}, {"Type": "System.Collections.Generic.List`1[[BlueTape.Company.API.Models.Errors.ErrorResponse, BlueTape.Company.API, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 500}, {"Type": "System.Collections.Generic.IEnumerable`1[[BlueTape.CompanyService.BankAccounts.BankAccountModel, BlueTape.CompanyService, Version=1.3.3.0, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "BlueTape.Company.API.Controllers.BankAccountController", "Method": "Create", "RelativePath": "bank-account/{companyId}", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "companyId", "Type": "System.String", "IsRequired": true}, {"Name": "userId", "Type": "System.String", "IsRequired": false}, {"Name": "models", "Type": "BlueTape.CompanyService.BankAccounts.CreateBankAccountModel", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Collections.Generic.List`1[[BlueTape.Company.API.Models.Errors.ErrorResponse, BlueTape.Company.API, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 400}, {"Type": "System.Collections.Generic.List`1[[BlueTape.Company.API.Models.Errors.ErrorResponse, BlueTape.Company.API, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 401}, {"Type": "System.Void", "MediaTypes": [], "StatusCode": 404}, {"Type": "System.Collections.Generic.List`1[[BlueTape.Company.API.Models.Errors.ErrorResponse, BlueTape.Company.API, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 500}, {"Type": "BlueTape.CompanyService.BankAccounts.BankAccountModel", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "BlueTape.Company.API.Controllers.BankAccountController", "Method": "GetById", "RelativePath": "bank-account/{id}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.String", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Collections.Generic.List`1[[BlueTape.Company.API.Models.Errors.ErrorResponse, BlueTape.Company.API, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 400}, {"Type": "System.Collections.Generic.List`1[[BlueTape.Company.API.Models.Errors.ErrorResponse, BlueTape.Company.API, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 401}, {"Type": "System.Void", "MediaTypes": [], "StatusCode": 404}, {"Type": "System.Collections.Generic.List`1[[BlueTape.Company.API.Models.Errors.ErrorResponse, BlueTape.Company.API, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 500}, {"Type": "BlueTape.CompanyService.BankAccounts.BankAccountModel", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "BlueTape.Company.API.Controllers.BankAccountController", "Method": "UpdateBankAccountNumberModel", "RelativePath": "bank-account/{id}", "HttpMethod": "PATCH", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.String", "IsRequired": true}, {"Name": "userId", "Type": "System.String", "IsRequired": false}, {"Name": "patchModel", "Type": "BlueTape.CompanyService.BankAccounts.UpdateBankAccountNumberModel", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Collections.Generic.List`1[[BlueTape.Company.API.Models.Errors.ErrorResponse, BlueTape.Company.API, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 400}, {"Type": "System.Collections.Generic.List`1[[BlueTape.Company.API.Models.Errors.ErrorResponse, BlueTape.Company.API, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 401}, {"Type": "System.Void", "MediaTypes": [], "StatusCode": 404}, {"Type": "System.Collections.Generic.List`1[[BlueTape.Company.API.Models.Errors.ErrorResponse, BlueTape.Company.API, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 500}, {"Type": "BlueTape.CompanyService.BankAccounts.BankAccountModel", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "BlueTape.Company.API.Controllers.BankAccountController", "Method": "UpdateAionSettings", "RelativePath": "bank-account/{id}/aion/settings", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Guid", "IsRequired": true}, {"Name": "model", "Type": "BlueTape.CompanyService.BankAccounts.BankAccountAionSettingsModel", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Collections.Generic.List`1[[BlueTape.Company.API.Models.Errors.ErrorResponse, BlueTape.Company.API, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 400}, {"Type": "System.Collections.Generic.List`1[[BlueTape.Company.API.Models.Errors.ErrorResponse, BlueTape.Company.API, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 401}, {"Type": "System.Void", "MediaTypes": [], "StatusCode": 404}, {"Type": "System.Collections.Generic.List`1[[BlueTape.Company.API.Models.Errors.ErrorResponse, BlueTape.Company.API, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 500}, {"Type": "System.Void", "MediaTypes": [], "StatusCode": 200}]}, {"ContainingType": "BlueTape.Company.API.Controllers.BankAccountController", "Method": "GetBankAccountIdentity", "RelativePath": "bank-account/{id}/bankAccountIdentity", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.String", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Collections.Generic.List`1[[BlueTape.Company.API.Models.Errors.ErrorResponse, BlueTape.Company.API, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 400}, {"Type": "System.Collections.Generic.List`1[[BlueTape.Company.API.Models.Errors.ErrorResponse, BlueTape.Company.API, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 401}, {"Type": "System.Void", "MediaTypes": [], "StatusCode": 404}, {"Type": "System.Collections.Generic.List`1[[BlueTape.Company.API.Models.Errors.ErrorResponse, BlueTape.Company.API, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 500}, {"Type": "BlueTape.CompanyService.BankAccounts.BankAccountModel", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "BlueTape.Company.API.Controllers.BankAccountController", "Method": "Patch", "RelativePath": "bank-account/{id}/cashFlow", "HttpMethod": "PATCH", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.String", "IsRequired": true}, {"Name": "patchModel", "Type": "BlueTape.CompanyService.BankAccounts.PatchBankAccountCashFlow", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Collections.Generic.List`1[[BlueTape.Company.API.Models.Errors.ErrorResponse, BlueTape.Company.API, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 400}, {"Type": "System.Collections.Generic.List`1[[BlueTape.Company.API.Models.Errors.ErrorResponse, BlueTape.Company.API, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 401}, {"Type": "System.Void", "MediaTypes": [], "StatusCode": 404}, {"Type": "System.Collections.Generic.List`1[[BlueTape.Company.API.Models.Errors.ErrorResponse, BlueTape.Company.API, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 500}, {"Type": "System.Void", "MediaTypes": [], "StatusCode": 200}]}, {"ContainingType": "BlueTape.Company.API.Controllers.BankAccountController", "Method": "UpdateGiactInfo", "RelativePath": "bank-account/{id}/giact", "HttpMethod": "PATCH", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.String", "IsRequired": true}, {"Name": "patchModel", "Type": "BlueTape.CompanyService.BankAccounts.Giact.BankAccountGiactModel", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Collections.Generic.List`1[[BlueTape.Company.API.Models.Errors.ErrorResponse, BlueTape.Company.API, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 400}, {"Type": "System.Collections.Generic.List`1[[BlueTape.Company.API.Models.Errors.ErrorResponse, BlueTape.Company.API, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 401}, {"Type": "System.Void", "MediaTypes": [], "StatusCode": 404}, {"Type": "System.Collections.Generic.List`1[[BlueTape.Company.API.Models.Errors.ErrorResponse, BlueTape.Company.API, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 500}, {"Type": "System.Void", "MediaTypes": [], "StatusCode": 200}]}, {"ContainingType": "BlueTape.Company.API.Controllers.BankAccountController", "Method": "Patch", "RelativePath": "bank-account/{id}/settings", "HttpMethod": "PATCH", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.String", "IsRequired": true}, {"Name": "patchModel", "Type": "BlueTape.CompanyService.BankAccounts.PatchBankAccountSettingsModel", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Collections.Generic.List`1[[BlueTape.Company.API.Models.Errors.ErrorResponse, BlueTape.Company.API, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 400}, {"Type": "System.Collections.Generic.List`1[[BlueTape.Company.API.Models.Errors.ErrorResponse, BlueTape.Company.API, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 401}, {"Type": "System.Void", "MediaTypes": [], "StatusCode": 404}, {"Type": "System.Collections.Generic.List`1[[BlueTape.Company.API.Models.Errors.ErrorResponse, BlueTape.Company.API, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 500}, {"Type": "System.Void", "MediaTypes": [], "StatusCode": 200}]}, {"ContainingType": "BlueTape.Company.API.Controllers.BankAccountController", "Method": "CreateMany", "RelativePath": "bank-account/batch/{companyId}", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "companyId", "Type": "System.String", "IsRequired": true}, {"Name": "models", "Type": "System.Collections.Generic.List`1[[BlueTape.CompanyService.BankAccounts.CreateBankAccountModel, BlueTape.CompanyService, Version=1.3.3.0, Culture=neutral, PublicKeyToken=null]]", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Collections.Generic.List`1[[BlueTape.Company.API.Models.Errors.ErrorResponse, BlueTape.Company.API, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 400}, {"Type": "System.Collections.Generic.List`1[[BlueTape.Company.API.Models.Errors.ErrorResponse, BlueTape.Company.API, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 401}, {"Type": "System.Void", "MediaTypes": [], "StatusCode": 404}, {"Type": "System.Collections.Generic.List`1[[BlueTape.Company.API.Models.Errors.ErrorResponse, BlueTape.Company.API, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 500}, {"Type": "System.Void", "MediaTypes": [], "StatusCode": 200}]}, {"ContainingType": "BlueTape.Company.API.Controllers.BankAccountController", "Method": "GetByCompanyId", "RelativePath": "bank-account/company/{id}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.String", "IsRequired": true}, {"Name": "showDeactivated", "Type": "System.Boolean", "IsRequired": false}], "ReturnTypes": [{"Type": "System.Collections.Generic.List`1[[BlueTape.Company.API.Models.Errors.ErrorResponse, BlueTape.Company.API, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 400}, {"Type": "System.Collections.Generic.List`1[[BlueTape.Company.API.Models.Errors.ErrorResponse, BlueTape.Company.API, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 401}, {"Type": "System.Void", "MediaTypes": [], "StatusCode": 404}, {"Type": "System.Collections.Generic.List`1[[BlueTape.Company.API.Models.Errors.ErrorResponse, BlueTape.Company.API, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 500}, {"Type": "System.Collections.Generic.IEnumerable`1[[BlueTape.CompanyService.BankAccounts.BankAccountModel, BlueTape.CompanyService, Version=1.3.3.0, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "BlueTape.Company.API.Controllers.BankAccountController", "Method": "GetByIds", "RelativePath": "bank-account/getByIds", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "ids", "Type": "System.String[]", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Collections.Generic.List`1[[BlueTape.Company.API.Models.Errors.ErrorResponse, BlueTape.Company.API, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 400}, {"Type": "System.Collections.Generic.List`1[[BlueTape.Company.API.Models.Errors.ErrorResponse, BlueTape.Company.API, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 401}, {"Type": "System.Void", "MediaTypes": [], "StatusCode": 404}, {"Type": "System.Collections.Generic.List`1[[BlueTape.Company.API.Models.Errors.ErrorResponse, BlueTape.Company.API, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 500}, {"Type": "System.Collections.Generic.IEnumerable`1[[BlueTape.CompanyService.BankAccounts.BankAccountModel, BlueTape.CompanyService, Version=1.3.3.0, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "BlueTape.Company.API.Controllers.BankAccountController", "Method": "UpdatePlaidBankAccountStatusByItemAndAccount", "RelativePath": "bank-account/plaid/item/{itemId}/account/{accountId}/status/{status}", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "itemId", "Type": "System.String", "IsRequired": true}, {"Name": "accountId", "Type": "System.String", "IsRequired": true}, {"Name": "status", "Type": "System.String", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Collections.Generic.List`1[[BlueTape.Company.API.Models.Errors.ErrorResponse, BlueTape.Company.API, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 400}, {"Type": "System.Collections.Generic.List`1[[BlueTape.Company.API.Models.Errors.ErrorResponse, BlueTape.Company.API, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 401}, {"Type": "System.Void", "MediaTypes": [], "StatusCode": 404}, {"Type": "System.Collections.Generic.List`1[[BlueTape.Company.API.Models.Errors.ErrorResponse, BlueTape.Company.API, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 500}, {"Type": "System.Void", "MediaTypes": [], "StatusCode": 200}]}, {"ContainingType": "BlueTape.Company.API.Controllers.BankAccountController", "Method": "UpdatePlaidBankAccountStatusByItem", "RelativePath": "bank-account/plaid/item/{itemId}/status/{status}", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "itemId", "Type": "System.String", "IsRequired": true}, {"Name": "status", "Type": "System.String", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Collections.Generic.List`1[[BlueTape.Company.API.Models.Errors.ErrorResponse, BlueTape.Company.API, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 400}, {"Type": "System.Collections.Generic.List`1[[BlueTape.Company.API.Models.Errors.ErrorResponse, BlueTape.Company.API, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 401}, {"Type": "System.Void", "MediaTypes": [], "StatusCode": 404}, {"Type": "System.Collections.Generic.List`1[[BlueTape.Company.API.Models.Errors.ErrorResponse, BlueTape.Company.API, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 500}, {"Type": "System.Void", "MediaTypes": [], "StatusCode": 200}]}, {"ContainingType": "BlueTape.Company.API.Controllers.v2.BankAccountController", "Method": "GetById", "RelativePath": "bank-account/v2", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "Id", "Type": "System.String", "IsRequired": false}, {"Name": "CompanyId", "Type": "System.String", "IsRequired": false}, {"Name": "PlaidAccountId", "Type": "System.String", "IsRequired": false}, {"Name": "ShowDeactivated", "Type": "System.Nullable`1[[<PERSON><PERSON>Boolean, System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}], "ReturnTypes": [{"Type": "System.Collections.Generic.List`1[[BlueTape.Company.API.Models.Errors.ErrorResponse, BlueTape.Company.API, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 400}, {"Type": "System.Collections.Generic.List`1[[BlueTape.Company.API.Models.Errors.ErrorResponse, BlueTape.Company.API, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 401}, {"Type": "System.Void", "MediaTypes": [], "StatusCode": 404}, {"Type": "System.Collections.Generic.List`1[[BlueTape.Company.API.Models.Errors.ErrorResponse, BlueTape.Company.API, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 500}, {"Type": "BlueTape.CompanyService.BankAccounts.BankAccountModel[]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "BlueTape.Company.API.Controllers.v2.BankAccountController", "Method": "GetByIdV2", "RelativePath": "bank-account/v2/{id}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Guid", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Collections.Generic.List`1[[BlueTape.Company.API.Models.Errors.ErrorResponse, BlueTape.Company.API, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 400}, {"Type": "System.Collections.Generic.List`1[[BlueTape.Company.API.Models.Errors.ErrorResponse, BlueTape.Company.API, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 401}, {"Type": "System.Void", "MediaTypes": [], "StatusCode": 404}, {"Type": "System.Collections.Generic.List`1[[BlueTape.Company.API.Models.Errors.ErrorResponse, BlueTape.Company.API, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 500}, {"Type": "BlueTape.CompanyService.BankAccounts.BankAccountModel", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "BlueTape.Company.API.Controllers.v2.BankAccountController", "Method": "GetByCompanyId_V2", "RelativePath": "bank-account/v2/company/{id}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Guid", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Collections.Generic.List`1[[BlueTape.Company.API.Models.Errors.ErrorResponse, BlueTape.Company.API, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 400}, {"Type": "System.Collections.Generic.List`1[[BlueTape.Company.API.Models.Errors.ErrorResponse, BlueTape.Company.API, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 401}, {"Type": "System.Void", "MediaTypes": [], "StatusCode": 404}, {"Type": "System.Collections.Generic.List`1[[BlueTape.Company.API.Models.Errors.ErrorResponse, BlueTape.Company.API, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 500}, {"Type": "System.Collections.Generic.IEnumerable`1[[BlueTape.CompanyService.BankAccounts.BankAccountModel, BlueTape.CompanyService, Version=1.3.3.0, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "BlueTape.Company.API.Controllers.v2.BankAccountController", "Method": "GetByIdV2", "RelativePath": "bank-account/v2/getByIds", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "ids", "Type": "System.Guid[]", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Collections.Generic.List`1[[BlueTape.Company.API.Models.Errors.ErrorResponse, BlueTape.Company.API, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 400}, {"Type": "System.Collections.Generic.List`1[[BlueTape.Company.API.Models.Errors.ErrorResponse, BlueTape.Company.API, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 401}, {"Type": "System.Void", "MediaTypes": [], "StatusCode": 404}, {"Type": "System.Collections.Generic.List`1[[BlueTape.Company.API.Models.Errors.ErrorResponse, BlueTape.Company.API, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 500}, {"Type": "BlueTape.CompanyService.BankAccounts.BankAccountModel[]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "BlueTape.Company.API.Controllers.v2.BankAccountController", "Method": "TryMigrateLegacyCompanyIds", "RelativePath": "bank-account/v2/try-migrate", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "ids", "Type": "System.String[]", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Collections.Generic.List`1[[BlueTape.Company.API.Models.Errors.ErrorResponse, BlueTape.Company.API, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 400}, {"Type": "System.Collections.Generic.List`1[[BlueTape.Company.API.Models.Errors.ErrorResponse, BlueTape.Company.API, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 401}, {"Type": "System.Void", "MediaTypes": [], "StatusCode": 404}, {"Type": "System.Collections.Generic.List`1[[BlueTape.Company.API.Models.Errors.ErrorResponse, BlueTape.Company.API, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 500}, {"Type": "BlueTape.CompanyService.Companies.MigrateResultModel[]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "BlueTape.Company.API.Controllers.BankAccountController", "Method": "GetByIdsV2", "RelativePath": "bank-account/v3/getByIds", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "ids", "Type": "System.String[]", "IsRequired": true}, {"Name": "showDeactivated", "Type": "System.Nullable`1[[<PERSON><PERSON>Boolean, System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}], "ReturnTypes": [{"Type": "System.Collections.Generic.List`1[[BlueTape.Company.API.Models.Errors.ErrorResponse, BlueTape.Company.API, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 400}, {"Type": "System.Collections.Generic.List`1[[BlueTape.Company.API.Models.Errors.ErrorResponse, BlueTape.Company.API, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 401}, {"Type": "System.Void", "MediaTypes": [], "StatusCode": 404}, {"Type": "System.Collections.Generic.List`1[[BlueTape.Company.API.Models.Errors.ErrorResponse, BlueTape.Company.API, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 500}, {"Type": "System.Collections.Generic.IEnumerable`1[[BlueTape.CompanyService.BankAccounts.BankAccountModel, BlueTape.CompanyService, Version=1.3.3.0, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "BlueTape.Company.API.Controllers.CompanyController", "Method": "CreateCompany", "RelativePath": "companies", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "createCompanyModel", "Type": "BlueTape.CompanyService.Companies.CreateCompanyModel", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Collections.Generic.List`1[[BlueTape.Company.API.Models.Errors.ErrorResponse, BlueTape.Company.API, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 400}, {"Type": "System.Collections.Generic.List`1[[BlueTape.Company.API.Models.Errors.ErrorResponse, BlueTape.Company.API, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 401}, {"Type": "System.Void", "MediaTypes": [], "StatusCode": 404}, {"Type": "System.Collections.Generic.List`1[[BlueTape.Company.API.Models.Errors.ErrorResponse, BlueTape.Company.API, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 500}, {"Type": "BlueTape.CompanyService.Companies.CompanyModel", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "BlueTape.Company.API.Controllers.CompanyController", "Method": "GetByFiltersWithPagination", "RelativePath": "companies", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "Id", "Type": "System.String", "IsRequired": false}, {"Name": "ActiveAccountOnly", "Type": "System.Boolean", "IsRequired": false}, {"Name": "ValidAccountOnly", "Type": "System.Boolean", "IsRequired": false}, {"Name": "Status", "Type": "System.String[]", "IsRequired": false}, {"Name": "Account<PERSON><PERSON><PERSON>", "Type": "System.String[]", "IsRequired": false}, {"Name": "Name", "Type": "System.String", "IsRequired": false}, {"Name": "Type", "Type": "System.String", "IsRequired": false}, {"Name": "IsGuest", "Type": "System.Nullable`1[[<PERSON><PERSON>Boolean, System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "Search", "Type": "System.String", "IsRequired": false}, {"Name": "SortBy", "Type": "System.String", "IsRequired": false}, {"Name": "SortOrder", "Type": "System.String", "IsRequired": false}, {"Name": "PageNumber", "Type": "System.Int32", "IsRequired": false}, {"Name": "PageSize", "Type": "System.Int32", "IsRequired": false}], "ReturnTypes": [{"Type": "System.Collections.Generic.List`1[[BlueTape.Company.API.Models.Errors.ErrorResponse, BlueTape.Company.API, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 400}, {"Type": "System.Collections.Generic.List`1[[BlueTape.Company.API.Models.Errors.ErrorResponse, BlueTape.Company.API, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 401}, {"Type": "System.Void", "MediaTypes": [], "StatusCode": 404}, {"Type": "System.Collections.Generic.List`1[[BlueTape.Company.API.Models.Errors.ErrorResponse, BlueTape.Company.API, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 500}, {"Type": "BlueTape.CompanyService.Companies.PaginatedCompanyResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "BlueTape.Company.API.Controllers.CompanyController", "Method": "GetCompanyUsers", "RelativePath": "companies/{companyId}/active-users", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "companyId", "Type": "System.String", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Collections.Generic.List`1[[BlueTape.Company.API.Models.Errors.ErrorResponse, BlueTape.Company.API, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 400}, {"Type": "System.Collections.Generic.List`1[[BlueTape.Company.API.Models.Errors.ErrorResponse, BlueTape.Company.API, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 401}, {"Type": "System.Void", "MediaTypes": [], "StatusCode": 404}, {"Type": "System.Collections.Generic.List`1[[BlueTape.Company.API.Models.Errors.ErrorResponse, BlueTape.Company.API, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 500}, {"Type": "System.Collections.Generic.List`1[[BlueTape.Company.Domain.DTOs.Companies.UserDto, BlueTape.Company.Domain, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "BlueTape.Company.API.Controllers.CompanyController", "Method": "AddPlaidAssetReport", "RelativePath": "companies/{companyId}/cashFlow", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "companyId", "Type": "System.String", "IsRequired": true}, {"Name": "plaidCashFlow", "Type": "BlueTape.Company.API.Models.CashFlow.AddPlaidCashFlow", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Collections.Generic.List`1[[BlueTape.Company.API.Models.Errors.ErrorResponse, BlueTape.Company.API, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 400}, {"Type": "System.Collections.Generic.List`1[[BlueTape.Company.API.Models.Errors.ErrorResponse, BlueTape.Company.API, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 401}, {"Type": "System.Void", "MediaTypes": [], "StatusCode": 404}, {"Type": "System.Collections.Generic.List`1[[BlueTape.Company.API.Models.Errors.ErrorResponse, BlueTape.Company.API, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 500}, {"Type": "BlueTape.Company.API.Models.CashFlow.Responses.AddPlaidCashFlowResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "BlueTape.Company.API.Controllers.CompanyController", "Method": "GetPlaidAssetReport", "RelativePath": "companies/{companyId}/cashFlow", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "companyId", "Type": "System.String", "IsRequired": true}, {"Name": "AccountId", "Type": "System.String", "IsRequired": false}, {"Name": "From", "Type": "System.Nullable`1[[System.DateTime, System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "To", "Type": "System.Nullable`1[[System.DateTime, System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "Grouping", "Type": "System.String", "IsRequired": false}], "ReturnTypes": [{"Type": "System.Collections.Generic.List`1[[BlueTape.Company.API.Models.Errors.ErrorResponse, BlueTape.Company.API, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 400}, {"Type": "System.Collections.Generic.List`1[[BlueTape.Company.API.Models.Errors.ErrorResponse, BlueTape.Company.API, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 401}, {"Type": "System.Void", "MediaTypes": [], "StatusCode": 404}, {"Type": "System.Collections.Generic.List`1[[BlueTape.Company.API.Models.Errors.ErrorResponse, BlueTape.Company.API, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 500}, {"Type": "BlueTape.Company.API.Models.CashFlow.Responses.CashFlowItemResponse[]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "BlueTape.Company.API.Controllers.CompanyController", "Method": "AddManualCashFlow", "RelativePath": "companies/{companyId}/cashFlow/{accountId}/file", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "companyId", "Type": "System.String", "IsRequired": true}, {"Name": "accountId", "Type": "System.String", "IsRequired": true}, {"Name": "userId", "Type": "System.String", "IsRequired": false}, {"Name": "ContentType", "Type": "System.String", "IsRequired": false}, {"Name": "ContentDisposition", "Type": "System.String", "IsRequired": false}, {"Name": "Headers", "Type": "Microsoft.AspNetCore.Http.IHeaderDictionary", "IsRequired": false}, {"Name": "Length", "Type": "System.Int64", "IsRequired": false}, {"Name": "Name", "Type": "System.String", "IsRequired": false}, {"Name": "FileName", "Type": "System.String", "IsRequired": false}], "ReturnTypes": [{"Type": "BlueTape.Company.API.Models.Errors.ErrorResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 400}, {"Type": "System.Collections.Generic.List`1[[BlueTape.Company.API.Models.Errors.ErrorResponse, BlueTape.Company.API, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 401}, {"Type": "BlueTape.Company.API.Models.Errors.ErrorResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 404}, {"Type": "System.Collections.Generic.List`1[[BlueTape.Company.API.Models.Errors.ErrorResponse, BlueTape.Company.API, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 500}, {"Type": "BlueTape.CompanyService.CashFlow.AddManualCashFlow", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "BlueTape.Company.API.Controllers.CompanyController", "Method": "GetPlaidAssetReportSummary", "RelativePath": "companies/{companyId}/cashFlow/aggregate", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "companyId", "Type": "System.String", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Collections.Generic.List`1[[BlueTape.Company.API.Models.Errors.ErrorResponse, BlueTape.Company.API, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 400}, {"Type": "System.Collections.Generic.List`1[[BlueTape.Company.API.Models.Errors.ErrorResponse, BlueTape.Company.API, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 401}, {"Type": "System.Void", "MediaTypes": [], "StatusCode": 404}, {"Type": "System.Collections.Generic.List`1[[BlueTape.Company.API.Models.Errors.ErrorResponse, BlueTape.Company.API, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 500}, {"Type": "BlueTape.Company.API.Models.CashFlow.Responses.CashFlowAggregatedResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "BlueTape.Company.API.Controllers.CompanyController", "Method": "GetDetailedPlaidAssetReport", "RelativePath": "companies/{companyId}/cashFlow/details", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "companyId", "Type": "System.String", "IsRequired": true}, {"Name": "AccountId", "Type": "System.String", "IsRequired": false}, {"Name": "From", "Type": "System.Nullable`1[[System.DateTime, System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "To", "Type": "System.Nullable`1[[System.DateTime, System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "Grouping", "Type": "System.String", "IsRequired": false}], "ReturnTypes": [{"Type": "System.Collections.Generic.List`1[[BlueTape.Company.API.Models.Errors.ErrorResponse, BlueTape.Company.API, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 400}, {"Type": "System.Collections.Generic.List`1[[BlueTape.Company.API.Models.Errors.ErrorResponse, BlueTape.Company.API, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 401}, {"Type": "System.Void", "MediaTypes": [], "StatusCode": 404}, {"Type": "System.Collections.Generic.List`1[[BlueTape.Company.API.Models.Errors.ErrorResponse, BlueTape.Company.API, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 500}, {"Type": "BlueTape.Company.API.Models.CashFlow.Responses.CashFlowResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "BlueTape.Company.API.Controllers.CompanyController", "Method": "GetBankStatements", "RelativePath": "companies/{companyId}/cashFlow/files", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "companyId", "Type": "System.String", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Collections.Generic.List`1[[BlueTape.Company.API.Models.Errors.ErrorResponse, BlueTape.Company.API, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 400}, {"Type": "System.Collections.Generic.List`1[[BlueTape.Company.API.Models.Errors.ErrorResponse, BlueTape.Company.API, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 401}, {"Type": "System.Void", "MediaTypes": [], "StatusCode": 404}, {"Type": "System.Collections.Generic.List`1[[BlueTape.Company.API.Models.Errors.ErrorResponse, BlueTape.Company.API, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 500}, {"Type": "BlueTape.CompanyService.Documents.Responses.BankStatementResponse[]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "BlueTape.Company.API.Controllers.CompanyController", "Method": "AddManualReport", "RelativePath": "companies/{companyId}/cashFlow/manual", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "companyId", "Type": "System.String", "IsRequired": true}, {"Name": "manualCashFlow", "Type": "BlueTape.Company.API.Models.CashFlow.AddManualCashFlow", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Collections.Generic.List`1[[BlueTape.Company.API.Models.Errors.ErrorResponse, BlueTape.Company.API, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 400}, {"Type": "System.Collections.Generic.List`1[[BlueTape.Company.API.Models.Errors.ErrorResponse, BlueTape.Company.API, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 401}, {"Type": "System.Void", "MediaTypes": [], "StatusCode": 404}, {"Type": "System.Collections.Generic.List`1[[BlueTape.Company.API.Models.Errors.ErrorResponse, BlueTape.Company.API, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 500}, {"Type": "BlueTape.Company.API.Models.CashFlow.Responses.AddPlaidCashFlowResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "BlueTape.Company.API.Controllers.CompanyController", "Method": "GetPaginatedCashFlowList", "RelativePath": "companies/{companyId}/cashFlow/paginated", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "companyId", "Type": "System.String", "IsRequired": true}, {"Name": "PageNumber", "Type": "System.Int32", "IsRequired": false}, {"Name": "PageSize", "Type": "System.Int32", "IsRequired": false}], "ReturnTypes": [{"Type": "System.Collections.Generic.List`1[[BlueTape.Company.API.Models.Errors.ErrorResponse, BlueTape.Company.API, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 400}, {"Type": "System.Collections.Generic.List`1[[BlueTape.Company.API.Models.Errors.ErrorResponse, BlueTape.Company.API, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 401}, {"Type": "System.Void", "MediaTypes": [], "StatusCode": 404}, {"Type": "System.Collections.Generic.List`1[[BlueTape.Company.API.Models.Errors.ErrorResponse, BlueTape.Company.API, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 500}, {"Type": "BlueTape.Company.API.Models.PaginatedList`1[[BlueTape.Company.API.Models.CashFlow.Responses.CashFlowItemResponse, BlueTape.Company.API, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "BlueTape.Company.API.Controllers.CompanyController", "Method": "UpdateCompany", "RelativePath": "companies/{id}", "HttpMethod": "PATCH", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.String", "IsRequired": true}, {"Name": "userId", "Type": "System.String", "IsRequired": false}, {"Name": "updateCompanyModel", "Type": "BlueTape.CompanyService.Companies.UpdateCompanyModel", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Collections.Generic.List`1[[BlueTape.Company.API.Models.Errors.ErrorResponse, BlueTape.Company.API, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 400}, {"Type": "System.Collections.Generic.List`1[[BlueTape.Company.API.Models.Errors.ErrorResponse, BlueTape.Company.API, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 401}, {"Type": "System.Void", "MediaTypes": [], "StatusCode": 404}, {"Type": "System.Collections.Generic.List`1[[BlueTape.Company.API.Models.Errors.ErrorResponse, BlueTape.Company.API, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 500}, {"Type": "BlueTape.CompanyService.Companies.CompanyModel", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "BlueTape.Company.API.Controllers.CompanyController", "Method": "GetById", "RelativePath": "companies/{id}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.String", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Collections.Generic.List`1[[BlueTape.Company.API.Models.Errors.ErrorResponse, BlueTape.Company.API, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 400}, {"Type": "System.Collections.Generic.List`1[[BlueTape.Company.API.Models.Errors.ErrorResponse, BlueTape.Company.API, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 401}, {"Type": "System.Void", "MediaTypes": [], "StatusCode": 404}, {"Type": "System.Collections.Generic.List`1[[BlueTape.Company.API.Models.Errors.ErrorResponse, BlueTape.Company.API, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 500}, {"Type": "BlueTape.CompanyService.Companies.CompanyModel", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "BlueTape.Company.API.Controllers.CompanyController", "Method": "UpdateAionSettings", "RelativePath": "companies/{id}/aion/settings", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Guid", "IsRequired": true}, {"Name": "model", "Type": "BlueTape.CompanyService.Companies.CompanyAionSettingsModel", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Collections.Generic.List`1[[BlueTape.Company.API.Models.Errors.ErrorResponse, BlueTape.Company.API, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 400}, {"Type": "System.Collections.Generic.List`1[[BlueTape.Company.API.Models.Errors.ErrorResponse, BlueTape.Company.API, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 401}, {"Type": "System.Void", "MediaTypes": [], "StatusCode": 404}, {"Type": "System.Collections.Generic.List`1[[BlueTape.Company.API.Models.Errors.ErrorResponse, BlueTape.Company.API, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 500}, {"Type": "System.Void", "MediaTypes": [], "StatusCode": 200}]}, {"ContainingType": "BlueTape.Company.API.Controllers.CompanyController", "Method": "AddNote", "RelativePath": "companies/{id}/notes", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.String", "IsRequired": true}, {"Name": "userId", "Type": "System.String", "IsRequired": false}, {"Name": "note", "Type": "BlueTape.CompanyService.Companies.CreateCompanyNoteModel", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Collections.Generic.List`1[[BlueTape.Company.API.Models.Errors.ErrorResponse, BlueTape.Company.API, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 400}, {"Type": "System.Collections.Generic.List`1[[BlueTape.Company.API.Models.Errors.ErrorResponse, BlueTape.Company.API, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 401}, {"Type": "System.Void", "MediaTypes": [], "StatusCode": 404}, {"Type": "System.Collections.Generic.List`1[[BlueTape.Company.API.Models.Errors.ErrorResponse, BlueTape.Company.API, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 500}, {"Type": "BlueTape.CompanyService.Companies.CompanyNoteModel", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "BlueTape.Company.API.Controllers.CompanyController", "Method": "GetPaymentDetailsById", "RelativePath": "companies/{id}/paymentDetails", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.String", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Collections.Generic.List`1[[BlueTape.Company.API.Models.Errors.ErrorResponse, BlueTape.Company.API, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 400}, {"Type": "System.Collections.Generic.List`1[[BlueTape.Company.API.Models.Errors.ErrorResponse, BlueTape.Company.API, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 401}, {"Type": "System.Void", "MediaTypes": [], "StatusCode": 404}, {"Type": "System.Collections.Generic.List`1[[BlueTape.Company.API.Models.Errors.ErrorResponse, BlueTape.Company.API, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 500}, {"Type": "BlueTape.CompanyService.Companies.CompanyModel", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "BlueTape.Company.API.Controllers.CompanyController", "Method": "ValidateManualCashFlow", "RelativePath": "companies/cashFlow/validate-file", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "ContentType", "Type": "System.String", "IsRequired": false}, {"Name": "ContentDisposition", "Type": "System.String", "IsRequired": false}, {"Name": "Headers", "Type": "Microsoft.AspNetCore.Http.IHeaderDictionary", "IsRequired": false}, {"Name": "Length", "Type": "System.Int64", "IsRequired": false}, {"Name": "Name", "Type": "System.String", "IsRequired": false}, {"Name": "FileName", "Type": "System.String", "IsRequired": false}], "ReturnTypes": [{"Type": "BlueTape.Company.API.Models.Errors.ErrorResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 400}, {"Type": "System.Collections.Generic.List`1[[BlueTape.Company.API.Models.Errors.ErrorResponse, BlueTape.Company.API, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 401}, {"Type": "BlueTape.Company.API.Models.Errors.ErrorResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 404}, {"Type": "System.Collections.Generic.List`1[[BlueTape.Company.API.Models.Errors.ErrorResponse, BlueTape.Company.API, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 500}, {"Type": "BlueTape.Company.API.Models.CashFlow.Responses.ManualCashFlowValidationResultResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "BlueTape.Company.API.Controllers.CompanyController", "Method": "GetByIds", "RelativePath": "companies/getByIds", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "ids", "Type": "System.String[]", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Collections.Generic.List`1[[BlueTape.Company.API.Models.Errors.ErrorResponse, BlueTape.Company.API, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 400}, {"Type": "System.Collections.Generic.List`1[[BlueTape.Company.API.Models.Errors.ErrorResponse, BlueTape.Company.API, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 401}, {"Type": "System.Void", "MediaTypes": [], "StatusCode": 404}, {"Type": "System.Collections.Generic.List`1[[BlueTape.Company.API.Models.Errors.ErrorResponse, BlueTape.Company.API, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 500}, {"Type": "BlueTape.CompanyService.Companies.CompanyModel[]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "BlueTape.Company.API.Controllers.v2.CompanyController", "Method": "Get", "RelativePath": "companies/v2", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "validAccountsOnly", "Type": "System.Nullable`1[[<PERSON><PERSON>Boolean, System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}], "ReturnTypes": [{"Type": "System.Collections.Generic.List`1[[BlueTape.Company.API.Models.Errors.ErrorResponse, BlueTape.Company.API, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 400}, {"Type": "System.Collections.Generic.List`1[[BlueTape.Company.API.Models.Errors.ErrorResponse, BlueTape.Company.API, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 401}, {"Type": "System.Void", "MediaTypes": [], "StatusCode": 404}, {"Type": "System.Collections.Generic.List`1[[BlueTape.Company.API.Models.Errors.ErrorResponse, BlueTape.Company.API, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 500}]}, {"ContainingType": "BlueTape.Company.API.Controllers.v2.CompanyController", "Method": "GetAllByAccountStatuses", "RelativePath": "companies/v2", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "statuses", "Type": "BlueTape.CompanyService.Common.Enums.AccountStatusEnum[]", "IsRequired": true}, {"Name": "isIdOnly", "Type": "System.Nullable`1[[<PERSON><PERSON>Boolean, System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}], "ReturnTypes": [{"Type": "System.Collections.Generic.List`1[[BlueTape.Company.API.Models.Errors.ErrorResponse, BlueTape.Company.API, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 400}, {"Type": "System.Collections.Generic.List`1[[BlueTape.Company.API.Models.Errors.ErrorResponse, BlueTape.Company.API, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 401}, {"Type": "System.Void", "MediaTypes": [], "StatusCode": 404}, {"Type": "System.Collections.Generic.List`1[[BlueTape.Company.API.Models.Errors.ErrorResponse, BlueTape.Company.API, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 500}]}, {"ContainingType": "BlueTape.Company.API.Controllers.v2.CompanyController", "Method": "GetById_V2", "RelativePath": "companies/v2/{id}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Guid", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Collections.Generic.List`1[[BlueTape.Company.API.Models.Errors.ErrorResponse, BlueTape.Company.API, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 400}, {"Type": "System.Collections.Generic.List`1[[BlueTape.Company.API.Models.Errors.ErrorResponse, BlueTape.Company.API, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 401}, {"Type": "System.Void", "MediaTypes": [], "StatusCode": 404}, {"Type": "System.Collections.Generic.List`1[[BlueTape.Company.API.Models.Errors.ErrorResponse, BlueTape.Company.API, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 500}, {"Type": "BlueTape.CompanyService.Companies.CompanyModel", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "BlueTape.Company.API.Controllers.v2.CompanyController", "Method": "GetById_V2", "RelativePath": "companies/v2/getByIds", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "ids", "Type": "System.Guid[]", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Collections.Generic.List`1[[BlueTape.Company.API.Models.Errors.ErrorResponse, BlueTape.Company.API, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 400}, {"Type": "System.Collections.Generic.List`1[[BlueTape.Company.API.Models.Errors.ErrorResponse, BlueTape.Company.API, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 401}, {"Type": "System.Void", "MediaTypes": [], "StatusCode": 404}, {"Type": "System.Collections.Generic.List`1[[BlueTape.Company.API.Models.Errors.ErrorResponse, BlueTape.Company.API, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 500}, {"Type": "BlueTape.CompanyService.Companies.CompanyModel[]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "BlueTape.Company.API.Controllers.v2.CompanyController", "Method": "TryMigrateLegacyCompanyIds", "RelativePath": "companies/v2/try-migrate", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "ids", "Type": "System.String[]", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Collections.Generic.List`1[[BlueTape.Company.API.Models.Errors.ErrorResponse, BlueTape.Company.API, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 400}, {"Type": "System.Collections.Generic.List`1[[BlueTape.Company.API.Models.Errors.ErrorResponse, BlueTape.Company.API, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 401}, {"Type": "System.Void", "MediaTypes": [], "StatusCode": 404}, {"Type": "System.Collections.Generic.List`1[[BlueTape.Company.API.Models.Errors.ErrorResponse, BlueTape.Company.API, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 500}, {"Type": "BlueTape.CompanyService.Companies.MigrateResultModel[]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "BlueTape.Company.API.Controllers.v2.CompanyController", "Method": "GetWithPagination", "RelativePath": "companies/v2/with-pagination", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "pageSize", "Type": "System.Int32", "IsRequired": false}, {"Name": "pageNumber", "Type": "System.Int32", "IsRequired": false}, {"Name": "validAccountsOnly", "Type": "System.Boolean", "IsRequired": false}], "ReturnTypes": [{"Type": "System.Collections.Generic.List`1[[BlueTape.Company.API.Models.Errors.ErrorResponse, BlueTape.Company.API, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 400}, {"Type": "System.Collections.Generic.List`1[[BlueTape.Company.API.Models.Errors.ErrorResponse, BlueTape.Company.API, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 401}, {"Type": "System.Void", "MediaTypes": [], "StatusCode": 404}, {"Type": "System.Collections.Generic.List`1[[BlueTape.Company.API.Models.Errors.ErrorResponse, BlueTape.Company.API, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 500}]}, {"ContainingType": "BlueTape.Company.API.Controllers.CustomerController", "Method": "SetCustomerIhcSettings", "RelativePath": "customers/{customerId}", "HttpMethod": "PATCH", "IsController": true, "Order": 0, "Parameters": [{"Name": "customerId", "Type": "System.String", "IsRequired": true}, {"Name": "updateCustomerModel", "Type": "BlueTape.CompanyService.Customers.UpdateCustomerModel", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Collections.Generic.List`1[[BlueTape.Company.API.Models.Errors.ErrorResponse, BlueTape.Company.API, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 400}, {"Type": "System.Collections.Generic.List`1[[BlueTape.Company.API.Models.Errors.ErrorResponse, BlueTape.Company.API, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 401}, {"Type": "System.Void", "MediaTypes": [], "StatusCode": 404}, {"Type": "System.Collections.Generic.List`1[[BlueTape.Company.API.Models.Errors.ErrorResponse, BlueTape.Company.API, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 500}, {"Type": "BlueTape.CompanyService.Customers.CustomerModel", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "BlueTape.Company.API.Controllers.CustomerController", "Method": "GetById", "RelativePath": "customers/{id}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.String", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Collections.Generic.List`1[[BlueTape.Company.API.Models.Errors.ErrorResponse, BlueTape.Company.API, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 400}, {"Type": "System.Collections.Generic.List`1[[BlueTape.Company.API.Models.Errors.ErrorResponse, BlueTape.Company.API, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 401}, {"Type": "System.Void", "MediaTypes": [], "StatusCode": 404}, {"Type": "System.Collections.Generic.List`1[[BlueTape.Company.API.Models.Errors.ErrorResponse, BlueTape.Company.API, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 500}, {"Type": "BlueTape.CompanyService.Customers.CustomerModel", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "BlueTape.Company.API.Controllers.CustomerController", "Method": "GetBillingContactsById", "RelativePath": "customers/billing-contacts/{id}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.String", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Collections.Generic.List`1[[BlueTape.Company.API.Models.Errors.ErrorResponse, BlueTape.Company.API, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 400}, {"Type": "System.Collections.Generic.List`1[[BlueTape.Company.API.Models.Errors.ErrorResponse, BlueTape.Company.API, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 401}, {"Type": "System.Void", "MediaTypes": [], "StatusCode": 404}, {"Type": "System.Collections.Generic.List`1[[BlueTape.Company.API.Models.Errors.ErrorResponse, BlueTape.Company.API, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 500}, {"Type": "System.Collections.Generic.List`1[[BlueTape.CompanyService.Customers.CustomerModel, BlueTape.CompanyService, Version=1.3.3.0, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "BlueTape.Company.API.Controllers.CustomerController", "Method": "GetByCompanyId", "RelativePath": "customers/companies/{id}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.String", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Collections.Generic.List`1[[BlueTape.Company.API.Models.Errors.ErrorResponse, BlueTape.Company.API, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 400}, {"Type": "System.Collections.Generic.List`1[[BlueTape.Company.API.Models.Errors.ErrorResponse, BlueTape.Company.API, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 401}, {"Type": "System.Void", "MediaTypes": [], "StatusCode": 404}, {"Type": "System.Collections.Generic.List`1[[BlueTape.Company.API.Models.Errors.ErrorResponse, BlueTape.Company.API, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 500}, {"Type": "System.Collections.Generic.List`1[[BlueTape.CompanyService.Customers.CustomerModel, BlueTape.CompanyService, Version=1.3.3.0, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "BlueTape.Company.API.Controllers.CustomerController", "Method": "GetCustomersByMerchantIdAndCompanyId", "RelativePath": "customers/companies/{merchantId}/customerCompanyId/{customerCompanyId}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "merchantId", "Type": "System.String", "IsRequired": true}, {"Name": "customerCompanyId", "Type": "System.String", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Collections.Generic.List`1[[BlueTape.Company.API.Models.Errors.ErrorResponse, BlueTape.Company.API, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 400}, {"Type": "System.Collections.Generic.List`1[[BlueTape.Company.API.Models.Errors.ErrorResponse, BlueTape.Company.API, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 401}, {"Type": "System.Void", "MediaTypes": [], "StatusCode": 404}, {"Type": "System.Collections.Generic.List`1[[BlueTape.Company.API.Models.Errors.ErrorResponse, BlueTape.Company.API, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 500}, {"Type": "System.Collections.Generic.List`1[[BlueTape.CompanyService.Customers.CustomerModel, BlueTape.CompanyService, Version=1.3.3.0, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "BlueTape.Company.API.Controllers.CustomerController", "Method": "GetByCompanyIds", "RelativePath": "customers/companies/getByIds", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "ids", "Type": "System.String[]", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Collections.Generic.List`1[[BlueTape.Company.API.Models.Errors.ErrorResponse, BlueTape.Company.API, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 400}, {"Type": "System.Collections.Generic.List`1[[BlueTape.Company.API.Models.Errors.ErrorResponse, BlueTape.Company.API, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 401}, {"Type": "System.Void", "MediaTypes": [], "StatusCode": 404}, {"Type": "System.Collections.Generic.List`1[[BlueTape.Company.API.Models.Errors.ErrorResponse, BlueTape.Company.API, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 500}, {"Type": "System.Collections.Generic.List`1[[BlueTape.CompanyService.Customers.CustomerModel, BlueTape.CompanyService, Version=1.3.3.0, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "BlueTape.Company.API.Controllers.CustomerController", "Method": "GetByIds", "RelativePath": "customers/getByIds", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "ids", "Type": "System.String[]", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Collections.Generic.List`1[[BlueTape.Company.API.Models.Errors.ErrorResponse, BlueTape.Company.API, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 400}, {"Type": "System.Collections.Generic.List`1[[BlueTape.Company.API.Models.Errors.ErrorResponse, BlueTape.Company.API, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 401}, {"Type": "System.Void", "MediaTypes": [], "StatusCode": 404}, {"Type": "System.Collections.Generic.List`1[[BlueTape.Company.API.Models.Errors.ErrorResponse, BlueTape.Company.API, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 500}, {"Type": "System.Collections.Generic.List`1[[BlueTape.CompanyService.Customers.CustomerModel, BlueTape.CompanyService, Version=1.3.3.0, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "BlueTape.Company.API.Controllers.CustomerController", "Method": "GetByIntegrationContactIdAsync", "RelativePath": "customers/integration/{integrationId}/contact/{contactId}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "contactId", "Type": "System.String", "IsRequired": true}, {"Name": "integrationId", "Type": "System.String", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Collections.Generic.List`1[[BlueTape.Company.API.Models.Errors.ErrorResponse, BlueTape.Company.API, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 400}, {"Type": "System.Collections.Generic.List`1[[BlueTape.Company.API.Models.Errors.ErrorResponse, BlueTape.Company.API, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 401}, {"Type": "System.Void", "MediaTypes": [], "StatusCode": 404}, {"Type": "System.Collections.Generic.List`1[[BlueTape.Company.API.Models.Errors.ErrorResponse, BlueTape.Company.API, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 500}, {"Type": "BlueTape.CompanyService.Customers.CustomerModel", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "BlueTape.Company.API.Controllers.Document.DocumentController", "Method": "GetDocumentApproval", "RelativePath": "documents/{id}/approvals", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Guid", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Collections.Generic.List`1[[BlueTape.Company.API.Models.Errors.ErrorResponse, BlueTape.Company.API, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 400}, {"Type": "System.Collections.Generic.List`1[[BlueTape.Company.API.Models.Errors.ErrorResponse, BlueTape.Company.API, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 401}, {"Type": "System.Void", "MediaTypes": [], "StatusCode": 404}, {"Type": "System.Collections.Generic.List`1[[BlueTape.Company.API.Models.Errors.ErrorResponse, BlueTape.Company.API, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 500}, {"Type": "System.Collections.Generic.List`1[[BlueTape.CompanyService.Documents.Responses.DocumentApprovalResponse, BlueTape.CompanyService, Version=1.3.3.0, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "BlueTape.Company.API.Controllers.Document.DocumentController", "Method": "CreateDocumentApproval", "RelativePath": "documents/{id}/approvals", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Guid", "IsRequired": true}, {"Name": "X-User-Id", "Type": "System.String", "IsRequired": false}, {"Name": "request", "Type": "BlueTape.Company.API.Models.Documents.Requests.CreateDocumentApprovalRequest", "IsRequired": true}, {"Name": "alreadyApproved", "Type": "System.Boolean", "IsRequired": false}], "ReturnTypes": [{"Type": "System.Collections.Generic.List`1[[BlueTape.Company.API.Models.Errors.ErrorResponse, BlueTape.Company.API, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 400}, {"Type": "System.Collections.Generic.List`1[[BlueTape.Company.API.Models.Errors.ErrorResponse, BlueTape.Company.API, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 401}, {"Type": "System.Void", "MediaTypes": [], "StatusCode": 404}, {"Type": "System.Collections.Generic.List`1[[BlueTape.Company.API.Models.Errors.ErrorResponse, BlueTape.Company.API, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 500}, {"Type": "System.String", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "BlueTape.Company.API.Controllers.Document.DocumentController", "Method": "GetByDocumentId", "RelativePath": "documents/{id}/path", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Guid", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Collections.Generic.List`1[[BlueTape.Company.API.Models.Errors.ErrorResponse, BlueTape.Company.API, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 400}, {"Type": "System.Collections.Generic.List`1[[BlueTape.Company.API.Models.Errors.ErrorResponse, BlueTape.Company.API, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 401}, {"Type": "System.Void", "MediaTypes": [], "StatusCode": 404}, {"Type": "System.Collections.Generic.List`1[[BlueTape.Company.API.Models.Errors.ErrorResponse, BlueTape.Company.API, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 500}, {"Type": "System.String", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "BlueTape.Company.API.Controllers.Document.DocumentLegacyController", "Method": "GetAllAsync", "RelativePath": "documents/all", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "System.Collections.Generic.List`1[[BlueTape.Company.API.Models.Errors.ErrorResponse, BlueTape.Company.API, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 400}, {"Type": "System.Collections.Generic.List`1[[BlueTape.Company.API.Models.Errors.ErrorResponse, BlueTape.Company.API, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 401}, {"Type": "System.Void", "MediaTypes": [], "StatusCode": 404}, {"Type": "System.Collections.Generic.List`1[[BlueTape.Company.API.Models.Errors.ErrorResponse, BlueTape.Company.API, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 500}, {"Type": "System.Collections.Generic.List`1[[BlueTape.CompanyService.Documents.Responses.DocumentReportResponse, BlueTape.CompanyService, Version=1.3.3.0, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "BlueTape.Company.API.Controllers.Document.DocumentController", "Method": "AuthorizeApproval", "RelativePath": "documents/approvals/authorize", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "BlueTape.Company.API.Models.Documents.Requests.AuthorizeApprovalRequest", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Collections.Generic.List`1[[BlueTape.Company.API.Models.Errors.ErrorResponse, BlueTape.Company.API, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 400}, {"Type": "System.Collections.Generic.List`1[[BlueTape.Company.API.Models.Errors.ErrorResponse, BlueTape.Company.API, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 401}, {"Type": "System.Void", "MediaTypes": [], "StatusCode": 404}, {"Type": "System.Collections.Generic.List`1[[BlueTape.Company.API.Models.Errors.ErrorResponse, BlueTape.Company.API, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 500}, {"Type": "BlueTape.CompanyService.Documents.Responses.DocumentApprovalResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "BlueTape.Company.API.Controllers.Document.DocumentController", "Method": "CreateDocument", "RelativePath": "documents/company/{companyId}/reference/{referenceId}", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "companyId", "Type": "System.String", "IsRequired": true}, {"Name": "referenceId", "Type": "System.String", "IsRequired": true}, {"Name": "applicationRequest", "Type": "BlueTape.Company.API.Models.Documents.Requests.DocumentLoanApplicationRequest", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Collections.Generic.List`1[[BlueTape.Company.API.Models.Errors.ErrorResponse, BlueTape.Company.API, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 400}, {"Type": "System.Collections.Generic.List`1[[BlueTape.Company.API.Models.Errors.ErrorResponse, BlueTape.Company.API, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 401}, {"Type": "System.Void", "MediaTypes": [], "StatusCode": 404}, {"Type": "System.Collections.Generic.List`1[[BlueTape.Company.API.Models.Errors.ErrorResponse, BlueTape.Company.API, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 500}, {"Type": "BlueTape.CompanyService.Documents.Responses.DocumentLoanApplicationResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 201}]}, {"ContainingType": "BlueTape.Company.API.Controllers.Document.DocumentController", "Method": "GetByCompanyIdAndTemplateTypes", "RelativePath": "documents/company/{id}/all", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.String", "IsRequired": true}, {"Name": "types", "Type": "BlueTape.CompanyService.Documents.Enums.TemplateTypeFilter[]", "IsRequired": false}], "ReturnTypes": [{"Type": "System.Collections.Generic.List`1[[BlueTape.Company.API.Models.Errors.ErrorResponse, BlueTape.Company.API, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 400}, {"Type": "System.Collections.Generic.List`1[[BlueTape.Company.API.Models.Errors.ErrorResponse, BlueTape.Company.API, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 401}, {"Type": "System.Void", "MediaTypes": [], "StatusCode": 404}, {"Type": "System.Collections.Generic.List`1[[BlueTape.Company.API.Models.Errors.ErrorResponse, BlueTape.Company.API, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 500}, {"Type": "System.Collections.Generic.List`1[[BlueTape.CompanyService.Documents.Responses.DocumentLoanApplicationResponse, BlueTape.CompanyService, Version=1.3.3.0, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "BlueTape.Company.API.Controllers.Document.DocumentController", "Method": "GetByCompanyIdAndTemplateType", "RelativePath": "documents/company/{id}/type/{code}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.String", "IsRequired": true}, {"Name": "code", "Type": "System.String", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Collections.Generic.List`1[[BlueTape.Company.API.Models.Errors.ErrorResponse, BlueTape.Company.API, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 400}, {"Type": "System.Collections.Generic.List`1[[BlueTape.Company.API.Models.Errors.ErrorResponse, BlueTape.Company.API, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 401}, {"Type": "System.Void", "MediaTypes": [], "StatusCode": 404}, {"Type": "System.Collections.Generic.List`1[[BlueTape.Company.API.Models.Errors.ErrorResponse, BlueTape.Company.API, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 500}, {"Type": "System.Collections.Generic.List`1[[BlueTape.CompanyService.Documents.Responses.DocumentLoanApplicationResponse, BlueTape.CompanyService, Version=1.3.3.0, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "BlueTape.Company.API.Controllers.Document.DocumentController", "Method": "GetMetaDataOfLatestAgreement", "RelativePath": "documents/company/{id}/type/{code}/metadata", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.String", "IsRequired": true}, {"Name": "code", "Type": "System.String", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Collections.Generic.List`1[[BlueTape.Company.API.Models.Errors.ErrorResponse, BlueTape.Company.API, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 400}, {"Type": "System.Collections.Generic.List`1[[BlueTape.Company.API.Models.Errors.ErrorResponse, BlueTape.Company.API, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 401}, {"Type": "System.Void", "MediaTypes": [], "StatusCode": 404}, {"Type": "System.Collections.Generic.List`1[[BlueTape.Company.API.Models.Errors.ErrorResponse, BlueTape.Company.API, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 500}, {"Type": "BlueTape.CompanyService.Documents.Responses.DocumentMetadataResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "BlueTape.Company.API.Controllers.Document.DocumentController", "Method": "GetByLoanApplicationId", "RelativePath": "documents/loan-application/{id}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.String", "IsRequired": true}, {"Name": "code", "Type": "System.String", "IsRequired": false}], "ReturnTypes": [{"Type": "System.Collections.Generic.List`1[[BlueTape.Company.API.Models.Errors.ErrorResponse, BlueTape.Company.API, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 400}, {"Type": "System.Collections.Generic.List`1[[BlueTape.Company.API.Models.Errors.ErrorResponse, BlueTape.Company.API, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 401}, {"Type": "System.Void", "MediaTypes": [], "StatusCode": 404}, {"Type": "System.Collections.Generic.List`1[[BlueTape.Company.API.Models.Errors.ErrorResponse, BlueTape.Company.API, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 500}, {"Type": "System.Collections.Generic.List`1[[BlueTape.CompanyService.Documents.Responses.DocumentLoanApplicationResponse, BlueTape.CompanyService, Version=1.3.3.0, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "BlueTape.Company.API.Controllers.Document.DocumentController", "Method": "CreateLoanApplicationDocument", "RelativePath": "documents/loan-application/{id}", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.String", "IsRequired": true}, {"Name": "applicationRequest", "Type": "BlueTape.Company.API.Models.Documents.Requests.DocumentLoanApplicationRequest", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Collections.Generic.List`1[[BlueTape.Company.API.Models.Errors.ErrorResponse, BlueTape.Company.API, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 400}, {"Type": "System.Collections.Generic.List`1[[BlueTape.Company.API.Models.Errors.ErrorResponse, BlueTape.Company.API, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 401}, {"Type": "System.Void", "MediaTypes": [], "StatusCode": 404}, {"Type": "System.Collections.Generic.List`1[[BlueTape.Company.API.Models.Errors.ErrorResponse, BlueTape.Company.API, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 500}, {"Type": "BlueTape.CompanyService.Documents.Responses.DocumentLoanApplicationResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 201}]}, {"ContainingType": "BlueTape.Company.API.Controllers.Document.DocumentController", "Method": "Delete", "RelativePath": "documents/loan-application/{id}", "HttpMethod": "DELETE", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.String", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Collections.Generic.List`1[[BlueTape.Company.API.Models.Errors.ErrorResponse, BlueTape.Company.API, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 400}, {"Type": "System.Collections.Generic.List`1[[BlueTape.Company.API.Models.Errors.ErrorResponse, BlueTape.Company.API, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 401}, {"Type": "System.Void", "MediaTypes": [], "StatusCode": 404}, {"Type": "System.Collections.Generic.List`1[[BlueTape.Company.API.Models.Errors.ErrorResponse, BlueTape.Company.API, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 500}, {"Type": "BlueTape.CompanyService.Documents.Responses.NewDocumentNameResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "BlueTape.Company.API.Controllers.Document.DocumentLegacyController", "Method": "GetLoanApplicationForAgreementCreation", "RelativePath": "documents/loan-application/agreement", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "System.Collections.Generic.List`1[[BlueTape.Company.API.Models.Errors.ErrorResponse, BlueTape.Company.API, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 400}, {"Type": "System.Collections.Generic.List`1[[BlueTape.Company.API.Models.Errors.ErrorResponse, BlueTape.Company.API, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 401}, {"Type": "System.Void", "MediaTypes": [], "StatusCode": 404}, {"Type": "System.Collections.Generic.List`1[[BlueTape.Company.API.Models.Errors.ErrorResponse, BlueTape.Company.API, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 500}, {"Type": "System.Collections.Generic.List`1[[BlueTape.Document.Domain.DTOs.LoanApplicationMigrationInfo, BlueTape.Document.Domain, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "BlueTape.Company.API.Controllers.Document.DocumentLegacyController", "Method": "CreateLoanApplicationDocumentAsync", "RelativePath": "documents/loan-application/legacy", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "applicationRequest", "Type": "BlueTape.Company.API.Models.Documents.Requests.DocumentLoanApplicationLegacyRequest", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Collections.Generic.List`1[[BlueTape.Company.API.Models.Errors.ErrorResponse, BlueTape.Company.API, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 400}, {"Type": "System.Collections.Generic.List`1[[BlueTape.Company.API.Models.Errors.ErrorResponse, BlueTape.Company.API, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 401}, {"Type": "System.Void", "MediaTypes": [], "StatusCode": 404}, {"Type": "System.Collections.Generic.List`1[[BlueTape.Company.API.Models.Errors.ErrorResponse, BlueTape.Company.API, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 500}, {"Type": "BlueTape.CompanyService.Documents.Responses.DocumentLoanApplicationResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 201}]}, {"ContainingType": "BlueTape.Company.API.Controllers.Document.DocumentController", "Method": "GetNewLoanApplicationDocumentName", "RelativePath": "documents/new/{id}/by-template-id", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.String", "IsRequired": true}, {"Name": "templateId", "Type": "System.Guid", "IsRequired": false}], "ReturnTypes": [{"Type": "System.Collections.Generic.List`1[[BlueTape.Company.API.Models.Errors.ErrorResponse, BlueTape.Company.API, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 400}, {"Type": "System.Collections.Generic.List`1[[BlueTape.Company.API.Models.Errors.ErrorResponse, BlueTape.Company.API, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 401}, {"Type": "System.Void", "MediaTypes": [], "StatusCode": 404}, {"Type": "System.Collections.Generic.List`1[[BlueTape.Company.API.Models.Errors.ErrorResponse, BlueTape.Company.API, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 500}, {"Type": "BlueTape.CompanyService.Documents.Responses.NewDocumentNameResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "BlueTape.Company.API.Controllers.Document.DocumentController", "Method": "GetNewDocumentNameByLatestType", "RelativePath": "documents/new/{id}/by-template-type", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.String", "IsRequired": true}, {"Name": "code", "Type": "System.String", "IsRequired": false}], "ReturnTypes": [{"Type": "System.Collections.Generic.List`1[[BlueTape.Company.API.Models.Errors.ErrorResponse, BlueTape.Company.API, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 400}, {"Type": "System.Collections.Generic.List`1[[BlueTape.Company.API.Models.Errors.ErrorResponse, BlueTape.Company.API, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 401}, {"Type": "System.Void", "MediaTypes": [], "StatusCode": 404}, {"Type": "System.Collections.Generic.List`1[[BlueTape.Company.API.Models.Errors.ErrorResponse, BlueTape.Company.API, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 500}, {"Type": "BlueTape.CompanyService.Documents.Responses.NewDocumentNameResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "BlueTape.Company.API.Controllers.Document.DocumentController", "Method": "GetNewDocumentNameByOwnerId", "RelativePath": "documents/new/{templateType}/company/{companyId}/reference/{referenceId}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "templateType", "Type": "System.String", "IsRequired": true}, {"Name": "companyId", "Type": "System.String", "IsRequired": true}, {"Name": "referenceId", "Type": "System.String", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Collections.Generic.List`1[[BlueTape.Company.API.Models.Errors.ErrorResponse, BlueTape.Company.API, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 400}, {"Type": "System.Collections.Generic.List`1[[BlueTape.Company.API.Models.Errors.ErrorResponse, BlueTape.Company.API, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 401}, {"Type": "System.Void", "MediaTypes": [], "StatusCode": 404}, {"Type": "System.Collections.Generic.List`1[[BlueTape.Company.API.Models.Errors.ErrorResponse, BlueTape.Company.API, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 500}, {"Type": "BlueTape.CompanyService.Documents.Responses.NewDocumentNameResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "BlueTape.Company.API.Controllers.Document.DocumentController", "Method": "GetLatestByReferenceIdAndTemplateType", "RelativePath": "documents/reference/{referenceId}/type/{type}/latest", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "referenceId", "Type": "System.String", "IsRequired": true}, {"Name": "type", "Type": "System.String", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Collections.Generic.List`1[[BlueTape.Company.API.Models.Errors.ErrorResponse, BlueTape.Company.API, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 400}, {"Type": "System.Collections.Generic.List`1[[BlueTape.Company.API.Models.Errors.ErrorResponse, BlueTape.Company.API, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 401}, {"Type": "System.Void", "MediaTypes": [], "StatusCode": 404}, {"Type": "System.Collections.Generic.List`1[[BlueTape.Company.API.Models.Errors.ErrorResponse, BlueTape.Company.API, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 500}, {"Type": "BlueTape.Company.API.Models.Documents.Responses.DocumentResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "BlueTape.Company.API.Controllers.Document.TemplateController", "Method": "GetLatestVersionByTemplateType", "RelativePath": "documents/templates", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "code", "Type": "System.String", "IsRequired": false}], "ReturnTypes": [{"Type": "System.Collections.Generic.List`1[[BlueTape.Company.API.Models.Errors.ErrorResponse, BlueTape.Company.API, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 400}, {"Type": "System.Collections.Generic.List`1[[BlueTape.Company.API.Models.Errors.ErrorResponse, BlueTape.Company.API, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 401}, {"Type": "System.Void", "MediaTypes": [], "StatusCode": 404}, {"Type": "System.Collections.Generic.List`1[[BlueTape.Company.API.Models.Errors.ErrorResponse, BlueTape.Company.API, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 500}, {"Type": "BlueTape.CompanyService.Documents.Responses.TemplateResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "BlueTape.Company.API.Controllers.Document.TemplateController", "Method": "CreateTemplate", "RelativePath": "documents/templates", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "templateRequest", "Type": "BlueTape.Company.API.Models.Documents.Requests.CreateTemplateRequest", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Collections.Generic.List`1[[BlueTape.Company.API.Models.Errors.ErrorResponse, BlueTape.Company.API, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 400}, {"Type": "System.Collections.Generic.List`1[[BlueTape.Company.API.Models.Errors.ErrorResponse, BlueTape.Company.API, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 401}, {"Type": "System.Void", "MediaTypes": [], "StatusCode": 404}, {"Type": "System.Collections.Generic.List`1[[BlueTape.Company.API.Models.Errors.ErrorResponse, BlueTape.Company.API, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 500}, {"Type": "BlueTape.CompanyService.Documents.Responses.TemplateResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 201}]}, {"ContainingType": "BlueTape.Company.API.Controllers.Document.TemplateController", "Method": "GetById", "RelativePath": "documents/templates/{id}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Guid", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Collections.Generic.List`1[[BlueTape.Company.API.Models.Errors.ErrorResponse, BlueTape.Company.API, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 400}, {"Type": "System.Collections.Generic.List`1[[BlueTape.Company.API.Models.Errors.ErrorResponse, BlueTape.Company.API, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 401}, {"Type": "System.Void", "MediaTypes": [], "StatusCode": 404}, {"Type": "System.Collections.Generic.List`1[[BlueTape.Company.API.Models.Errors.ErrorResponse, BlueTape.Company.API, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 500}, {"Type": "BlueTape.CompanyService.Documents.Responses.TemplateResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "BlueTape.Company.API.Controllers.Document.TemplateController", "Method": "UpdateTemplateFileName", "RelativePath": "documents/templates/{templateId}/future-file-name", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "templateId", "Type": "System.Guid", "IsRequired": true}, {"Name": "request", "Type": "BlueTape.Company.API.Models.Documents.Requests.UpdateFutureFileNameTemplateRequest", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Collections.Generic.List`1[[BlueTape.Company.API.Models.Errors.ErrorResponse, BlueTape.Company.API, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 400}, {"Type": "System.Collections.Generic.List`1[[BlueTape.Company.API.Models.Errors.ErrorResponse, BlueTape.Company.API, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 401}, {"Type": "System.Void", "MediaTypes": [], "StatusCode": 404}, {"Type": "System.Collections.Generic.List`1[[BlueTape.Company.API.Models.Errors.ErrorResponse, BlueTape.Company.API, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 500}, {"Type": "BlueTape.CompanyService.Documents.Responses.TemplateResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 201}]}, {"ContainingType": "BlueTape.Company.API.Controllers.Document.TemplateController", "Method": "UpdateTemplatePath", "RelativePath": "documents/templates/{templateId}/update-file-path", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "templateId", "Type": "System.Guid", "IsRequired": true}, {"Name": "request", "Type": "BlueTape.Company.API.Models.Documents.Requests.UpdateFutureFileNameTemplateRequest", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Collections.Generic.List`1[[BlueTape.Company.API.Models.Errors.ErrorResponse, BlueTape.Company.API, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 400}, {"Type": "System.Collections.Generic.List`1[[BlueTape.Company.API.Models.Errors.ErrorResponse, BlueTape.Company.API, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 401}, {"Type": "System.Void", "MediaTypes": [], "StatusCode": 404}, {"Type": "System.Collections.Generic.List`1[[BlueTape.Company.API.Models.Errors.ErrorResponse, BlueTape.Company.API, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 500}, {"Type": "BlueTape.CompanyService.Documents.Responses.TemplateResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 201}]}, {"ContainingType": "BlueTape.Company.API.Controllers.Document.TemplateController", "Method": "GetAllVersionsByTemplateType", "RelativePath": "documents/templates/allversions", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "code", "Type": "System.String", "IsRequired": false}], "ReturnTypes": [{"Type": "System.Collections.Generic.List`1[[BlueTape.Company.API.Models.Errors.ErrorResponse, BlueTape.Company.API, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 400}, {"Type": "System.Collections.Generic.List`1[[BlueTape.Company.API.Models.Errors.ErrorResponse, BlueTape.Company.API, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 401}, {"Type": "System.Void", "MediaTypes": [], "StatusCode": 404}, {"Type": "System.Collections.Generic.List`1[[BlueTape.Company.API.Models.Errors.ErrorResponse, BlueTape.Company.API, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 500}, {"Type": "System.Collections.Generic.List`1[[BlueTape.CompanyService.Documents.Responses.TemplateResponse, BlueTape.CompanyService, Version=1.3.3.0, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "BlueTape.Company.API.Controllers.Document.DocumentController", "Method": "ValidateJwt", "RelativePath": "documents/validate-jwt", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "jwt", "Type": "System.String", "IsRequired": false}], "ReturnTypes": [{"Type": "System.Collections.Generic.List`1[[BlueTape.Company.API.Models.Errors.ErrorResponse, BlueTape.Company.API, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 400}, {"Type": "System.Collections.Generic.List`1[[BlueTape.Company.API.Models.Errors.ErrorResponse, BlueTape.Company.API, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 401}, {"Type": "System.Void", "MediaTypes": [], "StatusCode": 404}, {"Type": "System.Collections.Generic.List`1[[BlueTape.Company.API.Models.Errors.ErrorResponse, BlueTape.Company.API, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 500}, {"Type": "System.Void", "MediaTypes": [], "StatusCode": 200}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 412}]}, {"ContainingType": "Program+<>c__DisplayClass0_0", "Method": "<<Main>$>b__12", "RelativePath": "env", "HttpMethod": "GET", "IsController": false, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "Microsoft.AspNetCore.Hosting.IWebHostEnvironment", "MediaTypes": ["application/json"], "StatusCode": 200}]}, {"ContainingType": "BlueTape.Company.API.Controllers.GuestSuppliersController", "Method": "GetGuestSuppliers", "RelativePath": "guestSup<PERSON><PERSON>", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "Search", "Type": "System.String", "IsRequired": false}, {"Name": "SortBy", "Type": "System.String", "IsRequired": false}, {"Name": "SortOrder", "Type": "System.String", "IsRequired": false}, {"Name": "PageNumber", "Type": "System.Int32", "IsRequired": false}, {"Name": "PageSize", "Type": "System.Int32", "IsRequired": false}], "ReturnTypes": [{"Type": "System.Collections.Generic.List`1[[BlueTape.Company.API.Models.Errors.ErrorResponse, BlueTape.Company.API, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 400}, {"Type": "System.Collections.Generic.List`1[[BlueTape.Company.API.Models.Errors.ErrorResponse, BlueTape.Company.API, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 401}, {"Type": "System.Void", "MediaTypes": [], "StatusCode": 404}, {"Type": "System.Collections.Generic.List`1[[BlueTape.Company.API.Models.Errors.ErrorResponse, BlueTape.Company.API, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 500}, {"Type": "BlueTape.Company.API.Models.ResultWithPaginationModel`1[[BlueTape.CompanyService.GuestSuppliers.GuestSupplierModel, BlueTape.CompanyService, Version=1.3.3.0, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "BlueTape.Company.API.Controllers.GuestSuppliersController", "Method": "GetGuestSuppliersCustomers", "RelativePath": "guestSuppliers/{id}/customers", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.String", "IsRequired": true}, {"Name": "Account<PERSON><PERSON><PERSON>", "Type": "System.String", "IsRequired": false}, {"Name": "Search", "Type": "System.String", "IsRequired": false}, {"Name": "SortBy", "Type": "System.String", "IsRequired": false}, {"Name": "SortOrder", "Type": "System.String", "IsRequired": false}, {"Name": "PageNumber", "Type": "System.Int32", "IsRequired": false}, {"Name": "PageSize", "Type": "System.Int32", "IsRequired": false}], "ReturnTypes": [{"Type": "System.Collections.Generic.List`1[[BlueTape.Company.API.Models.Errors.ErrorResponse, BlueTape.Company.API, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 400}, {"Type": "System.Collections.Generic.List`1[[BlueTape.Company.API.Models.Errors.ErrorResponse, BlueTape.Company.API, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 401}, {"Type": "System.Void", "MediaTypes": [], "StatusCode": 404}, {"Type": "System.Collections.Generic.List`1[[BlueTape.Company.API.Models.Errors.ErrorResponse, BlueTape.Company.API, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 500}, {"Type": "BlueTape.Company.API.Models.ResultWithPaginationModel`1[[BlueTape.CompanyService.GuestSuppliers.GuestSupplierCustomerModel, BlueTape.CompanyService, Version=1.3.3.0, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "BlueTape.Company.API.Controllers.GuestSuppliersController", "Method": "GetGuestSuppliersInvoices", "RelativePath": "guestSuppliers/{id}/invoices", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.String", "IsRequired": true}, {"Name": "DueDateFrom", "Type": "System.Nullable`1[[System.DateTime, System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "DueDateTo", "Type": "System.Nullable`1[[System.DateTime, System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "Search", "Type": "System.String", "IsRequired": false}, {"Name": "SortBy", "Type": "System.String", "IsRequired": false}, {"Name": "SortOrder", "Type": "System.String", "IsRequired": false}, {"Name": "PageNumber", "Type": "System.Int32", "IsRequired": false}, {"Name": "PageSize", "Type": "System.Int32", "IsRequired": false}], "ReturnTypes": [{"Type": "System.Collections.Generic.List`1[[BlueTape.Company.API.Models.Errors.ErrorResponse, BlueTape.Company.API, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 400}, {"Type": "System.Collections.Generic.List`1[[BlueTape.Company.API.Models.Errors.ErrorResponse, BlueTape.Company.API, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 401}, {"Type": "System.Void", "MediaTypes": [], "StatusCode": 404}, {"Type": "System.Collections.Generic.List`1[[BlueTape.Company.API.Models.Errors.ErrorResponse, BlueTape.Company.API, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 500}, {"Type": "BlueTape.Company.API.Models.ResultWithPaginationModel`1[[BlueTape.CompanyService.GuestSuppliers.GuestSupplierInvoiceModel, BlueTape.CompanyService, Version=1.3.3.0, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "BlueTape.Company.API.Controllers.GuestSuppliersController", "Method": "GetGuestSupplierNotes", "RelativePath": "guestSuppliers/{id}/notes", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.String", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Collections.Generic.List`1[[BlueTape.Company.API.Models.Errors.ErrorResponse, BlueTape.Company.API, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 400}, {"Type": "System.Collections.Generic.List`1[[BlueTape.Company.API.Models.Errors.ErrorResponse, BlueTape.Company.API, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 401}, {"Type": "System.Void", "MediaTypes": [], "StatusCode": 404}, {"Type": "System.Collections.Generic.List`1[[BlueTape.Company.API.Models.Errors.ErrorResponse, BlueTape.Company.API, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 500}, {"Type": "System.Collections.Generic.List`1[[BlueTape.CompanyService.Companies.CompanyNoteModel, BlueTape.CompanyService, Version=1.3.3.0, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "BlueTape.Company.API.Controllers.Document.HelperController", "Method": "GetAllLoanApplicationsForSupplierId", "RelativePath": "helper/loan-application/{id}/all-approved-by-supplier-id", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.String", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Collections.Generic.List`1[[BlueTape.Company.API.Models.Errors.ErrorResponse, BlueTape.Company.API, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 400}, {"Type": "System.Collections.Generic.List`1[[BlueTape.Company.API.Models.Errors.ErrorResponse, BlueTape.Company.API, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 401}, {"Type": "System.Void", "MediaTypes": [], "StatusCode": 404}, {"Type": "System.Collections.Generic.List`1[[BlueTape.Company.API.Models.Errors.ErrorResponse, BlueTape.Company.API, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 500}, {"Type": "System.String[]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "BlueTape.Company.API.Controllers.Document.HelperController", "Method": "LoCReport", "RelativePath": "helper/loc/report", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "batchSize", "Type": "System.Int32", "IsRequired": false}, {"Name": "loanApproved", "Type": "System.Nullable`1[[<PERSON><PERSON>Boolean, System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "loanExist", "Type": "System.Nullable`1[[<PERSON><PERSON>Boolean, System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "loCNumberExist", "Type": "System.Nullable`1[[<PERSON><PERSON>Boolean, System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "allCompanyDocuments", "Type": "System.Nullable`1[[<PERSON><PERSON>Boolean, System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}], "ReturnTypes": [{"Type": "System.Collections.Generic.List`1[[BlueTape.Company.API.Models.Errors.ErrorResponse, BlueTape.Company.API, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 400}, {"Type": "System.Collections.Generic.List`1[[BlueTape.Company.API.Models.Errors.ErrorResponse, BlueTape.Company.API, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 401}, {"Type": "System.Void", "MediaTypes": [], "StatusCode": 404}, {"Type": "System.Collections.Generic.List`1[[BlueTape.Company.API.Models.Errors.ErrorResponse, BlueTape.Company.API, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 500}, {"Type": "System.Collections.Generic.Dictionary`2[[System.String, System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.Collections.Generic.List`1[[BlueTape.Company.API.Models.Documents.Responses.DocumentLocReportResponse, BlueTape.Company.API, Version=*******, Culture=neutral, PublicKeyToken=null]], System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "BlueTape.Company.API.Controllers.MigrationController", "Method": "MigrateCompany", "RelativePath": "migration/company/{id}", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.String", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Collections.Generic.List`1[[BlueTape.Company.API.Models.Errors.ErrorResponse, BlueTape.Company.API, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 401}, {"Type": "System.Collections.Generic.List`1[[BlueTape.Company.API.Models.Errors.ErrorResponse, BlueTape.Company.API, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 500}, {"Type": "System.Void", "MediaTypes": [], "StatusCode": 200}]}]