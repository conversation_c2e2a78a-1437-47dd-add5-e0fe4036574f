<mxfile host="app.diagrams.net" modified="2023-04-25T11:35:52.754Z" agent="Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36" etag="r8hPlp27snrNVQlYmbd_" version="21.1.4" type="google">
  <diagram id="w0ujp3LWbF3F44Qjv7nZ" name="Page-1">
    <mxGraphModel grid="1" page="1" gridSize="10" guides="1" tooltips="1" connect="1" arrows="1" fold="1" pageScale="1" pageWidth="827" pageHeight="1169" math="0" shadow="0">
      <root>
        <mxCell id="0" />
        <mxCell id="1" parent="0" />
        <mxCell id="R7oqpf7VCK0qXVtkzQWH-5" value="1:N" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;fontFamily=Verdana;strokeWidth=2;endArrow=ERmany;endFill=0;" edge="1" parent="1" source="R7oqpf7VCK0qXVtkzQWH-1" target="R7oqpf7VCK0qXVtkzQWH-2">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="R7oqpf7VCK0qXVtkzQWH-1" value="LoanTemplates" style="rounded=0;whiteSpace=wrap;html=1;strokeWidth=2;fontStyle=1;fontFamily=Verdana;fillColor=#ffe6cc;strokeColor=#d79b00;" vertex="1" parent="1">
          <mxGeometry x="294" y="60" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="R7oqpf7VCK0qXVtkzQWH-6" value="1:N" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;fontFamily=Verdana;strokeWidth=2;endArrow=ERmany;endFill=0;" edge="1" parent="1" source="R7oqpf7VCK0qXVtkzQWH-2" target="R7oqpf7VCK0qXVtkzQWH-4">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="R7oqpf7VCK0qXVtkzQWH-8" value="1:N" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeWidth=2;fontFamily=Verdana;endArrow=ERmany;endFill=0;" edge="1" parent="1" source="R7oqpf7VCK0qXVtkzQWH-2" target="R7oqpf7VCK0qXVtkzQWH-7">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="R7oqpf7VCK0qXVtkzQWH-10" value="1:N" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeWidth=2;fontFamily=Verdana;endArrow=ERmany;endFill=0;" edge="1" parent="1" source="R7oqpf7VCK0qXVtkzQWH-2" target="R7oqpf7VCK0qXVtkzQWH-9">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="R7oqpf7VCK0qXVtkzQWH-2" value="Loans" style="rounded=0;whiteSpace=wrap;html=1;strokeWidth=2;fontStyle=1;fontFamily=Verdana;fillColor=#dae8fc;strokeColor=#6c8ebf;" vertex="1" parent="1">
          <mxGeometry x="294" y="170" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="R7oqpf7VCK0qXVtkzQWH-4" value="LoanParameters" style="rounded=0;whiteSpace=wrap;html=1;strokeWidth=2;fontStyle=1;fontFamily=Verdana;fillColor=#ffe6cc;strokeColor=#d79b00;" vertex="1" parent="1">
          <mxGeometry x="110" y="170" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="R7oqpf7VCK0qXVtkzQWH-7" value="Payments" style="rounded=0;whiteSpace=wrap;html=1;strokeWidth=2;fontStyle=1;fontFamily=Verdana;fillColor=#e1d5e7;strokeColor=#9673a6;" vertex="1" parent="1">
          <mxGeometry x="480" y="170" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="R7oqpf7VCK0qXVtkzQWH-9" value="LoanReceivables" style="rounded=0;whiteSpace=wrap;html=1;strokeWidth=2;fontStyle=1;fontFamily=Verdana;fillColor=#d5e8d4;strokeColor=#82b366;" vertex="1" parent="1">
          <mxGeometry x="294" y="290" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="R7oqpf7VCK0qXVtkzQWH-11" value="LatePaymentFee&lt;br&gt;Details" style="rounded=0;whiteSpace=wrap;html=1;strokeWidth=2;fontStyle=1;fontFamily=Verdana;fillColor=#d5e8d4;strokeColor=#82b366;gradientColor=none;" vertex="1" parent="1">
          <mxGeometry x="110" y="290" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="R7oqpf7VCK0qXVtkzQWH-12" value="Loan Entities" style="text;html=1;strokeColor=none;fillColor=none;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;dashed=1;strokeWidth=2;fontFamily=Verdana;fontSize=20;" vertex="1" parent="1">
          <mxGeometry x="95" y="50" width="150" height="30" as="geometry" />
        </mxCell>
        <mxCell id="r6d7Iq06VaXus2Shrn9N-2" value="1:1" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;fontFamily=Verdana;strokeWidth=2;endArrow=none;endFill=0;entryX=1;entryY=0.5;entryDx=0;entryDy=0;exitX=0;exitY=0.5;exitDx=0;exitDy=0;" edge="1" parent="1" source="R7oqpf7VCK0qXVtkzQWH-9" target="R7oqpf7VCK0qXVtkzQWH-11">
          <mxGeometry relative="1" as="geometry">
            <mxPoint x="290" y="320" as="sourcePoint" />
            <mxPoint x="240" y="210" as="targetPoint" />
            <mxPoint as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="r6d7Iq06VaXus2Shrn9N-3" value="LoanReceivables&lt;br&gt;Payments" style="rounded=0;whiteSpace=wrap;html=1;strokeWidth=2;fontStyle=1;fontFamily=Verdana;fillColor=#99FF99;strokeColor=#82b366;gradientColor=#CC99FF;gradientDirection=south;" vertex="1" parent="1">
          <mxGeometry x="480" y="290" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="r6d7Iq06VaXus2Shrn9N-4" value="1:N" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeWidth=2;fontFamily=Verdana;endArrow=ERmany;endFill=0;entryX=0.5;entryY=0;entryDx=0;entryDy=0;exitX=0.5;exitY=1;exitDx=0;exitDy=0;" edge="1" parent="1" source="R7oqpf7VCK0qXVtkzQWH-7" target="r6d7Iq06VaXus2Shrn9N-3">
          <mxGeometry relative="1" as="geometry">
            <mxPoint x="540" y="230" as="sourcePoint" />
            <mxPoint x="490" y="210" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="r6d7Iq06VaXus2Shrn9N-5" value="1:N" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeWidth=2;fontFamily=Verdana;endArrow=ERmany;endFill=0;" edge="1" parent="1" source="R7oqpf7VCK0qXVtkzQWH-9" target="r6d7Iq06VaXus2Shrn9N-3">
          <mxGeometry relative="1" as="geometry">
            <mxPoint x="414" y="319.75" as="sourcePoint" />
            <mxPoint x="480" y="319.75" as="targetPoint" />
          </mxGeometry>
        </mxCell>
      </root>
    </mxGraphModel>
  </diagram>
</mxfile>
