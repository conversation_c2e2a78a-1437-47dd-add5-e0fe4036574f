﻿namespace BlueTape.Aion.Application.Models.InternalTransfer;

public class CreateInternal
{
    public BaseInternalAccountDetails Originator { get; set; } = null!;
    public BaseInternalAccountDetails Receiver { get; set; } = null!;
    public decimal Amount { get; set; }
    public string Description { get; set; } = null!;
    public string TransactionNumber { get; set; } = null!;
    public string TransactionId { get; set; } = null!;
}