import type { TFunction } from 'i18next'
import { isNumber } from 'lodash'

import { PaymentFrequency } from '@/lib/redux/api/draw-application/types/draw-application-detailed.type'
import type { IDrawApplicationDetailedPaymentPlan } from '@/lib/redux/api/draw-application/types/draw-application-detailed.type'

export const getPaymentFrequencyTranslation = (
  t: TFunction,
  frequency: PaymentFrequency,
): string => {
  switch (frequency) {
    case PaymentFrequency.WEEKLY:
      return t('paymentPlan.weekly')
    case PaymentFrequency.MONTHLY:
      return t('paymentPlan.monthly')
    default:
      return t('paymentPlan.single')
  }
}

export const formatInitialPurchaserPackage = (
  downPaymentRequired: boolean,
  t: TFunction,
  paymentPlan?: IDrawApplicationDetailedPaymentPlan | null,
): string => {
  if (!paymentPlan?.name) {
    return t('na')
  }

  const { name, frequency, firstPaymentDelayDays } = paymentPlan

  if (!frequency) {
    return name
  }

  const frequencyTranslated = getPaymentFrequencyTranslation(t, frequency)

  let result = downPaymentRequired
    ? `${name}: ${t('drawApplication.page.detailed.tabs.tradeCredit.downPaymentPlan')} + ${frequencyTranslated}`
    : `${name}: ${frequencyTranslated}`

  if (isNumber(firstPaymentDelayDays) && firstPaymentDelayDays > 0) {
    result += ` ${t('drawApplication.page.detailed.tabs.tradeCredit.installmentsStartingDays', { days: firstPaymentDelayDays })}`
  }

  return result
}
