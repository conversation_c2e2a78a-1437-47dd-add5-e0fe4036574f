import {Browser, expect} from "@playwright/test";
import {Action} from "../database/autoTests/entities/AutoTestReport";
import {BaseTest} from "../tests/test-utils";
import {CreateInvoiceSimplifyRequest} from "../api/common/genericAPI/requests/createSimplifyInvoiceRequest";
import {createSimplifyInvoice, getCheckOutUrl} from "../api/common/send-generic-api-request";
import {PageManager} from "../objects/pages/page-manager";


export class CustomerService
{
    async payInvoiceViaAch(
        url: string,
        browser: Browser,
        invoiceNumber: string,
        login: string,
        password: string,
        actionsPerTest: Action[]) {
        let customerPage = await browser.newPage();
        await customerPage.goto(`${url}`)

        let pageManager = new PageManager(customerPage);
        await pageManager.loginPage.login(login, password)
        await pageManager.sideMenu.openPaySubTab(pageManager.sideMenu.sideMenuSubTabs.pay.payables);

        await pageManager.invoicesList.clickOnInvoiceRow(invoiceNumber);
        await pageManager.invoicesDetails.payForLatestInvoice();

        await customerPage.close();
    }

    async payInvoiceViaCard(
        url: string,
        browser: Browser,
        invoiceNumber: string,
        login: string,
        password: string,
        actionsPerTest: Action[]) {
        let customerPage = await browser.newPage();
        await customerPage.goto(`${url}`)

        let pageManager = new PageManager(customerPage);
        await pageManager.loginPage.login(login, password)
        await pageManager.sideMenu.openPaySubTab(pageManager.sideMenu.sideMenuSubTabs.pay.payables);

        await pageManager.invoicesList.clickOnInvoiceRow(invoiceNumber);
        await pageManager.invoicesDetails.payForLatestInvoiceWithCard();

        await customerPage.close();
    }

    async payInvoiceViaDraw(
        url: string,
        browser: Browser,
        invoiceNumber: string,
        login: string,
        password: string,
        actionsPerTest: Action[]) 
        {
        const customerContext = await browser.newContext();
        let customerPage = await customerContext.newPage();
        await customerPage.goto(`${url}`)

        let pageManager = new PageManager(customerPage);
        await pageManager.loginPage.login(login, password)
        await pageManager.sideMenu.openPaySubTab(pageManager.sideMenu.sideMenuSubTabs.pay.payables);

        await pageManager.invoicesList.clickOnInvoiceRow(invoiceNumber);
        await pageManager.invoicesDetails.payForLatestInvoiceWithBTC();
        await BaseTest.delayOperation(6000)
        await customerPage.close();
    }

    async payDrawViaAch(
        url: string,
        browser: Browser,
        actionsPerTest: Action[],
        invoiceNumber: string,
        login: string,
        password: string) 
        {
        const customerContext = await browser.newContext();
        let customerPage = await customerContext.newPage();
        await customerPage.goto(`${url}`)

        let pageManager = new PageManager(customerPage);
        //await pageManager.loginPage.login("<EMAIL>", "Qazwsx123!")
        await pageManager.loginPage.login(login, password)
        await pageManager.sideMenu.openTradeCreditSubTab(pageManager.sideMenu.sideMenuSubTabs.tradeCredit.draws);

        await pageManager.drawList.clickOnDrawRow(invoiceNumber);
        await pageManager.payDrawModal.payRemainingBalance();
        await BaseTest.delayOperation(6000)
        await customerPage.close();
    }

    async createDirectTermsInvoice(
        url: string,
        browser: Browser,
        login: string,
        password: string,
        actionsPerTest: Action[],
        invoiceAmount?: string) : Promise<string> 
        {
        const invoiceId = BaseTest.getGUID();
        const invoiceNumber = `AUTO-${invoiceId}`
        const totalAmount = invoiceAmount ? parseInt(invoiceAmount) * 100 : BaseTest.getRandomNumber(10000, 50000);
        
        actionsPerTest.push({
            description: `Creating direct terms invoice with ${invoiceAmount ? 'specified amount: ' + totalAmount : 'random amount: ' + totalAmount}`
        });

        const customerContext = await browser.newContext();
        let customerPage = await customerContext.newPage();
        await customerPage.goto(`${url}`)

        let pageManager = new PageManager(customerPage);
        await pageManager.loginPage.login(login, password)
        
// Before clicking the button, we need to set up a handler for the file chooser
        // This approach bypasses the system dialog completely
        const filePath = 'test-data/files/testFile.png';
        
        // Click on the Upload Invoice button
        await customerPage.locator('[data-testid="uploadInvoiceSupplierContractor"]').click();
        
        // Look for the file input element (it's usually either visible or hidden in the DOM)
        // Using a more general selector to find the file input
        await customerPage.setInputFiles('input[type="file"]', filePath);
        
        // Wait for the upload to complete
        await BaseTest.delayOperation(2000);
        
        // Fill the invoice number input field with provided invoice number
        await customerPage.locator('[data-testid="invoice_upload_invoice_number_input"]').fill(invoiceNumber);
    
        // Fill the amount input field with random amount
        await customerPage.locator('[data-testid="invoice_upload_amount_input"]').fill(totalAmount.toString());
        
        await customerPage.locator('[data-testid="invoice_upload_next_btn"]').click();
        // Fill additional required fields
        // Business name
        await customerPage.locator('[data-testid="Business name_input"]').fill('Test Business LLC');
        
        // Contact name
        await customerPage.locator('[data-testid="invoice_upload_contact_name_input"]').fill('John Smith');
        
        // Email with valid format
        await customerPage.locator('[data-testid="invoice_upload_email_input"]').fill('<EMAIL>');
        
        // Phone with USA format (xxx) xxx-xxxx
        await customerPage.locator('[data-testid="invoice_upload_phone_input"]').fill('************');
        
        // Click the next button to proceed
        await customerPage.locator('[data-testid="invoice_upload_add_invoice_btn"]').click();
        
        // Wait for navigation/processing
        await BaseTest.delayOperation(2000);

        await pageManager.invoicesDetails.payForLatestInvoiceWithBTC();
        
        await BaseTest.delayOperation(6000)
        await customerPage.close();

        return invoiceNumber;
    }
}
