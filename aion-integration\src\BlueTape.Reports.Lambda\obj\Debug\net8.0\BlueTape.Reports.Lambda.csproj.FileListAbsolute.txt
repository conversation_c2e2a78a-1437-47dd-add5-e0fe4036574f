C:\Users\<USER>\source\repos\BlueTape\aion-integration\src\BlueTape.Reports.Lambda\bin\Debug\net8.0\appsettings.beta.json
C:\Users\<USER>\source\repos\BlueTape\aion-integration\src\BlueTape.Reports.Lambda\bin\Debug\net8.0\appsettings.dev.json
C:\Users\<USER>\source\repos\BlueTape\aion-integration\src\BlueTape.Reports.Lambda\bin\Debug\net8.0\appsettings.Development.json
C:\Users\<USER>\source\repos\BlueTape\aion-integration\src\BlueTape.Reports.Lambda\bin\Debug\net8.0\appsettings.json
C:\Users\<USER>\source\repos\BlueTape\aion-integration\src\BlueTape.Reports.Lambda\bin\Debug\net8.0\appsettings.prod.json
C:\Users\<USER>\source\repos\BlueTape\aion-integration\src\BlueTape.Reports.Lambda\bin\Debug\net8.0\appsettings.qa.json
C:\Users\<USER>\source\repos\BlueTape\aion-integration\src\BlueTape.Reports.Lambda\bin\Debug\net8.0\BlueTape.Reports.Lambda.deps.json
C:\Users\<USER>\source\repos\BlueTape\aion-integration\src\BlueTape.Reports.Lambda\bin\Debug\net8.0\BlueTape.Reports.Lambda.runtimeconfig.json
C:\Users\<USER>\source\repos\BlueTape\aion-integration\src\BlueTape.Reports.Lambda\bin\Debug\net8.0\BlueTape.Reports.Lambda.dll
C:\Users\<USER>\source\repos\BlueTape\aion-integration\src\BlueTape.Reports.Lambda\bin\Debug\net8.0\BlueTape.Reports.Lambda.pdb
C:\Users\<USER>\source\repos\BlueTape\aion-integration\src\BlueTape.Reports.Lambda\bin\Debug\net8.0\Amazon.Lambda.Core.dll
C:\Users\<USER>\source\repos\BlueTape\aion-integration\src\BlueTape.Reports.Lambda\bin\Debug\net8.0\Amazon.Lambda.Serialization.SystemTextJson.dll
C:\Users\<USER>\source\repos\BlueTape\aion-integration\src\BlueTape.Reports.Lambda\bin\Debug\net8.0\Amazon.Lambda.SQSEvents.dll
C:\Users\<USER>\source\repos\BlueTape\aion-integration\src\BlueTape.Reports.Lambda\bin\Debug\net8.0\Autofac.dll
C:\Users\<USER>\source\repos\BlueTape\aion-integration\src\BlueTape.Reports.Lambda\bin\Debug\net8.0\Autofac.Configuration.dll
C:\Users\<USER>\source\repos\BlueTape\aion-integration\src\BlueTape.Reports.Lambda\bin\Debug\net8.0\Autofac.Extensions.DependencyInjection.dll
C:\Users\<USER>\source\repos\BlueTape\aion-integration\src\BlueTape.Reports.Lambda\bin\Debug\net8.0\AutoMapper.dll
C:\Users\<USER>\source\repos\BlueTape\aion-integration\src\BlueTape.Reports.Lambda\bin\Debug\net8.0\AutoMapper.Extensions.Microsoft.DependencyInjection.dll
C:\Users\<USER>\source\repos\BlueTape\aion-integration\src\BlueTape.Reports.Lambda\bin\Debug\net8.0\AWSSDK.Core.dll
C:\Users\<USER>\source\repos\BlueTape\aion-integration\src\BlueTape.Reports.Lambda\bin\Debug\net8.0\AWSSDK.Extensions.NETCore.Setup.dll
C:\Users\<USER>\source\repos\BlueTape\aion-integration\src\BlueTape.Reports.Lambda\bin\Debug\net8.0\AWSSDK.KeyManagementService.dll
C:\Users\<USER>\source\repos\BlueTape\aion-integration\src\BlueTape.Reports.Lambda\bin\Debug\net8.0\AWSSDK.S3.dll
C:\Users\<USER>\source\repos\BlueTape\aion-integration\src\BlueTape.Reports.Lambda\bin\Debug\net8.0\AWSSDK.SecretsManager.dll
C:\Users\<USER>\source\repos\BlueTape\aion-integration\src\BlueTape.Reports.Lambda\bin\Debug\net8.0\Amazon.SecretsManager.Extensions.Caching.dll
C:\Users\<USER>\source\repos\BlueTape\aion-integration\src\BlueTape.Reports.Lambda\bin\Debug\net8.0\AWSSDK.SecurityToken.dll
C:\Users\<USER>\source\repos\BlueTape\aion-integration\src\BlueTape.Reports.Lambda\bin\Debug\net8.0\AWSSDK.SimpleNotificationService.dll
C:\Users\<USER>\source\repos\BlueTape\aion-integration\src\BlueTape.Reports.Lambda\bin\Debug\net8.0\AWSSDK.SQS.dll
C:\Users\<USER>\source\repos\BlueTape\aion-integration\src\BlueTape.Reports.Lambda\bin\Debug\net8.0\Azure.Core.dll
C:\Users\<USER>\source\repos\BlueTape\aion-integration\src\BlueTape.Reports.Lambda\bin\Debug\net8.0\Azure.Core.Amqp.dll
C:\Users\<USER>\source\repos\BlueTape\aion-integration\src\BlueTape.Reports.Lambda\bin\Debug\net8.0\Azure.Data.Tables.dll
C:\Users\<USER>\source\repos\BlueTape\aion-integration\src\BlueTape.Reports.Lambda\bin\Debug\net8.0\Azure.Identity.dll
C:\Users\<USER>\source\repos\BlueTape\aion-integration\src\BlueTape.Reports.Lambda\bin\Debug\net8.0\Azure.Messaging.ServiceBus.dll
C:\Users\<USER>\source\repos\BlueTape\aion-integration\src\BlueTape.Reports.Lambda\bin\Debug\net8.0\Azure.Security.KeyVault.Keys.dll
C:\Users\<USER>\source\repos\BlueTape\aion-integration\src\BlueTape.Reports.Lambda\bin\Debug\net8.0\Azure.Security.KeyVault.Secrets.dll
C:\Users\<USER>\source\repos\BlueTape\aion-integration\src\BlueTape.Reports.Lambda\bin\Debug\net8.0\BlueTape.AWSMessaging.dll
C:\Users\<USER>\source\repos\BlueTape\aion-integration\src\BlueTape.Reports.Lambda\bin\Debug\net8.0\BlueTape.AWSS3.dll
C:\Users\<USER>\source\repos\BlueTape\aion-integration\src\BlueTape.Reports.Lambda\bin\Debug\net8.0\BlueTape.AzureKeyVault.dll
C:\Users\<USER>\source\repos\BlueTape\aion-integration\src\BlueTape.Reports.Lambda\bin\Debug\net8.0\BlueTape.Common.ExceptionHandling.dll
C:\Users\<USER>\source\repos\BlueTape\aion-integration\src\BlueTape.Reports.Lambda\bin\Debug\net8.0\BlueTape.Common.Extensions.dll
C:\Users\<USER>\source\repos\BlueTape\aion-integration\src\BlueTape.Reports.Lambda\bin\Debug\net8.0\BlueTape.CompanyClient.dll
C:\Users\<USER>\source\repos\BlueTape\aion-integration\src\BlueTape.Reports.Lambda\bin\Debug\net8.0\BlueTape.CompanyService.dll
C:\Users\<USER>\source\repos\BlueTape\aion-integration\src\BlueTape.Reports.Lambda\bin\Debug\net8.0\BlueTape.CompanyService.Common.dll
C:\Users\<USER>\source\repos\BlueTape\aion-integration\src\BlueTape.Reports.Lambda\bin\Debug\net8.0\BlueTape.Integrations.Aion.dll
C:\Users\<USER>\source\repos\BlueTape\aion-integration\src\BlueTape.Reports.Lambda\bin\Debug\net8.0\BlueTape.Integrations.Aion.AzureTableStorage.dll
C:\Users\<USER>\source\repos\BlueTape\aion-integration\src\BlueTape.Reports.Lambda\bin\Debug\net8.0\BlueTape.Integrations.Aion.Infrastructure.dll
C:\Users\<USER>\source\repos\BlueTape\aion-integration\src\BlueTape.Reports.Lambda\bin\Debug\net8.0\BlueTape.LambdaBase.dll
C:\Users\<USER>\source\repos\BlueTape\aion-integration\src\BlueTape.Reports.Lambda\bin\Debug\net8.0\BlueTape.LS.dll
C:\Users\<USER>\source\repos\BlueTape\aion-integration\src\BlueTape.Reports.Lambda\bin\Debug\net8.0\BlueTape.LS.Domain.dll
C:\Users\<USER>\source\repos\BlueTape\aion-integration\src\BlueTape.Reports.Lambda\bin\Debug\net8.0\BlueTape.MongoDB.dll
C:\Users\<USER>\source\repos\BlueTape\aion-integration\src\BlueTape.Reports.Lambda\bin\Debug\net8.0\BlueTape.OBS.dll
C:\Users\<USER>\source\repos\BlueTape\aion-integration\src\BlueTape.Reports.Lambda\bin\Debug\net8.0\BlueTape.ServiceBusMessaging.dll
C:\Users\<USER>\source\repos\BlueTape\aion-integration\src\BlueTape.Reports.Lambda\bin\Debug\net8.0\BlueTape.SNS.dll
C:\Users\<USER>\source\repos\BlueTape\aion-integration\src\BlueTape.Reports.Lambda\bin\Debug\net8.0\BlueTape.Utilities.dll
C:\Users\<USER>\source\repos\BlueTape\aion-integration\src\BlueTape.Reports.Lambda\bin\Debug\net8.0\DnsClient.dll
C:\Users\<USER>\source\repos\BlueTape\aion-integration\src\BlueTape.Reports.Lambda\bin\Debug\net8.0\Elastic.CommonSchema.dll
C:\Users\<USER>\source\repos\BlueTape\aion-integration\src\BlueTape.Reports.Lambda\bin\Debug\net8.0\Elastic.CommonSchema.Serilog.dll
C:\Users\<USER>\source\repos\BlueTape\aion-integration\src\BlueTape.Reports.Lambda\bin\Debug\net8.0\Microsoft.Azure.Amqp.dll
C:\Users\<USER>\source\repos\BlueTape\aion-integration\src\BlueTape.Reports.Lambda\bin\Debug\net8.0\Microsoft.Bcl.AsyncInterfaces.dll
C:\Users\<USER>\source\repos\BlueTape\aion-integration\src\BlueTape.Reports.Lambda\bin\Debug\net8.0\Microsoft.Extensions.DependencyModel.dll
C:\Users\<USER>\source\repos\BlueTape\aion-integration\src\BlueTape.Reports.Lambda\bin\Debug\net8.0\Microsoft.Extensions.Http.Polly.dll
C:\Users\<USER>\source\repos\BlueTape\aion-integration\src\BlueTape.Reports.Lambda\bin\Debug\net8.0\Microsoft.Extensions.Options.dll
C:\Users\<USER>\source\repos\BlueTape\aion-integration\src\BlueTape.Reports.Lambda\bin\Debug\net8.0\Microsoft.Identity.Client.dll
C:\Users\<USER>\source\repos\BlueTape\aion-integration\src\BlueTape.Reports.Lambda\bin\Debug\net8.0\Microsoft.Identity.Client.Extensions.Msal.dll
C:\Users\<USER>\source\repos\BlueTape\aion-integration\src\BlueTape.Reports.Lambda\bin\Debug\net8.0\Microsoft.IdentityModel.Abstractions.dll
C:\Users\<USER>\source\repos\BlueTape\aion-integration\src\BlueTape.Reports.Lambda\bin\Debug\net8.0\Microsoft.IdentityModel.JsonWebTokens.dll
C:\Users\<USER>\source\repos\BlueTape\aion-integration\src\BlueTape.Reports.Lambda\bin\Debug\net8.0\Microsoft.IdentityModel.Logging.dll
C:\Users\<USER>\source\repos\BlueTape\aion-integration\src\BlueTape.Reports.Lambda\bin\Debug\net8.0\Microsoft.IdentityModel.Tokens.dll
C:\Users\<USER>\source\repos\BlueTape\aion-integration\src\BlueTape.Reports.Lambda\bin\Debug\net8.0\MongoDB.Bson.dll
C:\Users\<USER>\source\repos\BlueTape\aion-integration\src\BlueTape.Reports.Lambda\bin\Debug\net8.0\MongoDB.Driver.dll
C:\Users\<USER>\source\repos\BlueTape\aion-integration\src\BlueTape.Reports.Lambda\bin\Debug\net8.0\MongoDB.Driver.Core.dll
C:\Users\<USER>\source\repos\BlueTape\aion-integration\src\BlueTape.Reports.Lambda\bin\Debug\net8.0\MongoDB.Libmongocrypt.dll
C:\Users\<USER>\source\repos\BlueTape\aion-integration\src\BlueTape.Reports.Lambda\bin\Debug\net8.0\Newtonsoft.Json.dll
C:\Users\<USER>\source\repos\BlueTape\aion-integration\src\BlueTape.Reports.Lambda\bin\Debug\net8.0\Polly.dll
C:\Users\<USER>\source\repos\BlueTape\aion-integration\src\BlueTape.Reports.Lambda\bin\Debug\net8.0\Polly.Extensions.Http.dll
C:\Users\<USER>\source\repos\BlueTape\aion-integration\src\BlueTape.Reports.Lambda\bin\Debug\net8.0\BouncyCastle.Crypto.dll
C:\Users\<USER>\source\repos\BlueTape\aion-integration\src\BlueTape.Reports.Lambda\bin\Debug\net8.0\Serilog.dll
C:\Users\<USER>\source\repos\BlueTape\aion-integration\src\BlueTape.Reports.Lambda\bin\Debug\net8.0\Serilog.AspNetCore.dll
C:\Users\<USER>\source\repos\BlueTape\aion-integration\src\BlueTape.Reports.Lambda\bin\Debug\net8.0\Serilog.Enrichers.GlobalLogContext.dll
C:\Users\<USER>\source\repos\BlueTape\aion-integration\src\BlueTape.Reports.Lambda\bin\Debug\net8.0\Serilog.Extensions.Hosting.dll
C:\Users\<USER>\source\repos\BlueTape\aion-integration\src\BlueTape.Reports.Lambda\bin\Debug\net8.0\Serilog.Extensions.Logging.dll
C:\Users\<USER>\source\repos\BlueTape\aion-integration\src\BlueTape.Reports.Lambda\bin\Debug\net8.0\Serilog.Formatting.Compact.dll
C:\Users\<USER>\source\repos\BlueTape\aion-integration\src\BlueTape.Reports.Lambda\bin\Debug\net8.0\Serilog.Settings.Configuration.dll
C:\Users\<USER>\source\repos\BlueTape\aion-integration\src\BlueTape.Reports.Lambda\bin\Debug\net8.0\Serilog.Sinks.Console.dll
C:\Users\<USER>\source\repos\BlueTape\aion-integration\src\BlueTape.Reports.Lambda\bin\Debug\net8.0\Serilog.Sinks.Debug.dll
C:\Users\<USER>\source\repos\BlueTape\aion-integration\src\BlueTape.Reports.Lambda\bin\Debug\net8.0\Serilog.Sinks.File.dll
C:\Users\<USER>\source\repos\BlueTape\aion-integration\src\BlueTape.Reports.Lambda\bin\Debug\net8.0\Serilog.Sinks.Http.dll
C:\Users\<USER>\source\repos\BlueTape\aion-integration\src\BlueTape.Reports.Lambda\bin\Debug\net8.0\Serilog.Sinks.Logz.Io.dll
C:\Users\<USER>\source\repos\BlueTape\aion-integration\src\BlueTape.Reports.Lambda\bin\Debug\net8.0\Serilog.Sinks.PeriodicBatching.dll
C:\Users\<USER>\source\repos\BlueTape\aion-integration\src\BlueTape.Reports.Lambda\bin\Debug\net8.0\SharpCompress.dll
C:\Users\<USER>\source\repos\BlueTape\aion-integration\src\BlueTape.Reports.Lambda\bin\Debug\net8.0\Snappier.dll
C:\Users\<USER>\source\repos\BlueTape\aion-integration\src\BlueTape.Reports.Lambda\bin\Debug\net8.0\System.ClientModel.dll
C:\Users\<USER>\source\repos\BlueTape\aion-integration\src\BlueTape.Reports.Lambda\bin\Debug\net8.0\System.IdentityModel.Tokens.Jwt.dll
C:\Users\<USER>\source\repos\BlueTape\aion-integration\src\BlueTape.Reports.Lambda\bin\Debug\net8.0\System.Memory.Data.dll
C:\Users\<USER>\source\repos\BlueTape\aion-integration\src\BlueTape.Reports.Lambda\bin\Debug\net8.0\System.Security.Cryptography.ProtectedData.dll
C:\Users\<USER>\source\repos\BlueTape\aion-integration\src\BlueTape.Reports.Lambda\bin\Debug\net8.0\ZstdSharp.dll
C:\Users\<USER>\source\repos\BlueTape\aion-integration\src\BlueTape.Reports.Lambda\bin\Debug\net8.0\runtimes\linux\native\libmongocrypt.so
C:\Users\<USER>\source\repos\BlueTape\aion-integration\src\BlueTape.Reports.Lambda\bin\Debug\net8.0\runtimes\osx\native\libmongocrypt.dylib
C:\Users\<USER>\source\repos\BlueTape\aion-integration\src\BlueTape.Reports.Lambda\bin\Debug\net8.0\runtimes\win\native\mongocrypt.dll
C:\Users\<USER>\source\repos\BlueTape\aion-integration\src\BlueTape.Reports.Lambda\bin\Debug\net8.0\runtimes\win\lib\netstandard2.0\System.Security.Cryptography.ProtectedData.dll
C:\Users\<USER>\source\repos\BlueTape\aion-integration\src\BlueTape.Reports.Lambda\bin\Debug\net8.0\BlueTape.Aion.Application.dll
C:\Users\<USER>\source\repos\BlueTape\aion-integration\src\BlueTape.Reports.Lambda\bin\Debug\net8.0\BlueTape.Aion.DataAccess.External.dll
C:\Users\<USER>\source\repos\BlueTape\aion-integration\src\BlueTape.Reports.Lambda\bin\Debug\net8.0\BlueTape.Aion.DataAccess.MongoDB.dll
C:\Users\<USER>\source\repos\BlueTape\aion-integration\src\BlueTape.Reports.Lambda\bin\Debug\net8.0\BlueTape.Aion.Domain.dll
C:\Users\<USER>\source\repos\BlueTape\aion-integration\src\BlueTape.Reports.Lambda\bin\Debug\net8.0\BueTape.Aion.Infrastructure.dll
C:\Users\<USER>\source\repos\BlueTape\aion-integration\src\BlueTape.Reports.Lambda\bin\Debug\net8.0\BlueTape.Aion.Application.pdb
C:\Users\<USER>\source\repos\BlueTape\aion-integration\src\BlueTape.Reports.Lambda\bin\Debug\net8.0\BlueTape.Aion.DataAccess.External.pdb
C:\Users\<USER>\source\repos\BlueTape\aion-integration\src\BlueTape.Reports.Lambda\bin\Debug\net8.0\BlueTape.Aion.DataAccess.MongoDB.pdb
C:\Users\<USER>\source\repos\BlueTape\aion-integration\src\BlueTape.Reports.Lambda\bin\Debug\net8.0\BlueTape.Aion.Domain.pdb
C:\Users\<USER>\source\repos\BlueTape\aion-integration\src\BlueTape.Reports.Lambda\bin\Debug\net8.0\BueTape.Aion.Infrastructure.pdb
C:\Users\<USER>\source\repos\BlueTape\aion-integration\src\BlueTape.Reports.Lambda\obj\Debug\net8.0\BlueTape.Reports.Lambda.csproj.AssemblyReference.cache
C:\Users\<USER>\source\repos\BlueTape\aion-integration\src\BlueTape.Reports.Lambda\obj\Debug\net8.0\BlueTape.Reports.Lambda.GeneratedMSBuildEditorConfig.editorconfig
C:\Users\<USER>\source\repos\BlueTape\aion-integration\src\BlueTape.Reports.Lambda\obj\Debug\net8.0\BlueTape.Reports.Lambda.AssemblyInfoInputs.cache
C:\Users\<USER>\source\repos\BlueTape\aion-integration\src\BlueTape.Reports.Lambda\obj\Debug\net8.0\BlueTape.Reports.Lambda.AssemblyInfo.cs
C:\Users\<USER>\source\repos\BlueTape\aion-integration\src\BlueTape.Reports.Lambda\obj\Debug\net8.0\BlueTape.Reports.Lambda.csproj.CoreCompileInputs.cache
C:\Users\<USER>\source\repos\BlueTape\aion-integration\src\BlueTape.Reports.Lambda\obj\Debug\net8.0\BlueTape.Reports.Lambda.sourcelink.json
C:\Users\<USER>\source\repos\BlueTape\aion-integration\src\BlueTape.Reports.Lambda\obj\Debug\net8.0\BlueTape.64B49175.Up2Date
C:\Users\<USER>\source\repos\BlueTape\aion-integration\src\BlueTape.Reports.Lambda\obj\Debug\net8.0\BlueTape.Reports.Lambda.dll
C:\Users\<USER>\source\repos\BlueTape\aion-integration\src\BlueTape.Reports.Lambda\obj\Debug\net8.0\refint\BlueTape.Reports.Lambda.dll
C:\Users\<USER>\source\repos\BlueTape\aion-integration\src\BlueTape.Reports.Lambda\obj\Debug\net8.0\BlueTape.Reports.Lambda.pdb
C:\Users\<USER>\source\repos\BlueTape\aion-integration\src\BlueTape.Reports.Lambda\obj\Debug\net8.0\BlueTape.Reports.Lambda.genruntimeconfig.cache
C:\Users\<USER>\source\repos\BlueTape\aion-integration\src\BlueTape.Reports.Lambda\obj\Debug\net8.0\ref\BlueTape.Reports.Lambda.dll
