import axios from "axios";

require('dotenv').config();

//const constants = JSON.parse(JSON.stringify(require('../../constants/companyTestData.json')))

export async function sendCompanyRequest(method: string, endpoint: string, body: any = null) {
    try {
        const url = `${process.env.COMPANY_BASE_URL}/${endpoint}`;
        let response;

        switch (method) {
            case 'post':
                response = await axios.post(url, body, {
                    headers: {
                        accept: 'text/plain',
                        'Content-Type': 'application/json-patch+json',
                        Authorization: 'Bearer Im7x2C7smVdxZgFRFw4AmLhJcjOWKZcfG0KyEFHX=',
                        'type': 'text'
                    }
                });
                break;
            case 'get':
                response = await axios.get(url, {
                    headers: {
                        accept: 'text/plain',
                        Authorization: 'Bearer Im7x2C7smVdxZgFRFw4AmLhJcjOWKZcfG0KyEFHX=',
                        'type': 'text'
                    }
                });
                break;
        }

        return response;
    } catch (error) {
        console.log(error);
        return error;
    }
}
