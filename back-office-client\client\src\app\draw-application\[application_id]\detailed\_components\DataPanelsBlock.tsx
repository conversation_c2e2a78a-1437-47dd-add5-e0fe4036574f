import { Flex } from 'antd'
import { isNumber } from 'lodash'
import { useMemo } from 'react'
import { useTranslation } from 'react-i18next'
import { styled } from 'styled-components'
import { CheckOutlined, WarningOutlined } from '@ant-design/icons'

import {
  getSecurityDepositStatus,
  getSecurityDepositTranslation,
} from '../_utils/draw-application-security-deposit'
import { getDrawApplicationDownPaymentStatusTranslation } from '../_utils/draw-application-down-payment'

import RejectionReasonBlock from '@/app/draw-application/[application_id]/detailed/_components/RejectionReasonBlock'
import type { StatisticsBlockItem } from '@/components/common/StatisticsBlock'
import { DepositStatus, DrawApplicationStatus } from '@/globals/types'
import {
  currencyMask,
  formatDateDay,
  getDrawApplicationTypeTranslation,
} from '@/globals/utils'
import type {
  IDrawApplicationCompanyDetailsResponse,
  IDrawApplicationDetailedResponse,
} from '@/lib/redux/api/draw-application/types'
import StyledText from '@/components/common/typography/StyledText'
import { globalDesignTokens } from '@/globals/styles/global-design-tokens'
import type { IDrawApplicationStates } from '@/app/draw-application/[application_id]/detailed/_utils'
import StatisticCardGroup from '@/components/common/StatisticCardGroup'


interface IProps {
  data: IDrawApplicationDetailedResponse
  drawApplicationStates: IDrawApplicationStates
  companyDetails?: IDrawApplicationCompanyDetailsResponse
}

const DataPanelsBlock = ({
  data,
  drawApplicationStates,
  companyDetails,
}: IProps): JSX.Element => {
  const { t } = useTranslation()

  const drawDetailsItems = useMemo(() => {
    const createBaseEntries = (): StatisticsBlockItem[] => [
      {
        title: t('drawApplication.page.detailed.drawDetailsBlock.terms'),
        value: t('drawApplication.page.detailed.drawDetailsBlock.termsValue', {
          count: data.paymentPlanDetails.days,
          fee: data.paymentPlanDetails.fee,
        }),
      },
      {
        title: t('drawApplication.page.detailed.drawDetailsBlock.type'),
        value: getDrawApplicationTypeTranslation(data.type, t),
      },
      {
        title: t('drawApplication.page.detailed.drawDetailsBlock.drawAmount'),
        value: currencyMask(data.drawAmount),
        valueRender: (value) =>
          isNumber(data.creditDetails.availableTradeCreditBalance) &&
          data.drawAmount > data.creditDetails.availableTradeCreditBalance ? (
            <StyledText $color={globalDesignTokens.orange} $fontSize="xl">
              {value}
            </StyledText>
          ) : (
            value
          ),
      },
    ]

    const createQuoteEntries = (): StatisticsBlockItem[] => {
      const entries: StatisticsBlockItem[] = []
      if (
        data.status === DrawApplicationStatus.IN_REVIEW ||
        data.status === DrawApplicationStatus.APPROVED
      ) {
        entries.push({
          title: t(
            'drawApplication.page.detailed.drawDetailsBlock.authHoldAmount',
          ),
          value: isNumber(data.creditHoldAmount)
            ? currencyMask(data.creditHoldAmount)
            : t('na'),
        })
      }
      if (drawApplicationStates.isQuoteAuthorized) {
        entries.push(
          {
            title: t(
              'drawApplication.page.detailed.drawDetailsBlock.authHoldAmount',
            ),
            value: isNumber(data.creditHoldAmount)
              ? currencyMask(data.creditHoldAmount)
              : t('na'),
          },
          {
            title: t(
              'drawApplication.page.detailed.drawDetailsBlock.decisionMadeBy',
            ),
            value: data.lastStatusChangedBy || t('na'),
          },
          {
            title: t(
              'drawApplication.page.detailed.drawDetailsBlock.authorizationExpirationDate',
            ),
            value: data.expirationDate
              ? formatDateDay(data.expirationDate)
              : '-',
          },
        )
      }
      if (drawApplicationStates.isQuoteApproved) {
        entries.push(
          {
            title: t(
              'drawApplication.page.detailed.drawDetailsBlock.decisionMadeBy',
            ),
            value: data.lastStatusChangedBy || t('na'),
          },
          {
            title: t(
              'drawApplication.page.detailed.drawDetailsBlock.dateOfInvoicing',
            ),
            value: data.invoicedAt ? formatDateDay(data.invoicedAt) : '-',
          },
        )
      }
      return entries
    }

    const createNonQuoteEntries = (): StatisticsBlockItem[] => {
      const entries: StatisticsBlockItem[] = [
        {
          title: t(
            'drawApplication.page.detailed.drawDetailsBlock.numOfInvoices',
          ),
          value: data.payablesDetails.length,
        },
      ]
      if (data.status !== DrawApplicationStatus.IN_REVIEW) {
        entries.push(
          {
            title: t(
              'drawApplication.page.detailed.drawDetailsBlock.decisionMadeBy',
            ),
            value: data.lastStatusChangedBy || t('na'),
          },
          {
            title: t(
              'drawApplication.page.detailed.drawDetailsBlock.dateOfDecision',
            ),
            value: data.lastStatusChangedAt
              ? formatDateDay(data.lastStatusChangedAt)
              : '-',
          },
        )
      }
      return entries
    }

    const array = createBaseEntries()

    if (drawApplicationStates.isQuote) {
      array.push(...createQuoteEntries())
    } else {
      array.push(...createNonQuoteEntries())
    }
    if (
      [
        DrawApplicationStatus.AUTHORIZED,
        DrawApplicationStatus.APPROVED,
        DrawApplicationStatus.AUTO_APPROVED,
      ].includes(data.status)
    ) {
      array.push({
        title: t('drawApplication.page.detailed.drawDetailsBlock.debtInvestor'),
        value: data.debtInvestor ?? t('na'),
      })
    }

    return array
  }, [
    data.creditDetails.availableTradeCreditBalance,
    data.creditHoldAmount,
    data.drawAmount,
    data.expirationDate,
    data.invoicedAt,
    data.lastStatusChangedAt,
    data.lastStatusChangedBy,
    data.payablesDetails.length,
    data.paymentPlanDetails.days,
    data.paymentPlanDetails.fee,
    data.status,
    data.type,
    drawApplicationStates.isQuote,
    drawApplicationStates.isQuoteApproved,
    drawApplicationStates.isQuoteAuthorized,
    t,
  ])

  const securedProductCardItems = useMemo(() => {
    const depositDetails = companyDetails?.settings?.depositDetails
    const securityDepositStatus = getSecurityDepositStatus(depositDetails)

    const isDownPaymentRequired = drawApplicationStates.isDownPaymentRequired

    const items: StatisticsBlockItem[] = [
      {
        title: t(
          'drawApplication.page.detailed.securedProductBlock.securityDeposit',
        ),
        value: getSecurityDepositTranslation(t, depositDetails),
        prefix:
          securityDepositStatus === DepositStatus.PAID ||
          securityDepositStatus === DepositStatus.NOT_REQUIRED ? (
            <CheckOutlined style={{ color: globalDesignTokens.green }} />
          ) : (
            <WarningOutlined style={{ color: globalDesignTokens.orange }} />
          ),
      },
      {
        title: t(
          'drawApplication.page.detailed.securedProductBlock.downPayment',
        ),
        value: getDrawApplicationDownPaymentStatusTranslation(
          t,
          data.downPaymentDetails,
        ),
        prefix: !isDownPaymentRequired ? (
          <CheckOutlined style={{ color: globalDesignTokens.green }} />
        ) : (
          <WarningOutlined style={{ color: globalDesignTokens.orange }} />
        ),
      },
    ]

    return items
  }, [
    companyDetails,
    data.downPaymentDetails?.percentage,
    data.paymentPlanDetails?.days,
    data.paymentPlanDetails?.fee,
    t,
  ])

  return (
    <Flex gap={18} align="start" wrap="wrap">
      <StatisticCardGroup
        title={t('drawApplication.page.detailed.drawDetailsBlock.title')}
        items={drawDetailsItems}
        data-testid="drawApplicationDetailedDrawDetailsBlock"
        cardWidth={173}
      />
      {drawApplicationStates.isNoSupplier && (
        <StatisticCardGroup
          title={t('drawApplication.page.detailed.securedProductBlock.title')}
          items={securedProductCardItems}
          data-testid="drawApplicationDetailedSupplierDetailsBlock"
          cardWidth={173}
        />
      )}

      {data.status === DrawApplicationStatus.REJECTED && (
        <StyledRejectionReasonWrapper>
          <RejectionReasonBlock
            statusCode={data.statusCode}
            statusNote={data.statusNote}
          />
        </StyledRejectionReasonWrapper>
      )}
    </Flex>
  )
}

const StyledRejectionReasonWrapper = styled.div`
  align-self: center;
  margin-left: 12px;
`

export default DataPanelsBlock
