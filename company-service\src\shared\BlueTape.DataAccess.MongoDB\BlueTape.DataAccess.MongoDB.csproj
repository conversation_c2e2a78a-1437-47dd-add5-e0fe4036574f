﻿<Project Sdk="Microsoft.NET.Sdk">

    <PropertyGroup>
        <TargetFramework>net8.0</TargetFramework>
        <ImplicitUsings>enable</ImplicitUsings>
        <Nullable>enable</Nullable>
    </PropertyGroup>

    <ItemGroup>
        <PackageReference Include="BlueTape.MongoDB" Version="1.1.30" />
        <PackageReference Include="AutoMapper" Version="12.0.0" />
        <PackageReference Include="AutoMapper.Extensions.Microsoft.DependencyInjection" Version="12.0.0" />
		<PackageReference Include="BlueTape.CompanyService" Version="1.3.2" />
		<PackageReference Include="BlueTape.Utilities" Version="1.4.5" />
    </ItemGroup>

    <ItemGroup>
      <ProjectReference Include="..\..\company\BlueTape.Company.Domain\BlueTape.Company.Domain.csproj" />
      <ProjectReference Include="..\..\document\BlueTape.Document.Domain\BlueTape.Document.Domain.csproj" />
      <ProjectReference Include="..\BlueTape.Domain\BlueTape.Domain.csproj" />
    </ItemGroup>
</Project>
