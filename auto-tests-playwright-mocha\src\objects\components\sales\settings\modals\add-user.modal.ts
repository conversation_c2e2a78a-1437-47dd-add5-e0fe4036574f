import {BasePage} from '../../../../base.page';

export class AddUserModal extends BasePage {
    constructor(page){
        super(page);
    };

    inputFields = {
        firstName: this.page.locator('[autocapitalize="sentences"] >> nth=0'),
        lastName: this.page.locator('[autocapitalize="sentences"] >> nth=1'),
        email: this.page.locator('[autocapitalize="sentences"] >> nth=2'),
    };

    buttons = {
        send: this.page.locator('"Send"'),
        cancel: this.page.locator('"Cancel"'),
    };

    checkBoxes = {
        user: this.page.locator('"User"'),
        admin: this.page.locator('"Admin"'),
    };

    async fillUpUserInformation(firstName, lastName, email){
        await this.inputFields.firstName.fill(firstName);
        await this.inputFields.lastName.fill(lastName);
        await this.inputFields.email.fill(email);
    };
}