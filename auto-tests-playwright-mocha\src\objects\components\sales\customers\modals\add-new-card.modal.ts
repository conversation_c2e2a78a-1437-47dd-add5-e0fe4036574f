import {BaseTest} from '../../../../../tests/test-utils';
import {BasePage} from '../../../../base.page';

export class AddNewCardModal extends BasePage {
    constructor(page){
        super(page);
    };

    containers = {
        ssoIFrame: this.page.frameLocator('[id="sso"]'),
    };

    inputFields = {
        cardNumber: this.containers.ssoIFrame.locator('[placeholder="13-19 digits"]'),
        expirationDate: this.containers.ssoIFrame.locator('[placeholder="MM/YY"]'),
        securityCode: this.containers.ssoIFrame.locator('[placeholder="3-4 digits"]'),

        firstName: this.page.locator('//*[@data-testid="First Name"]//input'),
        lastName: this.page.locator('//*[@data-testid="Last Name"]//input'),

        streetAddress: this.page.locator('//*[@data-testid="Street Address"]//input'),
        aptSteBldg: this.page.locator('//*[@data-testid="Apt., ste., bldg."]//input'),
        city: this.page.locator('//*[@data-testid="City"]//input'),
        zip: this.page.locator('//*[@data-testid="Zip"]//input'),
    };

    buttons = {
        addCustomerCard: this.page.locator('[data-testid="add-card-to-customer"]'),
        addCard: this.containers.ssoIFrame.locator('[value="Add Card"]'),
        cancel: this.containers.ssoIFrame.locator('[value="Cancel"]'),
        next: this.page.locator('"Next"'),
        addCardSubmit: this.page.locator('"Add Card"'),
        done: this.page.locator('"Done"'),
    };

    radioButtons = {
        personalAccount: this.page.locator('"Personal Account"'),
        businessAccount: this.page.locator('"Business Account"'),
    };

    dropdowns = {
        stateField: this.page.locator('.css-901oao.r-1loqt21.r-ubezar.r-fdjqy7.r-13qz1uu'), // ask for selector later
        stateList: this.page.locator(`"${BaseTest.constants.address.state}"`),
    };

    async fillUpCardNumber(cardNumber, expirationDate, securityCode){
        await this.inputFields.cardNumber.fill(cardNumber);
        await this.inputFields.expirationDate.fill(expirationDate);
        await this.inputFields.securityCode.fill(securityCode);
        await this.buttons.addCard.click();
    };

    async fillUpNameOnCard(firstName, lastName){
        await this.inputFields.firstName.fill(firstName);
        await this.inputFields.lastName.fill(lastName);
        await this.buttons.next.click();
    };
    
    async fillUpBillingAddress(streetAddress, city, state, zip){
        await this.inputFields.streetAddress.fill(streetAddress);
        await this.inputFields.city.fill(city);
        await this.dropdowns.stateField.click();
        await this.page.locator(`"${state}"`).click();
        await this.inputFields.zip.fill(zip);
        await this.buttons.addCardSubmit.click();
        await this.buttons.done.click();
    };
}