﻿using System.Runtime.Serialization;
using System.Text.Json.Serialization;

namespace BlueTape.Aion.DataAccess.External.Models;

[DataContract]
public class BaseAionPageAbleResponseModel : BaseAionResponseModel
{
    [JsonPropertyName("count")]
    public int Count { get; set; }
    
    [JsonPropertyName("numPages")]
    public int NumPages { get; set; } 
        
    [JsonPropertyName("pageNumber")]
    public int PageNumber { get; set; }
}