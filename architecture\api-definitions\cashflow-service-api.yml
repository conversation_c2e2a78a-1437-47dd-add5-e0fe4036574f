openapi: "3.0.0"
info:
  version: "0.0.1"
  title: "CashFlow Private Service API (as a part of Companies)"
  description: |
    API definition of CashFlow Private Service API
servers:
  - url: TBD-Dev
    description: Development server
  - url: TBD-Prod
    description: Production server
paths:
  /companies/{companyId}/cashflow:
    post:
      tags:
        - cashflow
      summary: Adds Plaid's asset report to parse
      description: Adds Plaid's asset report to parse
      operationId: addCashFlowByPlaid
      parameters:
        - name: companyId
          description: The company id
          in: path
          required: true
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/AddPlaidCashFlow"
      responses:
        201:
          description: The response, contains no information
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/AddPlaidCashFlowResponse'
        400:
          description: Bad request
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
    get:
      tags:
        - cashflow
      summary: Gets cashflow by companyId and other options
      description: Gets cashflow by companyId and other options
      operationId: getCashFlowByCompanyId
      parameters:
        - name: companyId
          description: The company id
          in: path
          required: true
          schema:
            type: string
        - name: accountId
          description: The account id
          in: query
          required: false
          schema:
            type: string
        - name: from
          description: The date from
          in: query
          required: false
          schema:
            type: string
        - name: to
          description: The date to
          in: query
          required: false
          schema:
            type: string
        - name: grouping
          description: The grouping type
          in: query
          required: false
          schema:
            type: string
            enum: 
              - daily
              - monthly
            default: daily
      responses:
        200:
          description: The items
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/CashFlowItem'
        400:
          description: Bad request
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
  /companies/{companyId}/cashflow/manual:
    post:
      tags:
        - cashflow
      summary: Creates an asset report manually
      description: Creates an asset report manually
      operationId: createAssetReportManually
      parameters:
        - name: companyId
          description: The company id
          in: path
          required: true
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/CreateAssetReportManuallyRequest"
      responses:
        201:
          description: The report was created
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/AddManualCashFlowResponse'
        400:
          description: Bad request
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
  /companies/{companyId}/cashflow/{accountId}/file/parse:
    post:
      tags:
        - cashflow
      summary: Reads an already uploaded transaction file and calculates cashflow by it (option A)
      description: Reads an already uploaded transaction file and calculates cashflow by it (option A)
      operationId: createCashFlowByTransactionFileAlreadyUploaded
      parameters:
        - name: companyId
          description: The company id
          in: path
          required: true
          schema:
            type: string
        - name: accountId
          description: The account id
          in: path
          required: true
          schema:
            type: string
        - name: userId
          description: Identifier of the user who manually uploaded
          example: 62c5e900cd65d31d3f25c34c
          in: header
          required: true
          schema:
            type: string
            nullable: false
      requestBody:
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/UploadedTransactionFile"
      responses:
        201:
          description: The cashflow was calculated.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/AddManualCashFlowResponse'
        400:
          description: Bad request
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        404:
          description: Not found
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
  /companies/{companyId}/cashflow/{accountId}/file:
    post:
      tags:
        - cashflow
      summary: Uploads a transaction list and calculates cashflow by it (option B)
      description: Uploads a transaction list and calculates cashflow by it (option B)
      operationId: createCashFlowByTransactionFile
      parameters:
        - name: companyId
          description: The company id
          in: path
          required: true
          schema:
            type: string
        - name: accountId
          description: The account id
          in: path
          required: true
          schema:
            type: string
        - name: userId
          description: Identifier of the user who manually uploaded
          example: 62c5e900cd65d31d3f25c34c
          in: header
          required: true
          schema:
            type: string
            nullable: false
      requestBody:
        content:
          text/csv:
            schema:
              type: string
              format: binary
      responses:
        201:
          description: The cashflow was calculated.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/AddManualCashFlowResponse'
        400:
          description: Bad request
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        404:
          description: Not found
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
  /companies/{companyId}/cashflow/files:
    get: 
      tags: 
        - cashflow
      summary: Lists manually uploaded transaction files
      description: Lists manually uploaded transaction files
      operationId: getTransactionFiles
      parameters:
        - name: companyId
          description: The company id
          in: path
          required: true
          schema:
            type: string
      responses:
        200:
          description: The manually uploaded transaction files
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/ManuallyUploadedTransactionFile'
        404:
          description: Not found
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
components:
  schemas:
    AddPlaidCashFlow:
      type: object
      required: 
        - accountId
        - plaidAssetReport
      properties:
        accountId:
          type: string
        plaidAssetReport:
          type: object
    AddPlaidCashFlowResponse:
      type: object
      properties:
        result:
          type: boolean
        daysAvailableFrom:
          type: string
          format: date
        daysAvailableTo:
          type: string
          format: date
    CashFlowItem:
      type: object
      properties:
        accountId:
          type: string
          nullable: true
        assetReportId:
          type: string
        date:
          type: string
          format: date
        debit:
          type: number
          format: decimal
          example: 700.00
          pattern: '[0-9]*\.[0-9]{2}'
          multipleOf: 0.01
        credit:
          type: number
          format: decimal
          example: -900.00
          pattern: '[0-9]*\.[0-9]{2}'
          multipleOf: 0.01
        balance:
          type: number
          format: decimal
          example: 11000.00
          pattern: '[0-9]*\.[0-9]{2}'
          multipleOf: 0.01
        cashFlow:
          type: number
          format: decimal
          example: -200.00
          pattern: '[0-9]*\.[0-9]{2}'
          multipleOf: 0.01
    ErrorResponse:
      type: object
      required:
        - statusCode
        - message
        - errorDescription
      properties:
        statusCode:
          description: HTTP status code corresponding to the error.
          type: integer
          example: 400
        message:
          description: A human readable error message.
          type: string
          example: Invalid model.
        errorDescription:
          description: Detailed information.
          type: string
    CreateAssetReportManuallyRequest:
      type: object
      properties: 
        accountId:
          type: string
        transactions:
          type: array
          items:
            $ref: "#/components/schemas/AssetReportManualTransactionItem"
    AssetReportManualTransactionItem:
      type: object
      properties:
        transactionId:
          type: string
        transactionDate:
          type: string
          format: date-time
        amount:
          type: number
        balance:
          type: number
    AddManualCashFlowResponse:
      type: object
      properties:
        result:
          type: boolean
        daysAvailableFrom:
          type: string
          format: date
        daysAvailableTo:
          type: string
          format: date
    ManuallyUploadedTransactionFile:
      type: object
      properties:
        accountId:
          type: string
        daysAvailableFrom:
          type: string
          format: date
        daysAvailableTo:
          type: string
          format: date
        fileName:
          type: string
          description: Filename to display on UI
        uploadedBy:
          type: string
        uploadedAt:
          type: string
          format: date-time
          description: The upload time
          example: 2024-03-08T12:49:24.907Z
    UploadedTransactionFile:
      type: object
      properties:
        s3Url:
          type: string
          example: s3://prod.uw1.linqpal-user-assets/manualtransactions/6626a37e27d09af3006a0a2b-*****************.csv
  securitySchemes:
    ApiKey:
      type: apiKey
      in: header
      name: X-Api-Key
security:
  - ApiKey: []  
