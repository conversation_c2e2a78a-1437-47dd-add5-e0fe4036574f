import {expect} from '@playwright/test';
import {test} from '../../test-utils';
import {removeKeys} from "../../../api/user/loanReceivables";
import axios from "axios";
import {sendLMSRequest} from '../../../api/common/lms-send-request';

const constants = JSON.parse(JSON.stringify(require('../../../constants/LMStestData.json')));

test.describe.skip(`Postponement of accounts receivable. API Test. @LMS @API`, async () => {
    let loanReceivables;
    let loanId: string; //todo check

    test.beforeAll(async () => {
        const requestBody = {
            "companyId": `${constants.loans.ownerId}`,
            "amount": 1140,
            "einHash": `${constants.loans.einHash}`,
            "loanTemplateId": `${constants.loanTemplates.TemplateWithTwoInstallments}`
        };
        const response = await sendLMSRequest('post', 'Loans', requestBody);
        console.log(response.response.data)

        loanId = response.data.id;
        expect(response.status).toEqual(200);

        const loanReceivableResponse = await sendLMSRequest('get', `Admin/Loans/${loanId}/LoanReceivables`);
        loanReceivables = loanReceivableResponse.data;
        expect(response.status).toEqual(200);
    });

    test.afterAll(async () => {
        const response = await axios.delete(`${process.env.LMS_BASE_URL}/Loans/${loanId}`, {
            headers: {
                accept: '*/*'
            }
        });
        expect(response.status).toEqual(200);
    });

    test(`Reschedule Receivables.`, async () => {
        const updatedData = loanReceivables.slice();
        for (let i = 0; i < updatedData.length; i++) {
            const obj = updatedData[i];
            if (obj["status"] === "Pending" && obj["type"] === "Installment") {
                updatedData[i] = {...obj, newScheduleStatus: 'Rescheduled'};
                break;
            }
        }

        const keyMap = {
            type: 'receivableType',
            expectedDate: 'newExpectedDate',
            scheduleStatus: 'newScheduleStatus',
            expectedAmount: 'newExpectedAmount',
        };

        const newArray = updatedData.map(obj => {
            const newObj = {};
            for (const [key, value] of Object.entries(obj)) {
                newObj[keyMap[key] || key] = value;
            }
            return newObj;
        });

        const keysToRemove = ['actualDate', 'actualAmount', 'adjustDate', 'adjustAmount', 'loanReceivablesPayments', 'createdBy',
            'updatedBy', 'latePaymentFeeDetails', 'status', 'loanId'];

        const modifiedArray = removeKeys(newArray, keysToRemove);

        const processedData = {
            "loanReceivables": modifiedArray,
            "note": "Extending duration"
        };

        processedData.loanReceivables.push(constants.loanResivables[0], constants.loanResivables[1]);

        const response = await axios.post(`${process.env.LMS_BASE_URL}/Admin/Loans/${loanId}/LoanReceivables/reschedule`, processedData, {
            headers: {
                accept: 'text/plain',
                userId: `${constants.loans[1].ownerId}`,
                'Content-Type': 'application/json-patch+json'
            }
        });

        expect(response.status).toEqual(200);
        expect(response.data.amount).toEqual(1140);
        expect(response.data.loanReceivables.length).toBe(8);
        expect(response.data.companyId).toEqual(constants.loans[1].ownerId);
    });
});
