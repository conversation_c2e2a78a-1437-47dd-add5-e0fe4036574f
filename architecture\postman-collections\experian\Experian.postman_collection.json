{"info": {"_postman_id": "ee0721b4-ec17-41ce-9192-c187e8109647", "name": "Experian", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json", "_exporter_id": "24833147"}, "item": [{"name": "Get Token", "event": [{"listen": "test", "script": {"exec": ["pm.environment.set(\"access_token\", pm.response.json().access_token);\r", "pm.environment.set(\"refresh_token\", pm.response.json().refresh_token);"], "type": "text/javascript"}}], "request": {"method": "POST", "header": [{"key": "Grant_type", "value": "password", "type": "text"}], "body": {"mode": "raw", "raw": "{\r\n\t\"username\": \"{{UserN<PERSON>}}\",\r\n\t\"password\": \"{{Pass}}\",\r\n\t\"client_id\": \"{{ClientId}}\",\r\n\t\"client_secret\": \"{{ClientSecret}}\"\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{url}}/oauth2/v1/token", "host": ["{{url}}"], "path": ["oauth2", "v1", "token"]}}, "response": []}, {"name": "Refresh <PERSON>", "event": [{"listen": "test", "script": {"exec": ["pm.environment.set(\"access_token\", pm.response.json().access_token);\r", "pm.environment.set(\"refresh_token\", pm.response.json().refresh_token);"], "type": "text/javascript"}}], "request": {"method": "POST", "header": [{"key": "Grant_type", "value": "refresh_token", "type": "text"}, {"key": "Refresh_Token", "value": "{{refresh_token}}", "type": "text"}, {"key": "client_id", "value": "{{ClientId}}", "type": "text"}, {"key": "client_secret", "value": "{{Client<PERSON><PERSON><PERSON>}}", "type": "text"}], "body": {"mode": "raw", "raw": "{\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{url}}/oauth2/v1/token", "host": ["{{url}}"], "path": ["oauth2", "v1", "token"]}}, "response": []}, {"name": "Business Search", "event": [{"listen": "test", "script": {"exec": [""], "type": "text/javascript"}}], "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{access_token}}", "type": "text"}], "body": {"mode": "raw", "raw": "{\r\n  \"name\": \"Experian\",\r\n  \"city\": \"Costa Mesa\",\r\n  \"state\": \"CA\",\r\n  \"subcode\": \"0563736\",\r\n  \"street\": \"475 ANTON BLVD\",\r\n  \"zip\": \"92626\",\r\n  \"phone\": \"9495673800\",\r\n  \"taxId\": \"*********\",\r\n  \"geo\": true,\r\n  \"comments\": \"testing\"\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{url}}/businessinformation/businesses/v1/search", "host": ["{{url}}"], "path": ["businessinformation", "businesses", "v1", "search"]}}, "response": []}, {"name": "(NOT USED) Business Headers", "event": [{"listen": "test", "script": {"exec": [""], "type": "text/javascript"}}], "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{access_token}}", "type": "text"}], "body": {"mode": "raw", "raw": "{\r\n  \"bin\": \"*********\",\r\n  \"subcode\": \"0586548\"\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{url}}/businessinformation/businesses/v1/headers", "host": ["{{url}}"], "path": ["businessinformation", "businesses", "v1", "headers"]}}, "response": []}, {"name": "(NOT USED) Business Aggregates", "event": [{"listen": "test", "script": {"exec": [""], "type": "text/javascript"}}], "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{access_token}}", "type": "text"}], "body": {"mode": "raw", "raw": "{\r\n  \"bin\": \"*********\",\r\n  \"subcode\": \"0586548\",\r\n  \"comments\": \"test\"\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{url}}/businessinformation/businesses/v1/aggregates", "host": ["{{url}}"], "path": ["businessinformation", "businesses", "v1", "aggregates"]}}, "response": []}, {"name": "Business Bankruptcies", "event": [{"listen": "test", "script": {"exec": [""], "type": "text/javascript"}}], "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{access_token}}", "type": "text"}], "body": {"mode": "raw", "raw": "{\r\n  \"bin\": \"*********\",\r\n  \"subcode\": \"0586548\",\r\n  \"bankruptcySummary\": true,\r\n  \"bankruptcyDetail\": true\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{url}}/businessinformation/businesses/v1/bankruptcies", "host": ["{{url}}"], "path": ["businessinformation", "businesses", "v1", "bankruptcies"]}}, "response": []}, {"name": "Business Liens", "event": [{"listen": "test", "script": {"exec": [""], "type": "text/javascript"}}], "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{access_token}}", "type": "text"}], "body": {"mode": "raw", "raw": "{\r\n  \"bin\": \"*********\",\r\n  \"subcode\": \"0586548\",\r\n  \"lienSummary\": true,\r\n  \"lienDetail\": true\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{url}}/businessinformation/businesses/v1/liens", "host": ["{{url}}"], "path": ["businessinformation", "businesses", "v1", "liens"]}}, "response": []}, {"name": "Business Judgments", "event": [{"listen": "test", "script": {"exec": [""], "type": "text/javascript"}}], "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{access_token}}", "type": "text"}], "body": {"mode": "raw", "raw": "{\r\n  \"bin\": \"*********\",\r\n  \"subcode\": \"0586548\",\r\n  \"judgmentSummary\": true,\r\n  \"judgmentDetail\": true\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{url}}/businessinformation/businesses/v1/judgments", "host": ["{{url}}"], "path": ["businessinformation", "businesses", "v1", "judgments"]}}, "response": []}, {"name": "Business Trades", "event": [{"listen": "test", "script": {"exec": [""], "type": "text/javascript"}}], "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{access_token}}", "type": "text"}], "body": {"mode": "raw", "raw": "{\r\n  \"bin\": \"700000001\",\r\n  \"subcode\": \"0586548\",\r\n  \"tradePaymentSummary\": true,\r\n  \"tradePaymentTotals\": true,\r\n  \"tradePaymentExperiences\": true,\r\n  \"tradePaymentTrends\": true\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{url}}/businessinformation/businesses/v1/trades", "host": ["{{url}}"], "path": ["businessinformation", "businesses", "v1", "trades"]}}, "response": []}, {"name": "Business Credit Status", "event": [{"listen": "test", "script": {"exec": [""], "type": "text/javascript"}}], "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{access_token}}", "type": "text"}], "body": {"mode": "raw", "raw": "{\r\n  \"bin\": \"*********\",\r\n  \"subcode\": \"0586548\"\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{url}}/businessinformation/businesses/v1/creditStatus", "host": ["{{url}}"], "path": ["businessinformation", "businesses", "v1", "creditStatus"]}}, "response": []}, {"name": "Business Owners Reports Bop", "event": [{"listen": "test", "script": {"exec": [""], "type": "text/javascript"}}], "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{access_token}}", "type": "text"}], "body": {"mode": "raw", "raw": "{\r\n    \"subcode\" : \"0589437\",\r\n    \"businessOwners\" : [ \r\n        {\r\n            \"ownerName\" : {\r\n                \"firstName\" : \"\",\r\n                \"lastName\" : \"\"\r\n            },\r\n            \"currentAddress\" : {\r\n                \"street\" : \"\",\r\n                \"city\" : \"\",\r\n                \"state\" : \"\",\r\n                \"zip\" : \"\"\r\n            },\r\n            \"ssn\" : \"\",\r\n            \"dob\" : {\r\n                \"day\" : 6,\r\n                \"month\" : 5,\r\n                \"year\" : 1973\r\n            }\r\n        }\r\n    ]\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{url}}/businessinformation/businessowners/v1/reports/bop", "host": ["{{url}}"], "path": ["businessinformation", "businessowners", "v1", "reports", "bop"]}}, "response": []}, {"name": "(NOT USED) Business SCBS Scores", "event": [{"listen": "test", "script": {"exec": [""], "type": "text/javascript"}}], "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{access_token}}", "type": "text"}], "body": {"mode": "raw", "raw": "{\r\n  \"bin\": \"*********\",\r\n  \"subcode\": \"0586548\"\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{url}}/businessinformation/sbcs/v1/reportsSbcsscores", "host": ["{{url}}"], "path": ["businessinformation", "sbcs", "v1", "reportsSbcsscores"]}}, "response": []}]}