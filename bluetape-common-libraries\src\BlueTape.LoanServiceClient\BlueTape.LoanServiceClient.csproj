﻿<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
	  <Version>1.0.34</Version>
	  <TargetFramework>net6.0</TargetFramework>
	  <ImplicitUsings>enable</ImplicitUsings>
	  <Nullable>enable</Nullable>
	  <GeneratePackageOnBuild>True</GeneratePackageOnBuild>
	  <PlatformTarget>AnyCPU</PlatformTarget>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="Microsoft.Extensions.Configuration" Version="8.0.0" />
    <PackageReference Include="Microsoft.Extensions.Logging" Version="8.0.0" />
  </ItemGroup>

  <ItemGroup>
	<ProjectReference Include="..\BlueTape.Common.ExceptionHandling\BlueTape.Common.ExceptionHandling.csproj" />
	<ProjectReference Include="..\BlueTape.Common.Extensions\BlueTape.Common.Extensions.csproj" />
	<ProjectReference Include="..\BlueTape.LS.Domain\BlueTape.LS.Domain.csproj" />
	<ProjectReference Include="..\BlueTape.LS\BlueTape.LS.csproj" />
	<ProjectReference Include="..\BlueTape.Services.Utilities\BlueTape.Utilities.csproj" />
  </ItemGroup>

</Project>
