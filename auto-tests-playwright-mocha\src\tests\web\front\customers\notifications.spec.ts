import {test} from "../../../test-utils";
import {BaseAPI} from "../../../../api/base-api";
import {createCustomerInvoice} from "../../../../api/user/create-invoice";
import {expect} from "@playwright/test";

test.describe('Notification tests', async () => {
    const userInfo_id = process.env.USER_CUSTOMERID;
    const totalAmount = Math.floor(Math.random() * 200).toString() + ".00";

    test.beforeEach(async ({userIdToken}) => {
        const userAuth = await BaseAPI.getAuth(userIdToken);
        await createCustomerInvoice(userAuth.session, userAuth.challenge, userInfo_id, totalAmount);
    });

    test.afterEach(async ({customerPageManager}) => {
        await customerPageManager.page.close();
    });

    test('Post-invoice notification', async ({customerPageManager}) => {
        await customerPageManager.sideMenu.sideMenuTabs.notifications.click();

        await customerPageManager.page.waitForLoadState("networkidle");

        const lastInvoice = await customerPageManager.notificationsMain.notifications.allOfNotificationsLocator.first();

        await expect(lastInvoice).toContainText(totalAmount);
        await expect(lastInvoice).toContainText("few seconds ago");
    });
});
