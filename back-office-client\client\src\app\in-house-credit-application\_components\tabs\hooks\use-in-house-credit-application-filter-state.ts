import { produce } from 'immer'
import type { ParsedQuery } from 'query-string'

import type { InHouseCreditApplicationStatus } from '@/app/in-house-credit-application/_types'
import type { IFilterState } from '@/app/in-house-credit-application/_components/tabs/components/InHouseCreditApplicationTableFilter'
import { updateParamsFromState, useSearchParamsState } from '@/globals/hooks'
import type { AutomatedDecision, BusinessCategory } from '@/globals/types'

const mapParamsToState = (params: ParsedQuery): IFilterState => ({
  category: (params.category as BusinessCategory[]) ?? [],
  status: (params.status as InHouseCreditApplicationStatus[]) ?? [],
  name: (params.name as string) ?? undefined,
  appDateFrom: (params.appDateFrom as string) ?? undefined,
  appDateTo: (params.appDateTo as string) ?? undefined,
  decisionDateFrom: (params.decisionDateFrom as string) ?? undefined,
  decisionDateTo: (params.decisionDateTo as string) ?? undefined,
  automatedDecision: (params.automatedDecision as AutomatedDecision[]) ?? [],
})

const mapStateToParams = (state: IFilterState, params: ParsedQuery) =>
  produce(params, (draft) => {
    const keys = [
      'category',
      'status',
      'automatedDecision',
      'name',
      'appDateFrom',
      'appDateTo',
      'decisionDateFrom',
      'decisionDateTo',
    ] as const

    updateParamsFromState(draft, state, keys)
  })

export const useInHouseCreditApplicationFilterState = (
  isActive: boolean,
  shouldUseDefault: boolean,
) => {
  return useSearchParamsState(
    isActive,
    shouldUseDefault,
    mapParamsToState,
    mapStateToParams,
  )
}
