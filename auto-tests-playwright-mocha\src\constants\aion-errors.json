{"pull": {"code": {"invalidBankAccount": "bankaccount_does_not_exist", "descriptionMoreThanTenCharacters": "MaximumLengthValidator", "addendaMoreThanEightyCharacters": "PredicateValidator", "notNullValidator": "NotNullValidator", "invalidModelState": "InvalidModelState"}, "reason": {"addendaMoreThanEightyCharacters": "The total number of characters in the array must not exceed 80.", "emptyCompanyId": "'Company Id' must not be empty.", "emptyBankAccountId": "'Bank Account Id' must not be empty."}}}