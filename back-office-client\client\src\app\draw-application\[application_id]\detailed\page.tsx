'use client'

import { Spin } from 'antd'
import type { BreadcrumbItemType } from 'antd/es/breadcrumb/Breadcrumb'
import { useTranslation } from 'react-i18next'
import { useParams } from 'next/navigation'
import { useMemo } from 'react'

import PageWrapper from '@/components/common/PageWrapper'
import CentredSpinner from '@/components/common/CentredSpinner'
import Detail from '@/app/draw-application/[application_id]/detailed/_components/Detail'
import { AppRoutes } from '@/globals/routes'
import { useDrawApplicationQuery } from '@/lib/redux/api/draw-application'

const DrawApplicationDetailPage = (): JSX.Element => {
  const { t } = useTranslation<string | undefined>()
  const router = useParams()
  const applicationId = router.application_id as string

  const { data, isFetching, isLoading, isSuccess } = useDrawApplicationQuery({
    applicationId,
  })

  const breadcrumbItems = useMemo(() => {
    const items: BreadcrumbItemType[] = [
      {
        href: AppRoutes.drawApplication.main(),
        title: t('menu.drawApplication'),
      },
    ]

    if (data) {
      items.push({
        title: data.businessName,
      })
    }

    return items
  }, [t, data])

  return (
    <PageWrapper breadcrumbItems={breadcrumbItems}>
      {isLoading || !isSuccess ? (
        <CentredSpinner />
      ) : (
        <Spin spinning={isFetching} delay={0} size="large">
          <Detail data={data} />
        </Spin>
      )}
    </PageWrapper>
  )
}

export default DrawApplicationDetailPage
