﻿<?xml version="1.0" encoding="utf-8"?>
<package xmlns="http://schemas.microsoft.com/packaging/2012/06/nuspec.xsd">
  <metadata>
    <id>BlueTape.LoanServiceClient</id>
    <version>1.0.33</version>
    <authors>BlueTape.LoanServiceClient</authors>
    <description>Package Description</description>
    <repository type="git" commit="6779c077507acaa8ff59bda9391017a624200bbe" />
    <dependencies>
      <group targetFramework="net6.0">
        <dependency id="BlueTape.Common.ExceptionHandling" version="1.0.8" exclude="Build,Analyzers" />
        <dependency id="BlueTape.Common.Extensions" version="1.1.0" exclude="Build,Analyzers" />
        <dependency id="BlueTape.LS.Domain" version="1.1.36" exclude="Build,Analyzers" />
        <dependency id="BlueTape.LS" version="1.1.78" exclude="Build,Analyzers" />
        <dependency id="BlueTape.Utilities" version="1.4.6" exclude="Build,Analyzers" />
        <dependency id="Microsoft.Extensions.Configuration" version="8.0.0" exclude="Build,Analyzers" />
        <dependency id="Microsoft.Extensions.Logging" version="8.0.0" exclude="Build,Analyzers" />
      </group>
    </dependencies>
  </metadata>
  <files>
    <file src="C:\Users\<USER>\source\repos\BlueTape\bluetape-common-libraries\src\BlueTape.LoanServiceClient\bin\Debug\net6.0\BlueTape.LoanServiceClient.dll" target="lib\net6.0\BlueTape.LoanServiceClient.dll" />
  </files>
</package>