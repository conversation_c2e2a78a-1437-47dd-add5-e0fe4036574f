using BlueTape.Aion.Application.Abstractions;
using BlueTape.Aion.Domain.Constants;
using BlueTape.Aion.Domain.Extensions;
using BlueTape.Common.ExceptionHandling.Exceptions.Base;
using BlueTape.Common.ExceptionHandling.Extensions;
using BlueTape.Common.Extensions.Abstractions;
using Microsoft.Azure.Functions.Worker;
using Microsoft.Extensions.Logging;
using Serilog.Context;
using System.Diagnostics.CodeAnalysis;

namespace BlueTape.Functions.Aion.TransactionStatusReport;

[ExcludeFromCodeCoverage]
public class DailyAchProcessingFunction(
    ITraceIdAccessor traceIdAccessor,
    IDailyAchProcessingService dailyAchProcessingService,
    IErrorNotificationService notificationService,
    ILogger<DailyAchProcessingFunction> logger)
{
    private const string BlueTapeCorrelationId = "BlueTapeCorrelationId";

    [Function("DailyAchProcessingFunction")]
    public async Task Run([TimerTrigger("0 */5 10-22 * * 1-5")] TimerInfo myTimer, CancellationToken ct)
    {
        traceIdAccessor.TraceId = $"{Guid.NewGuid()}-{nameof(ExternalTransactionStatusReportFunction)}";

        using (GlobalLogContext.PushProperty("Method", "Scheduler"))
        using (GlobalLogContext.PushProperty(BlueTapeCorrelationId, traceIdAccessor.TraceId))
        {
            try
            {
                logger.LogInformation("Start DailyAchProcessingFunction");
                var environment = Environment.GetEnvironmentVariable(EnvironmentConstants.Environment);

                if (environment?.IsEnvironmentDevOrBeta() ?? false)
                {
                    logger.LogInformation("DailyAchProcessingFunction execution is suppressed due to environment: {environment}", environment);
                    return;
                }

                await dailyAchProcessingService.ProcessAsync(ct: ct);

            }
            catch (DomainException domainEx)
            {
                await notificationService.Notify(domainEx.GetSlackEventMessageBody(EnvironmentExtensions.GetExecutionEnvironment(), AionFunctionConstants.ProjectName), ct);
                logger.LogError(domainEx, "DailyAchProcessingFunction scheduler function failed: {message}", domainEx.Message);
                throw;
            }
            catch (Exception ex)
            {
                await notificationService.Notify(ex.GetSlackEventMessageBody(EnvironmentExtensions.GetExecutionEnvironment(), AionFunctionConstants.ProjectName, maxMessageLength: 400), ct);
                logger.LogError(ex, "DailyAchProcessingFunction scheduler function failed: {message}", ex.Message);
                throw;
            }
        }
    }
}
