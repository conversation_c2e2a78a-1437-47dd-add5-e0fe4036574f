﻿import {BaseTest, test} from '../../test-utils';
import {insertTestReport} from "../../../database/autoTests/AutoTestReportRepository";
import {Action, ITestReport} from "../../../database/autoTests/entities/AutoTestReport";
import {GenericIntegrationService} from "../../../services/genericIntegrationService";
import {paymentService} from "../../../services/paymentService";
import {CustomerService} from "../../../services/customerService";
import {BackOfficeClient} from "../../../api/back-office-decision-engine/backOfficeClient";
import { DrawApprovalsRepository } from '../../../database/drawApplication/drawApprovalsRepository';
import { InvoiceRepository } from '../../../database/invoices/invoiceRepository';
import { BackOfficeService } from '../../../services/backOfficeService';

test.use({storageState: {cookies: [], origins: []}});

const testGroup = `@paymentsE2E Create invoice via generic API, and pay via all possible methods.`

test.describe.parallel(testGroup, async () => {
    
    let testGroupId: string
    let resultPerTest: boolean
    let actionsPerTest: Action[]
    let report: ITestReport
    let _genericIntegrationService:GenericIntegrationService
    let _paymentService: paymentService
    
    test.beforeAll(async () => {
        _genericIntegrationService = new GenericIntegrationService()
        _paymentService = new paymentService(new InvoiceRepository(), new BackOfficeService(new DrawApprovalsRepository(), new BackOfficeClient()), new CustomerService())
        resetReportToDefault()
    })
    
    function resetReportToDefault() {
        resultPerTest = true;
        testGroupId = process.env.TestGroupId
        actionsPerTest = []
        
        report = {
            testGroup: testGroup,
            testGroupId: testGroupId,
        } as ITestReport
    }

    async function finalizeSingleTestReport() {
        report.createdAt = new Date()
        report.result = resultPerTest
        report.action = actionsPerTest;

        try {
            await insertTestReport(report);
        }
        catch (e) {
            console.log(e)
        }

        resetReportToDefault()
    }

    test.afterEach(async ({}, testInfo) => {
        if (testInfo.status !== 'passed') {
            actionsPerTest.push({ description: `Test failed with error: ${testInfo.error?.message}`});
            resultPerTest = false;
        }
        
        await finalizeSingleTestReport();
        report._id = null;
    });

    test("@paymentsE2E Create simplify invoice via genericAPI and pay it with ACH", async ({ browser }) => {
        report.testName = "@genericPayments Create simplify invoice via genericAPI and pay it with ACH"
        test.setTimeout(250000);
        
        const createInvoiceResponse = await _genericIntegrationService.createGenericSimplifyInvoiceAndGetCheckOutUrl(browser, actionsPerTest);
        await _genericIntegrationService.integrationPaymentRedirectPayWithAch(createInvoiceResponse.redirectUrl, browser, actionsPerTest);
        await _paymentService.makeInoviceSuccesfulyPaidViaAch(createInvoiceResponse.invoiceId, browser, actionsPerTest);
    });

    test("@paymentsE2E Create simplify invoice via genericAPI and pay it with BTC", async ({ browser, adminIdToken }) => {
        report.testName = "@genericPayments Create simplify invoice via genericAPI and pay it with BTC"
        test.setTimeout(450000);

        const createInvoiceResponse = await _genericIntegrationService.createGenericSimplifyInvoiceAndGetCheckOutUrl(browser, actionsPerTest);
        await _genericIntegrationService.integrationPaymentRedirectPayWithBTC(createInvoiceResponse.redirectUrl, browser, actionsPerTest);

        await BaseTest.delayOperation(20000)
        
        await _paymentService.makeInoviceSuccesfulyPaidViaBTC(createInvoiceResponse.invoiceId, '', browser, '<EMAIL>', 'Qazwsx123!', actionsPerTest, adminIdToken);
    });
});