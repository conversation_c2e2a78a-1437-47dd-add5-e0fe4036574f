@startuml Notification.Invoices
title Notification.Invoices processing mechanism

participant "UserInteractionService" as uis #SkyBlue
queue "SQS.fifo" as sqs
participant "Notification.Invoicing" as notif #SkyBlue
database "-Invoices-\n-Conversations-\n-Notifications-\n-Notification sessions-" as db #Orange
participant "Linqpal" as linq #SkyBlue

autonumber "<b>[00]"

== Scheduled process ==

linq -> db : Invoices: Invoice created
linq -> db : Notifications: Invoice created

loop
    notif -> db : Notifications: Collect pending notifications

    loop for each customer in pending notifications
        notif -> db : Sessions: Check active notification session\n**considering expiration time (10 mins)**
        notif -> db : Conversations: Check ongoing conversation with customer\n**considering expiration time (10 mins)**

        alt #LightGreen if session and conversation not exist
            notif -> db : Sessions: Create notification session with supplier
            notif -> db : Notifications: Update notifications with sessionId
            notif -> db : Invoices: Collect remaining unpaid invoices by the supplier
            
            notif -> sqs : Send notification\n(notified and unpaid invoices)
            sqs -> uis        
        else #LightPink
            alt session exists, same supplier
                notif -> db : Session: Add invoice to session
                notif -> sqs : Send notification\n(notified and unpaid invoices)
                sqs -> uis        
            else different supplier
                notif --> notif : Do nothing
            end
        end        
    end
end

@enduml