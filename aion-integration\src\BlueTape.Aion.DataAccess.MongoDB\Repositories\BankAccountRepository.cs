﻿using AutoMapper;
using BlueTape.Aion.DataAccess.MongoDB.Abstractions;
using BlueTape.Aion.DataAccess.MongoDB.Entities.BanksAccounts;
using BlueTape.Aion.Domain.DTOs.BankAccount;
using BlueTape.MongoDB.Abstractions;
using MongoDB.Driver;

namespace BlueTape.Aion.DataAccess.MongoDB.Repositories;

public class BankAccountRepository : IBankAccountRepository
{
    private readonly IMongoDbContext _dbContext;
    private readonly IMapper _mapper;

    public BankAccountRepository(IMongoDbContext dbContext, IMapper mapper)
    {
        ArgumentNullException.ThrowIfNull(dbContext);
        ArgumentNullException.ThrowIfNull(mapper);

        _dbContext = dbContext;
        _mapper = mapper;
    }

    public async Task<BankAccountDto> GetByBankAccountIdAsync(string bankAccountId, CancellationToken cancellationToken)
    {
        var bankAccount = await _dbContext.GetCollection<BankAccountEntity>()
            .Find(x => x.BlueTapeBankAccountId.Equals(bankAccountId))
            .FirstOrDefaultAsync(cancellationToken);

        return _mapper.Map<BankAccountDto>(bankAccount);
    }

    public async Task UpdateAionSettingsAsync(BankAccountDto bankAccountDto, CancellationToken cancellationToken)
    {
        if (bankAccountDto.AionSettings == null)
            return;

        var filter = Builders<BankAccountEntity>
            .Filter.Where(x => x.BlueTapeBankAccountId == bankAccountDto.BlueTapeBankAccountId);

        var existingAccount = await _dbContext.GetCollection<BankAccountEntity>()
            .Find(filter)
            .FirstOrDefaultAsync(cancellationToken);

        if (existingAccount == null)
            return;

        var updatedAionSettings = existingAccount.AionSettings ?? new BankAccountAionSettingsEntity();

        if (bankAccountDto.AionSettings.TransferMethodId != null)
            updatedAionSettings.TransferMethodId = bankAccountDto.AionSettings.TransferMethodId;

        if (bankAccountDto.AionSettings.TransferMethodId2 != null)
            updatedAionSettings.TransferMethodId2 = bankAccountDto.AionSettings.TransferMethodId2;

        if (bankAccountDto.AionSettings.TransferMethodId3 != null)
            updatedAionSettings.TransferMethodId2 = bankAccountDto.AionSettings.TransferMethodId3;
        
        if (bankAccountDto.AionSettings.WireTransferMethodId != null)
            updatedAionSettings.WireTransferMethodId = bankAccountDto.AionSettings.WireTransferMethodId;
        
        if (bankAccountDto.AionSettings.WireTransferMethodId2 != null)
            updatedAionSettings.WireTransferMethodId2 = bankAccountDto.AionSettings.WireTransferMethodId2;
        
        if (bankAccountDto.AionSettings.WireTransferMethodId3 != null)
            updatedAionSettings.WireTransferMethodId3 = bankAccountDto.AionSettings.WireTransferMethodId3;
        
        if (bankAccountDto.AionSettings.InstantTransferMethodId != null)
            updatedAionSettings.InstantTransferMethodId = bankAccountDto.AionSettings.InstantTransferMethodId;
        
        if (bankAccountDto.AionSettings.InstantTransferMethodId2 != null)
            updatedAionSettings.InstantTransferMethodId2 = bankAccountDto.AionSettings.InstantTransferMethodId2;
        
        if (bankAccountDto.AionSettings.InstantTransferMethodId3 != null)
            updatedAionSettings.InstantTransferMethodId3 = bankAccountDto.AionSettings.InstantTransferMethodId3;

        var update = Builders<BankAccountEntity>.Update
            .Set(x => x.AionSettings, updatedAionSettings);

        await _dbContext.GetCollection<BankAccountEntity>()
            .UpdateOneAsync(filter, update, cancellationToken: cancellationToken);
    }
}
