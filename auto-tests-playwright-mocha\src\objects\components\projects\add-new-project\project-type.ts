import {BasePage} from "../../../base.page";

export class ProjectType extends BasePage {
    constructor(page) {
        super(page);
    };

    buttons = {
        private: this.page.locator('[data-testid="businessOwner_isAuthorized.private"]'),
        public: this.page.locator('[data-testid="businessOwner_isAuthorized.public"]'),
        federal: this.page.locator('[data-testid="businessOwner_isAuthorized.federal"]'),
        commercial: this.page.locator('[data-testid="projectType_privateType.commercial"]'),
        residential: this.page.locator('[data-testid="projectType_privateType.residential"]'),
        spec: this.page.locator('[data-testid="projectType_builtFor.spec"]'),
        propertyNewBuilding: this.page.locator('[data-testid="projectType_builtFor.newBuilding"]'),
        propertyRenovation: this.page.locator('[data-testid="projectType_builtFor.renovation"]'),
        yes: this.page.locator('[data-testid="projectType_bondExists.yes"]'),
        no: this.page.locator('[data-testid="projectType_bondExists.no"]'),
        uploadFile: this.page.locator('[data-testid="UploadButton"]'),
        deleteFile: this.page.locator('_react=[key="uploadBond"] >> _react=l'),
        deleteButton: this.page.getByText('Delete')
    };

    elements = {
        uploadedFile: this.page.locator('_react=[key="uploadBond"] >> _react=q'),
        uploadedFileName: this.page.locator('text=testFile.png')
    };

    input = {
        publicDescription: this.page.locator('[data-testid="descriptionOfPublicProject"] input'),
        privateOther: this.page.locator('[data-testid="built-for-other"]')
    };

    async chooseFederalTypeWithoutBond() {
        await this.buttons.federal.click();
        await this.buttons.no.click();
    };

    async chooseFederalTypeWithBondAndUploadFile() {
        await this.buttons.federal.click();
        await this.buttons.yes.click();
        await this.buttons.uploadFile.click();
        await this.uploadFile(this.buttons.uploadFile);
    };

    async uploadMockFile() {
        await this.buttons.uploadFile.click();
        await this.uploadFile(this.buttons.uploadFile);
    }
}