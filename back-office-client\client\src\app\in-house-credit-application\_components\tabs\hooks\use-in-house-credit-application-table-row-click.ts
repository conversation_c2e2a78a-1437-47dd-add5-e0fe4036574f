import { useCallback, type Mouse<PERSON><PERSON><PERSON><PERSON><PERSON> } from 'react'
import { useRouter } from 'next/navigation'

import { AutomatedDecisionStatus, TableRowClasses } from '@/globals/types'
import { AppRoutes } from '@/globals/routes'

interface IRecord {
  id: string
  automatedDecisionStatus: AutomatedDecisionStatus
}

export const useInHouseCreditApplicationTableRowClick = () => {
  const router = useRouter()

  const onRowClick = useCallback(
    (record: IRecord): MouseEventHandler =>
      (e) => {
        if (e.target instanceof HTMLElement && e.target.tagName === 'A') {
          return
        }

        if (
          record.automatedDecisionStatus !== AutomatedDecisionStatus.PROCESSING
        ) {
          router.push(AppRoutes.inHouseCreditApplication.details(record.id))
        }
      },
    [router],
  )

  const rowClassName = useCallback((record: IRecord) => {
    return record.automatedDecisionStatus !== AutomatedDecisionStatus.PROCESSING
      ? TableRowClasses.CLICKABLE_ROW
      : TableRowClasses.NON_CLICKABLE_ROW
  }, [])

  return { onRowClick, rowClassName }
}
