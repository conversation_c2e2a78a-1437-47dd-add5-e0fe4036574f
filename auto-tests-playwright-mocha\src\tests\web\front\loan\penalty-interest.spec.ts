import {expect} from '@playwright/test';
import {test, BaseTest} from '../../../test-utils';
import {BaseAPI} from '../../../../api/base-api';
import {createCustomerInvoice} from '../../../../api/user/create-invoice';
import {payWithBluetapeCredit, callDecision} from '../../../../api/user';
import {verifyLoan, approveLoan} from '../../../../api/admin';
import {currentDateInNorthAmericanFormat, delay} from '../../../../utils/waiters';
import {
    calculateReplanDate,
    postReplaceLoanReceivables,
    sendLMSRequest
} from '../../../../api/common/lms-send-request';
import {changeDateWithDays, getDaysBetweenDates} from '../../../../utils/change-date';
import {getSortAi} from '../../../../api/sofrai/getSorfAI';

test.use({storageState: {cookies: [], origins: []}});
const constants = JSON.parse(JSON.stringify(require('../../../../constants/LMStestData.json')));

test.describe('Loans edit payment schedule', async () => {

    let supplierAuth: { session: any; challenge: any; };
    let customerAuth: { session: any; challenge: any; };
    let invoice: { id: string; };
    let loanRequest: { id: string; };
    let loanId: string;
    let loanReceivables;
    let outstandingPrincipalAmount = 0;
    const processingPaymentsAmount = 0;
    const basisPoint: number = constants.penaltyInterest.basisPoint;
    let sofrAI30days: number;
    const daysInYear: number = constants.penaltyInterest.daysInYear;
    let amountForDay;
    let currentDate: string;
    let totalAmount: string;
    let totalAmountOnSite: string;
    let responsePenaltyDetector: { data: { loanReceivables: any; }; };
    let loanOutstandingAmount: string | RegExp | (string | RegExp)[];

    async function calculateOutstandingPrincipalAmount(arrOfReceivables) {
        for (const el of arrOfReceivables) {
            if (el.status !== "Paid" && (el.type === "Installment" || el.type === "LoanFee")) {
                outstandingPrincipalAmount += await el.expectedAmount;
            }
        }
    }

    async function reloadPageUntilElementIsVisible({adminPageManager}, element) {
        for (let i = 0; i < 20; i += 1) {
            if (await element.isVisible()) {
                break;
            } else {
                await adminPageManager.page.reload();
                await adminPageManager.backOfficeSideMenu.sideMenuTabs.loans.click();
            }
        }
    }

    async function changeExpectedDateOfReceivables(arrOfReceivables, element, overdueDays) {
        const expectedDate = await arrOfReceivables[element].expectedDate;
        const overdueDate = await calculateReplanDate(-overdueDays);
        const dif = await getDaysBetweenDates(expectedDate, overdueDate);
        await arrOfReceivables.forEach(async function (el) {
            const getNewDate = await changeDateWithDays(el.expectedDate, -dif);
            el.expectedDate = getNewDate;
        });
    }

    test.beforeAll(async ({userIdToken, customerIdToken, adminIdToken, adminPageManager}) => {
        test.slow();
        const getSorfAi = await getSortAi();
        sofrAI30days = getSorfAi.data.refRates[0].average30day;
        currentDate = await currentDateInNorthAmericanFormat();
        totalAmount = `5${new Date().getMilliseconds().toString()}`.padEnd(4, '0');
        totalAmountOnSite = `$${totalAmount.slice(0, 1)},${totalAmount.slice(1)}.00`;
        supplierAuth = await BaseAPI.getAuth(userIdToken);
        invoice = await createCustomerInvoice(supplierAuth.session, supplierAuth.challenge, process.env.USER_CUSTOMERID, totalAmount);
        customerAuth = await BaseAPI.getAuth(customerIdToken);
        loanRequest = await payWithBluetapeCredit(customerAuth.session, customerAuth.challenge, invoice.id);
        await callDecision(customerAuth.session, customerAuth.challenge, loanRequest.id);
        await delay(270000);
        await verifyLoan(adminIdToken, loanRequest.id);
        await delay(2000);
        await approveLoan(adminIdToken, loanRequest.id);
        await delay(65000);
        await adminPageManager.backOfficeSideMenu.sideMenuTabs.loans.click();
        loanId = await adminPageManager.loans.getLoanId(totalAmountOnSite);
        const getLoanReceivables = await sendLMSRequest('get', `Loans/${loanId}`);
        loanReceivables = await getLoanReceivables.data.loanReceivables;
        await changeExpectedDateOfReceivables(loanReceivables, 0, 8);
        await postReplaceLoanReceivables(loanId, loanReceivables);
        responsePenaltyDetector = await sendLMSRequest('patch', `PenaltyDetector/${loanId}`);
    });

    test.afterEach(async ({adminPage, customerPage}) => {
        await adminPage.close();
        await customerPage.close();
    });

    test.skip('Penalty interest should be visible @penalty', async ({adminPageManager}) => {
        await adminPageManager.backOfficeSideMenu.sideMenuTabs.loans.click();
        await reloadPageUntilElementIsVisible({adminPageManager}, await adminPageManager.backOfficeSideMenu.sideMenuTabs.loans);
        await adminPageManager.loans.elements.companyName(totalAmountOnSite).click();

        await expect(adminPageManager.loansStatusDetails.elements.penaltyInterest, 'Penalty interest should be visible').toBeVisible();
    });

    test.skip(`Penalty interest was calculated. Date should be today @penalty`, async ({adminPageManager}) => {
        await adminPageManager.backOfficeSideMenu.sideMenuTabs.loans.click();
        await reloadPageUntilElementIsVisible({adminPageManager}, await adminPageManager.backOfficeSideMenu.sideMenuTabs.loans);
        await adminPageManager.loans.elements.companyName(totalAmountOnSite).click();

        await expect(adminPageManager.loansStatusDetails.elements.penaltyInterest, `Penalty date should be equal ${currentDate}`).toContainText(currentDate);
    });

    test.skip(`Penalty interest was calculated. Penalty amount should be equal calculated @penalty`, async ({adminPageManager}) => {
        await adminPageManager.backOfficeSideMenu.sideMenuTabs.loans.click();
        await reloadPageUntilElementIsVisible({adminPageManager}, await adminPageManager.backOfficeSideMenu.sideMenuTabs.loans);
        await adminPageManager.loans.elements.companyName(totalAmountOnSite).click();
        const loanReceivablesWithPenalty = responsePenaltyDetector.data.loanReceivables;
        await calculateOutstandingPrincipalAmount(loanReceivablesWithPenalty);
        amountForDay = ((outstandingPrincipalAmount - processingPaymentsAmount) * ((basisPoint * 12) + sofrAI30days / 100) / daysInYear).toFixed(2);

        await expect(adminPageManager.loansStatusDetails.elements.penaltyInterest, `Penalty date should be equal ${amountForDay}`).toContainText(amountForDay);
    });

    test.skip(`Penalty interest was calculated. Outstanding Principal Amount in penalty interest should be equal @penalty`, async ({adminPageManager}) => {
        loanOutstandingAmount = (Number(outstandingPrincipalAmount) + Number(amountForDay)).toFixed(2).replace(/\d(?=(\d{3})+\.)/g, '$&,');
        await reloadPageUntilElementIsVisible({adminPageManager}, await adminPageManager.backOfficeSideMenu.sideMenuTabs.loans);
        await adminPageManager.backOfficeSideMenu.sideMenuTabs.loans.click();
        await adminPageManager.loans.elements.companyName(totalAmountOnSite).click();

        await expect(adminPageManager.loansStatusDetails.elements.penaltyInterest, `Outstanding principal amount should be equal ${loanOutstandingAmount}`).toContainText(loanOutstandingAmount);
    });

    test.skip(`Penalty interest was calculated.Loan Outstanding Principal Amount should be equal ${loanOutstandingAmount} @penalty`, async ({adminPageManager}) => {
        await adminPageManager.backOfficeSideMenu.sideMenuTabs.loans.click();
        await reloadPageUntilElementIsVisible({adminPageManager}, await adminPageManager.backOfficeSideMenu.sideMenuTabs.loans);
        await adminPageManager.loans.elements.companyName(totalAmountOnSite).click();
        await adminPageManager.page.waitForLoadState('networkidle');
        const loanOutstandingAmountOnPage = await adminPageManager.loansStatusDetails.elements.loanOutstandingAmount.getAttribute('value');

        await expect(loanOutstandingAmountOnPage, `Loan outstanding principal amount should be equal ${loanOutstandingAmount}`).toEqual(`$${loanOutstandingAmount}`);
    });

    test.skip(`Penalty interest was calculated. Next payment amount should be equal @penalty`, async ({adminPageManager}) => {
        const getLoanReceivables = await sendLMSRequest('get', `Loans/${loanId}`);
        const loanReceivables = getLoanReceivables.data.loanReceivables;
        const nextPaymentAmount = await adminPageManager.loansStatusDetails.getAmountOfLateInstallmentsAndPenalty(loanReceivables);
        await adminPageManager.backOfficeSideMenu.sideMenuTabs.loans.click();
        await reloadPageUntilElementIsVisible({adminPageManager}, await adminPageManager.backOfficeSideMenu.sideMenuTabs.loans);
        await adminPageManager.loans.elements.companyName(totalAmountOnSite).click();
        await adminPageManager.page.waitForLoadState('networkidle');
        const nextPaymentAmountOnPage = await adminPageManager.loansStatusDetails.elements.nextPaymentAmount.getAttribute('value');

        await expect(nextPaymentAmountOnPage, `Next payment amount should be equal ${nextPaymentAmountOnPage}`).toEqual(`$${nextPaymentAmount.toFixed(2).replace(/\d(?=(\d{3})+\.)/g, '$&,')}`);
    });

    test.skip(`Penalty interest was calculated. Total daily penalty interest' box is displayed @penalty`, async ({customerPageManager}) => {
        await customerPageManager.sideMenu.openPaySubTab(await customerPageManager.sideMenu.sideMenuSubTabs.pay.credit);
        await customerPageManager.creditList.loanList.loanListElement.last().click();
        await customerPageManager.page.waitForLoadState('networkidle');

        await expect(customerPageManager.creditDetails.containers.penaltyInfo, "Total daily penalty interest' box is displayed").toContainText(amountForDay);
    });

    test.skip(`Penalty interest was calculated. Click on the button "Make Payment". In the modal window should be visible penalty interest @penalty`, async ({customerPageManager}) => {
        await customerPageManager.sideMenu.openPaySubTab(await customerPageManager.sideMenu.sideMenuSubTabs.pay.credit);
        await customerPageManager.creditList.loanList.loanListElement.last().click();
        await customerPageManager.page.waitForLoadState('networkidle');
        await customerPageManager.creditDetails.buttons.makePayment.click();
        await customerPageManager.page.waitForLoadState('networkidle');

        await expect(customerPageManager.makePaymentModal.text.penaltyInterestAmount).toContainText(amountForDay);
    });

    test.skip(`Penalty interest was calculated. Total daily penalty interest' box is displayed in the credit list @penalty`, async ({customerPageManager}) => {
        await customerPageManager.sideMenu.openPaySubTab(await customerPageManager.sideMenu.sideMenuSubTabs.pay.credit);

        await expect(customerPageManager.creditList.containers.penaltyInfo, "Total daily penalty interest' box is displayed").toBeVisible();
    });

    // test('Loan fee and 1th Installment is late to 7 calendar days. Penalty interest should be calculated ', async ({adminPageManager}) => {
    //     test.slow();
    //     await adminPageManager.backOfficeSideMenu.sideMenuTabs.loans.click();
    //     loanId = await adminPageManager.loans.getLoanId(totalAmountOnSite);
    //     const getLoanReceivables = await sendLMSRequest('get', `Loans/${loanId}`);
    //     loanReceivables = getLoanReceivables.data.loanReceivables;
    //     await changeExpectedDateOfReceivables(loanReceivables, 0, 8);
    //     await postReplaceLoanReceivables(loanId, loanReceivables);
    //     const getPenaltyInfo = await sendLMSRequest('patch', `PenaltyDetector/${loanId}`);
    //     const loanReceivablesWithPenalty = getPenaltyInfo.data.loanReceivables;
    //     await calculateOutstandingPrincipalAmount(loanReceivablesWithPenalty);
    //     amountForDay = ((outstandingPrincipalAmount - processingPaymentsAmount) * ((basisPoint * 12) + sofrAI30days/100)/daysInYear).toFixed(2);
    //     const penaltyReceivable = loanReceivablesWithPenalty.find(el => el.type === 'PenaltyInterestFee');
    //     await adminPageManager.loans.elements.companyName(totalAmountOnSite).click();
    //     await expect(adminPageManager.loansStatusDetails.elements.penaltyInterest, 'Penalty interest should be calculated').toBeVisible();
    //     await expect(adminPageManager.loansStatusDetails.elements.penaltyInterest, `Penalty ecpected amount should be equal ${amountForDay}`).toContainText(amountForDay);
    // });

    // test('Loan Fee and 1th Installment is late to 4 calendar days and paid, 2th Installment has been paid on time, 3th Installment is late to 3 days. Penalty interest should be calculated ', async ({adminPageManager}) => {
    //     test.slow();
    //     await adminPageManager.backOfficeSideMenu.sideMenuTabs.loans.click();
    //     loanId = await adminPageManager.loans.getLoanId(totalAmountOnSite);
    //     const getLoanReceivables = await sendLMSRequest('get', `Loans/${loanId}`);
    //     loanReceivables = getLoanReceivables.data.loanReceivables;
    //     await changeExpectedDateOfReceivables(loanReceivables, 3, 4);
    //     await postReplaceLoanReceivables(loanId, loanReceivables);
    //     const amountOfLoanAndFirstInstallment = loanReceivables[0].expectedAmount + loanReceivables[1].expectedAmount;
    //     const amountOfSecondInstallment = loanReceivables[2].expectedAmount;
    //     const dateOfPaymentLoanAndFirstInstallment = await changeDateWithDays(loanReceivables[0].expectedDate, 4);
    //     const dateOfPaymentSecondInstallment = loanReceivables[2].expectedDate;
    //     const paymentForLoanAndFirstInstallment = await postAdminPayment(loanId, amountOfLoanAndFirstInstallment, dateOfPaymentLoanAndFirstInstallment, constants.loans.ownerId);
    //     const paymentForSecondInstallment = await postAdminPayment(loanId, amountOfSecondInstallment, dateOfPaymentSecondInstallment, constants.loans.ownerId);
    //     const getPenaltyInfo = await sendLMSRequest('patch', `PenaltyDetector/${loanId}`);
    //     const loanReceivablesWithPenalty = getPenaltyInfo.data.loanReceivables;
    //     await calculateOutstandingPrincipalAmount(loanReceivablesWithPenalty);
    //     amountForDay = ((outstandingPrincipalAmount - processingPaymentsAmount) * ((basisPoint * 12) + sofrAI30days/100)/daysInYear).toFixed(2);
    //     const penaltyReceivable = loanReceivablesWithPenalty.find(el => el.type === 'PenaltyInterestFee');
    //     await adminPageManager.loans.elements.companyName(totalAmountOnSite).click();
    //     await expect(adminPageManager.loansStatusDetails.elements.penaltyInterest, 'Penalty interest should be calculated').toBeVisible();
    //     await expect(adminPageManager.loansStatusDetails.elements.penaltyInterest, `Penalty ecpected amount should be equal ${amountForDay}`).toContainText(amountForDay);
    // });

    // test('Loan Fee and 1th Installment paid on time. 2th Installment is late for 2 calendar days, 3th Installment is late to 3 days. Penalty interest should not be calculated ', async ({adminPageManager}) => {
    //     test.slow();
    //     await adminPageManager.backOfficeSideMenu.sideMenuTabs.loans.click();
    //     loanId = await adminPageManager.loans.getLoanId(totalAmountOnSite);
    //     const getLoanReceivables = await sendLMSRequest('get', `Loans/${loanId}`);
    //     loanReceivables = getLoanReceivables.data.loanReceivables;
    //     await changeExpectedDateOfReceivables(loanReceivables, 3, 4);
    //     await postReplaceLoanReceivables(loanId, loanReceivables);
    //     const amountOfLoanAndFirstInstallment = loanReceivables[0].expectedAmount + loanReceivables[1].expectedAmount;
    //     const amountOfSecondInstallment = loanReceivables[2].expectedAmount;
    //     const dateOfPaymentLoanAndFirstInstallment = loanReceivables[0].expectedDate;
    //     const dateOfPaymentSecondInstallment = await changeDateWithDays(loanReceivables[2].expectedDate, 2);
    //     const paymentForLoanAndFirstInstallment = await postAdminPayment(loanId, amountOfLoanAndFirstInstallment, dateOfPaymentLoanAndFirstInstallment, constants.loans.ownerId);
    //     const paymentForSecondInstallment = await postAdminPayment(loanId, amountOfSecondInstallment, dateOfPaymentSecondInstallment, constants.loans.ownerId);
    //     const getPenaltyInfo = await sendLMSRequest('patch', `PenaltyDetector/${loanId}`);
    //     const loanReceivablesWithPenalty = getPenaltyInfo.data.loanReceivables;
    //     await adminPageManager.loans.elements.companyName(totalAmountOnSite).click();
    //     await expect(adminPageManager.loansStatusDetails.elements.penaltyInterest, 'Note should be visible in the notes schedule').toBeVisible({visible: false});
    // });

    // test('Loan Fee has been paid. 1th Installment has been paid partially on time, second part has paid in 3 days. 2th Installment has paid partially on time, second part has paid in 3 days. Penalty interest should be calculated ', async ({adminPageManager}) => {
    //     test.slow();
    //     await adminPageManager.backOfficeSideMenu.sideMenuTabs.loans.click();
    //     loanId = await adminPageManager.loans.getLoanId(totalAmountOnSite);
    //     const getLoanReceivables = await sendLMSRequest('get', `Loans/${loanId}`);
    //     loanReceivables = getLoanReceivables.data.loanReceivables;
    //     await changeExpectedDateOfReceivables(loanReceivables, 2, 4);
    //     await postReplaceLoanReceivables(loanId, loanReceivables);
    //     const amountOfLoanAndHalfOfFirstInstallment = await loanReceivables[0].expectedAmount + loanReceivables[1].expectedAmount - loanReceivables[1].expectedAmount / 2;
    //     const amountOfSecondHalfOfFirstInstallment = await loanReceivables[1].expectedAmount / 2;
    //     const amountOfHalfOfSecondInstallment = await loanReceivables[2].expectedAmount / 2;
    //     const dateOfPaymentLoanAndHalfOfFirstInstallment = await loanReceivables[0].expectedDate;
    //     const dateOfPaymentSecondHalfOfFirstInstallment = await changeDateWithDays(loanReceivables[0].expectedDate, 4);
    //     const dateOfPaymentFirstHalfOfSecondInstallment = loanReceivables[2].expectedDate;
    //     const dateOfPaymentSecondHalfOfSecondInstallment = await changeDateWithDays(loanReceivables[2].expectedDate, 4);
    //     const paymentForLoanAndHalfOfFirstInstallment = await postAdminPayment(loanId, amountOfLoanAndHalfOfFirstInstallment, dateOfPaymentLoanAndHalfOfFirstInstallment, constants.loans.ownerId);
    //     const paymentForSecondHalfOfFirstInstallment = await postAdminPayment(loanId, amountOfSecondHalfOfFirstInstallment, dateOfPaymentSecondHalfOfFirstInstallment, constants.loans.ownerId);
    //     const paymentForThirdInstallment = await postAdminPayment(loanId, amountOfHalfOfSecondInstallment, dateOfPaymentFirstHalfOfSecondInstallment, constants.loans.ownerId);
    //     const paymentForSecondHalfOfSecondInstallment = await postAdminPayment(loanId, amountOfHalfOfSecondInstallment, dateOfPaymentSecondHalfOfSecondInstallment, constants.loans.ownerId);
    //     const getPenaltyInfo = await sendLMSRequest('patch', `PenaltyDetector/${loanId}`);
    //     const loanReceivablesWithPenalty = getPenaltyInfo.data.loanReceivables;
    //     await calculateOutstandingPrincipalAmount(loanReceivablesWithPenalty);
    //     amountForDay = ((outstandingPrincipalAmount - processingPaymentsAmount) * ((basisPoint * 12) + sofrAI30days/100)/daysInYear).toFixed(2);
    //     const penaltyReceivable = loanReceivablesWithPenalty.find(el => el.type === 'PenaltyInterestFee');
    //     await adminPageManager.loans.elements.companyName(totalAmountOnSite).click();
    //     await expect(adminPageManager.loansStatusDetails.elements.penaltyInterest, 'Penalty interest should be calculated').toBeVisible();
    //     await expect(adminPageManager.loansStatusDetails.elements.penaltyInterest, `Penalty ecpected amount should be equal ${amountForDay}`).toContainText(amountForDay);
    // });

    // test('Loan has paid partially. 1th Installment has paid partially on time, second part has paid in 3 days. 2th Installment has paid partially on time, second part has paid in 3 days. Penalty interest should be calculated ', async ({adminPageManager}) => {
    //     test.slow();
    //     await adminPageManager.backOfficeSideMenu.sideMenuTabs.loans.click();
    //     loanId = await adminPageManager.loans.getLoanId(totalAmountOnSite);
    //     const getLoanReceivables = await sendLMSRequest('get', `Loans/${loanId}`);
    //     loanReceivables = getLoanReceivables.data.loanReceivables;
    //     await changeExpectedDateOfReceivables(loanReceivables, 5, 4);
    //     await postReplaceLoanReceivables(loanId, loanReceivables);
    //     const amountOfHalfOfLoanFee = loanReceivables[0].expectedAmount / 2;
    //     const amountOfSecondHalfOfLoanFeeAndFirstInstallment = amountOfHalfOfLoanFee + loanReceivables[1].expectedAmount;
    //     const amountOfHalfOfSecondInstallment = loanReceivables[2].expectedAmount;
    //     const amountOfHalfOfThirdInstallment = loanReceivables[3].expectedAmount;
    //     const amountOfHalfOfFourthInstallment = loanReceivables[4].expectedAmount;
    //     const amountOfHalfOfFifthInstallment = loanReceivables[5].expectedAmount;
    //     const dateOfPaymentHalfOfLoan = loanReceivables[0].expectedDate;
    //     const dateOfPaymentHalfOfLoanAndFirstInstallment = await changeDateWithDays(loanReceivables[1].expectedDate, 4);
    //     const dateOfPaymentOfSecondInstallment = loanReceivables[2].expectedDate;
    //     const dateOfPaymentOfThirdInstallment = loanReceivables[3].expectedDate;
    //     const dateOfPaymentOfFourthInstallment = loanReceivables[4].expectedDate;
    //     const dateOfPaymentOfFifthInstallment = await changeDateWithDays(loanReceivables[5].expectedDate, 4);
    //     const paymentForHalfOfLoan = await postAdminPayment(loanId, amountOfHalfOfLoanFee, dateOfPaymentHalfOfLoan, constants.loans.ownerId);
    //     const paymentForHalfOfLoanAndFirstInstallment = await postAdminPayment(loanId, amountOfSecondHalfOfLoanFeeAndFirstInstallment, dateOfPaymentHalfOfLoanAndFirstInstallment, constants.loans.ownerId);
    //     const paymentForSecondInstallment = await postAdminPayment(loanId, amountOfHalfOfSecondInstallment, dateOfPaymentOfSecondInstallment, constants.loans.ownerId);
    //     const paymentForThirdInstallment = await postAdminPayment(loanId, amountOfHalfOfThirdInstallment, dateOfPaymentOfThirdInstallment, constants.loans.ownerId);
    //     const paymentForFourthInstallment = await postAdminPayment(loanId, amountOfHalfOfFourthInstallment, dateOfPaymentOfFourthInstallment, constants.loans.ownerId);
    //     const paymentForFifthInstallment = await postAdminPayment(loanId, amountOfHalfOfFifthInstallment, dateOfPaymentOfFifthInstallment, constants.loans.ownerId);
    //     const getPenaltyInfo = await sendLMSRequest('patch', `PenaltyDetector/${loanId}`);
    //     const loanReceivablesWithPenalty = getPenaltyInfo.data.loanReceivables;
    //     await calculateOutstandingPrincipalAmount(loanReceivablesWithPenalty);
    //     amountForDay = ((outstandingPrincipalAmount - processingPaymentsAmount) * ((basisPoint * 12) + sofrAI30days/100)/daysInYear).toFixed(2);
    //     const penaltyReceivable = loanReceivablesWithPenalty.find(el => el.type === 'PenaltyInterestFee');
    //     await adminPageManager.loans.elements.companyName(totalAmountOnSite).click();
    //     await expect(adminPageManager.loansStatusDetails.elements.penaltyInterest, 'Penalty interest should be calculated').toBeVisible();
    //     await expect(adminPageManager.loansStatusDetails.elements.penaltyInterest, `Penalty ecpected amount should be equal ${amountForDay}`).toContainText(amountForDay);
    // });

    // test('Loan Fee is late to 4 calendar days and paid. Penalty interest should not be calculated ', async ({adminPageManager}) => {
    //     test.slow();
    //     await adminPageManager.backOfficeSideMenu.sideMenuTabs.loans.click();
    //     loanId = await adminPageManager.loans.getLoanId(totalAmountOnSite);
    //     const getLoanReceivables = await sendLMSRequest('get', `Loans/${loanId}`);
    //     loanReceivables = getLoanReceivables.data.loanReceivables;
    //     await changeExpectedDateOfReceivables(loanReceivables, 0, 4);
    //     await postReplaceLoanReceivables(loanId, loanReceivables);
    //     const amountOfLoanFee = loanReceivables[0].expectedAmount;
    //     const dateOfPaymentLoanFee = await changeDateWithDays(loanReceivables[0].expectedDate, 4);
    //     const paymentForLoanFee = await postAdminPayment(loanId, amountOfLoanFee, dateOfPaymentLoanFee, constants.loans.ownerId);
    //     const getPenaltyInfo = await sendLMSRequest('patch', `PenaltyDetector/${loanId}`);
    //     const loanReceivablesWithPenalty = getPenaltyInfo.data.loanReceivables;
    //     await calculateOutstandingPrincipalAmount(loanReceivablesWithPenalty);
    //     amountForDay = ((outstandingPrincipalAmount - processingPaymentsAmount) * ((basisPoint * 12) + sofrAI30days/100)/daysInYear).toFixed(2);
    //     const penaltyReceivable = loanReceivablesWithPenalty.find(el => el.type === 'PenaltyInterestFee');
    //     await adminPageManager.loans.elements.companyName(totalAmountOnSite).click();
    //     await expect(adminPageManager.loansStatusDetails.elements.penaltyInterest, 'Penalty interest should not be calculated').toBeVisible({visible:false});
    // });

    // test('Loan Fee and 1th Installment are late to 7 calendar days and paid. Run over due detector. Penalty interest should be calculated. Outstanding Principal Amount should be calculate without Late Fee ', async ({adminPageManager}) => {
    //     test.slow();
    //     await adminPageManager.backOfficeSideMenu.sideMenuTabs.loans.click();
    //     loanId = await adminPageManager.loans.getLoanId(totalAmountOnSite);
    //     const getLoanReceivables = await sendLMSRequest('get', `Loans/${loanId}`);
    //     loanReceivables = getLoanReceivables.data.loanReceivables;
    //     await changeExpectedDateOfReceivables(loanReceivables, 0, 8);
    //     await postReplaceLoanReceivables(loanId, loanReceivables);
    //     const overDueResponse = await sendLMSRequest('patch', `OverDueDetector`);
    //     const getPenaltyInfo = await sendLMSRequest('patch', `PenaltyDetector/${loanId}`);
    //     const loanReceivablesWithPenalty = getPenaltyInfo.data.loanReceivables;
    //     await calculateOutstandingPrincipalAmount(loanReceivablesWithPenalty);
    //     amountForDay = ((outstandingPrincipalAmount - processingPaymentsAmount) * ((basisPoint * 12) + sofrAI30days/100)/daysInYear).toFixed(2);
    //     const penaltyReceivable = loanReceivablesWithPenalty.find(el => el.type === 'PenaltyInterestFee');
    //     await adminPageManager.loans.elements.companyName(totalAmountOnSite).click();
    //     await expect(adminPageManager.loansStatusDetails.elements.penaltyInterest, 'Penalty interest should be calculated').toBeVisible();
    //     await expect(adminPageManager.loansStatusDetails.elements.penaltyInterest, `Penalty ecpected amount should be equal ${amountForDay}`).toContainText(amountForDay);
    // });

    // test('Loan Fee and 1th Installment are late to 7 calendar days and paid. Start over due detector, add late and extension fee manualy. Penalty interest should be calculated correctly ', async ({adminPageManager}) => {
    //     test.slow();
    //     await adminPageManager.backOfficeSideMenu.sideMenuTabs.loans.click();
    //     loanId = await adminPageManager.loans.getLoanId(totalAmountOnSite);
    //     const getLoanReceivables = await sendLMSRequest('get', `Loans/${loanId}`);
    //     loanReceivables = getLoanReceivables.data.loanReceivables;
    //     await changeExpectedDateOfReceivables(loanReceivables, 0, 8);
    //     await postReplaceLoanReceivables(loanId, loanReceivables);
    //     const overDueResponse = await sendLMSRequest('patch', `OverDueDetector`);
    //     await postAdminLatePaymentFee(loanId, constants.penaltyInterest.notEnoughAmountForCreatePenalty, currentDate, constants.loans.ownerId);
    //     await postAdminExtensionFee(loanId, constants.penaltyInterest.notEnoughAmountForCreatePenalty, currentDate, constants.loans.ownerId);
    //     const getPenaltyInfo = await sendLMSRequest('patch', `PenaltyDetector/${loanId}`);
    //     const loanReceivablesWithPenalty = getPenaltyInfo.data.loanReceivables;
    //     await calculateOutstandingPrincipalAmount(loanReceivablesWithPenalty);
    //     amountForDay = ((outstandingPrincipalAmount - processingPaymentsAmount) * ((basisPoint * 12) + sofrAI30days/100)/daysInYear).toFixed(2);
    //     console.log((outstandingPrincipalAmount - processingPaymentsAmount) * ((basisPoint * 12) + sofrAI30days/100)/daysInYear);
    //     const penaltyReceivable = loanReceivablesWithPenalty.find(el => el.type === 'PenaltyInterestFee');
    //     await adminPageManager.loans.elements.companyName(totalAmountOnSite).click();
    //     await expect(adminPageManager.loansStatusDetails.elements.penaltyInterest, 'Penalty interest should be calculated').toBeVisible();
    //     await expect(adminPageManager.loansStatusDetails.elements.penaltyInterest, `Penalty ecpected amount should be equal ${amountForDay}`).toContainText(amountForDay);
    // });
});
