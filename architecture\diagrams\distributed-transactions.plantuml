@startuml Distributed Transactions with orchestrator

title Distributed Transactions with orchestrator

queue "Messages" as pm #Orange
participant "Orchestrator" as or #LightCyan
participant "Service1" as s1 #LightGray
database "DB1" as s1db #LightGray
participant "Service2" as s2 #LightBlue
database "DB1" as s2db #LightBlue

autonumber

== First Operation ==
pm -> or : Reads message
or -> s1 : Invoke function
s1 --> s1 : Validate request
s1 -> s1db : Perform operation
s1 --> or : Operation successful
== Second Operation ==
or -> s2 : Invoke function
s2 --> s2 : Validate request
s2 --> or : **Invalid request / operation**
== Rollback First Operation ==
or -> s1 : Invoke reversal function
s1 -> s1db : Cancel operation
s1 --> or : Rollback successful

@enduml