using BlueTape.InvoiceService.Common.Enums;

namespace BlueTape.BackOffice.DecisionEngine.Api.Models.Responses.DrawApprovals.Details;

public class DrawPayablesDetailsResponse
{
    public string Number { get; set; } = string.Empty;
    public DateOnly? DateOfUpload { get; set; }
    public DateOnly Date { get; set; }
    public DateOnly DueDate { get; set; }
    public string Status { get; set; } = string.Empty;
    public decimal MaterialSubTotal { get; set; }
    public decimal TaxAmount { get; set; }
    public decimal TotalAmount { get; set; }
    public string Url { get; set; } = string.Empty;
    public InvoiceType Type { get; set; }
    public PayablesSupplierDetailsResponse SupplierDetails { get; set; } = new();
    /// <summary>
    /// The shipping address of the invoice
    /// </summary>
    /// <example>123 Main St, Springfield, IL 62701</example>
    public string? ShippingAddress { get; set; }
    /// <summary>
    /// The attention to field of the invoice
    /// </summary>
    /// <example><PERSON></example>
    public string? Attention { get; set; }
}
