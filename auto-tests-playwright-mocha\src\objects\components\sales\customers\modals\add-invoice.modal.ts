import {BasePage} from '../../../../base.page';

export class AddInvoiceModal extends BasePage {
    constructor(page){
        super(page);
    };

    inputFields = {
        invoiceNumber: this.page.locator('//*[@data-testid="Invoice #"]//input'),
        subtotal: this.page.locator('[placeholder="Subtotal"]'),
        taxAmount: this.page.locator('[placeholder="Tax Amount"]'),
    };

    buttons = {
        saveSend: this.page.getByRole('button', { name: 'Save & Send' }),
        saveDraft: this.page.locator('[data-testid="save_draft_button"]'),
        cancel: this.page.locator('[data-testid="invoice_cancel_btn"]'),
    };

    radioButtons = {
        delivierTo: this.page.locator('[data-testid="deliver_to_item"]'),
        pickUp: this.page.locator('[data-testid="pick_up_item"]'),
        service: this.page.locator('[data-testid="service_item"]'),
        textInvoice: this.page.locator('[data-testid="check_box_item_text"]'),
        emailInvoice: this.page.locator('[data-testid="check_box_item_email"]'),
        both: this.page.locator('[data-testid="check_box_item_both"]'),
    };

    async fillUpAddInvoice(invoiceNumber, subtotal, taxAmount){
        await this.inputFields.invoiceNumber.fill(invoiceNumber);
        await this.inputFields.subtotal.fill(subtotal);
        await this.inputFields.taxAmount.fill(taxAmount);
        await this.radioButtons.service.click();
        await this.radioButtons.emailInvoice.click();
        await this.buttons.saveSend.click();
        await this.page.waitForLoadState('networkidle')
    };
}