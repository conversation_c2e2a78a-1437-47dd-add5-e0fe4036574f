﻿namespace BlueTape.Aion.DataAccess.External.Constants;

public static class ClientConstants
{
    public const string AionAuthHeaderName = "AionAuth";
    public const string AionAuthHttpClientName = "AionAuthHttpClient";
    public const string AionHttpClientName = "AionHttpClient";

    public const string AionUserId = "AION-USER-ID";
    public const string AionPassword = "AION-PASSWORD";
    public const string AionExternalApiKey = "AION-EXTERNAL-API-KEY";
    public const string PaymentSubscriptionTypeHeader = "X-PAYMENT-SUBSCRIPTION-TYPE";
}