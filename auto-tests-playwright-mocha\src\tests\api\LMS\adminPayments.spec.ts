// import { expect } from '@playwright/test';
// import { test, BaseTest } from '../../test-utils';
// import sendLMSRequest from '../../../api/common/lms-send-request';
// const constants = JSON.parse(JSON.stringify(require('../../../constants/LMStestData.json')));
// //const currentDate = BaseTest.getCurrentDate();
// import axios from "axios";

// test.describe(`LMS. Admin Payments. API Test. @lms`, async () => {
//   let paymentAmount = 100;
//   let loanReceivables;
//   let loanId;
//   test.beforeAll(async () => {
//     const requestBody = {
//       "companyId": `${constants.loans[0].ownerId}`,
//       "amount": 1100,
//       "loanTemplateId": `${constants.loanTemplates[0].loanTemplateId}`
//     };
//     const response = await sendLMSRequest('post', 'Loans', requestBody);
//     loanId = response.data.id;
//     expect(response.status).toEqual(200);

//     const loanReceivableResponse = await sendLMSRequest('get', `Admin/Loans/${loanId}/LoanReceivables`);
//     loanReceivables = loanReceivableResponse.data;
//     expect(response.status).toEqual(200);
//   });

//   test.afterAll(async () => {
//     // const response = await sendLMSRequest('delete', `Loans/${loanId}`);
//     const response = await axios.delete(`${process.env.LMS_BASE_URL}/Loans/${loanId}`, {
//       headers: {
//         accept: '*/*'
//       }
//     });
//     expect(response.status).toEqual(200);
//   });

//   test(`Create manual payment.  @lms`, async () => {
//     const requestBody = {
//       "loanId": `${loanId}`,
//       "amount": paymentAmount,
//       "date": `${currentDate}`,
//       "note": "AutoTest"
//     };

//     const response = await axios.post(`${process.env.LMS_BASE_URL}/Admin/Payments`, requestBody, {
//       headers: {
//         accept: '*/*',
//         userId: `${constants.loans[0].ownerId}`, // optional parameter
//         'Content-Type': 'application/json-patch+json'
//       }
//     });

//     expect(response.status).toEqual(200);
//     expect(response.statusText).toContain('OK')
//     expect(response.config.data).toEqual(JSON.stringify(requestBody));
//   });

//   test(`Create refund.  @lms`, async () => {
//     const requestBody = {
//       "loanId": `${loanId}`,
//       "amount": paymentAmount,
//       "date": `${currentDate}`,
//       "note": "AutoTest"
//     };

//     const response = await axios.post(`${process.env.LMS_BASE_URL}/Admin/Refunds`, requestBody, {
//       headers: {
//         accept: 'text/plain',
//         userId: `${constants.loans[0].ownerId}`,
//         'Content-Type': 'application/json-patch+json'
//       }
//     });

//     expect(response.status).toEqual(200);
//     expect(response.statusText).toContain('OK');
//     expect(response.config.data).toEqual(JSON.stringify(requestBody));
//   });

//   test(`Create manual late payment fee. @lms`, async () => {
//     const requestBody = {
//       "expectedDate": `${currentDate}`,
//       "expectedAmount": 30,
//       "loanId": `${constants.loans[0].id}`,
//       "note": "AutoTest"
//     };

//     const response = await axios.post(`${process.env.LMS_BASE_URL}/Admin/LatePaymentFee`, requestBody, {
//       headers: {
//         accept: '*/*',
//         userId: `${constants.loans[0].ownerId}`,
//         'Content-Type': 'application/json-patch+json'
//       }
//     });

//     expect(response.status).toEqual(200);
//     expect(response.statusText).toContain('OK');
//     expect(response.config.data).toEqual(JSON.stringify(requestBody));
//   });

//   test(`Change installment expected date. @lms`, async () => {
//     const requestBody = {
//       "date": `${currentDate}`,
//       "note": "AutoTest"
//     };

//     const response = await axios.patch(`${process.env.LMS_BASE_URL}/Admin/LoanReceivables/${loanReceivables[0].id}`, requestBody, {
//       headers: {
//         accept: 'text/plain',
//         userId: `${constants.loans[0].ownerId}`,
//         'Content-Type': 'application/json-patch+json'
//       }
//     });
  
//     expect(response.status).toEqual(200);
//     expect(response.statusText).toContain('OK');
//     expect(response.config.data).toEqual(JSON.stringify(requestBody));
//   });

//   test(`Cancel payment. @lms`, async () => {
//     const requestBody = {
//       "status": "None",
//       "note": "AutoTest"
//     };

//     const response = await axios.patch(`${process.env.LMS_BASE_URL}/Admin/Payments/${loanReceivables[0].id}`, requestBody, {
//       headers: {
//         accept: '*/*',
//         userId: `${constants.loans[0].ownerId}`,
//         'Content-Type': 'application/json-patch+json'
//       }
//     });

//     expect(response.status).toEqual(200);
//     expect(response.statusText).toContain('OK');
//     expect(response.config.data).toEqual(JSON.stringify(requestBody));
//   });
// });