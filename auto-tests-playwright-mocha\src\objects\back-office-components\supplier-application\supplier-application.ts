import {BasePage} from '../../base.page';

export class SupplierApplication extends BasePage {
    constructor(page){
        super(page);
    };

    containers = {
        supplierApplicationTableContainer: this.page.locator('.table'),
    };

    buttons = {
        edit: this.page.locator('"Edit"'),
        cardPackage: this.page.locator(''),
    };

    loanPackages = {
        packageA: this.containers.supplierApplicationTableContainer.locator('"Package A" >> nth=0'), // TODO: find container
    };

    cardPackages = {
        optOut: this.containers.supplierApplicationTableContainer.locator('"Opt Out" >> nth=0'), // TODO: find container
    };

    async clickOnSupplierApplication(userEmail){
        await this.containers.supplierApplicationTableContainer.locator(`//*[text()="${userEmail}"]`).click();
    };

    async selectCardLoanPackages(userEmail){ // TODO: FULLY REFACTOR
        await this.containers.supplierApplicationTableContainer.locator(`//*[text()="${userEmail}"]/parent::*//button[@class="dropdown-toggle btn btn-warning btn-md"] >> nth=0`).click(); // TODO: create locators
        await this.cardPackages.optOut.click();
        await this.page.waitForSelector(`//*[text()="${userEmail}"]/parent::*//button[@class="dropdown-toggle btn btn-info btn-md"]`);
        await this.containers.supplierApplicationTableContainer.locator(`//*[text()="${userEmail}"]/parent::*//button[text()="not selected"]`).click(); // TODO: create locators
        await this.loanPackages.packageA.click();
        await this.page.waitForSelector(`//*[text()="${userEmail}"]/parent::*//button[text()="Package A"]`);
    };

    async waitForApplicationStatusToUpdate(userEmail){
        await this.page.waitForSelector(`.table >> "${userEmail}"`, { state: 'hidden' });
    };
}