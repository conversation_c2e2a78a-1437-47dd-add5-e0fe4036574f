using AutoMapper;
using BlueTape.BackOffice.DecisionEngine.Application.Extensions;
using BlueTape.BackOffice.DecisionEngine.Application.Mappings;
using BlueTape.BackOffice.DecisionEngine.Application.Services.DrawApprovals;
using BlueTape.BackOffice.DecisionEngine.Application.Services.Node;
using BlueTape.BackOffice.DecisionEngine.Application.Tests.Models;
using BlueTape.BackOffice.DecisionEngine.DataAccess.External.CompanyApi;
using BlueTape.BackOffice.DecisionEngine.DataAccess.External.InvoiceApi;
using BlueTape.BackOffice.DecisionEngine.DataAccess.External.LoanApi.Proxy;
using BlueTape.BackOffice.DecisionEngine.DataAccess.External.OnboardingApi;
using BlueTape.CompanyService.Companies;
using BlueTape.Firebase.Abstractions;
using BlueTape.InvoiceService.Common.Enums;
using BlueTape.InvoiceService.Models.Invoice;
using BlueTape.OBS.DTOs;
using BlueTape.OBS.DTOs.Draft;
using BlueTape.OBS.DTOs.DrawApproval.Queries;
using BlueTape.OBS.DTOs.DrawApproval.Responses;
using BlueTape.OBS.DTOs.Payables;
using BlueTape.Utilities.Providers;
using NSubstitute;
using Shouldly;
using System.Diagnostics.CodeAnalysis;
using System.Text;

namespace BlueTape.BackOffice.DecisionEngine.Application.Tests.Services;

[SuppressMessage("ReSharper", "InconsistentNaming")]
public class DrawApprovalServiceTests_Details_PayablesDetails_Tests
{
    private readonly DrawApprovalsService _drawApprovalsService;

    private readonly IOnboardingApiProxy _onBoardingApi = Substitute.For<IOnboardingApiProxy>();
    private readonly IInvoiceApiProxy _invoiceApi = Substitute.For<IInvoiceApiProxy>();
    private readonly ICompanyApiProxy _companyApi = Substitute.For<ICompanyApiProxy>();
    private readonly IDateProvider _dateProvider = Substitute.For<IDateProvider>();
    private readonly IFirebaseClient _firebaseClient = Substitute.For<IFirebaseClient>();
    private readonly ILoanApiProxy _loanExternalService = Substitute.For<ILoanApiProxy>();
    private readonly INodeService _nodeService = Substitute.For<INodeService>();

    public DrawApprovalServiceTests_Details_PayablesDetails_Tests()
    {
        var mapperConfig = new MapperConfiguration(c => c.AddProfile(new ApplicationProfile()));
        var mapper = mapperConfig.CreateMapper();

        _drawApprovalsService = new DrawApprovalsService(mapper, _onBoardingApi, _invoiceApi,
            _companyApi, _dateProvider, _firebaseClient, _loanExternalService, _nodeService);
    }

    [Fact]
    public async Task GetDrawApprovalDetails_PayablesExists_ReturnsCorrectDetailsResponse()
    {
        var payablesIds = new[] { "id1", "id2" };
        var executionId = "executionId";
        var userIds = new[] { Convert.ToBase64String("x-user-id"u8.ToArray()) };
        var id = Guid.NewGuid().ToString();
        var invoice = new InvoiceModel()
        {
            Id = nameof(InvoiceModel.Id),
            InvoiceDueDate = DateTimeOffset.UtcNow,
            InvoiceNumber = nameof(InvoiceModel.InvoiceNumber),
            CompanyId = nameof(InvoiceModel.CompanyId),
            Document = new InvoiceDocumentModel()
            {
                Url = nameof(InvoiceModel.Document.Url),
            },
            MaterialSubtotal = 1000,
            TaxAmount = 6.42m,
            TotalAmount = 1006.42m,
            CreatedAt = "02/02/2024 00:00:00",
            InvoiceDate = DateTimeOffset.UtcNow,
            Status = nameof(InvoiceModel.Status),
            Type = InvoiceType.Invoice,
            Address = "123 Main St, Springfield, IL 62701",
            Attention = "John Doe",
        };

        var drawApprovalDto = new DrawApprovalDto()
        {
            Id = id,
            LastStatusChangedBy = userIds[0],
            ExecutionId = executionId,
            Payables = new List<PayableItemDto>()
            {
                new()
                {
                    Id = payablesIds[0],
                },
                new()
                {
                    Id = payablesIds[1],
                }
            }
        };

        _firebaseClient.GetUsers(Arg.Is<IEnumerable<string>>(x => x.SequenceEqual(userIds)), default).Returns([new UserInfo { DisplayName = "x-user-name" }]);
        _onBoardingApi.GetDrawApproval(id, default).Returns(drawApprovalDto);
        _invoiceApi.GetByIds(Arg.Is<string[]>(x => x.SequenceEqual(payablesIds)), default).Returns(new List<InvoiceModel>() { invoice });
        _companyApi.GetCompanyById(invoice.CompanyId, default).Returns(new CompanyModel());
        _onBoardingApi.GetDrawApprovalList(Arg.Any<GetDrawApprovalsQueryWithPagination>(), default).Returns(new GetQueryWithPaginationResultDto<DrawApprovalDto>());

        var result = await _drawApprovalsService.GetDrawApprovalDetails(drawApprovalDto.Id, default);

        result.LastStatusChangedBy.ShouldBe("x-user-name");
        result.PayablesDetails.First().Status.ShouldBe(invoice.Status);
        result.PayablesDetails.First().Date.ShouldBe(invoice.InvoiceDate.DateTime.ToCstDate());
        result.PayablesDetails.First().DateOfUpload.ShouldBe(invoice.CreatedAt.ToDateOnly());
        result.PayablesDetails.First().DueDate.ShouldBe(invoice.InvoiceDueDate.DateTime.ToCstDate());
        result.PayablesDetails.First().MaterialSubTotal.ShouldBe(invoice.MaterialSubtotal);
        result.PayablesDetails.First().Number.ShouldBe(invoice.InvoiceNumber);
        result.PayablesDetails.First().TaxAmount.ShouldBe(invoice.TaxAmount);
        result.PayablesDetails.First().TotalAmount.ShouldBe(invoice.TotalAmount);
        result.PayablesDetails.First().Url.ShouldBe(invoice.Document.Url);
        result.PayablesDetails.First().Type.ShouldBe(invoice.Type);
        result.PayablesDetails.First().ShippingAddress.ShouldBe(invoice.Address);
        result.PayablesDetails.First().Attention.ShouldBe(invoice.Attention);
    }

    [Fact]
    public async Task GetDrawApprovalDetails_PayablesDoNotExists_ReturnsCorrectDetailsResponse()
    {
        var payablesIds = Array.Empty<string>();
        var id = Guid.NewGuid().ToString();

        var drawApprovalDto = new DrawApprovalDto()
        {
            Id = id
        };

        _onBoardingApi.GetDrawApprovalList(Arg.Any<GetDrawApprovalsQueryWithPagination>(), default).Returns(new GetQueryWithPaginationResultDto<DrawApprovalDto>());
        _onBoardingApi.GetDrawApproval(id, default).Returns(drawApprovalDto);
        _invoiceApi.GetByIds(Arg.Is<string[]>(x => x.SequenceEqual(payablesIds)), default).Returns(new List<InvoiceModel>());

        var result = await _drawApprovalsService.GetDrawApprovalDetails(drawApprovalDto.Id, default);

        result.PayablesDetails.ShouldBeEmpty();
        await _invoiceApi.Received(1).GetByIds(Arg.Is<string[]>(x => x.SequenceEqual(payablesIds)), default);
        await _companyApi.Received(0).GetCompanyById(string.Empty, default);
    }


    [Fact]
    public async Task GetDrawApprovalDetails_PayablesExists_ReturnsCorrectSupplierDetailsResponse()
    {
        var payablesIds = new[] { "id1" };
        var id = Guid.NewGuid().ToString();
        var invoice = new InvoiceModel() { CompanyId = nameof(InvoiceModel.CompanyId) };
        var email = "email";
        var phone = "phone";
        var company = new CompanyModel()
        {
            Id = nameof(CompanyModel.Id),
            Name = nameof(CompanyModel.Name),
            Email = nameof(CompanyModel.Phone),
            Phone = nameof(CompanyModel.Email),
        };
        var draft = new DraftDto
        {
            CompanyId = null,
            Data = new DataDto
            {
                BusinessOwner = new BusinessOwnerDto
                {
                    Items = new[]
                    {
                        new ItemDto
                        {
                            Identifier = "email",
                            Content = email
                        },
                        new ItemDto
                        {
                            Identifier = "phone",
                            Content = phone
                        }
                    }
                },
            },
        };
        var drawApprovalDto = new DrawApprovalDto()
        {
            Id = id,
            MerchantId = nameof(DrawApprovalDto.MerchantId),
            Payables = new List<PayableItemDto>()
            {
                new()
                {
                    Id = payablesIds[0],
                },
            }
        };

        _onBoardingApi.GetDrawApprovalList(Arg.Any<GetDrawApprovalsQueryWithPagination>(), default).Returns(new GetQueryWithPaginationResultDto<DrawApprovalDto>());
        _onBoardingApi.GetDrawApproval(id, default).Returns(drawApprovalDto);
        _invoiceApi.GetByIds(Arg.Is<string[]>(x => x.SequenceEqual(payablesIds)), default).Returns(new List<InvoiceModel>() { invoice });
        _companyApi.GetCompanyById(invoice.CompanyId, default).Returns(company);
        _onBoardingApi.GetDraftsListByQuery(null, drawApprovalDto.MerchantId, null, default).Returns([draft]);
        var result = await _drawApprovalsService.GetDrawApprovalDetails(drawApprovalDto.Id, default);

        result.PayablesDetails.First().SupplierDetails.Id.ShouldBe(company.Id);
        result.PayablesDetails.First().SupplierDetails.Email.ShouldBe(email);
        result.PayablesDetails.First().SupplierDetails.Name.ShouldBe(company.Name);
        result.PayablesDetails.First().SupplierDetails.Phone.ShouldBe(phone);
    }

    [Fact]
    public async Task GetDrawApprovalDetails_PayablesExists_ExternalCallsArePerformed()
    {
        var payablesIds = new[] { "id1" };
        var id = Guid.NewGuid().ToString();
        var invoice = new InvoiceModel() { CompanyId = nameof(InvoiceModel.CompanyId) };
        var company = new CompanyModel() { };
        var userIds = new[] { Convert.ToBase64String(Encoding.UTF8.GetBytes("x-user-id")) };

        var drawApprovalDto = new DrawApprovalDto()
        {
            Id = id,
            LastStatusChangedBy = userIds[0],
            Payables = new List<PayableItemDto>()
            {
                new()
                {
                    Id = payablesIds[0],
                },
            }
        };

        _firebaseClient.GetUsers(Arg.Is<IEnumerable<string>>(x => x.SequenceEqual(userIds)), default).Returns([new UserInfo { DisplayName = "x-user-name" }]);
        _onBoardingApi.GetDrawApproval(id, default).Returns(drawApprovalDto);
        _invoiceApi.GetByIds(Arg.Is<string[]>(x => x.SequenceEqual(payablesIds)), default).Returns(new List<InvoiceModel>() { invoice });
        _companyApi.GetCompanyById(invoice.CompanyId, default).Returns(company);
        _onBoardingApi.GetDrawApprovalList(Arg.Any<GetDrawApprovalsQueryWithPagination>(), default).Returns(new GetQueryWithPaginationResultDto<DrawApprovalDto>());

        await _drawApprovalsService.GetDrawApprovalDetails(drawApprovalDto.Id, default);

        await _onBoardingApi.Received(1).GetDrawApproval(id, default);
        await _invoiceApi.Received(1).GetByIds(Arg.Is<string[]>(x => x.SequenceEqual(payablesIds)), default);
        await _companyApi.Received(1).GetCompanyById(invoice.CompanyId, default);
        await _firebaseClient.Received(1).GetUsers(Arg.Is<IEnumerable<string>>(x => x.SequenceEqual(userIds)), Arg.Any<CancellationToken>());
    }

    [Fact]
    public async Task GetDrawApprovalDetails_PayablesExistsDraftNotFound_ReturnsCorrectSupplierDetailsResponse()
    {
        var payablesIds = new[] { "id1" };
        var id = Guid.NewGuid().ToString();
        var invoice = new InvoiceModel() { CompanyId = nameof(InvoiceModel.CompanyId) };
        var company = new CompanyModel()
        {
            Id = nameof(CompanyModel.Id),
            Name = nameof(CompanyModel.Name),
            Email = nameof(CompanyModel.Phone),
            Phone = nameof(CompanyModel.Email),
        };
        var drawApprovalDto = new DrawApprovalDto()
        {
            Id = id,
            MerchantId = nameof(DrawApprovalDto.MerchantId),
            Payables = new List<PayableItemDto>()
            {
                new()
                {
                    Id = payablesIds[0],
                },
            }
        };
        _onBoardingApi.GetDrawApprovalList(Arg.Any<GetDrawApprovalsQueryWithPagination>(), default).Returns(new GetQueryWithPaginationResultDto<DrawApprovalDto>());
        _onBoardingApi.GetDrawApproval(id, default).Returns(drawApprovalDto);
        _invoiceApi.GetByIds(Arg.Is<string[]>(x => x.SequenceEqual(payablesIds)), default).Returns(new List<InvoiceModel>() { invoice });
        _companyApi.GetCompanyById(invoice.CompanyId, default).Returns(company);
        _onBoardingApi.GetDraftsListByQuery(null, drawApprovalDto.MerchantId, null, default).Returns(Enumerable.Empty<DraftDto>());
        var result = await _drawApprovalsService.GetDrawApprovalDetails(drawApprovalDto.Id, default);

        result.PayablesDetails.First().SupplierDetails.Id.ShouldBe(company.Id);
        result.PayablesDetails.First().SupplierDetails.Email.ShouldBe(string.Empty);
        result.PayablesDetails.First().SupplierDetails.Name.ShouldBe(company.Name);
        result.PayablesDetails.First().SupplierDetails.Phone.ShouldBe(string.Empty);
    }

    [Fact]
    public async Task GetDrawApprovalDetails_PayablesWithShippingAddress_ReturnsCorrectShippingAddressAndAttention()
    {
        var payablesIds = new[] { "id1" };
        var id = Guid.NewGuid().ToString();
        var shippingAddress = "456 Oak Street, Chicago, IL 60601";
        var attention = "Jane Smith";

        var invoice = new InvoiceModel()
        {
            Id = nameof(InvoiceModel.Id),
            InvoiceDueDate = DateTimeOffset.UtcNow,
            InvoiceNumber = nameof(InvoiceModel.InvoiceNumber),
            CompanyId = nameof(InvoiceModel.CompanyId),
            Document = new InvoiceDocumentModel()
            {
                Url = nameof(InvoiceModel.Document.Url),
            },
            MaterialSubtotal = 1000,
            TaxAmount = 6.42m,
            TotalAmount = 1006.42m,
            CreatedAt = "02/02/2024 00:00:00",
            InvoiceDate = DateTimeOffset.UtcNow,
            Status = nameof(InvoiceModel.Status),
            Type = InvoiceType.Invoice,
            Address = shippingAddress,
            Attention = attention,
        };

        var drawApprovalDto = new DrawApprovalDto()
        {
            Id = id,
            Payables = new List<PayableItemDto>()
            {
                new()
                {
                    Id = payablesIds[0],
                },
            }
        };

        _onBoardingApi.GetDrawApprovalList(Arg.Any<GetDrawApprovalsQueryWithPagination>(), default).Returns(new GetQueryWithPaginationResultDto<DrawApprovalDto>());
        _onBoardingApi.GetDrawApproval(id, default).Returns(drawApprovalDto);
        _invoiceApi.GetByIds(Arg.Is<string[]>(x => x.SequenceEqual(payablesIds)), default).Returns(new List<InvoiceModel>() { invoice });
        _companyApi.GetCompanyById(invoice.CompanyId, default).Returns(new CompanyModel());

        var result = await _drawApprovalsService.GetDrawApprovalDetails(drawApprovalDto.Id, default);

        result.PayablesDetails.First().ShippingAddress.ShouldBe(shippingAddress);
        result.PayablesDetails.First().Attention.ShouldBe(attention);
    }

    [Fact]
    public async Task GetDrawApprovalDetails_PayablesWithNullShippingAddress_ReturnsNullShippingAddressAndAttention()
    {
        var payablesIds = new[] { "id1" };
        var id = Guid.NewGuid().ToString();

        var invoice = new InvoiceModel()
        {
            Id = nameof(InvoiceModel.Id),
            InvoiceDueDate = DateTimeOffset.UtcNow,
            InvoiceNumber = nameof(InvoiceModel.InvoiceNumber),
            CompanyId = nameof(InvoiceModel.CompanyId),
            Document = new InvoiceDocumentModel()
            {
                Url = nameof(InvoiceModel.Document.Url),
            },
            MaterialSubtotal = 1000,
            TaxAmount = 6.42m,
            TotalAmount = 1006.42m,
            CreatedAt = "02/02/2024 00:00:00",
            InvoiceDate = DateTimeOffset.UtcNow,
            Status = nameof(InvoiceModel.Status),
            Type = InvoiceType.Invoice,
        };

        var drawApprovalDto = new DrawApprovalDto()
        {
            Id = id,
            Payables = new List<PayableItemDto>()
            {
                new()
                {
                    Id = payablesIds[0],
                },
            }
        };

        _onBoardingApi.GetDrawApprovalList(Arg.Any<GetDrawApprovalsQueryWithPagination>(), default).Returns(new GetQueryWithPaginationResultDto<DrawApprovalDto>());
        _onBoardingApi.GetDrawApproval(id, default).Returns(drawApprovalDto);
        _invoiceApi.GetByIds(Arg.Is<string[]>(x => x.SequenceEqual(payablesIds)), default).Returns(new List<InvoiceModel>() { invoice });
        _companyApi.GetCompanyById(invoice.CompanyId, default).Returns(new CompanyModel());

        var result = await _drawApprovalsService.GetDrawApprovalDetails(drawApprovalDto.Id, default);

        result.PayablesDetails.First().ShippingAddress.ShouldBeNull();
        result.PayablesDetails.First().Attention.ShouldBeNull();
    }

    [Fact]
    public async Task GetDrawApprovalDetails_PayablesWithPartialShippingAddress_ReturnsCorrectValues()
    {
        var payablesIds = new[] { "id1" };
        var id = Guid.NewGuid().ToString();
        var shippingAddress = "789 Pine Avenue, Boston, MA 02101";

        var invoice = new InvoiceModel()
        {
            Id = nameof(InvoiceModel.Id),
            InvoiceDueDate = DateTimeOffset.UtcNow,
            InvoiceNumber = nameof(InvoiceModel.InvoiceNumber),
            CompanyId = nameof(InvoiceModel.CompanyId),
            Document = new InvoiceDocumentModel()
            {
                Url = nameof(InvoiceModel.Document.Url),
            },
            MaterialSubtotal = 1000,
            TaxAmount = 6.42m,
            TotalAmount = 1006.42m,
            CreatedAt = "02/02/2024 00:00:00",
            InvoiceDate = DateTimeOffset.UtcNow,
            Status = nameof(InvoiceModel.Status),
            Type = InvoiceType.Invoice,
            Address = shippingAddress,
        };

        var drawApprovalDto = new DrawApprovalDto()
        {
            Id = id,
            Payables = new List<PayableItemDto>()
            {
                new()
                {
                    Id = payablesIds[0],
                },
            }
        };

        _onBoardingApi.GetDrawApprovalList(Arg.Any<GetDrawApprovalsQueryWithPagination>(), default).Returns(new GetQueryWithPaginationResultDto<DrawApprovalDto>());
        _onBoardingApi.GetDrawApproval(id, default).Returns(drawApprovalDto);
        _invoiceApi.GetByIds(Arg.Is<string[]>(x => x.SequenceEqual(payablesIds)), default).Returns(new List<InvoiceModel>() { invoice });
        _companyApi.GetCompanyById(invoice.CompanyId, default).Returns(new CompanyModel());

        var result = await _drawApprovalsService.GetDrawApprovalDetails(drawApprovalDto.Id, default);

        result.PayablesDetails.First().ShippingAddress.ShouldBe(shippingAddress);
        result.PayablesDetails.First().Attention.ShouldBeNull();
    }
}
