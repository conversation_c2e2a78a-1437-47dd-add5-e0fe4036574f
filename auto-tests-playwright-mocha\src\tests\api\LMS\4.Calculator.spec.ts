import {expect} from '@playwright/test';
import {test} from '../../test-utils';
import {sendLMSRequest} from '../../../api/common/lms-send-request';


const constants = JSON.parse(JSON.stringify(require('../../../constants/LMStestData.json')));

test.describe(`Calculator API Tests @LMS @API`, async () => {

    let templateId: string;

    test.beforeAll(async () => {
        const requestBody = constants.loanTemplates.TemplateWithTwoInstallments;

        const response = await sendLMSRequest('post', 'LoanTemplates', requestBody);

        templateId = response.data.id;
    });

    test.afterAll('Delete loan template after test by id', async () => {
        await sendLMSRequest('delete', `LoanTemplates/${templateId}`);
    });

    test(`Calculate loan fee amount and installments by template Id.`, async () => {
        const response = await sendLMSRequest('get', `Calculator?Amount=1000&LoanTemplateId=${templateId}`);

        expect(response.status, `Status code 200`)
            .toEqual(200);
    });

    test(`Loan Receivables Number is equal "Installments Number + 1"`, async () => {
        const response = await sendLMSRequest('get', `Calculator?Amount=1000&LoanTemplateId=${templateId}`);

        expect(response.data.loanReceivablesNumber, `Loan Receivables Number is equal to Installments Number + 1`)
            .toEqual(constants.loanTemplates.TemplateWithTwoInstallments.InstallmentsNumber + 1);
    });

    test(`Check the first Expected Date of Loan Receivable'`, async () => {
        const expectedDate = await calculateReceivableDate(constants.loanTemplates.TemplateWithTwoInstallments.PaymentDelayInDays);

        const response = await sendLMSRequest('get', `Calculator?Amount=1000&LoanTemplateId=${templateId}`);

        expect(response.data.loanReceivables[0].expectedDate, `Loan Receivable Expected Date calculated correctly`)
            .toBe(expectedDate);
    });

    test(`Check the last Expected Date of Loan Receivable'`, async () => {
        const expectedDate = await calculateReceivableDate(constants.loanTemplates.TemplateWithTwoInstallments.TotalDurationInDays);

        const response = await sendLMSRequest('get', `Calculator?Amount=1000&LoanTemplateId=${templateId}`);

        expect(response.data.loanReceivables[response.data.loanReceivables.length - 1].expectedDate, `Loan Receivable Expected Date calculated correctly`)
            .toBe(expectedDate);
    });

    test(`Calculate loan fee amount and installments using all existing Templates'`, async () => {
        const response = await sendLMSRequest('get', `Calculator/100`);

        expect(response.status, `Status code 200`)
            .toEqual(200);
    });

    /**
     * Negative Tests
     */

    test(`Server validation for GET request by invalid template Id.`, async () => {
        const response = await sendLMSRequest('get', `Calculator?Amount=1000&LoanTemplateId=${constants.invalidLoanTemplateIDs.id}`);

        expect(response.response.status, `Status code 500`)
            .toEqual(500);

        expect(response.response.data.Message).toBe('Internal server error');
    });

    test(`Server validation for GET request by amount equal to 0.`, async () => {
        const response = await sendLMSRequest('get', `Calculator/0`);

        expect(response.response.status, `Status code 400`)
            .toEqual(400);

        expect(response.response.data.ErrorDescription, `Amount should be greater than 0`)
            .toEqual('Amount should be greater than 0');
    });
});

/**
 * Functions
 */

async function calculateReceivableDate(daysNumber: number) {
    const date = new Date(); //+ daysNumber
    date.setDate(date.getDate() + daysNumber);
    const day = ("0" + (date.getDate())).slice(-2);
    const month = ("0" + (date.getMonth() + 1)).slice(-2);
    const year = date.getFullYear();
    return `${year}-${month}-${day}`;
}
