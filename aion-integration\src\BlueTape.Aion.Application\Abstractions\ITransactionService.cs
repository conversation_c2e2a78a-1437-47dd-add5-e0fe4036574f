﻿using BlueTape.Aion.DataAccess.External.Models.CreateAchTransfer.Response;
using BlueTape.Aion.DataAccess.External.Models.InstantTransfer.Response;
using BlueTape.Aion.DataAccess.External.Models.InternalTransfer;
using BlueTape.Aion.DataAccess.External.Models.Transactions;
using BlueTape.Integrations.Aion.AzureTableStorage.Entities;

namespace BlueTape.Aion.Application.Abstractions;

public interface ITransactionService
{
    Task<List<TransactionEntity>> GetAllExistingSentOrReceivedTransactions(CancellationToken ctx);
    Task SaveAionAchTransactionsAsync(List<AchResponseObj> achList, CancellationToken ctx);
    Task SaveAionWireTransactionsAsync(List<WireTransferObjItem> listOfWire, CancellationToken ctx);
    Task SaveAionInstantTransactionsAsync(List<InstantTransferObjectItemResponse> listOfInstant, CancellationToken ctx);
    Task SaveAionAchTransactionReturnsAsync(List<AchResponseObj> achReturnsList, CancellationToken ctx);
    Task SaveAionInternalTransactionsAsync(List<BookTransferObj> achList, CancellationToken ctx);
    Task SaveAionCreatedAchTransactionAsync(AchResponseObj achTransaction, CancellationToken ctx);
    Task SaveAionCreatedWireTransactionAsync(WireTransferObjItem wireTransferObjItem, CancellationToken ctx);
    Task SaveAionCreatedInstantTransactionAsync(InstantTransferObjectItemResponse instantTransferObjectItemResponse, CancellationToken ctx);
    
    Task SaveAionCreatedInternalTransactionAsync(BookTransferObj achTransaction, CancellationToken ctx);
    Task ProcessClearedTransactionsAsync(
        List<TransactionListObj> transactionList,
        List<TransactionEntity> transactionEntities,
        CancellationToken ctx);
    Task<IEnumerable<TransactionEntity>> GetByRowKeyPrefixAsync(string prefix, DateTime? startDate, DateTime? endDate, CancellationToken ct);
    Task EnsureTransactionDoesNotExist(string transactionId, string transactionNumber, CancellationToken ctx);
}