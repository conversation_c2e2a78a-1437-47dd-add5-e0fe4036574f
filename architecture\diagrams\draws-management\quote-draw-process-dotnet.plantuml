@startuml Quote-based Draw Process (.NET)

title Quote-based Draw Process (.NET)

participant "OnBoarding\nService" as obs #SkyBlue
participant "Connector\nService?" as cs #Orange
participant "LFSASJ" as lfsasj #SkyBlue
queue "Draw Events Queue" as dq #PaleVioletRed
participant "Loan Flow Service" as lfs #SkyBlue
participant "LMS" as lms #SkyBlue
queue "Payment Queue" as pq #PaleVioletRed

autonumber

== Merchant sends quote ==

obs --> obs : Quote approved by\ncustomer & backoffice
obs -> dq : Place event\n""Draw.Approved"" with type ""quote""
dq -> lfs : Consume draw events
lfs -> lms : Create authorization period\nwith credit hold 30 days

== Merchant sends invoice ==

cs --> cs : Invoice sent
cs -> dq : Place event\n""Draw.Approved"" with type ""invoice""
dq -> lfs : Consume draw events
lfs -> lms : Create draw and release credit hold
lms --> lms : Create 3days authorization period

== 3-days objection period expires ==

loop Daily schedule
    lfsasj --> lfsasj : Daily schedule
    lfsasj -> lms : Read expired authorizations
    lms --> lfsasj
end

lfsasj -> dq : Place event\n""Draw.Authorization.Expired""
dq -> lfs : Consume draw events
lfs -> lms : Start draw (already created)
lfs -> pq : Place payment request\n""CREATE.DRAW.DISBURSEMENT""

== Quote expires (merchant does not send invoice) ==

loop Daily schedule
    lfsasj --> lfsasj : Daily schedule
    lfsasj -> lms : Read expired authorizations
    lms --> lfsasj
end

lfsasj -> lms : Checks expired quotes
lms --> lfsasj
lfsasj -> dq : Places ""Draw.Expired"" event
dq -> lfs
lfs -> lms : Cancels auth. period

@enduml