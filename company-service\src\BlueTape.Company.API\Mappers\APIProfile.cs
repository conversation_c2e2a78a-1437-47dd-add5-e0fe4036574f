using AutoMapper;
using BlueTape.CashFlow.Domain.DTOs;
using BlueTape.CashFlow.Domain.DTOs.AssetReportModels;
using BlueTape.CashFlow.Domain.DTOs.Responses;
using BlueTape.Common.FileService.Models;
using BlueTape.Company.API.Models;
using BlueTape.Company.API.Models.CashFlow;
using BlueTape.Company.API.Models.CashFlow.AssetReportViewModels;
using BlueTape.Company.API.Models.CashFlow.Responses;
using BlueTape.Company.API.Models.Documents.Requests;
using BlueTape.Company.API.Models.Documents.Responses;
using BlueTape.Company.Domain.DTOs.BankAccounts;
using BlueTape.Company.Domain.DTOs.BankAccounts.Giact;
using BlueTape.Company.Domain.DTOs.Companies;
using BlueTape.Company.Domain.DTOs.Customers;
using BlueTape.Company.Domain.DTOs.GuestSuppliers;
using BlueTape.CompanyService.BankAccounts;
using BlueTape.CompanyService.BankAccounts.Giact;
using BlueTape.CompanyService.Common.Enums;
using BlueTape.CompanyService.Companies;
using BlueTape.CompanyService.Customers;
using BlueTape.CompanyService.Documents.Enums;
using BlueTape.CompanyService.Documents.Responses;
using BlueTape.CompanyService.GuestSuppliers;
using BlueTape.Document.Domain.DTOs;
using BlueTape.Document.Domain.DTOs.DocumentApproval;
using AddManualCashFlowResponseContract = BlueTape.CompanyService.CashFlow.AddManualCashFlow;
using AddressDataContract = BlueTape.CompanyService.Companies.AddressModel;
using ArAdvanceContract = BlueTape.CompanyService.Companies.ArAdvanceModel;
using CompanyAionSettingsDataContract = BlueTape.CompanyService.Companies.CompanyAionSettingsModel;
using CompanyCreditDataContract = BlueTape.CompanyService.Companies.CompanyCreditModel;
using CompanyDataContract = BlueTape.CompanyService.Companies.CompanyModel;
using CompanySettingsDataContract = BlueTape.CompanyService.Companies.CompanySettingsModel;
using CustomerSettingsDataContract = BlueTape.CompanyService.Customers.CustomerSettingsModel;
using EmailConfigurationDataContract = BlueTape.CompanyService.Companies.EmailConfigurationModel;
using InvoiceLoanPlanDataContract = BlueTape.CompanyService.Companies.InvoiceLoanPlanModel;
using RepaymentDataContract = BlueTape.CompanyService.Companies.RepaymentModel;

namespace BlueTape.Company.API.Mappers;

public class ApiProfile : Profile
{
    public ApiProfile()
    {
        CreateMap<CompanyDataContract, CompanyDto>()
            .ForMember(x => x.Owner, y => y.MapFrom(p => p.Owner))
            .ForMember(x => x.BlueTapeCompanyId, y => y.MapFrom(p => p.Id))
            .ReverseMap();
        CreateMap<CompanySettingsDto, CompanySettingsDataContract>()
            .ForMember(x => x.AchDiscount, y => y.Ignore())
            .ReverseMap();
        CreateMap<DepositDetailsDto, DepositDetailsModel>().ReverseMap();
        CreateMap<CreateCompanyModel, CreateCompanyDto>().ReverseMap();
        CreateMap<UpdateCompanyModel, UpdateCompanyDto>().ReverseMap();
        CreateMap<CompanyCreditDto, CompanyCreditDataContract>().ReverseMap();
        CreateMap<CompanyAionSettingsDto, CompanyAionSettingsDataContract>().ReverseMap();
        CreateMap<AddressDto, AddressDataContract>().ReverseMap();
        CreateMap<InvoiceLoanPlanDto, InvoiceLoanPlanDataContract>().ReverseMap();
        CreateMap<EmailConfigurationDto, EmailConfigurationDataContract>().ReverseMap();
        CreateMap<RepaymentDto, RepaymentDataContract>().ReverseMap();
        CreateMap<UserDto, CompanyService.Companies.UserModel>().ReverseMap();
        CreateMap<AddressModel, AddressDto>()
            .ForMember(x => x.CreatedAt, y => y.Ignore())
            .ForMember(x => x.Id, y => y.Ignore())
            .ForMember(x => x.UpdatedAt, y => y.Ignore())
            .ReverseMap();
        CreateMap<AccessTokenDto, AccessTokenPlaidModel>().ReverseMap();
        CreateMap<CompanyCreditModel, CompanyCreditDto>().ReverseMap();
        CreateMap<AccountNumberDto, AccountNumberModel>().ReverseMap();
        CreateMap<BankAccountDto, BankAccountModel>()
            .ForMember(x => x.IncludeInCashFlow,
                y => y.MapFrom(opt => opt.IncludeInCashFlow ?? true));
        CreateMap<BankAccountModel, BankAccountDto>()
            .ForMember(x => x.CreatedAt, y => y.Ignore())
            .ForMember(x => x.Version, y => y.Ignore())
            .ForMember(x => x.UpdatedAt, y => y.Ignore())
            .ForMember(x => x.IsPrimaryForCredit,
                y => y.MapFrom(opt => false)); //temp because we do not have it in input model
        CreateMap<PatchBankAccountSettingsModel, PatchBankAccountSettingsDto>().ReverseMap();
        CreateMap<BankAccountPlaidDto, BankAccountPlaidModel>().ForMember(x => x.IncludeInCashFlow,
            y => y.MapFrom(opt => opt.IncludeInCashFlow ?? true));
        CreateMap<BankAccountPlaidModel, BankAccountPlaidDto>();
        CreateMap<BankAccountIdentityModel, BankAccountIdentityDto>().ReverseMap();

        CreateMap<BankAccountGiactModel, BankAccountGiactDto>().ReverseMap();
        CreateMap<GiactAccountModel, GiactAccountDto>().ReverseMap();
        CreateMap<GiactVerificationModel, GiactVerificationDto>().ReverseMap();
        CreateMap<GiactAuthenticationModel, GiactAuthenticationDto>().ReverseMap();
        CreateMap<UpdateBankAccountNumberModel, UpdateBankAccountNumberDto>().ReverseMap();
        CreateMap<NoSupplierBankDetailsModel, NoSupplierBankDetailsDto>().ReverseMap();
        CreateMap<GiactResponseModel, GiactResponseDto>().ReverseMap();
        CreateMap<BillingAddressDto, BillingAddressModel>().ReverseMap();
        CreateMap<MigrateResultDto, MigrateResultModel>().ReverseMap();

        CreateMap<CompanyModelV2, CompanyDtoV2>()
            .ForMember(x => x.Owner, y => y.MapFrom(p => p.Owner))
            .ForMember(x => x.BlueTapeCompanyId, y => y.MapFrom(p => p.Id))
            .ReverseMap();
        CreateMap<BankAccountModelV2, BankAccountDtoV2>()
            .ForMember(x => x.CreatedAt, y => y.Ignore())
            .ForMember(x => x.Version, y => y.Ignore())
            .ForMember(x => x.UpdatedAt, y => y.Ignore())
            .ForMember(x => x.IsPrimaryForCredit,
                y => y.MapFrom(opt => false)) //temp because we do not have it in input model
            .ReverseMap();
        CreateMap<BankAccountAionSettingsDto, BankAccountAionSettingsModel>().ReverseMap();
        CreateMap<GuestSupplierModel, GuestSupplierDto>().ReverseMap();
        CreateMap<GuestSupplierCustomerModel, GuestSupplierCustomerDto>().ReverseMap();
        CreateMap<GuestSupplierInvoiceModel, GuestSupplierInvoiceDto>().ReverseMap();
        CreateMap<CompanyNoteModel, CompanyNoteDto>().ReverseMap();
        CreateMap<CreateCompanyNoteModel, CreateCompanyNoteDto>().ReverseMap();

        CreateMap<CreateBankAccountModel, CreateBankAccountDto>()
            .ForMember(x => x.AccountNumber, y => y.Ignore())
            .ForMember(x => x.Id, y => y.Ignore())
            .ForMember(x => x.Version, y => y.Ignore())
            .ForMember(x => x.IsPrimaryForCredit, y => y.MapFrom(opt => false));

        CreateMap<DirectTermsDto, DirectTermsModel>().ReverseMap();
        CreateMap<DownPaymentDetailsDto, DownPaymentDetailsModel>().ReverseMap();
        CreateMap<CompanyInHouseCreditDto, CompanyInHouseCreditModel>().ReverseMap();

        CreateMap<CustomerDto, CustomerModel>()
            .ForMember(x => x.Name, y => y.Ignore())
            .ForMember(x => x.SalesRepId, y => y.Ignore())
            .ForMember(x => x.LastPurchaseDate, y => y.Ignore())
            .ReverseMap();
        CreateMap<CreditInfoDto, CreditInfoModel>().ReverseMap();
        CreateMap<CustomerConnectorDto, CustomerConnectorModel>().ReverseMap();

        CreateMap<PatchBankAccountCashFlow, PatchBankAccountCashFlowDto>();
        CreateMap<CustomerSettingsDataContract, CustomerSettingsDto>().ReverseMap();
        CreateMap<CustomerInHouseCreditDto, CustomerInHouseCreditModel>().ReverseMap();
        CreateMap<AutomatedDrawApprovalModel, AutomatedDrawApprovalDto>().ReverseMap();
        CreateMap<ArAdvanceContract, ArAdvanceDto>().ReverseMap();

        CreateMap<ResultWithPaginationModel<CompanyModelV2>, ResultWithPaginationDto<CompanyDtoV2>>();
        CreateMap<ResultWithPaginationModel<GuestSupplierModel>, ResultWithPaginationDto<GuestSupplierDto>>()
            .ReverseMap();
        CreateMap<ResultWithPaginationModel<GuestSupplierCustomerModel>,
            ResultWithPaginationDto<GuestSupplierCustomerDto>>().ReverseMap();
        CreateMap<ResultWithPaginationModel<GuestSupplierInvoiceModel>,
            ResultWithPaginationDto<GuestSupplierInvoiceDto>>().ReverseMap();

        MapDocuments();
        MapCashFlow();
        MapEnums();
    }

    private void MapDocuments()
    {
        CreateMap<TemplateDto, TemplateResponse>().ReverseMap();
        CreateMap<CreateTemplateRequest, CreateTemplateDto>().ReverseMap();

        CreateMap<DocumentDto, DocumentLoanApplicationResponse>()
            .ForMember(x => x.Template, y => y.MapFrom(z => z.Template))
            .ForMember(x => x.LoanApplicationId, y => y.MapFrom(z => z.ReferenceId));
        CreateMap<DocumentDto, DocumentResponse>();

        CreateMap<DocumentLoanApplicationRequest, CreateDocumentDto>()
            .ForMember(x => x.CompanyId, y => y.Ignore())
            .ForMember(x => x.ReferenceId, y => y.Ignore());
        CreateMap<DocumentLoanApplicationRequest, CreateLoanDocumentApplicationDto>()
            .ForMember(x => x.LoanLegacyId, y => y.Ignore())
            .ForMember(x => x.ReferenceId, y => y.Ignore());
        CreateMap<NewLoanDocumentApplicationNameDto, NewDocumentNameResponse>();
        CreateMap<DocumentReportDto, DocumentReportResponse>().ReverseMap();

        CreateMap<DocumentApprovalDto, DocumentApprovalResponse>().ReverseMap();
        CreateMap<CreateDocumentApprovalRequest, CreateDocumentApprovalDto>()
            .ForMember(x => x.CreatedBy, y => y.Ignore())
            .ForMember(x => x.DocumentId, y => y.Ignore());
        CreateMap<AuthorizeApprovalDto, AuthorizeApprovalRequest>().ReverseMap();
    }

    private void MapCashFlow()
    {
        CreateMap<AccountViewModel, AccountModel>();
        CreateMap<AddressesViewModel, AddressesModel>();
        CreateMap<AssetReportViewModel, AssetReportModel>();
        CreateMap<BalanceViewModel, BalanceModel>();
        CreateMap<DataViewModel, DataModel>();
        CreateMap<EmailsViewModel, EmailsModel>();
        CreateMap<HistoricalBalancesViewModel, HistoricalBalancesModel>();
        CreateMap<ItemsViewModel, ItemsModel>();
        CreateMap<OwnersViewModel, OwnersModel>();
        CreateMap<PhoneNumbersViewModel, PhoneNumbersModel>();
        CreateMap<PlaidAssetReportViewModel, PlaidAssetReportModel>();
        CreateMap<TransactionViewModel, TransactionModel>();
        CreateMap<UserViewModel, BlueTape.CashFlow.Domain.DTOs.AssetReportModels.UserModel>().ReverseMap();
        CreateMap<AddPlaidCashFlow, AddPlaidCashFlowModel>();
        CreateMap<CashFlowResponseModel, CashFlowResponse>();
        CreateMap<CashFlowItemResponseModel, CashFlowItemResponse>();
        CreateMap<CashFlowAggregateModel, CashFlowAggregatedResponse>();
        CreateMap<BankStatementResponseModel, BankStatementResponse>()
            .ForMember(x => x.UploadedAt, y => y.MapFrom(opt => opt.CreatedAt))
            .ForMember(x => x.UploadedBy, y => y.MapFrom(opt => opt.CreatedBy))
            .ForMember(x => x.FileUrl, y => y.MapFrom(opt => opt.S3Url));
        CreateMap<AddPlaidCashFlowResponseModel, AddManualCashFlow>()
            .ForMember(x => x.AccountId, y => y.Ignore())
            .ForMember(x => x.Transactions, y => y.Ignore());
        CreateMap<AddPlaidCashFlowResponseModel, AddManualCashFlowResponseContract>();
        CreateMap<AddManualCashFlow, AddManualCashFlowModel>()
            .ForMember(x => x.FileName, y => y.Ignore())
            .ForMember(x => x.S3Url, y => y.Ignore());
        CreateMap<AddCashFlowTransactions, AddCashFlowTransactionsModel>();
        CreateMap(typeof(PaginatedListModel<>), typeof(PaginatedList<>)).ReverseMap();
        CreateMap<CashFlowListQuery, CashFlowListQueryModel>();
        CreateMap<FileRowValidationResult, CashFlowRowValidationResponse>();
        CreateMap<FileValidationResult, ManualCashFlowValidationResultResponse>();
    }

    private void MapEnums()
    {
        CreateMap<TemplateTypeFilter, TemplateType?>()
            .ConvertUsing((value, _) =>
            {
                return value switch
                {
                    TemplateTypeFilter.BNPL_AGREEMENT => TemplateType.BNPL_AGREEMENT,
                    TemplateTypeFilter.CUSTOMER_AGREEMENT_FOR_SELLER => TemplateType.CUSTOMER_AGREEMENT_FOR_SELLER,
                    TemplateTypeFilter.INVOICE_PURCHASER_AGREEMENT => TemplateType.INVOICE_PURCHASER_AGREEMENT,
                    TemplateTypeFilter.GUARANTY_SECURED_AGREEMENT => TemplateType.GUARANTY_SECURED_AGREEMENT,
                    TemplateTypeFilter.MASTER_AGREEMENT => TemplateType.MASTER_AGREEMENT,
                    TemplateTypeFilter.PERSONAL_GUARANTOR_AGREEMENT => TemplateType.PERSONAL_GUARANTOR_AGREEMENT,
                    TemplateTypeFilter.PERSONAL_GUARANTOR_AGREEMENT_ENTITY => TemplateType
                        .PERSONAL_GUARANTOR_AGREEMENT_ENTITY,
                    TemplateTypeFilter.ALL => null,
                    _ => default
                };
            });
    }
}