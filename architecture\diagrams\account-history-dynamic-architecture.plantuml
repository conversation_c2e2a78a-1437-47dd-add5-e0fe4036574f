@startuml Accounts History

title Accounts History Update

participant "First party\nservices" as first #SkyBlue
queue "AccountHistory\nUpdates" as ahqueue #LightSalmon
participant "Company\n(Account)\nService" as cs #SlateBlue
database "Company Db" as cdb
participant "First party\nservices" as first2 #SkyBlue

autonumber

== Write Account History ==

first --> first : Something changed in service data
first -> ahqueue : Places history event
ahqueue -> cs : Reads events
cs -> first2 : Reads necessary service
cs -> first2 : Reads necessary service
cs -> first2 : Reads necessary service
cs --> cs : Constructs message by template
cs -> cdb : Writes history change

== Read Account History ==

first -> cs : Get account history
cs -> cdb : Read history
cdb --> cs
cs --> first : API responds with history


@enduml