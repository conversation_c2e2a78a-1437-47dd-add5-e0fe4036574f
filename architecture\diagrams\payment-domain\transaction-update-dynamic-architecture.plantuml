@startuml

title Transaction Update

database "Table\nStorage" as ts
participant "Transaction\nStatus Job" as trs #SkyBlue
participant "Aion\nIntegration" as aint #SkyBlue
participant "Aion" as aion #Orange
queue "TransactionStatus" as trsqs #LightSalmon
participant "Payment\nService" as pserv #SkyBlue
database "PaymentDB" as pdb

autonumber

== Update Transaction Statuses ==

loop Scheduled process
   trs -> aint : Get transactions\nin a time window
   aint -> aion
   aion --> aint
   aion --> trs
   trs -> ts : Get last\nstatuses
   ts --> trs
   trs --> trs : Identify changes
   trs -> trsqs : Transaction Status update events
   trs -> ts : Write\nactual statuses
end

== Update transaction statuses ==

trsqs -> pserv : Get events
pserv -> pdb : Update\ntransaction status

@enduml