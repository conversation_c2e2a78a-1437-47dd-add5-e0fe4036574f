using System.Text.Json.Serialization;

namespace BlueTape.Aion.DataAccess.External.Models.InstantTransfer.Response;

public class Network
{
    [JsonPropertyName("messageId")]
    public string? MessageId { get; set; }

    [JsonPropertyName("createdAt")]
    public DateTime CreatedAt { get; set; }

    [JsonPropertyName("currency")]
    public string? Currency { get; set; }

    [JsonPropertyName("interbankSettlementDate")]
    public DateTime InterbankSettlementDate { get; set; }
    
    [JsonPropertyName("numberOfTransactions")]
    public decimal NumberOfTransactions { get; set; }
    
    [Json<PERSON>ropertyName("interbankSettlementAmount")]
    public decimal InterbankSettlementAmount { get; set; }
    
    [JsonPropertyName("settlementMethod")]
    public string? SettlementMethod { get; set; }
    
    [JsonPropertyName("clearingSystemCode")]
    public string? ClearingSystemCode { get; set; }
}