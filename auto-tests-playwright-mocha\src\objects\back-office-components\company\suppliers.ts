import {BasePage} from "../../base.page";

export class Suppliers extends BasePage {
    constructor(page) {
        super(page);
    };

    inputFields = {
        searchbar: this.page.getByRole('textbox'),
        invoiceThreshold: this.page.getByRole('textbox').nth(2),
        maxInvoiceAmount: this.page
            .locator(`//label[text()='Only invoices up to this threshold will be submitted for Automated Trade Credit']/following-sibling::input[1]`),
    };

    buttons = {
        searchButton: this.page.getByRole('button', {name: 'Search'}),
        settingsButton: this.page.getByRole('button', {name: 'Settings'}),
        save: this.page.getByRole('button', {name: 'Save'}),
        ninetyDaysPayment: this.page.locator('#react-select-4-option-2'),
        sixtyDaysPayment: this.page.locator('#react-select-4-option-1'),
        thirtyDaysPayment: this.page.locator('#react-select-4-option-0'),
        customersOfSupplier: this.page.getByRole('button', {name: 'Customers'}),
    };

    toggles = {
        automatedTradeCredit: this.page.locator('div')
            .filter({hasText: /^Automated Trade Credit$/})
            .locator('span'),  //todo ask frontend dev about id for this
    };

    elements = {
        setInvoicePackages: this.page.getByText('Set Invoice Packages'),
        defaultPlans: this.page.getByText('Default Plans (Installments - Days - Fees)'),
        maxInvoiceAmount: this.page.getByText('Max. Invoice Amount'),
        tradeCreditTerms: this.page.getByText('Regular Trade Credit Terms (For Non-Automated Invoices)'),
    };

    dropdowns = {
        plans: this.page.locator('.css-1d8n9bt').first(),
        chosenPlan: this.page.locator('.css-qc6sy-singleValue').first()
        // plans: this.page.locator('id=react-select-37-input')
    };

    async findSupplier(supplierEmail: string) {
        await this.inputFields.searchbar.fill(supplierEmail);
        await this.buttons.searchButton.click();
    }

    async clickOnSettingsButton() {
        await this.buttons.settingsButton.click();
    }

    async clickOnAutomatedTradeCreditToggle() {
        await this.toggles.automatedTradeCredit.click();
    }

    async wipeInvoiceThresholdField() {
        await this.inputFields.invoiceThreshold.dblclick();
        await this.page.keyboard.press('Backspace');
    }

    async clickOnPlanDropdown() {
        await this.dropdowns.plans.click();
    }
}
