import {BaseTest} from "../../test-utils";

export let storedCompanyRequestBody: {
    businessAddress: string;
    businessPhoneNumber: string;
    businessName: string;
    businessDisplayName: string;
    creditDetails: {
        accountType: string;
        accountCreationDate: string;
        pricePackage: string;
        creditProvider: string;
        creditStatus: string;
        creditAmount: number;
        outstandingAmount: number;
        overdueBalance: number;
        agingOverdueBalance: { name: string; daysFrom: number; daysTo: number; overdueBalance: number; }[];
        invoicesOverallAmount: number;
        invoicesOverallCount: number;
        invoicesLastYearAmount: number;
        invoicesLastYearCount: number;
        invoicesYearToDateAmount: number;
        invoicesYearToDateCount: number;
        invoicesLastMonthAmount: number;
        invoicesLastMonthCount: number;
        invoicesMonthToDateAmount: number;
        invoicesMonthToDateCount: number;
        invoicesMonthlyAverageAmount: number;
        invoicesMonthlyAverageCount: number;
    };
    sourceModifiedDate: string;
    id: string;
    customers: {
        firstName: string;
        lastName: string;
        cellPhoneNumber: string;
        emailAddress: string;
        sourceModifiedDate: any;
        id: string;
    }[];
};

/**
 * @param data array with next data, don't forget - all params with 'customer' prefix depends on each other
 * @companyId should be unique (use getGUID from BaseTest)
 * @businessPhoneNumber can be non-unique, by default ***********
 * @sourceModifiedDate date when action was (use getCurrentDate from BaseTest)
 * @customerFirstName from constants (AutoCustomerFirst)
 * @customerLastName from constants (AutoCustomerLast)
 * @customerCellPhoneNumber must be unique (use getCellPhoneNumber from BaseTest)
 * @customerEmailAddress must be unique (use this pattern sergei+genericapicustomer${BaseTest.dateTimePrefix()}@bluetape.com)
 * @customerId must be unique (use getGUID from BaseTest)
 * @invoicesOverallCount not important for now, 0 by default
 * @isCustomerUsed if company must be with customer, set to true
 * @businessName
 * @businessDisplayName
 */
export async function generateCompanyRequestBody(data: {
    companyId: string,
    businessPhoneNumber?: string,
    sourceModifiedDate?: string,
    customerFirstName?: string,
    customerLastName?: string,
    customerCellPhoneNumber?: string,
    customerEmailAddress?: string,
    customerId?: string,
    invoicesOverallCount?: number,
    businessName?: string,
    businessDisplayName?: string,
    isCustomerUsed: boolean
}) {

    /**
     * Due to the fact that a company can be created with (or without) customer
     * we must have the option of transferring customer data or not
     * because both options will be valid
     */
    let customersParamsArray: {
        firstName: string;
        lastName: string;
        cellPhoneNumber: string;
        emailAddress: string;
        sourceModifiedDate: any;
        id: string;
    }[] = [];

    if (data.isCustomerUsed === true) {
        customersParamsArray.push({
            "firstName": data.customerFirstName ?? 'AutoCustomerFirst',
            "lastName": data.customerLastName ?? 'AutoCustomerLast',
            "cellPhoneNumber": data.customerCellPhoneNumber ?? BaseTest.getCellPhoneNumber(),
            "emailAddress": data.customerEmailAddress ?? `sergei+genericapicustomer${BaseTest.dateTimePrefix()}@bluetape.com`,
            "sourceModifiedDate": data.sourceModifiedDate ?? BaseTest.getCurrentDate(),
            "id": data.customerId ?? BaseTest.getGUID()
        })
    } else customersParamsArray = [];

    const requestBody = {
        "businessAddress": "string",
        "businessPhoneNumber": data.businessPhoneNumber ?? "************",
        "businessName": data.businessName ?? `businessName`,
        "businessDisplayName": data.businessDisplayName ?? `businessDisplayName`,
        "creditDetails": {
            "accountType": "autoAccountType",
            "accountCreationDate": data.sourceModifiedDate ?? BaseTest.getCurrentDate(),
            "pricePackage": "autoPricePackage",
            "creditProvider": "autoCreditProvider",
            "creditStatus": "autoCreditStatus",
            "creditAmount": 5,
            "outstandingAmount": 0,
            "overdueBalance": 0,
            "agingOverdueBalance": [
                {
                    "name": "autoAgingOverdueBalanceName",
                    "daysFrom": 1,
                    "daysTo": 5,
                    "overdueBalance": 0
                }
            ],
            "invoicesOverallAmount": 100,
            "invoicesOverallCount": data.invoicesOverallCount ?? 0,
            "invoicesLastYearAmount": 0,
            "invoicesLastYearCount": 0,
            "invoicesYearToDateAmount": 0,
            "invoicesYearToDateCount": 0,
            "invoicesLastMonthAmount": 0,
            "invoicesLastMonthCount": 0,
            "invoicesMonthToDateAmount": 0,
            "invoicesMonthToDateCount": 0,
            "invoicesMonthlyAverageAmount": 0,
            "invoicesMonthlyAverageCount": 0
        },
        "sourceModifiedDate": data.sourceModifiedDate ?? BaseTest.getCurrentDate(),
        "id": data.companyId,
        "customers": customersParamsArray
    };

    storedCompanyRequestBody = requestBody;

    return requestBody;
}
