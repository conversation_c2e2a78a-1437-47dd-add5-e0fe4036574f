import { Col, Flex } from 'antd'
import { useTranslation } from 'react-i18next'
import { useState, useMemo } from 'react'

import Spacer from '@/components/common/Spacer'
import type {
  IArAdvanceApplicationDetailedBankAccount,
  IArAdvanceApplicationDetailedDocumentVerification,
} from '@/lib/redux/api/ar-advance-application/types'
import BankStatementBlock from '@/components/common/BankStatementBlock'
import { StyledButton } from '@/components/common/Button'
import { BankStatementUploadModal } from '@/components/common/modals/bank-statement-upload/BankStatementUploadModal'
import type { IBankStatementUploadFormAccount } from '@/globals/types/modals/bank-statement-upload'
import { ShortProductType } from '@/globals/types'

interface IProps {
  documentVerification: IArAdvanceApplicationDetailedDocumentVerification
  bankAccounts: IArAdvanceApplicationDetailedBankAccount[]
  companyId: string
}

const DocumentsAndAgreements = ({
  documentVerification,
  bankAccounts,
  companyId,
}: IProps): JSX.Element => {
  const { t } = useTranslation()

  const [isUploadModalOpened, setIsUploadModalOpened] = useState(false)

  const accountOptions = useMemo<IBankStatementUploadFormAccount[]>(
    () =>
      bankAccounts.map((account) => ({
        name: account.name ?? account.identifier,
        accountId: account.identifier,
      })),
    [bankAccounts],
  )

  return (
    <>
      <Spacer height={28} />
      <Flex gap={77}>
        <Col flex="1"></Col>
        <Col flex="1">
          <BankStatementBlock
            bankStatements={documentVerification.bankStatements}
          />
          <Spacer height={14} />
          <StyledButton
            type="primary"
            onClick={() => setIsUploadModalOpened(true)}
          >
            {t('bankStatementBlock.uploadDocument')}
          </StyledButton>
          <BankStatementUploadModal
            isOpened={isUploadModalOpened}
            onClose={() => setIsUploadModalOpened(false)}
            companyId={companyId}
            accounts={accountOptions}
            product={ShortProductType.AR_ADVANCE}
          ></BankStatementUploadModal>
        </Col>
      </Flex>
    </>
  )
}

export default DocumentsAndAgreements
