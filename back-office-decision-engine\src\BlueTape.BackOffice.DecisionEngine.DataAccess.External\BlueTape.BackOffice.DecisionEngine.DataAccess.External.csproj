<Project Sdk="Microsoft.NET.Sdk">
    <ItemGroup>
      <None Remove="OnboardingApi\OnboardingApiProxy.cs~RF11214853.TMP" />
    </ItemGroup>
    <ItemGroup>
        <FrameworkReference Include="Microsoft.AspNetCore.App" />
    </ItemGroup>
    <ItemGroup>
        <PackageReference Include="BlueTape.Integrations.Plaid" Version="1.0.7" />
        <PackageReference Include="bluetape.companyservice" Version="1.3.4" />
        <PackageReference Include="bluetape.companyservice.common" Version="1.1.21" />
        <PackageReference Include="BlueTape.AzureKeyVault" Version="1.0.3" />
        <PackageReference Include="BlueTape.LS.Domain" Version="1.1.36" />
        <PackageReference Include="BlueTape.PaymentService" Version="1.0.14" />
        <PackageReference Include="Microsoft.Extensions.Http.Polly" Version="8.0.0" />
        <PackageReference Include="Microsoft.Extensions.DependencyInjection" Version="8.0.0" />
        <PackageReference Include="Microsoft.Extensions.Logging" Version="8.0.0" />
        <PackageReference Include="BlueTape.Common.ExceptionHandling" Version="1.0.8" />
        <PackageReference Include="BlueTape.InvoiceClient" Version="1.0.24" />
        <PackageReference Include="BlueTape.InvoiceService" Version="1.0.43" />
        <PackageReference Include="BlueTape.LS" Version="1.1.76" />
        <PackageReference Include="BlueTape.OBS" Version="1.6.72" />

        <PackageReference Include="BlueTape.Utilities" Version="1.4.6" />
        <PackageReference Include="Newtonsoft.Json" Version="13.0.1" />
        <PackageReference Include="BlueTape.Integrations.Giact" Version="1.0.3" />
    </ItemGroup>
    <ItemGroup>
      <ProjectReference Include="..\BlueTape.BackOffice.DecisionEngine.Domain\BlueTape.BackOffice.DecisionEngine.Domain.csproj" />
    </ItemGroup>
    <ItemGroup>
        <AssemblyAttribute Include="System.Runtime.CompilerServices.InternalsVisibleTo">
            <_Parameter1>BlueTape.BackOffice.DecisionEngine.DataAccess.External.Tests</_Parameter1>
        </AssemblyAttribute>
    </ItemGroup>
</Project>
