import { IsNotEmpty, Min } from 'class-validator'
import i18n from 'i18next'

import { BaseValidationModel } from '@/lib/class-validator/base-validation-model'

export class DrawApplicationProjectData extends BaseValidationModel {
  constructor(contractValue: any, endDate: any) {
    super()
    this.contractValue = contractValue
    this.endDate = endDate
  }

  @IsNotEmpty({
    message: i18n.t('drawApplicationProjectForm.isContractValueLimitNotEmpty', {
      ns: 'validation',
    }),
  })
  @Min(0, {
    message: i18n.t('drawApplicationProjectForm.minContractValueLimit', {
      ns: 'validation',
    }),
  })
  contractValue: number

  @IsNotEmpty({
    message: i18n.t('drawApplicationProjectForm.isEndOfJobNotEmpty', {
      ns: 'validation',
    }),
  })
  endDate: string
}
