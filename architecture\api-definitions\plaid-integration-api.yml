openapi: "3.0.0"
info:
  version: "0.0.1"
  title: "Plaid Integration API"
  description: |
    API definition of Plaid Integration
servers:
  - url: TBD-Dev
    description: Development server
  - url: TBD-Prod
    description: Production server
paths:
  /asset_report/create:
    post:
      tags:
        - assetReport
      summary: Requests to create an asset report from Plaid, to get actual cashflow data
      description: Requests to create an asset report from Plaid, to get actual cashflow data
      operationId: requestAssetReportByPlaid
      requestBody:
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/CreateAssetReportRequest"
      responses:
        201:
          description: The request was created
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/CreateAssetReportResponse'
        400:
          description: Bad request
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
  /asset_report/{reportRequestId}/status:
    get: 
      tags:
        - assetReport
      summary: Gets status of asset report by request id
      description: Gets status of asset report by request id
      operationId: getAssetReportRequestStatus
      parameters:
        - name: reportRequestId
          description: The report's request id
          in: path
          required: true
          schema:
            type: string
      responses:
        200:
          description: The request was created
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/CreateAssetReportStatusResponse'
        400:
          description: Bad request
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'      
components:
  schemas:
    CreateAssetReportRequest:
      type: object
      required: 
        - companyId
        - accessTokens
      properties:
        companyId:
          type: string
        bankAccountIds:
          type: array
          items:
            type: string
    CreateAssetReportResponse:
      type: object
      properties:
        reportRequestId:
          type: string
    CreateAssetReportStatusResponse:
      type: object
      properties:
        reportRequestId:
          type: string
        status:
          type: string
          enum:
            - processing
            - success
            - error
        daysAvailableFrom:
          type: string
          format: date
          nullable: true
        daysAvailableTo:
          type: string
          format: date
          nullable: true
    ErrorResponse:
      type: object
      required:
        - statusCode
        - message
        - errorDescription
      properties:
        statusCode:
          description: HTTP status code corresponding to the error.
          type: integer
          example: 400
        message:
          description: A human readable error message.
          type: string
          example: Invalid model.
        errorDescription:
          description: Detailed information.
          type: string
  securitySchemes:
    ApiKey:
      type: apiKey
      in: header
      name: X-Api-Key
security:
  - ApiKey: []  
