# Service Bus Namespace
resource "azurerm_servicebus_namespace" "asbn" {
  name                = "aion-service-bus-namespace-${var.application_name.full}-${var.environment}"
  location            = var.resource_group_location
  resource_group_name = var.resource_group_name
  sku                 = "Standard"

  tags = {
    environment = title(var.environment)
    source      = "Terraform"
    app         = title(var.application_name.full)
    CreatedOn   = formatdate("YYYY-MM-DD hh:mm ZZZ", timestamp())
    Type        = "Microsoft Azure Service Bus Namespace"
  }

  lifecycle {
    ignore_changes = [
      tags["CreatedOn"]
    ]
  }

}

resource "azurerm_servicebus_queue" "sbqet" {
  name         = "externaltransactionqueue-${var.environment}"
  namespace_id = azurerm_servicebus_namespace.asbn.id
  max_delivery_count = 3
} 

resource "azurerm_key_vault_secret" "external-transaction-queue-secret" {
  name         = "${var.external_transaction_queue_connection}"
  value        = azurerm_servicebus_queue_authorization_rule.external-transaction-queue-read-write-rule.primary_connection_string
  key_vault_id = var.key_vault_id
}

resource "azurerm_key_vault_secret" "external-transaction-queue-name-secret" {
  name         = "${var.external_transaction_queue_name}"
  value        = azurerm_servicebus_queue.sbqet.name
  key_vault_id = var.key_vault_id
} 

resource "azurerm_servicebus_queue_authorization_rule" "external-transaction-queue-read-write-rule" {
  name     = "${var.application_name.slug}-bus-queue-connection-auth-rule"
  queue_id = azurerm_servicebus_queue.sbqet.id
  listen = true
  send = true
}

resource "azurerm_servicebus_queue" "sbqit" {
name         = "internalltransactionqueue-${var.environment}"
namespace_id = azurerm_servicebus_namespace.asbn.id
max_delivery_count = 3
}

resource "azurerm_key_vault_secret" "internal-transaction-queue-secret" {
name         = "${var.internal_transaction_queue_connection}"
value        = azurerm_servicebus_queue_authorization_rule.internal-transaction-queue-read-write-rule.primary_connection_string
key_vault_id = var.key_vault_id
}

resource "azurerm_key_vault_secret" "internal-transaction-queue-name-secret" {
name         = "${var.internal_transaction_queue_name}"
value        = azurerm_servicebus_queue.sbqit.name
key_vault_id = var.key_vault_id
}

resource "azurerm_servicebus_queue_authorization_rule" "internal-transaction-queue-read-write-rule" {
name     = "${var.application_name.slug}-bus-queue-connection-auth-rule"
queue_id = azurerm_servicebus_queue.sbqit.id
listen = true
send = true
}
