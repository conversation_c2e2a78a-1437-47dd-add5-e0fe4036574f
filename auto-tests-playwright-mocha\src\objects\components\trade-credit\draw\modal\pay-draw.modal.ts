import {BasePage} from '../../../../base.page';

export class PayDrawModal extends BasePage {
    constructor(page){
        super(page);
    };

    radioButtons = {
        remainingBalance: this.page.locator('"Remaining Balance"'),
    };

    buttons = {
        pay: this.page.locator('[tabindex="0"]:has-text("Pay $")'),
    };

    async payRemainingBalance() {
        await this.radioButtons.remainingBalance.click({delay: 2000});
        await this.buttons.pay.click({delay: 2000});
    }
}