openapi: '3.0.0'
info:
  version: '0.0.2'
  title: 'Company/Customer Service API'
  description: | 
    API definition of private service for companies, suppliers low-level functions
servers:
- url: TBD-Dev
  description: Development server
- url: TBD-QA
  description: QA
- url: TBD-Prod
  description: Production server
paths:
  /companies:
    get:
      tags:
        - companies
      summary: Gets companies by various filters
      description: Gets companies by various filters
      operationId: getCompanies
      parameters:
        - name: id
          description: The id of required company.
          in: query
          required: false
          schema:
            type: string
        - name: activeAccountOnly
          description: Filter to active accounts. Active accounts are goodStanding*, pastDue, onHold, inCollection, potentiallyFraud
          in: query
          required: false
          schema:
            type: boolean
        - name: validAccountOnly
          description: Filter to valid accounts. Valid accounts are goodStanding*, pastDue, onHold, inCollection, inactive,  potentiallyFraud
          in: query
          required: false
          schema:
            type: boolean
        - name: status
          description: The status of company.
          in: query
          required: false
          schema:
            type: string
            enum: 
              - active
              - inactive
              - closed
              - potentiallyFraud
        - name: accountstatus
          description: The account status.
          in: query
          required: false
          schema:
            type: string
            enum: 
              - incomplete
              - underReview
              - underStipulation
              - goodStandingConnected
              - goodStandingManual
              - pastDue
              - onHold
              - inCollection
              - inactive
              - potentiallyFraud
              - closed
        - name: name
          description: The business name or dba to search for
          in: query
          required: false
          schema:
            type: string
        - name: page
          description: The page number
          example: 2
          in: query
          required: false
          schema:
            type: number
        - name: items
          description: The items number on a page
          example: 50
          in: query
          required: false
          schema:
            type: number
      responses:
        200:
          description: The paginated companies.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/PagedCompanies'
        400:
          description: Invalid request
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
  /admin/companies/{id}/status:
    patch:
      tags:
        - accounts Admin
      summary: Changes account status manually
      description: Changes account status manually
      operationId: patchManualAccountStatus
      parameters:
        - name: id
          description: The id of required company.
          in: path
          required: true
          schema:
            type: string
        - name: userId
          description: Identifier of the user who manually set the status
          example: 62c5e900cd65d31d3f25c34c
          in: header
          required: true
          schema:
            type: string
            nullable: false
      requestBody:
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/PatchAccountStatus"
      responses:
        200:
          description: The updated company.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Company'
        400:
          description: Invalid request
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
  /admin/companies/{id}/status/delete:
    patch:
      tags:
        - accounts Admin
      summary: Removes manually set status
      description: Removes manually set status
      operationId: clearManualAccountStatus
      parameters:
        - name: id
          description: The id of required company.
          in: path
          required: true
          schema:
            type: string
        - name: userId
          description: Identifier of the user who manually clears the status
          example: 62c5e900cd65d31d3f25c34c
          in: header
          required: true
          schema:
            type: string
            nullable: false
      requestBody:
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/DeleteAccountStatus"
      responses:
        200:
          description: The updated company.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Company'
        400:
          description: Invalid request
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
  /companies/{id}/accountHistory:
    get:
      tags: 
        - accounts history
      summary: Gets a company account history (default descending order)
      description: Gets a company account history (default descending order)
      operationId: getAccountHistory
      parameters:
        - name: id
          description: The id of required company.
          in: path
          required: true
          schema:
            type: string
        - name: draftId
          description: Id of draft to filter events
          example: 62c5e900cd65d31d3f25c34c
          in: query
          required: false
          schema:
            type: string
        - name: creditAppplicationId
          description: Id of credit application to filter events
          example: 62c5e900cd65d31d3f25c34c
          in: query
          required: false
          schema:
            type: string
        - name: drawApprovalId
          description: Id of draw approval to filter events
          example: 62c5e900cd65d31d3f25c34c
          in: query
          required: false
          schema:
            type: string
        - name: creditId
          description: Id of credit to filter events
          example: 62c5e900cd65d31d3f25c34c
          in: query
          required: false
          schema:
            type: string
        - name: drawId
          description: Id of draw to filter events
          example: 62c5e900cd65d31d3f25c34c
          in: query
          required: false
          schema:
            type: string
        - name: projectId
          description: Id of project to filter events
          example: 62c5e900cd65d31d3f25c34c
          in: query
          required: false
          schema:
            type: string
      responses:
        200:
          description: List of history events
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/AccountHistoryEvent'
        404:
          description: Not found
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
  /companies/{id}:
    get:
      tags:
        - companies
      summary: Gets a company by id
      description: Gets a company by id
      operationId: getCompanyById
      parameters:
        - name: id
          description: The id of required company.
          in: path
          required: true
          schema:
            type: string
      responses:
        200:
          description: The company itself.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Company'
        400:
          description: Invalid request
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
  /companies/getByIds:
    post:
      tags:
        - companies
      summary: Gets companies by id list
      description: Gets companies by id list
      operationId: getCompaniesByIdList
      requestBody:
        content:
          application/json:
            schema:
              items:
                type: string
      responses:
        200:
          description: List of companies.
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/Company'
        400:
          description: Invalid request
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
  /companies/ein/{einHash}:
    get:
      tags:
        - companies
      summary: Gets a company by ein hash
      description: Gets a company by ein hash
      operationId: getCompanyByEinHash
      parameters:
        - name: einHash
          description: The ein hash of the required company.
          in: path
          required: true
          schema:
            type: string
      responses:
        200:
          description: The company itself.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Company'
        400:
          description: Invalid request
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
  /customers:
    get:
      tags:
        - customers
      summary: Gets customers by various filters
      description: Gets customers by various filters
      operationId: getCustomers
      parameters: 
        - name: id
          description: The id of required customer.
          in: query
          required: false
          schema:
            type: string
        - name: page
          description: The page number
          example: 2
          in: query
          required: false
          schema:
            type: number
        - name: items
          description: The items number on a page
          example: 50
          in: query
          required: false
          schema:
            type: number
      responses:
        200:
          description: The paginated customers.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/PagedCustomers'
        400:
          description: Invalid request
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
  /customers/{id}:
    get:
      tags:
        - customers
      summary: Gets a customer by id
      description: Gets a customer by id
      operationId: getCustomerById
      parameters:
        - name: id
          description: The id of required customer.
          in: path
          required: true
          schema:
            type: string
      responses:
        200:
          description: The customer
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Customer'
        400:
          description: Invalid request
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
  /customers/getByIds:
    post:
      tags:
        - customers
      summary: Gets customers by id list
      description: Gets customers by id list
      operationId: getCustomersByIdList
      requestBody:
        content:
          application/json:
            schema:
              items:
                type: string
      responses:
        200:
          description: List of customers
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/Customer'
        400:
          description: Invalid request
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
  /customers/integration/{integrationId}/contact/{contactId}:
    get:
      tags:
        - customers
      summary: Gets customer by integration and contact id
      description: Gets customer by integration and contact id
      operationId: getCustomerByContactId
      parameters:
        - name: integrationId
          description: The integration id
          in: path
          required: true
          schema:
            type: string
        - name: contactId
          description: The contact id
          in: path
          required: true
          schema:
            type: string
      responses:
        200:
          description: The customer
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Customer'
        400:
          description: Invalid request
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
  /customers/billing-contacts/{id}:
    get:
      tags:
        - customers
      summary: Gets customers by billing contact
      description: Gets customers by billing contact
      operationId: getCustomersByBillingContact
      parameters:
        - name: id
          description: The billing contact id
          in: path
          required: true
          schema:
            type: string
      responses:
        200:
          description: List of customers
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/Customer'
        400:
          description: Invalid request
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
  /bank-account:
    get:
      tags:
        - bankAccounts
      summary: Gets bank account by various filters
      description: Gets bank account by various filters
      operationId: getBankAccountByFilters
      parameters: 
        - name: id
          description: The bank account id
          in: query
          required: false
          schema:
            type: string
        - name: plaid-accountId
          description: The plaid account id
          in: query
          required: false
          schema:
            type: string
      responses:
        200:
          description: The bank accounts
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/BankAccountModel'
        400:
          description: Invalid request
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
    post:
      tags:
        - bankAccounts
      summary: Add bank account
      description: Add bank account
      operationId: createBankAccount
      requestBody:
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/CreateBankAccountRequest"
      responses:
        201:
          description: The created bank account
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/BankAccountModel'
        400:
          description: Invalid request
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'                
  /bank-account/{id}:
    get:
      tags:
        - bankAccounts
      summary: Gets bank account by id
      description: Gets bank account by id
      operationId: getBankAccountById
      parameters:
        - name: id
          description: The bank account id
          in: path
          required: true
          schema:
            type: string
      responses:
        200:
          description: The bank account
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/BankAccountModel'
        400:
          description: Invalid request
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
    patch:
      tags:
        - bankAccounts
      summary: Updates bank account by id with Plaid
      description: Updates bank account by id with Plaid
      operationId: updateBankAccountById
      parameters:
        - name: id
          description: The bank account id
          in: path
          required: true
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/UpdateBankAccountRequest"
      responses:
        200:
          description: The bank account
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/BankAccountModel'
        400:
          description: Invalid request
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
  /bank-account/getByIds:
    post:
      tags:
        - bankAccounts
      summary: Gets bank accounts by id list
      description: Gets bank accounts by id list
      operationId: getBankAccountByIdList
      requestBody:
        content:
          application/json:
            schema:
              items:
                type: string
      responses:
        200:
          description: The bank accounts
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/BankAccountModel'
        400:
          description: Invalid request
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
components:
  schemas:
    PaginatedCompanyResponse:
      type: object
      required:
        - limit
        - offset
        - count
        - result
      properties:
        limit:
          description: Number of results requested.
          type: integer
          example: 50
        offset:
          description: Number of elements skipped.
          type: integer
          example: 150
        count:
          description: Actual number of results returned.
          type: integer
          example: 14
        total:
          description: Total number of results matching the query.
          type: integer
          example: 164
        result:
          description: Invoices matching the query.
          type: array
          items:
            $ref: '#/components/schemas/Company'
    Company:
      type: object
      properties:
        id:
          description: Id of company
          type: string
        type:
          type: string
          enum:
            - supplier
            - contractor
        status:
          type: string
          enum:
            - active
            - inactive
            - closed
            - potentiallyFraud
        bankAccounts:
          type: array
          items:
            type: string
        createdAt:
          type: string
          format: date-time
        updatedAt:
          type: string
          format: date-time
        name:
          description: Name of company
          type: string
          example: Raj's QA business
        entity:
          type: string
          nullable: true
          example: LLC
        legalName:
          type: string
          nullable: true
          example: Raj's QA business
        website:
          type: string
          nullable: true
        address:
          $ref: '#/components/schemas/AddressModel'
        phone:
          description: Phone number
          type: string
          nullable: true
        email:
          description: Email address
          type: string
          nullable: true
        isBusiness:
          type: boolean
          example: true
        settings:
          $ref: '#/components/schemas/CompanySettingsModel'
        accountStatus:
          type: string
          enum:
            - incomplete
            - underReview
            - underStipulation
            - goodStandingConnected
            - goodStandingManual
            - pastDue
            - onHold
            - inCollection
            - inactive
            - potentiallyFraud
            - closed
        accountStatusUpdatedAt:
          type: string
          format: date-time
        isAccountStatusManuallySet:
          type: boolean
        manualStatusBy:
          type: string
          nullable: true        
        manualStatusNote:
          type: string
          nullable: true
    Customer:
      type: object
      properties:
        id:
          type: string
        companyId:
          type: string
        displayName:
          type: string
        firstName:
          type: string
        lastName:
          type: string
        emailAddress:
          type: string
        cellPhoneNumber:
          type: string
        businessPhoneNumber:
          type: string
        businessAddress:
          type: string
        integration:
          $ref: "#/components/schemas/CustomerConnectorModel"
        isDraft:
          type: boolean
        isDeleted:
          type: boolean
        contactSource:
          type: string
          enum:
            - UnDefined
            - NetSuite
            - QuickBooks
            - Generic
        syncToken:
          type: string
    CustomerConnectorModel:
      type: object
      properties:
        connectorCustomerId:
          type: string
        integrationId:
          type: string
        connectorContactId:
          type: string
        businessName:
          type: string
        sourceModifiedDate:
          type: string
          format: date-time
        isPrimaryContact:
          type: boolean
        blueTapeCustomerCompanyId:
          type: string
        isCompanyType:
          type: boolean
    AddressModel:
      type: object
      properties:
        address:
          description: Address line
          type: string
        unitNumber:
          description: Unit number
          type: string
        city:
          description: City
          type: string
        state:
          description: State
          type: string
        zip:
          description: Zipcode
          type: string
        phone:
          description: Phone number
          type: string
    CompanySettingsModel:
      type: object
      properties:
        cardPricingPackageId:
          type: string
          enum:
            - packageA
            - packageB
            - packageC
            - packageD
            - packageE
            - optOut
        loanPricingPackageId:
          type: string
          enum:
            - packageA
            - packageB
            - packageC
            - optOut
    BankAccountModel:
      type: object
      properties:
        id:
          type: string
        name:
          type: string
        accountHolderName:
          type: string
        accountName:
          type: string
        accountType:
          type: string
          enum:
            - brokerageaccount
            - business
            - checking
            - creditCard
            - creditcard
            - investment
            - lineofcredit
            - loan
            - moneymarket
            - mortgage
            - personal
            - savings
        finicityHistorySyncDone:
          type: boolean
        isDeactivated:
          type: boolean
        isManualEntry:  
          type: boolean
        isPrimary:
          type: boolean
        paymentMethodType:
          type: string
          enum:
            - bank
            - card
        routingNumber:
          type: string
        status:
          type: string
          enum:
            - manualverified
            - notverified
            - verified
        thirdPartyId:
          type: string
        voidedCheck:
          type: string
        accountNumber:
          $ref: "#/components/schemas/AccountNumberModel"
        billingAddress:
          $ref: "#/components/schemas/BillingAddressModel"
        plaid:
          $ref: "#/components/schemas/PlaidModel"
    AccountNumberModel:
      type: object
      properties:
        display:
          type: string
    BillingAddressModel:
      type: object
      properties:
        addressLine1:
          type: string
        addressLine2:
          type: string
        city:
          type: string
        stateCode:
          type: string
        zipCode:
          type: string
    PlaidModel:
      type: object
      properties:
        itemId:
          type: string
        accountId:
          type: string
        accessToken:
          type: string
        institutionId:  
          type: string
    CreateBankAccountRequest:
      type: object
      properties:
        name:
          type: string
        accountHolderName:
          type: string
        accountName:
          type: string
        accountType:
          type: string
          enum:
            - brokerageaccount
            - business
            - checking
            - creditCard
            - creditcard
            - investment
            - lineofcredit
            - loan
            - moneymarket
            - mortgage
            - personal
            - savings
        finicityHistorySyncDone:
          type: boolean
        isDeactivated:
          type: boolean
        isManualEntry:  
          type: boolean
        isPrimary:
          type: boolean
        paymentMethodType:
          type: string
          enum:
            - bank
            - card
        routingNumber:
          type: string
        status:
          type: string
          enum:
            - manualverified
            - notverified
            - verified
        thirdPartyId:
          type: string
        voidedCheck:
          type: string
        accountNumber:
          $ref: "#/components/schemas/AccountNumberModel"
        billingAddress:
          $ref: "#/components/schemas/BillingAddressModel"
        plaid:
          $ref: "#/components/schemas/PlaidModel"
    UpdateBankAccountRequest:
      type: object
      properties:
        plaid:
          $ref: "#/components/schemas/PlaidModel"
    ErrorResponse:
      type: object
      required:
        - statusCode
        - message
        - errorDescription
      properties:
        statusCode:
          description: HTTP status code corresponding to the error.
          type: integer
          example: 400
        message:
          description: A human readable error message.
          type: string
          example: Invalid model.
        errorDescription:
          description: Detailed information.
          type: string
    PagedCompanies:
      type: object
      properties:
        pageNumber:
          type: number
        pagesCount:
          type: number
        totalCount:
          type: number
        result:
          type: array
          items:
            $ref: "#/components/schemas/Company"
    PagedCustomers:
      type: object
      properties:
        pageNumber:
          type: number
        pagesCount:
          type: number
        totalCount:
          type: number
        result:
          type: array
          items:
            $ref: "#/components/schemas/Customer"
    PatchAccountStatus:
      type: object
      properties: 
        newAccountStatus:
          type: string
          enum: 
            - goodStandingConnected
            - goodStandingManual
            - pastDue
            - onHold
            - inCollection
            - inactive
            - potentiallyFraud
            - closed
        note:
          type: string
    DeleteAccountStatus:
      type: object
      properties: 
        note:
          type: string
    AccountHistoryEvent:
      type: object
      properties: 
        createdAt:
          type: string
          format: date-time
        user:
          type: string
        event:
          type: string
        message:
          type: string
  securitySchemes:
    ApiKey:
      type: apiKey
      in: header
      name: X-Api-Key
security:
  - ApiKey: []
