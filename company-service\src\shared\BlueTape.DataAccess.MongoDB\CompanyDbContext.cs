﻿using BlueTape.DataAccess.MongoDB.Entities.Audit;
using BlueTape.DataAccess.MongoDB.Entities.BankAccounts;
using BlueTape.DataAccess.MongoDB.Entities.Company;
using BlueTape.DataAccess.MongoDB.Entities.Customer;
using BlueTape.DataAccess.MongoDB.Entities.Drafts;
using BlueTape.DataAccess.MongoDB.Entities.Invoice;
using BlueTape.DataAccess.MongoDB.Entities.LoanApplication;
using BlueTape.DataAccess.MongoDB.Entities.Users;
using BlueTape.MongoDB;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using MongoDB.Driver;
using System.Diagnostics.CodeAnalysis;

namespace BlueTape.Company.API.DataAccess;

[ExcludeFromCodeCoverage]
public class CompanyDbContext : MongoAzDbContext
{
    public CompanyDbContext(IConfiguration configuration, ILogger<CompanyDbContext> logger) : base(configuration, logger)
    {
    }

    public IMongoCollection<CompanyEntity> Companies => Collection<CompanyEntity>("companies");
    public IMongoCollection<BankAccountEntity> BankAccounts => Collection<BankAccountEntity>("bankaccounts");
    public IMongoCollection<CustomerEntity> Customers => Collection<CustomerEntity>("customeraccounts");
    public IMongoCollection<LoanApplicationEntity> LoanApplication => Collection<LoanApplicationEntity>("loanapplications");
    public IMongoCollection<InvoiceEntity> Invoice => Collection<InvoiceEntity>("invoices");
    public IMongoCollection<AccountStatusChangeAuditEntity> AccountStatusChangeAudit => Collection<AccountStatusChangeAuditEntity>("accountStatusChangeAudits");
    public IMongoCollection<UserEntity> Users => Collection<UserEntity>("users");
    public IMongoCollection<DraftEntity> Drafts => Collection<DraftEntity>("drafts");
    public IMongoCollection<UserRoleEntity> UserRoles => Collection<UserRoleEntity>("userroles");
}
