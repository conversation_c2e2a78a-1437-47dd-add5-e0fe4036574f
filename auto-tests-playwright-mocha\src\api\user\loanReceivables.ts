import {BaseTest} from '../../tests/test-utils';

export function generateJSONReceivable(loanReceivablesTemplate) {
    const JSONdates = [BaseTest.getReceivablesDates(0),
        BaseTest.getReceivablesDates(0),
        BaseTest.getReceivablesDates(7),
        BaseTest.getReceivablesDates(14),
        BaseTest.getReceivablesDates(21),
        BaseTest.getReceivablesDates(28),
        BaseTest.getReceivablesDates(60)
    ];
    for (let i = 0; i < loanReceivablesTemplate.loanReceivable.loanReceivables.length; i++) {
        loanReceivablesTemplate.loanReceivable.loanReceivables[i].expectedDate = JSONdates[i];
    }
    return loanReceivablesTemplate;
}

export function removeKeys(jsonArray, keysToRemove) { //removes unnecessary keys and values 
    return jsonArray.map(obj => {
        const newObj = {};
        for (const [key, value] of Object.entries(obj)) {
            if (!keysToRemove.includes(key)) {
                newObj[key] = value;
            }
        }
        return newObj;
    });
}
