.beta:
  extends: .test:base
  variables:
    CI_ENVIRONMENT_URL: $CI_ENVIRONMENT_URL
    CI_ENVIRONMENT_BACKEND_URL: $BETA_CI_ENVIRONMENT_BACKEND_URL
    ADMIN_BACKEND_URL: $BETA_BACKEND_URL
    ADMIN_EMAIL: $BETA_ADMIN_EMAIL
    ADMIN_PASSWORD: $BETA_ADMIN_PASSWORD
    USER_EMAIL: $BETA_USER_EMAIL
    USER_PASSWORD: $BETA_USER_PASSWORD
    USER_FIRSTNAME: $BETA_USER_FIRSTNAME
    USER_LASTNAME: $BETA_USER_LASTNAME
    USER_BACKEND_URL: $BETA_USER_BACKEND_URL
    USER_DOMAIN: $BETA_USER_DOMAIN
    USER_KEY: $BETA_USER_KEY
    MONGODB_URI: $MONGODB_URI
    ACCESS_KEY_AWS: $ACCESS_KEY_AWS
    SECRET_ACCESS_KEY_AWS: $SECRET_ACCESS_KEY_AWS
    BUCKETNAME_AWS: $BETA_BUCKETNAME_AWS
  environment:
    name: staging
    url: https://beta.bluetape.com/

  artifacts:
    when: always
    paths:
      - allure-report
    expire_in: 1 week
