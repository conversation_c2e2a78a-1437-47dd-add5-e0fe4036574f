﻿using System.Diagnostics.CodeAnalysis;
using BlueTape.Common.Extensions.Abstractions;
using Serilog.Context;

namespace BlueTape.Aion.API.Middlewares;

[ExcludeFromCodeCoverage]
public class AionLoggingMiddleware
{
    private readonly RequestDelegate _next;
    private readonly IHttpContextAccessor _httpContextAccessor;

    public AionLoggingMiddleware(RequestDelegate _next, IHttpContextAccessor httpContextAccessor)
    {
        this._next = _next;
        _httpContextAccessor = httpContextAccessor;
    }

    public async Task Invoke(HttpContext context, ITraceIdAccessor traceIdAccessor)
    {
        var correlationId = context.Request.Headers["X-Correlation-Id"];
        var paymentRequestId = context.Request.Headers["PaymentRequestId"];

        if (!string.IsNullOrEmpty(correlationId))
            traceIdAccessor.TraceId = correlationId;

        if (string.IsNullOrEmpty(correlationId))
            correlationId = _httpContextAccessor.HttpContext!.TraceIdentifier;

        using (GlobalLogContext.PushProperty("Path", context.Request.Path))
        using (GlobalLogContext.PushProperty("Method", context.Request.Method))
        using (GlobalLogContext.PushProperty("PaymentRequestId", paymentRequestId))
        using (GlobalLogContext.PushProperty("BlueTapeCorrelationId", correlationId))
        {
            await _next.Invoke(context);
        }
    }
}