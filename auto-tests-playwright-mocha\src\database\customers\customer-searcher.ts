import {dbSearcher} from "../db-searcher";
import {ObjectId} from "mongodb";

export async function searchCustomerByObjectId(customerObjectId: string) {
    const customerCollection = await dbSearcher('customeraccounts');

    const query = {'_id': new ObjectId(customerObjectId)}

    return await customerCollection.findOne(query)
}

/**
 * Search for a customer by email address where name exists
 * @param email - The customer's email address
 * @returns The customer account document with a name property, or null if not found
 */
export async function searchCustomerByEmail(email: string) {
    const customerCollection = await dbSearcher('customeraccounts');

    // Query for customers with the given email where name exists
    const query = { 
        'email': email,
        //'name': { $exists: true, $ne: null, $ne: '' }
    };

    // First try to get a customer with name field that exists and is not empty
    const customerWithName = await customerCollection.findOne(query);
    
    if (customerWithName) {
        return customerWithName;
    }
    
    // Fallback: if no customer with name exists, just get any customer with this email
    return await customerCollection.findOne({ 'email': email });
}

/**
 * Get customer name by email address
 * @param email - The customer's email address
 * @returns The customer name or empty string if customer not found
 */
export async function getCustomerNameByEmail(email: string): Promise<string> {
    try {
        const customer = await searchCustomerByEmail(email);
        if (customer && customer.name) {
            return customer.name;
        }
        return '';
    } catch (error) {
        console.error(`Error retrieving customer name for email ${email}:`, error);
        return '';
    }
}

/**
 * Get supplier ID by email address by looking up user sub and company ID in userroles
 * @param email - The supplier's email address
 * @returns The supplier ID (company ID) or empty string if not found
 */

// Remove it later
export async function getCompanyIdByEmail(email: string): Promise<string> {
    try {
        // Step 1: Find the user by email in users collection
        const usersCollection = await dbSearcher('users');
        // Case insensitive regex search for email or login
        const user = await usersCollection.findOne({
            $or: [
              { email: { $regex: new RegExp(`^${email}$`, 'i') } },
              { login: { $regex: new RegExp(`^${email}$`, 'i') } }
            ]
          });
        
        if (!user || !user.sub) {
            console.error(`No user found with email ${email} or missing sub field`);
            return '';
        }
        
        // Step 2: Get the user's sub ID
        const userSub = user.sub;
        
        // Step 3: Find the user role in the userroles collection using the sub
        const userRolesCollection = await dbSearcher('userroles');
        const userRole = await userRolesCollection.findOne({ sub: userSub });
        
        if (!userRole || !userRole.companyId) {
            console.error(`No user role found for sub ${userSub} or missing companyId`);
            return '';
        }
        
        // Step 4: Return the company ID from the user role
        return userRole.companyId.toString();
    } catch (error) {
        console.error(`Error retrieving supplier ID for email ${email}:`, error);
        return '';
    }
}
