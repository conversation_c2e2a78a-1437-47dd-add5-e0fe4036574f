﻿import mongoose, { Document, Schema, Model } from 'mongoose';

// Define the action sub-document interface
export class Action {
    description: string;
}

// Define the main test report interface that extends mongoose.Document
export class ITestReport extends Document {
    createdAt: Date;
    testGroup: string;
    testName: string;
    result: boolean;
    testGroupId: string; // GUID as string
    action: Action[]; // Define action as an array of Action
}

// Define the Action Schema
const actionSchema = new Schema<Action>({
    description: { type: String, required: true },
});

// Define the main Test Report Schema
const testReportSchema = new Schema<ITestReport>({
    createdAt: { type: Date, default: Date.now },
    testGroup: { type: String, required: true },
    testName: { type: String, required: true },
    result: { type: Boolean, required: true },
    testGroupId: { type: String, required: true },
    action: [actionSchema]
});

// Create and export the model
const TestReport: Model<ITestReport> = mongoose.model<ITestReport>('TestReport', testReportSchema);

export default TestReport;
