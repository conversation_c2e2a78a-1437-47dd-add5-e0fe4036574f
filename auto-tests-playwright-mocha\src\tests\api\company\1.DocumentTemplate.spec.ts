import {expect} from '@playwright/test';
import {test} from '../../test-utils';
import {sendCompanyRequest} from '../../../api/common/company-request';

test.describe(`Document Templates. CRUD API Tests @docs @API`, async () => {

    const documentTemplateTypes: string[] = ["BNPL_AGREEMENT", "MASTER_AGREEMENT"];
    const templateId = '358f802b-411e-4269-8edc-54b3246881f6'; // hardcode should be updated after new DELETE endroint is ready
    const invalidTemplateId = '358f802b-411e-4269-8edc-54b3246881f7';
    let lastSemanticVersion: string;
    const currentDate: string = new Date().toISOString();

    test(`Get all Versions of Templates @docs`, async () => {
        const response = await sendCompanyRequest('get', `documents/templates/allversions`);

        expect(response.status, `Status code 200`).toEqual(200);

        expect(response.data,
            `Response contains Array of Template's Versions`).toEqual(expect.any(Array));
    });

    for (const type of documentTemplateTypes) {

        test(`Get the last Template version with ${type} type @docs`, async () => {
            const response = await sendCompanyRequest('get', `documents/templates?code=${type}`);

            expect(response.status, `Status code 200`).toEqual(200);

            expect(response.data,
                `Response contains Object`).toEqual(expect.any(Object));

            lastSemanticVersion = response.data.semanticVersion;
        });

        // Failed for Now because Pavlo will prepare endpoint to delete generated templates
        test.skip(`Create new Template Version that is 1 higher than the current one for ${type} type @docs`, async () => {
            const requestBody = {
                'code': `${type}`,
                'filePath': 'contracts/bnpl_agreement_template.docx',
                'semanticVersion': `${parseFloat(lastSemanticVersion) + 1}.0`,
                'validFrom': `${currentDate}`
            };
            const response = await sendCompanyRequest('post', `documents/templates`, requestBody);

            expect(response.status, `Status code 201`).toEqual(201);

            expect(response.data,
                `Response contains Object`).toEqual(expect.any(Object));
        });
    }

    test(`Get Document Template by TemplateID. @docs`, async () => {
        const response = await sendCompanyRequest('get', `documents/templates/${templateId}`); // templateId should be dynamic

        expect(response.status, `Status code 200`).toEqual(200);

        expect(response.data,
            `Response contains Object`).toEqual(expect.any(Object));

        expect(response.data.code,
            `Template type is equal to 'BNPL_AGREEMENT'`).toBe(documentTemplateTypes[0]);
    });

    // Negative Tests

    test(`Cannot create Template with Semantic Version that already exist @docs`, async () => {
        const theLastVersionResponse = await sendCompanyRequest('get', `documents/templates?code=${documentTemplateTypes[0]}`);
        const currectSemanticVersion = theLastVersionResponse.data.semanticVersion;
        const requestBody = {
            'code': `${documentTemplateTypes[0]}`,
            'filePath': 'contracts/bnpl_agreement_template.docx',
            'semanticVersion': `${currectSemanticVersion}`,
            'validFrom': `${currentDate}`
        };
        const response = await sendCompanyRequest('post', `documents/templates`, requestBody);

        expect(response.response.status, `Status code 400`).toEqual(400);
    });

    test(`'204 No Content' returned if Template Id doesn't exist @docs`, async () => {
        const response = await sendCompanyRequest('get', `documents/templates/${invalidTemplateId}`);

        expect(response.status, `Status code 204`).toEqual(204);
    });
});
