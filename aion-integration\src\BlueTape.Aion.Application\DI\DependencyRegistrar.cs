﻿using Amazon.SimpleNotificationService;
using BlueTape.Aion.Application.Abstractions;
using BlueTape.Aion.Application.Abstractions.MessageSenders;
using BlueTape.Aion.Application.Mappers;
using BlueTape.Aion.Application.MessageSenders;
using BlueTape.Aion.Application.Service;
using BlueTape.Aion.DataAccess.External.DI;
using BlueTape.Aion.DataAccess.MongoDB.DI;
using BlueTape.AWSS3.Extensions;
using BlueTape.CompanyClient.DI;
using BlueTape.Integrations.Aion.AzureTableStorage.DI;
using BlueTape.Services.Utilities.AWS;
using BlueTape.SNS.SlackNotification.Extensions;
using BlueTape.Utilities.Providers;
using BueTape.Aion.Infrastructure.Options;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;

namespace BlueTape.Aion.Application.DI
{
    public static class DependencyRegistrar
    {
        public static void AddApplicationDependencies(this IServiceCollection services, IConfiguration config)
        {
            services.AddAutoMapper(typeof(ApplicationProfile));
            services.AddDataAccessExternalDependencies(config);
            services.AddDataAccessMongoDbDependencies();
            services.AddTransient<IAchService, AchService>();
            services.AddTransient<ICompanyService, Service.CompanyService>();
            services.AddTransient<IBankAccountService, BankAccountService>();
            services.AddTransient<IKmsDecryptorService, KmsDecryptorService>();
            services.AddTransient<IReportService, ReportService>();
            services.AddTransient<IInternalTransferService, InternalTransferService>();
            services.AddTransient<ITransactionService, TransactionService>();
            services.AddTransient<IHelperService, HelperService>();
            services.AddTransient<IDailyAchProcessingService, DailyAchProcessingService>();

            services.AddTransient<IAionLimitService, AionLimitService>();

            services.AddCompanyServiceClient(config);

            services.AddScoped<IDateProvider, DateProvider>();

            services.AddSnsSlackNotifications();
            services.AddTransient<IErrorNotificationService, ErrorNotificationService>();
            services.AddAWSService<IAmazonSimpleNotificationService>();

            services.AddS3Client();

            services.Configure<AionReportOptions>(config.GetSection(AionReportOptions.SectionName));
            services.Configure<AionInternalTransferOptions>(config.GetSection(AionInternalTransferOptions.SectionName));
            services.Configure<DailyAchProcessingOptions>(config.GetSection(DailyAchProcessingOptions.SectionName));

            services.AddAzureDataTableDependencies(config);

            services.AddScoped<IAionInternalTransactionMessageSender, AionInternalTransactionMessageSender>();
            services.AddScoped<IAionExternalTransactionMessageSender, AionExternalTransactionMessageSender>();
        }
    }
}