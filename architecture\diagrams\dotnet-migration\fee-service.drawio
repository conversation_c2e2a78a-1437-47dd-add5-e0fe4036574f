<mxfile host="app.diagrams.net" modified="2023-06-21T05:56:57.835Z" agent="Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36" etag="iq78DV5sS8fhMOHMufC_" version="21.5.0" type="google">
  <diagram name="Page-1" id="DxsnQ8dKYRf8VqsKn0XC">
    <mxGraphModel grid="1" page="1" gridSize="10" guides="1" tooltips="1" connect="1" arrows="1" fold="1" pageScale="1" pageWidth="1169" pageHeight="827" math="0" shadow="0">
      <root>
        <mxCell id="0" />
        <mxCell id="1" parent="0" />
        <mxCell id="Vv0EZMYSqh_iPp7z7qcc-36" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" edge="1" parent="1" source="Vv0EZMYSqh_iPp7z7qcc-33">
          <mxGeometry relative="1" as="geometry">
            <mxPoint x="71" y="310" as="targetPoint" />
            <Array as="points">
              <mxPoint x="71" y="70" />
              <mxPoint x="71" y="310" />
            </Array>
          </mxGeometry>
        </mxCell>
        <mxCell id="Vv0EZMYSqh_iPp7z7qcc-4" value="" style="group" connectable="0" vertex="1" parent="1">
          <mxGeometry x="294" y="230" width="60" height="80" as="geometry" />
        </mxCell>
        <mxCell id="Vv0EZMYSqh_iPp7z7qcc-1" value="" style="sketch=0;outlineConnect=0;fontColor=#232F3E;gradientColor=none;fillColor=#D45B07;strokeColor=none;dashed=0;verticalLabelPosition=bottom;verticalAlign=top;align=center;html=1;fontSize=12;fontStyle=0;aspect=fixed;pointerEvents=1;shape=mxgraph.aws4.lambda_function;" vertex="1" parent="Vv0EZMYSqh_iPp7z7qcc-4">
          <mxGeometry x="6" width="48" height="48" as="geometry" />
        </mxCell>
        <mxCell id="Vv0EZMYSqh_iPp7z7qcc-3" value="API" style="text;html=1;strokeColor=none;fillColor=none;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;" vertex="1" parent="Vv0EZMYSqh_iPp7z7qcc-4">
          <mxGeometry y="50" width="60" height="30" as="geometry" />
        </mxCell>
        <mxCell id="Vv0EZMYSqh_iPp7z7qcc-5" value="" style="group" connectable="0" vertex="1" parent="1">
          <mxGeometry x="295" y="306" width="60" height="80" as="geometry" />
        </mxCell>
        <mxCell id="Vv0EZMYSqh_iPp7z7qcc-6" value="" style="sketch=0;outlineConnect=0;fontColor=#232F3E;gradientColor=none;fillColor=#D45B07;strokeColor=none;dashed=0;verticalLabelPosition=bottom;verticalAlign=top;align=center;html=1;fontSize=12;fontStyle=0;aspect=fixed;pointerEvents=1;shape=mxgraph.aws4.lambda_function;" vertex="1" parent="Vv0EZMYSqh_iPp7z7qcc-5">
          <mxGeometry x="6" width="48" height="48" as="geometry" />
        </mxCell>
        <mxCell id="Vv0EZMYSqh_iPp7z7qcc-7" value="Events&lt;br&gt;processor" style="text;html=1;strokeColor=none;fillColor=none;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;" vertex="1" parent="Vv0EZMYSqh_iPp7z7qcc-5">
          <mxGeometry y="50" width="60" height="30" as="geometry" />
        </mxCell>
        <mxCell id="Vv0EZMYSqh_iPp7z7qcc-12" value="" style="group" connectable="0" vertex="1" parent="1">
          <mxGeometry x="171" y="315" width="60" height="60" as="geometry" />
        </mxCell>
        <mxCell id="Vv0EZMYSqh_iPp7z7qcc-9" value="" style="sketch=0;outlineConnect=0;fontColor=#232F3E;gradientColor=none;fillColor=#B0084D;strokeColor=none;dashed=0;verticalLabelPosition=bottom;verticalAlign=top;align=center;html=1;fontSize=12;fontStyle=0;aspect=fixed;pointerEvents=1;shape=mxgraph.aws4.queue;" vertex="1" parent="Vv0EZMYSqh_iPp7z7qcc-12">
          <mxGeometry x="5.100000000000023" width="49.79" height="30" as="geometry" />
        </mxCell>
        <mxCell id="Vv0EZMYSqh_iPp7z7qcc-10" value="Fee SQS" style="text;html=1;strokeColor=none;fillColor=none;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;" vertex="1" parent="Vv0EZMYSqh_iPp7z7qcc-12">
          <mxGeometry y="30" width="60" height="30" as="geometry" />
        </mxCell>
        <mxCell id="Vv0EZMYSqh_iPp7z7qcc-14" value="" style="group" connectable="0" vertex="1" parent="1">
          <mxGeometry x="51" y="310" width="60" height="70" as="geometry" />
        </mxCell>
        <mxCell id="Vv0EZMYSqh_iPp7z7qcc-11" value="" style="sketch=0;points=[[0,0,0],[0.25,0,0],[0.5,0,0],[0.75,0,0],[1,0,0],[0,1,0],[0.25,1,0],[0.5,1,0],[0.75,1,0],[1,1,0],[0,0.25,0],[0,0.5,0],[0,0.75,0],[1,0.25,0],[1,0.5,0],[1,0.75,0]];outlineConnect=0;fontColor=#232F3E;gradientColor=#FF4F8B;gradientDirection=north;fillColor=#BC1356;strokeColor=#ffffff;dashed=0;verticalLabelPosition=bottom;verticalAlign=top;align=center;html=1;fontSize=12;fontStyle=0;aspect=fixed;shape=mxgraph.aws4.resourceIcon;resIcon=mxgraph.aws4.eventbridge;" vertex="1" parent="Vv0EZMYSqh_iPp7z7qcc-14">
          <mxGeometry x="10" width="40" height="40" as="geometry" />
        </mxCell>
        <mxCell id="Vv0EZMYSqh_iPp7z7qcc-13" value="EventBridge" style="text;html=1;strokeColor=none;fillColor=none;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;" vertex="1" parent="Vv0EZMYSqh_iPp7z7qcc-14">
          <mxGeometry y="40" width="60" height="30" as="geometry" />
        </mxCell>
        <mxCell id="Vv0EZMYSqh_iPp7z7qcc-17" value="" style="group" connectable="0" vertex="1" parent="1">
          <mxGeometry x="294" y="120" width="60" height="70" as="geometry" />
        </mxCell>
        <mxCell id="Vv0EZMYSqh_iPp7z7qcc-15" value="" style="sketch=0;points=[[0,0,0],[0.25,0,0],[0.5,0,0],[0.75,0,0],[1,0,0],[0,1,0],[0.25,1,0],[0.5,1,0],[0.75,1,0],[1,1,0],[0,0.25,0],[0,0.5,0],[0,0.75,0],[1,0.25,0],[1,0.5,0],[1,0.75,0]];outlineConnect=0;fontColor=#232F3E;gradientColor=#FF4F8B;gradientDirection=north;fillColor=#BC1356;strokeColor=#ffffff;dashed=0;verticalLabelPosition=bottom;verticalAlign=top;align=center;html=1;fontSize=12;fontStyle=0;aspect=fixed;shape=mxgraph.aws4.resourceIcon;resIcon=mxgraph.aws4.api_gateway;" vertex="1" parent="Vv0EZMYSqh_iPp7z7qcc-17">
          <mxGeometry x="10" width="40" height="40" as="geometry" />
        </mxCell>
        <mxCell id="Vv0EZMYSqh_iPp7z7qcc-16" value="AGW" style="text;html=1;strokeColor=none;fillColor=none;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;" vertex="1" parent="Vv0EZMYSqh_iPp7z7qcc-17">
          <mxGeometry y="40" width="60" height="30" as="geometry" />
        </mxCell>
        <mxCell id="Vv0EZMYSqh_iPp7z7qcc-19" value="&#xa;&#xa;&#xa;      Fee&#xa;Service" style="swimlane;startSize=0;dashed=1;dashPattern=12 12;align=left;" vertex="1" parent="1">
          <mxGeometry x="256" y="200" width="140" height="200" as="geometry">
            <mxRectangle x="515" y="180" width="80" height="90" as="alternateBounds" />
          </mxGeometry>
        </mxCell>
        <mxCell id="Vv0EZMYSqh_iPp7z7qcc-28" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" edge="1" parent="1" source="Vv0EZMYSqh_iPp7z7qcc-24">
          <mxGeometry relative="1" as="geometry">
            <mxPoint x="91" y="310" as="targetPoint" />
            <Array as="points">
              <mxPoint x="91" y="310" />
            </Array>
          </mxGeometry>
        </mxCell>
        <mxCell id="Vv0EZMYSqh_iPp7z7qcc-25" value="" style="group;fillColor=#f5f5f5;fontColor=#333333;strokeColor=#666666;" connectable="0" vertex="1" parent="1">
          <mxGeometry x="20" y="190" width="122" height="90" as="geometry" />
        </mxCell>
        <mxCell id="Vv0EZMYSqh_iPp7z7qcc-21" value="" style="sketch=0;outlineConnect=0;fontColor=#232F3E;gradientColor=none;fillColor=#D45B07;strokeColor=none;dashed=0;verticalLabelPosition=bottom;verticalAlign=top;align=center;html=1;fontSize=12;fontStyle=0;aspect=fixed;pointerEvents=1;shape=mxgraph.aws4.lambda_function;" vertex="1" parent="Vv0EZMYSqh_iPp7z7qcc-25">
          <mxGeometry x="66" width="48" height="48" as="geometry" />
        </mxCell>
        <mxCell id="Vv0EZMYSqh_iPp7z7qcc-23" value="" style="sketch=0;outlineConnect=0;fontColor=#232F3E;gradientColor=none;fillColor=#D45B07;strokeColor=none;dashed=0;verticalLabelPosition=bottom;verticalAlign=top;align=center;html=1;fontSize=12;fontStyle=0;aspect=fixed;pointerEvents=1;shape=mxgraph.aws4.lambda_function;" vertex="1" parent="Vv0EZMYSqh_iPp7z7qcc-25">
          <mxGeometry x="10" width="48" height="48" as="geometry" />
        </mxCell>
        <mxCell id="Vv0EZMYSqh_iPp7z7qcc-24" value="Scheduled Jobs&lt;br&gt;(monthly+daily fees)" style="text;html=1;strokeColor=none;fillColor=none;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;" vertex="1" parent="Vv0EZMYSqh_iPp7z7qcc-25">
          <mxGeometry y="48" width="122" height="42" as="geometry" />
        </mxCell>
        <mxCell id="Vv0EZMYSqh_iPp7z7qcc-29" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" edge="1" parent="1" source="Vv0EZMYSqh_iPp7z7qcc-11" target="Vv0EZMYSqh_iPp7z7qcc-9">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="Vv0EZMYSqh_iPp7z7qcc-30" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" edge="1" parent="1" source="Vv0EZMYSqh_iPp7z7qcc-9" target="Vv0EZMYSqh_iPp7z7qcc-6">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="Vv0EZMYSqh_iPp7z7qcc-31" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" edge="1" parent="1" source="Vv0EZMYSqh_iPp7z7qcc-21" target="Vv0EZMYSqh_iPp7z7qcc-1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="Vv0EZMYSqh_iPp7z7qcc-32" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;exitX=0.5;exitY=1;exitDx=0;exitDy=0;" edge="1" parent="1" source="Vv0EZMYSqh_iPp7z7qcc-16" target="Vv0EZMYSqh_iPp7z7qcc-1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="Vv0EZMYSqh_iPp7z7qcc-34" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;entryX=0.5;entryY=0;entryDx=0;entryDy=0;entryPerimeter=0;" edge="1" parent="1" source="Vv0EZMYSqh_iPp7z7qcc-33" target="Vv0EZMYSqh_iPp7z7qcc-15">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="Vv0EZMYSqh_iPp7z7qcc-33" value="1st parties" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#dae8fc;strokeColor=#6c8ebf;fontColor=#000066;" vertex="1" parent="1">
          <mxGeometry x="284.25" y="50" width="79.5" height="40" as="geometry" />
        </mxCell>
        <mxCell id="Vv0EZMYSqh_iPp7z7qcc-35" value="Read operations" style="text;html=1;strokeColor=none;fillColor=none;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;" vertex="1" parent="1">
          <mxGeometry x="311" y="90" width="130" height="30" as="geometry" />
        </mxCell>
        <mxCell id="Vv0EZMYSqh_iPp7z7qcc-37" value="Occasional Fee events" style="text;html=1;strokeColor=none;fillColor=none;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;" vertex="1" parent="1">
          <mxGeometry x="131" y="72" width="79" height="30" as="geometry" />
        </mxCell>
        <mxCell id="Vv0EZMYSqh_iPp7z7qcc-38" value="Fee Service" style="text;html=1;strokeColor=none;fillColor=none;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=15;" vertex="1" parent="1">
          <mxGeometry x="10" y="10" width="110" height="30" as="geometry" />
        </mxCell>
        <mxCell id="tZoxreGJt63OsR3ysJBP-1" value="3rd parties" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#bac8d3;strokeColor=#23445d;fontColor=#FFFFFF;labelBorderColor=none;labelBackgroundColor=none;fontStyle=0" vertex="1" parent="1">
          <mxGeometry x="441" y="233" width="79.5" height="40" as="geometry" />
        </mxCell>
        <mxCell id="tZoxreGJt63OsR3ysJBP-2" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;dashed=1;dashPattern=1 2;" edge="1" parent="1" source="Vv0EZMYSqh_iPp7z7qcc-1" target="tZoxreGJt63OsR3ysJBP-1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
      </root>
    </mxGraphModel>
  </diagram>
</mxfile>
