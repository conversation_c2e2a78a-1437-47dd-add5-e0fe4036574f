import {Page, expect} from '@playwright/test';
import {test, BaseTest} from '../../../test-utils';
import {PageManager} from '../../../../objects/pages/page-manager';
import {BaseAPI} from '../../../../api/base-api';
import {deleteUser, getUserSub} from "../../../../api/admin";

test.use({storageState: {cookies: [], origins: []}});

test.describe('Sign up e2e tests.', async () => {
    let page: Page;
    let email: string;
    let firstName: string;
    let lastName: string;
    let businessName: string;
    let accountName: string;

    test.beforeEach(async ({browser}) => {
        email = `automation_user+${BaseTest.dateTimePrefix()}@bluetape.com`;
        firstName = BaseTest.dateTimePrefix() + 'firstName';
        lastName = BaseTest.dateTimePrefix() + 'lastName';
        businessName = BaseTest.dateTimePrefix() + 'businessName';
        accountName = BaseTest.dateTimePrefix() + 'accountName';

        const googleIDToken = await BaseAPI.googleSignUp(email, BaseTest.constants.password);
        if (googleIDToken === null) {
            test.fail(true, 'idToken is empty.');
        }
        await BaseAPI.userSignUp(businessName, email, firstName, googleIDToken, lastName, BaseTest.constants.user.cellPhoneNumber);

        page = await browser.newPage();
        await BaseTest.verificationLinkSignIn(page, email);
    });

    test.afterEach(async ({adminIdToken}) => {
        const userSub = await getUserSub(adminIdToken, email);
        await deleteUser(adminIdToken, userSub);
        await page.close();
    });

    test.skip(`Success Dealer/Retailer/Supplier sign up. Submit Partnership application`, async () => {
        // test skipped due to problem with locators
        const pageManager = new PageManager(page);
        await pageManager.loginPage.login(email, BaseTest.constants.password);
        await pageManager.onBoardingPage.chooseRole('Dealer / Retailer / Supplier');
        await pageManager.onBoardingPage.buttons.setupAccount.click();

        await expect(pageManager.home.labels.emailVerified,
            'Email is verified on the main page should be visible.').toBeVisible();

        await expect(pageManager.partnershipApplication.businessOwnerInformation.firstName,
            `First name field should be pre-filled with ${firstName}`).toHaveValue(firstName);

        await expect(pageManager.partnershipApplication.businessOwnerInformation.lastName,
            `Last name field should be pre-filled with ${lastName}`).toHaveValue(lastName);

        await expect(pageManager.partnershipApplication.businessOwnerInformation.emailAddress,
            `Email address field should be pre-filled with ${email}`).toHaveValue(email);

        await expect(pageManager.partnershipApplication.businessOwnerInformation.phoneNumber,
            `Cell phone number field should be pre-filled with ${BaseTest.constants.user.cellPhoneNumber}`).toHaveValue(BaseTest.constants.user.cellPhoneNumber);

        await pageManager.partnershipApplication.fillUpBusinessDetails(businessName, '12/2000', '666111111');

        await pageManager.partnershipApplication
            .fillUpBusinessAddress(BaseTest.constants.address.street, BaseTest.constants.address.city, BaseTest.constants.address.zipCode, BaseTest.constants.address.state, BaseTest.constants.user.cellPhoneNumber);

        await pageManager.partnershipApplication
            .fillUpBusinessOwnerInformation('100', BaseTest.constants.address.street, BaseTest.constants.address.city, BaseTest.constants.address.zipCode, BaseTest.constants.address.state, '********', BaseTest.constants.user.socialSecurityNumber);

        await pageManager.partnershipApplication
            .fillUpBankInformation(BaseTest.constants.bankAccount.bankName, accountName, BaseTest.constants.bankAccount.accountNumber, BaseTest.constants.bankAccount.routingNumber);

        await pageManager.buyNowPayLater.fillUpBuyNowPayLater();

        await expect(pageManager.home.labels.applicationPendingApproval,
            `Application pengind approval label on the main page should be visible.`).toBeVisible();

        await page.goto(`${process.env.CI_ENVIRONMENT_BACKEND_URL}`);
        await pageManager.backOfficeLoginPage.login(`${process.env.ADMIN_EMAIL}`, `${process.env.ADMIN_PASSWORD}`);
        await pageManager.backOfficeSideMenu.sideMenuTabs.supplierApplication.click();
        await pageManager.supplierApplication.clickOnSupplierApplication(email);
        await pageManager.supplierApplication.buttons.edit.click();
        // add data test-id for assertin inputs on the supplier application details modal
        await pageManager.supplierApplicationDetailsModal.buttons.approve.click();
    });
});
