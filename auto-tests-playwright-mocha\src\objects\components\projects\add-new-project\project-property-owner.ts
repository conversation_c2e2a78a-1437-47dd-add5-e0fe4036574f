import {Page} from "@playwright/test";
import {BasePage} from "../../../base.page";

export class ProjectPropertyOwner extends BasePage {
    constructor(page: Page) {
        super(page);
    };

    containers = {
        streetAddressContainer: this.page.locator('_react=[key=".0"]'),
    };

    buttons = {
        next: this.page.locator('_react=[testID="ProjectNextButton"]'),
        back: this.page.locator('_react=[testID="ProjectBackButton"]'),
        individualOwner: this.page.locator('[data-testid="propertyOwnerType.individual"]'),
        business: this.page.locator('[data-testid="propertyOwnerType.business"]'),
        addAnotherBusOrOwner: this.page.getByText('Add another business or owner')
    };

    input = {
        firstName: this.page.locator('[data-testid="propertyOwnerFirstname"] input'),
        lastName: this.page.locator('[data-testid="propertyOwnerLastname"] input'),
        phoneNumber: this.page.locator('[data-testid="contractorPhone"] input'),
        homeAddress: this.page.locator('//div[text()=\'Home Address\']/following-sibling::div//input'),
        businessName: this.page.locator('[data-testid="propertyOwnerBusinessName"] input'),
    };

    elements = {
        firstAddressInAddressList: this.containers.streetAddressContainer.locator('_react=t[key=".$0"]'),
    };

    async fillIndividualInfo(firstName: string, lastName: string, phoneNumber: string, homeAddress: string) {
        await this.input.firstName.fill(firstName);
        await this.input.lastName.fill(lastName);
        await this.input.phoneNumber.fill(phoneNumber);
        await this.input.homeAddress.type(homeAddress);
        //await this.input.homeAddress.click();
        await this.elements.firstAddressInAddressList.click();
    };
}
