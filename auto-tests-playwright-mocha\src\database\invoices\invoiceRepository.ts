import {client} from "../client";
import { ObjectId } from 'mongodb';

const collectionName = 'invoices'

export class InvoiceRepository {
    async getInvoiceById(invoiceId: string) {

        try {
            await client.connect();

            const database = client.db(`${process.env.test_env}`);
            const collection = database.collection(collectionName);

            const result = await collection
            .find({_id: new ObjectId(invoiceId)})
            .sort({ createdAt: -1 })
            .limit(1)
            .toArray();

        return result[0] || null;
        } catch (e) {
            console.error(e);
            return null;
        } finally {
            await client.close();
        }
    }

    async getInvoiceByNumber(invoiceNumber: string) {
        try {
            await client.connect();
            
            const database = client.db(`${process.env.test_env}`);
            const collection = database.collection(collectionName);
    
            const result = await collection
            .find({ invoice_number: invoiceNumber })
            .sort({ createdAt: -1 })
            .limit(1)
            .toArray();

        if (!result[0]) {
            console.warn(`Invoice with number ${invoiceNumber} not found.`);
        }
        
        return result[0] || null;
        } catch (e) {
            console.error("Error fetching invoice:", e);
            return null;  // Explicitly return null or handle as needed
        } finally {
            await client.close();
        }
    }
}
