import {expect} from '@playwright/test';
import {BaseTest, test} from '../../test-utils';
import {
    createInvoiceR,
    getCompanyCreditInfo,
    sendGenericApiRequest
} from "../../../api/common/send-generic-api-request";
import {searchCustomerByObjectId} from "../../../database/customers/customer-searcher";
import {generateBodyForGetCreditInfoAssertion} from "../../../utils/genericAPI-utils";
import {generateCompanyRequestBody} from "./genericAPI-request-body-functions-and-storage";

const constants = JSON.parse(JSON.stringify(require('../../../constants/constants.json')));

test.describe(`@genericR @GET @API @company`, async () => {
    let response: any;
    let connectorCustomerId: string;

    test.beforeAll(``, async () => {
        let currentDate = new Date().toISOString();

        let uniqueCustomerEmail = `sergei+genericapicustomer${BaseTest.dateTimePrefix()}@bluetape.com`;
        let uniqueCustomerPhone = BaseTest.getCellPhoneNumber();
        let uniqueCustomerId = `customerId_${BaseTest.getGUID()}`;

        let uniqueBusinessPhone = BaseTest.getCellPhoneNumber();
        let companyId = BaseTest.getGUID();

        const customerFirstName = constants["generic"].firstName;
        const customerLastName = constants["generic"].lastName;

        const companyRequestBody = await generateCompanyRequestBody({
            isCustomerUsed: true,
            companyId: companyId,
            businessPhoneNumber: uniqueBusinessPhone,
            customerEmailAddress: uniqueCustomerEmail,
            customerId: uniqueCustomerId,
            customerFirstName: customerFirstName,
            customerLastName: customerLastName,
            customerCellPhoneNumber: uniqueCustomerPhone,
        });

        response = await sendGenericApiRequest('post', 'company', companyRequestBody);
    });

    test(`Get credit info of new registered company`, async () => {
        const DBObject =
            await searchCustomerByObjectId(await response.data.customers[0]["blueTapeCustomerId"]);

        /**
         * Used in companyId field in getCompanyCreditInfo and in expect as externalCompanyId
         */
        connectorCustomerId = await DBObject["connector"].customer_id;

        /**
         * Used in expect as externalCustomerId
         */
        const connectorContactId = await DBObject["connector"].connector_contact_id;

        const getInfoResponse = await getCompanyCreditInfo(connectorCustomerId);

        expect(getInfoResponse.status).toEqual(200);

        expect(getInfoResponse.data).toEqual(expect.objectContaining(
            await generateBodyForGetCreditInfoAssertion(
                connectorCustomerId,
                connectorContactId
            )
        ));
    });


    /**
     * Hard and overweight test(s), but it's an only way to test all flow
     * @param supplierPageForGenericAPI - page where supplier create invoice and pay for it via BT credit
     * @param adminPage - admin page where statuses will be changed
     * And all the changes will be checked via GET request with connectorCustomerId as companyID
     *
     *
     * Will be added soon, skipped because of trouble with payment via card
     */
    test.skip(``, async ({supplierForGenericAPIPageManager, adminPageManager}) => {

    })
})
