﻿using AutoMapper;
using BlueTape.Company.Domain.DTOs.Audit;
using BlueTape.Company.Domain.DTOs.BankAccounts;
using BlueTape.Company.Domain.DTOs.BankAccounts.Giact;
using BlueTape.Company.Domain.DTOs.Companies;
using BlueTape.Company.Domain.DTOs.Customers;
using BlueTape.Company.Domain.DTOs.Drafts;
using BlueTape.Company.Domain.DTOs.GuestSuppliers;
using BlueTape.DataAccess.MongoDB.Entities.Audit;
using BlueTape.DataAccess.MongoDB.Entities.BankAccounts;
using BlueTape.DataAccess.MongoDB.Entities.BankAccounts.Giact;
using BlueTape.DataAccess.MongoDB.Entities.Company;
using BlueTape.DataAccess.MongoDB.Entities.Customer;
using BlueTape.DataAccess.MongoDB.Entities.Drafts;
using BlueTape.DataAccess.MongoDB.Entities.LoanApplication;
using BlueTape.DataAccess.MongoDB.Entities.Users;
using BlueTape.Document.Domain.DTOs.LoanApplication;
using MongoDB.Bson;

namespace BlueTape.DataAccess.MongoDB.Mappers;

public class MongoDbProfile : Profile
{
    public MongoDbProfile()
    {
        CreateMap<CreateCompanyDto, CompanyEntity>()
            .ForMember(x => x.BankAccounts, y =>
                y.MapFrom(z => z.BankAccounts != null ? z.BankAccounts!.Select(acc => new ObjectId(acc)) : new List<ObjectId>()));
        CreateMap<UpdateCompanyDto, CompanyEntity>()
            .ForMember(x => x.BankAccounts, y =>
                y.MapFrom(z => z.BankAccounts != null ? z.BankAccounts!.Select(acc => new ObjectId(acc)) : new List<ObjectId>()));
        CreateMap<CompanyDto, CompanyEntity>()
            .ForMember(x => x.BankAccounts, y =>
                y.MapFrom(z => z.BankAccounts.Select(acc => new ObjectId(acc))));
        CreateMap<CompanyEntity, CompanyDto>()
            .ForMember(x => x.BankAccounts, y =>
                y.MapFrom(z => z.BankAccounts.Select(acc => acc.ToString())))
            .ForMember(x => x.ContactName,
                y => y.MapFrom(z => z.ContactName ?? (z.Owner != null ? z.Owner.ContactName : null)));
        CreateMap<CompanyEntity, GuestSupplierDto>()
            .ForMember(x => x.BusinessName,
                y => y.MapFrom(z => !string.IsNullOrWhiteSpace(z.Name) ? z.Name : z.LegalName))
            .ForMember(x => x.ContactName,
                y => y.MapFrom(z => z.ContactName ?? (z.Owner != null ? z.Owner.ContactName : null)))
            .ForMember(x => x.Id, y => y.MapFrom(z => z.BlueTapeCompanyId));
        CreateMap<AddressEntity, AddressDto>().ReverseMap();
        CreateMap<CompanySettingsEntity, CompanySettingsDto>().ReverseMap();
        CreateMap<DepositDetailsEntity, DepositDetailsDto>().ReverseMap();
        CreateMap<EmailConfigurationEntity, EmailConfigurationDto>().ReverseMap();
        CreateMap<InvoiceLoanPlanEntity, InvoiceLoanPlanDto>().ReverseMap();
        CreateMap<RepaymentEntity, RepaymentDto>().ReverseMap();
        CreateMap<CompanyCreditDto, CompanyCreditEntity>().ReverseMap();
        CreateMap<CompanyAionSettingsEntity, CompanyAionSettingsDto>().ReverseMap();
        CreateMap<DirectTermsDto, DirectTermsEntity>().ReverseMap();
        CreateMap<DownPaymentDetailsDto, DownPaymentDetailsEntity>().ReverseMap();

        CreateMap<AccountStatusChangeAuditEntity, AccountStatusChangeAuditDto>().ReverseMap();

        CreateMap<BankAccountEntity, BankAccountDto>()
            .ForMember(dest => dest.Network, opt =>
            {
                opt.PreCondition(src => src.CardMetadata != null);
                opt.MapFrom(src => src.CardMetadata!.Network);
            })
            .ForMember(dest => dest.Type, opt =>
            {
                opt.PreCondition(src => src.CardMetadata != null);
                opt.MapFrom(src => src.CardMetadata!.Type);
            })
            .ForMember(dest => dest.Response, opt =>
            {
                opt.PreCondition(src => src.CardMetadata != null);
                opt.MapFrom(src => src.CardMetadata!.Response);
            })
            .ReverseMap();
        CreateMap<BankAccountPlaidDto, BankAccountPlaidEntity>();
        CreateMap<BankAccountPlaidEntity, BankAccountPlaidDto>()
            .ForMember(x => x.IncludeInCashFlow,
                opt => opt.MapFrom(y => y.IncludeInCashFlow ?? true));
        CreateMap<BankAccountAionSettingsEntity, BankAccountAionSettingsDto>().ReverseMap();
        CreateMap<BankAccountIdentityEntity, BankAccountIdentityDto>().ReverseMap();

        CreateMap<AccessTokenEntity, AccessTokenDto>().ReverseMap();
        CreateMap<AccountNumberEntity, AccountNumberDto>().ReverseMap();
        CreateMap<BillingAddressEntity, BillingAddressDto>().ReverseMap();

        CreateMap<CustomerConnectorEntity, CustomerConnectorDto>().ReverseMap();

        CreateMap<CreditInfoDto, CreditInfoEntity>().ReverseMap();

        CreateMap<CustomerDto, CustomerEntity>()
            .ForMember(x => x.Phone, y => y.MapFrom(z => z.CellPhoneNumber))
            .ForMember(x => x.Address, y => y.MapFrom(z => z.BusinessAddress))
            .ForMember(x => x.Name, y => y.Ignore())
            .ForMember(x => x.BusinessPhone, y => y.MapFrom(z => z.BusinessPhoneNumber));

        CreateMap<CustomerEntity, CustomerDto>()
            .ForMember(x => x.CellPhoneNumber, y => y.MapFrom(z => z.Phone))
            .ForMember(x => x.BusinessAddress, y => y.MapFrom(z => z.Address))
            .ForMember(x => x.BusinessPhoneNumber, y => y.MapFrom(z => z.BusinessPhone));

        CreateMap<CustomerSettingsEntity, CustomerSettingsDto>().ReverseMap();
        CreateMap<CustomerInHouseCreditDto, CustomerInHouseCreditEntity>().ReverseMap();
        CreateMap<CompanyInHouseCreditDto, CompanyInHouseCreditEntity>().ReverseMap();

        CreateMap<ArAdvanceEntity, ArAdvanceDto>().ReverseMap();
        CreateMap<AutomatedDrawApprovalEntity, AutomatedDrawApprovalDto>().ReverseMap();

        CreateMap<LoanApplicationEntity, LoanApplicationDto>();

        CreateMap<BankAccountGiactEntity, BankAccountGiactDto>().ReverseMap();
        CreateMap<GiactAccountEntity, GiactAccountDto>()
            .ForMember(x => x.Id, y => y.MapFrom(z => z.AccountId)).ReverseMap();
        CreateMap<GiactVerificatioEntity, GiactVerificationDto>().ReverseMap();
        CreateMap<GiactAuthenticationEntity, GiactAuthenticationDto>().ReverseMap();
        CreateMap<GiactResponseEntity, GiactResponseDto>().ReverseMap();

        CreateMap<UserEntity, UserDto>().ReverseMap();
        CreateMap<UserRoleEntity, UserRoleDto>().ReverseMap();
        CreateMap<UserRoleSettingsEntity, UserRoleSettingsDto>().ReverseMap();

        CreateMap<DraftEntity, DraftDto>().ReverseMap();
        CreateMap<DraftDataEntity, DraftDataDto>().ReverseMap();
        CreateMap<BusinessInfoEntity, BusinessInfoDto>().ReverseMap();
        CreateMap<BusinessInfoItemEntity, BusinessInfoItemDto>()
            .ForMember(dest => dest.Content, opt => opt.MapFrom(src => ConvertBsonValueToObject(src.Content)))
            .ReverseMap()
            .ForMember(dest => dest.Content, opt => opt.MapFrom(src => ConvertObjectToBsonValue(src.Content)));
    }

    private static object? ConvertBsonValueToObject(BsonValue? bsonValue)
    {
        if (bsonValue == null || bsonValue.IsBsonNull)
            return null;

        return bsonValue.BsonType switch
        {
            BsonType.Document => bsonValue.AsBsonDocument.ToDictionary(),
            BsonType.String => bsonValue.AsString,
            BsonType.Int32 => bsonValue.AsInt32,
            BsonType.Int64 => bsonValue.AsInt64,
            BsonType.Double => bsonValue.AsDouble,
            BsonType.Boolean => bsonValue.AsBoolean,
            BsonType.DateTime => bsonValue.ToUniversalTime(),
            BsonType.Array => bsonValue.AsBsonArray.Select(ConvertBsonValueToObject).ToList(),
            _ => bsonValue.ToString()
        };
    }

    private static BsonValue? ConvertObjectToBsonValue(object? obj)
    {
        if (obj == null)
            return BsonNull.Value;

        return obj switch
        {
            Dictionary<string, object> dict => new BsonDocument(dict.ToDictionary(kvp => kvp.Key, kvp => ConvertObjectToBsonValue(kvp.Value))),
            string str => new BsonString(str),
            int i => new BsonInt32(i),
            long l => new BsonInt64(l),
            double d => new BsonDouble(d),
            bool b => new BsonBoolean(b),
            DateTime dt => new BsonDateTime(dt),
            IEnumerable<object> list => new BsonArray(list.Select(ConvertObjectToBsonValue)),
            _ => new BsonString(obj.ToString() ?? "")
        };
    }
}