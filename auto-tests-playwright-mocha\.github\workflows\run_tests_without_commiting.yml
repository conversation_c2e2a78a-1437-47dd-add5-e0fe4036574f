name: Run on beta without commiting

on:
  workflow_dispatch:
  schedule:
    - cron: '0 3 * * *'
jobs:
  regress-beta:
    timeout-minutes: 120
    environment: DEV
    runs-on: ubuntu-latest
    container:
      image: mcr.microsoft.com/playwright:v1.37.1-jammy
    env:
      SLACK_WEBHOOK_URL: ${{ vars.SLACK_WEBHOOK_URL }}
      CI_ENVIRONMENT_URL: ${{ vars.CI_ENVIRONMENT_URL }}
      CI_ENVIRONMENT_BACKEND_URL: ${{ vars.CI_ENVIRONMENT_BACKEND_URL }}
      ADMIN_BACKEND_URL: ${{ vars.ADMIN_BACKEND_URL }}
      ADMIN_EMAIL: ${{ vars.ADMIN_EMAIL }}
      ADMIN_PASSWORD: ${{ vars.ADMIN_PASSWORD }}
      ADMIN_FIRSTNAME: ${{ vars.ADMIN_FIRSTNAME }}
      ADMIN_LASTNAME: ${{ vars.ADMIN_LASTNAME }}
      USER_DOMAIN: ${{ vars.USER_DOMAIN }}
      USER_BACKEND_URL: ${{ vars.USER_BACKEND_URL }}
      USER_EMAIL: ${{ vars.USER_EMAIL }}
      USER_PASSWORD: ${{ vars.USER_PASSWORD }}
      USER_FIRSTNAME: ${{ vars.USER_FIRSTNAME }}
      USER_LASTNAME: ${{ vars.USER_LASTNAME }}
      USER_COMPANYNAME: ${{ vars.USER_COMPANYNAME }}
      USER_CUSTOMEREMAIL: ${{ vars.USER_CUSTOMEREMAIL }}
      USER_CUSTOMERPASSWORD: ${{ vars.USER_CUSTOMERPASSWORD }}
      USER_CUSTOMERID: ${{ vars.USER_CUSTOMERID }}
      USER_CUSTOMER_COMPANYNAME: ${{ vars.USER_CUSTOMER_COMPANYNAME }}
      GENERIC_API_BASE_URL: ${{ vars.GENERIC_API_BASE_URL }}
      X_BLUETAPE_KEY: ${{ vars.X_BLUETAPE_KEY }}
      X_INTEGRATION_ACCOUNT_ID: ${{ vars.X_INTEGRATION_ACCOUNT_ID }}
      MONGODB_URI: ${{ vars.MONGODB_URI }}
      ACCESS_KEY_AWS: ${{ vars.ACCESS_KEY_AWS }}
      SECRET_ACCESS_KEY_AWS: ${{ vars.SECRET_ACCESS_KEY_AWS }}
    steps:
      - name: Checkout repository
        uses: actions/checkout@v3

      - name: Set up Node.js
        uses: actions/setup-node@v3
        with:
          node-version: 18

#      - name: install cross-env
#        run: npm install --g cross-env

      - name: Install dependencies
        run: npm ci

      - name: Run Playwright tests
        run: npx playwright test --grep "@genericR" --reporter=html

      - name: Archive test results
        if: always()
        run: |
          mkdir -p test-report
          mv playwright-report/*.html test-report/

      - name: Upload test results as artifact
        if: always()
        uses: actions/upload-artifact@v3
        with:
          name: test-results
          path: test-report/
          retention-days: 7

  publishTestResults:
    name: "Publish test results to GH pages"
    needs: [ regress-beta ]
    runs-on: ubuntu-latest
    if: always()
    steps:
      - name: Download artifacts
        uses: actions/download-artifact@v3
        with:
          name: test-results
          path: test-report/

      - name: Deploy to GitHub Pages
        uses: peaceiris/actions-gh-pages@v3.7.3
        with:
          github_token: ${{ secrets.PIPELINE }}
          publish_dir: test-report/
          publish_branch: gh-pages
          keep_files: true

      - name: Show vars
        run: |
          echo "SLACK_WEBHOOK_URL: ${{ vars.SLACK_WEBHOOK_URL }}"
          echo "var: ${{ vars.ADMIN_EMAIL }}"

      - name: Send Slack notification
        if: always()
        run: |
          curl -X POST -H 'Content-type: application/json' --data '{"text":"Playwright tests have been published! Check the results here https://bluetape-org.github.io/auto-tests-playwright-mocha/playwright-report/"}' ""

      - name: Debug environment variables
        if: always()
        run: |
          env