﻿using System.Diagnostics.CodeAnalysis;
using BlueTape.Aion.Application.Abstractions.MessageSenders;
using BlueTape.Aion.Domain.Constants;
using BlueTape.ServiceBusMessaging;
using BueTape.Aion.Infrastructure.ServiceBusMessages;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;

namespace BlueTape.Aion.Application.MessageSenders;

[ExcludeFromCodeCoverage]
public class AionExternalTransactionMessageSender : ServiceBusMessageSender<AionExternalTransactionMessage>, IAionExternalTransactionMessageSender
{
    public AionExternalTransactionMessageSender(IConfiguration configuration, ILogger<AionExternalTransactionMessageSender> logger)
        : base(configuration, logger, AionFunctionConstants.AionExternalTransactionQueueName, AionFunctionConstants.AionExternalTransactionQueueConnection)
    {
    }
}