import {delay} from '../../../utils/waiters';
import {BasePage} from '../../base.page';
import * as pdfParse from "pdf-parse";
import * as fs from "fs";
import {Page} from '@playwright/test';

export class LoanApplications extends BasePage {
    constructor(page: Page) {
        super(page);
    };

    containers = {
        supplierApplicationTableContainer: this.page.locator('.table'),
        navigationSupplierApplicationTableContainer: this.page.locator('.nav-tabs'),
    };

    buttons = {
        verify: this.page.locator('.btn-group button:has-text("Verify")'),
        approve: this.page.locator('.btn-group button:has-text("Approve")'),
        downloadAgreement: this.page.locator('.ml-2.btn.btn-outline-primary.btn-sm.btn-square'),
    };

    tabs = {
        inReview: this.containers.navigationSupplierApplicationTableContainer.locator('"In Review"'),
        approved: this.containers.navigationSupplierApplicationTableContainer.locator('"Approved"'),
        rejected: this.containers.navigationSupplierApplicationTableContainer.locator('"Rejected"'),
        other: this.containers.navigationSupplierApplicationTableContainer.locator('"Other"'),
        searchTab: this.page.getByPlaceholder('Company name'),
    };

    elements = {
        companyName: (userCompanyName) => this.containers.supplierApplicationTableContainer.locator(`tr:has-text("${userCompanyName}")`).first(),
        LoanApplication: this.containers.supplierApplicationTableContainer.locator('_react=[key="0"]'),
        fieldWithFullCompanyInformation: this.page.locator('//tr[@tabindex="0"]'),
    };

    async searchCompany(companyName: string) {
        await this.tabs.searchTab.click();
        await this.tabs.searchTab.fill(companyName);
    }

    async clickOnLoanApplication(userCompanyName) {
        await this.elements.companyName(userCompanyName).click();
    };

    async waitForApplicationStatusToUpdate(userEmail) {
        await this.page.waitForSelector(`.table >> "${userEmail}"`, {state: 'hidden'});
    };

    async clickOnLoanTab(tabElement) {
        await tabElement.click();
        await this.page.waitForSelector('.progress-bar.progress-bar-animated.progress-bar-striped.bg-info', {state: 'hidden'});
    };

    async downloadFirstAgreement() {
        await super.clickFistOption(this.buttons.downloadAgreement);
    };

    async downloadCreditAgreement() {
        const downloadPromise = this.page.waitForEvent('download');
        await this.buttons.downloadAgreement.first().click();
        const download = await downloadPromise;
        const filePath = 'test-data/files/file.pdf';
        await download.saveAs(filePath);
    };

    // TODO: PDF: Get Fields and check

    async parsePDF(filePath) {
        const dataBuffer = fs.readFileSync(filePath);
        pdfParse(dataBuffer).then(function (data) {
            const filePath = 'test-data/files/output.txt';
            fs.writeFile(filePath, data.text, (err) => {
                if (err) throw err;
                console.log('Text is saved into creditagreement.txt');
            });
        });

        fs.readFile(filePath, 'utf8', (err, data) => {
            if (err) {
                console.error(err);
                return;
            }
            const regexTitle = /BlueTape Credit Agreement/;
        });
    };

    async waitForElementExist(element) {
        for (let i = 0; i < 20; i += 1) {
            await this.page.waitForLoadState('networkidle');
            if (await element.isVisible()) {
                break;
            } else {
                await delay(2000);
                await this.page.reload();
            }
        }
    };

    async waitForStatusChange(element, status) {
        for (let i = 0; i < 20; i += 1) {
            await this.page.waitForLoadState('networkidle');
            for (let g = 0; g < 20; g += 1) {
                await this.page.waitForLoadState('networkidle');
                if (await element.isVisible()) {
                    break;
                } else {
                    await delay(2000);
                    await this.page.reload();
                    if (status === 'Approved') {
                        await this.tabs.approved.click();
                    }
                }
            }
            const elementText = await element.textContent();
            if (elementText.includes(status)) {
                break;
            } else {
                await delay(2000);
                await this.page.reload();
                if (status === 'Approved') {
                    await this.tabs.approved.click();
                }
            }
        }
    };

    async verifyLoanApplication(businessName) {
        await this.waitForStatusChange(this.elements.companyName(businessName), 'In Review');
        await this.clickOnLoanApplication(businessName);
        await this.buttons.verify.click();
    };

    async waitLoanAppVerifyAndClickApprove(businessName) {
        await this.page.waitForTimeout(5000);
        await this.page.reload();
        await this.waitForStatusChange(this.elements.companyName(businessName), 'In Review');
        await this.clickOnLoanApplication(businessName);
        await this.buttons.approve.click();
    };
}
