/**
 * 1. create loan
 * 2. check receivables
 * 3. update Loan receivables date in the past
 * 4. trigger overdue detector
 * 5. get latePayments
 */

import {expect} from '@playwright/test';
import {test} from '../../test-utils';
import {
    calculateReplanDate,
    createLoanByTemplate,
    deleteLoan,
    sendLMSRequest
} from '../../../api/common/lms-send-request';

const constants = JSON.parse(JSON.stringify(require('../../../constants/LMStestData.json')));

test.describe(`Late Payments API Tests @LMS @API`, async () => {

    let loanId: string;
    let loanObject: { loanReceivables: any; };
    let loanReceivablesArray: { status: string; }[];

    test.beforeAll(async () => {
        loanId = await createLoanByTemplate(constants.loanTemplateIDs.totalDurationInDays60);
    });

    test.afterAll(async () => {
        const response = await deleteLoan(loanId);

        expect(response.status, `Status code 200`)
            .toEqual(200);
    });

    test(`OverDue Loan Receivables should be changed to LATE status. @lms`, async () => {

        /**
         * Update Loan status
         */

        const requestUpdateLoanStateBody = {
            "status": "Started"
        };

        const response = await sendLMSRequest('patch', `Loans/${loanId}`, requestUpdateLoanStateBody);

        /**
         * Update Loan date
         */

        const requestUpdateLoanDateBody = {
            "newLoanTemplateId": constants.loanTemplateIDs.totalDurationInDays60,
            "replanDate": await calculateReplanDate(30)
        };

        const updatedLoanResponse = await sendLMSRequest('put', `Loans/${loanId}`, requestUpdateLoanDateBody);

        /**
         * Get Loan by Loan ID
         */

        const loanReceivablesResponse = await sendLMSRequest('get', `Loans/${loanId}`);

        loanObject = loanReceivablesResponse.data;

        /**
         * Update "receivableExpectedDate"
         */

        for (const loanReceivable of loanObject.loanReceivables) {
            loanReceivable.expectedDate = calculatePaymentDate(loanReceivable.expectedDate);
        }

        /**
         * Update Installments via QA endpoint Qa/Loans
         */

        await sendLMSRequest('post', `Qa/Loans/${loanId}`, loanObject);

        /**
         * Run OverDue Detector
         */

        const overDueResponse = await sendLMSRequest('patch', `OverDueDetector`);

        expect(overDueResponse.status, `Status code 200`)
            .toEqual(200);

        /**
         * Check that loan Receivable "status": "Late",
         */

        const updatedLoanReceivablesResponse = await sendLMSRequest('get', `Loans/${loanId}`);

        loanReceivablesArray = updatedLoanReceivablesResponse.data.loanReceivables;

        expect(loanReceivablesArray[0].status === "Late", `Loan Receivables state is 'Late'`)
            .toBeTruthy();
    });
});

/**
 * Functions
 */

function calculatePaymentDate(strDate) {
    const dateObj = new Date(strDate);
    dateObj.setMonth(dateObj.getMonth() - 6);
    const year = dateObj.getFullYear();
    const month = ("0" + (dateObj.getMonth() + 1)).slice(-2);
    const day = ("0" + (dateObj.getDate())).slice(-2);
    return `${year}-${month}-${day}`;
}
