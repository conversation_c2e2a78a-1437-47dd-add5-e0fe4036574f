﻿using AutoFixture;
using AutoMapper;
using BlueTape.Application.Tests.FixtureHelpers.FixtureAttributes;
using BlueTape.Common.Enums;
using BlueTape.Common.Exceptions.Companies;
using BlueTape.Company.Application.Abstractions.Processor;
using BlueTape.Company.Application.Services;
using BlueTape.Company.Domain.DTOs.BankAccounts;
using BlueTape.Company.Domain.DTOs.BankAccounts.Giact;
using BlueTape.Company.Domain.DTOs.Companies;
using BlueTape.CompanyService.BankAccounts;
using BlueTape.CompanyService.Common.Functions.AccountStatus;
using BlueTape.CompanyService.Common.Senders;
using BlueTape.DataAccess.MongoDB.Abstractions;
using BlueTape.Document.DataAccess.EF.Abstractions;
using BlueTape.Domain.Entities.BankAccounts;
using BlueTape.Domain.Entities.Documents;
using BlueTape.Plaid.Client.Abstractions.ExternalServices;
using BlueTape.ServiceBusMessaging.Attributes;
using BlueTape.Utilities.Security;
using Moq;
using Shouldly;
using Xunit;

namespace BlueTape.Application.Tests.Company;

public class BankAccountServiceTests
{
    private readonly Mock<IBankAccountRepository> _bankAccountRepositoryMock = new();
    private readonly Mock<ICompanyRepository> _companyRepository = new();
    private readonly Mock<IBankAccountProcessor> _bankAccountProcessorMock = new();

    private readonly Mock<IEfBankAccountRepository> _efBankAccountRepositoryMock = new();
    private readonly Mock<IEfCompanyRepository> _efCompanyRepositoryMock = new();
    private readonly Mock<IEfCompanyNoteRepository> _efCompanyNoteRepositoryMock = new();

    private readonly Mock<IMapper> _mapperMock = new();

    private readonly Mock<IAccountStatusChangeQueueSender> _mockAccountMessageSender = new();

    private readonly Mock<IPlaidApiProxy> _plaidApiProxy = new();
    private readonly Mock<IKmsEncryptionService> _kmsEncryptionService = new();
    private readonly Mock<IDraftRepository> _draftRepository = new();

    private readonly Fixture _fixture = new();

    private BankAccountService GetService()
    {
        return new BankAccountService(
            _bankAccountRepositoryMock.Object,
            _companyRepository.Object,
            _bankAccountProcessorMock.Object,
            _efBankAccountRepositoryMock.Object,
            _efCompanyRepositoryMock.Object,
            _efCompanyNoteRepositoryMock.Object,
            _plaidApiProxy.Object,
            _kmsEncryptionService.Object,
            _mapperMock.Object,
            _mockAccountMessageSender.Object,
            _draftRepository.Object);
    }

    private void VerifyNoOtherCalls()
    {
        _bankAccountRepositoryMock.VerifyNoOtherCalls();
        _companyRepository.VerifyNoOtherCalls();
        _bankAccountProcessorMock.VerifyNoOtherCalls();
    }

    [Fact]
    public async Task GetByBankAccountId_Execute_Success()
    {
        var bankAccountId = _fixture.Create<string>();

        await GetService().GetByBankAccountId(bankAccountId, It.IsAny<CancellationToken>());

        _bankAccountRepositoryMock.Verify(x =>
            x.GetByBankAccountId(bankAccountId, default), Times.Once);

        VerifyNoOtherCalls();
    }

    [Fact]
    public async Task UpdateGiactInfo_Execute_Success()
    {
        var bankAccountId = _fixture.Create<string>();
        var bankAccount = _fixture.Create<BankAccountDto>();

        _bankAccountRepositoryMock.Setup(x => x.GetByBankAccountId(bankAccountId, default)).ReturnsAsync(bankAccount);
        _bankAccountRepositoryMock.Setup(x => x.UpdateGiactAsync(bankAccount, default));

        await GetService()
            .UpdateGiactInfo(bankAccountId, It.IsAny<BankAccountGiactDto>(), It.IsAny<CancellationToken>());

        _bankAccountRepositoryMock.Verify(x => x.GetByBankAccountId(bankAccountId, default), Times.Once);
        _bankAccountRepositoryMock.Verify(x => x.UpdateGiactAsync(It.IsAny<BankAccountDto>(), default), Times.Once);

        VerifyNoOtherCalls();
    }

    [Fact]
    public async Task GetByBankAccountIds_Execute_Success()
    {
        var bankAccountIds = _fixture.Create<string[]>();

        await GetService().GetByBankAccountIds(bankAccountIds, It.IsAny<CancellationToken>());

        _bankAccountRepositoryMock.Verify(x =>
            x.GetByBankAccountIds(bankAccountIds, true, default), Times.Once);

        VerifyNoOtherCalls();
    }

    [Fact]
    public async Task GetByBankAccountAndPlaidIds_Execute_Success()
    {
        var bankAccountIds = _fixture.Create<string[]>();

        await GetService().GetByBankAccountAndPlaidIds(bankAccountIds, true, It.IsAny<CancellationToken>());

        _bankAccountRepositoryMock.Verify(x =>
            x.GetByBankAccountAndPlaidIds(bankAccountIds, true, default), Times.Once);

        VerifyNoOtherCalls();
    }

    [Theory]
    [InlineData(true)]
    [InlineData(false)]
    public async Task AddMany_Throws_BankAccountAddException(bool isNull)
    {
        var companyId = _fixture.Create<string>();
        var bankAccountDtos = _fixture.Create<List<CreateBankAccountDto>>();

        var processedBankAccounts = isNull ? default : Array.Empty<BankAccountDto>();

        _bankAccountProcessorMock.Setup(x =>
                x.PreProcessAddMany(
                    It.IsAny<string>(),
                    It.IsAny<List<CreateBankAccountDto>>(),
                    default))!
            .ReturnsAsync(processedBankAccounts);

        await GetService()
            .AddMany(companyId, bankAccountDtos, It.IsAny<CancellationToken>())
            .ShouldThrowAsync<BankAccountAddException>();

        _bankAccountProcessorMock.Verify(x =>
            x.PreProcessAddMany(companyId, bankAccountDtos, default), Times.Once);

        VerifyNoOtherCalls();
    }

    [Fact]
    public async Task AddMany_Execute_Success_Add_And_Update()
    {
        var companyId = _fixture.Create<string>();
        var existingId = _fixture.Create<string>();

        var bankAccountDtos = new List<CreateBankAccountDto>
        {
            new()
            {
                Id = existingId,
                Plaid = _fixture.Create<BankAccountPlaidDto>(),
                Name = _fixture.Create<string>(),
            },
            new()
            {
                Name = _fixture.Create<string>()
            }
        };

        var processedBankAccounts = new BankAccountDto[]
        {
            new()
            {
                Id = existingId,
                Plaid = _fixture.Create<BankAccountPlaidDto>(),
                Name = _fixture.Create<string>(),
            },
            new()
            {
                Name = _fixture.Create<string>()
            }
        };

        _bankAccountRepositoryMock.Setup(x =>
                x.AddMany(It.IsAny<List<BankAccountDto>>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(_fixture.Create<List<BankAccountDto>>());

        _bankAccountProcessorMock.Setup(x =>
                x.PreProcessAddMany(
                    It.IsAny<string>(),
                    It.IsAny<List<CreateBankAccountDto>>(),
                    default))!
            .ReturnsAsync(processedBankAccounts);

        await GetService()
            .AddMany(companyId, bankAccountDtos, It.IsAny<CancellationToken>())
            .ShouldNotThrowAsync();

        _bankAccountRepositoryMock.Verify(x =>
            x.AddMany(It.IsAny<List<BankAccountDto>>(), It.IsAny<CancellationToken>()), Times.Once);

        _companyRepository.Verify(x =>
            x.AddBankAccountIds(
                companyId,
                It.IsAny<string[]>(),
                It.IsAny<CancellationToken>()), Times.Once);

        _bankAccountProcessorMock.Verify(x =>
            x.PreProcessAddMany(companyId, bankAccountDtos, default), Times.Once);

        _bankAccountRepositoryMock.Verify(x =>
            x.UpdatePlaidConfigAsync(
                It.IsAny<BankAccountDto>(),
                It.IsAny<CancellationToken>()), Times.Once);

        VerifyNoOtherCalls();
    }

    [Fact]
    public async Task Add_Execute_Success_Update()
    {
        var companyId = _fixture.Create<string>();
        var bankAccountDto = _fixture.Create<CreateBankAccountDto>();
        var bankToUpdate = _fixture.Create<BankAccountDto>();

        _bankAccountProcessorMock.Setup(x =>
                x.PreProcessAdd(
                    It.IsAny<string>(),
                    It.IsAny<CreateBankAccountDto>(),
                    It.IsAny<CancellationToken>()))
            .ReturnsAsync(bankToUpdate);

        await GetService()
            .Add(companyId, userId: null, bankAccountDto, It.IsAny<CancellationToken>())
            .ShouldNotThrowAsync();

        _bankAccountProcessorMock.Verify(x =>
            x.PreProcessAdd(
                companyId,
                bankAccountDto,
                It.IsAny<CancellationToken>()), Times.Once);

        _bankAccountRepositoryMock.Verify(x =>
            x.UpdatePlaidConfigAsync(bankToUpdate, It.IsAny<CancellationToken>()), Times.Once);

        _bankAccountRepositoryMock.Verify(x =>
            x.UpdateGiactAsync(bankToUpdate, It.IsAny<CancellationToken>()), Times.Once);

        VerifyNoOtherCalls();
    }

    [Fact]
    public async Task Add_Execute_Success_Add()
    {
        var companyId = _fixture.Create<string>();
        var bankAccountDto = _fixture.Create<CreateBankAccountDto>();
        var bankToCreate = _fixture.Create<BankAccountDto>();
        bankToCreate.Id = string.Empty;

        _bankAccountProcessorMock.Setup(x =>
                x.PreProcessAdd(
                    It.IsAny<string>(),
                    It.IsAny<CreateBankAccountDto>(),
                    It.IsAny<CancellationToken>()))
            .ReturnsAsync(bankToCreate);

        _bankAccountRepositoryMock
            .Setup(x => x.Add(bankToCreate, default))
            .ReturnsAsync(_fixture.Create<BankAccountDto>());
        await GetService()
            .Add(companyId, userId: null, bankAccountDto, It.IsAny<CancellationToken>())
            .ShouldNotThrowAsync();

        _bankAccountProcessorMock.Verify(x =>
            x.PreProcessAdd(
                companyId,
                bankAccountDto,
                It.IsAny<CancellationToken>()), Times.Once);

        _bankAccountRepositoryMock
            .Verify(x => x.Add(bankToCreate, default), Times.Once);

        _companyRepository
            .Verify(x =>
                x.AddBankAccountIds(
                    companyId,
                    It.IsAny<string[]>(),
                    default), Times.Once);

        _bankAccountRepositoryMock
            .Verify(x => x.UpdateGiactAsync(bankToCreate, default), Times.Once);

        VerifyNoOtherCalls();
    }


    [Fact]
    public async Task GetBankAccountsByQuery_WithEmptyQuery_ReturnsEmptyList()
    {
        var service = GetService();
        var query = new BankAccountQuery();

        var result = await service.GetBankAccountsByQuery(query, CancellationToken.None);

        result.ShouldNotBeNull();
        result.ShouldBeEmpty();
        VerifyNoOtherCalls();
    }

    [Fact]
    public async Task GetBankAccountsByQuery_WithValidCompanyId_ReturnsBankAccounts()
    {
        var service = GetService();
        var query = new BankAccountQuery { CompanyId = _fixture.Create<string>() };
        var bankAccounts = _fixture.Create<List<BankAccountDto>>();
        foreach (var account in bankAccounts)
        {
            account.IsDeactivated = false;
        }

        _bankAccountRepositoryMock
            .Setup(x => x.GetByCompanyId(query.CompanyId, It.IsAny<CancellationToken>()))
            .ReturnsAsync(bankAccounts);

        var result = await service.GetBankAccountsByQuery(query, CancellationToken.None);

        result.ShouldNotBeNull();
        result.Count.ShouldBe(bankAccounts.Count);
        _bankAccountRepositoryMock.Verify(x =>
            x.GetByCompanyId(query.CompanyId, It.IsAny<CancellationToken>()), Times.Once);

        VerifyNoOtherCalls();
    }

    [Theory]
    [InlineData(true)]
    [InlineData(false)]
    public async Task GetBankAccountsByQuery_WithValidAccountId_ReturnsAccount(bool includeDuplicates)
    {
        var service = GetService();
        var query = new BankAccountQuery
        {
            Id = _fixture.Create<string>(),
            CompanyId = includeDuplicates ? _fixture.Create<string>() : null
        };
        var bankAccount = _fixture.Create<BankAccountDto>();
        var resultList = includeDuplicates ? new List<BankAccountDto> { bankAccount } : new List<BankAccountDto>();

        _bankAccountRepositoryMock
            .Setup(x => x.GetByBankAccountId(query.Id, It.IsAny<CancellationToken>()))
            .ReturnsAsync(bankAccount);

        var result = await service.GetBankAccountsByQuery(query, CancellationToken.None);

        if (includeDuplicates)
        {
            // Ensure no duplicates are added
            result.Count.ShouldBe(resultList.Count);
        }
        else
        {
            // Ensure the result includes the new account
            result.Count.ShouldBe(resultList.Count + 1);
        }

        _bankAccountRepositoryMock.Verify(x =>
            x.GetByBankAccountId(query.Id, It.IsAny<CancellationToken>()), Times.Once);
        if (includeDuplicates)
        {
            _bankAccountRepositoryMock.Verify(x =>
                x.GetByCompanyId(query.CompanyId!, It.IsAny<CancellationToken>()), Times.Once);
        }

        VerifyNoOtherCalls();
    }

    [Fact]
    public async Task GetBankAccountsByQuery_WithValidPlaidAccountId_AddsNewAccounts()
    {
        var service = GetService();
        var query = new BankAccountQuery { PlaidAccountId = _fixture.Create<string>() };
        var bankAccounts = _fixture.Create<List<BankAccountDto>>();
        foreach (var account in bankAccounts)
        {
            account.IsDeactivated = false;
        }

        _bankAccountRepositoryMock
            .Setup(x => x.GetByPlaidAccountIds(It.IsAny<string[]>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(bankAccounts);

        var result = await service.GetBankAccountsByQuery(query, CancellationToken.None);

        result.ShouldNotBeNull();
        result.Count.ShouldBe(bankAccounts.Count);
        _bankAccountRepositoryMock.Verify(x =>
            x.GetByPlaidAccountIds(It.IsAny<string[]>(), It.IsAny<CancellationToken>()), Times.Once);

        VerifyNoOtherCalls();
    }

    [Fact]
    public async Task GetBankAccountsByQueryV2_WithEmptyQuery_ReturnsEmptyList()
    {
        var service = GetService();
        var query = new BankAccountQuery();

        var result = await service.GetBankAccountsByQueryV2(query, CancellationToken.None);

        result.ShouldNotBeNull();
        result.ShouldBeEmpty();
        VerifyNoOtherCalls();
    }

    [Fact]
    public async Task GetBankAccountsByQueryV2_WithValidCompanyId_ReturnsBankAccounts()
    {
        var service = GetService();
        var query = new BankAccountQuery { CompanyId = _fixture.Create<string>() };
        var bankAccounts = _fixture.Create<List<BankAccountEntity>>();
        var company = _fixture.Build<Domain.Entities.Documents.CompanyEntity>().Without(c => c.Account).Create();

        _efCompanyRepositoryMock
            .Setup(x => x.GetById(It.IsAny<Guid>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(company);

        _bankAccountRepositoryMock
            .Setup(x => x.GetByCompanyId(It.IsAny<string>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(new List<BankAccountDto>());

        _efBankAccountRepositoryMock
            .Setup(x => x.GetByLegacyIds(It.IsAny<string[]>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(bankAccounts);

        var result = await service.GetBankAccountsByQueryV2(query, CancellationToken.None);

        result.ShouldNotBeNull();
        _efCompanyRepositoryMock.Verify(x =>
            x.GetById(It.IsAny<Guid>(), It.IsAny<CancellationToken>()), Times.Once);
        _bankAccountRepositoryMock.Verify(x =>
            x.GetByCompanyId(company.LegacyId!, It.IsAny<CancellationToken>()), Times.Once);
        _efBankAccountRepositoryMock.Verify(x =>
            x.GetByLegacyIds(It.IsAny<string[]>(), It.IsAny<CancellationToken>()), Times.Once);

        VerifyNoOtherCalls();
    }

    [Fact]
    public async Task GetBankAccountsByQueryV2_InvalidCompanyId_DoesNotThrow()
    {
        var service = GetService();
        var query = new BankAccountQuery { CompanyId = "INVALID_GUID" };

        await service.GetBankAccountsByQueryV2(query, CancellationToken.None);

        VerifyNoOtherCalls();
    }

    [Fact]
    public async Task GetBankAccountsByQueryV2_WithValidPlaidAccountId_ReturnsMappedBankAccounts()
    {
        // Arrange
        var service = GetService();

        var query = new BankAccountQuery { PlaidAccountId = _fixture.Create<string>() };
        var mongoBankAccounts = _fixture.Create<List<BankAccountDto>>();
        var efBankAccounts = _fixture.Create<List<BankAccountEntity>>();

        _bankAccountRepositoryMock
            .Setup(x => x.GetByPlaidAccountIds(It.IsAny<string[]>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(mongoBankAccounts);

        _efBankAccountRepositoryMock
            .Setup(x => x.GetByLegacyIds(It.IsAny<string[]>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(efBankAccounts);

        _mapperMock
            .Setup(m => m.Map<BankAccountDtoV2>(It.IsAny<BankAccountDto>()))
            .Returns((BankAccountDto source) =>
            {
                var mapped = _fixture.Create<BankAccountDtoV2>();
                mapped.Id = source.Id;
                return mapped;
            });

        // Act
        var result = await service.GetBankAccountsByQueryV2(query, CancellationToken.None);

        // Assert
        result.ShouldNotBeNull();
        result.Count.ShouldBe(mongoBankAccounts.Count);

        _bankAccountRepositoryMock.Verify(x =>
            x.GetByPlaidAccountIds(It.IsAny<string[]>(), It.IsAny<CancellationToken>()), Times.Once);

        _efBankAccountRepositoryMock.Verify(x =>
            x.GetByLegacyIds(It.IsAny<string[]>(), It.IsAny<CancellationToken>()), Times.Once);

        VerifyNoOtherCalls();
    }

    [Fact]
    public async Task GetByBankAccountIdV2_WithValidId_ReturnsMappedBankAccount()
    {
        // Arrange
        var service = GetService();

        var bankAccountId = _fixture.Create<Guid>();
        var efBankAccount = _fixture.Build<BankAccountEntity>()
            .With(x => x.LegacyId, _fixture.Create<string>())
            .Create();
        var mongoBankAccount = _fixture.Create<BankAccountDto>();
        var expectedResult = _fixture.Create<BankAccountDtoV2>();

        _efBankAccountRepositoryMock
            .Setup(x => x.GetById(bankAccountId, It.IsAny<CancellationToken>()))
            .ReturnsAsync(efBankAccount);

        _bankAccountRepositoryMock
            .Setup(x => x.GetByBankAccountId(efBankAccount.LegacyId!, It.IsAny<CancellationToken>()))
            .ReturnsAsync(mongoBankAccount);

        _mapperMock
            .Setup(m => m.Map<BankAccountDtoV2>(mongoBankAccount))
            .Returns(expectedResult);

        // Act
        var result = await service.GetByBankAccountIdV2(bankAccountId, CancellationToken.None);

        // Assert
        result.ShouldNotBeNull();
        result.ShouldBe(expectedResult);
        result!.BlueTapeGuidBankAccountId.ShouldBe(bankAccountId);

        _efBankAccountRepositoryMock.Verify(x =>
            x.GetById(bankAccountId, It.IsAny<CancellationToken>()), Times.Once);

        _bankAccountRepositoryMock.Verify(x =>
            x.GetByBankAccountId(efBankAccount.LegacyId!, It.IsAny<CancellationToken>()), Times.Once);

        _mapperMock.Verify(m => m.Map<BankAccountDtoV2>(mongoBankAccount), Times.Once);

        VerifyNoOtherCalls();
    }

    [Fact]
    public async Task GetByBankAccountIdV2_WithInvalidId_ReturnsNull()
    {
        // Arrange
        var service = GetService();

        var bankAccountId = _fixture.Create<Guid>();

        _efBankAccountRepositoryMock
            .Setup(x => x.GetById(bankAccountId, It.IsAny<CancellationToken>()))
            .ReturnsAsync((BankAccountEntity?)null);

        // Act
        var result = await service.GetByBankAccountIdV2(bankAccountId, CancellationToken.None);

        // Assert
        result.ShouldBeNull();

        _efBankAccountRepositoryMock.Verify(x =>
            x.GetById(bankAccountId, It.IsAny<CancellationToken>()), Times.Once);

        VerifyNoOtherCalls();
    }

    [Fact]
    public async Task GetByBankAccountIdV2_WithNoLegacyId_ReturnsNull()
    {
        // Arrange
        var service = GetService();

        var bankAccountId = _fixture.Create<Guid>();
        var efBankAccount = _fixture.Build<BankAccountEntity>()
            .Without(x => x.LegacyId) // No LegacyId
            .Create();

        _efBankAccountRepositoryMock
            .Setup(x => x.GetById(bankAccountId, It.IsAny<CancellationToken>()))
            .ReturnsAsync(efBankAccount);

        // Act
        var result = await service.GetByBankAccountIdV2(bankAccountId, CancellationToken.None);

        // Assert
        result.ShouldBeNull();

        _efBankAccountRepositoryMock.Verify(x =>
            x.GetById(bankAccountId, It.IsAny<CancellationToken>()), Times.Once);

        VerifyNoOtherCalls();
    }

    [Fact]
    public async Task GetByCompanyId_WithValidCompanyId_ReturnsBankAccounts()
    {
        // Arrange
        var service = GetService();
        var companyId = _fixture.Create<string>();
        var company = _fixture.Build<CompanyDto>()
            .With(c => c.BankAccounts, _fixture.Create<string[]>())
            .Create();
        var bankAccounts = _fixture.Create<List<BankAccountDto>>();

        _companyRepository
            .Setup(x => x.GetByCompanyId(companyId, It.IsAny<CancellationToken>()))
            .ReturnsAsync(company);

        _bankAccountRepositoryMock
            .Setup(x => x.GetByBankAccountIds(company.BankAccounts, false, It.IsAny<CancellationToken>()))
            .ReturnsAsync(bankAccounts);

        // Act
        var result = await service.GetByCompanyId(companyId, false, CancellationToken.None);

        // Assert
        result.ShouldNotBeNull();
        result.ShouldBe(bankAccounts);

        _companyRepository.Verify(x =>
            x.GetByCompanyId(companyId, It.IsAny<CancellationToken>()), Times.Once);

        _bankAccountRepositoryMock.Verify(x =>
            x.GetByBankAccountIds(company.BankAccounts, false, It.IsAny<CancellationToken>()), Times.Once);

        VerifyNoOtherCalls();
    }

    [Fact]
    public async Task GetByCompanyId_WithInvalidCompanyId_ThrowsCompanyDoesNotExistException()
    {
        // Arrange
        var service = GetService();
        var companyId = _fixture.Create<string>();

        _companyRepository
            .Setup(x => x.GetByCompanyId(companyId, It.IsAny<CancellationToken>()))
            .ReturnsAsync((CompanyDto?)null);

        // Act & Assert
        await service
            .GetByCompanyId(companyId, false, CancellationToken.None)
            .ShouldThrowAsync<CompanyDoesNotExistException>();

        _companyRepository.Verify(x =>
            x.GetByCompanyId(companyId, It.IsAny<CancellationToken>()), Times.Once);

        VerifyNoOtherCalls();
    }

    [Fact]
    public async Task GetByBankAccountIdsV2_WithValidIds_ReturnsBankAccountsV2()
    {
        // Arrange
        var service = GetService();
        var bankAccountIds = _fixture.Create<Guid[]>();
        var efBankAccounts = _fixture.Create<List<BankAccountEntity>>();
        var mongoBankAccounts = _fixture.Create<List<BankAccountDto>>();

        _efBankAccountRepositoryMock
            .Setup(x => x.GetByIds(bankAccountIds, It.IsAny<CancellationToken>()))
            .ReturnsAsync(efBankAccounts);

        _bankAccountRepositoryMock
            .Setup(x => x.GetByBankAccountIds(It.IsAny<string[]>(), true, It.IsAny<CancellationToken>()))
            .ReturnsAsync(mongoBankAccounts);

        _mapperMock
            .Setup(m => m.Map<BankAccountDtoV2>(It.IsAny<BankAccountDto>()))
            .Returns((BankAccountDto source) =>
            {
                var mapped = _fixture.Create<BankAccountDtoV2>();
                mapped.Id = source.Id;
                return mapped;
            });

        // Act
        var result = await service.GetByBankAccountIdsV2(bankAccountIds, CancellationToken.None);

        // Assert
        result.ShouldNotBeNull();
        result.Count.ShouldBe(mongoBankAccounts.Count);

        _efBankAccountRepositoryMock.Verify(x =>
            x.GetByIds(bankAccountIds, It.IsAny<CancellationToken>()), Times.Once);

        _bankAccountRepositoryMock.Verify(x =>
            x.GetByBankAccountIds(It.IsAny<string[]>(), true, It.IsAny<CancellationToken>()), Times.Once);

        _mapperMock.Verify(m => m.Map<BankAccountDtoV2>(It.IsAny<BankAccountDto>()),
            Times.Exactly(mongoBankAccounts.Count));

        VerifyNoOtherCalls();
    }

    [Fact]
    public async Task GetByBankAccountIdsV2_WithNoMatchingEFBankAccounts_ReturnsEmptyList()
    {
        // Arrange
        var service = GetService();
        var bankAccountIds = _fixture.Create<Guid[]>();

        _efBankAccountRepositoryMock
            .Setup(x => x.GetByIds(bankAccountIds, It.IsAny<CancellationToken>()))
            .ReturnsAsync(new List<BankAccountEntity>());

        // Act
        var result = await service.GetByBankAccountIdsV2(bankAccountIds, CancellationToken.None);

        // Assert
        result.ShouldNotBeNull();
        result.ShouldBeEmpty();

        _efBankAccountRepositoryMock.Verify(x =>
            x.GetByIds(bankAccountIds, It.IsAny<CancellationToken>()), Times.Once);

        VerifyNoOtherCalls();
    }

    [Fact]
    public async Task GetByBankAccountIdsV2_WithNoMongoResults_ReturnsEmptyList()
    {
        // Arrange
        var service = GetService();
        var bankAccountIds = _fixture.Create<Guid[]>();
        var efBankAccounts = _fixture.Create<List<BankAccountEntity>>();

        _efBankAccountRepositoryMock
            .Setup(x => x.GetByIds(bankAccountIds, It.IsAny<CancellationToken>()))
            .ReturnsAsync(efBankAccounts);

        _bankAccountRepositoryMock
            .Setup(x => x.GetByBankAccountIds(It.IsAny<string[]>(), true, It.IsAny<CancellationToken>()))
            .ReturnsAsync(new List<BankAccountDto>());

        // Act
        var result = await service.GetByBankAccountIdsV2(bankAccountIds, CancellationToken.None);

        // Assert
        result.ShouldNotBeNull();
        result.ShouldBeEmpty();

        _efBankAccountRepositoryMock.Verify(x =>
            x.GetByIds(bankAccountIds, It.IsAny<CancellationToken>()), Times.Once);

        _bankAccountRepositoryMock.Verify(x =>
            x.GetByBankAccountIds(It.IsAny<string[]>(), true, It.IsAny<CancellationToken>()), Times.Once);

        VerifyNoOtherCalls();
    }

    [Fact]
    public async Task GetBankAccountsByQueryV2_WithInvalidCompanyIdAndValidAccountId_ReturnsBankAccount()
    {
        // Arrange
        var service = GetService();

        var validCompanyId = Guid.NewGuid().ToString(); // Use a valid GUID string
        var validBankAccountId = _fixture.Create<Guid>();
        var validQuery = new BankAccountQuery
        {
            CompanyId = validCompanyId, // Valid GUID
            Id = validBankAccountId.ToString()
        };

        var efBankAccount = _fixture.Build<BankAccountEntity>()
            .With(x => x.LegacyId, _fixture.Create<string>())
            .Create();
        var mongoBankAccount = _fixture.Create<BankAccountDto>();
        var expectedBankAccountV2 = _fixture.Create<BankAccountDtoV2>();

        _efCompanyRepositoryMock
            .Setup(x => x.GetById(It.IsAny<Guid>(), It.IsAny<CancellationToken>()))
            .ThrowsAsync(new CompanyDoesNotExistException(validCompanyId));

        _efBankAccountRepositoryMock
            .Setup(x => x.GetById(validBankAccountId, It.IsAny<CancellationToken>()))
            .ReturnsAsync(efBankAccount);

        _bankAccountRepositoryMock
            .Setup(x => x.GetByBankAccountId(efBankAccount.LegacyId!, It.IsAny<CancellationToken>()))
            .ReturnsAsync(mongoBankAccount);

        _mapperMock
            .Setup(m => m.Map<BankAccountDtoV2>(mongoBankAccount))
            .Returns(expectedBankAccountV2);

        // Act
        var result = await service.GetBankAccountsByQueryV2(validQuery, CancellationToken.None);

        // Assert
        result.ShouldNotBeNull();
        result.Count.ShouldBe(1);
        result[0].ShouldBe(expectedBankAccountV2);

        _efCompanyRepositoryMock.Verify(x =>
            x.GetById(It.IsAny<Guid>(), It.IsAny<CancellationToken>()), Times.Once);

        _efBankAccountRepositoryMock.Verify(x =>
            x.GetById(validBankAccountId, It.IsAny<CancellationToken>()), Times.Once);

        _bankAccountRepositoryMock.Verify(x =>
            x.GetByBankAccountId(efBankAccount.LegacyId!, It.IsAny<CancellationToken>()), Times.Once);

        _mapperMock.Verify(m => m.Map<BankAccountDtoV2>(mongoBankAccount), Times.Once);

        VerifyNoOtherCalls();
    }

    [Fact]
    public async Task UpdatePlaidBankAccountStatus_ValidItemAndAccountId_UpdatesStatus()
    {
        // Arrange
        var service = GetService();
        var itemId = _fixture.Create<string>();
        var accountId = _fixture.Create<string>();
        var bankAccount = _fixture.Build<BankAccountDto>()
            .With(x => x.Plaid, new BankAccountPlaidDto { AccountId = accountId })
            .Create();
        var companyDto = _fixture.Create<CompanyDto>();
        var status = PlaidBankAccountStatus.Active;

        _bankAccountRepositoryMock
            .Setup(x => x.GetByPlaidData(itemId, It.IsAny<CancellationToken>()))
            .ReturnsAsync([bankAccount]);

        _companyRepository
            .Setup(x => x.GetByBankAccountId(bankAccount.Id, It.IsAny<CancellationToken>()))
            .ReturnsAsync(companyDto);

        // Act
        await service.UpdatePlaidBankAccountStatus(itemId, accountId, status, CancellationToken.None);

        // Assert
        _bankAccountRepositoryMock.Verify(x =>
            x.UpdatePlaidBankAccountStatusAsync(bankAccount, status, It.IsAny<CancellationToken>()), Times.Once);
        _bankAccountRepositoryMock.Verify(x =>
            x.GetByPlaidData(itemId, It.IsAny<CancellationToken>()), Times.Once);
        _companyRepository
            .Verify(x => x.GetByBankAccountId(bankAccount.Id, It.IsAny<CancellationToken>()), Times.Once);

        _mockAccountMessageSender.Verify(x =>
            x.SendMessages(It.IsAny<IEnumerable<ServiceBusMessageBt<ChangeAccountStatusModel>>>(),
                It.IsAny<CancellationToken>()), Times.Once);

        VerifyNoOtherCalls();
    }

    [Fact]
    public async Task PatchCashFlowInclusion_ValidInput_UpdatesIncludeInCashFlow()
    {
        // Arrange
        var service = GetService();
        var bankAccountId = _fixture.Create<string>();
        var patchDto = _fixture.Create<PatchBankAccountCashFlowDto>();
        var existingBankAccount = _fixture.Create<BankAccountDto>();
        existingBankAccount.Id = bankAccountId;

        _bankAccountRepositoryMock
            .Setup(x => x.GetByBankAccountId(bankAccountId, It.IsAny<CancellationToken>()))
            .ReturnsAsync(existingBankAccount);

        _bankAccountRepositoryMock
            .Setup(x => x.UpdatePlaidBankAccountInclusionAsync(existingBankAccount, patchDto.IncludeInCashFlow, It.IsAny<CancellationToken>()))
            .Returns(Task.CompletedTask);

        // Act
        await service.PatchCashFlowInclusion(bankAccountId, patchDto, CancellationToken.None);

        // Assert
        _bankAccountRepositoryMock.Verify(x => x.GetByBankAccountId(bankAccountId, It.IsAny<CancellationToken>()), Times.Once);
        _bankAccountRepositoryMock.Verify(x => x.UpdatePlaidBankAccountInclusionAsync(existingBankAccount, patchDto.IncludeInCashFlow, It.IsAny<CancellationToken>()), Times.Once);
        VerifyNoOtherCalls();
    }


    [Fact]
    public async Task PatchSettings_ValidInput_UpdatesBankAccountSettings()
    {
        // Arrange
        var service = GetService();
        var bankAccountId = _fixture.Create<string>();
        var patchDto = _fixture.Create<PatchBankAccountSettingsDto>();
        var existingBankAccount = _fixture.Create<BankAccountDto>();
        existingBankAccount.Id = bankAccountId;
        _bankAccountRepositoryMock
            .Setup(x => x.GetByBankAccountId(bankAccountId, It.IsAny<CancellationToken>()))
            .ReturnsAsync(existingBankAccount);
        // Act
        await service.PatchSettings(bankAccountId, patchDto, CancellationToken.None);
        // Assert
        _bankAccountRepositoryMock.Verify(x => x.GetByBankAccountId(bankAccountId, It.IsAny<CancellationToken>()),
            Times.Once);
        _bankAccountRepositoryMock.Verify(x => x.UpdateSettingsAsync(existingBankAccount, patchDto, It.IsAny<CancellationToken>()),
            Times.Once);
        VerifyNoOtherCalls();
    }

    [Theory, AutoDataWithDateOnly]
    public async Task UpdateBankAccountNumberModel_ValidInput_CreatesAndUpdatesBankAccount(CompanyEntity efCompany)
    {
        // Arrange
        var service = GetService();
        var accountId = _fixture.Create<string>();
        var userId = _fixture.Create<string>();

        var bankDetails = _fixture.Create<NoSupplierBankDetailsDto>();
        var patchDto = _fixture.Build<UpdateBankAccountNumberDto>()
            .With(x => x.BankDetails, bankDetails)
            .Create();

        var existingBankAccount = _fixture.Create<BankAccountDto>();
        var companyDto = _fixture.Create<CompanyDto>();

        // Mock bank account mapping
        var newBankAccount = _fixture.Create<BankAccountDto>(); // Simulate a new generated account
        _mapperMock
            .Setup(m => m.Map<BankAccountDto>(existingBankAccount))
            .Returns(newBankAccount);

        _bankAccountRepositoryMock
            .Setup(x => x.GetByBankAccountId(accountId, It.IsAny<CancellationToken>()))
            .ReturnsAsync(existingBankAccount);

        _companyRepository
            .Setup(x => x.GetByBankAccountId(existingBankAccount.Id, It.IsAny<CancellationToken>()))
            .ReturnsAsync(companyDto);

        _bankAccountRepositoryMock
            .Setup(x => x.Add(It.IsAny<BankAccountDto>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(newBankAccount);

        _efCompanyRepositoryMock
            .Setup(x => x.GetByLegacyId(companyDto.BlueTapeCompanyId, It.IsAny<CancellationToken>()))
            .ReturnsAsync(efCompany);

        _efCompanyNoteRepositoryMock
            .Setup(x => x.Add(It.IsAny<CompanyNoteEntity>(), It.IsAny<CancellationToken>()))
            .Verifiable();

        // Act
        var result = await service.UpdateBankAccountNumberModel(accountId, userId, patchDto, CancellationToken.None);

        // Assert
        result.ShouldNotBeNull();
        _mapperMock.Verify(m => m.Map<BankAccountDto>(existingBankAccount),
            Times.Once); // Ensure mapping is called correctly
        _bankAccountRepositoryMock.Verify(x => x.GetByBankAccountId(accountId, It.IsAny<CancellationToken>()),
            Times.Once);
        _companyRepository.Verify(x => x.GetByBankAccountId(existingBankAccount.Id, It.IsAny<CancellationToken>()),
            Times.Once);
        _bankAccountRepositoryMock.Verify(x => x.Add(It.IsAny<BankAccountDto>(), It.IsAny<CancellationToken>()),
            Times.Once);
        _bankAccountRepositoryMock.Verify(x => x.DeactivateAsync(accountId, It.IsAny<CancellationToken>()), Times.Once);
        _bankAccountRepositoryMock.Verify(
            x => x.UpdateGiactAsync(It.IsAny<BankAccountDto>(), It.IsAny<CancellationToken>()), Times.Once);

        _companyRepository.Verify(x =>
                x.AddBankAccountIds(
                    companyDto.BlueTapeCompanyId,
                    It.IsAny<IEnumerable<string>>(),
                    It.IsAny<CancellationToken>()),
            Times.Once);

        _efCompanyNoteRepositoryMock.Verify(
            x => x.Add(It.Is<CompanyNoteEntity>(note =>
                    note!.Note!.Contains("Bank Account details were updated by") && note.CompanyId == efCompany.Id),
                It.IsAny<CancellationToken>()),
            Times.Once);

        VerifyNoOtherCalls();
    }

    [Fact]
    public async Task UpdateBankAccountNumberModel_BankAccountNotFound_ThrowsException()
    {
        // Arrange
        var service = GetService();
        var accountId = _fixture.Create<string>();
        var userId = _fixture.Create<string>();
        var patchDto = _fixture.Create<UpdateBankAccountNumberDto>();

        _bankAccountRepositoryMock
            .Setup(x => x.GetByBankAccountId(accountId, It.IsAny<CancellationToken>()))
            .ReturnsAsync((BankAccountDto?)null);

        // Act & Assert
        await service.UpdateBankAccountNumberModel(accountId, userId, patchDto, CancellationToken.None)
            .ShouldThrowAsync<BankDoesNotExistException>();

        _bankAccountRepositoryMock.Verify(x => x.GetByBankAccountId(accountId, It.IsAny<CancellationToken>()),
            Times.Once);
        VerifyNoOtherCalls();
    }

    [Fact]
    public async Task UpdateBankAccountNumberModel_CompanyNotFound_ThrowsException()
    {
        // Arrange
        var service = GetService();
        var accountId = _fixture.Create<string>();
        var userId = _fixture.Create<string>();
        var patchDto = _fixture.Create<UpdateBankAccountNumberDto>();
        var existingBankAccount = _fixture.Create<BankAccountDto>();

        _bankAccountRepositoryMock
            .Setup(x => x.GetByBankAccountId(accountId, It.IsAny<CancellationToken>()))
            .ReturnsAsync(existingBankAccount);

        _companyRepository
            .Setup(x => x.GetByBankAccountId(existingBankAccount.Id, It.IsAny<CancellationToken>()))
            .ReturnsAsync((CompanyDto?)null);

        // Act & Assert
        await service.UpdateBankAccountNumberModel(accountId, userId, patchDto, CancellationToken.None)
            .ShouldThrowAsync<CompanyDoesNotExistException>();

        _bankAccountRepositoryMock.Verify(x => x.GetByBankAccountId(accountId, It.IsAny<CancellationToken>()),
            Times.Once);
        _companyRepository.Verify(x => x.GetByBankAccountId(existingBankAccount.Id, It.IsAny<CancellationToken>()),
            Times.Once);
        VerifyNoOtherCalls();
    }

    [Fact]
    public async Task UpdateBankAccountNumberModel_NoBankDetails_ReturnsExistingBankAccount()
    {
        // Arrange
        var service = GetService();
        var accountId = _fixture.Create<string>();
        var userId = _fixture.Create<string>();
        var patchDto = _fixture.Build<UpdateBankAccountNumberDto>()
            .Without(x => x.BankDetails)
            .Create();

        var existingBankAccount = _fixture.Create<BankAccountDto>();

        _bankAccountRepositoryMock
            .Setup(x => x.GetByBankAccountId(accountId, It.IsAny<CancellationToken>()))
            .ReturnsAsync(existingBankAccount);

        // Act
        var result = await service.UpdateBankAccountNumberModel(accountId, userId, patchDto, CancellationToken.None);

        // Assert
        result.ShouldNotBeNull();
        result.ShouldBe(existingBankAccount);

        _bankAccountRepositoryMock.Verify(x => x.GetByBankAccountId(accountId, It.IsAny<CancellationToken>()),
            Times.Once);
        VerifyNoOtherCalls();
    }

    [Fact]
    public async Task Add_WithIsRegulated_SetsIsRegulatedProperty()
    {
        // Arrange
        var companyId = _fixture.Create<string>();
        var bankAccountDto = _fixture.Create<CreateBankAccountDto>();
        bankAccountDto.IsRegulated = true;

        var bankToCreate = _fixture.Create<BankAccountDto>();
        bankToCreate.Id = string.Empty;
        bankToCreate.IsRegulated = true;

        _bankAccountProcessorMock.Setup(x =>
                x.PreProcessAdd(
                    It.IsAny<string>(),
                    It.IsAny<CreateBankAccountDto>(),
                    It.IsAny<CancellationToken>()))
            .ReturnsAsync(bankToCreate);

        _bankAccountRepositoryMock
            .Setup(x => x.Add(bankToCreate, CancellationToken.None))
            .ReturnsAsync(_fixture.Create<BankAccountDto>());

        // Act
        var result = await GetService()
            .Add(companyId, userId: null, bankAccountDto, It.IsAny<CancellationToken>());

        // Assert
        result.ShouldNotBeNull();
        _bankAccountProcessorMock.Verify(x =>
            x.PreProcessAdd(
                companyId,
                It.Is<CreateBankAccountDto>(dto => dto.IsRegulated!.Value),
                It.IsAny<CancellationToken>()), Times.Once);
    }
}
