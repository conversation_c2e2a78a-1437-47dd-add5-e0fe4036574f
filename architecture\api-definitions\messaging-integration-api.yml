openapi: '3.0.0'
info:
  version: '0.0.1'
  title: 'Messaging Integration API'
  description: | 
    API definition for 3rd party messaging services integration.

    Both GET and POST requests are supported, for sake of simplicity defined as GETs.
paths:
  /twilio/webhooks/request:
    get:
      tags:
        - twilio
      summary: Endpoint to setup webhook calls
      operationId: twilioWebhookRequest
      parameters:
        - in: header
          name: X-Twilio-Signature
          schema:
            type: string
          required: true
          description: Request signature
        - in: query
          name: MessageSid
          schema:
            type: string
          required: true
          description: A 34 character unique identifier for the message.
        - in: query
          name: AccountSid
          schema:
            type: string
          required: true
          description: The 34 character id of the Account this message is associated with.
        - in: query
          name: MessagingServiceSid
          schema:
            type: string
          required: true
          description: The 34 character id of the Messaging Service associated with the message.
        - in: query
          name: From
          schema:
            type: string
          required: true
          description: The phone number or Channel address that sent this message.
        - in: query
          name: To
          schema:
            type: string
          required: true
          description: The phone number or Channel address of the recipient.
        - in: query
          name: Body
          schema:
            type: string
          required: true
          description: The text body of the message. Up to 1600 characters long.
      responses:
        200:
          description: Webhook
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Success'
        400:
          description: Invalid request
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
        401:
          description: Unauthorized
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
        404:
          description: Not found
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
        500:
          description: Unexpected error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
  /twilio/webhooks/failover:
    get:
      tags:
        - twilio
      summary: Endpoint to setup failover calls
      operationId: twilioWebhookFailover
      parameters:
        - in: header
          name: X-Twilio-Signature
          schema:
            type: string
          required: true
          description: Request signature
        - in: query
          name: ErrorCode
          schema:
            type: string
          required: true
          description: The error code, see https://www.twilio.com/docs/api/errors for more info.
        - in: query
          name: ErrorUrl
          schema:
            type: string
          required: true
          description: The full error URL, causing error.
      responses:
        200:
          description: Webhook
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Success'
        400:
          description: Invalid request
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
        401:
          description: Unauthorized
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
        404:
          description: Not found
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
        500:
          description: Unexpected error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
  /twilio/webhooks/deliverystatus:
    get:
      tags:
        - twilio
      summary: Endpoint to setup delivery status callbacks
      operationId: twilioWebhookDeliveryStatus
      parameters:
        - in: header
          name: X-Twilio-Signature
          schema:
            type: string
          required: true
          description: Request signature
        - in: query
          name: MessageSid
          schema:
            type: string
          required: true
          description: A 34 character unique identifier for the message.
        - in: query
          name: AccountSid
          schema:
            type: string
          required: true
          description: The 34 character id of the Account this message is associated with.
        - in: query
          name: MessagingServiceSid
          schema:
            type: string
          required: true
          description: The 34 character id of the Messaging Service associated with the message.
        - in: query
          name: From
          schema:
            type: string
          required: true
          description: The phone number or Channel address that sent this message.
        - in: query
          name: To
          schema:
            type: string
          required: true
          description: The phone number or Channel address of the recipient.
        - in: query
          name: Body
          schema:
            type: string
          required: true
          description: The text body of the message. Up to 1600 characters long.
        - in: query
          name: MessageStatus
          schema:
            type: string
            enum:
            - accepted
            - queued
            - sending
            - sent
            - failed
            - delivered
            - undelivered
            - receiving
            - received
          required: true
          description: The status of delivery of the message.
      responses:
        200:
          description: Webhook
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Success'
        400:
          description: Invalid request
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
        401:
          description: Unauthorized
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
        404:
          description: Not found
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
        500:
          description: Unexpected error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
components:
  schemas:
    Success:
      type: object
    Error:
      type: object
      required:
        - message
      properties:
        message:
          description: A human readable error message
          type: string
