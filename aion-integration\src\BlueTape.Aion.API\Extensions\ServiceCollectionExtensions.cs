using Amazon;
using Amazon.Extensions.NETCore.Setup;
using Amazon.KeyManagementService;
using Amazon.SecretsManager;
using BlueTape.Aion.API.Helpers;
using BlueTape.Services.Utilities.AWS;
using BlueTape.Services.Utilities.Security;

namespace BlueTape.Aion.API.Extensions;

internal static class ServiceCollectionExtensions
{
    internal static void AddUtilities(this IServiceCollection services)
    {
        services.AddSingleton<IMd5HashService, Md5Hashservice>();
        services.AddSingleton<ISecretsManagerService, AwsSecretsManagerService>();
        services.AddSingleton<IEncryptionService, AesEncryptionService>();
        services.AddTransient<IExceptionToResponseMapper, ExceptionToResponseMapper>();
        services.AddSingleton<IHttpContextAccessor, HttpContextAccessor>();
    }

    internal static void AddAWSServices(this IServiceCollection services, ConfigurationManager configuration)
    {
        var awsOptions = configuration.GetAWSOptions();
        services.AddDefaultAWSOptions(awsOptions);
        services.AddAWSService<IAmazonSecretsManager>(new AWSOptions { Region = RegionEndpoint.USWest1 });
        services.AddAWSService<IAmazonKeyManagementService>(new AWSOptions { Region = RegionEndpoint.USWest1 });
    }
}
