import React from 'react'
import { styled } from 'styled-components'
import { Col, Flex } from 'antd'

import BankAccountBlock from '@/components/common/BankAccounts/BankAccountsBlock'
import CashFlowBlock from '@/app/line-of-credit/[application_id]/detailed/_components/tabs/bank-cash-flow/components/CashFlowBlock'
import type { ILineOfCreditBankDetails } from '@/lib/redux/api/line-of-credit/types'
import Spacer from '@/components/common/Spacer'
import GiactVerificationBlock from '@/app/line-of-credit/[application_id]/detailed/_components/tabs/bank-cash-flow/components/GiactVerificationBlock'
import BankCreditStatusBlock from '@/components/common/BankCreditStatusBlock'

interface IProps {
  bankDetails: ILineOfCreditBankDetails
  applicationId: string
  isHistorical?: boolean
  accountId: string
  companyName: string
}

const BankCashFlow = (props: IProps): JSX.Element => {
  return (
    <>
      <Spacer height={28} />
      <BankCreditStatusBlock bankDetails={props.bankDetails} />
      <StyledFlex gap={77}>
        <Col flex="2">
          <BankAccountBlock
            bankAccounts={props.bankDetails.bankAccounts}
            isHistorical={props.isHistorical}
          />
          <Spacer height={54} />
          <GiactVerificationBlock
            giactItems={props.bankDetails.giact}
            applicationId={props.applicationId}
          />
        </Col>
        <Col flex="2">
          <CashFlowBlock
            items={props.bankDetails.cashFlowItems}
            accountId={props.accountId}
            companyName={props.companyName}
          />
        </Col>
      </StyledFlex>
    </>
  )
}

const StyledFlex = styled(Flex)`
  margin-top: 54px;
`

export default BankCashFlow
