{"info": {"_postman_id": "621db7e3-46b4-47b6-8f5a-e8cff319f2dd", "name": "CBW Payments", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json", "_exporter_id": "24833147"}, "item": [{"name": "Create PULL", "event": [{"listen": "prerequest", "script": {"exec": [""], "type": "text/javascript", "packages": {}}}, {"listen": "test", "script": {"exec": [""], "type": "text/javascript", "packages": {}}}], "request": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\r\n  \"method\": \"ledger.ach.transfer\",\r\n  \"id\": \"90544a73-462e-454c-9ac3-e008e64c8565\",\r\n  \"params\": {\r\n    \"payload\": {\r\n        \"channel\": \"ACH\",\r\n        \"product\": \"LINQPAL\",\r\n        \"program\": \"LINQPAL\",\r\n        \"reason\": \"Invoice payment, customerID: 6576fe09487f337ec1abfa5f merchantID: 64ef18c6a7da5b048d7e3e26\",\r\n        \"reference\": \"REF1617049508172:0\",\r\n        \"transactionAmount\": { \r\n            \"amount\": \"100\", \r\n            \"currency\": \"USD\" \r\n        },\r\n        \"transactionDateTime\": \"2024-03-14 00:00:00\",\r\n        \"transactionType\": \"ACH_PULL\",\r\n        \"debtor\": {\r\n            \"userType\": \"INDIVIDUAL\",\r\n            \"firstName\": \"AutoFirst8\",\r\n            \"lastName\": \"AutoLast8\"\r\n        },\r\n        \"debtorAccount\": {\r\n            \"identification\": \"*****************\",\r\n            \"identificationType\": \"ACCOUNT_NUMBER\",\r\n            \"identificationType2\": \"CHECKING\",\r\n            \"institution\": {\r\n                \"name\": \"Bank of America\",\r\n                \"identification\": \"*********\",\r\n                \"identificationType\": \"ABA\"\r\n            }\r\n        },\r\n        \"debtorContact\": { \"primaryEmail\": null, \"primaryPhone\": null },\r\n        \"debtorPostalAddress\": {\r\n            \"addressType\": \"HOUSE\",\r\n            \"addressLine1\": null,\r\n            \"city\": null,\r\n            \"state\": null,\r\n            \"zipCode\": null,\r\n            \"countryCode\": \"840\",\r\n            \"nationality\": \"USA\"\r\n        },\r\n        \"creditorAccount\": {\r\n            \"identification\": \"***************\",\r\n            \"identificationType\": \"ACCOUNT_NUMBER\"\r\n        }\r\n    },\r\n    \"api\": {\r\n      \"type\": \"TRANSFER\",\r\n      \"credential\": \"{{credential}}\",\r\n      \"signature\": \"MIGUAkgA6RrNvnEnWVBLb+S5NeXefLaRRxUsCorWjl6GCem5HrscP5H8NYBMSowN8avQvut30qmWQhwm6Ff/0G7Tw1bZoXv8dt4BthMCSAMcj/eSbhRaX+RdCn+oyecdC1aFu1THYWyhG0OYZnvk+3OUNAZfBSU+NNti3aDlr2VakpXEUWtdAJkO3rSTsSWPun9nYld85Q==\",\r\n      \"apiKey\": \"{{apikey}}\"\r\n    }\r\n  }\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{host}}", "host": ["{{host}}"]}}, "response": []}, {"name": "Create OUT", "request": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\r\n  \"method\": \"ledger.ach.transfer\",\r\n  \"id\": \"1\",\r\n  \"params\": {\r\n    \"payload\": {\r\n      \"channel\": \"ACH\",\r\n      \"transactionType\": \"ACH_OUT\",\r\n      \"product\": \"LEDGER\",\r\n      \"program\": \"LEDGER\",\r\n      \"transactionDateTime\": \"2021-01-19T06:20:25.000Z\",\r\n      \"reference\": \"REF1617049508172:0\",\r\n      \"reason\": \"Settlement\",\r\n      \"transactionAmount\": {\r\n        \"amount\": \"2000\",\r\n        \"currency\": \"USD\"\r\n      },\r\n      \"debtorAccount\": {\r\n        \"identification\": \"****************\",\r\n        \"identificationType\": \"ACCOUNT_NUMBER\",\r\n        \"identificationType2\": \"CHECKING\",\r\n        \"institution\": {\r\n          \"name\": \"ACME BANK\",\r\n          \"identification\": \"*********\",\r\n          \"identificationType\": \"ABA\"\r\n        }\r\n      },\r\n      \"creditor\": {\r\n        \"userType\": \"INDIVIDUAL\",\r\n        \"identification\": \"*********\",\r\n        \"identificationType\": \"SSN\",\r\n        \"firstName\": \"<PERSON>\",\r\n        \"middleName\": \"\",\r\n        \"lastName\": \"<PERSON>\"\r\n      },\r\n      \"creditorPostalAddress\": {\r\n        \"addressType\": \"HOUSE\",\r\n        \"addressLine1\": \"1\",\r\n        \"addressLine2\": \"Forest Street\",\r\n        \"city\": \"PHOENIX\",\r\n        \"state\": \"AZ\",\r\n        \"zipCode\": \"85123\",\r\n        \"countryCode\": \"840\"\r\n      },\r\n      \"creditorContact\": {\r\n        \"primaryEmail\": \"<EMAIL>\",\r\n        \"primaryPhone\": \"**********\"\r\n      },\r\n      \"creditorAccount\": {\r\n        \"identification\": \"****************\",\r\n        \"identificationType\": \"ACCOUNT_NUMBER\",\r\n        \"identificationType2\": \"CHECKING\",\r\n        \"institution\": {\r\n          \"name\": \"ACME BANK\",\r\n          \"identification\": \"*********\",\r\n          \"identificationType\": \"ABA\"\r\n        }\r\n      }\r\n    },\r\n    \"api\": {\r\n      \"type\": \"TRANSFER\",\r\n      \"credential\": \"{{credential}}\",\r\n      \"signature\": \"MIGUAkgA6RrNvnEnWVBLb+S5NeXefLaRRxUsCorWjl6GCem5HrscP5H8NYBMSowN8avQvut30qmWQhwm6Ff/0G7Tw1bZoXv8dt4BthMCSAMcj/eSbhRaX+RdCn+oyecdC1aFu1THYWyhG0OYZnvk+3OUNAZfBSU+NNti3aDlr2VakpXEUWtdAJkO3rSTsSWPun9nYld85Q==\",\r\n      \"apiKey\": \"{{apikey}}\"\r\n    }\r\n  }\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{host}}", "host": ["{{host}}"]}}, "response": []}, {"name": "Create INTERNAL", "request": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\r\n  \"method\": \"ledger.internal.transfer\",\r\n  \"id\": \"1\",\r\n  \"params\": {\r\n    \"payload\": {\r\n      \"channel\": \"INTERNAL\",\r\n      \"transactionType\": \"INTERNAL_TRANSFER\",\r\n      \"product\": \"LEDGER\",\r\n      \"program\": \"LEDGER\",\r\n      \"transactionDateTime\": \"2021-01-19T06:20:25.000Z\",\r\n      \"reference\": \"REF1617049508172:0\",\r\n      \"reason\": \"Settlement\",\r\n      \"transactionAmount\": {\r\n        \"amount\": \"2000\",\r\n        \"currency\": \"USD\"\r\n      },\r\n      \"debtorAccount\": {\r\n        \"identification\": \"****************\",\r\n        \"identificationType\": \"ACCOUNT_NUMBER\",\r\n        \"identificationType2\": \"CHECKING\",\r\n        \"institution\": {\r\n          \"name\": \"ACME BANK\",\r\n          \"identification\": \"*********\",\r\n          \"identificationType\": \"ABA\"\r\n        }\r\n      },\r\n      \"creditorAccount\": {\r\n        \"identification\": \"****************\",\r\n        \"identificationType\": \"ACCOUNT_NUMBER\",\r\n        \"identificationType2\": \"CHECKING\",\r\n        \"institution\": {\r\n          \"name\": \"ACME BANK\",\r\n          \"identification\": \"*********\",\r\n          \"identificationType\": \"ABA\"\r\n        }\r\n      }\r\n    },\r\n    \"api\": {\r\n      \"type\": \"TRANSFER\",\r\n      \"credential\": \"{{credential}}\",\r\n      \"signature\": \"MIGUAkgA6RrNvnEnWVBLb+S5NeXefLaRRxUsCorWjl6GCem5HrscP5H8NYBMSowN8avQvut30qmWQhwm6Ff/0G7Tw1bZoXv8dt4BthMCSAMcj/eSbhRaX+RdCn+oyecdC1aFu1THYWyhG0OYZnvk+3OUNAZfBSU+NNti3aDlr2VakpXEUWtdAJkO3rSTsSWPun9nYld85Q==\",\r\n      \"apiKey\": \"{{apikey}}\"\r\n    }\r\n  }\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{host}}", "host": ["{{host}}"]}}, "response": []}, {"name": "Transaction Status", "request": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\r\n  \"method\": \"ledger.ach.transfer\",\r\n  \"id\": \"1\",\r\n  \"params\": {\r\n    \"payload\": {\r\n      \"reference\": \"REF1617049508172:0\",\r\n      \"transactionType\": \"TRANSACTION_STATUS\",\r\n      \"originalReference\": \"LEDGER1558130492492\",\r\n      \"originalExternalReference\": \"REF1617316443042\",\r\n      \"debtorAccount\": {\r\n        \"identification\": \"****************\",\r\n        \"identificationType\": \"ACCOUNT_NUMBER\",\r\n        \"identificationType2\": \"CHECKING\",\r\n        \"institution\": {\r\n          \"name\": \"ACME BANK\",\r\n          \"identification\": \"*********\",\r\n          \"identificationType\": \"ABA\"\r\n        }\r\n      },\r\n      \"product\": \"LEDGER\",\r\n      \"channel\": \"ACH\",\r\n      \"program\": \"LEDGER\"\r\n    },\r\n    \"api\": {\r\n      \"type\": \"TRANSFER\",\r\n      \"credential\": \"{{credential}}\",\r\n      \"signature\": \"MIGUAkgA6RrNvnEnWVBLb+S5NeXefLaRRxUsCorWjl6GCem5HrscP5H8NYBMSowN8avQvut30qmWQhwm6Ff/0G7Tw1bZoXv8dt4BthMCSAMcj/eSbhRaX+RdCn+oyecdC1aFu1THYWyhG0OYZnvk+3OUNAZfBSU+NNti3aDlr2VakpXEUWtdAJkO3rSTsSWPun9nYld85Q==\",\r\n      \"apiKey\": \"{{apikey}}\"\r\n    }\r\n  }\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{host}}", "host": ["{{host}}"]}}, "response": []}, {"name": "Balance Enquiry", "request": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\r\n  \"method\": \"ledger.ach.transfer\",\r\n  \"id\": \"1\",\r\n  \"params\": {\r\n    \"payload\": {\r\n      \"reference\": \"REF1617049508172:0\",\r\n      \"transactionType\": \"BALANCE_ENQUIRY\",\r\n      \"originalReference\": \"LEDGER1558130492492\",\r\n      \"originalExternalReference\": \"REF1617316443042\",\r\n      \"debtorAccount\": {\r\n        \"identification\": \"****************\",\r\n        \"identificationType\": \"ACCOUNT_NUMBER\",\r\n        \"identificationType2\": \"CHECKING\",\r\n        \"institution\": {\r\n          \"name\": \"ACME BANK\",\r\n          \"identification\": \"*********\",\r\n          \"identificationType\": \"ABA\"\r\n        }\r\n      },\r\n      \"product\": \"LEDGER\",\r\n      \"channel\": \"ADMIN\",\r\n      \"program\": \"***************\"\r\n    },\r\n    \"api\": {\r\n      \"type\": \"TRANSFER\",\r\n      \"credential\": \"{{credential}}\",\r\n      \"signature\": \"MIGUAkgA6RrNvnEnWVBLb+S5NeXefLaRRxUsCorWjl6GCem5HrscP5H8NYBMSowN8avQvut30qmWQhwm6Ff/0G7Tw1bZoXv8dt4BthMCSAMcj/eSbhRaX+RdCn+oyecdC1aFu1THYWyhG0OYZnvk+3OUNAZfBSU+NNti3aDlr2VakpXEUWtdAJkO3rSTsSWPun9nYld85Q==\",\r\n      \"apiKey\": \"{{apikey}}\"\r\n    }\r\n  }\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{host}}", "host": ["{{host}}"]}}, "response": []}, {"name": "Transaction History", "request": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\r\n  \"method\": \"ledger.ach.transfer\",\r\n  \"id\": \"1\",\r\n  \"params\": {\r\n    \"payload\": {\r\n      \"reference\": \"REF1617049508172:0\",\r\n      \"transactionType\": \"TRANSACTION_HISTORY\",\r\n      \"query\": {\r\n        \"dateFrom\": \"2021-08-20T00:00:00.000Z\",\r\n        \"dateTo\": \"2021-08-21T00:00:00.000Z\",\r\n        \"pagination\": {\r\n          \"offset\": 0\r\n        }\r\n      },\r\n      \"debtorAccount\": {\r\n        \"identification\": \"****************\",\r\n        \"identificationType\": \"ACCOUNT_NUMBER\",\r\n        \"identificationType2\": \"CHECKING\",\r\n        \"institution\": {\r\n          \"name\": \"ACME BANK\",\r\n          \"identification\": \"*********\",\r\n          \"identificationType\": \"ABA\"\r\n        }\r\n      },\r\n      \"product\": \"LEDGER\",\r\n      \"channel\": \"ADMIN\",\r\n      \"program\": \"***************\"\r\n    },\r\n    \"api\": {\r\n      \"type\": \"TRANSFER\",\r\n      \"credential\": \"{{credential}}\",\r\n      \"signature\": \"MIGUAkgA6RrNvnEnWVBLb+S5NeXefLaRRxUsCorWjl6GCem5HrscP5H8NYBMSowN8avQvut30qmWQhwm6Ff/0G7Tw1bZoXv8dt4BthMCSAMcj/eSbhRaX+RdCn+oyecdC1aFu1THYWyhG0OYZnvk+3OUNAZfBSU+NNti3aDlr2VakpXEUWtdAJkO3rSTsSWPun9nYld85Q==\",\r\n      \"apiKey\": \"{{apikey}}\"\r\n    }\r\n  }\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{host}}", "host": ["{{host}}"]}}, "response": []}]}