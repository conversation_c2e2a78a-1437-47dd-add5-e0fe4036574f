using System.Runtime.Serialization;
using System.Text.Json.Serialization;

namespace BlueTape.Aion.DataAccess.External.Models.InstantTransfer.Request;

[DataContract]
public class CreateInstantTransferRequest
{
    [JsonPropertyName("FromAccountNumber")]
    public string FromAccountNumber { get; set; }
    
    [JsonPropertyName("amount")]
    public string Amount { get; set; }
    
    [JsonPropertyName("TransferMethod")]
    public string TransferMethodId { get; set; }

    [JsonPropertyName("SenderName")]
    public string SenderName { get; set; }
    
    [JsonPropertyName("ReceiverName")]
    public string ReceiverName { get; set; }

    [JsonPropertyName("senderDescription")]
    public string SenderDescription { get; set; }
    
    [JsonPropertyName("receiverDescription")]
    public string ReceiverDescription { get; set; }

    [JsonPropertyName("contextIdentifier")]
    public string ContextIdentifier { get; set; }
    
    [JsonPropertyName("purpose")]
    public string Purpose { get; set; }
}