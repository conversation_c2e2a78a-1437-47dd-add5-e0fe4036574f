import {expect} from '@playwright/test';
import {BaseTest, test} from '../../test-utils';
import {sendLMSRequest, postBasisRequest} from '../../../api/common/lms-send-request';
import {HttpStatusCode} from 'axios';

const constants = JSON.parse(JSON.stringify(require('../../../constants/LMStestData.json')));

test.describe(`Create Basis Point API Tests @LMS @API`, async () => {
    let basisId: string;
    const basisPointValue: number = constants.basisPoint.value;
    const currentDate = `${BaseTest.getCurrentDate()}`;

    test.afterEach(async () => {
        await sendLMSRequest('delete', `BasisPoint/${basisId}`);
    });

    test('Create basis point. @lms', async () => {
        const response = await postBasisRequest(basisPointValue, currentDate);

        basisId = response.data.id;

        expect(response.status, `Status code ${HttpStatusCode.Ok}`)
            .toEqual(HttpStatusCode.Ok);

        expect(response.data, `Response contains Object`)
            .toEqual(expect.any(Object));
    });

    /**
     * Negative Tests
     */

    test('Cannot create basis point with Basis Point Value = 0. @lms', async () => {
        const response = await postBasisRequest(0, currentDate);

        expect(response.response.status, `Status code ${HttpStatusCode.BadRequest}`)
            .toEqual(HttpStatusCode.BadRequest);

        expect(response.response.data, `Response contains Object`)
            .toEqual(expect.any(Object));
    });

    test('Cannot create basis point with Basis Point Value = null. @lms', async () => {
        const response = await postBasisRequest(null, currentDate);

        expect(response.response.status, `Status code ${HttpStatusCode.BadRequest}`)
            .toEqual(HttpStatusCode.BadRequest);

        expect(response.response.data, `Response contains Object`)
            .toEqual(expect.any(Object));
    });

    test('Cannot create basis point with negative Basis Point Value = -1. @lms', async () => {
        const response = await postBasisRequest(-1, currentDate);

        expect(response.response.status, `Status code ${HttpStatusCode.BadRequest}`)
            .toEqual(HttpStatusCode.BadRequest);

        expect(response.response.data, `Response contains Object`)
            .toEqual(expect.any(Object));
    });

    test('Cannot create basis point with Valid Date = null. @lms', async () => {
        const response = await postBasisRequest(basisPointValue, null);

        expect(response.response.status, `Status code ${HttpStatusCode.BadRequest}`)
            .toEqual(HttpStatusCode.BadRequest);

        expect(response.response.data, `Response contains Object`)
            .toEqual(expect.any(Object));
    });
});
