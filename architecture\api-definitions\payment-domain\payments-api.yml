openapi: '3.0.0'
info:
  version: '0.0.1'
  title: 'Payments API'
  description: API definition for private Payments API service
paths:
  /paymentRequests:
    post:
      tags:
        - PaymentRequests
      summary: Creates a payment request
      description: Creates a payment request
      operationId: createPaymentRequest
      requestBody:
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/CreatePaymentRequest"
      responses:
        201:
          description: Payment request created
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/CreatePaymentRequestResponse'
        400:
          description: Invalid request
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
    get:
      tags: 
        - PaymentRequests
      summary: Lists payment requests by various filtering options
      description: Lists payment requests by various filtering options
      operationId: getPaymentRequests
      parameters:
        - name: id
          description: Identifier of the payment request
          example: 6df30249-c7f0-4f17-b06f-c29b2fee0a62
          in: query
          required: false
          schema:
            type: string
        - name: customerId
          description: Identifier of the customer
          example: 6df30249-c7f0-4f17-b06f-c29b2fee0a62
          in: query
          required: false
          schema:
            type: string
        - name: customerName
          description: Name of the customer (startsWith)
          example: Deluxe
          in: query
          required: false
          schema:
            type: string
        - name: sellerId
          description: Identifier of the seller
          example: 6df30249-c7f0-4f17-b06f-c29b2fee0a62
          in: query
          required: false
          schema:
            type: string
        - name: sellerName
          description: Name of the seller (startsWith)
          example: Acme
          in: query
          required: false
          schema:
            type: string
        - name: payableId
          description: Identifier of the payable
          example: 6df30249-c7f0-4f17-b06f-c29b2fee0a62
          in: query
          required: false
          schema:
            type: string
        - name: from
          description: Date from
          example: 2023-01-01
          in: query
          required: false
          schema:
            type: string
            format: date
        - name: to
          description: Date to
          example: 2023-12-31
          in: query
          required: false
          schema:
            type: string
            format: date
        - name: flowTemplateCode
          description: Identifier of the flow template (contains)
          example: CREATE.PAYNOW.INVOICE_PAYMENT
          in: query
          required: false
          schema:
            type: string
        - name: page
          description: The page number
          example: 2
          in: query
          required: false
          schema:
            type: number
        - name: items
          description: The items number on a page
          example: 50
          in: query
          required: false
          schema:
            type: number
      responses:
        200:
          description: Payment request created
          content:
            application/json:              
              schema:
                $ref: '#/components/schemas/PagedPayments'
        400:
          description: Invalid request
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
  /paymentRequests/{id}/transactions:
    post:
      tags: 
        - PaymentRequests
      summary: Creates transaction for a payment request
      description: Creates transaction for a payment request
      operationId: createTransaction
      parameters:
        - name: id
          description: Identifier of the payment request
          example: 6df30249-c7f0-4f17-b06f-c29b2fee0a62
          in: path
          required: true
          schema:
            type: string
            nullable: false
      requestBody:
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/CreateTransactionRequest"
      responses:
        201:
          description: Transaction is created
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/CreateTransactionResponse'
        400:
          description: Invalid request
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
        404:
          description: Payment request not found
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
  /paymentRequests/{id}/transactions/{transactionId}/execute:
    post:
      tags: 
        - PaymentRequests
      summary: Executes a placed transaction
      description: Executes a placed transaction
      operationId: executeTransaction
      parameters:
        - name: id
          description: Identifier of the payment request
          example: 6df30249-c7f0-4f17-b06f-c29b2fee0a62
          in: path
          required: true
          schema:
            type: string
            nullable: false
        - name: transactionId
          description: Identifier of the transaction id
          example: 6df30249-c7f0-4f17-b06f-c29b2fee0a62
          in: path
          required: true
          schema:
            type: string
            nullable: false
      requestBody:
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/EmptyBody"
      responses:
        201:
          description: Transaction is executed
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/CreateTransactionResponse'
        400:
          description: Invalid request
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
        404:
          description: Payment request not found
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
  /paymentRequests/{id}:
    get:
      tags:
        - PaymentRequests
      summary: Gets a payment request by id (for compatibility)
      description: Gets a payment request by id (for compatibility)
      operationId: getPaymentRequestById
      parameters:
        - name: id
          description: Identifier of the payment request
          example: 6df30249-c7f0-4f17-b06f-c29b2fee0a62
          in: path
          required: true
          schema:
            type: string
            nullable: false
      responses:
        200:
          description: The payment request
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/PaymentRequest'
        404:
          description: Not found
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
    patch:
      tags:
        - PaymentRequests
      summary: Updates a payment request
      description: Updates a payment request
      operationId: updatePaymentRequest
      parameters:
        - name: id
          description: Identifier of the payment request
          example: 6df30249-c7f0-4f17-b06f-c29b2fee0a62
          in: path
          required: true
          schema:
            type: string
            nullable: false
      requestBody:
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/PatchPaymentRequest"
      responses:
        200:
          description: The updated payment request
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/PaymentRequest'
        404:
          description: Not found
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
  /paymentRequests/payable/{id}/is-paymentrequest-exist:
    get:
      tags:
        - PaymentRequests
      summary: Checks whether a payment request exists for a payable
      description: Checks whether a payment request exists for a payable
      operationId: getPaymentRequestExistsByInvoiceId
      parameters:
        - name: id
          description: Identifier of payable
          example: 6df30249-c7f0-4f17-b06f-c29b2fee0a62
          in: path
          required: true
          schema:
            type: string
            nullable: false
      responses:
        200:
          description: An array of payment requests where payable was involved.
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/PaymentRequest'
        400:
          description: Bad request
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
  /admin/paymentRequests/{id}/transactions/{transactionId}/retry:
    patch:
      tags:
        - PaymentRequests Admin
      summary: Retries a failed transaction manually
      description: Retries a failed transaction manually
      operationId: retryPaymentTransaction
      parameters:
        - name: id
          description: Identifier of the payment request
          example: 6df30249-c7f0-4f17-b06f-c29b2fee0a62
          in: path
          required: true
          schema:
            type: string
            nullable: false
        - name: transactionId
          description: Identifier of the transaction id
          example: 6df30249-c7f0-4f17-b06f-c29b2fee0a62
          in: path
          required: true
          schema:
            type: string
            nullable: false
        - name: userId
          description: Identifier of the user who approved or rejected
          example: 62c5e900cd65d31d3f25c34c
          in: header
          required: true
          schema:
            type: string
            nullable: false
      requestBody:
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/RetryTransactionRequest"
      responses:
        200:
          description: Transaction details
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Transaction'
        400:
          description: Bad request
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
        404:
          description: Not found
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
  /test/paymentRequests/{id}/recall:
    patch:
      tags:
        - PaymentRequests Test
      summary: Recalls a payment request (PullFromCustomer transaction)
      description: Recalls a payment request (PullFromCustomer transaction)
      operationId: recallPaymentRequest
      parameters:
        - name: id
          description: Identifier of the payment request
          example: 6df30249-c7f0-4f17-b06f-c29b2fee0a62
          in: path
          required: true
          schema:
            type: string
            nullable: false
      requestBody:
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/RecallPaymentRequest"
      responses:
        200:
          description: Transaction details
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/PaymentRequest'
        400:
          description: Bad request
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
        404:
          description: Not found
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
  /transactions/{id}:
    get:
      tags:
        - Transactions
      summary: Gets a single transaction full details
      description: Gets a single transaction full details
      operationId: getTransaction
      parameters:
        - name: id
          description: Identifier of the transaction
          example: 6df30249-c7f0-4f17-b06f-c29b2fee0a62
          in: path
          required: true
          schema:
            type: string
            nullable: false
      responses:
        200:
          description: Transaction details
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Transaction'
        404:
          description: Not found
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
  /transactionStatusUpdate:
    post: 
      tags:
        - QueueEvents
      summary: Payload definition of transaction status updates from queue
      description: Payload definition of transaction status updates from queue
      operationId: updateTransactionStatusFromQueue
      requestBody: 
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/UpdateTransactionStatusFromQueueRequest'
      responses:
        201:
          description: Consumed (just for compatibility)
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/EmptyResponse'
        400:
          description: Invalid request (just for compatibility)
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
      
components:
  schemas:
    CreateTransactionRequest:
      type: object
      properties:
        paymentMethod:
          type: string
          enum:
            - ach
            - card
        transactionType:
          type: string
          enum:
            - achPull
            - achPush
            - achInternal
            - cardPull
        originatorAccountId:
          type: string
        receiverAccountId:
          type: string
        amount:
          type: number
          format: decimal
          example: 22000.00
          pattern: '[0-9]*\.[0-9]{2}'
          multipleOf: 0.01
        discount:
          type: number
          format: decimal
          example: 22000.00
          pattern: '[0-9]*\.[0-9]{2}'
          multipleOf: 0.01
        currency:
          description: Currency of the transaction, could not differ from payment request currency
          type: string
          example: USD
        date:
          type: string
          format: date-time
          example: 2023-12-12T00:00:00.000Z
        transactionNumber:
          type: string
        referenceNumber:
          type: string
        reason:
          type: string
        metaData:
          type: object
    CreateTransactionResponse:
      type: object
    Transaction:
      allOf:
        - type: object
          additionalProperties: false
          properties:
            id:
              type: string
              description: Id of transaction
              example: fb5637b2e5f3
            createdAt:
              type: string
              format: date-time
              description: Date of creation
              example: 2014-04-05T12:59:59.000Z
            createdBy:
              type: string
              description: Creator of item, free text or userId
            updatedAt:
              type: string
              format: date-time
              description: Date of creation
              example: 2014-04-05T12:59:59.000Z
            updatedBy:
              type: string
              description: Last updater of item, free text or userId
        - $ref: "#/components/schemas/CreateTransactionRequest"
        - type: object
          properties:
            status:
              type: string
              enum:
                - placed
                - processing
                - processed
                - cleared
                - failed
                - returned
                - error
                - canceled
                - scheduled
                - recalled
              default: placed
              example: placed
    CreatePaymentRequest:
      type: object
      properties:
        subjectType:
          type: string
          enum:
            - payable
            - draw
        requestType:
          type: string
          enum:
            - invoicePayment
            - invoiceRefund
            - invoiceVoid
            - invoiceFinalPayment
        paymentMethod:
          type: string
          enum:
            - ach
            - card
            - tradeCredit
        templateName:
          type: string
        payerId:
          type: string
        payeeId:
          type: string
        sellerId:
          type: string
        creditId:
          type: string
        amount:
          type: number
          format: decimal
          example: 22000.00
          pattern: '[0-9]*\.[0-9]{2}'
          multipleOf: 0.01
        feeAmount:
          type: number
          format: decimal
          example: 22000.00
          pattern: '[0-9]*\.[0-9]{2}'
          multipleOf: 0.01
        currency:
          description: Currency of the payment request
          type: string
          example: USD
        status:
          type: string
          enum:
            - requested
            - processing
            - failed
            - settled
            - paused
          default: requested
          example: requested
        date:
          type: string
          format: date-time
          example: 2023-12-12T00:00:00.000Z
        payablesDetails:
          type: array
          items:
            $ref: '#/components/schemas/PayableDetail'
    CreatePaymentRequestResponse:
      type: object
    EmptyBody:
      type: object
      nullable: true
    EmptyResponse:
      type: object
      nullable: true
    Error:
      type: object
      required:
        - message
      properties:
        message:
          description: A human readable error message
          type: string
    PaymentRequest:
      allOf:
        - type: object
          additionalProperties: false
          properties:
            id:
              type: string
              description: Id of payment request
              example: fb5637b2e5f3
            createdAt:
              type: string
              format: date-time
              description: Date of creation
              example: 2014-04-05T12:59:59.000Z
            createdBy:
              type: string
              description: Creator of item, free text or userId
            updatedAt:
              type: string
              format: date-time
              description: Date of creation
              example: 2014-04-05T12:59:59.000Z
            updatedBy:
              type: string
              description: Last updater item, free text or userId
        - $ref: "#/components/schemas/CreatePaymentRequest"
        - type: object
          properties:
            transactions:
              type: array
              items:
                $ref: "#/components/schemas/Transaction"
    PayableDetail:
      type: object
      required: 
        - id
        - payableType
        - payableAmount
        - requestedAmount
      properties:
        id:
          type: string
        payableType:
          type: string
          enum:
            - invoice
            - salesOrder
          example: invoice
        payableAmount:
          type: number
          format: decimal
          example: 55000.00
          pattern: '[0-9]*\.[0-9]{2}'
          multipleOf: 0.01
        requestedAmount:
          type: number
          format: decimal
          example: 22000.00
          pattern: '[0-9]*\.[0-9]{2}'
          multipleOf: 0.01
    PatchPaymentRequest:
      type: object
      properties:
        updatedBy:
          type: string
        newStatus:
          type: string
          enum:
            - requested
            - processing
            - failed
            - settled
    UpdateTransactionStatusFromQueueRequest:
      type: object
      required: 
        - flowTemplateCode
      properties:
        eventType:
          type: string
          example: UPDATE.ACH_TRANSACTION_STATUS
        blueTapeCorrelationId:
          type: string
          example: 657056ddff868bcdb89bb8f1
        createdBy:
          type: string
          example: TransactionStatusUpdateJob
        details:
          $ref: "#/components/schemas/UpdateTransactionStatusFromQueueDetails"
    UpdateTransactionStatusFromQueueDetails:
      type: object
      properties:
        processedAt:
          type: string
          format: date-time
          example: 2023-12-12T00:00:00.000Z
        transactionId:
          type: string
          description: The BlueTape's transaction id
        referenceId:
          type: string
          description: The payment provider's reference id
        oldStatus:
          type: string
          enum:
            - placed
            - processing
            - processed
            - cleared
            - failed
            - returned
            - error
            - canceled
            - scheduled
            - recalled
        newStatus:
          type: string
          enum:
            - placed
            - processing
            - processed
            - cleared
            - failed
            - returned
            - error
            - canceled
            - scheduled
            - recalled
        error:
          type: string
    PagedPayments:
      type: object
      properties:
        pageNumber:
          type: number
        pagesCount:
          type: number
        totalCount:
          type: number
        result:
          type: array
          items:
            $ref: '#/components/schemas/PaymentRequest'
    RetryTransactionRequest:
      type: object
      nullable: true
    RecallPaymentRequest:
      type: object
      nullable: true
  securitySchemes:
    ApiKey:
      type: apiKey
      in: header
      name: X-Api-Key
security:
  - ApiKey: []