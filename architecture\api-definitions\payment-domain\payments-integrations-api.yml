openapi: '3.0.0'
info:
  version: '0.0.1'
  title: 'Payment Integrations API (high level)'
  description: API definition for connecting payment providers (ACH and Card)
paths:
  /transactions/ach/pull:
    post:
      tags:
        - ACH
      summary: Creates a new ACH pull transaction
      description: Creates a new ACH pull transaction
      operationId: createAchPullTransaction
      requestBody:
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/TransactionRequest"
      responses:
        201:
          description: Transaction successfully created
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/PaymentTransaction'
        400:
          description: Invalid request
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
        401:
          description: Unauthorized
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
        404:
          description: Not found
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
        500:
          description: Unexpected error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
  /transactions/ach/out:
    post:
      tags:
        - ACH
      summary: Creates a new ACH out transaction
      description: Creates a new ACH out transaction
      operationId: createAchOutTransaction
      requestBody:
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/TransactionRequest"
      responses:
        201:
          description: Transaction successfully created
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/PaymentTransaction'
        400:
          description: Invalid request
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
        401:
          description: Unauthorized
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
        404:
          description: Not found
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
        500:
          description: Unexpected error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
  /transactions/ach/internal:
    post:
      tags:
        - ACH
      summary: Creates a new ACH internal transaction
      description: Creates a new ACH internal transaction
      operationId: createAchInternalTransaction
      requestBody:
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/TransactionRequest"
      responses:
        201:
          description: Transaction successfully created
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/PaymentTransaction'
        400:
          description: Invalid request
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
        401:
          description: Unauthorized
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
        404:
          description: Not found
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
        500:
          description: Unexpected error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
  /transactions/ach/{id}/return:
    post:
      tags:
        - ACH
      summary: Creates a return ACH transaction
      description: Creates a return ACH transaction
      operationId: createAchReturnTransaction
      parameters:
        - name: id
          description: Identifier of the transaction
          example: 6df30249-c7f0-4f17-b06f-c29b2fee0a62
          in: path
          required: true
          schema:
            type: string
            nullable: false
      requestBody:
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/TransactionRequest"
      responses:
        201:
          description: Return transaction successfully created
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/PaymentTransaction'
        400:
          description: Invalid request
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
        401:
          description: Unauthorized
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
        404:
          description: Not found
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
        500:
          description: Unexpected error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
  /transactions/ach:
    get:
      tags:
        - ACH
      summary: Gets transactions list
      description: Gets transactions list
      operationId: getTransactions
      parameters:
        - name: from
          description: Date from
          example: 
          in: query
          required: false
          schema:
            type: string
            format: datetime
            nullable: true
        - name: to
          description: Date to
          example: 
          in: query
          required: false
          schema:
            type: string
            format: datetime
            nullable: true
        - name: debtorAccount
          description: Debtor account
          example: 
          in: query
          required: true
          schema:
            type: string
            nullable: false
      responses:
        200:
          description: Transaction status
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/PaymentTransaction'
        400:
          description: Invalid request
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
  /transactions/ach/{id}:
    get:
      tags:
        - ACH
      summary: Gets transaction details, if possible
      description: Gets transaction details, if possible
      operationId: getTransactionDetails
      parameters:
        - name: id
          description: Identifier of the transaction
          example: 6df30249-c7f0-4f17-b06f-c29b2fee0a62
          in: path
          required: true
          schema:
            type: string
            nullable: false
      responses:
        200:
          description: Transaction details
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/PaymentTransaction'
        404:
          description: Not found
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
  /transactions/ach/{id}/status:
    get:
      tags:
        - ACH
      summary: Gets transaction status
      description: Gets transaction status
      operationId: getTransactionStatus
      parameters:
        - name: id
          description: Identifier of the transaction
          example: 6df30249-c7f0-4f17-b06f-c29b2fee0a62
          in: path
          required: true
          schema:
            type: string
            nullable: false
      responses:
        200:
          description: Transaction status
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/PaymentTransaction'
        404:
          description: Not found
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
  /transactions/card:
    post:
      tags: 
        - Card
      summary: Creates a new card transaction
      description: Creates a new card transaction
      operationId: createCardTransaction
      requestBody:
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/TransactionRequest"
      responses:
        201:
          description: Transaction successfully created
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/PaymentTransaction'
        400:
          description: Invalid request
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
  /transactions/card/{id}:
    delete:
      tags:
        - Card
      summary: Cancels a card transaction
      description: Cancels a new card transaction
      operationId: cancelCardTransaction
      parameters:
        - name: id
          description: Identifier of the transaction
          example: 6df30249-c7f0-4f17-b06f-c29b2fee0a62
          in: path
          required: true
          schema:
            type: string
            nullable: false
      responses:
        200:
          description: Transaction
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/EmptyResponse'
        404:
          description: Not found
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
  /cards/{id}:
    get:
      tags:
        - Card
      summary: Gets card details
      description: Gets a card's details
      operationId: getCardDetails
      parameters:
        - name: id
          description: Identifier of card
          example: 6df30249-c7f0-4f17-b06f-c29b2fee0a62
          in: path
          required: true
          schema:
            type: string
            nullable: false
      responses:
        200:
          description: Transaction
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/CardResponse'
        404:
          description: Not found
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
  /cards/{id}/deactivate:
    put:
      tags:
        - Card
      summary: Deactivates a card
      description: Deactivates a card
      operationId: updateCardDeactivate
      parameters:
        - name: id
          description: Identifier of card
          example: 6df30249-c7f0-4f17-b06f-c29b2fee0a62
          in: path
          required: true
          schema:
            type: string
            nullable: false
      requestBody:
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/CardDeactivateRequest"
      responses:
        200:
          description: Card deactivated
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/CardDeactivateResponse'
        400:
          description: Invalid request
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
  /cards/{id}/limit:
    put:
      tags:
        - Card
      summary: Sets card limit
      description: Sets card limit
      operationId: updateCardLimit
      parameters:
        - name: id
          description: Identifier of card
          example: 6df30249-c7f0-4f17-b06f-c29b2fee0a62
          in: path
          required: true
          schema:
            type: string
            nullable: false
      requestBody:
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/CardLimitRequest"
      responses:
        200:
          description: Card limit has been set
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/CardLimitResponse'
        400:
          description: Invalid request
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
  /accounts:
    post:
      tags:
        - Card
      summary: Creates a new account
      description: Creates a new account
      operationId: createAccount
      requestBody:
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/AccountRequest"
      responses:
        201:
          description: Account successfully created
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/AccountResponse'
        400:
          description: Invalid request
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
  /virtualcards:
    post:
      tags:
        - VirtualCard
      summary: Creates virtual card
      description: Creates virtual card
      operationId: createVirtualCard
      requestBody:
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/CreateVirtualCardRequest"
      responses:
        201:
          description: Virtual card successfully created
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/CreateVirtualCardResponse'
        400:
          description: Invalid request
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
  /virtualcards/{id}:
    get:
      tags:
        - VirtualCard
      summary: Gets a virtual card
      description: Gets a virtual card
      operationId: getVirtualCard
      parameters:
        - name: id
          description: Identifier of the virtual card
          example: 6df30249-c7f0-4f17-b06f-c29b2fee0a62
          in: path
          required: true
          schema:
            type: string
            nullable: false
      responses:
        200:
          description: The virtual card
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/VirtualCardResponse'
        400:
          description: Invalid request
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
  /virtualcards/{id}/cardholder:
    get:
      tags:
        - VirtualCard
      summary: Gets cardholder of a virtual card
      description: Gets cardholder of a virtual card
      operationId: getCardHolderOfVirtualCard
      parameters:
        - name: id
          description: Identifier of the virtual card
          example: 6df30249-c7f0-4f17-b06f-c29b2fee0a62
          in: path
          required: true
          schema:
            type: string
            nullable: false
      responses:
        200:
          description: Virtual card holder
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/VirtualCardHolderResponse'
        400:
          description: Invalid request
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
        404:
          description: Not found
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
    post:
      tags:
        - VirtualCard
      summary: Adds cardholder to a virtual card
      description: Adds cardholder to a virtual card
      operationId: createCardHolderForVirtualCard
      parameters:
        - name: id
          description: Identifier of the virtual card
          example: 6df30249-c7f0-4f17-b06f-c29b2fee0a62
          in: path
          required: true
          schema:
            type: string
            nullable: false
      requestBody:
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/CreateVirtualCardHolderRequest"
      responses:
        201:
          description: Virtual card holder successfully created
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/CreateVirtualCardHolderResponse'
        400:
          description: Invalid request
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
        404:
          description: Not found
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
  /virtualcards/{id}/cvv:
    get:
      tags:
        - VirtualCard
      summary: Gets virtual card cvv
      description: Gets virtual card cvv
      operationId: getVirtualCardCVV
      parameters:
        - name: id
          description: Identifier of the virtual card
          example: 6df30249-c7f0-4f17-b06f-c29b2fee0a62
          in: path
          required: true
          schema:
            type: string
            nullable: false
      responses:
        200:
          description: Virtual card cvv
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/VirtualCardCVVResponse'
        400:
          description: Invalid request
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
        404:
          description: Not found
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
  /virtualcards/{id}/countrygroup:
    get:
      tags:
        - VirtualCard
      summary: Gets virtual card country group
      description: Gets virtual card country group
      operationId: getVirtualCardCountryGroup
      parameters:
        - name: id
          description: Identifier of the virtual card
          example: 6df30249-c7f0-4f17-b06f-c29b2fee0a62
          in: path
          required: true
          schema:
            type: string
            nullable: false
      responses:
        200:
          description: Virtual card country group
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/VirtualCardCountryGroupResponse'
        400:
          description: Invalid request
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
        404:
          description: Not found
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
  /virtualcards/{id}/mccgroup:
    get:
      tags:
        - VirtualCard
      summary: Gets virtual card mcc group
      description: Gets virtual card mcc group
      operationId: getVirtualCardMCCGroup
      parameters:
        - name: id
          description: Identifier of the virtual card
          example: 6df30249-c7f0-4f17-b06f-c29b2fee0a62
          in: path
          required: true
          schema:
            type: string
            nullable: false
      responses:
        200:
          description: Virtual card mcc group
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/VirtualCardMccGroupResponse'
        400:
          description: Invalid request
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
        404:
          description: Not found
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
  /virtualcards/{id}/limit:
    put:
      tags:
        - VirtualCard
      summary: Sets virtual card limit
      description: Sets virtual card limit
      operationId: updateVirtualCardLimit
      parameters:
        - name: id
          description: Identifier of the virtual card
          example: 6df30249-c7f0-4f17-b06f-c29b2fee0a62
          in: path
          required: true
          schema:
            type: string
            nullable: false
      requestBody:
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/UpdateVirtualCardLimitRequest"
      responses:
        200:
          description: The virtual card
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/UpdateVirtualCardLimitResponse'
        400:
          description: Invalid request
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'              
        404:
          description: Not found
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
components:
  schemas:
    TransactionRequest:
      type: object
      required:
        - id
        - operationId
        - amount
        - currency
        - feeAmount
        - paymentMethod
        - status
        - effectiveDate
        - targetAccount
        - number
        - transactionStatus
        - method
        - reference
        - statusCode
        - createdAt
        - updatedAt
        - identificationNumber
        - direction
        - details
      properties:
        operationId:
          type: string
          description: Identifier of operation, which initiated the transaction (BlueTape)
          example: 637dff014189f7dd1fa510a1
        createdAt:
          type: string
          format: datetime
          description: Time of transaction creating. Immutable after creation.
          example: 2023-01-04T09:47:57.477Z
        updatedAt:
          type: string
          format: datetime
          description: Time of last transaction update. In the time of creation equals to createdAt.
          example: 2023-01-04T09:47:57.477Z
        payer:
          $ref: '#/components/schemas/ParticipantDetails'
          description: The requester, the payer of the operation.
        payee:
          $ref: '#/components/schemas/ParticipantDetails'
          description: The requested, the payee of the operation.
        sourceAccount:
          $ref: '#/components/schemas/Account'
          description: Source account details of transaction
        targetAccount:
          $ref: '#/components/schemas/Account'
          description: Target account details of transaction
        amount:
          type: number
          format: decimal
          description: Amount of transaction with fixed 2 decimals
          pattern: '[0-9]*\.[0-9]{2}'
          multipleOf: 0.01
          example: 0.00
        currency:
          type: string
          description: Currency code of transaction (ISO 4217)
          pattern: '^[A-Z]{3}$'
          example: USD
        feeAmount:
          type: number
          format: decimal
          description: Amount of applied fees with fixed 2 decimals
          pattern: '[0-9]*\.[0-9]{2}'
          multipleOf: 0.01
          example: 0.00
        paymentMethod:
          type: string
          description: Payment method
          example: ach
          enum:
            - card
            - ach
            - loan
        direction:
          type: string
          description: Direction of transaction (* should be removed)
          example: debit
          enum:
            - debit
            - credit
        effectiveDate:
          type: string
          description: Effective entry date
          format: datetime
          example: 2023-01-04T00:00:00.000Z
        status:
          type: string
          description: Status of transaction
          example: placed
          enum:
            - pending
            - processing
            - success
            - error
            - canceled
            - scheduled
        statusHistory:
          type: array
          description: Status change history
          nullable: true
          items:
            $ref: '#/components/schemas/StatusHistoryItem'
        addenda:
          type: object
          description: Additional values stored along with transaction data
          nullable: true
        details:
          type: object
          oneOf:
            - $ref: '#/components/schemas/AchTransactionDetails'
            - $ref: '#/components/schemas/CardTransactionDetails'
    PaymentTransaction:
      allOf:
        - type: object
          required:
            - id
          properties:
            id:
              type: string
              description: Identifier of transaction (BlueTape)
              example: f7dd1fa510a1637dff014189
        - $ref: '#/components/schemas/TransactionRequest'
    ParticipantDetails:
      type: object
      required:
        - id
      properties:
        id:
          type: string
          description: Id of participant (companyId)
          example: b079e25663bb593810002681
        name:
          type: string
          description: Name of participant
          example: John Doe
    Account:
      type: object
      required:
        - number
        - id
        - status
      properties:
        number:
          type: string
          description: Account number
        id:
          type: string
          description: Account id
    StatusHistoryItem:
      type: object
      required:
        - status
        - statusUpdatedAt
      properties:
        status:
          type: string
          description: Status of transaction
          example: placed
          enum:
            - placed
            - processing
            - success
            - error
            - canceled
            - scheduled
        statusUpdatedAt:
          type: string
          format: datetime
          description: Time of status update.
          example: 2023-01-04T09:47:57.477Z
    AchTransactionDetails:
      type: object
      description: Current model represents CBW model
      properties:
        provider:
          type: string
          description: Provider name or identifier
          example: CBW
        referenceNumber:
          type: string
          description: Transaction reference of provider
        originalReferenceNumber:
          type: string
          description: Backreferece for returns and refunds
        batchReferenceNumber:
          type: string
          description: Batch transaction reference of provider
        identificationNumber:
          type: string
          description: Identification number of transaction by the provider, AKA transaction number
        method:
          type: string
          description: Transaction method on provider side
          example: pull
          enum:
            - pull
            - out
            - internal
        errorCode:
          type: string
          description: ErrorCode of ACH transaction
        addenda:
          type: object
          description: Additional values stored along with ACH transaction data
          nullable: true
    CardTransactionDetails:
      type: object
      description: Current model represents Tabapay model
      properties:
        provider:
          type: string
          description: Provider name or identifier
          example: Tabapay
        referenceNumber:
          type: string
          description: Transaction reference or identifier for the provider by initiator
          example: aBnElgzL24QAEUolE3FOUO
        transactionId:
          type: string
          description: Transaction identifier of the provider
        method:
          type: string
          description: Transaction method on provider side
          example: pull
          enum:
            - pull
            - out
            - internal
        network:
          type: string
          example: Visa
        networkRC:
          type: string
          description: Network response code
          example: 00
        networkId:
          type: string
          nullable: true
        approvalCode:
          type: string
          example: 155410
        avsResponseCode:
          type: string
        securityCodeResponseCode:
          type: string
          nullable: true
          example: M
        fees:
          type: array
          items:
            $ref: '#/components/schemas/CardPaymentFees'
        cardLastFour:
          type: string
          example: 0000
        cardExpirationDate:
          type: string
          description: Card expiration date in YYYYMM format
          example: 202312
        addenda:
          type: object
          description: Additional values stored along with ACH transaction data
          nullable: true
    CardPaymentFees:
      type: object
      required:
        - feeType
        - amount
      properties:
        feeType:
          type: string
          description: Type of card payment fee
          enum:
            - interchange
            - network
            - provider
        amount:
          type: number
          format: decimal
          description: Amount of fee. Currency is same as transaction currency.
          pattern: '[0-9]*\.[0-9]{2}'
          multipleOf: 0.01
          example: 0.00
          nullable: true
    CardResponse:
      type: object
    AccountRequest:
      type: object
    AccountResponse:
      type: object
    CardDeactivateRequest:
      type: object
    CardDeactivateResponse:
      type: object
    CardLimitRequest:
      type: object
    CardLimitResponse:
      type: object
    CreateVirtualCardRequest:
      type: object
    CreateVirtualCardResponse:
      type: object
    VirtualCardResponse:
      type: object
    CreateVirtualCardHolderRequest:
      type: object
    CreateVirtualCardHolderResponse:
      type: object
    VirtualCardHolderResponse:
      type: object
    VirtualCardCVVResponse:
      type: object
    VirtualCardCountryGroupResponse:
      type: object
    VirtualCardMccGroupResponse:
      type: object
    UpdateVirtualCardLimitRequest:
      type: object
    UpdateVirtualCardLimitResponse:
      type: object
    Error:
      type: object
      required:
        - message
      properties:
        message:
          description: A human readable error message
          type: string
    EmptyResponse:
      type: object
      nullable: true
  securitySchemes:
    ApiKey:
      type: apiKey
      in: header
      name: X-Api-Key
security:
  - ApiKey: []