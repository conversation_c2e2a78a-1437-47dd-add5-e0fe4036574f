import {request} from '@playwright/test';

require('dotenv').config();

export default async function sendUserRequest(method: string, endpoint: string, session: string, challenge: string, body: any = null) {
    const url = `${process.env.USER_BACKEND_URL}/${endpoint}`;
    const apiContext = await request.newContext();
    let response;
    try {
        switch (method) {
            case 'post':
                response = await apiContext.post(url,
                    {
                        headers: {
                            'session': `${session}`,
                            'accept': 'application/json, text/plain, */*',
                            'Content-Type': 'application/json;charset=UTF-8',
                            'challenge': `${challenge}`
                        },
                        data: body,
                    });
                break;
            case 'get':
                response = await apiContext.get(url,
                    {
                        headers: {
                            'session': `${session}`,
                            'accept': 'application/json, text/plain, */*',
                            'Content-Type': 'application/json;charset=UTF-8',
                            'challenge': `${challenge}`,
                        },
                    });
                break;
            case 'delete':
                response = await apiContext.delete(url,
                    {
                        headers: {
                            'session': `${session}`,
                            'accept': 'application/json, text/plain, */*',
                            'Content-Type': 'application/json;charset=UTF-8',
                            'challenge': `${challenge}`,
                        },
                    });
                break;
        }
        if (response.status() === 200) {
            console.log(`Request ${endpoint} was successful.\n`);
            const responseBody = await response.body();
            return responseBody;
        } else {
            throw new Error(`Request failed. Endpoint: ${endpoint} Status code: ${response.status()} ${response.statusText()}`);
        }
    } catch (error) {
        throw error;
    }
};
