import { BaseTest } from '../../../tests/test-utils';
import {BasePage} from '../../base.page';

export class Loans extends BasePage {
    constructor(page){
        super(page);
    };

    containers = {
        loansContainer: this.page.locator('.card-body'),
    };

    elements = {
        companyName: (userCompanyName) => this.containers.loansContainer.locator(`tr:has-text("${userCompanyName}")`).first(),
    };

    async getLoanId(userCompanyName) {
        return await this.elements.companyName(userCompanyName).locator(':nth-child(13)').textContent();
    }
}