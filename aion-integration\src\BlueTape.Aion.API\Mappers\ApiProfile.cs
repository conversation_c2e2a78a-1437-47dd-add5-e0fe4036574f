using AutoMapper;
using BlueTape.Aion.Application.Models.Ach.Pull;
using BlueTape.Aion.Application.Models.InternalTransfer;
using BlueTape.Integrations.Aion.Internal;
using System.Diagnostics.CodeAnalysis;
using BlueTape.Integrations.Aion.Ach.CreateAchTransfer;

namespace BlueTape.Aion.API.Mappers;

[ExcludeFromCodeCoverage]
public class ApiProfile : Profile
{
    public ApiProfile()
    {
        CreateMap<CreateAchModel, CreateAch>();
        CreateMap<OriginatorModel, Originator>();
        CreateMap<ReceiverModel, Receiver>();

        CreateMap<CreateInternalModel, CreateInternal>();
        CreateMap<BaseInternalAccountDetailsModel, BaseInternalAccountDetails>();
    }
}
