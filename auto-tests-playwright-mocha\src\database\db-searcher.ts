import {client} from "./client";
import {Collection, Db} from "mongodb";

export async function dbSearcher(requiredCollection: string) {
    let database: Db;

    //todo create more useful variant

    switch (process.env.CI_ENVIRONMENT_URL) {
        case 'https://dev.bluetape.com/':
        case 'https://dev.bluetape.com':
            database = client.db(`dev`);
            break;
        case 'https://beta.bluetape.com/':
        case 'https://beta.bluetape.com':
            database = client.db(`beta`);
            break;
        case 'https://qa.bluetape.com/':
        case 'https://qa.bluetape.com':
            database = client.db(`qa`);
            break;
    }

    return database.collection(requiredCollection);
}
