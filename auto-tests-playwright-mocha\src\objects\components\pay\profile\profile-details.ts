import {BasePage} from "../../../base.page";

export class ProfileDetails extends BasePage {
    constructor(page) {
        super(page);
    }

    inputFields = {
        fullName: this.page.locator("//*[text()='Full Name']/parent::div/parent::div/following-sibling::div/child::input"),
        businessName: this.page.locator("//div[contains(text(),'Business name')]/parent::div/following-sibling::div/child::input"),
        emailAddress: this.page.locator("//div[contains(text(),'Email Addres')]/parent::div/following-sibling::div/child::input"),
        cellPhoneNumber: this.page.locator("//div[contains(text(),'Cell phone number')]/parent::div/following-sibling::div/child::input"),
    };
}
