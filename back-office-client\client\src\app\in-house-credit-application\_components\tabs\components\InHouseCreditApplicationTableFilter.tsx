'use client'

import { Flex } from 'antd'
import { produce } from 'immer'
import { useCallback, useMemo, type Dispatch } from 'react'
import { useTranslation } from 'react-i18next'
import { styled } from 'styled-components'

import type { InHouseCreditApplicationStatus } from '@/app/in-house-credit-application/_types'
import { getInHouseCreditApplicationStatusOptions } from '@/app/in-house-credit-application/_utils'
import DateRangePicker from '@/components/common/DateRangePicker'
import Searchbar from '@/components/common/Searchbar'
import SelectWithDefaultEmpty from '@/components/common/SelectWithDefaultEmpty'
import type { AutomatedDecision, BusinessCategory } from '@/globals/types'
import {
  getAutomatedDecisionOptions,
  getBusinessCategoryOptions,
} from '@/globals/utils'

export interface IFilterState {
  name?: string
  automatedDecision?: AutomatedDecision[]
  status?: InHouseCreditApplicationStatus[]
  category?: BusinessCategory[]
  appDateFrom?: string
  appDateTo?: string
  decisionDateFrom?: string
  decisionDateTo?: string
}

interface IProps {
  onFilterChange: Dispatch<(prevState: IFilterState) => IFilterState>
  defaultValue: IFilterState
  withSubmissionDateRange?: boolean
  withDecisionDateRange?: boolean
  withStatus?: boolean
  withCategory?: boolean
  withSearchbar?: boolean
  withAutomatedDecision?: boolean
}

const InHouseCreditApplicationTableFilter = ({
  onFilterChange,
  defaultValue,
  withSearchbar = false,
  withSubmissionDateRange = false,
  withDecisionDateRange = false,
  withAutomatedDecision = false,
  withStatus = false,
  withCategory = false,
}: IProps): JSX.Element => {
  const { t } = useTranslation()

  const onSearchChange = useCallback(
    (value: string | undefined) => {
      onFilterChange(
        produce((draft) => {
          if (value !== undefined) {
            draft.name = value
          } else {
            delete draft.name
          }
        }),
      )
    },
    [onFilterChange],
  )

  const automatedDecisionOptions = useMemo(
    () => getAutomatedDecisionOptions(t),
    [t],
  )
  const onAutomatedDecisionChange = useCallback(
    (value: AutomatedDecision[]) => {
      onFilterChange(
        produce((draft) => {
          if (value.length > 0) draft.automatedDecision = value
          else delete draft.automatedDecision
        }),
      )
    },
    [onFilterChange],
  )

  const onSubmissionDateChange = useCallback(
    (value: [string, string] | null) => {
      onFilterChange(
        produce((draft) => {
          if (value !== null) {
            draft.appDateFrom = value[0]
            draft.appDateTo = value[1]
          } else {
            delete draft.appDateFrom
            delete draft.appDateTo
          }
        }),
      )
    },
    [onFilterChange],
  )

  const dateSubmissionDefaultValue = useMemo(
    () =>
      defaultValue.appDateFrom && defaultValue.appDateTo
        ? ([defaultValue.appDateFrom, defaultValue.appDateTo] as [
            string,
            string,
          ])
        : undefined,
    [defaultValue.appDateFrom, defaultValue.appDateTo],
  )

  const onDecisionDateChange = useCallback(
    (value: [string, string] | null) => {
      onFilterChange(
        produce((draft) => {
          if (value !== null) {
            draft.decisionDateFrom = value[0]
            draft.decisionDateTo = value[1]
          } else {
            delete draft.decisionDateFrom
            delete draft.decisionDateTo
          }
        }),
      )
    },
    [onFilterChange],
  )

  const decisionDateDefaultValue = useMemo(
    () =>
      defaultValue.decisionDateFrom && defaultValue.decisionDateTo
        ? ([defaultValue.decisionDateFrom, defaultValue.decisionDateTo] as [
            string,
            string,
          ])
        : undefined,
    [defaultValue.decisionDateFrom, defaultValue.decisionDateTo],
  )

  const statusOptions = useMemo(
    () => getInHouseCreditApplicationStatusOptions(t),
    [t],
  )
  const onStatusChange = useCallback(
    (value: InHouseCreditApplicationStatus[]) => {
      onFilterChange(
        produce((draft) => {
          if (value.length > 0) draft.status = value
          else delete draft.status
        }),
      )
    },
    [onFilterChange],
  )

  const categoryOptions = useMemo(() => getBusinessCategoryOptions(t), [t])
  const onCategoryChange = useCallback(
    (value: BusinessCategory[]) => {
      onFilterChange(
        produce((draft) => {
          if (value.length > 0) draft.category = value
          else delete draft.category
        }),
      )
    },
    [onFilterChange],
  )

  return (
    <Flex gap={16} wrap="wrap">
      {withSearchbar && (
        <StyledSearchbar
          onSearch={onSearchChange}
          defaultValue={defaultValue.name}
          placeholder={t(
            'inHouseCreditApplication.page.list.filter.searchbarPlaceholder',
          )}
        />
      )}

      {withAutomatedDecision && (
        <SelectWithDefaultEmpty<AutomatedDecision>
          defaultValue={defaultValue.automatedDecision}
          emptyValueLabel={t('all')}
          maxTagCount={1}
          placeholder={t(
            'inHouseCreditApplication.page.list.filter.automatedDecision',
          )}
          options={automatedDecisionOptions}
          onChange={onAutomatedDecisionChange}
          minWidth={186}
        />
      )}
      {withCategory && (
        <SelectWithDefaultEmpty<BusinessCategory>
          emptyValueLabel={t('all')}
          defaultValue={defaultValue.category}
          maxTagCount={1}
          placeholder={t('inHouseCreditApplication.page.list.filter.category')}
          options={categoryOptions}
          onChange={onCategoryChange}
          minWidth={118}
        />
      )}
      {withStatus && (
        <SelectWithDefaultEmpty<InHouseCreditApplicationStatus>
          defaultValue={defaultValue.status}
          emptyValueLabel={t('all')}
          maxTagCount={1}
          placeholder={t('inHouseCreditApplication.page.list.filter.status')}
          options={statusOptions}
          onChange={onStatusChange}
          minWidth={99}
        />
      )}
      {withSubmissionDateRange && (
        <DateRangePicker
          onChange={onSubmissionDateChange}
          defaultValue={dateSubmissionDefaultValue}
          placeholder={[
            t('inHouseCreditApplication.page.list.filter.selectDateRange'),
            t('inHouseCreditApplication.page.list.filter.forSubmissionDate'),
          ]}
        />
      )}
      {withDecisionDateRange && (
        <DateRangePicker
          onChange={onDecisionDateChange}
          defaultValue={decisionDateDefaultValue}
          placeholder={[
            t('inHouseCreditApplication.page.list.filter.selectDateRange'),
            t('inHouseCreditApplication.page.list.filter.forDecisionDate'),
          ]}
        />
      )}
    </Flex>
  )
}

const StyledSearchbar = styled(Searchbar)`
  width: 465px;
`

export default InHouseCreditApplicationTableFilter
