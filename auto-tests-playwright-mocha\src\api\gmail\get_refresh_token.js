const { OAuth2Client } = require('google-auth-library');
const open = require('open');
const http = require('http');
const url = require('url');

async function main() {
    const clientId = '685270156579-f8rqeo15js65vu96jrsspp6gkdnv4n9c.apps.googleusercontent.com';
    const clientSecret = '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';
    const redirectUri = 'http://localhost:3001/';

    const oauth2Client = new OAuth2Client(clientId, clientSecret, redirectUri);

    const authUrl = oauth2Client.generateAuthUrl({
        access_type: 'offline',
        scope: ['https://www.googleapis.com/auth/gmail.readonly'],
    });

    console.log('Authorize this app by visiting this URL:', authUrl);
    await open(authUrl);

    const server = http
        .createServer(async (req, res) => {
            if (req.url.indexOf('/?code=') === 0) {
                const queryObject = url.parse(req.url, true).query;
                const code = queryObject.code;
                const { tokens } = await oauth2Client.getToken(code);
                console.log('Refresh token:', tokens.refresh_token);
                res.end('Refresh token received. You can close this window.');
                server.close();
            } else {
                res.end('Invalid request');
            }
        })
        .listen(3001, () => {
            console.log('Server is listening on port 3000');
        });
}

main().catch(console.error);
