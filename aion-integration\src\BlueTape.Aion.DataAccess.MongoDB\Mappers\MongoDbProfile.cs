﻿using AutoMapper;
using BlueTape.Aion.DataAccess.MongoDB.Entities.BanksAccounts;
using BlueTape.Aion.DataAccess.MongoDB.Entities.Companies;
using BlueTape.Aion.Domain.DTOs.BankAccount;
using BlueTape.Aion.Domain.DTOs.Company;
using MongoDB.Bson;

namespace BlueTape.Aion.DataAccess.MongoDB.Mappers;

public class MongoDbProfile : Profile
{
    public MongoDbProfile()
    {
        CreateMap<CompanyDto, CompanyEntity>()
            .ForMember(x => x.BankAccounts, 
                y => y.MapFrom(z => 
                    (z.BankAccounts ?? Array.Empty<string>()).Select(bank => new ObjectId(bank)).ToArray()));
        
        CreateMap<CompanyEntity, CompanyDto>()
            .ForMember(x => x.BankAccounts, 
                y => y.MapFrom(z => 
                    (z.BankAccounts ?? new List<BsonObjectId>()).Select(bank => bank.Value.ToString()).ToArray()));
        
        CreateMap<CompanyAionSettingsDto, CompanyAionSettingsEntity>().ReverseMap();
        
        CreateMap<BankAccountDto, BankAccountEntity>().ReverseMap();
        CreateMap<BankAccountNumberDto, BankAccountNumberEntity>().ReverseMap();
        CreateMap<BankAccountAionSettingsDto, BankAccountAionSettingsEntity>().ReverseMap();
        CreateMap<BankAccountIdentityDto, BankAccountIdentityEntity>().ReverseMap();
    }
}
