import {BasePage} from '../base.page';

const pageLocator = '"Welcome back"';

export class LoginPage extends BasePage {
    constructor(page, locator = pageLocator){
        super(page, locator); 
    };
    
    inputFields = {
        email: this.page.locator('[data-testid="login"]'),
        password: this.page.locator('[data-testid="password"]'),
    };

    buttons = {
        continue: this.page.locator('"Continue"'),
        logIn: this.page.locator('"Log In"'),
        signUp: this.page.locator('"Sign Up"')
    };

    async login(email, password){
        await this.inputFields.email.fill(email);
        await this.buttons.continue.click();
        await this.inputFields.password.fill(password);
        await this.buttons.logIn.click();
    };
}

