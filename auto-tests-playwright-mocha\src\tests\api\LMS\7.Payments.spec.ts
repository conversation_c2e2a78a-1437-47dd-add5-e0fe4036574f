import {expect} from '@playwright/test';
import {BaseTest, test} from '../../test-utils';
import {sendLMSRequest, createLoan, deleteLoan, postAdminPayment} from '../../../api/common/lms-send-request';
import {validate} from 'jsonschema';

const constants = JSON.parse(JSON.stringify(require('../../../constants/LMStestData.json')));

test.describe(`Loan Payments API Tests @LMS @API`, async () => {
    // DEV ISSUE ask about CUSTOM paymentType

    let loanId: string;
    let paymentId: string;
    const paymentTypeArray = ["AutoDebit", "Manual"];
    const paymentStatusArray = ["Success", "Rejected"];  // dev
    const currentDate: string = BaseTest.getCurrentDate();


    test.beforeAll(async () => {
        loanId = await createLoan();
    });

    test.afterAll(async () => {
        const response = await deleteLoan(loanId);

        expect(response.status, `Status code 200`)
            .toEqual(200);
    });

    test(`Get payments by Loan ID.`, async () => {
        const response = await sendLMSRequest('get', `Payments?LoanId=${loanId}`);

        expect(response.status, `Status code 200`)
            .toEqual(200);

        expect(response.data, `Array is returned in Response`)
            .toEqual(expect.any(Array));

        const validationResult = validate(response.data, constants.paymentsSchema);

        expect(validationResult.valid, `JSON Schema is correct`)
            .toBeTruthy();
    });

    for (const paymentType of paymentTypeArray) {
        test(`Create payment with ${paymentType} state.`, async () => {
            const requestBody = {
                "amount": 100,
                "loanId": loanId,
                "type": paymentType,
            };

            const response = await sendLMSRequest('post', `Payments`, requestBody);

            paymentId = response.data.id;

            expect(response.status, `Status code 200`)
                .toEqual(200);

            expect(response.data.amount, `Payment amount is equal to ${response.data.amount}`)
                .toEqual(requestBody.amount);

            expect(response.data.loanId, `Loan ID is equal to ${response.data.loanId}`)
                .toEqual(requestBody.loanId);

            expect(response.data.type, `Payment Type is equal to ${response.data.type}`)
                .toEqual(requestBody.type);
        });

        test(`Payment saved with correct ${paymentType} and returned by Loan ID`, async () => {
            const response = await sendLMSRequest('get', `Payments?LoanId=${loanId}`);
            expect(response.status, `Status code 200`)
                .toEqual(200);
            expect(response.data[response.data.length - 1].type, `Payment saved with correct ${response.data.type} type`)
                .toEqual(paymentType);
        });
    }

    test(`Create payment with Custom state. Payment saved with correct Custom and returned by Loan ID`, async () => {
        await postAdminPayment(loanId, 100, currentDate, constants.loans.ownerId);
        const response = await sendLMSRequest('get', `Payments?LoanId=${loanId}`);
        expect(response.status, `Status code 200`)
            .toEqual(200);
        expect(response.data[response.data.length - 1].type, `Payment saved with correct ${response.data.type} type`)
            .toEqual('Custom');
    });

    for (const paymentStatus of paymentStatusArray) {
        test(`Change payment status with ${paymentStatus} status.`, async () => {
            const requestBody = {
                "status": paymentStatus,
            };
            const response = await sendLMSRequest('patch', `Payments/${paymentId}`, requestBody);
            expect(response.data, `Status code 200`);
        });
    }

    // test("1pdate Payment status7", async () => {
    //     const requestBody = {
    //         "amount": 100,
    //         "loanId": loanId,
    //         "type": paymentType,
    //     };
    //     const response = await sendLMSRequest('patch', `Payments/${paymentId}`, requestBody);
    //     expect(response.data, `Status code 200`)
    //     //expect(response.data, `Array is returned in Resonse`)
    //     .toEqual(expect.any(Array));
    // });

    // Negative Tests
    test(`Getting empty payments by invalid loan Id.`, async () => {
        const response = await sendLMSRequest('get', `Payments?LoanId=${constants.invalidLoanIDs.id}`);
        expect(response.status, `Status code 200`)
            .toEqual(200);
        expect(response.data, `Received answer contains an empty array`)
            .toEqual([]);
    });

    test(`Get failed data with invalid status.`, async () => {
        const requestBody = {
            "status": "invalidStatus",
        };
        const response = await sendLMSRequest('patch', `Payments/${paymentId}`, requestBody);
        expect(response.response.status, `Status code 400`)
            .toEqual(400);
    });
});
