import {BasePage} from '../../../base.page';

export class CustomersList extends BasePage {
    constructor(page){
        super(page);
    };

    containers = {
        contactRow: this.page.locator('.rdt_TableRow'),
    };

    popups = {
        invoiceSent: this.page.locator('//*[contains(text(), "is placed")]'),
    };

    buttons = {
        addCustomer: this.page.locator('[data-testid="account_add_customer_button"]'),
        
    };

    tabs = {
        tradeCreditTab: this.page.locator('[role="tab"]', { hasText: 'Trade Credit' }),
        arAdvanceTab: this.page.locator('[role="tab"]', { hasText: 'AR Advance' }).first(),
    }

    async clickOnArAdvanceTab(){
        await this.tabs.arAdvanceTab.click();
    };

    async clickOnTradeCreditTab(){
        await this.tabs.tradeCreditTab.click();
    };

    async clickOnCustomerRow(businessName){
        await this.containers.contactRow.locator(`"${businessName}"`).click();
    };
}