import {BasePage} from '../../../../base.page';

export class EditPaymentScheduleModal extends BasePage {
    constructor(page){
        super(page);
    };

    containers = {
        modal: this.page.locator('.modal-content'),   
    };

    buttons = {
        applyNewDate: this.containers.modal.locator('button:has-text("Apply New Date")'),
        cancel: this.containers.modal.locator('button:has-text("Cancel")'),
    };

    fields = {
        originalDate: this.containers.modal.locator('div[class="row"]:has-text("Original date")'),
    };

    checkBoxes = {
        addExtensionFee: this.containers.modal.locator('#payment_schedule_charge_fee'),
    };

    inputFields = {
        newDate: this.containers.modal.locator('[class="col"] [type="date"]'),
        note: this.containers.modal.locator('[class="my-auto col"] [type="text"]'),
        extensionFeeDate: this.containers.modal.locator('[class="my-auto col"] [type="date"]'),
        amountOfFee: this.containers.modal.locator('[value="$"]'),
    };
}