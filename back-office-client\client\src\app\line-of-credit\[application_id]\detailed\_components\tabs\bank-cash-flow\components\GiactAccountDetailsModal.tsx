import { Input } from 'antd'
import FormItem from 'antd/es/form/FormItem'
import { useTranslation } from 'react-i18next'

import { StyledButton } from '@/components/common/Button'
import { StyledModal } from '@/components/common/Modal'
import StyledVerticalForm from '@/components/common/StyledVerticalForm'
import StyledTitle from '@/components/common/typography/StyledTitle'
import type { IModalProps } from '@/globals/types'

interface IProps extends IModalProps {
  name: string | null
  routingNumber: string | null
  accountNumber: string | null
  accountHolderName: string | null
}

export const GiactAccountDetailsModal = ({
  isOpened,
  onClose,
  name,
  routingNumber,
  accountNumber,
  accountHolderName,
}: Readonly<IProps>): JSX.Element => {
  const { t } = useTranslation()

  const bankAccountsData = [
    {
      label: t(
        'lineOfCredit.detailPage.tabs.bankCashFlowTab.giactAccountDetailsModal.name',
      ),
      value: name,
    },
    {
      label: t(
        'lineOfCredit.detailPage.tabs.bankCashFlowTab.giactAccountDetailsModal.routingNumber',
      ),
      value: routingNumber,
    },
    {
      label: t(
        'lineOfCredit.detailPage.tabs.bankCashFlowTab.giactAccountDetailsModal.accountNumber',
      ),
      value: accountNumber,
    },
    {
      label: t(
        'lineOfCredit.detailPage.tabs.bankCashFlowTab.giactAccountDetailsModal.accountHolderName',
      ),
      value: accountHolderName,
    },
  ]

  return (
    <StyledModal
      open={isOpened}
      title={
        <StyledTitle level={4}>
          {t(
            'lineOfCredit.detailPage.tabs.bankCashFlowTab.giactAccountDetailsModal.title',
          )}
        </StyledTitle>
      }
      onCancel={onClose}
      destroyOnClose
      footer={
        <StyledButton type="text" onClick={onClose}>
          {t(
            'lineOfCredit.detailPage.tabs.bankCashFlowTab.giactAccountDetailsModal.close',
          )}
        </StyledButton>
      }
    >
      <StyledVerticalForm requiredMark={false}>
        {bankAccountsData.map((el) => (
          <FormItem label={el.label} key={el.label}>
            <Input value={el.value ?? t('na')} readOnly />
          </FormItem>
        ))}
      </StyledVerticalForm>
    </StyledModal>
  )
}
