﻿using AutoFixture;
using BlueTape.Aion.Application.Service;
using BlueTape.Aion.DataAccess.External.Models.CreateAchTransfer.Response;
using BlueTape.SNS.SlackNotification;
using BlueTape.SNS.SlackNotification.Models;
using BueTape.Aion.Infrastructure.Options;
using Microsoft.Extensions.Options;
using Moq;
using System.Threading;
using System.Threading.Tasks;

namespace BlueTape.Application.Tests.Services;

public class ErrorNotificationServiceTests
{
    private readonly Mock<ISnsEndpoint> _snsEndpointMock = new();
    private readonly Mock<IOptions<AionReportOptions>> _reportOptionsMock = new();
    private AionReportOptions _reportOptions = new();
    private Fixture _fixture = new();

    private ErrorNotificationService getService()
    {
        _reportOptions = new AionReportOptions
        {
            BucketName = _fixture.Create<string>(),
            DateRange = 7,
            ErrorSnsTopicName = _fixture.Create<string>()
        };

        _reportOptionsMock
            .SetupGet(opt => opt.Value)
            .Returns(_reportOptions);

        return new ErrorNotificationService(
            _snsEndpointMock.Object,
            _reportOptionsMock.Object);
    }

    private void VerifyNoOtherCalls()
    {
        _snsEndpointMock.VerifyNoOtherCalls();
    }

    [Fact]
    public async Task NotifyWithString_Execute_Success()
    {
        var request = _fixture.Create<EventMessageBody>();
        var service = getService();

        await service.Notify(request.Message, request.EventName, request.EventSource, CancellationToken.None).ShouldNotThrowAsync();

        _snsEndpointMock.Verify(x => x.PublishSlackNotificationAsync(
            It.IsAny<EventMessageBody>(),
            It.IsAny<string>(),
            It.IsAny<string>(),
            It.IsAny<CancellationToken>()), Times.Once);

        VerifyNoOtherCalls();
    }

    [Fact]
    public async Task Notify_Execute_Success()
    {
        var request = _fixture.Create<EventMessageBody>();
        var service = getService();

        await service.Notify(request, CancellationToken.None).ShouldNotThrowAsync();

        _snsEndpointMock.Verify(x => x.PublishSlackNotificationAsync(
            It.IsAny<EventMessageBody>(),
            It.IsAny<string>(),
            It.IsAny<string>(),
            It.IsAny<CancellationToken>()), Times.Once);

        VerifyNoOtherCalls();
    }

    [Fact]
    public async Task NotifyErrorReport_Execute_Success()
    {
        var request = _fixture.Create<AchResponseObj>();
        var service = getService();

        await service.NotifyUnmatchedReturn(request, CancellationToken.None).ShouldNotThrowAsync();

        _snsEndpointMock.Verify(x => x.PublishSlackNotificationAsync(
            It.IsAny<EventMessageBody>(),
            It.IsAny<string>(),
            It.IsAny<string>(),
            It.IsAny<CancellationToken>()), Times.Once);

        VerifyNoOtherCalls();
    }
}