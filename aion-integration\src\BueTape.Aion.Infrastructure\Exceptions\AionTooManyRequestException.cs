﻿using BlueTape.Common.ExceptionHandling.Exceptions.Base;
using System.Net;
using System.Runtime.Serialization;

namespace BueTape.Aion.Infrastructure.Exceptions;

[Serializable]
public class AionTooManyRequestException : DomainException
{
    public HttpStatusCode SourceHttpStatusCode { get; set; }
    public string? RequestPath { get; set; }

    public AionTooManyRequestException(string requestPath)
        : base(BuildErrorMessage(), HttpStatusCode.TooManyRequests)
    {
        SourceHttpStatusCode = HttpStatusCode.TooManyRequests;
        RequestPath = requestPath;
    }

    protected AionTooManyRequestException(SerializationInfo info, StreamingContext context) : base(info, context)
    {
    }

    private static string BuildErrorMessage()
    {
        return $"Aion service request was failed due-to too many request exception";
    }

    public override string Code => ErrorCodes.AionTooManyRequestError;
}