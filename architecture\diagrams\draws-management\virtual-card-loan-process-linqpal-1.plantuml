@startuml Virtual Card Loan Process (with BlueTape + CBW) /1

title Virtual Card Loan Process (with BlueTape + CBW) /1

participant "Decision Engine" as de #SkyBlue
participant "OnBoarding Service" as obs #SkyBlue
queue "Virtual Card Events" as vq #PaleVioletRed
participant "BlueTape" as bt #SkyBlue
participant "CBW" as cbw #LightGray
database "Mongo" as mongo

autonumber

== Approve draw, create virtual card ==

de -> obs : Draw approved
obs -> vq : Place event\n""VirtualCard.Create""
vq -> bt : Consume event
bt -> cbw : Create virtual card
bt -> mongo : Save virtual card

@enduml