import {BaseTest} from '../../tests/test-utils';
import sendAdminRequest from '../common/send-admin-request';

export default async function getAllBankConnectionsIds(bearerToken: string) {
    try {
        const companyName = process.env.CI_ENVIRONMENT_URL == 'https://dev.bluetape.com' ? BaseTest.constants.user.devCompanyName : BaseTest.constants.user.betaCompanyName;
        const endpoint = `finicity-accounts?search=${companyName}`;
        const response = await sendAdminRequest('get', endpoint, bearerToken);
        const jsonResponse = JSON.parse(response.toString());
        const connectionIDs: any[] = [];
        const companyIDs: any[] = [];
        for (const bankConnection of jsonResponse.items) {
            connectionIDs.push(bankConnection._id);
            companyIDs.push(bankConnection.company_id);
        }
        return {
            'connectionIDs': connectionIDs,
            'companyIDs': companyIDs,
        };
    } catch (error) {
        console.log(error);
        return error;
    }
};

// const searchForBankConnectionId = async (bearerToken, accountHolderName = null) => { // search by account holder name
//     try {
//       const endpoint = `beta/v1/admin/finicity-accounts?search=${accountHolderName}`;
//       const response = await sendRequest('get', endpoint, bearerToken);
//       const bodyString = response.toString().replace(/(?:\r\n|\r|\n)/g, "").replace(/(?:\ )/g, "");
//       const json = JSON.parse(bodyString);
//       return json.items[0]._id;
//     }
//     catch (error) {
//       console.log(error)
//     }
// };
