﻿using BlueTape.Integrations.Aion.Infrastructure.Enums;
using BueTape.Aion.Infrastructure.Exceptions;

namespace BueTape.Aion.Infrastructure.Extensions;

public static class PaymentSubscriptionExtensions
{
    public static PaymentSubscriptionType ParseToPaymentSubscription(this string value)
    {
        var isPaymentSubscriptionValid = Enum.TryParse<PaymentSubscriptionType>(value, out var paymentSubscription);
        if (!isPaymentSubscriptionValid) throw new InvalidPaymentSubscriptionType(value);

        return paymentSubscription;
    }
}
