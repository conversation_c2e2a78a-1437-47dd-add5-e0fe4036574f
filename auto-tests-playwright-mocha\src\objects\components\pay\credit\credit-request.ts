import {BasePage} from '../../../base.page';

export class CreditRequest extends BasePage {
    constructor(page){
        super(page);
    };
    
    hiddenElements = {
        creditRequesModal: 'body > div:nth-child(17) > div',
    };

    buttons = {
        getPrequalifiedHomePage: this.page.locator('[data-testid="CreditStatusCard"]'),
        edit: this.page.locator('"Edit" >> nth=1'),
        getPrequalified: this.page.locator('"Get prequalified"'),
        next: this.page.locator('"Next"'),
        submit: this.page.locator('"Agree & Submit"'),
    };

    creditInputs = {
        dept: this.page.locator('//*[text()="Debt"]//..//input'),
        credit: this.page.locator('//*[text()="BlueTape Credit Request"]//..//input'),
    };

    applicationDetails = {
        applicationSubmitted: this.page.locator('"Application Submitted"'),
        okayBtn: this.page.locator('"Okay, Thanks"'),
    };

    async getPrequalifiedCredit(dept, credit) {
        await this.page.waitForTimeout(3000);
        // await this.page.evaluate(() => {
        //     const element = document.querySelector('body > div:nth-child(17) > div') as HTMLElement;
        //     element.style.display = 'block'; // or 'visibility: visible;'
        // });
        await this.buttons.getPrequalified.click();
        // await this.page.evaluate(() => {
        //     const element = document.querySelector('body > div:nth-child(17) > div') as HTMLElement;
        //     element.style.display = 'none'; // or 'visibility: visible;'
        // });

        // await this.changeDisplayStateOfElement('body > div:nth-child(17) > div', 'block');
        // await this.buttons.getPrequalified.click();
        // await this.changeDisplayStateOfElement('body > div:nth-child(17) > div', 'none');
        
        // await this.buttons.next.click();
        await this.creditInputs.dept.fill(dept);
        await this.buttons.next.click();
        await this.creditInputs.credit.fill(credit);
        await this.buttons.next.click();
        await this.buttons.next.click();
    };

    async changeDisplayStateOfElement(selector, displayState){ // TODO: FINISH THIS METHOD
        await this.page.evaluate(() => {
            const element = document.querySelector(`${selector}`) as HTMLElement;
            element.style.display = displayState;
        });
    };
}