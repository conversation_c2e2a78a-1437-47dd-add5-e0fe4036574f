﻿using BlueTape.Aion.Application.Abstractions;
using BlueTape.Aion.Application.Constants;
using BlueTape.Aion.DataAccess.External.Abstractions;
using BlueTape.Aion.DataAccess.External.Models.CreateAchTransfer;
using BlueTape.Integrations.Aion.Infrastructure.Enums;
using BueTape.Aion.Infrastructure.Extensions;
using BueTape.Aion.Infrastructure.Options;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;

namespace BlueTape.Aion.Application.Service;

public class DailyAchProcessingService(
    IAionHttpClient aionHttpClient,
    ITransactionService transactionService,
    IOptions<AionInternalTransferOptions> internalTransferOptions,
    IOptions<DailyAchProcessingOptions> dailyAchProcessingOptions,
    IConfiguration configuration,
    ILogger<DailyAchProcessingService> logger) : IDailyAchProcessingService
{
    private readonly Random _random = new Random();
    private const int TransactionsTotalLimit = 2801;
    private const int TransactionsDailyLimit = 350;

    public async Task<int> ProcessAsync(decimal amount = 1, int transactionsLimitPerCall = 10, CancellationToken ct = default)
    {
        int transactionsCreated = 0;
        var transactions = await transactionService.GetByRowKeyPrefixAsync("DP", null, null, ct);
        var transactionsCount = transactions.Count();
        var createdForToday = transactions.Count(x => x.CreatedAt.Date == DateTime.Today);

        var transactionsTotalLimitValue = configuration["AION-DAILY-TRANSACTIONS-TOTAL-LIMIT"];
        var transactionsTotalLimit = string.IsNullOrEmpty(transactionsTotalLimitValue)
            ? TransactionsTotalLimit
            : int.Parse(transactionsTotalLimitValue);

        logger.LogInformation($"Daily ACH Service Started: DailyLimit = {TransactionsDailyLimit}, TotalLimit = {transactionsTotalLimit} transactions/day. Already created: Today: {createdForToday}, Total = {transactionsCount}");

        if (transactionsCount >= transactionsTotalLimit || createdForToday >= TransactionsDailyLimit)
            return createdForToday;

        while (transactionsCreated < transactionsLimitPerCall)
        {
            await Task.Delay(GetRandomInterval(), ct);

            internalTransferOptions.Value.AccountNumberSecretNames
                .TryGetValue(AccountCodeType.COLLECTIONREPAYMENT, out var accountNumberSecret);
            internalTransferOptions.Value.AccountIdSecretNames
                .TryGetValue(AccountCodeType.COLLECTIONREPAYMENT, out var accountIdSecret);

            if (string.IsNullOrEmpty(accountNumberSecret)) throw new VariableNullException(nameof(accountNumberSecret));
            if (string.IsNullOrEmpty(accountIdSecret)) throw new VariableNullException(nameof(accountIdSecret));

            var accountNumber = configuration[accountNumberSecret] ?? throw new VariableNullException(nameof(accountNumberSecret));
            var accountId = configuration[accountIdSecret] ?? throw new VariableNullException(nameof(accountIdSecret));
            var paymentSubscription = PaymentSubscriptionType.SUBSCRIPTION2;

            var response = await aionHttpClient.CreateAchTransfer(new CreateAchTransferRequest()
            {
                AchObj = new AchObjectRequest
                {
                    AccountId = accountId,
                    Addenda = ["BlueTape"],
                    Amount = amount.RoundDecimalToString(),
                    Description = "Daily ACH processing",
                    AccountNumber = accountNumber,
                    CounterpartyId = dailyAchProcessingOptions.Value.ReceiverCounterpartyId!,
                    CounterpartyName = "BlueTape Inc",
                    SecCode = AionConstants.AionBusinessSecCode,
                    SendEmail = false,
                    ServiceType = "Standard",
                    TransactionType = TransactionType.Pull.ToString(),
                    TransferMethodId = dailyAchProcessingOptions.Value.ReceiverTransferMethodId!,
                    ContextIdentifier = GenerateUniqueTransactionId()
                }
            }, paymentSubscription, ct);

            await transactionService.SaveAionCreatedAchTransactionAsync(response.AchObj, ct);

            transactionsCreated++;
            logger.LogInformation($"Transaction successful. Total transactions today: {transactionsCreated}");
        }

        return createdForToday + transactionsCreated;
    }

    private int GetRandomInterval()
    {
        // Generate a random interval between 0,5 and 2 seconds
        return _random.Next(500, 2000);
    }

    private decimal GetRandomAmount(decimal min, decimal max)
    {
        return (decimal)_random.NextDouble() * (max - min) + min;
    }

    private string GenerateUniqueTransactionId()
    {
        var uniquePart = Guid.NewGuid().ToString("N").Substring(0, 14).ToUpper();
        return $"DP{uniquePart}";
    }
}
