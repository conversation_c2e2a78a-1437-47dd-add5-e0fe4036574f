﻿using System.Diagnostics.CodeAnalysis;
using BlueTape.Aion.DataAccess.MongoDB.Abstractions;
using BlueTape.Aion.DataAccess.MongoDB.Constants;
using BlueTape.Aion.DataAccess.MongoDB.Entities;
using BlueTape.MongoDB.DTO;
using BueTape.Aion.Infrastructure.Exceptions;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Logging;
using MongoDB.Bson.Serialization;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using System.Text;

namespace BlueTape.Aion.DataAccess.MongoDB.Services;

public class AionLoggingService : IAionLoggingService
{
    private readonly IHttpContextAccessor _accessor;
    private readonly IAionLoggingRepository _aionLoggingRepository;
    private readonly ILogger<AionLoggingService> _logger;

    public AionLoggingService(
        ILogger<AionLoggingService> logger,
        IAionLoggingRepository aionLoggingRepository,
        IHttpContextAccessor accessor)
    {
        _logger = logger;
        _aionLoggingRepository = aionLoggingRepository;
        _accessor = accessor;
    }

    [ExcludeFromCodeCoverage]
    public async Task Log(string request, string response, string requestUrl, bool isSuccess, CancellationToken ct)
    {
        try
        {
            var utcNow = DateTime.UtcNow;
            var requestObject = JObject.Parse(request);
            var parsedResponseObject = JObject.Parse(response);
            JObject? rawResponseObject;
            
            try
            {
                rawResponseObject = JObject.Parse(response);
            }
            catch (Exception)
            {
                rawResponseObject = new()
                {
                    ["error"] = "Parsing JSON failed",
                    ["originalResponse"] = response
                };
            }

            var aionLoggingEntity = new AionLoggingEntity
            {
                Success = isSuccess,
                Request = GetRequestBody(requestObject),
                Response = GetResponseDocument(rawResponseObject, parsedResponseObject, isSuccess),
                TraceId = _accessor.HttpContext?.TraceIdentifier,
                PaymentRequestId = _accessor.HttpContext?.Request.Headers["PaymentRequestId"],
                RequestUrl = requestUrl,
                CreatedAt = utcNow,
                UpdatedAt = utcNow,
            };

            _logger.LogInformation("Start logging request and response");

            await _aionLoggingRepository.Add(aionLoggingEntity, ct);

            _logger.LogInformation("Finish logging request and response");
        }
        catch (Exception ex)
        {
            _logger.LogError("Logging aion request failed. Error message: {message}, request: {request}, response: {response}, requestUrl: {requestUrl}, isSuccess: {isSuccess}",
                ex.Message, request, response, requestUrl, isSuccess);
        }
    }
    
    public string GetMaskedJson(object data)
    {
        var json = System.Text.Json.JsonSerializer.Serialize(data);
        
        if (string.IsNullOrEmpty(json))
        {
            throw new RequestNullException(ExceptionConstants.RequestJObjectNullMessage);
        }

        var requestObject = JObject.Parse(json);
        return GetRequestBody(requestObject);
    }

    private static ResponseDocument GetResponseDocument(JObject? rawResponseObject, JObject? parsedResponseObject, bool isStatusSuccess)
    {
        return isStatusSuccess
            ? new ResponseDocument
            {
                ParsedBody = GetParsedResponseBody(parsedResponseObject)
            }
            : new ResponseDocument
            {
                RawBody = JsonConvert.SerializeObject(BsonSerializer.Deserialize<object>(rawResponseObject?.ToString()))
            };
    }

    private static string GetRequestBody(JObject request)
    {
        if (request is null) throw new RequestNullException(ExceptionConstants.RequestJObjectNullMessage);

        foreach (var fieldPath in FieldsForEncryptionConstants.MaskOutFields)
        {
            ReplaceProperty(request, fieldPath);
        }

        return JsonConvert.SerializeObject(request.ToObject<object>());
    }

    private static void ReplaceProperty(JObject obj, FieldMaskInfo fieldMaskInfo)
    {
        var properties = obj.Properties();

        foreach (var property in properties)
        {
            if (property.Name == fieldMaskInfo.FieldName)
            {
                if (property.Value.Type == JTokenType.Array)
                {
                    property.Value = new JArray();
                }

                if (property.Value.Type == JTokenType.String)
                {
                    switch (fieldMaskInfo.FieldMaskType)
                    {
                        case FieldMaskType.FullHide:
                            property.Value = "******";
                            break;
                        case FieldMaskType.LastDigits:
                            property.Value = GetDisplayForField(property.Value.ToString());
                            break;
                        default:
                            property.Value = "******";
                            break;
                    }
                }
            }
            else
            {
                if (property.Value.Type == JTokenType.Object)
                {
                    ReplaceProperty((JObject)property.Value, fieldMaskInfo);
                }
                else if (property.Value.Type == JTokenType.Array)
                {
                    foreach (JObject item in property.Value.Children<JObject>())
                    {
                        ReplaceProperty(item, fieldMaskInfo);
                    }
                }
            }
        }
    }

    private static string GetDisplayForField(string fieldValue)
    {
        var fieldValueBuilder = new StringBuilder();
        var visibleChars = 0;

        switch (fieldValue.Length)
        {
            case >= 7:
                visibleChars = 4;
                break;
            case < 7 and > 3:
                visibleChars = 2;
                break;
            default:
                visibleChars = 0;
                break;
        }

        fieldValueBuilder.Append(new string('*', fieldValue.Length - visibleChars));
        fieldValueBuilder.Append(fieldValue.AsSpan(fieldValue.Length - visibleChars, visibleChars));

        return fieldValueBuilder.ToString();
    }

    private static object GetParsedResponseBody(JObject? response)
    {
        if (response is null) throw new ResponseNullException(ExceptionConstants.ResponseJObjectNullMessage);

        foreach (var fieldPath in FieldsForEncryptionConstants.MaskOutFields)
        {
            ReplaceProperty(response, fieldPath);
        }

        return BsonSerializer.Deserialize<object>(response.ToString() ?? throw new ResponseNullException(ExceptionConstants.ResponseJObjectNullMessage));
    }
}
