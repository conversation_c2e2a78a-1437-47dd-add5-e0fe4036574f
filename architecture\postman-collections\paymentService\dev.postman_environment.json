{"id": "ad8195ae-1d1d-414c-aa19-9a9e5827e840", "name": "dev", "values": [{"key": "PaymentRequestId", "value": "bcd769df-1934-4b9b-811e-ad362f4d0bd4", "type": "default", "enabled": true}, {"key": "CommandToManualExecuting", "value": "", "type": "default", "enabled": true}, {"key": "PaymentServiceUrl", "value": "https://ca-payment-service-dev.bravesea-34bfcf76.westus.azurecontainerapps.io", "type": "default", "enabled": true}, {"key": "transaction4", "value": "", "type": "any", "enabled": true}, {"key": "transaction3", "value": "", "type": "any", "enabled": true}, {"key": "transaction2", "value": "", "type": "any", "enabled": true}, {"key": "transaction1", "value": "", "type": "any", "enabled": true}, {"key": "TransactionToUpdate", "value": "", "type": "any", "enabled": true}, {"key": "x-api-key", "value": "881C9B8G6427C67CA66491CAB20720A3", "type": "default", "enabled": true}], "_postman_variable_scope": "environment", "_postman_exported_at": "2024-03-08T12:49:24.907Z", "_postman_exported_using": "Postman/10.23.11"}