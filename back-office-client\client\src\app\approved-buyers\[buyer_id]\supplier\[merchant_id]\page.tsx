'use client'

import { Spin } from 'antd'
import type { BreadcrumbItemType } from 'antd/es/breadcrumb/Breadcrumb'
import { useTranslation } from 'react-i18next'
import { useParams, useSearchParams } from 'next/navigation'
import { useMemo } from 'react'

import Detail from './_components/Detail'

import PageWrapper from '@/components/common/PageWrapper'
import CentredSpinner from '@/components/common/CentredSpinner'
import { AppRoutes } from '@/globals/routes'
import {
  useApprovedBorrowerSupplierDetailsQuery,
  useApprovedBuyerQuery,
} from '@/lib/redux/api/approved-buyers'
import { ShortProductType } from '@/globals/types'
import { useGetLoanTemplatesQuery } from '@/lib/redux/api/admin'

const SupplierDetailPage = (): JSX.Element => {
  const { t } = useTranslation<string | undefined>()
  const router = useParams()
  const searchParams = useSearchParams()
  const borrowerId = router.buyer_id as string
  const supplierId = router.merchant_id as string
  const type = searchParams.get('applicationType') as ShortProductType

  const { data: borrowerData, isLoading: isBorrowerLoading } =
    useApprovedBuyerQuery({
      buyerId: borrowerId,
      product: type,
    })

  const {
    data: supplierDataArray,
    isFetching,
    isLoading,
    isSuccess,
  } = useApprovedBorrowerSupplierDetailsQuery({
    merchantId: supplierId,
    companyId: borrowerId,
    type: ShortProductType.IN_HOUSE_CREDIT,
    creditDetails: true,
  })

  const supplierData = supplierDataArray?.result[0]

  const {
    data: loanTemplates,
    isFetching: isLoanTemplatesFetching,
    isLoading: isLoanTemplatesLoading,
  } = useGetLoanTemplatesQuery({})

  const breadcrumbItems = useMemo(() => {
    const items: BreadcrumbItemType[] = [
      {
        href: AppRoutes.approvedBuyers.main(),
        title: t('menu.approvedBorrowers'),
      },
    ]

    if (!borrowerData) return items
    items.push({
      title: borrowerData.businessName + ' Account',
      href: AppRoutes.approvedBuyers.details(borrowerData.id),
    })

    if (!supplierData) return items
    items.push({
      title: supplierData.businessName + ' Limits',
    })

    return items
  }, [t, supplierData, borrowerData])

  const backLinkHref = borrowerId
    ? `${AppRoutes.approvedBuyers.details(borrowerId)}?applicationType=ihc&tab=suppliers`
    : undefined

  const backLinkText = borrowerId
    ? t(
        'approvedBuyers.page.details.tabs.suppliers.details.backToAccountDetails',
        { borrowerName: borrowerData?.businessName ?? t('na') },
      )
    : undefined

  const isPageLoading =
    isBorrowerLoading ||
    isLoanTemplatesLoading ||
    isLoading ||
    !isSuccess ||
    !supplierData
  const isPageFetching = isLoanTemplatesFetching || isFetching

  return (
    <PageWrapper breadcrumbItems={breadcrumbItems}>
      {isPageLoading ? (
        <CentredSpinner />
      ) : (
        <Spin spinning={isPageFetching} delay={0} size="large">
          <Detail
            data={supplierData}
            companyId={borrowerId}
            merchantId={supplierId}
            businessName={borrowerData?.businessName ?? ''}
            loanTemplates={loanTemplates}
            backLinkHref={backLinkHref}
            backLinkText={backLinkText}
          />
        </Spin>
      )}
    </PageWrapper>
  )
}

export default SupplierDetailPage
