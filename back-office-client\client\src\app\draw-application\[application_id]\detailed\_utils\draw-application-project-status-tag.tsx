import type { TFunction } from 'i18next'
import Badge from 'antd/es/badge'

import { DrawApplicationProjectStatus } from '@/app/draw-application/_types'
import { getDrawApplicationProjectStatusTranslation } from '@/globals/utils'

export const getDrawApplicationProjectStatusTag = (
  status: DrawApplicationProjectStatus,
  isExpired: boolean,
  t: TFunction,
): JSX.Element => {
  if (isExpired) {
    return (
      <Badge
        color="red"
        text={t(
          'drawApplication.page.detailed.tabs.project.projectDateHasExpired',
        )}
      />
    )
  }

  if (status === DrawApplicationProjectStatus.IN_REVIEW) {
    return (
      <Badge
        color="blue"
        text={getDrawApplicationProjectStatusTranslation(status, t)}
      />
    )
  }

  if (status === DrawApplicationProjectStatus.REJECTED) {
    return (
      <Badge
        color="red"
        text={getDrawApplicationProjectStatusTranslation(status, t)}
      />
    )
  }

  if (status === DrawApplicationProjectStatus.APPROVED) {
    return (
      <Badge
        color="green"
        text={getDrawApplicationProjectStatusTranslation(status, t)}
      />
    )
  }

  return <>{status}</>
}
