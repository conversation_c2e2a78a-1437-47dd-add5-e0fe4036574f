﻿using BlueTape.Application.Tests.CashFlow.Models;
using BlueTape.Application.Tests.FixtureHelpers.FixtureAttributes;
using BlueTape.Application.Tests.MapperForTests;
using BlueTape.AWSS3.Abstractions;
using BlueTape.CashFlow.Application.Services;
using BlueTape.CashFlow.Domain.DTOs;
using BlueTape.CashFlow.Domain.DTOs.Responses;
using BlueTape.Common.Exceptions.Documents;
using BlueTape.Common.FileService.Abstractions;
using BlueTape.Company.Domain.DTOs.BankAccounts;
using BlueTape.DataAccess.MongoDB.Abstractions;
using BlueTape.Document.DataAccess.EF.Abstractions;
using BlueTape.Domain.Entities.Documents;
using BlueTape.Utilities.Providers;
using Microsoft.Extensions.Configuration;
using Moq;
using Shouldly;
using System.Globalization;
using System.Linq.Expressions;
using BlueTape.CashFlow.Application.Mappers;
using BlueTape.CashFlow.Domain.DTOs.AssetReportModels;
using BlueTape.Common.Exceptions;
using BlueTape.Common.FileService.Models;
using MockQueryable.Moq;
using Xunit;

namespace BlueTape.Application.Tests.CashFlow
{
    public class CashFlowServiceTests
    {
        private readonly CashFlowService _service;
        private readonly Mock<IBankAccountRepository> _bankAccountRepositoryMock = new();
        private readonly Mock<IEfCashFlowRepository> _cashFlowRepositoryMock = new();
        private readonly Mock<IEfCashFlowItemRepository> _cashFlowItemRepositoryMock = new();
        private readonly Mock<IEfCompanyRepository> _companyRepositoryMock = new();
        private readonly Mock<ICsvFileReader> _csvFileReaderMock = new();
        private readonly Mock<IDateProvider> _dateProviderMock = new();
        private readonly Mock<IConfiguration> _configurationMock;
        private readonly Mock<IS3Client> _s3ClientMock = new();

        public CashFlowServiceTests()
        {
            _configurationMock = new Mock<IConfiguration>();
            var configuration = _configurationMock.Object;

            _service = new CashFlowService(
                _cashFlowRepositoryMock.Object,
                _cashFlowItemRepositoryMock.Object,
                _companyRepositoryMock.Object,
                _bankAccountRepositoryMock.Object,
                _csvFileReaderMock.Object,
                MapperCreator.CreateWithApplicationProfile(),
                _s3ClientMock.Object,
                _dateProviderMock.Object,
                configuration); // Use _configuration instead of _configurationMock.Object
        }

        private void VerifyNoOtherCalls()
        {
            _cashFlowRepositoryMock.VerifyNoOtherCalls();
            _cashFlowItemRepositoryMock.VerifyNoOtherCalls();
            _companyRepositoryMock.VerifyNoOtherCalls();
            _bankAccountRepositoryMock.VerifyNoOtherCalls();
        }

        [Theory]
        [AutoDataWithDateOnly]
        public async Task Add_ValidData_AddValidCashFlow(
            string companyId,
            CompanyEntity company,
            AddPlaidCashFlowModel cashFlowModel)
        {
            var account = cashFlowModel.PlaidAssetReport.AssetReport.Items.First().Accounts.First();
            account.AccountId = cashFlowModel.AccountId;

            _companyRepositoryMock.Setup(x => x.GetByLegacyId(companyId, default)).ReturnsAsync(company);
            _cashFlowRepositoryMock.Setup(x => x.GetByAccountId(It.IsAny<string>(), default)).ReturnsAsync(value: null);
            _cashFlowRepositoryMock.Setup(x => x.Add(It.IsAny<CashFlowEntity>(), default));

            var result = await _service.Add(companyId, cashFlowModel, default);

            _companyRepositoryMock.Verify(x => x.GetByLegacyId(companyId, default), Times.Once);
            _cashFlowRepositoryMock.Verify(x => x.GetByAccountId(It.IsAny<string>(), default), Times.Once);
            _cashFlowRepositoryMock.Verify(x => x.Add(It.IsAny<CashFlowEntity>(), default), Times.Once);

            VerifyNoOtherCalls();

            result.Result.ShouldBe(true);
            result.DaysAvailableFrom.ShouldBe(account.HistoricalBalances.Min(x => x.Date)
                .ToString(CultureInfo.CurrentCulture));
            result.DaysAvailableTo.ShouldBe(account.HistoricalBalances.Max(x => x.Date)
                .ToString(CultureInfo.CurrentCulture));
        }


        [Theory]
        [AutoDataWithDateOnly]
        public async Task Add_ValidData_UpdateValidCashFlow(
            string companyId,
            CompanyEntity company,
            AddPlaidCashFlowModel cashFlowModel,
            CashFlowEntity existingCashFlow)
        {
            var account = cashFlowModel.PlaidAssetReport.AssetReport.Items.First().Accounts.First();
            account.AccountId = cashFlowModel.AccountId;

            _companyRepositoryMock.Setup(x => x.GetByLegacyId(companyId, default)).ReturnsAsync(company);
            _cashFlowRepositoryMock.Setup(x => x.GetByAccountId(It.IsAny<string>(), default))
                .ReturnsAsync(existingCashFlow);
            _cashFlowRepositoryMock.Setup(x => x.Update(It.IsAny<CashFlowEntity>(), default));

            var result = await _service.Add(companyId, cashFlowModel, default);

            _companyRepositoryMock.Verify(x => x.GetByLegacyId(companyId, default), Times.Once);
            _cashFlowRepositoryMock.Verify(x => x.GetByAccountId(It.IsAny<string>(), default), Times.Once);
            _cashFlowRepositoryMock.Verify(x => x.Update(It.IsAny<CashFlowEntity>(), default), Times.Once);

            VerifyNoOtherCalls();

            result.Result.ShouldBe(true);
            result.DaysAvailableFrom.ShouldBe(account.HistoricalBalances.Min(x => x.Date)
                .ToString(CultureInfo.CurrentCulture));
            result.DaysAvailableTo.ShouldBe(account.HistoricalBalances.Max(x => x.Date)
                .ToString(CultureInfo.CurrentCulture));
        }

        [Theory]
        [AutoDataWithDateOnly]
        public async Task Add_InvalidAccountId_ThrowException(
            string companyId,
            CompanyEntity company,
            AddPlaidCashFlowModel cashFlowModel)
        {
            cashFlowModel.AccountId = string.Empty;

            _companyRepositoryMock.Setup(x => x.GetByLegacyId(companyId, default)).ReturnsAsync(company);

            await Should.ThrowAsync<AccountDoesNotExistException>(_service.Add(companyId, cashFlowModel, default));

            _companyRepositoryMock.Verify(x => x.GetByLegacyId(companyId, default), Times.Once);

            VerifyNoOtherCalls();
        }

        [Theory]
        [AutoDataWithDateOnly]
        public async Task GetById_InvalidAccountId_EmptyList(
            string companyId,
            CompanyEntity company)
        {
            var assetReportQuery = AssetReportQueryModels.SingleAccountGroupingByDay;

            _companyRepositoryMock.Setup(x => x.GetByLegacyId(companyId, CancellationToken.None)).ReturnsAsync(company);

            // Setup for getting account IDs
            _cashFlowRepositoryMock
                .Setup(x => x.GetAccountIds(It.IsAny<Expression<Func<CashFlowEntity, bool>>>(), CancellationToken.None))
                .ReturnsAsync(new List<string>());

            // Setup for bank accounts
            _bankAccountRepositoryMock
                .Setup(x => x.GetByPlaidAccountIds(It.IsAny<string[]>(), CancellationToken.None))
                .ReturnsAsync(new List<BankAccountDto>());

            _bankAccountRepositoryMock
                .Setup(x => x.GetByBankAccountIds(It.IsAny<string[]>(), true, CancellationToken.None))
                .ReturnsAsync(new List<BankAccountDto>());

            var result = await _service.GetById(companyId, assetReportQuery, CancellationToken.None);

            _companyRepositoryMock.Verify(x => x.GetByLegacyId(companyId, CancellationToken.None), Times.Once);
            _cashFlowRepositoryMock.Verify(
                x => x.GetAccountIds(It.IsAny<Expression<Func<CashFlowEntity, bool>>>(), CancellationToken.None),
                Times.Once);

            result.CashFlowItems.ShouldNotBeNull();
            result.CashFlowItems.Count.ShouldBe(0);
            result.UpdatedAt.ShouldBeNull();
        }

        [Theory]
        [AutoDataWithDateOnly]
        public async Task GetById_SingleAccountGroupingByDay_ValidData(
            string companyId,
            CompanyEntity company)
        {
            // Arrange
            var assetReportQuery = AssetReportQueryModels.SingleAccountGroupingByDay;
            var accountId = "test-account-id";

            // Create CashFlow entity first
            var cashFlow = new CashFlowEntity
            {
                Id = Guid.NewGuid(),
                CompanyId = company.Id,
                AccountId = accountId,
                IsFileUpload = false,
                UpdatedAt = DateTime.UtcNow
            };

            var today = DateOnly.FromDateTime(DateTime.Today);
            var cashFlowItems = new List<CashFlowItemEntity>
            {
                new()
                {
                    Id = Guid.NewGuid(),
                    Date = today,
                    CashFlow = cashFlow,
                    CashFlowId = cashFlow.Id,
                    Credit = 100,
                    Debit = 50,
                    CashFlowResult = 50,
                    Balance = 1000,
                    UpdatedAt = DateTime.UtcNow
                },
                new()
                {
                    Id = Guid.NewGuid(),
                    Date = today.AddDays(-1),
                    CashFlow = cashFlow,
                    CashFlowId = cashFlow.Id,
                    Credit = 200,
                    Debit = 100,
                    CashFlowResult = 100,
                    Balance = 950,
                    UpdatedAt = DateTime.UtcNow
                }
            };

            // Set up bidirectional relationship
            cashFlow.CashFlowItems = cashFlowItems;

            _companyRepositoryMock
                .Setup(x => x.GetByLegacyId(companyId, default))
                .ReturnsAsync(company);

            _cashFlowRepositoryMock
                .Setup(x => x.GetAccountIds(It.IsAny<Expression<Func<CashFlowEntity, bool>>>(), default))
                .ReturnsAsync(new List<string> { accountId });

            _bankAccountRepositoryMock
                .Setup(x => x.GetByPlaidAccountIds(It.IsAny<string[]>(), default))
                .ReturnsAsync(new List<BankAccountDto>
                {
                    new()
                    {
                        Id = Guid.NewGuid().ToString(),
                        Plaid = new BankAccountPlaidDto
                        {
                            AccountId = accountId,
                            IncludeInCashFlow = true
                        }
                    }
                });

            // Setup mock queryable using BuildMockDbSet
            var mockQueryable = cashFlowItems.AsQueryable().BuildMockDbSet();
            _cashFlowItemRepositoryMock.Setup(x => x.Query).Returns(mockQueryable.Object);

            // Act
            var result = await _service.GetById(companyId, assetReportQuery, default);

            // Assert
            result.ShouldNotBeNull();
            result.CashFlowItems.ShouldNotBeNull();
            result.CashFlowItems.Count.ShouldBe(cashFlowItems.Count);
            result.CashFlowItems.Sum(x => x.Balance).ShouldBe(cashFlowItems.Sum(x => x.Balance));
            CheckResultSums(result.CashFlowItems, cashFlowItems);

            // Verify repository calls
            _companyRepositoryMock.Verify(x => x.GetByLegacyId(companyId, default), Times.Once);
            _cashFlowRepositoryMock.Verify(
                x => x.GetAccountIds(It.IsAny<Expression<Func<CashFlowEntity, bool>>>(), default), Times.Once);
            _bankAccountRepositoryMock.Verify(x => x.GetByPlaidAccountIds(It.IsAny<string[]>(), default), Times.Once);
        }

        [Theory]
        [AutoDataWithDateOnly]
        public async Task GetById_SingleAccountGroupingByMonth_ValidData(
            string companyId,
            CompanyEntity company)
        {
            // Arrange
            var assetReportQuery = AssetReportQueryModels.SingleAccountGroupingByMonth;
            var accountId = "test-account-id";

            // Create CashFlow entity first
            var cashFlow = new CashFlowEntity
            {
                Id = Guid.NewGuid(),
                CompanyId = company.Id,
                AccountId = accountId,
                IsFileUpload = false,
                UpdatedAt = DateTime.UtcNow
            };

            var today = DateOnly.FromDateTime(DateTime.Today);
            var cashFlowItems = new List<CashFlowItemEntity>
            {
                new()
                {
                    Id = Guid.NewGuid(),
                    Date = today,
                    CashFlow = cashFlow,
                    CashFlowId = cashFlow.Id,
                    Credit = 100,
                    Debit = 50,
                    CashFlowResult = 50,
                    Balance = 1000,
                    UpdatedAt = DateTime.UtcNow
                },
                new()
                {
                    Id = Guid.NewGuid(),
                    Date = today.AddDays(-1),
                    CashFlow = cashFlow,
                    CashFlowId = cashFlow.Id,
                    Credit = 200,
                    Debit = 100,
                    CashFlowResult = 100,
                    Balance = 950,
                    UpdatedAt = DateTime.UtcNow
                },
                new()
                {
                    Id = Guid.NewGuid(),
                    Date = today.AddMonths(-1), // Item from previous month
                    CashFlow = cashFlow,
                    CashFlowId = cashFlow.Id,
                    Credit = 300,
                    Debit = 150,
                    CashFlowResult = 150,
                    Balance = 900,
                    UpdatedAt = DateTime.UtcNow
                }
            };

            // Set up bidirectional relationship
            cashFlow.CashFlowItems = cashFlowItems;

            _companyRepositoryMock
                .Setup(x => x.GetByLegacyId(companyId, default))
                .ReturnsAsync(company);

            _cashFlowRepositoryMock
                .Setup(x => x.GetAccountIds(It.IsAny<Expression<Func<CashFlowEntity, bool>>>(), default))
                .ReturnsAsync(new List<string> { accountId });

            _bankAccountRepositoryMock
                .Setup(x => x.GetByPlaidAccountIds(It.IsAny<string[]>(), default))
                .ReturnsAsync(new List<BankAccountDto>
                {
                    new()
                    {
                        Id = Guid.NewGuid().ToString(),
                        Plaid = new BankAccountPlaidDto
                        {
                            AccountId = accountId,
                            IncludeInCashFlow = true
                        }
                    }
                });

            // Setup mock queryable using BuildMockDbSet
            var mockQueryable = cashFlowItems.AsQueryable().BuildMockDbSet();
            _cashFlowItemRepositoryMock.Setup(x => x.Query).Returns(mockQueryable.Object);

            // Act
            var result = await _service.GetById(companyId, assetReportQuery, default);

            // Assert
            result.ShouldNotBeNull();
            result.CashFlowItems.ShouldNotBeNull();

            // Should be grouped by month, so we expect 2 items (current month and previous month)
            var expectedMonthGroups = cashFlowItems.GroupBy(x => new { x.Date.Month, x.Date.Year }).Count();
            result.CashFlowItems.Count.ShouldBe(expectedMonthGroups);

            result.CashFlowItems.ShouldAllBe(x => x.AccountId == null);
            CheckResultSums(result.CashFlowItems, cashFlowItems);

            // Verify repository calls
            _companyRepositoryMock.Verify(x => x.GetByLegacyId(companyId, default), Times.Once);
            _cashFlowRepositoryMock.Verify(
                x => x.GetAccountIds(It.IsAny<Expression<Func<CashFlowEntity, bool>>>(), default), Times.Once);
            _bankAccountRepositoryMock.Verify(x => x.GetByPlaidAccountIds(It.IsAny<string[]>(), default), Times.Once);
        }

        [Theory]
        [AutoDataWithDateOnly]
        public async Task GetById_MultipleAccountsGroupingByDay_ValidData(
            string companyId,
            CompanyEntity company)
        {
            var assetReportQuery = AssetReportQueryModels.MultipleAccountGroupingByDay;
            var accountId1 = "test-account-id-1";
            var accountId2 = "test-account-id-2";

            var cashFlow1 = new CashFlowEntity
            {
                Id = Guid.NewGuid(),
                CompanyId = company.Id,
                AccountId = accountId1
            };

            var cashFlow2 = new CashFlowEntity
            {
                Id = Guid.NewGuid(),
                CompanyId = company.Id,
                AccountId = accountId2
            };

            var today = DateOnly.FromDateTime(DateTime.Today);
            var cashFlowItems = new List<CashFlowItemEntity>
            {
                new()
                {
                    Date = today,
                    CashFlow = cashFlow1,
                    CashFlowId = cashFlow1.Id,
                    Credit = 100,
                    Debit = 50,
                    CashFlowResult = 50,
                    Balance = 1000
                },
                new()
                {
                    Date = today,
                    CashFlow = cashFlow2,
                    CashFlowId = cashFlow2.Id,
                    Credit = 200,
                    Debit = 100,
                    CashFlowResult = 100,
                    Balance = 2000
                },
                new()
                {
                    Date = today.AddDays(-1),
                    CashFlow = cashFlow1,
                    CashFlowId = cashFlow1.Id,
                    Credit = 300,
                    Debit = 150,
                    CashFlowResult = 150,
                    Balance = 950
                },
                new()
                {
                    Date = today.AddDays(-1),
                    CashFlow = cashFlow2,
                    CashFlowId = cashFlow2.Id,
                    Credit = 400,
                    Debit = 200,
                    CashFlowResult = 200,
                    Balance = 1900
                }
            };

            cashFlow1.CashFlowItems = cashFlowItems.Where(x => x.CashFlowId == cashFlow1.Id).ToList();
            cashFlow2.CashFlowItems = cashFlowItems.Where(x => x.CashFlowId == cashFlow2.Id).ToList();

            _companyRepositoryMock.Setup(x => x.GetByLegacyId(companyId, default)).ReturnsAsync(company);

            _cashFlowRepositoryMock
                .Setup(x => x.GetAccountIds(It.IsAny<Expression<Func<CashFlowEntity, bool>>>(), default))
                .ReturnsAsync(new List<string> { accountId1, accountId2 });

            _bankAccountRepositoryMock
                .Setup(x => x.GetByPlaidAccountIds(It.IsAny<string[]>(), default))
                .ReturnsAsync(new List<BankAccountDto>
                {
                    new() { Plaid = new() { AccountId = accountId1, IncludeInCashFlow = true } },
                    new() { Plaid = new() { AccountId = accountId2, IncludeInCashFlow = true } }
                });

            // Setup mock queryable
            var mockQueryable = cashFlowItems.AsQueryable().BuildMockDbSet();
            _cashFlowItemRepositoryMock.Setup(x => x.Query).Returns(mockQueryable.Object);

            var result = await _service.GetById(companyId, assetReportQuery, default);

            // Verify results
            result.CashFlowItems.ShouldNotBeNull();
            result.CashFlowItems.ShouldAllBe(x => x.AccountId == null);

            // Should be grouped by day, so 2 days worth of data
            result.CashFlowItems.Count.ShouldBe(2);

            // Verify sums
            CheckResultSums(result.CashFlowItems, cashFlowItems);

            // Verify calls
            _companyRepositoryMock.Verify(x => x.GetByLegacyId(companyId, default), Times.Once);
            _cashFlowRepositoryMock.Verify(
                x => x.GetAccountIds(It.IsAny<Expression<Func<CashFlowEntity, bool>>>(), default), Times.Once);
            _bankAccountRepositoryMock.Verify(x => x.GetByPlaidAccountIds(It.IsAny<string[]>(), default), Times.Once);
        }

        [Theory]
        [AutoDataWithDateOnly]
        public async Task GetBankStatements_CompanyExists_ReturnsValidData(
            string companyId,
            CompanyEntity company)
        {
            var accountId = "test-account-id";
            var uploadDate = DateTime.UtcNow;
            var url = "test-url";
            var fileName = "test-file.csv";
            var minDate = DateOnly.FromDateTime(uploadDate.AddDays(-10));
            var maxDate = DateOnly.FromDateTime(uploadDate.AddDays(8));
            var id = Guid.NewGuid();

            var cashFlowEntities = new List<CashFlowEntity>
            {
                new()
                {
                    Id = id,
                    AccountId = accountId,
                    S3Url = url,
                    FileName = fileName,
                    IsFileUpload = true,
                    CashFlowItems = new List<CashFlowItemEntity>
                    {
                        new() { Date = maxDate },
                        new() { Date = maxDate },
                        new() { Date = minDate },
                        new() { Date = DateOnly.FromDateTime(uploadDate.AddDays(-1)) }
                    }
                }
            };

            _companyRepositoryMock.Setup(x => x.GetByLegacyId(companyId, default)).ReturnsAsync(company);
            _cashFlowRepositoryMock
                .Setup(x => x.Get(It.IsAny<Expression<Func<CashFlowEntity, bool>>>(), default))
                .ReturnsAsync(cashFlowEntities);

            var result = await _service.GetBankStatements(companyId, default);

            result.Count.ShouldBe(1);
            result[0].Id.ShouldBe(id);
            result[0].AccountId.ShouldBe(accountId);
            result[0].S3Url.ShouldBe(url);
            result[0].FileName.ShouldBe(fileName);
            result[0].DaysAvailableFrom.ShouldBe(minDate);
            result[0].DaysAvailableTo.ShouldBe(maxDate);
        }

        [Theory]
        [AutoDataWithDateOnly]
        public async Task GetById_MultipleAccountsGroupingByMonth_ValidData(
            string companyId,
            CompanyEntity company)
        {
            // Arrange
            var assetReportQuery = AssetReportQueryModels.MultipleAccountGroupingByMonth;
            var accountId1 = "test-account-id-1";
            var accountId2 = "test-account-id-2";

            var today = DateOnly.FromDateTime(DateTime.Today);

            var cashFlow1 = new CashFlowEntity
            {
                Id = Guid.NewGuid(),
                CompanyId = company.Id,
                AccountId = accountId1,
                IsFileUpload = false,
                UpdatedAt = DateTime.UtcNow,
                CashFlowItems = new List<CashFlowItemEntity>()
            };

            var cashFlow2 = new CashFlowEntity
            {
                Id = Guid.NewGuid(),
                CompanyId = company.Id,
                AccountId = accountId2,
                IsFileUpload = false,
                UpdatedAt = DateTime.UtcNow,
                CashFlowItems = new List<CashFlowItemEntity>()
            };

            var cashFlowItems = new List<CashFlowItemEntity>
            {
                new()
                {
                    Id = Guid.NewGuid(),
                    Date = today,
                    CashFlow = cashFlow1,
                    CashFlowId = cashFlow1.Id,
                    Credit = 100,
                    Debit = 50,
                    CashFlowResult = 50,
                    Balance = 1000,
                    UpdatedAt = DateTime.UtcNow
                },
                new()
                {
                    Id = Guid.NewGuid(),
                    Date = today,
                    CashFlow = cashFlow2,
                    CashFlowId = cashFlow2.Id,
                    Credit = 200,
                    Debit = 100,
                    CashFlowResult = 100,
                    Balance = 2000,
                    UpdatedAt = DateTime.UtcNow
                },
                new()
                {
                    Id = Guid.NewGuid(),
                    Date = today.AddMonths(-1),
                    CashFlow = cashFlow1,
                    CashFlowId = cashFlow1.Id,
                    Credit = 300,
                    Debit = 150,
                    CashFlowResult = 150,
                    Balance = 950,
                    UpdatedAt = DateTime.UtcNow
                }
            };

            // Set up bidirectional relationships
            foreach (var item in cashFlowItems)
            {
                var parentCashFlow = item.CashFlowId == cashFlow1.Id ? cashFlow1 : cashFlow2;
                parentCashFlow.CashFlowItems.Add(item);
            }

            _companyRepositoryMock
                .Setup(x => x.GetByLegacyId(companyId, default))
                .ReturnsAsync(company);

            _cashFlowRepositoryMock
                .Setup(x => x.GetAccountIds(It.IsAny<Expression<Func<CashFlowEntity, bool>>>(), default))
                .ReturnsAsync(new List<string> { accountId1, accountId2 });

            _bankAccountRepositoryMock
                .Setup(x => x.GetByPlaidAccountIds(It.IsAny<string[]>(), default))
                .ReturnsAsync(new List<BankAccountDto>
                {
                    new()
                    {
                        Id = Guid.NewGuid().ToString(),
                        Plaid = new BankAccountPlaidDto
                        {
                            AccountId = accountId1,
                            IncludeInCashFlow = true
                        }
                    },
                    new()
                    {
                        Id = Guid.NewGuid().ToString(),
                        Plaid = new BankAccountPlaidDto
                        {
                            AccountId = accountId2,
                            IncludeInCashFlow = true
                        }
                    }
                });

            // Setup mock queryable instead of Get
            var mockQueryable = cashFlowItems.AsQueryable().BuildMockDbSet();
            _cashFlowItemRepositoryMock.Setup(x => x.Query).Returns(mockQueryable.Object);

            // Act
            var result = await _service.GetById(companyId, assetReportQuery, default);

            // Assert
            result.ShouldNotBeNull();
            result.CashFlowItems.ShouldNotBeNull();
            result.CashFlowItems.Count.ShouldBe(2); // One for each month
            result.CashFlowItems.ShouldAllBe(x => x.AccountId == null);

            // Verify monthly totals
            var currentMonth = result.CashFlowItems.First(x => x.Date.Month == today.Month);
            currentMonth.Credit.ShouldBe(300); // 100 + 200
            currentMonth.Debit.ShouldBe(150); // 50 + 100
            currentMonth.CashFlowResult.ShouldBe(150);

            var previousMonth = result.CashFlowItems.First(x => x.Date.Month == today.AddMonths(-1).Month);
            previousMonth.Credit.ShouldBe(300);
            previousMonth.Debit.ShouldBe(150);
            previousMonth.CashFlowResult.ShouldBe(150);

            // Verify repository calls
            _companyRepositoryMock.Verify(x => x.GetByLegacyId(companyId, default), Times.Once);
            _cashFlowRepositoryMock.Verify(
                x => x.GetAccountIds(It.IsAny<Expression<Func<CashFlowEntity, bool>>>(), default), Times.Once);
            _bankAccountRepositoryMock.Verify(x => x.GetByPlaidAccountIds(It.IsAny<string[]>(), default), Times.Once);
        }

        [Theory]
        [AutoDataWithDateOnly]
        public async Task GetById_MultipleAccountsGroupingByMonthWithToDate_ValidData(
            string companyId,
            CompanyEntity company)
        {
            // Arrange
            var assetReportQuery = AssetReportQueryModels.MultipleAccountGroupingByMonthWithToDate;
            var accountId1 = "test-account-id-1";
            var accountId2 = "test-account-id-2";

            // Get the ToDate from the query and ensure all test data is after it
            var toDate = DateOnly.FromDateTime(assetReportQuery.To!.Value);
            var futureDate = toDate.AddDays(5); // Use a date well after the ToDate

            var cashFlowItems = new List<CashFlowItemEntity>
            {
                new()
                {
                    Date = futureDate,
                    CashFlow = new CashFlowEntity { CompanyId = company.Id, AccountId = accountId1 },
                    Credit = 100,
                    Debit = 50,
                    CashFlowResult = 50,
                    Balance = 1000
                },
                new()
                {
                    Date = futureDate.AddDays(1),
                    CashFlow = new CashFlowEntity { CompanyId = company.Id, AccountId = accountId2 },
                    Credit = 200,
                    Debit = 100,
                    CashFlowResult = 100,
                    Balance = 2000
                }
            };

            _companyRepositoryMock
                .Setup(x => x.GetByLegacyId(companyId, default))
                .ReturnsAsync(company);

            _cashFlowRepositoryMock
                .Setup(x => x.GetAccountIds(It.IsAny<Expression<Func<CashFlowEntity, bool>>>(), default))
                .ReturnsAsync(new List<string> { accountId1, accountId2 });

            _bankAccountRepositoryMock
                .Setup(x => x.GetByPlaidAccountIds(It.IsAny<string[]>(), default))
                .ReturnsAsync(new List<BankAccountDto>
                {
                    new() { Plaid = new() { AccountId = accountId1, IncludeInCashFlow = true } },
                    new() { Plaid = new() { AccountId = accountId2, IncludeInCashFlow = true } }
                });

            // Setup mock queryable instead of Get
            var mockQueryable = cashFlowItems.AsQueryable().BuildMockDbSet();
            _cashFlowItemRepositoryMock.Setup(x => x.Query).Returns(mockQueryable.Object);

            // Act
            var result = await _service.GetById(companyId, assetReportQuery, default);

            // Assert
            result.ShouldNotBeNull();
            result.CashFlowItems.ShouldNotBeNull();
            result.CashFlowItems.Count.ShouldBe(0); // Since all dates are after ToDate
            result.CashFlowItems.ShouldBeEmpty();

            // Verify repository calls
            _companyRepositoryMock.Verify(x => x.GetByLegacyId(companyId, default), Times.Once);
            _cashFlowRepositoryMock.Verify(
                x => x.GetAccountIds(It.IsAny<Expression<Func<CashFlowEntity, bool>>>(), default), Times.Once);
            _bankAccountRepositoryMock.Verify(x => x.GetByPlaidAccountIds(It.IsAny<string[]>(), default), Times.Once);
        }


        [Theory]
        [AutoDataWithDateOnly]
        public async Task GetCashFlowPaginated_ValidCompanyId_ShouldReturnPaginatedListOfCashflowItems(
            string companyId,
            CompanyEntity company)
        {
            // Arrange
            var query = new CashFlowListQueryModel
            {
                PageNumber = 1,
                PageSize = 4
            };

            var bankAccountId = "test-account-id";
            var cashFlow = new CashFlowEntity
            {
                CompanyId = company.Id,
                AccountId = bankAccountId,
                Id = Guid.NewGuid(),
                IsFileUpload = false,
                CreatedAt = DateTime.UtcNow
            };

            var cashFlowItems = new List<CashFlowItemEntity>
            {
                new()
                {
                    Date = DateOnly.FromDateTime(DateTime.Today),
                    CashFlow = cashFlow,
                    CashFlowId = cashFlow.Id,
                    Credit = 100,
                    Debit = 50,
                    CashFlowResult = 50,
                    Balance = 1000,
                },
                new()
                {
                    Date = DateOnly.FromDateTime(DateTime.Today.AddDays(-1)),
                    CashFlow = cashFlow,
                    CashFlowId = cashFlow.Id,
                    Credit = 200,
                    Debit = 100,
                    CashFlowResult = 100,
                    Balance = 950,
                },
                new()
                {
                    Date = DateOnly.FromDateTime(DateTime.Today.AddDays(-2)),
                    CashFlow = cashFlow,
                    CashFlowId = cashFlow.Id,
                    Credit = 300,
                    Debit = 150,
                    CashFlowResult = 150,
                    Balance = 850,
                },
                new()
                {
                    Date = DateOnly.FromDateTime(DateTime.Today.AddDays(-3)),
                    CashFlow = cashFlow,
                    CashFlowId = cashFlow.Id,
                    Credit = 400,
                    Debit = 200,
                    CashFlowResult = 200,
                    Balance = 700,
                },
                new()
                {
                    Date = DateOnly.FromDateTime(DateTime.Today.AddDays(-4)),
                    CashFlow = cashFlow,
                    CashFlowId = cashFlow.Id,
                    Credit = 500,
                    Debit = 250,
                    CashFlowResult = 250,
                    Balance = 500,
                }
            };

            cashFlow.CashFlowItems = cashFlowItems;

            var bankAccounts = new List<BankAccountDto>
            {
                new()
                {
                    Id = Guid.NewGuid().ToString(),
                    Name = "Test Account",
                    Plaid = new()
                    {
                        IncludeInCashFlow = true,
                        AccountId = bankAccountId,
                    }
                }
            };

            _companyRepositoryMock
                .Setup(x => x.GetByLegacyId(companyId, default))
                .ReturnsAsync(company);

            _cashFlowRepositoryMock
                .Setup(x => x.GetAccountIds(It.IsAny<Expression<Func<CashFlowEntity, bool>>>(), default))
                .ReturnsAsync(new List<string> { bankAccountId });

            _bankAccountRepositoryMock
                .Setup(x => x.GetByPlaidAccountIds(It.IsAny<string[]>(), default))
                .ReturnsAsync(bankAccounts);

            _cashFlowRepositoryMock
                .Setup(x => x.Get(It.IsAny<Expression<Func<CashFlowEntity, bool>>>(), default))
                .ReturnsAsync(new List<CashFlowEntity> { cashFlow });

            // Act
            var result = await _service.GetPaginatedCashFlow(companyId, query, default);

            // Assert
            result.ShouldNotBeNull();
            result.Result.ShouldNotBeNull();
            result.Result.Count().ShouldBe(4); // PageSize
            result.PageNumber.ShouldBe(query.PageNumber);
            result.PagesCount.ShouldBe((int)Math.Ceiling((double)cashFlowItems.Count / query.PageSize));

            // Verify items are ordered by date descending
            var items = result.Result.ToList();
            items.ShouldNotBeEmpty();
            for (int i = 1; i < items.Count; i++)
            {
                items[i - 1].Date.ShouldBeGreaterThan(items[i].Date);
            }

            // Verify first item's properties
            var firstItem = items.First();
            firstItem.ShouldNotBeNull();
            firstItem.Date.ShouldBe(DateOnly.FromDateTime(DateTime.Today));
            firstItem.Credit.ShouldBe(100);
            firstItem.Debit.ShouldBe(50);
            firstItem.CashFlowResult.ShouldBe(50);
            firstItem.Balance.ShouldBe(1000);

            // Verify repository calls
            _companyRepositoryMock.Verify(x => x.GetByLegacyId(companyId, default), Times.Once);
            _cashFlowRepositoryMock.Verify(
                x => x.GetAccountIds(It.IsAny<Expression<Func<CashFlowEntity, bool>>>(), default), Times.Once);
            _bankAccountRepositoryMock.Verify(x => x.GetByPlaidAccountIds(It.IsAny<string[]>(), default), Times.Once);
            _cashFlowRepositoryMock.Verify(x => x.Get(It.IsAny<Expression<Func<CashFlowEntity, bool>>>(), default),
                Times.Once);
        }

        [Theory]
        [AutoDataWithDateOnly]
        public async Task GetById_MultipleAccountsGroupingByMonthWithFromDate_ValidData(
            string companyId,
            CompanyEntity company)
        {
            // Arrange
            var assetReportQuery = AssetReportQueryModels.MultipleAccountGroupingByMonthWithFromDate;
            var accountId1 = "test-account-id-1";
            var accountId2 = "test-account-id-2";

            // Setup company repository
            _companyRepositoryMock
                .Setup(x => x.GetByLegacyId(companyId, default))
                .ReturnsAsync(company);

            // Setup cashflow repository for account IDs
            _cashFlowRepositoryMock
                .Setup(x => x.GetAccountIds(It.IsAny<Expression<Func<CashFlowEntity, bool>>>(), default))
                .ReturnsAsync(new List<string> { accountId1, accountId2 });

            // Setup bank account repository
            _bankAccountRepositoryMock
                .Setup(x => x.GetByPlaidAccountIds(new[] { accountId1, accountId2 }, default))
                .ReturnsAsync(new List<BankAccountDto>
                {
                    new() { Plaid = new() { AccountId = accountId1, IncludeInCashFlow = true } },
                    new() { Plaid = new() { AccountId = accountId2, IncludeInCashFlow = true } }
                });

            var fromDate = DateOnly.FromDateTime(assetReportQuery.From!.Value);
            var cashFlowItems = new List<CashFlowItemEntity>
            {
                new()
                {
                    Date = fromDate.AddDays(-10), // Outside range (before)
                    CashFlowResult = 100,
                    Debit = 50,
                    Credit = 150,
                    Balance = 1000,
                    CashFlow = new CashFlowEntity { CompanyId = company.Id, AccountId = accountId1 }
                },
                new()
                {
                    Date = fromDate.AddDays(5), // Inside range
                    CashFlowResult = 200,
                    Debit = 100,
                    Credit = 300,
                    Balance = 1200,
                    CashFlow = new CashFlowEntity { CompanyId = company.Id, AccountId = accountId1 }
                },
                new()
                {
                    Date = fromDate.AddDays(35), // Inside range, next month
                    CashFlowResult = 300,
                    Debit = 150,
                    Credit = 450,
                    Balance = 1500,
                    CashFlow = new CashFlowEntity { CompanyId = company.Id, AccountId = accountId2 }
                }
            };

            // Setup mock queryable
            var mockQueryable = cashFlowItems.AsQueryable().BuildMockDbSet();
            _cashFlowItemRepositoryMock.Setup(x => x.Query).Returns(mockQueryable.Object);

            // Act
            var result = await _service.GetById(companyId, assetReportQuery, default);

            // Assert
            result.ShouldNotBeNull();
            result.CashFlowItems.ShouldNotBeNull();

            // Should have 2 items (grouped by month) since two dates are within range in different months
            result.CashFlowItems.Count.ShouldBe(2);
            result.CashFlowItems.ShouldAllBe(x => x.AccountId == null);

            // Verify the sums for items after fromDate
            var itemsAfterFromDate = cashFlowItems.Where(x => x.Date >= fromDate);
            result.CashFlowItems.Sum(x => x.CashFlowResult).ShouldBe(itemsAfterFromDate.Sum(x => x.CashFlowResult));
            result.CashFlowItems.Sum(x => x.Debit).ShouldBe(itemsAfterFromDate.Sum(x => x.Debit));
            result.CashFlowItems.Sum(x => x.Credit).ShouldBe(itemsAfterFromDate.Sum(x => x.Credit));

            // Verify repository calls
            _companyRepositoryMock.Verify(x => x.GetByLegacyId(companyId, default), Times.Once);
            _cashFlowRepositoryMock.Verify(
                x => x.GetAccountIds(It.IsAny<Expression<Func<CashFlowEntity, bool>>>(), default), Times.Once);
            _bankAccountRepositoryMock.Verify(x => x.GetByPlaidAccountIds(It.IsAny<string[]>(), default), Times.Once);
        }

        [Theory]
        [AutoDataWithDateOnly]
        public async Task GetById_MultipleAccountsGroupingByMonthWithBothDates_ValidData(
            string companyId,
            CompanyEntity company)
        {
            var assetReportQuery = AssetReportQueryModels.MultipleAccountGroupingByMonthWithBothDates;
            var accountId1 = "test-account-id-1";
            var accountId2 = "test-account-id-2";

            var fromDate = DateOnly.FromDateTime(assetReportQuery.From!.Value);
            var toDate = DateOnly.FromDateTime(assetReportQuery.To!.Value);

            // Setup company repository
            _companyRepositoryMock
                .Setup(x => x.GetByLegacyId(companyId, default))
                .ReturnsAsync(company);

            // Setup cashflow repository for account IDs
            _cashFlowRepositoryMock
                .Setup(x => x.GetAccountIds(It.IsAny<Expression<Func<CashFlowEntity, bool>>>(), default))
                .ReturnsAsync(new List<string> { accountId1, accountId2 });

            // Setup bank account repository
            _bankAccountRepositoryMock
                .Setup(x => x.GetByPlaidAccountIds(new[] { accountId1, accountId2 }, default))
                .ReturnsAsync(new List<BankAccountDto>
                {
                    new() { Plaid = new() { AccountId = accountId1, IncludeInCashFlow = true } },
                    new() { Plaid = new() { AccountId = accountId2, IncludeInCashFlow = true } }
                });

            var cashFlowItems = new List<CashFlowItemEntity>
            {
                new()
                {
                    Date = fromDate.AddDays(-10), // Outside range (before)
                    CashFlowResult = 100,
                    Debit = 50,
                    Credit = 150,
                    CashFlow = new CashFlowEntity { CompanyId = company.Id, AccountId = accountId1 }
                },
                new()
                {
                    Date = fromDate.AddDays(5), // Inside range
                    CashFlowResult = 200,
                    Debit = 100,
                    Credit = 300,
                    CashFlow = new CashFlowEntity { CompanyId = company.Id, AccountId = accountId1 }
                },
                new()
                {
                    Date = toDate.AddDays(10), // Outside range (after)
                    CashFlowResult = 300,
                    Debit = 150,
                    Credit = 450,
                    CashFlow = new CashFlowEntity { CompanyId = company.Id, AccountId = accountId2 }
                }
            };

            // Setup mock queryable instead of Get
            var mockQueryable = cashFlowItems.AsQueryable().BuildMockDbSet();
            _cashFlowItemRepositoryMock.Setup(x => x.Query).Returns(mockQueryable.Object);

            var result = await _service.GetById(companyId, assetReportQuery, default);

            // Should have 1 item (grouped by month) since only one date is within range
            result.CashFlowItems.Count.ShouldBe(1);

            var item = result.CashFlowItems.Single();
            item.CashFlowResult.ShouldBe(200);
            item.Debit.ShouldBe(100);
            item.Credit.ShouldBe(300);
            item.AccountId.ShouldBeNull(); // Because it's grouped by month

            // Verify calls
            _companyRepositoryMock.Verify(x => x.GetByLegacyId(companyId, default), Times.Once);
            _cashFlowRepositoryMock.Verify(
                x => x.GetAccountIds(It.IsAny<Expression<Func<CashFlowEntity, bool>>>(), default), Times.Once);
            _bankAccountRepositoryMock.Verify(x => x.GetByPlaidAccountIds(It.IsAny<string[]>(), default), Times.Once);
        }

        private static void CheckResultSums(
            IEnumerable<CashFlowItemResponseModel> result,
            IEnumerable<CashFlowItemEntity> cashFlowItems)
        {
            var cashFlowItemResponseModels = result as CashFlowItemResponseModel[] ?? result.ToArray();
            var cashFlowItemEntities = cashFlowItems as CashFlowItemEntity[] ?? cashFlowItems.ToArray();

            cashFlowItemResponseModels.Sum(x => x.CashFlowResult)
                .ShouldBe(cashFlowItemEntities.Sum(x => x.CashFlowResult));
            cashFlowItemResponseModels.Sum(x => x.Debit).ShouldBe(cashFlowItemEntities.Sum(x => x.Debit));
            cashFlowItemResponseModels.Sum(x => x.Credit).ShouldBe(cashFlowItemEntities.Sum(x => x.Credit));
        }

        [Theory]
        [AutoDataWithDateOnly]
        public async Task GetCashFlowAggregate_WhenCompanyHasData_ReturnsAggregatedData(
            string companyId,
            CompanyEntity company,
            List<string> accountIds)
        {
            // Arrange
            var currentDate = new DateTime(2024, 1, 15, hour: 9, 0, 0, DateTimeKind.Utc);

            _dateProviderMock.Setup(x => x.CurrentDateTime).Returns(currentDate);
            _companyRepositoryMock.Setup(x => x.GetByLegacyId(companyId, default))
                .ReturnsAsync(company);

            _cashFlowRepositoryMock
                .Setup(x => x.GetAccountIds(It.IsAny<Expression<Func<CashFlowEntity, bool>>>(), default))
                .ReturnsAsync(accountIds);

            // Mock included accounts
            var includedAccountIds = accountIds.Take(2).ToList();
            _bankAccountRepositoryMock
                .Setup(x => x.GetByPlaidAccountIds(It.IsAny<string[]>(), default))
                .ReturnsAsync(includedAccountIds.Select(id => new BankAccountDto
                {
                    Plaid = new BankAccountPlaidDto { AccountId = id, IncludeInCashFlow = true }
                }).ToList());

            // Mock cash flow items
            var cashFlowItems = new List<CashFlowItemEntity>
            {
                new()
                {
                    Date = DateOnly.FromDateTime(currentDate),
                    Balance = 1000,
                    CashFlowResult = 100,
                    CashFlow = new CashFlowEntity { CompanyId = company.Id, AccountId = includedAccountIds[0] }
                },
                new()
                {
                    Date = DateOnly.FromDateTime(currentDate.AddMonths(-1)),
                    Balance = 900,
                    CashFlowResult = 200,
                    CashFlow = new CashFlowEntity { CompanyId = company.Id, AccountId = includedAccountIds[0] }
                },
                new()
                {
                    Date = DateOnly.FromDateTime(currentDate.AddMonths(-2)),
                    Balance = 800,
                    CashFlowResult = 300,
                    CashFlow = new CashFlowEntity { CompanyId = company.Id, AccountId = includedAccountIds[1] }
                }
            };

            var mockQueryable = cashFlowItems.AsQueryable().BuildMockDbSet();
            _cashFlowItemRepositoryMock.Setup(x => x.Query)
                .Returns(mockQueryable.Object);

            // Act
            var result = await _service.GetCashFlowAggregate(companyId, default);

            // Assert
            result.ShouldNotBeNull();
            result.SixMonthAverageBankBalance.ShouldBe(900M); // Average of all balances
            result.AverageMonthlyCashFlow.ShouldBe(200M); // Average of all cash flow results

            // Verify calls
            _companyRepositoryMock.Verify(x => x.GetByLegacyId(companyId, default), Times.Once);
            _cashFlowRepositoryMock.Verify(
                x => x.GetAccountIds(It.IsAny<Expression<Func<CashFlowEntity, bool>>>(), default),
                Times.Once);
            _bankAccountRepositoryMock.Verify(
                x => x.GetByPlaidAccountIds(It.IsAny<string[]>(), default),
                Times.Once);
        }

        [Theory]
        [AutoDataWithDateOnly]
        public async Task GetCashFlowAggregate_WhenCompanyHasNoAccounts_ReturnsEmptyAggregate(
            string companyId,
            CompanyEntity company)
        {
            // Arrange
            var currentDate = new DateTime(2024, 1, 15, hour: 9, 0, 0, DateTimeKind.Utc);
            _dateProviderMock.Setup(x => x.CurrentDateTime)
                .Returns(currentDate);

            _companyRepositoryMock.Setup(x => x.GetByLegacyId(companyId, default))
                .ReturnsAsync(company);

            _cashFlowRepositoryMock
                .Setup(x => x.GetAccountIds(It.IsAny<Expression<Func<CashFlowEntity, bool>>>(), default))
                .ReturnsAsync(new List<string>());

            // Act
            var result = await _service.GetCashFlowAggregate(companyId, default);

            // Assert
            result.ShouldNotBeNull();
            result.SixMonthAverageBankBalance.ShouldBe(0);
            result.AverageMonthlyCashFlow.ShouldBe(0);

            // Verify calls
            _companyRepositoryMock.Verify(x => x.GetByLegacyId(companyId, default), Times.Once);
            _cashFlowRepositoryMock.Verify(
                x => x.GetAccountIds(It.IsAny<Expression<Func<CashFlowEntity, bool>>>(), default),
                Times.Once);
        }

        [Fact]
        public async Task GetCashFlowAggregate_WhenCompanyDoesNotExist_ThrowsException()
        {
            // Arrange
            var companyId = "non-existent";
            _companyRepositoryMock.Setup(x => x.GetByLegacyId(companyId, default))
                .ReturnsAsync((CompanyEntity)null!);

            // Act & Assert
            await Should.ThrowAsync<CompanyDoesNotExistException>(
                () => _service.GetCashFlowAggregate(companyId, default));
        }

        [Theory]
        [AutoDataWithDateOnly]
        public async Task GetCashFlowAggregate_WhenNoTransactionsExist_ReturnsZeroAverages(
            string companyId,
            CompanyEntity company,
            List<string> accountIds)
        {
            // Arrange
            var currentDate = new DateTime(2024, 1, 15, hour: 9, 0, 0, DateTimeKind.Utc);
            _dateProviderMock.Setup(x => x.CurrentDateTime)
                .Returns(currentDate);

            _companyRepositoryMock.Setup(x => x.GetByLegacyId(companyId, default))
                .ReturnsAsync(company);

            // Setup accounts that are included in cash flow
            _cashFlowRepositoryMock
                .Setup(x => x.GetAccountIds(It.IsAny<Expression<Func<CashFlowEntity, bool>>>(), default))
                .ReturnsAsync(accountIds);

            var includedAccountIds = accountIds.Take(2).ToList();
            _bankAccountRepositoryMock
                .Setup(x => x.GetByPlaidAccountIds(It.IsAny<string[]>(), default))
                .ReturnsAsync(includedAccountIds.Select(id => new BankAccountDto
                {
                    Plaid = new BankAccountPlaidDto { AccountId = id, IncludeInCashFlow = true }
                }).ToList());

            // Setup empty query result
            var emptyQueryable = new List<CashFlowItemEntity>().AsQueryable();
            var mockQueryable = emptyQueryable.BuildMockDbSet();
            _cashFlowItemRepositoryMock.Setup(x => x.Query)
                .Returns(mockQueryable.Object);

            // Act
            var result = await _service.GetCashFlowAggregate(companyId, default);

            // Assert
            result.ShouldNotBeNull();
            result.SixMonthAverageBankBalance.ShouldBe(0);
            result.AverageMonthlyCashFlow.ShouldBe(0);

            // Verify that Query was called
            _cashFlowItemRepositoryMock.Verify(x => x.Query, Times.AtLeast(2));
        }

        [Theory]
        [AutoDataWithDateOnly]
        public async Task ValidateManualCashFlow_InvalidFile_ReturnsValidationErrors(
            FileValidationResult expectedResult)
        {
            // Arrange
            var stream = new MemoryStream();
            var writer = new StreamWriter(stream);
            await writer.WriteAsync("Invalid,CSV,Format\nrow1,row2");
            await writer.FlushAsync();
            stream.Position = 0;

            _csvFileReaderMock
                .Setup(x => x.ValidateAsync(It.IsAny<Stream>(), It.IsAny<ManualReportTransactionModelMap>(), false))
                .ReturnsAsync(expectedResult);

            // Act
            var result = await _service.ValidateManualCashFlow(stream);

            // Assert
            result.ShouldNotBeNull();
            result.ShouldBe(expectedResult);

            _csvFileReaderMock.Verify(
                x => x.ValidateAsync(It.IsAny<Stream>(), It.IsAny<ManualReportTransactionModelMap>(), false),
                Times.Once);
        }

        [Theory]
        [AutoDataWithDateOnly]
        public async Task AddManualByFile_CompanyNotFound_ThrowsNullReferenceException(
            string companyId,
            string accountId,
            string userId)
        {
            // Arrange
            var stream = new MemoryStream();
            var writer = new StreamWriter(stream);
            await writer.WriteAsync("Date,Amount,Balance\n01/01/2024,100.00,1000.00");
            await writer.FlushAsync();
            stream.Position = 0;

            var currentDateTime = new DateTime(2024, 1, 1);
            _dateProviderMock
                .Setup(x => x.CurrentDateTime)
                .Returns(currentDateTime);

            _configurationMock
                .Setup(x => x["CompanyS3Options:UserAssetsBucket"])
                .Returns("test-bucket");

            _companyRepositoryMock
                .Setup(x => x.GetByLegacyId(companyId, It.IsAny<CancellationToken>()))
                .ReturnsAsync((CompanyEntity)null!);

            _companyRepositoryMock
                .Setup(x => x.TryMigrateLegacyCompanyIdAsync(companyId, It.IsAny<CancellationToken>()))
                .ReturnsAsync((CompanyEntity)null!);

            _csvFileReaderMock
                .Setup(x => x.ReadAsync(It.IsAny<Stream>(), It.IsAny<ManualReportTransactionModelMap>(), false))
                .ReturnsAsync(new List<ManualReportTransactionModel>
                {
                    new()
                    {
                        Date = "01/01/2024",
                        TransactionAmount = 100.00m,
                        CumulatedBalance = 1000.00m
                    }
                });

            _s3ClientMock
                .Setup(x => x.SaveTextAsync(It.IsAny<string>(), It.IsAny<string>(), It.IsAny<string>(),
                    It.IsAny<CancellationToken>()))
                .Returns(Task.CompletedTask);

            // Act & Assert
            await Should.ThrowAsync<NullReferenceException>(
                async () => await _service.AddManualByFile(companyId, accountId, userId, stream, default));

            // Verify
            _companyRepositoryMock.Verify(x => x.GetByLegacyId(companyId, It.IsAny<CancellationToken>()), Times.Once);
            _companyRepositoryMock.Verify(
                x => x.TryMigrateLegacyCompanyIdAsync(companyId, It.IsAny<CancellationToken>()), Times.Once);
            _s3ClientMock.Verify(
                x => x.SaveTextAsync(It.IsAny<string>(), It.IsAny<string>(), "test-bucket",
                    It.IsAny<CancellationToken>()),
                Times.Once);
            _csvFileReaderMock.Verify(
                x => x.ReadAsync(It.IsAny<Stream>(), It.IsAny<ManualReportTransactionModelMap>(), false),
                Times.Once);
        }

        [Theory]
        [AutoDataWithDateOnly]
        public async Task AddManualByFile_InvalidCsvFormat_ThrowsException(
            string companyId,
            string accountId,
            string userId,
            CompanyEntity company)
        {
            // Arrange
            var stream = new MemoryStream();
            var writer = new StreamWriter(stream);
            await writer.WriteAsync("Invalid,CSV,Format\nrow1,row2");
            await writer.FlushAsync();
            stream.Position = 0;

            var currentDateTime = new DateTime(2024, 1, 1);
            _dateProviderMock
                .Setup(x => x.CurrentDateTime)
                .Returns(currentDateTime);

            _configurationMock
                .Setup(x => x["CompanyS3Options:UserAssetsBucket"])
                .Returns("test-bucket");

            _companyRepositoryMock
                .Setup(x => x.GetByLegacyId(It.IsAny<string>(), It.IsAny<CancellationToken>()))
                .ReturnsAsync(company);

            _csvFileReaderMock
                .Setup(x => x.ReadAsync(It.IsAny<Stream>(), It.IsAny<ManualReportTransactionModelMap>(), false))
                .ReturnsAsync(new List<ManualReportTransactionModel>
                {
                    new()
                    {
                        Date = "Invalid Date",
                        TransactionAmount = 100.00m,
                        CumulatedBalance = 1000.00m
                    }
                });

            // Act & Assert
            await Should.ThrowAsync<IncorrectDateFormatException>(
                () => _service.AddManualByFile(companyId, accountId, userId, stream, default));

            _companyRepositoryMock.Verify(
                x => x.GetByLegacyId(It.IsAny<string>(), It.IsAny<CancellationToken>()),
                Times.Never);
            _csvFileReaderMock.Verify(
                x => x.ReadAsync(It.IsAny<Stream>(), It.IsAny<ManualReportTransactionModelMap>(), false),
                Times.Once);
        }

        [Theory]
        [AutoDataWithDateOnly]
        public async Task AddManualByFile_ExistingCashFlow_UpdatesExistingRecords(
            string companyId,
            string accountId,
            string userId,
            CompanyEntity company,
            CashFlowEntity existingCashFlow)
        {
            // Arrange
            var stream = new MemoryStream();
            var writer = new StreamWriter(stream);
            await writer.WriteAsync("Date,Amount,Balance\n01/01/2024,100.00,1000.00");
            await writer.FlushAsync();
            stream.Position = 0;

            var currentDateTime = new DateTime(2024, 1, 1);
            _dateProviderMock
                .Setup(x => x.CurrentDateTime)
                .Returns(currentDateTime);

            var bucketUri = "test-bucket";
            _configurationMock
                .Setup(x => x["CompanyS3Options:UserAssetsBucket"])
                .Returns(bucketUri);

            _companyRepositoryMock
                .Setup(x => x.GetByLegacyId(companyId, default))
                .ReturnsAsync(company);

            var testTransactions = new List<ManualReportTransactionModel>
            {
                new()
                {
                    Date = "01/01/2024",
                    TransactionAmount = 100.00m,
                    CumulatedBalance = 1000.00m
                }
            };

            _csvFileReaderMock
                .Setup(x => x.ReadAsync(It.IsAny<Stream>(), It.IsAny<ManualReportTransactionModelMap>(), false))
                .ReturnsAsync(testTransactions);

            _cashFlowRepositoryMock
                .Setup(x => x.GetByAccountId(accountId, default))
                .ReturnsAsync(existingCashFlow);

            CashFlowEntity capturedEntity = null!;
            _cashFlowRepositoryMock
                .Setup(x => x.UpdateRange(It.IsAny<IEnumerable<CashFlowEntity>>(), default))
                .Callback<IEnumerable<CashFlowEntity>, CancellationToken>((entities, _) =>
                {
                    capturedEntity = entities.First();
                });

            // Act
            var result = await _service.AddManualByFile(companyId, accountId, userId, stream, default);

            // Assert
            result.ShouldNotBeNull();
            result.Result.ShouldBeTrue();

            capturedEntity.ShouldNotBeNull();
            capturedEntity.FileName!.ShouldContain("processed");
            capturedEntity.S3Url!.ShouldContain("processed");

            _companyRepositoryMock.Verify(x => x.GetByLegacyId(companyId, default), Times.Once);
            _csvFileReaderMock.Verify(
                x => x.ReadAsync(It.IsAny<Stream>(), It.IsAny<ManualReportTransactionModelMap>(), false),
                Times.Once);
            _s3ClientMock.Verify(
                x => x.SaveTextAsync(It.IsAny<string>(), It.IsAny<string>(), bucketUri, default),
                Times.Once);
            _s3ClientMock.Verify(
                x => x.MoveS3ObjectAsync(bucketUri, It.IsAny<string>(), It.IsAny<string>(), default),
                Times.Once);
            _cashFlowRepositoryMock.Verify(
                x => x.UpdateRange(It.IsAny<IEnumerable<CashFlowEntity>>(), default),
                Times.Once);
        }
    }
}