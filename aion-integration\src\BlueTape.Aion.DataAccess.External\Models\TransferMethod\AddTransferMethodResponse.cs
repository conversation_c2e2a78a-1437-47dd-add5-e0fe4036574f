﻿using System.Runtime.Serialization;
using System.Text.Json.Serialization;
using BlueTape.Aion.DataAccess.External.Models.CreateCounterParty;

namespace BlueTape.Aion.DataAccess.External.Models.TransferMethod;

[DataContract]
public class AddTransferMethodResponse : BaseAionResponseModel
{
    [JsonPropertyName("counterpartiesObj")]
    public CounterpartyObjectResponse CounterpartiesObj { get; set; } = null!;

    [JsonPropertyName("transferMethod")]
    public TransferMethodResponseObject TransferMethod { get; set; } = null!;
}