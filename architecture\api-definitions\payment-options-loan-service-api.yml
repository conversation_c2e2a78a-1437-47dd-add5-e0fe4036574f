openapi: '3.0.0'
info:
  version: '0.0.1'
  title: 'Loan Service Payables Details API'
  description: | 
    API definition proposal for payables for loans and loan receivables
servers:
- url: TBD-Dev
  description: Development server
- url: TBD-QA
  description: QA server
- url: TBD-Prod
  description: Production server
paths:
  /loans/{id}/payables/details:
    get:
      tags:
        - Loans
      summary: Gets loan payables details
      description: Gets loan payables details
      operationId: getLoanPayablesDetails
      parameters:
        - name: id
          description: Identifier of the loan
          example: 6df30249-c7f0-4f17-b06f-c29b2fee0a62
          in: path
          required: true
          schema:
            type: string
            nullable: false
        - name: date
          description: The date to query. If not set, default is today.
          in: query
          required: false
          schema:
            type: string
            format: date
            nullable: true
      responses:
        200:
          description: The loan payables details
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/LoanPayablesDetails'
        400:
          description: Invalid request
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        401:
          description: Unauthorized
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        404:
          description: Not found
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        500:
          description: Unexpected error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
components:
  schemas:
    LoanPayablesDetails:
      type: object
      properties:
        id:
          type: string
          description: Identifier of loan to get details for
          example: 6df30249-c7f0-4f17-b06f-c29b2fee0a62
        loanAmount:
          type: number
          description: The full expected amount of loan, with loan fee included, without occasional fees. Does not affected by refunds. Sum(ExpectedAmount) where Installment/LoanFee
          format: decimal
          example: 1000.00
          pattern: '[0-9]*\.[0-9]{2}'
          multipleOf: 0.01
        loanTotalAmount:
          type: number
          description: The full expected amount of loan receivables, including installments and fees. Does not affected by refunds. Sum(ExpectedAmount)
          format: decimal
          example: 1035.00
          pattern: '[0-9]*\.[0-9]{2}'
          multipleOf: 0.01
        totalPaidAmount:
          type: number
          description: The total paid amount of loan receivables (fees included). Sum(ActualAmount - AdjustAmount). TotalPaidAmount + TotalUnpaidAmount = LoanTotalAmount.
          format: decimal
          example: 300.00
          pattern: '[0-9]*\.[0-9]{2}'
          multipleOf: 0.01
        totalUnpaidAmount:
          type: number
          description: The total unpaid amount of loan receivables (fees included). Sum(ExpectedAmount - ActualAmount + AdjustAmount). TotalPaidAmount + TotalUnpaidAmount = LoanTotalAmount
          format: decimal
          example: 735.00
          pattern: '[0-9]*\.[0-9]{2}'
          multipleOf: 0.01
        totalPastDueAmount:
          type: number
          description: The total past due outstanding amount (fees included) before the required day (see date filter). Sum(ExpectedAmount - ActualAmount + AdjustAmount).
          format: decimal
          example: 235.00
          pattern: '[0-9]*\.[0-9]{2}'
          multipleOf: 0.01
        totalDueAmount:
          type: number
          description: The total due outstanding amount (fees included) exactly on the required day (see date filter). Sum(ExpectedAmount - ActualAmount + AdjustAmount).
          format: decimal
          example: 0.00
          pattern: '[0-9]*\.[0-9]{2}'
          multipleOf: 0.01
        totalProcessingPaymentsAmount:
          type: number
          description: The total amount of Payments of this loan which is in progress state, without filtering to SubType. (Processing Refunds might cause negative amount for this field).
          format: decimal
          example: 235.00
          pattern: '[0-9]*\.[0-9]{2}'
          multipleOf: 0.01
        oldestDueOrPastDueDate:
          type: string
          format: date
          description: The oldest (due or past due) unpaid receivable's expected date.
          example: 2023-01-01
        oldestPastDueLateBusinessDays:
          type: integer
          format: int32
          description: The oldest past due unpaid receivable's late business days.
          example: 26
        nextUpcomingInstallmentAmount:
          type: number
          description: The next upcoming installment's (!) expected amount, compared to required day (see date filter). Sum(ExpectedAmount - ActualAmount ($0.00) + AdjustAmount).
          format: decimal
          example: 75.00
          pattern: '[0-9]*\.[0-9]{2}'
          multipleOf: 0.01
        nextUpcomingInstallmentDate:
          type: string
          format: date
          description: The next upcoming installment's (!) expected date, compared to required day (see date filter).
          example: 2023-05-04
        amounts:
          type: array
          items:
            $ref: '#/components/schemas/ReceivableTypeAmount'
          description: Details of loan receivable types's amounts. Aggreggated by receivable type, so this array will contain 2 - 4 items. (Installment+LoanFee, plus occasional fees)
    ReceivableTypeAmount:
      type: object
      properties:
        type: 
          type: string
          enum:
            - installment
            - loanFee
            - lateFee
            - extensionFee
          description: The type of receivable
        totalAmount:
          type: number
          description: The total expected amount of receivable type. Sum(ExpectedAmount)
          format: decimal
          example: 900.00
          pattern: '[0-9]*\.[0-9]{2}'
          multipleOf: 0.01
        totalAmountWithAdjustments:
          type: number
          description: The total expected amount of receivable type modified with adjustments. Sum(ExpectedAmount + AdjustAmount)
          format: decimal
          example: 900.00
          pattern: '[0-9]*\.[0-9]{2}'
          multipleOf: 0.01
        pastDueAmount:
          type: number
          description: The total past due outstanding amount of receivable type before the required day (see date filter). Sum(ExpectedAmount - ActualAmount + AdjustAmount)
          format: decimal
          example: 200.00
          pattern: '[0-9]*\.[0-9]{2}'
          multipleOf: 0.01
        dueAmount:
          type: number
          description: The total due outstanding amount of receivable type exactly on the required day (see date filter). Sum(ExpectedAmount - ActualAmount + AdjustAmount)
          format: decimal
          example: 0.00
          pattern: '[0-9]*\.[0-9]{2}'
          multipleOf: 0.01
        upcomingAmount:
          type: number
          description: The total upcoming amount of receivable type from the required day (see date filter). Sum(ExpectedAmount - ActualAmount + AdjustAmount)
          format: decimal
          example: 500.00
          pattern: '[0-9]*\.[0-9]{2}'
          multipleOf: 0.01
        paidAmount:
          type: number
          description: The total paid amount of receivable type. (Partial payments included.) Sum(ActualAmount - AdjustAmount).
          format: decimal
          example: 200.00
          pattern: '[0-9]*\.[0-9]{2}'
          multipleOf: 0.01
        unpaidAmount:
          type: number
          description: The total unpaid amount of receivable type. (Partial payments included.) Sum(ExpectedAmount - ActualAmount + AdjustAmount).
          format: decimal
          example: 700.00
          pattern: '[0-9]*\.[0-9]{2}'
          multipleOf: 0.01
    ErrorResponse:
      type: object
      required:
        - statusCode
        - message
        - errorDescription
      properties:
        statusCode:
          description: HTTP status code corresponding to the error.
          type: integer
          example: 400
        message:
          description: A human readable error message.
          type: string
          example: Invalid model.
        errorDescription:
          description: Detailed information.
          type: string
  securitySchemes:
    ApiKey:
      type: apiKey
      in: header
      name: X-Api-Key
security:
  - ApiKey: []