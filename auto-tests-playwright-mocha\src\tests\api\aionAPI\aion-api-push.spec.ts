import {expect} from '@playwright/test';
import {test} from '../../test-utils';
import {getAllTransactions, pushAion, sendAionApiRequest} from '../../../api/common/send-aion-api-request';
import {getCompanyById} from '../../../utils/get-info-from-mongo';

const error = JSON.parse(JSON.stringify(require('../../../constants/aion-errors.json')));
const constants = JSON.parse(JSON.stringify(require('../../../constants/constants.json')));

test.describe(`Aion pull tests @API`, async () => {
    const companyId: string = process.env.COMPANY_ID;
    const bankAccountId: string = process.env.BANK_ACCOUNT_ID;
    const amount: number = constants.aion.amount;
    const description: string = constants.aion.validDescription;
    const addenda: Array<string> = constants.aion.validAddenda;
    const invalidDescription: string = constants.aion.invalidDescriptionWirhMoreThanTenSymbols;
    const invalidAddenda: string = constants.aion.invalidAddendaWithMoreThanEightySymbols;
    const invalidCompanyId: string = constants.aion.invalidCompanyId;
    const invalidBankAccountId: string = constants.aion.invalidBankAccountId;
    const roundingAmount = constants.aion.roundingAmounts;
    const roundedAmountToDown = String(Math.round(roundingAmount.down * 100) / 100);
    const roundedeAmountWithFive = String(Math.round(roundingAmount.averageValue * 100) / 100);
    const roundedAmountToUp = String(Math.round(roundingAmount.up * 100) / 100);

    test(`Ach push @aion`, async () => {
        const response = await pushAion(amount, description, addenda, companyId, bankAccountId);

        expect(response.status, `Status code 200`).toEqual(200);

        expect(response.data, `Response contains Array of Template's Versions`).toEqual(expect.any(Object));
    });

    test(`Can not ach push with invalid company id @aion`, async () => {
        const response = await pushAion(amount, description, addenda, invalidCompanyId, bankAccountId);

        expect(response.response.status,
            `Status code 500`).toEqual(500);
    });

    test(`Can not ach push with invalid bank account id @aion`, async () => {
        const response = await pushAion(amount, description, addenda, companyId, invalidBankAccountId);

        expect(response.response.status, `Status code 400`).toEqual(400);

        expect(response.response.data[0].code,
            `Status code 400`).toEqual(error.pull.code.invalidBankAccount);
    });

    test(`Can not ach push with description symbols more than 10 @aion`, async () => {
        const response = await pushAion(amount, invalidDescription, addenda, companyId, bankAccountId);

        expect(response.response.status, `Status code 400`).toEqual(400);

        expect(response.response.data[0].code,
            `The length of 'Description' must be 10 characters or fewer.`)
            .toEqual(error.pull.code.descriptionMoreThanTenCharacters);
    });

    test(`Can not ach push with addenda more than 80 @aion`, async () => {
        const response = await pushAion(amount, description, invalidAddenda, companyId, bankAccountId);

        expect(response.response.status, `Status code 400`).toEqual(400);

        expect(response.response.data[0].code,
            `PredicateValidator`).toEqual(error.pull.code.addendaMoreThanEightyCharacters);

        expect(response.response.data[0].reason,
            `The total number of characters in the array must not exceed 80.`)
            .toEqual(error.pull.reason.addendaMoreThanEightyCharacters);
    });

    test(`Can not ach push with amount equal -1 @aion`, async () => {
        const amount = -1;
        const response = await pushAion(amount, description, addenda, companyId, bankAccountId);

        expect(response.response.status, `Status code 500`).toEqual(500);
    });

    test(`Can not ach push with description equal null @aion`, async () => {
        const description = null;
        const response = await pushAion(amount, description, addenda, companyId, bankAccountId);

        expect(response.response.status, `Status code 500`).toEqual(500);
    });

    test(`Can not ach push with addenda equal null @aion`, async () => {
        const addenda = null;
        const response = await pushAion(amount, description, addenda, companyId, bankAccountId);

        expect(response.response.status, `Status code 500`).toEqual(500);
    });

    test(`Can not ach push with companyId equal null @aion`, async () => {
        const companyId = null;
        const response = await pushAion(amount, description, addenda, companyId, bankAccountId);

        expect(response.response.status,
            `Status code 400`).toEqual(400);

        expect(response.response.data[0].code,
            `NotNullValidator`).toEqual(error.pull.code.notNullValidator);

        expect(response.response.data[0].reason,
            `'Company Id' must not be empty.`).toEqual(error.pull.reason.emptyCompanyId);
    });

    test(`Can not ach push with bankAccountId equal null @aion`, async () => {
        const bankAccountId = null;
        const response = await pushAion(amount, description, addenda, companyId, bankAccountId);

        expect(response.response.status, `Status code 400`).toEqual(400);

        expect(response.response.data[0].code,
            `NotNullValidator`).toEqual(error.pull.code.notNullValidator);

        expect(response.response.data[0].reason,
            `'Company Id' must not be empty.`).toEqual(error.pull.reason.emptyBankAccountId);
    });

    test(`Can not ach push with amount equal null @aion`, async () => {
        const amount = null;
        const response = await pushAion(amount, description, addenda, companyId, bankAccountId);

        expect(response.response.status, `Status code 400`).toEqual(400);

        expect(response.response.data[0].code,
            `InvalidModelState`).toEqual(error.pull.code.invalidModelState);
    });

    test(`Can not ach push with invalid secret key @aion`, async () => {
        const requestBody = {
            "amount": amount,
            "description": description,
            "addenda": addenda,
            "receiver": {
                "companyId": companyId,
                "bankAccountId": bankAccountId
            }
        };
        const response = await sendAionApiRequest('post', `api/ach/push`, requestBody, '1111111');

        expect(response.response.status, `Status code 401`).toEqual(401);
    });

    test(`Ach push. Get transaction. Amount should be equal ${amount}, addenda should be equal ${addenda}, description should be equal ${description} @aion`, async () => {
        const responseOfPush = await pushAion(amount, description, addenda, companyId, bankAccountId);
        const transactionId = await responseOfPush.data.achObj.id;
        const arrayOfTransactions = await getAllTransactions();
        const findTransactionById = await arrayOfTransactions.find((el) => el.id === transactionId);

        expect(findTransactionById.amount,
            `Amount should be equla ${amount}`).toEqual(String(amount));

        expect(findTransactionById.addenda,
            `Addenda should be equal ${addenda}`).toEqual(addenda);

        expect(findTransactionById.description,
            `description should be equal ${description}`).toEqual(description);
    });

    test(`Ach push with amount = ${roundingAmount.down} Amount should be rounding to ${roundedAmountToDown} @aion`, async () => {
        const responseOfPush = await pushAion(roundingAmount.down, description, addenda, companyId, bankAccountId);
        const transactionId = await responseOfPush.data.achObj.id;
        const arrayOfTransactions = await getAllTransactions();
        const findTransactionById = await arrayOfTransactions.find((el) => el.id === transactionId);

        expect(findTransactionById.amount,
            `Amount should be rounded to ${roundedAmountToDown}`).toEqual(roundedAmountToDown);
    });

    test(`Ach push with amount = ${roundingAmount.averageValue} Amount should be rounding to ${roundedeAmountWithFive} @aion`, async () => {
        const responseOfPush = await pushAion(roundingAmount.averageValue, description, addenda, companyId, bankAccountId);
        const transactionId = await responseOfPush.data.achObj.id;
        const arrayOfTransactions = await getAllTransactions();
        const findTransactionById = await arrayOfTransactions.find((el) => el.id === transactionId);

        expect(findTransactionById.amount,
            `Amount should be rounded to ${roundedeAmountWithFive}`).toEqual(roundedeAmountWithFive);
    });

    test(`Ach push with amount = ${roundingAmount.up} Amount should be rounding to ${roundedAmountToUp} @aion`, async () => {
        const responseOfPush = await pushAion(roundingAmount.averageValue, description, addenda, companyId, bankAccountId);
        const transactionId = await responseOfPush.data.achObj.id;
        const arrayOfTransactions = await getAllTransactions();
        const findTransactionById = await arrayOfTransactions.find((el) => el.id === transactionId);

        expect(findTransactionById.amount,
            `Amount should be rounded to ${roundedAmountToUp}`).toEqual(roundedAmountToUp);
    });

    test(`Counterparty name and couner party id from the transaction should be equal information in mongoDB @aion`, async () => {
        const getCompanyFromMongo = await getCompanyById(companyId);
        const responseOfPush = await pushAion(roundingAmount.averageValue, description, addenda, companyId, bankAccountId);

        expect(getCompanyFromMongo[0].name).toEqual(responseOfPush.data.achObj.counterpartyName);

        expect(getCompanyFromMongo[0].aion.counterPartyId).toEqual(responseOfPush.data.achObj.counterpartyId);
    });
});
