import sendAdminRequest from '../common/send-admin-request';

export default async function getUserSub(bearerToken:string, userEmail:string){
    try {
        const endpoint = `signups?search=automation_user`;
        const response = await sendAdminRequest('get', endpoint, bearerToken);
        const jsonresponse = JSON.parse(response.toString());
        const usersList = jsonresponse.items;
        const userObj = usersList.filter(item => item.email == userEmail);
        return userObj[0].sub;
    } catch (error) {
        console.log(error);
        return error;
    }
}
