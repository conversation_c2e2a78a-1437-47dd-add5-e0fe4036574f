{"common": {"invalidData": "invalid_data", "badRequest": "ERR_BAD_REQUEST"}, "customer": {"code": {"errorMessageNonUnicId": "customer_already_exist", "errorMessageNonUnicPhoneOrEmail": "customer_with_phone_or_email_already_exist", "errorMessageInvalidData": "customer_invalid_data", "errorMessageInvalidModelState": "InvalidModelState"}, "reason": {"errorWithEmptyFirstName": "First name could not be empty", "errorWithEmptyLastName": "Last name could not be empty", "errorWithInvalidPhoneNumber": "CellPhoneNumber is not valid. Only USA phone allowed. Value:", "errorWithEmptyCustomerId": "ContactId can not be empty", "errorWithIgnoredFirstName": "The FirstName field is required.", "errorWithIgnoredLastName": "The LastName field is required.", "errorWithIgnoredPhone": "The CellPhoneNumber field is required.", "errorWithIgnoredEmail": "The EmailAddress field is required.", "errorWithIgnoredCustomerId": "The Id field is required.", "errorWithInvalidEmail": "Email is not valid", "errorWithPhoneOrEmailWhichAlreadyExist": "Customer with: {{phoneOrEmail}} already registered.", "errorWithIdAlreadyExist": "Customer with id: \"{{id}}\" already exist.", "errorCustomerAlreadyExist": "Customer with id: \"{{id}}\" does not exist."}}, "company": {"code": {"nullInIdField": "InvalidModelState", "changeCompany": "customer_company_change", "customerNonExistent": "customer_does_not_exist", "invalidPhone": "customer_invalid_data", "companyAlreadyExist": "company_already_exist"}, "reason": {"emptyId": "Integration connector can't be null or empty", "companyIdNull": "The Id field is required.", "nullInIdField": "The Id field is required.", "customerIdNull": "The CustomerId field is required.", "companyWithIdAlreadyExist": "External Company with id: \"{{id}}\" already exist."}}, "invoice": {"code": {"invalidQuoteRefNumber": "quote_reference_does_not_exist", "invalidAmount": "GreaterThanValidator", "invalidDataFormat": "InvalidModelState", "invalidValidator": "GreaterThanOrEqualValidator", "invoiceNonExistent": "invoice_does_not_exist", "invoiceLinked": "invoice_already_linked_to_project"}, "reason": {"invalidTotaAmount": "'Total Amount' must be greater than '0'.", "invalidSubTotal": "'Sub Total' must be greater than '0'.", "invalidTaxAmount": "'Tax Amount' must be greater than or equal to '0'.", "invalidDataFormat": "The model field is required.", "invalidCustomerId": "The CustomerId field is required.", "invalidInvoiceNum": "The InvoiceNumber field is required.", "invalidExpirationDate": "'Expiration Date' must be greater than or equal to", "nullInIdField": "The Id field is required.", "invoiceNonExistent": "Invoice does not exist", "invoiceLinked": "Invoice already linked to project"}}, "project": {"code": {"projectAlreadyExist": "project_already_exist", "invalidDataFormat": "InvalidModelState", "invalidAmount": "GreaterThanOrEqualValidator", "emptyValue": "NotEmptyValidator", "invalidId": "project_does_not_exist"}, "reason": {"projectAlreadyExist": "Project already exist", "nullInNameField": "The Name field is required.", "nullInAddressField": "The Address field is required.", "nullInIdField": "The Id field is required.", "invalidContractAmount": "'Contract Value' must be greater than or equal to '0'.", "emptyNameField": "'Name' must not be empty.", "invalidId": "Project does not exist", "emptyAddressField": "'Address' must not be empty.", "invalidDataFormat": "The model field is required.", "invalidEndDate": "'End Date' must be greater than", "invalidType": "'Type' must not be empty.", "nullInPrivateProjectDetails": "'Private Project Details' must not be empty.", "nullInJobState": "The State field is required.", "nullInJobZipCode": "The ZipCode field is required.", "nullInJobCity": "The City field is required."}}, "quote": {"code": {"quoteAlreadyExist": "quote_already_exist", "invalidDocumentData": "document_invalid_data", "invalidDataFormat": "InvalidModelState", "quoteAlreadyLinked": "invoice_already_linked_to_project", "quoteNonExistent": "invoice_does_not_exist"}, "reason": {"invalidTotalAmount": "Total amount could not be empty", "invalidDataFormat": "The model field is required.", "invalidSubTotal": "Sub total could not be empty", "nullInNumber": "The QuoteNumber field is required.", "invalidCustomerId": "The CustomerId field is required.", "invalidExpirationDate": "Expiration date could not be in the past", "quoteAlreadyLinked": "Invoice already linked to project", "quoteNonExistent": "Invoice does not exist"}}}