import axios from "axios";
import {expect} from "@playwright/test";
import {getCurrentDay} from "../base-api";

require('dotenv').config();

const defaultSellerId: string = process.env.SELLER_id;
const defaultPayerId: string = process.env.PAYER_ID;
const defaultPayeeId: string = process.env.PAYEE_ID;
const day: string = getCurrentDay();
const baseUrl: string = process.env.PAY_NOW_BASE_URL;
const paymentRequests = 'paymentRequests';
const transactions = 'transactions';

const configuredHeaders = {
    accept: 'application/json',
    'Content-Type': 'application/json; charset=utf-8',
    'X-Api-Key': process.env.X_API_KEY,
    'CreatedBY': 'AutoTestUser'
};


export interface CreatePaymentRequestDto {
    accountCode?: string;
    externalReferenceNumber?: string;
    drawId?: string;
    amount: number;
    currency?: string;
    manualPaymentMethod?: string;
    paymentDate: string;
    note?: string;
}

/*
Main generic function
*/
export async function sendPayNowApiRequest(method: string, endpoint: string, body: any = null) {
    try {
        const url = `${baseUrl}/${endpoint}`;
        let response;

        switch (method) {
            case 'post':
                response = await axios.post(url, body, {
                    headers: configuredHeaders
                });
                break;
            case 'get':
                response = await axios.get(url, {
                    headers: configuredHeaders
                });
                break;
            case 'put':
                response = await axios.put(url, body, {
                    headers: configuredHeaders,
                });
                break;
            case 'patch':
                response = await axios.patch(url, body, {
                    headers: configuredHeaders,
                });
                break;
            case 'delete':
                response = await axios.delete(url, {
                    headers: configuredHeaders
                });
                break;
        }
        return response;
    } catch (error) {
        return error;
    }
}

/*
Get functions
*/
export async function getRequiredPaymentRequestById(id: string) {
    return await sendPayNowApiRequest('get', `${paymentRequests}/${id}`);
}

export async function getRequiredTransactionById(id: string) {
    return await sendPayNowApiRequest(`get`, `${transactions}/${id}`);
}

export async function getPaymentTransactionHistory(id: string) {
    return await sendPayNowApiRequest(`get`, `${transactions}/${id}/payment-transaction-history`);
}

export async function getPaymentRequestExist(payableId: string) {
    return await sendPayNowApiRequest(`get`, `${paymentRequests}/payable/${payableId}/is-payment-request-exist`);
}

export async function getRequiredPaymentRequestByDay(day: string) {
    const sellerId = process.env.SELLER_ID;
    const response = await sendPayNowApiRequest(`get`, `${paymentRequests}?From=${day}&To=${day}&SellerId=${sellerId}`);
    return await response.data.result;
}

export async function getPaymentRequests(day: string, flowTemplateCode: string) {
    const response = await sendPayNowApiRequest(`get`, `${paymentRequests}?From=${day}&FlowTemplateCodes=${flowTemplateCode}`);
    return await response.data.result;
}

export async function getPaymentRequestTransactions(id: string) {
    return await sendPayNowApiRequest(`get`, `${paymentRequests}/${id}/payment-request-execution-history`);
}

export async function updatedTransactionsInfo(id: string) {
    const response = await getRequiredPaymentRequestById(id);
    expect(response.status).toEqual(200);
    return await response.data.transactions;
}

export async function isOperationExist(invoiceId: string) {
    return await sendPayNowApiRequest(`get`, `operations/invoice/${invoiceId}/is-operation-exist`);
}

export async function generateTabapayReport(invoiceId: string) {
    return await sendPayNowApiRequest(`get`, `$tests/generate-tabapay-report/invoice/${invoiceId}`);
}

/*
Patch functions
*/

export async function confirmPaymentRequest(id: string) {
    return await sendPayNowApiRequest(`patch`, `${paymentRequests}/${id}/approve`);
}

/*
Post functions
*/

export async function createManualPaymentRequestMessage(payload: CreatePaymentRequestDto) {
    return await sendPayNowApiRequest(`post`, `${paymentRequests}/message`, payload);
}

export async function createPaymentRequest(date: string = day,
    payerId: string = defaultPayerId,
    payeeId: string = defaultPayeeId,
    sellerId: string = defaultSellerId,
    customerAccountId: string,
    amount: number,
    invoiceID: string) {
    const body = {
        "subjectType": "Payable",
        "requestType": "InvoicePayment",
        "paymentMethod": "Card",
        "flowTemplateCode": "CREATE.PAYNOW.INVOICE_PAYMENT",
        "payerId": payerId,
        "payeeId": payeeId,
        "sellerId": sellerId,
        "customerAccountId": customerAccountId,
        "creditId": "3fa85f64-5717-4562-b3fc-2c963f66afa6",
        "amount": amount,
        "feeAmount": 0,
        "currency": "USD",
        "date": date,
        "paymentRequestPayables": [
            {
                "id": invoiceID,
                "payableAmount": amount,
                "requestedAmount": amount,
                "payableType": "Invoice",
                "discount": 0
            }
        ],
        "paymentRequestFees": [
            {
                "amount": 1,
                "type": "Purchaser",
                "companyId": "3fa85f64-5717-4562-b3fc-2c963f66afa6"
            }
        ]
    };
    return await sendPayNowApiRequest('post', paymentRequests, body);
}

export async function executeTransaction(paymentRequestId: string, transactionId: string) {
    let response;
    const url = `${baseUrl}/${paymentRequests}/${paymentRequestId}/${transactions}/${transactionId}/execute`;
    const localHeaders = {
        accept: 'application/json',
        'Content-Type': 'application/json; charset=utf-8',
        'X-Api-Key': process.env.X_API_KEY,
        'updatedBy': 'autotest'
    };

    try {
        response = await axios.post(url, {
            headers: localHeaders
        });
        return response;
    } catch (error) {
        return error;
    }
}

export async function createTransactionForPaymentRequest(paymentRequestId: string,
    generatedTransactionNumber: string,
    currentDate: string,
    amount: number) {
    let response;
    const url = `${baseUrl}/${paymentRequests}/${paymentRequestId}/${transactions}`;
    const localHeaders = {
        accept: 'application/json',
        'Content-Type': 'application/json; charset=utf-8',
        'X-Api-Key': process.env.X_API_KEY,
        'createdBy': 'autotest'
    };
    const localBody = {
        "transactionType": "Transfer",
        "paymentMethod": "Card",
        "originatorAccountId": "autotest",
        "receiverAccountId": "autotest",
        "amount": amount,
        "discount": 0,
        "currency": "USD",
        "date": currentDate,
        "transactionNumber": generatedTransactionNumber,
        "referenceNumber": generatedTransactionNumber,
        "metaData": "autotest",
        "reason": "autotest"
    };

    try {
        response = await axios.post(url, localBody, {
            headers: localHeaders
        });
        return response;
    } catch (error) {
        return error;
    }
}

/*
Delete functions
*/

export async function cancelPaymentRequest(paymentRequestId: string, userID: string) {
    let response;
    const url = `${baseUrl}/${paymentRequests}/${paymentRequestId}`;
    const localHeaders = {
        accept: 'application/json',
        'Content-Type': 'application/json; charset=utf-8',
        'X-Api-Key': process.env.X_API_KEY,
        'Userid': userID
    };

    try {
        response = await axios.delete(url, {
            headers: localHeaders
        });
        return response;
    } catch (error) {
        return error;
    }
}

export async function cancelTransactionRequest(transactionRequestId: string, userID: string) {
    let response;
    const url = `${baseUrl}/${transactions}/${transactionRequestId}`;
    const localHeaders = {
        accept: 'application/json',
        'Content-Type': 'application/json; charset=utf-8',
        'X-Api-Key': process.env.X_API_KEY,
        'Userid': userID
    };

    try {
        response = await axios.delete(url, {
            headers: localHeaders
        });
        return response;
    } catch (error) {
        return error;
    }
}

/*
Util functions
*/
export async function findRequiredPaymentRequest(payments, amount: number) {
    let result;
    for await (const obj of payments) {
        if (await obj.amount === amount) {
            result = await obj;
        }
    }
    return result;
}
