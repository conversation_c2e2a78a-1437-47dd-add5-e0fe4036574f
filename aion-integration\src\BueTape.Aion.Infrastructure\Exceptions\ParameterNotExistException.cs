﻿using BlueTape.Common.ExceptionHandling.Exceptions.Base;
using System.Net;
using System.Runtime.Serialization;

namespace BueTape.Aion.Infrastructure.Exceptions;

[Serializable]
public class ParameterNotExistException : DomainException
{
    public ParameterNotExistException(string parameterName, HttpStatusCode statusCode = HttpStatusCode.BadRequest) : base(BuildErrorMessage(parameterName), statusCode)
    {
    }

    protected ParameterNotExistException(SerializationInfo info, StreamingContext context) : base(info, context)
    {
    }

    private static string BuildErrorMessage(string parameterName)
    {
        return $"Parameter: {parameterName} not exist";
    }

    public override string Code => ErrorCodes.ParameterNotExist;
}