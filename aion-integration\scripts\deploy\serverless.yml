service: dotnet-aion-service
useDotenv: true
frameworkVersion: ^3.9.0

plugins:
  - serverless-dotenv-plugin
  - serverless-offline
  - serverless-plugin-warmup
  - serverless-prune-plugin
  - serverless-plugin-lambda-insights
package:
  individually: true

custom: ${file(_custom.yml):global}

provider:
  name: aws
  runtime: dotnet6
  region: us-west-1
  timeout: 30
  stage: ${env:STAGE, 'dev'}
  tracing:
    apiGateway: true
    lambda: true
  apiGateway:
    shouldStartNameWithService: true
    binaryMediaTypes:
      - "multipart/form-data"
  deploymentBucket:
    name: ${self:custom.aionService.deploymentBucket.${env:STAGE}}
  iam:
    role: ${self:custom.aionService.role.${env:STAGE}}
  vpc:
    securityGroupIds: ${self:custom.aionService.vpcGroup.${env:STAGE}}
    subnetIds: ${self:custom.aionService.vpcSubnet.${env:STAGE}}
  environment:
    LP_AWS_ACCOUNT: "${env:LP_AWS_ACCOUNT}"
    ASPNETCORE_ENVIRONMENT: "${env:ASPNETCORE_ENVIRONMENT}"
    LOGZIO_TOKEN: ${env:LOGZIO_TOKEN}
    PROJECT_NAME: AionService
    Branch:  "${env:BRANCH}"
    
functions:
  api:
    name: aion-api-${env:STAGE}
    lambdaInsights: true #enables Lambda Insights for this function
    handler: BlueTape.Aion.API
    memorySize: 1024
    package:
      artifact: ${env:AION_API_LAMBDA_PACKAGE_LOCATION}
    events:
      - http:
          path: /{proxy+}
          method: ANY
          cors: true
    description: "API for handling aion data"
    
  AionReport:
    name: AionReport-${env:STAGE}
    lambdaInsights: true #enables Lambda Insights for this function
    handler: BlueTape.Reports.Lambda::BlueTape.Reports.Lambda.Function::Handler
    reservedConcurrency: 1
    events:
      - schedule:
          rate: ${self:custom.transactionStatusReportScheduleRate.${env:STAGE}}
          enabled: ${self:custom.scheduleEnabled.${env:STAGE}}
          input:
            JobName: Transaction status report job
            Region: us-west-1
            ReportType: AionTransaction
    package:
      artifact: ${env:LAMBDA_AION_REPORT_PACKAGE_LOCATION}

resources:
  Resources:
    GatewayResponseDefault4XX:
      Type: 'AWS::ApiGateway::GatewayResponse'
      Properties:
        ResponseParameters:
          gatewayresponse.header.Access-Control-Allow-Origin: "'*'"
          gatewayresponse.header.Access-Control-Allow-Headers: "'Content-Type,X-Amz-Date,Authorization,X-Api-Key,X-Amz-Security-Token,X-Amz-User-Agent,session,challenge'"
        ResponseType: DEFAULT_4XX
        RestApiId:
          Ref: 'ApiGatewayRestApi'
    GatewayResponseDefault5XX:
      Type: 'AWS::ApiGateway::GatewayResponse'
      Properties:
        ResponseParameters:
          gatewayresponse.header.Access-Control-Allow-Origin: "'*'"
          gatewayresponse.header.Access-Control-Allow-Headers: "'Content-Type,X-Amz-Date,Authorization,X-Api-Key,X-Amz-Security-Token,X-Amz-User-Agent,session,challenge'"
        ResponseType: DEFAULT_5XX
        RestApiId:
          Ref: 'ApiGatewayRestApi'
    
    AionACHErrorsTopic:
      Type: AWS::SNS::Topic
      Properties:
        TopicName: aion-error-notification-${env:STAGE}     