﻿using BlueTape.Document.DataAccess.EF.Abstractions;
using BlueTape.Document.DataAccess.EF.Constants;
using BlueTape.Document.DataAccess.EF.Repositories;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;

namespace BlueTape.Document.DataAccess.EF.DI
{
    public static class DependencyRegister
    {
        public static void AddEFDataAccessDependencies(this IServiceCollection services, IConfiguration config)
        {
            services.AddDbContext<DatabaseContext>(context =>
            {
                if (Environment.GetEnvironmentVariable(DIConstants.Environment) is DIConstants.Development)
                {
                    context.UseNpgsql(config.GetConnectionString(DIConstants.DefaultConnection));
                }
                else
                {
                    context.UseNpgsql(config.GetSection(DIConstants.AzureConnectionString).Value);
                }
            });

            services.AddTransient<IEfAccountRepository, EfAccountRepository>();
            services.AddTransient<IEfAccountStatusHistoryRepository, EfAccountStatusHistoryRepository>();
            services.AddTransient<IEfDocumentRepository, EfDocumentRepository>();
            services.AddTransient<IEfCompanyRepository, EfCompanyRepository>();
            services.AddTransient<IEfCompanyNoteRepository, EfCompanyNoteRepository>();
            services.AddTransient<IEfDocumentRepository, EfDocumentRepository>();
            services.AddTransient<IEfDocumentTemplateRepository, EfEfDocumentTemplateRepository>();
            services.AddTransient<IEfCashFlowRepository, EfCashFlowRepository>();
            services.AddTransient<IEfCashFlowItemRepository, EfCashFlowItemRepository>();
            services.AddTransient<IEfDocumentApprovalRepository, EfDocumentApprovalRepository>();
            services.AddTransient<IEfBankAccountRepository, EfBankAccountRepository>();
            services.AddTransient<IEfSequencesRepository, EfSequencesRepository>();

            services.AddTransient(typeof(IEfGenericRepository<>), typeof(EfGenericRepository<>));
        }
    }
}