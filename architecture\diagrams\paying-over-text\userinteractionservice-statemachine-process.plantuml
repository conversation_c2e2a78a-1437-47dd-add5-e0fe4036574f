@startuml UserInteractionService State Machine
title UserInteractionService State Machine (sequence diagram)

skinparam roundcorner 20
skinparam ComponentBorderThickness 2

component "pot.request" as request #SkyBlue
component "pot.confirm" as confirm #SkyBlue
component "pot.selectInvoice" as selectInv #SkyBlue
component "pot.select\npayment method" as selectPM #SkyBlue
component "pot.help" as help #LightGoldenRodYellow
component "pot.fail" as fail #IndianRed
component "pot.succeed" as succeed #LightGreen

request -d-> confirm
confirm .r.> selectInv
selectInv .l.> confirm
confirm .l.> selectPM
selectPM .r.> confirm
confirm -d-> succeed
confirm -d-> fail
fail -d-> help

@enduml