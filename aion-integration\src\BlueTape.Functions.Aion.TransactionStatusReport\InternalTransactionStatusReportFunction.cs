using BlueTape.Aion.Application.Abstractions;
using BlueTape.Aion.Application.Abstractions.MessageSenders;
using BlueTape.Aion.Domain.Constants;
using BlueTape.Aion.Domain.Extensions;
using BlueTape.Common.ExceptionHandling.Exceptions.Base;
using BlueTape.Common.ExceptionHandling.Extensions;
using BlueTape.Common.Extensions.Abstractions;
using BlueTape.Integrations.Aion.Infrastructure.Enums;
using BlueTape.ServiceBusMessaging.Attributes;
using BueTape.Aion.Infrastructure.ServiceBusMessages;
using Microsoft.Azure.Functions.Worker;
using Microsoft.Extensions.Logging;
using Serilog.Context;
using System.Diagnostics.CodeAnalysis;

namespace BlueTape.Functions.Aion.TransactionStatusReport;

[ExcludeFromCodeCoverage]
public class InternalTransactionStatusReportFunction(
    ITraceIdAccessor traceIdAccessor,
    IErrorNotificationService notificationService,
    ILogger<InternalTransactionStatusReportFunction> logger,
    IAionInternalTransactionMessageSender aionInternalTransactionMessageSender)
{
    private const string BlueTapeCorrelationId = "BlueTapeCorrelationId";

    [Function("InternalTransactionStatusReportFunction")]
    public async Task Run([TimerTrigger($"%{AionFunctionConstants.AionInternalTransactionJobPeriod}%")] TimerInfo myTimer, CancellationToken ct)
    {
        traceIdAccessor.TraceId = $"{Guid.NewGuid()}-{nameof(InternalTransactionStatusReportFunction)}";

        using (GlobalLogContext.PushProperty("Method", "Scheduler"))
        using (GlobalLogContext.PushProperty(BlueTapeCorrelationId, traceIdAccessor.TraceId))
        {
            try
            {
                logger.LogInformation("Start InternalTransactionStatusReportFunction status report");
                var environment = Environment.GetEnvironmentVariable(EnvironmentConstants.Environment);

                if (environment?.IsEnvironmentDevOrBeta() ?? false)
                {
                    logger.LogInformation("InternalTransactionStatusReportFunction status report function execution is suppresed due to environment: {environment}", environment);
                    return;
                }

                var body = new AionInternalTransactionMessage();
                var paymentSubscriptions = Enum.GetValues<PaymentSubscriptionType>();
                foreach (var paymentSubscription in paymentSubscriptions)
                {
                    body.AionInternalTransactionReportRequest.InternalTransactionReportRequests.Add(new()
                    {
                        InternalTransactionPage = 1,
                        PaymentSubscriptionType = paymentSubscription
                    });
                }

                await aionInternalTransactionMessageSender.SendMessage(new ServiceBusMessageBt<AionInternalTransactionMessage>(body, new ServiceBusMessageAttributes()
                {
                    CorrelationId = traceIdAccessor.TraceId,
                    MessageId = traceIdAccessor.TraceId
                }), ct);
            }
            catch (DomainException domainEx)
            {
                await notificationService.Notify(domainEx.GetSlackEventMessageBody(EnvironmentExtensions.GetExecutionEnvironment(), "AionService"), ct);
                logger.LogError(domainEx, "InternalTransactionStatusReportFunction status scheduler function failed: {message}", domainEx.Message);
                throw;
            }
            catch (Exception ex)
            {
                await notificationService.Notify(ex.GetSlackEventMessageBody(EnvironmentExtensions.GetExecutionEnvironment(), "AionService", maxMessageLength: 400), ct);
                logger.LogError(ex, "InternalTransactionStatusReportFunction status scheduler function failed: {message}", ex.Message);
                throw;
            }
        }
    }
}
