﻿using AutoMapper;
using BlueTape.Aion.Application.Abstractions;
using BlueTape.Aion.Application.Constants;
using BlueTape.Aion.Application.Extensions;
using BlueTape.Aion.Application.Models.Ach.Pull;
using BlueTape.Aion.Application.Models.Ach.Pull.Response;
using BlueTape.Aion.DataAccess.External.Abstractions;
using BlueTape.Aion.DataAccess.External.Models.Accounts;
using BlueTape.Aion.DataAccess.External.Models.AchTransfer;
using BlueTape.Aion.DataAccess.External.Models.CreateAchTransfer;
using BlueTape.Aion.DataAccess.External.Models.CreateAchTransfer.Response;
using BlueTape.Aion.DataAccess.External.Models.InternalTransfer;
using BlueTape.Aion.DataAccess.External.Models.Transactions;
using BlueTape.Integrations.Aion.Accounts;
using BlueTape.Integrations.Aion.Infrastructure.Enums;
using BlueTape.Integrations.Aion.Transactions;
using BlueTape.Utilities.Extensions;
using BlueTape.Utilities.Models;
using BlueTape.Utilities.Providers;
using BueTape.Aion.Infrastructure.Exceptions;
using BueTape.Aion.Infrastructure.Extensions;
using BueTape.Aion.Infrastructure.Options;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Options;
using System.Diagnostics.CodeAnalysis;
using System.Text;
using BlueTape.Aion.Application.Models;
using BlueTape.Aion.Application.Models.Instant;
using BlueTape.Aion.DataAccess.External.Models.InstantTransfer.Request;
using BlueTape.Aion.DataAccess.External.Models.InstantTransfer.Response;
using BlueTape.Aion.DataAccess.External.Models.WireTransfer.Requests;
using BlueTape.Aion.DataAccess.MongoDB.Abstractions;
using BlueTape.Aion.Domain.DTOs.BankAccount;
using BlueTape.Aion.Domain.DTOs.Company;
using BlueTape.Integrations.Aion;
using BlueTape.Integrations.Aion.Ach.CreateWireTransfer.Response;
using TransactionListObj = BlueTape.Aion.DataAccess.External.Models.Transactions.TransactionListObj;

namespace BlueTape.Aion.Application.Service;

public class AchService : IAchService
{
    private readonly ICompanyService _companyService;
    private readonly IBankAccountService _bankAccountService;
    private readonly IAionHttpClient _aionHttpClient;
    private readonly ITransactionService _transactionService;
    private readonly IMapper _mapper;
    private readonly IConfiguration _configuration;
    private readonly AionInternalTransferOptions _internalTransferOptions;
    private readonly IDateProvider _dateProvider;
    private readonly IAionLoggingService _aionLoggingService;
    private readonly int PageSize = 100;

    public AchService(
        ICompanyService companyService,
        IBankAccountService bankAccountService,
        IAionHttpClient aionHttpClient,
        ITransactionService transactionService,
        IMapper mapper,
        IConfiguration configuration,
        IDateProvider dateProvider,
        IAionLoggingService aionLoggingService,
        IOptions<AionInternalTransferOptions> options)
    {
        _companyService = companyService;
        _bankAccountService = bankAccountService;
        _aionHttpClient = aionHttpClient;
        _transactionService = transactionService;
        _mapper = mapper;
        _configuration = configuration;
        _dateProvider = dateProvider;
        _aionLoggingService = aionLoggingService;
        _internalTransferOptions = options.Value;

        var tempPageSize = _configuration[AionConstants.AionReportPageSize];
        var isParse = int.TryParse(tempPageSize, out var parsedSize);

        PageSize = isParse ? parsedSize : 100;
    }

    public async Task<decimal> GetAvailableBalance(AccountCodeType accountCodeType, string paymentSubscriptionType, CancellationToken ctx)
    {
        _internalTransferOptions.AccountIdSecretNames
            .TryGetValue(accountCodeType, out var accountIdSecret);

        if (string.IsNullOrEmpty(accountIdSecret)) throw new AionAccountDoesNotExistException(accountCodeType.ToString());

        var accountId = _configuration[accountIdSecret] ?? throw new AionAccountDoesNotExistException(accountIdSecret);

        var accounts = await _aionHttpClient.GetAccounts(paymentSubscriptionType.ParseToPaymentSubscription(), ctx);

        var targetAccount = accounts.BankAccounts
            .FirstOrDefault(x => x.AccountId.Equals(accountId));

        if (targetAccount is null) throw new BankAccountDoesNotExistException(accountCodeType.ToString());

        return targetAccount.AvailableBalance;
    }

    public async Task<BlueTapeTransactionResponseModel> StartAionTransactionAsync(CreateAch createAch, TransactionType transactionType, string paymentSubscriptionType,
        AionPaymentMethodType aionPaymentMethodType, CancellationToken ctx)
    {
        switch (aionPaymentMethodType)
        {
            case AionPaymentMethodType.ACH:
                var achResult = await CreateAchAsync(createAch, transactionType, paymentSubscriptionType, ctx);
                return new BlueTapeTransactionResponseModel()
                {
                    AionReferenceId = achResult.AchObj.ReferenceId,
                    AionFee = achResult.AchObj.FeeAmount,
                    AionResponse = _aionLoggingService.GetMaskedJson(achResult)
                };
            case AionPaymentMethodType.WIRE:
                var wireResult = await CreateWireAsync(createAch, paymentSubscriptionType, ctx);
                return new BlueTapeTransactionResponseModel()
                {
                    AionReferenceId = wireResult.WireTransferObj.ReferenceId,
                    AionFee = wireResult.WireTransferObj.FeeAmount,
                    AionResponse = _aionLoggingService.GetMaskedJson(wireResult)
                };
            case AionPaymentMethodType.INSTANT:
                var instantResult = await CreateInstantAsync(createAch, paymentSubscriptionType, ctx);
                return new BlueTapeTransactionResponseModel()
                {
                    AionReferenceId = (instantResult.InstantTransfer?.RtpPaymentInfo?.ReferenceId ?? instantResult.InstantTransfer!.PaymentId)!,
                    AionFee = instantResult.InstantTransfer.FeeAmount,
                    AionResponse = _aionLoggingService.GetMaskedJson(instantResult)
                };
            case AionPaymentMethodType.DEFAULT:
            default:
                throw new ArgumentOutOfRangeException(nameof(aionPaymentMethodType), aionPaymentMethodType, null);
        }
    }

    private async Task<TransactionPreProcessingResult> PreProcessTransactionAsync(
        CreateAch createAch,
        string paymentSubscriptionType,
        AionPaymentMethodType aionPaymentMethodType,
        CancellationToken ctx)
    {
        _internalTransferOptions.AccountNumberSecretNames
            .TryGetValue(createAch.OriginatorAccountCode, out var accountNumberSecret);
        _internalTransferOptions.AccountIdSecretNames
            .TryGetValue(createAch.OriginatorAccountCode, out var accountIdSecret);

        if (string.IsNullOrEmpty(accountNumberSecret)) throw new VariableNullException(nameof(accountNumberSecret));
        if (string.IsNullOrEmpty(accountIdSecret)) throw new VariableNullException(nameof(accountIdSecret));

        await _transactionService.EnsureTransactionDoesNotExist(createAch.TransactionId, createAch.TransactionNumber, ctx);

        var accountNumber = _configuration[accountNumberSecret] ?? throw new VariableNullException(nameof(accountNumberSecret));
        var accountId = _configuration[accountIdSecret] ?? throw new VariableNullException(nameof(accountIdSecret));
        var company = await _companyService.SyncCompanyWithAionAsync(createAch.Receiver.CompanyId, paymentSubscriptionType, ctx);

        if (company.BankAccounts is null || !company.BankAccounts.Contains(createAch.Receiver.BankAccountId))
            throw new BankAccountDoesNotExistException(createAch.Receiver.BankAccountId);

        var bankAccount = await _bankAccountService
            .SynBankAccountWithAionAsync(createAch.Receiver.BankAccountId, company, paymentSubscriptionType, aionPaymentMethodType,  ctx);

        return new TransactionPreProcessingResult()
        {
            AccountNumber = accountNumber,
            AccountId = accountId,
            Company = company,
            BankAccount = bankAccount
        };
    }

    public async Task<CreateAchResponseModel> CreateAchAsync(
        CreateAch createAch,
        TransactionType transactionType,
        string paymentSubscriptionType,
        CancellationToken ctx)
    {
        var preProcessingResult = await PreProcessTransactionAsync(createAch, paymentSubscriptionType, AionPaymentMethodType.ACH, ctx);
        
        var paymentSubscription = paymentSubscriptionType.ParseToPaymentSubscription();
        
        var response = await _aionHttpClient.CreateAchTransfer(new CreateAchTransferRequest()
        {
            AchObj = new AchObjectRequest
            {
                AccountId = preProcessingResult.AccountId,
                Addenda = createAch.Addenda,
                Amount = createAch.Amount.RoundDecimalToString(),
                Description = createAch.Description,
                AccountNumber = preProcessingResult.AccountNumber,
                CounterpartyId = preProcessingResult.Company.AionSettings!.GetCounterPartyId(paymentSubscription),
                CounterpartyName = preProcessingResult.Company.LegalName ?? string.Empty,
                SecCode = preProcessingResult.Company.IsBusiness ? AionConstants.AionBusinessSecCode : AionConstants.AionIndividualSecCode,
                SendEmail = false,
                ServiceType = createAch.SameDayAch ? AionConstants.AchServiceTypeSameDay : AionConstants.AchServiceTypeStandard,
                TransactionType = transactionType.ToString(),
                TransferMethodId = preProcessingResult.BankAccount.AionSettings!.GetTransferMethodId(paymentSubscription, AionPaymentMethodType.ACH)!,
                ContextIdentifier = createAch.TransactionNumber
            }
        }, paymentSubscription, ctx);

        await _transactionService.SaveAionCreatedAchTransactionAsync(response.AchObj, ctx);

        return _mapper.Map<CreateAchResponseModel>(response);
    }
    
    public async Task<CreateWireTransferResponseModel> CreateWireAsync(
        CreateAch createAch,
        string paymentSubscriptionType,
        CancellationToken ctx)
    {
        var preProcessingResult = await PreProcessTransactionAsync(createAch, paymentSubscriptionType, AionPaymentMethodType.WIRE, ctx);
        
        var paymentSubscription = paymentSubscriptionType.ParseToPaymentSubscription();
        var response = await _aionHttpClient.CreateWireTransfer(
            new CreateWireTransferRequest
            {
                WireObj = new WireObjRequest
                {
                    AccountId = preProcessingResult.AccountId,
                    Amount = createAch.Amount.RoundDecimalToString(),
                    AccountNumber = preProcessingResult.AccountNumber,
                    CounterpartyId = preProcessingResult.Company.AionSettings!.GetCounterPartyId(paymentSubscription),
                    TransferMethodId = preProcessingResult.BankAccount.AionSettings!.GetTransferMethodId(paymentSubscription, AionPaymentMethodType.WIRE)!,
                    ContextIdentifier = createAch.TransactionNumber,
                    RemittanceInfo = string.Join(" ", createAch.Addenda),
                    Purpose = createAch.Description,
                }
            }
            , paymentSubscription, ctx);

        await _transactionService.SaveAionCreatedWireTransactionAsync(response.WireTransferObj, ctx);
        
        return _mapper.Map<CreateWireTransferResponseModel>(response);
    }
    
    public async Task<CreateInstantTransferResponseModel> CreateInstantAsync(
        CreateAch createAch,
        string paymentSubscriptionType,
        CancellationToken ctx)
    {
        var preProcessingResult = await PreProcessTransactionAsync(createAch, paymentSubscriptionType, AionPaymentMethodType.INSTANT, ctx);
        
        var paymentSubscription = paymentSubscriptionType.ParseToPaymentSubscription();
        var response = await _aionHttpClient.CreateInstantTransfer(
            new CreateInstantTransferRequest()
            {
                Amount = createAch.Amount.RoundDecimalToString(),
                FromAccountNumber = preProcessingResult.AccountNumber,
                TransferMethodId = preProcessingResult.BankAccount.AionSettings!.GetTransferMethodId(paymentSubscription, AionPaymentMethodType.INSTANT)!,
                ContextIdentifier = createAch.TransactionNumber,
                Purpose = createAch.Description,
            }
            , paymentSubscription, ctx);

        await _transactionService.SaveAionCreatedInstantTransactionAsync(response.InstantTransfer, ctx);
        
        return _mapper.Map<CreateInstantTransferResponseModel>(response);
    }
    
    [ExcludeFromCodeCoverage]
    public async Task<List<AchResponseObj>> GetAllAchTransactionsAsync(int forLastDaysNumber, string paymentSubscriptionType, CancellationToken ctx)
    {
        var achList = new List<AchResponseObj>();

        var startingDate = DateTime.UtcNow;
        var fromDate = startingDate.AddDays(-forLastDaysNumber);

        var formattedCurrentDate = startingDate.ToString("yyyy-MM-dd");
        var formattedSevenDaysAgo = fromDate.ToString("yyyy-MM-dd");

        var request = new GetACHTransfersRequest
        {
            Page = 0,
            Size = PageSize,
            FromDate = formattedSevenDaysAgo,
            ToDate = formattedCurrentDate
        };

        var paymentSubscription = paymentSubscriptionType.ParseToPaymentSubscription();
        var transactions = await _aionHttpClient.GetACHTransfers(request, paymentSubscription, ctx);
        achList.AddRange(transactions.AchList);

        while (transactions.NumPages > ++transactions.PageNumber)
        {
            request.Page++;
            transactions = await _aionHttpClient.GetACHTransfers(request, paymentSubscription, ctx);
            achList.AddRange(transactions.AchList);
        }

        return achList;
    }

    [ExcludeFromCodeCoverage]
    public async Task<List<AchResponseObj>> GetAllAchReturnTransactionsAsync(int forLastDaysNumber, string paymentSubscriptionType, CancellationToken ctx)
    {
        var achList = new List<AchResponseObj>();

        var startingDate = DateTime.UtcNow;
        var fromDate = startingDate.AddDays(-forLastDaysNumber);

        var formattedCurrentDate = startingDate.ToString("yyyy-MM-dd");
        var formattedSevenDaysAgo = fromDate.ToString("yyyy-MM-dd");

        var returnRequest = new GetACHTransfersReturnsRequest()
        {
            Page = 0,
            Size = PageSize,
            FromDate = formattedSevenDaysAgo,
            ToDate = formattedCurrentDate
        };
        var paymentSubscription = paymentSubscriptionType.ParseToPaymentSubscription();

        var transactionsReturn = await _aionHttpClient.GetACHTransferReturns(returnRequest, paymentSubscription, ctx);
        achList.AddRange(transactionsReturn.AchList);

        while (transactionsReturn.NumPages > ++transactionsReturn.PageNumber)
        {
            returnRequest.Page++;
            transactionsReturn = await _aionHttpClient.GetACHTransferReturns(returnRequest, paymentSubscription, ctx);
            achList.AddRange(transactionsReturn.AchList);
        }

        return achList;
    }

    [ExcludeFromCodeCoverage]
    public async Task<List<BookTransferObj>> GetAllInternalTransactionsAsync(int forLastDaysNumber, string paymentSubscriptionType, CancellationToken ctx)
    {
        var achList = new List<BookTransferObj>();

        var startingDate = DateTime.UtcNow;
        var fromDate = startingDate.AddDays(-forLastDaysNumber);

        var formattedCurrentDate = startingDate.ToString("yyyy-MM-dd");
        var formattedSevenDaysAgo = fromDate.ToString("yyyy-MM-dd");

        var request = new GetACHTransfersRequest
        {
            Page = 0,
            Size = PageSize,
            FromDate = formattedSevenDaysAgo,
            ToDate = formattedCurrentDate
        };
        var paymentSubscription = paymentSubscriptionType.ParseToPaymentSubscription();

        var transactions = await _aionHttpClient.GetInternalTransfers(request, paymentSubscription, ctx);
        achList.AddRange(transactions.AchList);

        while (transactions.NumPages > ++transactions.PageNumber)
        {
            request.Page++;
            transactions = await _aionHttpClient.GetInternalTransfers(request, paymentSubscription, ctx);
            achList.AddRange(transactions.AchList);
        }

        return achList;
    }

    [ExcludeFromCodeCoverage]
    public async Task<List<TransactionListObj>> GetAllTransactionsAsync(string accountId, DateTime? fromDate, DateTime? toDate, string paymentSubscriptionType, CancellationToken ctx)
    {
        var achList = new List<TransactionListObj>();

        var size = 200000;
        var formattedFromDate = fromDate?.AddDays(-1).ToString("yyyy-MM-dd") ?? string.Empty;
        var formattedToDate = toDate?.AddDays(-1).ToString("yyyy-MM-dd") ?? null;

        var request = new GetTransactionsRequest()
        {
            Page = 0,
            Size = size,
            FromDate = formattedFromDate,
            ToDate = formattedToDate,
            AccountId = accountId
        };
        var paymentSubscription = paymentSubscriptionType.ParseToPaymentSubscription();

        var transactions = await _aionHttpClient.GetTransactions(request, paymentSubscription, ctx);
        achList.AddRange(transactions.TransactionsList);

        while (transactions.NumPages > ++transactions.PageNumber)
        {
            request.Page++;
            transactions = await _aionHttpClient.GetTransactions(request, paymentSubscription, ctx);
            achList.AddRange(transactions.TransactionsList);
        }

        return achList;
    }

    [ExcludeFromCodeCoverage]
    public async Task<PaginatedResponse<TransactionListObj>> GetAllTransactionsAsync(TransactionsQuery query, CancellationToken ctx)
    {
        _internalTransferOptions.AccountIdSecretNames
            .TryGetValue(query.AccountCodeType, out var accountIdSecret);

        if (string.IsNullOrEmpty(accountIdSecret)) throw new VariableNullException(nameof(accountIdSecret));

        var accountId = _configuration[accountIdSecret] ?? throw new VariableNullException(nameof(accountIdSecret));

        var request = new GetTransactionsRequest()
        {
            Page = query.PageNumber - 1,
            Size = query.PageSize,
            AccountId = accountId
        };

        var paymentSubscription = AionSettingsExtensions.GetSubscriptionByAccountCode(query.AccountCodeType) ?? throw new VariableNullException(nameof(query.AccountCodeType));
        var result = await _aionHttpClient.GetTransactions(request, paymentSubscription, ctx);

        // Add processing wire transfers
        var wire = await _aionHttpClient.GetWireTransfers(
            new BaseGetACHTransfers() { Page = request.Page, Size = PageSize, FromDate = DateTime.Now.AddDays(-2).ToString("yyyy-MM-dd") },
            paymentSubscription,
            ctx);
        result.TransactionsList = AddProcessingWireTransfers(result.TransactionsList, wire?.WireList, accountId);

        return new PaginatedResponse<TransactionListObj>()
        {
            Limit = query.PageSize,
            Offset = query.PageNumber,
            Total = result.NumPages,
            Count = result.Count,
            Result = result.TransactionsList
        };
    }

    [ExcludeFromCodeCoverage]
    private List<TransactionListObj> AddProcessingWireTransfers(
        List<TransactionListObj> transactions,
        IEnumerable<WireTransferObjItem>? wireTransfers,
        string accountId)
    {
        if (wireTransfers == null || !wireTransfers.Any())
            return transactions;

        var existingWireIds = transactions
            .Where(x => !string.IsNullOrEmpty(x.WireId))
            .Select(x => x.WireId)
            .ToHashSet();

        var processingWires = wireTransfers
            .Where(x => x.AccountId == accountId && !string.IsNullOrEmpty(x.Id) && !existingWireIds.Contains(x.Id))
            .ToList();

        var wireTransactions = new List<TransactionListObj>();

        foreach (var processingWire in processingWires)
        {
            decimal parsedAmount = 0;
            var amountStr = processingWire.Amount;

            if (decimal.TryParse(processingWire.Amount, out var amount))
            {
                parsedAmount = amount;
                if (processingWire.Direction?.Equals("Outbound", StringComparison.OrdinalIgnoreCase) == true)
                {
                    parsedAmount = -parsedAmount;
                    if (!amountStr.StartsWith("-"))
                        amountStr = $"-{amountStr}";
                }
            }

            var wireTransaction = new TransactionListObj
            {
                Id = processingWire.Id,
                WireId = processingWire.Id,
                Amount = parsedAmount,
                AmountStr = amountStr,
                TxnDate = processingWire.CreatedAt.GetValueOrDefault(),
                DisplayDescription = $"{processingWire.Direction} {processingWire.Originator.Name} {processingWire.ReferenceId}",
            };

            wireTransactions.Add(wireTransaction);
        }

        transactions.AddRange(wireTransactions);

        return transactions.OrderByDescending(t => t.TxnDate).ToList();
    }

    [ExcludeFromCodeCoverage]
    public async Task<(List<AchResponseObj>, int?)> GetAchTransactionsByPageAsync(int page, int forLastDaysNumber, string paymentSubscriptionType, CancellationToken ctx)
    {
        var startingDate = DateTime.UtcNow;
        var fromDate = startingDate.AddBusinessDays(-forLastDaysNumber);

        var formattedCurrentDate = startingDate.ToString("yyyy-MM-dd");
        var formattedSevenDaysAgo = fromDate.ToString("yyyy-MM-dd");

        var request = new GetACHTransfersRequest
        {
            Page = page,
            Size = PageSize,
            FromDate = formattedSevenDaysAgo,
            ToDate = formattedCurrentDate
        };
        var paymentSubscription = paymentSubscriptionType.ParseToPaymentSubscription();
        request.Page -= 1;

        var transactions = await _aionHttpClient.GetACHTransfers(request, paymentSubscription, ctx);

        int? nextPage = null;

        if (transactions.NumPages > page)
        {
            nextPage = ++page;
        }

        return (transactions.AchList, nextPage);
    }

    [ExcludeFromCodeCoverage]
    public async Task<(List<AchResponseObj>, int?)> GetAchReturnTransactionsByPageAsync(int page, int forLastDaysNumber, string paymentSubscriptionType, CancellationToken ctx)
    {
        var startingDate = DateTime.UtcNow;
        var fromDate = startingDate.AddBusinessDays(-forLastDaysNumber);

        var formattedSevenDaysAgo = fromDate.ToString("yyyy-MM-dd");

        var request = new GetACHTransfersReturnsRequest
        {
            Page = page,
            Size = PageSize,
            FromDate = formattedSevenDaysAgo,
            //ToDate = formattedCurrentDate // Disabled because looks like filtration on aion side take the beginning of this date for filtration
        };

        request.Page -= 1;
        var paymentSubscription = paymentSubscriptionType.ParseToPaymentSubscription();

        var transactions = await _aionHttpClient.GetACHTransferReturns(request, paymentSubscription, ctx);

        int? nextPage = null;

        if (transactions.NumPages > page)
        {
            nextPage = ++page;
        }

        return (transactions.AchList, nextPage);
    }

    [ExcludeFromCodeCoverage]
    public async Task<(List<BookTransferObj>, int?)> GetAchInternalTransactionsByPageAsync(int page, int forLastDaysNumber, string paymentSubscriptionType, CancellationToken ctx)
    {
        var startingDate = DateTime.UtcNow;
        var fromDate = startingDate.AddBusinessDays(-forLastDaysNumber);

        var formattedCurrentDate = startingDate.ToString("yyyy-MM-dd");
        var formattedSevenDaysAgo = fromDate.ToString("yyyy-MM-dd");

        var request = new GetACHTransfersRequest
        {
            Page = page,
            Size = PageSize,
            FromDate = formattedSevenDaysAgo,
            ToDate = formattedCurrentDate
        };

        request.Page -= 1;
        var paymentSubscription = paymentSubscriptionType.ParseToPaymentSubscription();

        var transactions = await _aionHttpClient.GetInternalTransfers(request, paymentSubscription, ctx);

        int? nextPage = null;

        if (transactions.NumPages > page)
        {
            nextPage = ++page;
        }

        return (transactions.AchList, nextPage);
    }

    [ExcludeFromCodeCoverage]
    public async Task<(List<InstantTransferObjectItemResponse>, int?)> GetInstantTransactionsByPageAsync(int instantPage, int forLastDaysNumber, string paymentSubscriptionType,
        CancellationToken ctx)
    {
        var startingDate = DateTime.UtcNow;
        var fromDate = startingDate.AddBusinessDays(-forLastDaysNumber);

        var formattedCurrentDate = startingDate.ToString("yyyy-MM-dd");
        var formattedSevenDaysAgo = fromDate.ToString("yyyy-MM-dd");

        var request = new GetACHTransfersRequest
        {
            Page = instantPage,
            Size = PageSize,
            FromDate = formattedSevenDaysAgo,
            ToDate = formattedCurrentDate
        };
        var paymentSubscription = paymentSubscriptionType.ParseToPaymentSubscription();
        request.Page -= 1;

        var transactions = await _aionHttpClient.GetInstantTransfers(request, paymentSubscription, ctx);

        int? nextPage = null;

        if (transactions.NumPages > instantPage)
        {
            nextPage = ++instantPage;
        }

        return (transactions.InstantList, nextPage);
    }

    [ExcludeFromCodeCoverage]
    public async Task<(List<WireTransferObjItem>, int?)> GetWireTransactionsByPageAsync(int wirePage, int forLastDaysNumber, string paymentSubscriptionType,
        CancellationToken ctx)
    {
        var startingDate = DateTime.UtcNow;
        var fromDate = startingDate.AddBusinessDays(-forLastDaysNumber);

        var formattedCurrentDate = startingDate.ToString("yyyy-MM-dd");
        var formattedSevenDaysAgo = fromDate.ToString("yyyy-MM-dd");

        var request = new GetACHTransfersRequest
        {
            Page = wirePage,
            Size = PageSize,
            FromDate = formattedSevenDaysAgo,
            ToDate = formattedCurrentDate
        };
        var paymentSubscription = paymentSubscriptionType.ParseToPaymentSubscription();
        request.Page -= 1;

        var transactions = await _aionHttpClient.GetWireTransfers(request, paymentSubscription, ctx);

        int? nextPage = null;

        if (transactions.NumPages > wirePage)
        {
            nextPage = ++wirePage;
        }

        return (transactions.WireList, nextPage);
    }

    [ExcludeFromCodeCoverage]
    public async Task<List<AccountResponseObj>> GetAllAccounts(CancellationToken ctx)
    {
        var accountsSub1Task = _aionHttpClient.GetAccounts(PaymentSubscriptionType.SUBSCRIPTION1, ctx);
        var accountsSub2Task = _aionHttpClient.GetAccounts(PaymentSubscriptionType.SUBSCRIPTION2, ctx);
        var accountsSub3Task = _aionHttpClient.GetAccounts(PaymentSubscriptionType.SUBSCRIPTION3, ctx);

        await Task.WhenAll(accountsSub1Task, accountsSub2Task, accountsSub3Task);

        var accountsSub1 = await accountsSub1Task;
        var accountsSub2 = await accountsSub2Task;
        var accountsSub3 = await accountsSub3Task;

        var accounts = new List<BankAccounts>();
        accounts.AddRange(accountsSub1.BankAccounts);
        accounts.AddRange(accountsSub2.BankAccounts);
        accounts.AddRange(accountsSub3.BankAccounts);

        var result = new List<AccountResponseObj>();

        result.AddRange(accounts.Select(x =>
        {
            var account = new AccountResponseObj();
            account.Name = x.AccountName;
            account.AccountNumber = x.AccountNumber;
            account.AvailableBalance = x.AvailableBalance;
            account.CurrentBalance = x.CurrentBalance;
            account.AmountOnHold = x.CurrentBalance - x.AvailableBalance;
            account.Id = x.AccountId;
            account.PaymentProvider = "aion";
            account.AccountCodeType = GetAccountCodeTypeByAccountId(x.AccountId);
            return account;
        }));

        return result;
    }

    [ExcludeFromCodeCoverage]
    private bool IsCurrentTimeBefore10AmET()
    {
        var currentNyTime = _dateProvider.ConvertUtcToEasternTimeNewYork(DateTime.UtcNow);
        var today10amEastern = currentNyTime.Date.AddHours(10);
        return currentNyTime < today10amEastern;
    }
    
    [ExcludeFromCodeCoverage]
    private AccountCodeType? GetAccountCodeTypeByAccountId(string accountId)
    {
        var keyFromConfig = _configuration.GetChildren()
            .FirstOrDefault(x => x.Value == accountId)
            ?.Key;

        return _internalTransferOptions?.AccountIdSecretNames?
            .FirstOrDefault(x => x.Value == keyFromConfig)
            .Key ?? null;
    }
}
