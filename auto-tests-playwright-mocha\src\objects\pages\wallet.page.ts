import {BasePage} from '../base.page';

const pageLocator = "Bluetape Wallet";

export class WalletPage extends BasePage {
    constructor(page, locator = pageLocator){
        super(page, locator);
    };

    buttons = {
        addPaymentButton: this.page.getByRole('button', { name: '󰐕' }),
        connectYourBankButton: this.page.getByText('Connect your bankFor faster checkout'),
        cardsButton: this.page.getByRole('tab', { name: 'Cards Cards' })
    };

    elements = {
        connectedBanksLocator: this.page.getByText('Connected via Online Banking'),
        bankOfAmerica: this.page.getByText('Bank of America'),
        addedCard: this.page.getByText('Visa - Card********1111').first() //todo proceed after ui implementation
    };
}
