using Azure.Messaging.ServiceBus;
using BlueTape.Aion.Application.Abstractions;
using BlueTape.Aion.Domain.Constants;
using BlueTape.Common.ExceptionHandling.Exceptions.Base;
using BlueTape.Common.ExceptionHandling.Extensions;
using BlueTape.Common.Extensions.Abstractions;
using BueTape.Aion.Infrastructure.ServiceBusMessages;
using Microsoft.Azure.Functions.Worker;
using Microsoft.Extensions.Logging;
using Serilog.Context;
using System.Diagnostics.CodeAnalysis;
using System.Text.Json;
using System.Text.Json.Serialization;

namespace BlueTape.Functions.Aion.TransactionStatusReport;

[ExcludeFromCodeCoverage]
public class InternalTransactionConsumer(
    ILogger<InternalTransactionConsumer> logger,
    IErrorNotificationService notificationService,
    ITraceIdAccessor traceIdAccessor,
    IReportService reportService)
{
    private readonly JsonSerializerOptions _serializerOptions = new(JsonSerializerDefaults.Web)
    {
        Converters = { new JsonStringEnumConverter() },
        PropertyNameCaseInsensitive = true,
        WriteIndented = false
    };

    [Function(nameof(InternalTransactionConsumer))]
    public async Task Run([
        ServiceBusTrigger($"%{AionFunctionConstants.AionInternalTransactionQueueName}%",
                Connection = $"{AionFunctionConstants.AionInternalTransactionQueueConnection}")]
            ServiceBusReceivedMessage message,
            ServiceBusMessageActions messageActions,
            CancellationToken ct)
    {
        traceIdAccessor.TraceId = !string.IsNullOrEmpty(message.CorrelationId)
            ? message.CorrelationId
            : $"{Guid.NewGuid()}-{nameof(InternalTransactionConsumer)}";

        using (GlobalLogContext.PushProperty("functionName", nameof(InternalTransactionConsumer)))
        using (GlobalLogContext.PushProperty("BlueTapeCorrelationId", traceIdAccessor.TraceId))
        {
            logger.LogInformation("Got message from the queue: Session: {sessionId}, \n Message: {messageId}", message.SessionId, message.MessageId);

            try
            {
                var parsedMessage = message.Body.ToObjectFromJson<AionInternalTransactionMessage?>(_serializerOptions);

                if (parsedMessage is null)
                {
                    logger.LogError(
                        "Unable to process message with empty body or invalid fields. Session: {sessionId}, \n Message: {messageId}",
                        message.SessionId, message.MessageId);
                    throw new ArgumentNullException(nameof(message));
                }

                await reportService.RunInternalTransactionStatusReportAsync(
                    parsedMessage.AionInternalTransactionReportRequest,
                    default);

                logger.LogInformation("AionInternalTransactionMessage message was processed successfully");
            }
            catch (DomainException ex)
            {
                await notificationService.Notify(ex.GetSlackEventMessageBody(EnvironmentExtensions.GetExecutionEnvironment(), AionFunctionConstants.ProjectName), ct);
                logger.LogError("A business logic exception occurred while processing the message: {messageId}, \n  Exception: {ex}", message.MessageId, ex);
                await messageActions.DeadLetterMessageAsync(message, cancellationToken: ct);
                throw;
            }
            catch (Exception ex)
            {
                await notificationService.Notify(ex.GetSlackEventMessageBody(EnvironmentExtensions.GetExecutionEnvironment(), AionFunctionConstants.ProjectName), ct);
                logger.LogError("A business logic exception occurred while processing the message: {messageId}, \n  Exception: {ex}", message.MessageId, ex);
                await messageActions.DeadLetterMessageAsync(message, cancellationToken: ct);
                throw;
            }
        }
    }
}
