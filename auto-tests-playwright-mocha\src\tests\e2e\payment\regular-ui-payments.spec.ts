import {BaseTest, test} from '../../test-utils';
import {insertTestReport} from "../../../database/autoTests/AutoTestReportRepository";
import {Action, ITestReport} from "../../../database/autoTests/entities/AutoTestReport";
import {GenericIntegrationService} from "../../../services/genericIntegrationService";
import {paymentService} from "../../../services/paymentService";
import {CustomerService} from "../../../services/customerService";
import {BackOfficeClient} from "../../../api/back-office-decision-engine/backOfficeClient";
import { DrawApprovalsRepository } from '../../../database/drawApplication/drawApprovalsRepository';
import { OperationRepository } from '../../../database/operation/operationRepository';
import { InvoiceRepository } from '../../../database/invoices/invoiceRepository';
import { BackOfficeService } from '../../../services/backOfficeService';
import { SupplierService } from '../../../services/supplierService';
import {
    findLoans
} from '../../../api/common/lms-send-request';

interface EnvConfig {
    baseUrl: string;
    supplier: {
        email: string;
        password: string;
    };
    customer: {
        email: string;
        password: string;
        name: string;
    };
    ihcSupplier: {
        email: string;
        password: string;
    };
    ihcCustomer: {
        email: string;
        password: string;
        name: string;
    };
}

function getEnvConfig(): EnvConfig {
    return {
        baseUrl: process.env.CI_ENVIRONMENT_URL || 'https://beta.bluetape.com/',
        supplier: {
            email: process.env.LOC_USER_EMAIL || '',
            password: process.env.LOC_USER_PASSWORD || '',
        },
        customer: {
            email: process.env.LOC_CUSTOMER_EMAIL || '',
            password: process.env.LOC_CUSTOMER_PASSWORD || '',
            name: process.env.LOC_CUSTOMER_COMPANYNAME || '',
        },
        ihcSupplier: {
            email: process.env.IHC_USER_EMAIL || '',
            password: process.env.IHC_USER_PASSWORD || '',
        },
        ihcCustomer: {
            email: process.env.IHC_CUSTOMER_EMAIL || '',
            password: process.env.IHC_CUSTOMER_PASSWORD || '',
            name: process.env.IHC_CUSTOMER_COMPANYNAME || '',
    },
    };
}

test.use({storageState: {cookies: [], origins: []}});

const testGroup = `@paymentsE2E Create invoice via generic API, and pay via all possible methods.`

test.describe.parallel(testGroup, async () => {
    
    let testGroupId: string
    let resultPerTest: boolean
    let actionsPerTest: Action[]
    let report: ITestReport
    let _genericIntegrationService:GenericIntegrationService
    let _paymentService: paymentService
    let _supplierService: SupplierService
    let _customerService: CustomerService
    let _invoiceRepository: InvoiceRepository
    let _operationRepository: OperationRepository
    let _backOfficeService: BackOfficeService
    
    test.beforeAll(async () => {
        _genericIntegrationService = new GenericIntegrationService()
        _supplierService = new SupplierService()
        _customerService =  new CustomerService()
        _invoiceRepository = new InvoiceRepository()
        _operationRepository = new OperationRepository()
        _backOfficeService = new BackOfficeService(new DrawApprovalsRepository(), new BackOfficeClient())
        _paymentService = new paymentService(_invoiceRepository, _backOfficeService, new CustomerService())
        resetReportToDefault()
    })
    
    function resetReportToDefault() {
        resultPerTest = true;
        testGroupId = process.env.TestGroupId
        actionsPerTest = []
        
        report = {
            testGroup: testGroup,
            testGroupId: testGroupId,
        } as ITestReport
    }

    async function finalizeSingleTestReport() {
        report.createdAt = new Date()
        report.result = resultPerTest
        report.action = actionsPerTest;

        try {
            await insertTestReport(report);
        }
        catch (e) {
            console.log(e)
        }

        resetReportToDefault()
    }

    test.afterEach(async ({}, testInfo) => {
        if (testInfo.status !== 'passed') {
            actionsPerTest.push({ description: `Test failed with error: ${testInfo.error?.message}`});
            resultPerTest = false;
        }
        
        await finalizeSingleTestReport();
        report._id = null;
    });

    test("@paymentsE2E Create 2 invoice via UI and pay it with BTC", async ({ browser, adminIdToken }) => {
        report.testName = "@payments Create 2 invoice via UI and pay it with BTC"
        test.setTimeout(550000);

        const config = getEnvConfig();

        //config.supplier.email = '<EMAIL>';

        const createInvoiceResponse = await _supplierService.addInvoice(
            config.baseUrl,
            browser,
            config.supplier.email,
            config.supplier.password,
            config.customer.name,
            actionsPerTest);

            await _customerService.payInvoiceViaDraw(
                config.baseUrl,
                browser,
                createInvoiceResponse.invoiceNumber,
                config.customer.email,
                config.customer.password,
                actionsPerTest); 

            await BaseTest.delayOperation(20000)
        
            await _paymentService.makeInoviceSuccesfulyPaidViaBTC('', createInvoiceResponse.invoiceNumber, browser, config.customer.email, config.customer.password, actionsPerTest, adminIdToken);
    });

    test("@paymentsE2E Create Ar advacne invoice via UI and pay it with Factoring", async ({ browser, adminIdToken }) => {
        report.testName = "@payments Create 2 invoice via UI and pay it with BTC"
        test.setTimeout(550000);

        const config = getEnvConfig();

        const createInvoiceResponse = await _supplierService.addArAdvanceInvoice(
            config.baseUrl,
            browser,
            config.supplier.email,
            config.supplier.password,
            config.customer.name,
            actionsPerTest);

                // Invoice not found by ID, so try to find by number
                var invoice = await _invoiceRepository.getInvoiceByNumber(createInvoiceResponse.invoiceNumber);
                var invoiceId = invoice!._id.toString();

                await BaseTest.delayOperation(10000)
    
            await _backOfficeService.approveDrawApprovalViaBackOffice(invoiceId, browser, actionsPerTest, adminIdToken);
            await BaseTest.delayOperation(60000)

            await _customerService.payInvoiceViaAch(
                config.baseUrl,
                browser,
                createInvoiceResponse.invoiceNumber,
                config.customer.email,
                config.customer.password,
                actionsPerTest); 
        
        await _paymentService.makeInoviceSuccesfulyPaidViaFactoring(invoiceId, createInvoiceResponse.invoiceNumber, browser, '<EMAIL>', 'Ss@22222222', actionsPerTest, adminIdToken);
    });

    test("@paymentsE2E Create regular invoice via UI and pay it with ACH", async ({ browser, adminIdToken }) => {
        report.testName = "@payments Create regular invoice via UI and pay it with ACH"
        test.setTimeout(550000);
        
        const config = getEnvConfig();

        config.supplier.email = '<EMAIL>';
        
        const createInvoiceResponse = await _supplierService.addInvoice(
            config.baseUrl,
            browser,
            config.supplier.email,
            config.supplier.password,
            config.customer.name,
            actionsPerTest);

        await _customerService.payInvoiceViaAch(
            config.baseUrl,
            browser,
            createInvoiceResponse.invoiceNumber,
            config.customer.email,
            config.customer.password,
            actionsPerTest); 

        var invoice = await _invoiceRepository.getInvoiceByNumber(createInvoiceResponse.invoiceNumber);
        var invoiceId = invoice!._id.toString();
    
        await _paymentService.makeInoviceSuccesfulyPaidViaAch(
            invoiceId, 
            createInvoiceResponse.invoiceNumber, 
            browser, 
            config.customer.email, 
            config.customer.password, 
            actionsPerTest);
    });

    test("@paymentsE2E Create Ar advance and pay partially with manual payment", async ({ browser, adminIdToken }) => {
        report.testName = "@payments Create 2 invoice via UI and pay it with BTC"
        test.setTimeout(550000);

        const config = getEnvConfig();

        const createInvoiceResponse = await _supplierService.addArAdvanceInvoice(
            config.baseUrl,
            browser,
            config.supplier.email,
            config.supplier.password,
            config.customer.name,
            actionsPerTest);

        // Invoice not found by ID, so try to find by number
        var invoice = await _invoiceRepository.getInvoiceByNumber(createInvoiceResponse.invoiceNumber);
        var invoiceId = invoice!._id.toString();

        await BaseTest.delayOperation(10000)

        await _backOfficeService.approveDrawApprovalViaBackOffice(invoiceId, browser, actionsPerTest, adminIdToken);
        await BaseTest.delayOperation(60000)
        
        await _paymentService.makeInoviceSuccesfulyPaidViaFactoringWithManualPayment(
            invoiceId, 
            createInvoiceResponse.invoiceNumber, 
            browser, 
            config.customer.email, 
            config.customer.password, 
            actionsPerTest, 
            adminIdToken);
    });

    test("@paymentsE2E [4] Create Ar advance and pay partially with 2 manual and 2 customer payments", async ({ browser, adminIdToken }) => {
        report.testName = "@payments Create 2 invoice via UI and pay it with BTC"
        test.setTimeout(550000);

        const config = getEnvConfig();

        const createInvoiceResponse = await _supplierService.addArAdvanceInvoice(
            config.baseUrl,
            browser,
            config.supplier.email,
            config.supplier.password,
            config.customer.name,
            actionsPerTest);

        // Invoice not found by ID, so try to find by number
        var invoice = await _invoiceRepository.getInvoiceByNumber(createInvoiceResponse.invoiceNumber);
        var invoiceId = invoice!._id.toString();

        await BaseTest.delayOperation(10000)

        await _backOfficeService.approveDrawApprovalViaBackOffice(invoiceId, browser, actionsPerTest, adminIdToken);
        await BaseTest.delayOperation(60000)
        
        await _paymentService.makeInoviceSuccesfulyPaidViaFactoringWith4Payments(
            invoiceId, 
            createInvoiceResponse.invoiceNumber, 
            browser, 
            config.customer.email, 
            config.customer.password, 
            actionsPerTest, 
            adminIdToken);
    });

    test("@paymentsE2E Create Ar advance and pay partially with manual payment and Card", async ({ browser, adminIdToken }) => {
        report.testName = "@payments Create 2 invoice via UI and pay it with BTC"
        test.setTimeout(550000);

        const config = getEnvConfig();

        const createInvoiceResponse = await _supplierService.addArAdvanceInvoice(
            config.baseUrl,
            browser,
            config.supplier.email,
            config.supplier.password,
            config.customer.name,
            actionsPerTest);

                // Invoice not found by ID, so try to find by number
                var invoice = await _invoiceRepository.getInvoiceByNumber(createInvoiceResponse.invoiceNumber);
                var invoiceId = invoice!._id.toString();

                await BaseTest.delayOperation(10000)
    
            await _backOfficeService.approveDrawApprovalViaBackOffice(invoiceId, browser, actionsPerTest, adminIdToken);
            await BaseTest.delayOperation(60000)
        
        await _paymentService.makeInoviceSuccesfulyPaidViaFactoringWithManualPaymentAndCard(
            invoiceId, 
            createInvoiceResponse.invoiceNumber, 
            browser, 
            config.customer.email, 
            config.customer.password, 
            actionsPerTest, 
            adminIdToken
        );
    });


    test("@paymentsE2E Create Ar advance and pay with manual payment", async ({ browser, adminIdToken }) => {
        report.testName = "@payments Create 2 invoice via UI and pay it with BTC"
        test.setTimeout(550000);

        const config = getEnvConfig();

        const createInvoiceResponse = await _supplierService.addArAdvanceInvoice(
            config.baseUrl,
            browser,
            config.ihcSupplier.email,
            config.ihcSupplier.password,
            config.ihcCustomer.name,
            actionsPerTest);

                // Invoice not found by ID, so try to find by number
                var invoice = await _invoiceRepository.getInvoiceByNumber(createInvoiceResponse.invoiceNumber);
                var invoiceId = invoice!._id.toString();

                await BaseTest.delayOperation(10000)
    
            await _backOfficeService.approveDrawApprovalViaBackOffice(invoiceId, browser, actionsPerTest, adminIdToken);
            await BaseTest.delayOperation(60000)
        
        await _paymentService.makeInoviceSuccesfulyPaidViaFactoringWithManualPaymentFully(
            invoiceId, 
            createInvoiceResponse.invoiceNumber, 
            browser, 
            config.customer.email, 
            config.customer.password, 
            actionsPerTest, 
            adminIdToken
        );
    });

    test("@paymentsE2E test LMS", async ({ browser, adminIdToken }) => {
        report.testName = "@payments Create 2 invoice via UI and pay it with BTC"
        test.setTimeout(550000);
        
        let invoiceId = '67e14cf3702fa8e32accc555'
        _backOfficeService.approveDrawApprovalViaBackOffice(invoiceId, browser, actionsPerTest, adminIdToken);
    });
});