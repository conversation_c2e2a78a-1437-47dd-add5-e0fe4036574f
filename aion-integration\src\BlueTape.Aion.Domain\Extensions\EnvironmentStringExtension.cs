﻿using BlueTape.Aion.Domain.Constants;

namespace BlueTape.Aion.Domain.Extensions;

public static class EnvironmentStringExtension
{
    public static bool IsEnvironmentDevOrBeta(this string environment) =>
        environment == EnvironmentConstants.Dev ||
        environment == EnvironmentConstants.Beta;
    
    public static bool IsEnvironmentNotProd(this string environment) =>
        environment is EnvironmentConstants.Dev or EnvironmentConstants.Beta || 
        environment == EnvironmentConstants.Qa;
}
