import {BasePage} from '../../base.page';

export class Home extends BasePage {
    constructor(page){
        super(page);
    };

    labels = {
        emailVerified: this.page.locator('"Email is verified"'),
        applicationPendingApproval: this.page.locator('"Your application is pending approval"'),
        applicationApproved: this.page.locator('"Your application is approved! You can get paid now."'),
    };
}