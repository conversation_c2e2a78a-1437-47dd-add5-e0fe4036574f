﻿using BlueTape.Integrations.Aion.Infrastructure.Enums;

namespace BueTape.Aion.Infrastructure.ServiceBusMessages.Report;

public class AionExternalAchTransactionReportRequest
{
    public List<ExternalAchReportRequest> ExternalAchReportRequests { get; set; } = new();
}

public class ExternalAchReportRequest
{
    public PaymentSubscriptionType PaymentSubscriptionType { get; set; }
    public int? AchTransactionPage { get; set; }
    public int? AchReturnTransactionPage { get; set; }
    public int? InstantTransactionPage { get; set; }
    public int? WireTransactionPage { get; set; }
}