﻿using BlueTape.MongoDB.Constants;
using BlueTape.Services.DecisionEngine.BusinessLogic.Abstractions.Infrastructure.RetrieveDataDynamicallyStrategies;
using BlueTape.Services.DecisionEngine.BusinessLogic.Abstractions.Services;
using BlueTape.Services.DecisionEngine.BusinessLogic.Infrastructure.RetrieveDataDynamicallyStrategies.Experian;
using BlueTape.Services.DecisionEngine.BusinessLogic.Models.BusinessTrades;
using BlueTape.Services.DecisionEngine.BusinessLogic.Models.BVI;
using BlueTape.Services.DecisionEngine.BusinessLogic.Models.CreditApplication;
using BlueTape.Services.DecisionEngine.BusinessLogic.Models.Draft;
using BlueTape.Services.DecisionEngine.Domain.Models.AccountAuthorization;
using BlueTape.Services.DecisionEngine.Infrastructure.Exceptions;
using BlueTape.Services.Utilities.Configuration;
using BlueTape.Utilities.Security;

namespace BlueTape.Services.DecisionEngine.BusinessLogic.Tests.Infrastructure.RetrieveDataDynamicallyStrategies.Experian;

public class RetrieveTradesDataTests
{
    private readonly IRetrieveDataDynamicallyStrategy _strategy;
    private readonly IExperianService _experianServiceMock = Substitute.For<IExperianService>();
    private readonly IKmsEncryptionService _kmsEncryptionServiceMock = Substitute.For<IKmsEncryptionService>();
    private readonly IConfigurationService _configurationServiceMock = Substitute.For<IConfigurationService>();

    private const string Cipher = "MTMyMTMyMzEy";
    public RetrieveTradesDataTests()
    {
        _kmsEncryptionServiceMock.Decrypt(Arg.Any<byte[]>()).Returns(new byte[] { });
        _strategy = new RetrieveTradesData(_experianServiceMock, _kmsEncryptionServiceMock, _configurationServiceMock);
    }

    [Theory, CustomAutoData]
    public void RetrieveData_ParsedDraftNull_ThrowsStepExecutionException(AccountAuthorizationModel accountAuthorization, CreditApplicationModel creditApplication)
    {
        var act = async () => await _strategy.RetrieveData(accountAuthorization, null!, creditApplication, default);

        act.ShouldThrowAsync<StepExecutionException>();
    }

    [Theory, CustomAutoData]
    public async Task RetrieveData_BinIsNull_DefaultResult(AccountAuthorizationModel accountAuthorization,
        CreditApplicationModel creditApplication)
    {
        accountAuthorization.BusinessDetails.Bin = null;

        var act = await _strategy.RetrieveData(accountAuthorization, new ParsedDraftModel(), creditApplication, default);

        act.ShouldBeOfType<List<BviResult>>();
        act.Count.ShouldBe(1);
        act[0].IntegrationSource.ShouldBe(IntegrationServicesNamesConstants.Experian);
        act[0].IntegrationLogId.ShouldBeNull();
    }

    [Theory, CustomAutoData]
    public async Task RetrieveData_StrategyNotExecutedAndDataValid_UpdatesAccountAuthorizationDetails(AccountAuthorizationModel accountAuthorization,
        ParsedDraftModel parsedDraft, CreditApplicationModel creditApplication)
    {
        accountAuthorization.BusinessDetails.Bin!.Cipher = Cipher;
        accountAuthorization.IsSentBack = true;
        accountAuthorization.BusinessDetails.CompanyIncome = 1;
        _experianServiceMock.GetBusinessTrades(Arg.Is<BusinessTradesRequestModel>(x => x.TradePaymentTotals
                && x.TradePaymentExperiences), true, default)
            .Returns(StringResponseConstants.TradesResponseString);

        await _strategy.RetrieveData(accountAuthorization, parsedDraft, creditApplication, default);

        await _experianServiceMock.Received(1).GetBusinessTrades(Arg.Is<BusinessTradesRequestModel>(x => x.TradePaymentTotals
            && x.TradePaymentExperiences), true, default);
        accountAuthorization.BusinessDetails.TradeLinesPercentage.ShouldBe(100);
        accountAuthorization.BusinessDetails.TotalTradeLines.ShouldBe(5900);
        accountAuthorization.BusinessDetails.DBT60PlusPercentage.ShouldBe(0);
        accountAuthorization.BusinessDetails.FirstReportedTradeLineDate.ShouldBe(new DateOnly(2023, 06, 01));
        accountAuthorization.BusinessDetails.CreditUtilizationRatio.ShouldBe(0.5m);
        accountAuthorization.BusinessDetails.DBT60PlusAndRevenueRatio.ShouldBe(0.00m);
    }

    [Theory, CustomAutoData]
    public async Task RetrieveData_StrategyNotExecutedAndDataMissed_UpdatesAccountAuthorizationDetailsWithNull(AccountAuthorizationModel accountAuthorization,
        ParsedDraftModel parsedDraft, CreditApplicationModel creditApplication)
    {
        accountAuthorization.BusinessDetails.Bin!.Cipher = Cipher;
        accountAuthorization.BusinessDetails.FirstReportedTradeLineDate = null;
        accountAuthorization.IsSentBack = true;
        _experianServiceMock.GetBusinessTrades(Arg.Is<BusinessTradesRequestModel>(x => x.TradePaymentTotals
                && x.TradePaymentExperiences), true, default)
            .Returns(StringResponseConstants.TradesNullableResponseString);

        await _strategy.RetrieveData(accountAuthorization, parsedDraft, creditApplication, default);

        await _experianServiceMock.Received(1).GetBusinessTrades(Arg.Is<BusinessTradesRequestModel>(x => x.TradePaymentTotals
            && x.TradePaymentExperiences), true, default);

        accountAuthorization.BusinessDetails.TradeLinesPercentage.ShouldBeNull();
        accountAuthorization.BusinessDetails.TotalTradeLines.ShouldBeNull();
        accountAuthorization.BusinessDetails.DBT60PlusPercentage.ShouldBeNull();
        accountAuthorization.BusinessDetails.FirstReportedTradeLineDate.ShouldBeNull();
        accountAuthorization.BusinessDetails.DBT60PlusAndRevenueRatio.ShouldBeNull();
        accountAuthorization.BusinessDetails.CreditUtilizationRatio.ShouldBeNull();
    }

    [Theory]
    [InlineData("TotalTradeLines")]
    [InlineData("TradeLinesPercentage")]
    [InlineData("DBT60PlusPercentage")]
    [InlineData("FirstReportedTradeLineDate")]
    [InlineData("DBT60PlusAndRevenueRatio")]
    public void IsApplicable_ApplicableFields_ReturnsTrue(string field)
    {
        var result = _strategy.IsApplicable(new List<string>() { field });

        result.ShouldBeTrue();
    }

    [Theory]
    [InlineData("")]
    [InlineData("BusinessIndicator")]
    public void IsApplicable_NotApplicableFields_ReturnsFalse(string field)
    {
        var result = _strategy.IsApplicable(new List<string>() { field });

        result.ShouldBeFalse();
    }
}
