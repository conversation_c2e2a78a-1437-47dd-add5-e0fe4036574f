﻿using BlueTape.Aion.API.Models.Errors;
using BlueTape.Aion.Application.Abstractions;
using BlueTape.Aion.Application.Constants;
using BlueTape.Aion.DataAccess.External.Constants;
using BlueTape.Aion.Domain.Constants;
using BlueTape.Aion.Domain.Extensions;
using BlueTape.Integrations.Aion.Accounts;
using BlueTape.Integrations.Aion.Infrastructure.Enums;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using System.ComponentModel.DataAnnotations;
using System.Diagnostics.CodeAnalysis;

namespace BlueTape.Aion.API.Controllers;

[ApiController]
[Route("/api/account")]
[ExcludeFromCodeCoverage]
[Authorize]
[ProducesResponseType(typeof(List<ErrorModel>), StatusCodes.Status400BadRequest)]
[ProducesResponseType(typeof(void), StatusCodes.Status401Unauthorized)]
[ProducesResponseType(typeof(List<ErrorModel>), StatusCodes.Status500InternalServerError)]
public class AccountController : ControllerBase
{
    private readonly IAchService _achService;
    private readonly IConfiguration _configuration;

    public AccountController(
        IAchService achService,
        IConfiguration configuration)
    {
        _achService = achService;
        _configuration = configuration;
    }

    [HttpGet("account-code/{accountCodeType}")]
    [ProducesResponseType(typeof(string), StatusCodes.Status200OK)]
    public async Task<IActionResult> GetAccountBalance(
        [FromHeader(Name = "PaymentRequestId")][Required] string paymentRequestId,
        [FromHeader(Name = ClientConstants.PaymentSubscriptionTypeHeader)][Required] string paymentSubscriptionType,
        [FromRoute][Required] AccountCodeType accountCodeType,
        CancellationToken ctx)
    {
        if (Environment.GetEnvironmentVariable(EnvironmentConstants.Environment).IsEnvironmentDevOrBeta())
        {
            var balance = _configuration["AION-TEST-BALANCE"] ?? "0.00";
            var availableBalance = decimal.Parse(balance);
            return Ok(availableBalance);
        }

        var result = await _achService.GetAvailableBalance(accountCodeType, paymentSubscriptionType, ctx);
        return Ok(result);
    }

    [HttpGet]
    public async Task<List<AccountResponseObj>> GetAccounts(CancellationToken ctx)
    {
        var env = Environment.GetEnvironmentVariable(EnvironmentConstants.Environment);
        var accounts = await _achService.GetAllAccounts(ctx);

        if (env!.IsEnvironmentDevOrBeta() || env == EnvironmentConstants.Qa)
        {
            accounts.AddRange(DefaultAccounts());
        }

        return accounts;
    }

    private List<AccountResponseObj> DefaultAccounts() => new()
    {
        new AccountResponseObj
        {
            Id = _configuration[KeyVaultKeysConstants.FundingAccountId],
            AccountNumber = "**********",
            CurrentBalance = 47320.00m,
            AvailableBalance = 44649.81m,
            AmountOnHold = 2670,
            Name = "aion",
            PaymentProvider = "aion",
            AccountCodeType = AccountCodeType.LOCKBOXCOLLECTION,
        },
        new AccountResponseObj
        {
            Id = _configuration[KeyVaultKeysConstants.CollectionAccountId],
            AccountNumber = "**********",
            CurrentBalance = 57320.00m,
            AvailableBalance = 43649.81m,
            AmountOnHold = 2670,
            Name = "aion",
            PaymentProvider = "aion",
            AccountCodeType = AccountCodeType.DACACOLLECTION,
        }
    };
}