﻿using BlueTape.MongoDB.Attributes;
using MongoDB.Bson;
using MongoDB.Bson.Serialization.Attributes;

namespace BlueTape.Aion.DataAccess.MongoDB.Entities.BanksAccounts;

[MongoCollection("bankaccounts")]
[BsonIgnoreExtraElements]
public class BankAccountEntity
{
    [BsonId]
    [BsonRepresentation(BsonType.ObjectId)]
    [BsonElement("_id")]
    public string BlueTapeBankAccountId { get; set; } = null!;
    
    [BsonElement("aion")]
    public BankAccountAionSettingsEntity? AionSettings { get; set; }
    
    [BsonElement("name")]
    public string? Name { get; set; }
    
    [BsonElement("accountType")]
    public string? AccountType { get; set; }
    
    [BsonElement("paymentMethodType")]
    public string? PaymentMethodType { get; set; }
    
    [BsonElement("routingNumber")]
    public string? RoutingNumber { get; set; }
    
    [BsonElement("accountNumber")]
    public BankAccountNumberEntity? AccountNumber { get; set; }
    
    [BsonElement("identity")]
    public BankAccountIdentityEntity? Identity { get; set; }
}