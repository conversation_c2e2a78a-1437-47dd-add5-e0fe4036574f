import React from 'react'
import { useTranslation } from 'react-i18next'
import type { DescriptionsProps } from 'antd'
import { isNumber } from 'lodash'

import StyledDescription from '@/components/common/Description'
import { formatPercentage, getYesNoTranslation } from '@/globals/utils'

interface IProps {
  percentOwned: number | null
  principalOwner: boolean | null
  ownership: string
}

const PercentOwnedDescription = ({
  percentOwned,
  principalOwner,
  ownership,
}: IProps): JSX.Element => {
  const { t } = useTranslation<string | undefined>()

  const percentOwnedItems: DescriptionsProps['items'] = [
    {
      label: t('getPaidApplication.page.detailed.tabs.kyc.percentOwned'),
      children: isNumber(percentOwned)
        ? formatPercentage(percentOwned)
        : t('na'),
    },
    {
      label: t('getPaidApplication.page.detailed.tabs.kyc.principalOwner'),
      children: getYesNoTranslation(principalOwner, t) ?? t('na'),
    },
    {
      label: t('getPaidApplication.page.detailed.tabs.kyc.ownership'),
      children: ownership,
    },
  ]

  return <StyledDescription items={percentOwnedItems} />
}

export default PercentOwnedDescription
