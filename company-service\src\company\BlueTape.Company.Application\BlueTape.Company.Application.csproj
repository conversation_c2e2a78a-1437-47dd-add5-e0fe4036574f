﻿<Project Sdk="Microsoft.NET.Sdk">

    <PropertyGroup>
        <TargetFramework>net8.0</TargetFramework>
        <ImplicitUsings>enable</ImplicitUsings>
        <Nullable>enable</Nullable>
    </PropertyGroup>

    <ItemGroup>
      <PackageReference Include="BlueTape.CompanyService" Version="1.3.2" />
      <PackageReference Include="BlueTape.Utilities" Version="1.4.5" />
      <PackageReference Include="BlueTape.LMS" Version="1.0.2" />
      <ProjectReference Include="..\..\shared\BlueTape.DataAccess.EF\BlueTape.DataAccess.EF.csproj" />
      <ProjectReference Include="..\..\shared\BlueTape.DataAccess.MongoDB\BlueTape.DataAccess.MongoDB.csproj" />
      <ProjectReference Include="..\..\shared\BlueTape.Domain\BlueTape.Domain.csproj" />
      <ProjectReference Include="..\..\shared\services\BlueTape.LoanService.Client\BlueTape.LoanService.Client.csproj" />
      <ProjectReference Include="..\..\shared\services\BlueTape.OBS.Client\BlueTape.OBS.Client.csproj" />
      <ProjectReference Include="..\..\shared\services\BlueTape.Plaid.Client\BlueTape.Plaid.Client.csproj" />
    </ItemGroup>

</Project>
