import type { TFunction } from 'i18next'

import type { IDrawApplicationDepositDetails } from '@/lib/redux/api/draw-application/types'
import { DepositStatus } from '@/globals/types'

export const getSecurityDepositStatus = (
  depositDetails?: IDrawApplicationDepositDetails | null,
): DepositStatus => {
  const { isSecured, isDepositPaid } = depositDetails ?? {}

  if (!isSecured) {
    return DepositStatus.NOT_REQUIRED
  }

  return isDepositPaid ? DepositStatus.PAID : DepositStatus.UNPAID
}

export const getSecurityDepositTranslation = (
  t: TFunction,
  depositDetails?: IDrawApplicationDepositDetails | null,
): string => {
  const status = getSecurityDepositStatus(depositDetails)
  switch (status) {
    case DepositStatus.PAID:
      return t('depositStatus.paid')
    case DepositStatus.UNPAID:
      return t('depositStatus.unpaid')
    case DepositStatus.NOT_REQUIRED:
    default:
      return t('depositStatus.notRequired')
  }
}
