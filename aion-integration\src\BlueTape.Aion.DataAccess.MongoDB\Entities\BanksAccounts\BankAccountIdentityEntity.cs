using MongoDB.Bson.Serialization.Attributes;

namespace BlueTape.Aion.DataAccess.MongoDB.Entities.BanksAccounts;

[BsonIgnoreExtraElements]
public class BankAccountIdentityEntity
{
    [BsonElement("addressLine1")]
    public string? AddressLine1 { get; set; }
    
    [BsonElement("city")]
    public string? City { get; set; }
    
    [BsonElement("countryCode")]
    public string? CountryCode { get; set; }
    
    [BsonElement("countrySubDivisionCode")]
    public string? CountrySubDivisionCode { get; set; }
    
    [BsonElement("postalCode")]
    public string? PostalCode { get; set; }
}