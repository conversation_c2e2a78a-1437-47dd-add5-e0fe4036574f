{"info": {"_postman_id": "fbf4734c-8ec8-4d35-9495-dec9f3b95fc2", "name": "LexisNexis", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json", "_exporter_id": "24833147"}, "item": [{"name": "Business InstantId", "request": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\r\n    \"org_id\": \"{{OrgId}}\",\r\n    \"api_key\": \"{{<PERSON><PERSON><PERSON><PERSON>}}\",\r\n    \"service_type\": \"basic\",\r\n    \"policy\" : \"business_instantid\",\r\n    \"output_format\": \"json\",\r\n    \"business_name\" : \"\",\r\n    \"business_fein\" : \"\",\r\n    \"business_address_street1\" : \"\",\r\n    \"business_address_city\" : \"\",\r\n    \"business_address_state\" : \"\",\r\n    \"business_address_zip\" : \"\",\r\n    \"business_telephone_number\" : \"\",\r\n    \"primary_account_first_name\" : \"\",\r\n    \"primary_account_last_name\" : \"\",\r\n    \"primary_account_national_id_number\" : \"\",\r\n    \"primary_account_address_street1\" : \"\",\r\n    \"primary_account_address_city\" : \"\",\r\n    \"primary_account_address_zip\" : \"\",\r\n    \"primary_account_address_state\" : \"\",\r\n    \"primary_account_address_data_of_birth\" : \"\",\r\n    \"secondary_account_first_name\" : \"\",\r\n    \"secondary_account_last_name\" : \"\",\r\n    \"secondary_account_national_id_number\" : \"\",\r\n    \"secondary_account_address_street1\" : \"\",\r\n    \"secondary_account_address_city\" : \"\",\r\n    \"secondary_account_address_zip\" : \"\",\r\n    \"secondary_account_address_state\" : \"\",\r\n    \"secondary_account_address_data_of_birth\" : \"\",\r\n    \"third_account_first_name\" : \"\",\r\n\t\"third_account_last_name\" : \"\",\r\n\t\"third_account_national_id_number\" : \"\",\r\n\t\"third_account_address_street1\" : \"\",\r\n\t\"third_account_address_city\" : \"\",\r\n\t\"third_account_address_zip\" : \"\",\r\n\t\"third_account_address_state\" : \"\",\r\n\t\"third_account_address_data_of_birth\" : \"\",\r\n\t\"fourth_account_first_name\" : \"\",\r\n\t\"fourth_account_last_name\" : \"\",\r\n\t\"fourth_account_national_id_number\" : \"\",\r\n\t\"fourth_account_address_street1\" : \"\",\r\n\t\"fourth_account_address_city\" : \"\",\r\n\t\"fourth_account_address_zip\" : \"\",\r\n\t\"fourth_account_address_state\" : \"\",\r\n\t\"fourth_account_address_data_of_birth\" : \"\"\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{url}}/api/attribute-query", "host": ["{{url}}"], "path": ["api", "attribute-query"]}}, "response": []}, {"name": "Emailage", "request": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\r\n    \"org_id\": \"{{OrgId}}\",\r\n    \"api_key\": \"{{Api<PERSON><PERSON>}}\",\r\n    \"service_type\": \"basic\",\r\n    \"output_format\": \"json\",\r\n    \"policy\": \"emailage\",\r\n    \"account_address_city\" : \"cochrane\",\r\n    \"account_address_state\" : \"wisconsin\",\r\n    \"account_address_street1\" : \"s2257 yaeger valley rd\",\r\n    \"account_address_zip\" : \"54622\",\r\n    \"account_email\" : \"<EMAIL>\",\r\n    \"account_email_domain\" : \"bluetape.com\",\r\n    \"account_first_name\" : \"rekha\",\r\n    \"account_last_name\" : \"murthy\",\r\n    \"account_name\" : \"rekha_murthy\"\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{url}}/api/attribute-query", "host": ["{{url}}"], "path": ["api", "attribute-query"]}}, "response": []}, {"name": "<PERSON><PERSON><PERSON>", "request": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\r\n    \"org_id\": \"{{OrgId}}\",\r\n    \"api_key\": \"{{Api<PERSON><PERSON>}}\",\r\n    \"service_type\": \"basic\",\r\n    \"output_format\": \"json\",\r\n    \"policy\": \"fraudpoint\",\r\n    \"account_address_city\" : \"cochrane\",\r\n    \"account_address_state\" : \"WA\",\r\n    \"account_address_street1\" : \"s2257 yaeger valley rd\",\r\n    \"account_address_zip\" : \"54622\",\r\n    \"account_email\" : \"<EMAIL>\",\r\n    \"account_email_domain\" : \"bluetape.com\",\r\n    \"account_first_name\" : \"rekha\",\r\n    \"account_last_name\" : \"murthy\",\r\n    \"account_lex_id_region\" : \"US\",\r\n    \"account_name\" : \"rekha_murthy\"\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{url}}/api/attribute-query", "host": ["{{url}}"], "path": ["api", "attribute-query"]}}, "response": []}]}