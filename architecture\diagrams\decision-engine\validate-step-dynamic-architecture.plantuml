@startuml

title Validate step

actor "Start" as nps #LightGray
participant "Validate" as validate #LightGray
participant "OnBoarding\nService" as onbs #SkyBlue
participant "Invoice\nService" as is #SkyBlue
participant "Company\nService" as cs #SkyBlue
participant "Loan\nService" as ls #SkyBlue
database "Mongo" as db #SkyBlue
participant "Next step" as next

autonumber

nps -> validate

== Event Wrapper ==
validate -> onbs : Check loan application
validate -> onbs : Create draft if not exists
validate -> onbs : Updating progress
validate -> onbs : Save previous outputs

== Validate quote ==
validate -> cs : Get company and customer info (isAutoTradeCreditEnabled)
validate -> ls : Gets and set loan payment plan and collection
validate -> onbs : Set loan application and repayment metadata
alt If invoice and has quote
    validate -> is : Get quote
    validate -> validate : Validate quote

    alt #pink If quote rejected
        validate --> validate : Loan Application is canceled
    else #LightGreen If quote approved
        validate --> next : Next Step
    end
end

@enduml