﻿using BlueTape.Aion.DataAccess.External.Models.Accounts;
using BlueTape.Aion.DataAccess.External.Models.AchTransfer;
using BlueTape.Aion.DataAccess.External.Models.AchTransfer.Response;
using BlueTape.Aion.DataAccess.External.Models.CreateAchTransfer;
using BlueTape.Aion.DataAccess.External.Models.CreateAchTransfer.Response;
using BlueTape.Aion.DataAccess.External.Models.CreateCounterParty;
using BlueTape.Aion.DataAccess.External.Models.DailyLimmits;
using BlueTape.Aion.DataAccess.External.Models.InstantTransfer.Request;
using BlueTape.Aion.DataAccess.External.Models.InstantTransfer.Response;
using BlueTape.Aion.DataAccess.External.Models.InternalTransfer;
using BlueTape.Aion.DataAccess.External.Models.Transactions;
using BlueTape.Aion.DataAccess.External.Models.TransferMethod;
using BlueTape.Aion.DataAccess.External.Models.WireTransfer.Requests;
using BlueTape.Aion.DataAccess.External.Models.WireTransfer.Response;
using BlueTape.Integrations.Aion.Infrastructure.Enums;

namespace BlueTape.Aion.DataAccess.External.Abstractions;

public interface IAionHttpClient
{
    Task<string> Login(PaymentSubscriptionType paymentSubscription, CancellationToken ctx);
    Task<CreateAchResponse> CreateAchTransfer(CreateAchTransferRequest request, PaymentSubscriptionType paymentSubscription, CancellationToken ctx);
    Task<CreateWireTransferResponse> CreateWireTransfer(CreateWireTransferRequest request, PaymentSubscriptionType paymentSubscription, CancellationToken ctx);
    Task<CreateInstantTransferResponse> CreateInstantTransfer(CreateInstantTransferRequest request, PaymentSubscriptionType paymentSubscription, CancellationToken ctx);
    
    
    Task<CounterpartyResponse> CreateCounterParty(CreateCounterpartyRequest request, PaymentSubscriptionType paymentSubscription, CancellationToken ctx);
    Task<AddTransferMethodResponse> AddTransferMethod(AddTransferMethodRequest request, PaymentSubscriptionType paymentSubscription, CancellationToken ctx);
    Task<GetAccountsResponse> GetAccounts(PaymentSubscriptionType paymentSubscription, CancellationToken ctx);
    Task<GetTransferMethodResponse> GetTransferMethods(GetTransferMethodRequest request, PaymentSubscriptionType paymentSubscription, CancellationToken ctx);
    Task<GetACHTransferResponse> GetACHTransferReturns(GetACHTransfersReturnsRequest request, PaymentSubscriptionType paymentSubscription, CancellationToken ctx);
    Task<GetACHTransferResponse> GetACHTransfers(GetACHTransfersRequest request, PaymentSubscriptionType paymentSubscription, CancellationToken ctx);
    Task<InternalTransferResponse> CreateInternalTransfer(InternalTransferRequest request, PaymentSubscriptionType paymentSubscription, CancellationToken ctx);
    Task<GetInternalTransferResponse> GetInternalTransfers(GetACHTransfersRequest request, PaymentSubscriptionType paymentSubscription, CancellationToken ctx);
    Task<GetTransactionsResponseModel> GetTransactions(GetTransactionsRequest request, PaymentSubscriptionType paymentSubscription, CancellationToken ctx);
    Task<GetWireTransferResponse> GetWireTransfers(BaseGetACHTransfers request, PaymentSubscriptionType paymentSubscription, CancellationToken ctx);
    Task<GetInstantTransferResponse> GetInstantTransfers(BaseGetACHTransfers request, PaymentSubscriptionType paymentSubscription, CancellationToken ctx);
    
    Task<AionAccountLimitResponse> GetWireLimit(GetWireLimitRequest request, PaymentSubscriptionType paymentSubscription, CancellationToken ctx);
    Task<AionAccountLimitResponse> GetInstantLimit(GetInstantLimitRequest request, PaymentSubscriptionType paymentSubscription, CancellationToken ctx);
    Task<AionAccountLimitResponse> GetAchPushLimit(GetACHLimitRequest request, PaymentSubscriptionType paymentSubscription, CancellationToken ctx);
    Task<AionAccountLimitResponse> GetAchPullLimit(GetACHLimitRequest request, PaymentSubscriptionType paymentSubscription, CancellationToken ctx);
}