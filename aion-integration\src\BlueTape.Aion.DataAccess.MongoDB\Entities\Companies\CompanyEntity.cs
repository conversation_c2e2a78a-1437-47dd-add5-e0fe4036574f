﻿using BlueTape.MongoDB.Attributes;
using MongoDB.Bson;
using MongoDB.Bson.Serialization.Attributes;

namespace BlueTape.Aion.DataAccess.MongoDB.Entities.Companies;

[MongoCollection("companies")]
[BsonIgnoreExtraElements]
public class CompanyEntity
{
    [BsonId]
    [BsonRepresentation(BsonType.ObjectId)]
    [BsonElement("_id")]
    public string BlueTapeCompanyId { get; set; } = null!;
    
    [BsonElement("bankAccounts")]
    public List<BsonObjectId>? BankAccounts { get; set; }
    
    [BsonElement("name")]
    public string? Name { get; set; }
    
    [BsonElement("legalName")]
    public string? LegalName { get; set; }

    [BsonElement("email")]
    public string Email { get; set; } = null!;
    
    [BsonElement("isBusiness")]
    public bool IsBusiness { get; set; }
    
    [BsonElement("aion")]
    public CompanyAionSettingsEntity? AionSettings { get; set; }
}