using System.Text.Json.Serialization;

namespace BlueTape.Aion.DataAccess.External.Models.InstantTransfer.Response;

public class RtpPaymentInfo
{
    [JsonPropertyName("rtpPaymentId")]
    public string? RtpPaymentId { get; set; }

    [JsonPropertyName("originalPaymentId")]
    public string? OriginalPaymentId { get; set; }

    [JsonPropertyName("referenceId")]
    public string? ReferenceId { get; set; }

    [JsonPropertyName("accountNumber")]
    public string? AccountNumber { get; set; }

    [JsonPropertyName("clientIdentifier")]
    public string? ClientIdentifier { get; set; }

    [JsonPropertyName("amount")]
    public decimal Amount { get; set; }

    [JsonPropertyName("direction")]
    public string? Direction { get; set; }

    [JsonPropertyName("status")]
    public string? Status { get; set; }

    [JsonPropertyName("paymentType")]
    public string? PaymentType { get; set; }

    [JsonPropertyName("source")]
    public string? Source { get; set; }

    [JsonPropertyName("debtor")]
    public Debtor? Debtor { get; set; }

    [Json<PERSON>ropertyName("creditor")]
    public Creditor? Creditor { get; set; }

    [JsonPropertyName("network")]
    public Network? Network { get; set; }
}