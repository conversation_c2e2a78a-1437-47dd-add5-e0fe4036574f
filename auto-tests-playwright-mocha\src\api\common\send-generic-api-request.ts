import axios from "axios";

require('dotenv').config();
import {BaseTest} from '../../tests/test-utils';
import {changeDateByDayISO} from '../../utils/change-date';
import {wipeCustomerFields} from "../../database/customers/customer-fields-wiper";
import {UndefinedResponseError} from '../errors/UndefinedResponseError';
import {generateCompanyRequestBody} from '../../tests/api/genericAPIrework/genericAPI-request-body-functions-and-storage'
import {CreateInvoiceSimplifyRequest} from "./genericAPI/requests/createSimplifyInvoiceRequest";

// import {
//     storeResponseDataAndCode
// } from "../../tests/api/genericAPIrework/genericAPI-response-body-functions-and-storage";

const constants = JSON.parse(JSON.stringify(require('../../constants/constants.json')));

// Send Generic API requests

export async function sendGenericApiRequest(method: string, endpoint: string, body: any = null) {
    try {
        const url = `${process.env.GENERIC_API_BASE_URL}/integration/${endpoint}`; //X-BlueTape-Key
        let response;

        switch (method) {
            case 'post':
                response = await axios.post(url, body, {
                    headers: {
                        accept: 'application/json',
                        'Content-Type': 'application/json',
                        'X-BlueTape-Key': `${process.env.X_BLUETAPE_KEY}`,
                        'X-Integration-AccountId': `${process.env.X_INTEGRATION_ACCOUNT_ID}`,
                        'companyId': ``
                    }
                });
                break;
            case 'get':
                response = await axios.get(url, {
                    headers: {
                        accept: 'application/json',
                        'Content-Type': 'application/json',
                        'X-BlueTape-Key': `${process.env.X_BLUETAPE_KEY}`,
                        'X-Integration-AccountId': `${process.env.X_INTEGRATION_ACCOUNT_ID}`
                    }
                });
                break;
            case 'put':
                response = await axios.put(url, body, {
                    headers: {
                        accept: 'application/json',
                        'Content-Type': 'application/json',
                        'X-BlueTape-Key': `${process.env.X_BLUETAPE_KEY}`,
                        'X-Integration-AccountId': `${process.env.X_INTEGRATION_ACCOUNT_ID}`
                    }
                });
                break;
            case 'delete':
                response = await axios.delete(url, {
                    headers: {
                        accept: 'application/json',
                        'Content-Type': 'application/json',
                        'X-BlueTape-Key': `${process.env.X_BLUETAPE_KEY}`,
                        'X-Integration-AccountId': `${process.env.X_INTEGRATION_ACCOUNT_ID}`
                    }
                });
                break;
        }

        // await storeResponseDataAndCode(response);

        try {
            if (response === undefined) {
                throw new UndefinedResponseError();
            } else return response;
        } catch (error) {
            console.log(await error);
        }
    } catch (error) {
        return error;
    }
}

// Generate Requests Body Functions

export async function generateCustomerRequestBody(firstName: any = null, lastName: any = null, customerCellPhoneNumber: any = null, customerEmailAddress: any = null, customerId: any = null) {
    const currentDate = new Date().toISOString();
    const email = `sergei+genericapicustomer${BaseTest.dateTimePrefix()}@bluetape.com`;
    return {
        "firstName": firstName,
        "lastName": lastName,
        "cellPhoneNumber": customerCellPhoneNumber,
        "emailAddress": customerEmailAddress,
        "sourceModifiedDate": currentDate,
        "id": customerId
    };
}

export async function addCustomerToCompany(companyID: string, newCustomerID: string) {
    return await sendGenericApiRequest('post', `customer/${companyID}/customer`, {'customerId': newCustomerID});
}

export async function getCompanyCreditInfo(companyId: string) {
    return await sendGenericApiRequest('get', `company/${companyId}/creditInfo`);
}


/**
 * Customer creating
 * @param phoneNumber - always must be unique.
 * May encounter the problem of identical numbers when generating,
 * use {@link wipeCustomerFields} for overwriting numbers, but have to be careful
 * @param email - always must be unique
 * @param customerId - always must be unique
 */
export async function createCustomer(phoneNumber: string = null, email: string = null, customerId: string = null) {
    if (!email) email = `automation_user+${customerId}@bluetape.com`;
    if (!phoneNumber) phoneNumber = BaseTest.getCellPhoneNumber();
    if (!customerId) customerId = `customerId${BaseTest.dateTimePrefix()}`;

    const requestBody =
        await generateCustomerRequestBody(
            constants.generic.firstName,
            constants.generic.lastName,
            phoneNumber,
            email,
            customerId
        );

    const response = await sendGenericApiRequest('post', `customer`, requestBody);
    return response;
}

// Create Company
export async function createCompany(companyId: string = null) {
    try {
        if (!companyId) {
            companyId = `companyId${BaseTest.dateTimePrefix()}`;
        }

        const currentDate = new Date().toISOString();
        const phoneNumber = BaseTest.getCellPhoneNumber();

        const requestBody =
            await generateCompanyRequestBody({
                companyId: companyId,
                businessPhoneNumber: phoneNumber,
                sourceModifiedDate: currentDate,
                isCustomerUsed: true
            });

        const response = await sendGenericApiRequest('post', `company`, requestBody);

        if (response === undefined) {
            throw new UndefinedResponseError()
        } else return response;
    } catch (error) {
        console.log(error.message);
        throw error;
    }
}

// Update Company
export async function updateCompany(companyId: any = null, phoneNumber: string, currentDate: string) {
    const requestBody = await generateCompanyRequestBody({
        companyId: companyId,
        businessPhoneNumber: phoneNumber,
        sourceModifiedDate: currentDate,
        isCustomerUsed: true
    });
    const response = await sendGenericApiRequest('put', `company/${companyId}`, requestBody);
    return response;
}

export async function updateCompanyR() {

}


// Link Company with Customer
export async function linkCompanyAndCustomer(companyId: string, customerId: any) {
    try {
        const requestBody = {
            'customerId': customerId
        };

        const response = await sendGenericApiRequest('post', `company/${companyId}/customer`, requestBody);

        if (response === undefined) {
            throw new UndefinedResponseError();
        } else {
            return response;
        }
    } catch (error) {
        console.error(error.message);
        throw error;
    }
}

// CREATE INVOICE

export async function createInvoice(invoiceData: any) {
    const invoiceRequestBody = {
        "customerId": invoiceData.customerId,
        "dueDate": invoiceData.dueDate || new Date().toISOString(),
        "expirationDate": invoiceData.expirationDate || new Date().toISOString(),
        "subTotal": invoiceData.subTotalAmount === 0 ? invoiceData.subTotalAmount : invoiceData.subTotalAmount || 100,
        "totalAmount": invoiceData.totalAmount === 0 ? invoiceData.totalAmount : invoiceData.totalAmount || 1000,
        "taxAmount": invoiceData.taxAmount === null ? invoiceData.taxAmount : invoiceData.taxAmount || 10,
        "lines": [
            {
                "description": "string",
                "unitAmount": 0,
                "quantity": 0,
                "subTotal": 0,
                "taxAmount": 0,
                "totalAmount": 0
            }
        ],
        "sourceModifiedDate": invoiceData.sourceModifiedDate || new Date().toISOString(),
        "invoiceDate": invoiceData.invoiceDate || new Date().toISOString(),
        "invoiceNumber": invoiceData.invoiceNumber === null ? invoiceData.invoiceNumber : invoiceData.invoiceNumber || `invoiceNumber${BaseTest.dateTimePrefix()}`,
        "amountDue": 0,
        "status": "string",
        "id": invoiceData.invoiceId === null ? invoiceData.invoiceId : invoiceData.invoiceId || `invoiceId${BaseTest.dateTimePrefix()}`,
        "quoteRefNumber": invoiceData.quoteRefNumber,
        "quoteId": null
    };
    try {
        const response = await sendGenericApiRequest('post', `invoice`, invoiceRequestBody);
        const status = response.response ? response.response.status : response.status;
        const data = response.response ? response.response.data : response.data;
        return {status, data};
    } catch (error) {
        console.log('Error', error);
        return {status: null, data: null};
    }
}

// CREATE INVOICE

export async function createSimplifyInvoice(invoiceRequestBody: CreateInvoiceSimplifyRequest) {
    try {
        const response = await sendGenericApiRequest('post', `invoice/simplify`, invoiceRequestBody);
        const status = response.response ? response.response.status : response.status;
        const data = response.response ? response.response.data : response.data;
        return {status, data};
    } catch (error) {
        console.log('Error', error);
        return {status: null, data: null};
    }
}

export async function getCheckOutUrl(externalInvoiceId: string) {
    const url = `invoice/${externalInvoiceId}/checkout-redirect?redirectUrl=https://beta.bluetape.com/`
    return await sendGenericApiRequest('get', url);
}

/**
 *
 * @param invoiceData - array which params
 * @customerId - id which generated when customer is generated using next pattern customerId_GUID
 */
export async function createInvoiceR(invoiceData: {
    customerId: string,
    dueDate?: string,
    expirationDate?: string,
    subTotalAmount?: number,
    totalAmount?: number,
    taxAmount?: number,
    sourceModifiedDate?: string,
    invoiceDate?: string,
    invoiceNumber?: string,
    projectAddress?: string,
    invoiceId?: string,
    projectId?: string,
    projectName?: string,
    startDate?: string,
    endDate?: string,
    jobId?: string
}) {
    const invoiceRequestBody = {
        "customerId": invoiceData.customerId,
        "dueDate": invoiceData.dueDate || new Date().toISOString(),
        "expirationDate": invoiceData.expirationDate || new Date().toISOString(),
        "subTotal": invoiceData.subTotalAmount === 0 ? invoiceData.subTotalAmount : invoiceData.subTotalAmount || BaseTest.getRandomNumber(75, 125),
        "totalAmount": invoiceData.totalAmount === 0 ? invoiceData.totalAmount : invoiceData.totalAmount || BaseTest.getRandomNumber(500, 750),
        "taxAmount": invoiceData.taxAmount === null ? invoiceData.taxAmount : invoiceData.taxAmount || BaseTest.getRandomNumber(8, 12),
        "lines": [
            {
                "description": "string",
                "unitAmount": 0,
                "quantity": 0,
                "subTotal": 0,
                "taxAmount": 0,
                "totalAmount": 0
            }
        ],
        "sourceModifiedDate": invoiceData.sourceModifiedDate || new Date().toISOString(),
        "invoiceDate": invoiceData.invoiceDate || new Date().toISOString(),
        "invoiceNumber": invoiceData.invoiceNumber === null ? invoiceData.invoiceNumber : invoiceData.invoiceNumber || `invoiceNumber_${BaseTest.getGUID()}`,
        "amountDue": 0,
        "status": "string",
        "id": invoiceData.invoiceId === null ? invoiceData.invoiceId : invoiceData.invoiceId || `invoiceId_${BaseTest.getGUID()}`,
        "quoteRefNumber": "string",
        "quoteId": null,
        "projectDetails": {
            "projectId": invoiceData.projectId || `projectId_${BaseTest.getGUID()}`,
            "name": invoiceData.projectName || `projectName_${BaseTest.getGUID()}`,
            "address": invoiceData.projectAddress || `projectAddress_${BaseTest.getGUID()}`,
            "contractValue": 0,
            "startDate": invoiceData.startDate || new Date().toISOString(),
            "endDate": invoiceData.endDate || new Date().toISOString(),
            "jobId": invoiceData.jobId || `jobId_${BaseTest.getGUID()}`,
            "jobAddress": {
                "address": "string",
                "unitNo": "string",
                "state": "string",
                "zipCode": "string",
                "city": "string",
                "lotNumber": "string",
                "blockNumber": "string"
            },
            "role": "Default",
            "primeContractorDetails": {
                "businessName": "string",
                "firstName": "string",
                "lastName": "string",
                "businessPhoneNumber": "string",
                "businessEmail": "string",
                "businessAddress": "string",
                "state": "string",
                "city": "string",
                "zipCode": "string"
            },
            "type": "Default",
            "privateProjectDetails": {
                "privateProjectType": "Default",
                "builtFor": {
                    "buildForType": "Default",
                    "notes": "string"
                }
            },
            "publicProjectDetails": {
                "description": "string",
                "hasBond": true,
                "isBondRequired": true
            },
            "federalProjectDetails": {
                "hasBond": true
            },
            "individualOwners": [
                {
                    "firstName": "string",
                    "lastName": "string",
                    "phone": "string",
                    "homeAddress": "string",
                    "state": "string",
                    "city": "string",
                    "zipCode": "string"
                }
            ],
            "businessOwners": [
                {
                    "businessName": "string",
                    "firstName": "string",
                    "lastName": "string",
                    "businessPhoneNumber": "string",
                    "businessAddress": "string",
                    "state": "string",
                    "city": "string",
                    "zipCode": "string"
                }
            ]
        }
    };
    const response = await sendGenericApiRequest('post', `invoice`, invoiceRequestBody);
    return response;
}

export async function addInvoice(data: {
    invoiceId: string,
    customerId: string,
    dueDate?: string,
    expirationDate?: string,
    subTotal?: number,
    totalAmount?: number,
    taxAmount?: number,
    sourceModifiedDate?: string,
    invoiceDate?: string,
    invoiceNumber?: string,
    amountDue?: number,
    status?: string
}) {
    const body = {
        "customerId": data.customerId,
        "dueDate": data.dueDate ?? new Date().toISOString(),
        "expirationDate": data.dueDate ?? new Date().toISOString(),
        "subTotal": data.subTotal ?? 10,
        "totalAmount": data.totalAmount ?? 10,
        "taxAmount": data.taxAmount ?? 0,
        "lines": [
            {
                "description": "string",
                "unitAmount": 0,
                "quantity": 0,
                "subTotal": 0,
                "taxAmount": 0,
                "totalAmount": 0
            }
        ],
        "sourceModifiedDate": data.sourceModifiedDate ?? BaseTest.getCurrentDate(),
        "invoiceDate": data.invoiceDate ?? new Date().toISOString(),
        "invoiceNumber": data.invoiceNumber ?? `InvoiceNumber_${BaseTest.getGUID()}`,
        "amountDue": data.amountDue ?? 0,
        "status": data.status ?? "Placed"
    }

    const response = await sendGenericApiRequest('put', `invoice/${data.invoiceId}`, body)

    return response;
}

export async function cancelInvoice(invoiceId: string) {
    try {
        const response = await sendGenericApiRequest('put', `invoice/${invoiceId}/cancel`);
        const status = response.response ? response.response.status : response.status;
        const data = response.response ? response.response.data : response.data;
        return {status, data};
    } catch (error) {
        console.log('Error', error);
        return {status: null, data: null};
    }
}

export async function linkInvoiceAndProject(invoiceIdId: string, projectId: any) {
    try {
        const response = await sendGenericApiRequest('post', `invoice/${invoiceIdId}/project/${projectId}`);
        const status = response.response ? response.response.status : response.status;
        const data = response.response ? response.response.data : response.data;
        return {status, data};
    } catch (error) {
        console.error(error);
        throw error;
    }
}

export async function deleteLinkedInvoiceAndProject(invoiceIdId: any, projectId: any) {
    try {
        const response = await sendGenericApiRequest('delete', `invoice/${invoiceIdId}/project/${projectId}`);
        const status = response.response ? response.response.status : response.status;
        const data = response.response ? response.response.data : response.data;
        return {status, data};
    } catch (error) {
        console.error(error);
        throw error;
    }
}

//CREATE PROJECT

export async function createProject(projectData: any) {
    const privateProjectDetails = {
        "privateProjectType": projectData.privateProjectType === null ? projectData.privateProjectType : projectData.privateProjectType || 'Сommercial',
        "builtFor": {
            "type": projectData.builtForType === null ? projectData.builtForType : projectData.builtForType || 'Spec',
            "notes": "string"
        }
    };
    const publicProjectDetails = {
        "description": "string",
        "hasBond": true,
        "isBondRequired": true,
        "fileUrl": "string"
    };
    const federalProjectDetails = {
        "hasBond": true,
        "fileUrl": "string"
    };
    const individualOwners = [
        {
            "firstName": projectData.individualFristName === null ? projectData.individualFristName : projectData.individualFristName || `individualFristName${BaseTest.dateTimePrefix()}`,
            "lastName": projectData.individualLastName === null ? projectData.individualLastName : projectData.individualLastName || `individualLastName${BaseTest.dateTimePrefix()}`,
            "phone": projectData.individualPhone === null ? projectData.individualPhone : projectData.individualPhone || `2025552211`,
            "homeAddress": projectData.individualHomeAddress === null ? projectData.individualHomeAddress : projectData.individualHomeAddress || "51 West 51st Street, New York, NY, USA",
            "state": projectData.individualState === null ? projectData.individualState : projectData.individualState || "New York",
            "city": projectData.individualCity === null ? projectData.individualCity : projectData.individualCity || "New York",
            "zipCode": projectData.individualZipCode === null ? projectData.individualZipCode : projectData.individualZipCode || "10019",
        }
    ];
    const businessOwners = [
        {
            "businessName": projectData.businessFristName,
            "firstName": projectData.businessFristName,
            "lastName": projectData.businessLastName,
            "businessPhoneNumber": projectData.businessPhone,
            "businessAddress": projectData.businessHomeAddress,
            "state": projectData.businessState,
            "city": projectData.businessCity,
            "zipCode": projectData.businessZipCode
        }
    ];
    const primeContractorDetails = {
        "businessName": projectData.contactorBusinessName === null ? projectData.contactorBusinessName : projectData.contactorBusinessName || `businessName${BaseTest.dateTimePrefix()}`,
        "firstName": projectData.contractorFirstName === null ? projectData.contractorFirstName : projectData.contractorFirstName || `firstName${BaseTest.dateTimePrefix()}`,
        "lastName": projectData.contractorLastName === null ? projectData.lastName : projectData.lastName || `lastName${BaseTest.dateTimePrefix()}`,
        "businessPhoneNumber": projectData.contractorBusinessPhoneNumber === null ? projectData.contractorBusinessPhoneNumber : projectData.contractorBusinessPhoneNumber || `2025552222`,
        "businessEmail": projectData.contractorBusinessEmail === null ? projectData.contractorBusinessEmail : projectData.contractorBusinessEmail || `<EMAIL>`,
        "businessAddress": projectData.contractorBusinessAddress === null ? projectData.contractorBusinessAddress : projectData.contractorBusinessAddress || `51 West 51st Street, New York, NY, USA`,
        "state": projectData.contractorState === null ? projectData.contractorState : projectData.contractorState || 'New York',
        "city": projectData.contractorCity === null ? projectData.contractorCity : projectData.contractorCity || 'New York',
        "zipCode": projectData.contractorZipCode === null ? projectData.contractorZipCode : projectData.contractorZipCode || '10019',
    };
    const projectRequestBody = {
        "status": "active",
        "name": projectData.projectName === null ? projectData.projectName : projectData.projectName || `projectName${BaseTest.dateTimePrefix()}`,
        "sourceModifiedDate": projectData.sourceModifiedDate || new Date().toISOString(),
        "address": projectData.projectAddress === null ? projectData.projectAddress : projectData.projectAddress || `projectAddress${BaseTest.dateTimePrefix()}`,
        "contractValue": projectData.contractAmount === null ? projectData.contractAmount : projectData.contractAmount || Math.floor(Math.random() * 10),
        "startDate": projectData.startDate === null ? projectData.startDate : projectData.startDate || new Date().toISOString(),
        "endDate": projectData.endDate === null ? projectData.endDate : projectData.endDate || await changeDateByDayISO(3),
        "jobId": projectData.jobId || null,
        "jobAddress": {
            "address": projectData.jobAddress === null ? projectData.jobAddress : projectData.jobAddress || `jobAddress${BaseTest.dateTimePrefix()}`,
            "unitNo": projectData.unitNo || null,
            "state": projectData.jobState === null ? projectData.jobState : projectData.jobState || 'New York',
            "zipCode": projectData.jobZipCode === null ? projectData.jobZipCode : projectData.jobZipCode || '10019',
            "city": projectData.jobCity === null ? projectData.jobCity : projectData.jobCity || 'New York',
            "lotNumber": projectData.lotNumber || null,
            "blockNumber": projectData.blockNumber || null,
            "fileUrl": projectData.fileUrl || null,
        },
        "role": projectData.role === null ? projectData.role : projectData.role || 'PrimeOrGeneralContractor',
        "primeContractorDetails": projectData.primeContractorDetails === null ? null : primeContractorDetails,
        "type": projectData.type === null ? projectData.type : projectData.type || 'Private',
        "privateProjectDetails": projectData.privateProjectDetails === null ? null : privateProjectDetails,
        "publicProjectDetails": projectData.publicProjectDetails ? publicProjectDetails : null,
        "federalProjectDetails": projectData.federalProjectDetails ? federalProjectDetails : null,
        "individualOwners": projectData.businessFristName ? null : individualOwners,
        "businessOwners": projectData.businessFristName ? businessOwners : null,
        "id": projectData.projectId === null ? projectData.projectId : projectData.projectId || `projectId${BaseTest.dateTimePrefix()}`
    };
    try {
        const response = await sendGenericApiRequest('post', `project`, projectRequestBody);
        const status = response.response ? response.response.status : response.status;
        const data = response.response ? response.response.data : response.data;
        return {status, data};
    } catch (error) {
        console.log('Error', error);
        return {status: null, data: null};
    }
}

export async function updateProject(projectData: any, projectId) {
    const privateProjectDetails = {
        "privateProjectType": projectData.privateProjectType === null ? projectData.privateProjectType : projectData.privateProjectType || 'Сommercial',
        "builtFor": {
            "type": projectData.builtForType === null ? projectData.builtForType : projectData.builtForType || 'Spec',
            "notes": "string"
        }
    };
    const publicProjectDetails = {
        "description": "string",
        "hasBond": true,
        "isBondRequired": true,
        "fileUrl": "string"
    };
    const federalProjectDetails = {
        "hasBond": true,
        "fileUrl": "string"
    };
    const individualOwners = [
        {
            "firstName": projectData.individualFristName === null ? projectData.individualFristName : projectData.individualFristName || `individualFristName${BaseTest.dateTimePrefix()}`,
            "lastName": projectData.individualLastName === null ? projectData.individualLastName : projectData.individualLastName || `individualLastName${BaseTest.dateTimePrefix()}`,
            "phone": projectData.individualPhone === null ? projectData.individualPhone : projectData.individualPhone || `2025552211`,
            "homeAddress": projectData.individualHomeAddress === null ? projectData.individualHomeAddress : projectData.individualHomeAddress || "51 West 51st Street, New York, NY, USA",
            "state": projectData.individualState === null ? projectData.individualState : projectData.individualState || "New York",
            "city": projectData.individualCity === null ? projectData.individualCity : projectData.individualCity || "New York",
            "zipCode": projectData.individualZipCode === null ? projectData.individualZipCode : projectData.individualZipCode || "10019",
        }
    ];
    const businessOwners = [
        {
            "businessName": projectData.businessName === null ? projectData.businessName : projectData.businessName || `busName${BaseTest.dateTimePrefix()}`,
            "firstName": projectData.businessFristName === null ? projectData.businessFristName : projectData.businessFristName || `busFristName${BaseTest.dateTimePrefix()}`,
            "lastName": projectData.businessLastName === null ? projectData.businessLastName : projectData.businessLastName || `busLastName${BaseTest.dateTimePrefix()}`,
            "businessPhoneNumber": projectData.businessPhone === null ? projectData.businessPhone : projectData.businessPhone || `2025552222`,
            "businessAddress": projectData.businessHomeAddress === null ? projectData.businessHomeAddress : projectData.businessHomeAddress || "51 West 51st Street, New York, NY, USA",
            "state": projectData.businessState === null ? projectData.businessState : projectData.businessState || "New York",
            "city": projectData.businessCity === null ? projectData.businessCity : projectData.businessCity || "New York",
            "zipCode": projectData.businessZipCode === null ? projectData.businessZipCode : projectData.businessZipCode || "10019"
        }
    ];
    const primeContractorDetails = {
        "businessName": projectData.contactorBusinessName === null ? projectData.contactorBusinessName : projectData.contactorBusinessName || `businessName${BaseTest.dateTimePrefix()}`,
        "firstName": projectData.contractorFirstName === null ? projectData.contractorFirstName : projectData.contractorFirstName || `firstName${BaseTest.dateTimePrefix()}`,
        "lastName": projectData.contractorLastName === null ? projectData.lastName : projectData.lastName || `lastName${BaseTest.dateTimePrefix()}`,
        "businessPhoneNumber": projectData.contractorBusinessPhoneNumber === null ? projectData.contractorBusinessPhoneNumber : projectData.contractorBusinessPhoneNumber || `2025552222`,
        "businessEmail": projectData.contractorBusinessEmail === null ? projectData.contractorBusinessEmail : projectData.contractorBusinessEmail || `<EMAIL>`,
        "businessAddress": projectData.contractorBusinessAddress === null ? projectData.contractorBusinessAddress : projectData.contractorBusinessAddress || `51 West 51st Street, New York, NY, USA`,
        "state": projectData.contractorState === null ? projectData.contractorState : projectData.contractorState || 'New York',
        "city": projectData.contractorCity === null ? projectData.contractorCity : projectData.contractorCity || 'New York',
        "zipCode": projectData.contractorZipCode === null ? projectData.contractorZipCode : projectData.contractorZipCode || '10019',
    };
    const projectRequestBody = {
        "status": "active",
        "name": projectData.projectName === null ? projectData.projectName : projectData.projectName || `projectName${BaseTest.dateTimePrefix()}`,
        "sourceModifiedDate": projectData.sourceModifiedDate || new Date().toISOString(),
        "address": projectData.projectAddress === null ? projectData.projectAddress : projectData.projectAddress || `projectAddress${BaseTest.dateTimePrefix()}`,
        "contractValue": projectData.contractAmount === null ? projectData.contractAmount : projectData.contractAmount || Math.floor(Math.random() * 10),
        "startDate": projectData.startDate === null ? projectData.startDate : projectData.startDate || new Date().toISOString(),
        "endDate": projectData.endDate === null ? projectData.endDate : projectData.endDate || await changeDateByDayISO(3),
        "jobId": projectData.jobId || null,
        "jobAddress": {
            "address": projectData.jobAddress === null ? projectData.jobAddress : projectData.jobAddress || `jobAddress${BaseTest.dateTimePrefix()}`,
            "unitNo": projectData.unitNo || null,
            "state": projectData.jobState === null ? projectData.jobState : projectData.jobState || 'New York',
            "zipCode": projectData.jobZipCode === null ? projectData.jobZipCode : projectData.jobZipCode || '10019',
            "city": projectData.jobCity === null ? projectData.jobCity : projectData.jobCity || 'New York',
            "lotNumber": projectData.lotNumber || null,
            "blockNumber": projectData.blockNumber || null,
            "fileUrl": projectData.fileUrl || null,
        },
        "role": projectData.role === null ? projectData.role : projectData.role || 'PrimeOrGeneralContractor',
        "primeContractorDetails": projectData.primeContractorDetails === null ? null : primeContractorDetails,
        "type": projectData.type === null ? projectData.type : projectData.type || 'Private',
        "privateProjectDetails": projectData.privateProjectDetails === null ? null : privateProjectDetails,
        "publicProjectDetails": projectData.publicProjectDetails ? publicProjectDetails : null,
        "federalProjectDetails": projectData.federalProjectDetails ? federalProjectDetails : null,
        "individualOwners": projectData.businessName ? null : individualOwners,
        "businessOwners": projectData.businessName ? businessOwners : null,
        "id": projectData.projectId === null ? projectData.projectId : projectData.projectId || `projectId${BaseTest.dateTimePrefix()}`
    };
    try {
        const response = await sendGenericApiRequest('put', `project/${projectId}`, projectRequestBody);
        const status = response.response ? response.response.status : response.status;
        const data = response.response ? response.response.data : response.data;
        return {status, data};
    } catch (error) {
        console.log('Error', error);
        return {status: null, data: null};
    }
}

//QUOTE

export async function createQuote(quoteData: any) {
    const quoteRequestBody = {
        "customerId": quoteData.customerId,
        "dueDate": quoteData.dueDate || new Date().toISOString(),
        "expirationDate": quoteData.expirationDate || new Date().toISOString(),
        "subTotal": quoteData.subTotalAmount || 100,
        "totalAmount": quoteData.totalAmount || 1000,
        "taxAmount": quoteData.taxAmount === null ? quoteData.taxAmount : quoteData.taxAmount || 10,
        "lines": [
            {
                "description": "string",
                "unitAmount": 0,
                "quantity": 0,
                "subTotal": 0,
                "taxAmount": 0,
                "totalAmount": 0
            }
        ],
        "sourceModifiedDate": quoteData.quoteDate || new Date().toISOString(),
        "quoteDate": quoteData.quoteDate || new Date().toISOString(),
        "quoteNumber": quoteData.quoteNumber === null ? quoteData.quoteNumber : quoteData.quoteNumber || `quoteNumber${BaseTest.dateTimePrefix()}`,
        "id": quoteData.quoteId === null ? quoteData.quoteId : quoteData.quoteId || `quoteId${BaseTest.dateTimePrefix()}`
    };
    try {
        const response = await sendGenericApiRequest('post', `quote`, quoteRequestBody);
        const status = response.response ? response.response.status : response.status;
        const data = response.response ? response.response.data : response.data;
        return {status, data};
    } catch (error) {
        console.log('Error', error);
        return {status: null, data: null};
    }
}

export async function linkQuoteAndProject(quoteId: string, projectId: any) {
    try {
        const response = await sendGenericApiRequest('post', `quote/${quoteId}/project/${projectId}`);
        const status = response.response ? response.response.status : response.status;
        const data = response.response ? response.response.data : response.data;
        return {status, data};

    } catch (error) {
        console.error(error);
        throw error;
    }
}

export async function unlinkQuoteAndProject(quoteId: any, projectId: any) {
    try {
        const response = await sendGenericApiRequest('delete', `quote/${quoteId}/project/${projectId}`);
        const status = response.response ? response.response.status : response.status;
        const data = response.response ? response.response.data : response.data;
        return {status, data};

    } catch (error) {
        console.error(error);
        throw error;
    }
}


// if (!customerId) customerId = `customerId${BaseTest.dateTimePrefix()}`;
// if (!invoiceNumber) invoiceNumber = `invoiceNumber${BaseTest.dateTimePrefix()}`;
// if (!totalAmount) totalAmount = 1000;
// if (!subTotalAmount) subTotalAmount = 200;
// if (!taxAmount) taxAmount = 100;
// if (!dueDate) dueDate = new Date().toISOString();
// if (!expirationDate ) expirationDate = `invoiceNumber${BaseTest.dateTimePrefix()}`;
// if (!invoiceDate) invoiceDate = `invoiceNumber${BaseTest.dateTimePrefix()}`;
