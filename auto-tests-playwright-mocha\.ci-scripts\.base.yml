.test:base:
  stage: test
  image: timbru31/java-node
  rules:
    - if: $CI_PIPELINE_SOURCE == "schedule"
      when: manual
      allow_failure: true
    - when: manual
      allow_failure: true
  script:
    - echo "Deploy to DEVELOPMENT server"
    - echo "$CI_ENVIRONMENT_URL"
    - echo "$ADMIN_EMAIL"
    - echo "$ADMIN_PASSWORD"
    - echo "$USER_EMAIL"
    - echo "$USER_PASSWORD"
    - echo "$ADMIN_BACKEND_URL"
    - npm ci
    - npm install
    - npm install -g allure-commandline --save-dev
    - npx playwright install
    - npx playwright install-deps
    - set ALLURE_RESULTS_DIR=allure-results
    - npx playwright test --reporter=line,allure-playwright
    - allure generate allure-results -o allure-report --clean