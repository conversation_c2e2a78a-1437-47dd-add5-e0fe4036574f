@startuml

title Payment Domain Flow execution

participant "1st party" as first #LightGray
queue "Operations" as opsqs #LightSalmon
participant "Payment Flow\nService" as pflow #SkyBlue
participant "Payment\nService" as pserv #SkyBlue
database "PaymentDB" as pdb
participant "Aion\nIntegration" as aint #SkyBlue
participant "Aion" as aion #Orange

autonumber

== Trigger Invoice Payment ==

first -> opsqs : Place business event
opsqs -> pflow : Get events
pflow --> pflow : Validate request\n(1st parties)

== Place transactions ==

pflow --> pflow : Read template
pflow -> pserv : Create payment request
pserv -> pdb : Create payment request

loop Create transactions
    pflow -> pserv : Create transaction
    pserv -> pdb : Create transaction (placed)
end

== Execute Steps ==

loop A single step
    pflow -> pserv : Execute transaction
    pserv -> aint : Start ACH
    aint -> aion : Start ACH with Aion
    aion --> aint
    aint --> pserv
    pserv -> pdb : Writes result (processing)
    pserv --> pflow
end

@enduml