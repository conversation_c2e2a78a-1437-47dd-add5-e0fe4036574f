openapi: '3.0.0'
info:
  version: '0.0.1'
  title: 'Invoicing Service API'
  description: | 
    API definition of private service for invoicing low-level functions
servers:
- url: TBD-Dev
  description: Development server
- url: TBD-QA
  description: QA
- url: TBD-Prod
  description: Production server
paths:
  /invoices:
    get:
      tags:
        - invoice
      summary: Gets invoices
      description: Gets invoices
      operationId: getInvoice
      parameters:
        - name: limit
          description: Number of results requested.
          in: header
          required: false
          schema:
            type: integer
        - name: offset
          description: Number of elements skipped.
          in: header
          required: false
          schema:
            type: integer
        - name: type
          description: Type of invoice.
          in: query
          required: false
          schema:
            type: string
        - name: invoiceNumber
          description: Number of invoice. Does not result partial matches.
          in: query
          required: false
          schema:
            type: string
        - name: companyId
          description: Company Id.
          in: query
          required: false
          schema:
            type: string
        - name: customerId
          description: Customer Id.
          in: query
          required: false
          schema:
            type: string
        - name: status
          description: Status of invoice
          in: query
          required: false
          schema:
            type: string
        - name: isOverdue
          description: Calculated field to indicate overdue.
          in: query
          required: false
          schema:
            type: string
        - name: issueDateFrom
          description: Issue date from.
          in: query
          required: false
          schema:
            type: string
            format: date
        - name: issueDateTo
          description: Issue date to.
          in: query
          required: false
          schema:
            type: string
            format: date
      responses:
        200:
          description: List of invoices.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/PaginatedInvoiceResponse'
        400:
          description: Invalid request
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        401:
          description: Unauthorized
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        404:
          description: Not found
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        500:
          description: Unexpected error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
    post:
      tags:
        - invoice
      summary: (not for MVP) Creates a new invoice or quote
      description: Creates a new invoice or quote
      operationId: createInvoice
      requestBody:
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/InvoiceModel"
      responses:
        201:
          description: Created invoice.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/InvoiceModel'
        400:
          description: Invalid request
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        401:
          description: Unauthorized
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        404:
          description: Not found
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        500:
          description: Unexpected error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
  /invoices/{id}:
    get:
      tags:
        - invoice
      summary: Gets an invoice by id
      description: Gets an invoice by id
      operationId: getInvoiceById
      parameters:
        - name: id
          description: The id of required invoice.
          in: path
          required: true
          schema:
            type: string
      responses:
        200:
          description: The invoice itself.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/InvoiceModel'
        400:
          description: Invalid request
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        401:
          description: Unauthorized
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        404:
          description: Not found
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        500:
          description: Unexpected error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
    delete:
      tags:
        - invoice
      summary: (not for MVP) Logically deletes an invoice.
      description: Logically deletes an invoice. No physical delete from invoices. See isDeleted field.
      operationId: deleteInvoiceById
      parameters:
        - name: id
          description: The id of invoice to delete.
          in: path
          required: true
          schema:
            type: string
      responses:
        200:
          description: The invoice itself.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/InvoiceModel'
        400:
          description: Invalid request
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        401:
          description: Unauthorized
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        404:
          description: Not found
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        500:
          description: Unexpected error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
  /invoices/customer/{customerId}:
    get:
      tags:
        - invoice
      summary: (OPTIONAL) Gets customer's invoices
      description: Gets customer's invoices
      operationId: getInvoicesOfACustomer
      parameters:
        - name: customerId
          description: The customer id of required invoices.
          in: path
          required: true
          schema:
            type: string
        - name: limit
          description: Number of results requested.
          in: header
          required: false
          schema:
            type: integer
        - name: offset
          description: Number of elements skipped.
          in: header
          required: false
          schema:
            type: integer
        - name: type
          description: Type of invoice.
          in: query
          required: false
          schema:
            type: string
        - name: invoiceNumber
          description: Number of invoice. Does not result partial matches.
          in: query
          required: false
          schema:
            type: string
        - name: companyId
          description: Company Id.
          in: query
          required: false
          schema:
            type: string
        - name: status
          description: Status of invoice
          in: query
          required: false
          schema:
            type: string
        - name: isOverdue
          description: Calculated field to indicate overdue.
          in: query
          required: false
          schema:
            type: string
        - name: issueDateFrom
          description: Issue date from.
          in: query
          required: false
          schema:
            type: string
            format: date
        - name: issueDateTo
          description: Issue date to.
          in: query
          required: false
          schema:
            type: string
            format: date      
      responses:
        200:
          description: List of invoices.
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/PaginatedInvoiceResponse'
        400:
          description: Invalid request
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        401:
          description: Unauthorized
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        404:
          description: Not found
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        500:
          description: Unexpected error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
  /invoices/supplier/{supplierId}:
    get:
      tags:
        - invoice
      summary: (OPTIONAL) Gets supplier's invoices
      description: Gets supplier's invoices
      operationId: getInvoicesOfASupplier
      parameters:
        - name: supplierId
          description: The supplier id of required invoices.
          in: path
          required: true
          schema:
            type: string
        - name: limit
          description: Number of results requested.
          in: header
          required: false
          schema:
            type: integer
        - name: offset
          description: Number of elements skipped.
          in: header
          required: false
          schema:
            type: integer
        - name: type
          description: Type of invoice.
          in: query
          required: false
          schema:
            type: string
        - name: invoiceNumber
          description: Number of invoice. Does not result partial matches.
          in: query
          required: false
          schema:
            type: string
        - name: companyId
          description: Company Id.
          in: query
          required: false
          schema:
            type: string
        - name: customerId
          description: Customer Id.
          in: query
          required: false
          schema:
            type: string
        - name: status
          description: Status of invoice
          in: query
          required: false
          schema:
            type: string
        - name: isOverdue
          description: Calculated field to indicate overdue.
          in: query
          required: false
          schema:
            type: string
        - name: issueDateFrom
          description: Issue date from.
          in: query
          required: false
          schema:
            type: string
            format: date
        - name: issueDateTo
          description: Issue date to.
          in: query
          required: false
          schema:
            type: string
            format: date            
      responses:
        200:
          description: List of invoices.
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/PaginatedInvoiceResponse'
        400:
          description: Invalid request
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        401:
          description: Unauthorized
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        404:
          description: Not found
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        500:
          description: Unexpected error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
  /invoices/list:
    post:
      tags:
        - invoice
      summary: (OPTIONAL) Gets invoices by id list
      description: Gets invoices by id list
      operationId: getInvoicesByIdList
      requestBody:
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/InvoiceList"
      responses:
        200:
          description: List of invoices.
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/PaginatedInvoiceResponse'
        400:
          description: Invalid request
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        401:
          description: Unauthorized
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        404:
          description: Not found
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        500:
          description: Unexpected error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
  /invoices/healthcheck:
    get:
      tags:
        - healthcheck
      summary: Gets health of service
      description: Gets healthcheck
      operationId: getInvoicesHealthcheck
      responses:
        200:
          description: Status is ok
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HealthCheckSuccessResponse'
        404:
          description: Not found
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        500:
          description: Unexpected error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
  /invoices/{id}/status:
    put:
      tags:
        - invoice
      summary: (not for MVP) Update an invoice status by id
      description: Update an invoice status by id
      operationId: updateInvoiceStatusById
      parameters:
        - name: id
          description: The id of required invoice.
          in: path
          required: true
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/InvoiceStatusModel"
      responses:
        200:
          description: The updated invoice with new status.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/InvoiceModel'
        400:
          description: Invalid request
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        401:
          description: Unauthorized
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        404:
          description: Not found
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        500:
          description: Unexpected error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
components:
  schemas:
    PaginatedInvoiceResponse:
      type: object
      required:
        - limit
        - offset
        - count
        - result
      properties:
        limit:
          description: Number of results requested.
          type: integer
          example: 50
        offset:
          description: Number of elements skipped.
          type: integer
          example: 150
        count:
          description: Actual number of results returned.
          type: integer
          example: 14
        total:
          description: Total number of results matching the query.
          type: integer
          example: 164
        result:
          description: Invoices matching the query.
          type: array
          items:
            $ref: '#/components/schemas/InvoiceModel'
    InvoiceModel:
      type: object
      required:
        - createdAt
        - updatedAt
        - type
        - invoice_number
        - customer
        - tax_amount
        - total_amount
        - invoice_date
        - invoice_due_date
        - status
        - isDeleted
        - isApproved
        - isSeen
      properties:
        id:
          description: Id of invoice
          type: string
          example: 637dff014189f7dd1fa510a1
        quoteId:
          description: Id of quote (self-reference)
          type: string
          example: f7dd1fa510a1637dff014189
        createdAt:
          description: Invoice creation time.
          type: string
          format: datetime
          example: 2022-11-23T11:07:45.709Z
        updatedAt:
          description: Last update time. In the time of creation, equals to createdAt.
          type: string
          format: datetime
          example: 2022-11-23T11:07:45.709Z
        type:
          description: Invoice type
          type: string
          enum:
            - invoice
            - sales_order
            - quote
          example: invoice
        invoice_number:
          description: Number of invoice. Unique per supplier.
          type: string
          example: 259613/1
        operation_id:
          description: Last operation id.
          type: string
        company_id:
          description: Company id.
          type: string
        company:
          $ref: '#/components/schemas/CompanyModel'
        payer_id:
          description: The payer id (customer id).
          type: string
        customer_account_id:
          description: Customer account id.
          type: string
        customer:
          $ref: '#/components/schemas/CustomerModel'
        customerAddress:
          $ref: '#/components/schemas/CustomerAddressModel'
        material_description:
          description: Material description.
          type: string
        material_subtotal:
          description: Material subtotal.
          type: number
          example: 1200
        tax_amount:
          description: Tax amount.
          type: number
          example: 333
        refunded_amount:
          description: Refunded amount.
          type: number
          example: 0
        total_amount:
          description: Invoice total amount.
          type: number
          example: 1500
        invoice_date:
          description: Invoice issue date.
          type: string
          format: date
          example: 2022-11-15
        invoice_due_date:
          description: Invoice due date.
          type: string
          format: date
          example: 2022-11-23
        expiration_date:
          description: (!) Not clear why is this field for.
          type: string
          format: date
        notificationType:
          description: Invoice notification type.
          type: string
          enum:
            - EmailInvoice
            - TextInvoice
            - Both
          example: EmailInvoice
        status:
          description: Invoice actual status.
          type: string
          enum:
            - draft
            - placed
            - paid
            - cancelled
            - dismissed
          example: paid
        isDeleted:
          description: Indicator whether invoice is deleted.
          type: boolean
          example: false
        isApproved:
          description: Indicator whether invoice is approved. (!) can be an invoice not approved but paid?
          type: boolean
          example: true
        dismiss_reasons:
          description: Dismiss reasons when not approved.
          type: string
        isSeen:
          description: (!) Field purpose is not clear.
          type: boolean
          example: false
        isOverdue:
          description: Calculated field to indicate overdue.
          type: boolean
          example: false
        note:
          description: Note for invoice.
          type: string
          example: Final invoice
        document:
          $ref: '#/components/schemas/InvoiceDocumentModel'
    InvoiceList:
      type: array
      items:
        $ref: '#/components/schemas/InvoiceListItem'
    InvoiceListItem:
      type: object
      required:
        - id
      properties:
        id:
          description: Id of invoice
          type: string
          example: 637dff014189f7dd1fa510a1
    CustomerModel:
      type: object
      properties:
        id:
          description: Id of customer.
          type: string
          example: 47203640763fc60fa7c9c863
    CompanyModel:
      type: object
      properties:
        id:
          description: Id of company.
          type: string
          example: b5c63497cea320332f5ebd65
    CustomerAddressModel:
      type: object
      properties:
        addressType:
          description: (!) Review these address types.
          type: string
          enum:
            - Delivery
            - Pickup
            - Service
        addressLine1:
          description: Address line 1.
          type: string
          example: Market Str. 200.
        addressLine2:
          description: Address line 2.
          type: string
        unitNumber:
          description: Unit number.
          type: string
        city:
          description: City part of an address.
          type: string
          example: San Francisco
        state:
          description: State part of an address.
          type: string
          example: CA
        zip:
          description: Zipcode.
          type: string
          example: 94111
        latitude:
          description: Latitude part of geo data.
          type: number
        longitude:
          description: Longitude part of geo data.
          type: number
    SupplierModel:
      type: object
      properties:
        id:
          description: Id of supplier.
          type: string
          example: 637c731c0b04d42dae232427
    InvoiceDocumentModel:
      type: object
      properties:
        name:
          description: Name of document.
          type: string
          example: Invoice pdf file
        url:
          description: Url of document.
          type: string
          example: https://drive.google.com/?id=384283er2893eu
    HealthCheckSuccessResponse:
      type: object
    InvoiceStatusModel:
      type: object
      properties:
        status:
          description: Invoice actual status.
          type: string
          enum:
            - draft
            - placed
            - paid
            - cancelled
            - dismissed
          example: paid
    ErrorResponse:
      type: object
      required:
        - statusCode
        - message
        - errorDescription
      properties:
        statusCode:
          description: HTTP status code corresponding to the error.
          type: integer
          example: 400
        message:
          description: A human readable error message.
          type: string
          example: Invalid model.
        errorDescription:
          description: Detailed information.
          type: string
  securitySchemes:
    ApiKey:
      type: apiKey
      in: header
      name: X-Api-Key
security:
  - ApiKey: []