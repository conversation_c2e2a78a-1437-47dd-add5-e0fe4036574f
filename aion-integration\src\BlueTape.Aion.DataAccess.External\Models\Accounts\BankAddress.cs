﻿using System.Runtime.Serialization;
using System.Text.Json.Serialization;

namespace BlueTape.Aion.DataAccess.External.Models.Accounts;

[DataContract]
public class BankAddress
{
    [JsonPropertyName("line1")]
    public string Line1 { get; set; } = null!;
    
    [<PERSON>sonPropertyName("line2")]
    public string Line2 { get; set; } = null!;
    
    [JsonPropertyName("city")]
    public string City { get; set; } = null!;
    
    [<PERSON>son<PERSON>ropertyName("countrySubDivisionCode")]
    public string CountrySubDivisionCode { get; set; } = null!;
    
    [JsonPropertyName("postalCode")]
    public string PostalCode { get; set; } = null!;
    
    [JsonPropertyName("countryCode")]
    public string CountryCode { get; set; } = null!;
}