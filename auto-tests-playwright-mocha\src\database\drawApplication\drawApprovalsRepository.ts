import {client} from "../client";

const collectionName = 'drawapprovals'

export class DrawApprovalsRepository {
    async getDrawApprovalByInvoiceId(invoiceId: string) {

        try {
            await client.connect();

            const database = client.db(`${process.env.test_env}`);
            const collection = database.collection(collectionName);
            const query = {payables: {$elemMatch: {_id: `${invoiceId}`}}}

            return await collection.findOne(query)
        } catch (e) {
            console.error(e);
        } finally {
            await client.close();
        }
    }
}
