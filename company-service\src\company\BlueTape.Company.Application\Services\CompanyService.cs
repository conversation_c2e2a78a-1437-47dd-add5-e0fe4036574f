﻿using AutoMapper;
using BlueTape.Common.Exceptions;
using BlueTape.Common.Exceptions.Companies;
using BlueTape.Company.Application.Abstractions.Service;
using BlueTape.Company.Application.Extensions;
using BlueTape.Company.Domain.DTOs.Companies;
using BlueTape.CompanyService.Common.Enums;
using BlueTape.CompanyService.Companies;
using BlueTape.CompanyService.Enums;
using BlueTape.DataAccess.MongoDB.Abstractions;
using BlueTape.Document.DataAccess.EF.Abstractions;
using BlueTape.Domain.Entities.Documents;
using Microsoft.AspNetCore.Mvc;
using MongoDB.Bson;

namespace BlueTape.Company.Application.Services;

public class CompanyService(
    ICompanyRepository companyRepository,
    IEfCompanyRepository efCompanyRepository,
    ICompanyIdentifierService companyIdentifierService,
    IEfCompanyNoteRepository efCompanyNoteRepository,
    IMapper mapper)
    : ICompanyService
{
    public async Task<CompanyDto?> GetByCompanyId(string companyId, CancellationToken cancellationToken)
    {
        var companyDto = await companyRepository.GetByCompanyId(companyId, cancellationToken);

        var companyEntity = await efCompanyRepository.GetByLegacyId(companyId, cancellationToken);

        return companyDto.MapAccountEntityToCompanyDto(companyEntity);
    }

    public async Task<ResultWithPaginationDto<CompanyDto>> GetByFiltersWithPagination(CompanyQueryPaginated query,
        CancellationToken cancellationToken)
    {
        ArgumentNullException.ThrowIfNull(query);

        ResultWithPaginationDto<CompanyDto> data = new();

        var isObjectId = ObjectId.TryParse(query.Id, out _);

        //Type / Name field / sort fields are not present in pgsql
        if (!query.Type.HasValue
            && string.IsNullOrEmpty(query.Name)
            && string.IsNullOrEmpty(query.Search)
            && !query.IsGuest.HasValue
            && query.SortBy is null or AccountSortingParameter.accountStatus or AccountSortingParameter.createdAt
            && !isObjectId)
        {
            var efCompanies = await efCompanyRepository.GetByFiltersWithPagination(query, cancellationToken);
            var legacyIds = efCompanies.Result.Select(x => x.LegacyId!).ToArray();
            var companies = await companyRepository.GetByCompanyIds(legacyIds, cancellationToken) ?? [];

            data.PagesCount = efCompanies.PagesCount;
            data.PageNumber = efCompanies.PageNumber;
            data.TotalCount = efCompanies.TotalCount;

            data.Result = MergeCompanies(efCompanies.Result, companies, true);
        }
        //TODO: implement proper filtration in this case
        else
        {
            var companies = await companyRepository.GetByFiltersWithPagination(query, cancellationToken);
            var companyIds = companies.Result.Select(x => x.BlueTapeCompanyId).ToArray();
            var efCompanies = await efCompanyRepository.GetByLegacyIds(companyIds, cancellationToken);

            data.PagesCount = companies.PagesCount;
            data.PageNumber = companies.PageNumber;
            data.TotalCount = companies.TotalCount;

            data.Result = MergeCompanies(efCompanies, companies.Result, false);
        }

        return data;
    }

    private static IEnumerable<CompanyDto> MergeCompanies(IEnumerable<CompanyEntity> efCompanies,
        IEnumerable<CompanyDto> companies, bool isPostgreDataPrimary)
    {
        //Such implementation helps to save db ordering
        var result = new List<CompanyDto>();
        if (isPostgreDataPrimary)
        {
            foreach (var efCompany in efCompanies)
            {
                var mongoCompany = companies.FirstOrDefault(x => x.BlueTapeCompanyId == efCompany.LegacyId);
                if (mongoCompany == null) continue;
                var companyDto = mongoCompany.MapAccountEntityToCompanyDto(efCompany);
                result.Add(companyDto!);
            }
        }
        else
        {
            foreach (var mongoCompany in companies)
            {
                var efCompany = efCompanies.FirstOrDefault(x => x.LegacyId == mongoCompany.BlueTapeCompanyId);
                var companyDto = mongoCompany.MapAccountEntityToCompanyDto(efCompany);
                result.Add(companyDto!);
            }
        }

        return result;
    }

    public async Task<CompanyPaymentDetailsDto> GetCompanyPaymentDetailsById(string companyId,
        CancellationToken cancellationToken)
    {
        var companyDto = await companyRepository.GetByCompanyId(companyId, cancellationToken);

        if (companyDto == null) throw new VariableNullException(nameof(companyDto));

        if (string.IsNullOrEmpty(companyDto.PublicIdentifier))
        {
            var companyIdentifier = await companyIdentifierService.GetNextCompanyIdentifier(cancellationToken);
            companyDto.PublicIdentifier = companyIdentifier;
            await companyRepository.UpdatePublicIdentifierAsync(companyDto.BlueTapeCompanyId, companyIdentifier,
                cancellationToken);
        }

        var companyPaymentDetails = mapper.Map<CompanyPaymentDetailsDto>(companyDto);

        return companyPaymentDetails;
    }

    public async Task<CompanyDto[]?> GetByCompanyIds(string[] companyIds, CancellationToken cancellationToken)
    {
        var companyDtos = await companyRepository.GetByCompanyIds(companyIds, cancellationToken);
        var companyEntities = await efCompanyRepository.GetByLegacyIds(companyIds, cancellationToken);

        return companyDtos?.Select(companyDto =>
                companyDto.MapAccountEntityToCompanyDto(companyEntities.Find(entity =>
                    entity.LegacyId == companyDto.BlueTapeCompanyId))!)
            .ToArray();
    }

    public async Task<CompanyDtoV2?> GetByCompanyIdV2(Guid companyId, CancellationToken ct)
    {
        var efCompany = await efCompanyRepository.GetById(companyId, ct);
        if (efCompany is null || string.IsNullOrEmpty(efCompany.LegacyId)) return null;

        var companyDto = await companyRepository.GetByCompanyId(efCompany.LegacyId, ct);
        if (companyDto is null) return null;

        var result = mapper.Map<CompanyDtoV2>(companyDto.MapAccountEntityToCompanyDto(efCompany));
        result.BlueTapeGuidCompanyId = companyId;
        return result;
    }

    public async Task<CompanyDtoV2[]?> GetByCompanyIdsV2(Guid[] companyIds, CancellationToken ct)
    {
        var efCompanies = await efCompanyRepository.GetByIds(companyIds, ct);
        if (efCompanies.Count == 0) return Array.Empty<CompanyDtoV2>();

        return await GetByCompanyIdsV2(efCompanies, ct);
    }

    public async Task<List<MigrateResultDto>> TryMigrateLegacyCompanyIdsAsync(string[] companyIds, CancellationToken ct)
    {
        var result = new List<MigrateResultDto>();
        var mongoCompanies = await GetByCompanyIds(companyIds, ct);
        if (mongoCompanies == null || mongoCompanies.Length == 0) return result;

        foreach (var company in mongoCompanies)
        {
            var temp = await efCompanyRepository.TryMigrateLegacyCompanyIdAsync(company.BlueTapeCompanyId, ct);
            result.Add(new MigrateResultDto
            {
                LegacyId = temp.LegacyId!,
                NewId = temp.Id
            });
        }

        return result;
    }

    public async Task TryMigrateAllLegacyCompanyIdsAsync(CancellationToken ct)
    {
        var legacyCompanyIds = await companyRepository.GetAllIds(ct);
        if (legacyCompanyIds.Count == 0) return;

        await efCompanyRepository.TryMigrateAllLegacyCompanyIdsAsync(legacyCompanyIds.ToArray(), ct);
    }

    public async Task UpdateAionSettings(Guid companyId, CompanyAionSettingsDto companyAionSettingsDto,
        CancellationToken ct)
    {
        var efCompany = await efCompanyRepository.GetById(companyId, ct);
        if (efCompany == null || string.IsNullOrEmpty(efCompany.LegacyId))
            throw new CompanyDoesNotExistException(companyId.ToString());

        var mongoCompany = await companyRepository.GetByCompanyId(efCompany.LegacyId, ct);
        if (mongoCompany is null) throw new CompanyDoesNotExistException(efCompany.LegacyId);

        mongoCompany.AionSettings = companyAionSettingsDto;
        await companyRepository.UpdateAionSettingsAsync(mongoCompany, ct);
    }

    public async Task<CompanyDtoV2[]?> Get(bool? validAccountsOnly, CancellationToken ct)
    {
        var companyEntities =
            await efCompanyRepository.Get(ct, validAccountsOnly: validAccountsOnly.GetValueOrDefault());

        return await GetByCompanyIdsV2(companyEntities, ct);
    }

    public async Task<ResultWithPaginationDto<CompanyDtoV2>> GetWithPagination(int pageSize, int pageNumber,
        bool? validAccountsOnly, CancellationToken ct)
    {
        var companyEntities =
            await efCompanyRepository.GetWithPagination(pageSize, pageNumber, ct,
                validAccountsOnly.GetValueOrDefault());
        var companyDtos = await GetByCompanyIdsV2(companyEntities.Result.ToList(), ct);

        var result = mapper.Map<ResultWithPaginationDto<CompanyDtoV2>>(companyEntities);
        result.Result = companyDtos!;

        return result;
    }

    public async Task<CompanyDtoV2[]?> GetAllByAccountStatuses([FromBody] AccountStatusEnum[] statuses,
        CancellationToken ct)
    {
        var companyEntities = await efCompanyRepository.Get(ct, statuses: statuses);

        return await GetByCompanyIdsV2(companyEntities, ct);
    }

    private async Task<CompanyDtoV2[]?> GetByCompanyIdsV2(List<CompanyEntity> companyEntities, CancellationToken ct)
    {
        var mongoIds = companyEntities
            .Where(x => !string.IsNullOrEmpty(x.LegacyId))
            .Select(x => x.LegacyId)
            .ToArray();

        var mongoCompanies = await companyRepository.GetByCompanyIds(mongoIds!, ct);
        if (mongoCompanies == null || mongoCompanies.Length == 0) return Array.Empty<CompanyDtoV2>();

        return mongoCompanies.Select(comp =>
        {
            var efCompany = companyEntities.Find(y => comp.BlueTapeCompanyId.Equals(y.LegacyId));
            var compV2 = mapper.Map<CompanyDtoV2>(comp.MapAccountEntityToCompanyDto(efCompany));

            compV2.BlueTapeGuidCompanyId = efCompany?.Id;

            return compV2;
        }).ToArray();
    }

    public async Task<CompanyDto?> CreateCompany(CreateCompanyDto createCompanyDto, CancellationToken cancellationToken)
    {
        var companyDto = await companyRepository.CreateCompany(createCompanyDto, cancellationToken);
        if (companyDto != null)
            await efCompanyRepository.TryMigrateLegacyCompanyIdAsync(companyDto.BlueTapeCompanyId, cancellationToken);
        return companyDto;
    }

    public async Task<CompanyDto?> UpdateCompany(string id, string userId, UpdateCompanyDto updateDto,
        CancellationToken cancellationToken)
    {
        var companyDto = await companyRepository.GetByCompanyId(id, cancellationToken);
        if (companyDto == null) throw new CompanyDoesNotExistException(id);

        var changePricingPackage = updateDto.Settings?.LoanPricingPackageId != null
                                   && updateDto.Settings?.LoanPricingPackageId !=
                                   companyDto.Settings?.LoanPricingPackageId;

        companyDto = await companyRepository.UpdateCompany(id, updateDto, cancellationToken);

        if (companyDto != null && changePricingPackage)
        {
            await AddNoteInternally(companyDto.BlueTapeCompanyId, userId, new CreateCompanyNoteDto
            {
                Note = $"Trade Credit Package was updated by {userId}"
            }, false, cancellationToken);
        }

        return companyDto;
    }

    public async Task<CompanyNoteDto?> AddNote(string companyId, string userId, CreateCompanyNoteDto dto,
        CancellationToken cancellationToken)
    {
        return await AddNoteInternally(companyId, userId, dto, true, cancellationToken);
    }

    private async Task<CompanyNoteDto?> AddNoteInternally(
        string companyId,
        string userId, CreateCompanyNoteDto dto,
        bool manually = false,
        CancellationToken cancellationToken = default)
    {
        var efCompany = await efCompanyRepository.GetByLegacyId(companyId, cancellationToken);
        if (efCompany == null) throw new CompanyDoesNotExistException(companyId);

        var note = new CompanyNoteEntity
        {
            CompanyId = efCompany.Id,
            CreatedBy = userId,
            CreatedAt = DateTime.UtcNow,
            //this is necessary so that you can track in the general list of records that this not was created manually
            UpdatedBy = manually ? null : userId,
            Note = dto.Note
        };
        note = await efCompanyNoteRepository.Add(note, cancellationToken);
        return mapper.Map<CompanyNoteDto>(note);
    }
}