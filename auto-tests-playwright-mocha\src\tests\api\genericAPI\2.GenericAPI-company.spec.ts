import {expect} from '@playwright/test';
import {test} from '../../test-utils';
import {
    sendGenericApiRequest,
    generateCompanyRequestBody,
    createCustomer,
    createCompany,
    linkCompanyAndCustomer,
    updateCompany
} from '../../../api/common/send-generic-api-request';
import {BaseTest} from '../../test-utils';

const constantsError = JSON.parse(JSON.stringify(require('../../../constants/generic-api-errors.json')));

test.describe(`Company tests @generic @API`, async () => {

    let uniquePhone: string;
    let companyId: string;
    let currentDate: string;
    let customerId: string;

    test.beforeAll(async () => {
        currentDate = new Date().toISOString();
    });

    test.beforeEach(async () => {
        uniquePhone = BaseTest.getCellPhoneNumber();
        currentDate = new Date().toISOString();
    });

    test(`Create Company`, async () => {
        companyId = BaseTest.dateTimePrefix();
        const requestBody = await generateCompanyRequestBody(companyId, uniquePhone, currentDate);
        const response = await sendGenericApiRequest('post', `company`, requestBody);

        expect(await response.status, `Status code 201`).toEqual(201);

        expect(await response.data,
            `Response contains Object with BlueTapeID`).toEqual(expect.any(Object));

        expect(typeof response.data.blueTapeId).toEqual('string');
    });

    test(`Cannot create Company with non-unique ID`, async () => {
        const requestBody = await generateCompanyRequestBody(companyId || 'string', uniquePhone, currentDate);
        const response = await sendGenericApiRequest('post', `company`, requestBody);

        expect(response.response.status, `Status code 400`).toEqual(400);

        expect(response.response.data,
            `Response contains Array of Template's Versions`).toEqual(expect.any(Object));

        expect(response.response.data[0].code,
            `Error message: ${constantsError.customer.code.errorMessageNonUnicId}`)
            .toEqual(constantsError.customer.code.errorMessageNonUnicId);
    });

    test(`Cannot create Company with empty 'id' field`, async () => {
        const requestBody = await generateCompanyRequestBody('', uniquePhone, currentDate);
        const response = await sendGenericApiRequest('post', `company`, requestBody);

        expect(response.response.status, `Status code 400`).toEqual(400);

        expect(response.response.data,
            `Response contains Array with Error Object`).toEqual(expect.any(Array));

        expect(response.response.data[0].code,
            `Error Code: ${constantsError.customer.code.errorMessageInvalidData}`)
            .toEqual(constantsError.customer.code.errorMessageInvalidData);

        expect(response.response.data[0].reason,
            `Error Reason: ${constantsError.company.reason.emptyId}`)
            .toEqual(constantsError.company.reason.emptyId);
    });

    test(`Cannot create Company with null in id field`, async () => {
        const requestBody = await generateCompanyRequestBody(null, uniquePhone, currentDate);
        const response = await sendGenericApiRequest('post', `company`, requestBody);

        expect(await response.response.status, `Status code 400`).toEqual(400);

        expect(await response.response.data,
            `Response contains Array with Error Object`).toEqual(expect.any(Array));

        expect(await response.response.data[0].code,
            `Error Code: ${constantsError.company.code.nullInIdField}`)
            .toEqual(constantsError.company.code.nullInIdField);

        expect(await response.response.data[0].reason,
            `Error Reason: ${constantsError.company.reason.companyIdNull}`)
            .toEqual(constantsError.company.reason.companyIdNull);
    });

    const phoneNumberArray: string[] = ['**********-3297', 'string', '441234567899'];

    for (const invalidPhoneNumber of phoneNumberArray) {
        test(`Cannot create Company with invalid Phone Number ${invalidPhoneNumber}`, async () => {
            companyId = BaseTest.dateTimePrefix();
            const requestBody = await generateCompanyRequestBody(companyId, invalidPhoneNumber, currentDate);
            const response = await sendGenericApiRequest('post', `company`, requestBody);

            expect(response.response.status, `Status code 400`).toEqual(400);

            expect(response.response.data,
                `Response contains Array with Error Object`).toEqual(expect.any(Array));

            expect(response.response.data[0].code,
                `Error Code: ${constantsError.company.code.invalidPhone}`)
                .toEqual(constantsError.company.code.invalidPhone);

            expect(response.response.data[0].reason,
                `Error Reason: BusinessPhoneNumber is not valid. Value: ${invalidPhoneNumber}`)
                .toContain(`BusinessPhoneNumber is not valid. Value:`);
        });
    }

    test(`Link Customer with Company`, async () => {
        customerId = `customerId${BaseTest.dateTimePrefix()}`;
        companyId = `companyId${BaseTest.dateTimePrefix()}`;
        await createCustomer(null, null, customerId);
        await createCompany(companyId);
        const response = await linkCompanyAndCustomer(companyId, customerId);

        expect(response.status, `Status code 201`).toEqual(201);

        expect(response.data,
            `Response contains Object`).toEqual(expect.any(Object));
    });

    test(`Cannot link Customer with 2 and more companies`, async () => {
        const response = await linkCompanyAndCustomer(companyId, customerId);

        expect(response.response.status, `Status code 400`).toEqual(400);

        expect(response.response.data[0].code,
            `Error Code: ${constantsError.company.code.changeCompany}`)
            .toEqual(constantsError.company.code.changeCompany);

        expect(response.response.data[0].reason,
            `Error Reason: Customer with id: "${customerId}" can not change company`)
            .toEqual(`Customer with id: "${customerId}" can not change company`);
    });

    test(`Cannot link Company to a non-existent Customer`, async () => {
        const nonExistentCustomer = 'test12345';
        const response = await linkCompanyAndCustomer(companyId, nonExistentCustomer);

        expect(response.response.status, `Status code 400`).toEqual(400);

        expect(response.response.data[0].code,
            `Error Code: ${constantsError.company.code.customerNonExistent}`)
            .toEqual(constantsError.company.code.customerNonExistent);

        expect(response.response.data[0].reason,
            `Customer with id: "${nonExistentCustomer}" does not exist.`)
            .toEqual(`Customer with id: "${nonExistentCustomer}" does not exist.`);
    });

    test(`Customer ID can't be null`, async () => {
        const response = await linkCompanyAndCustomer(companyId, null);

        expect(response.response.status, `Status code 400`).toEqual(400);

        expect(response.response.data[0].code,
            `Error Code: ${constantsError.company.code.nullInIdField}`)
            .toEqual(constantsError.company.code.nullInIdField);

        expect(response.response.data[0].reason,
            `Error reason: ${constantsError.company.reason.customerIdNull}`)
            .toEqual(constantsError.company.reason.customerIdNull);
    });

    // TODO FOR KATE
    //https://dev-api.bluetape.com/genericBthubService/swagger/index.html#/ 
    // PUT /integration/company/{companyId} - TESTS FOR THIS ENDPOINT
    // CHECK IF COMPANY CAN BE UPDATED WITH ATTACHED CUSTOMER

    // TESTS FOR GET /integration/customer/{customerId}/creditInfo

    test(`Update company with valid values`, async () => {
        const response = await updateCompany(companyId, uniquePhone, currentDate);

        expect(response.status, `Status code 202`).toEqual(202);

        expect(response.data,
            `Response contains Object with BlueTapeID`).toEqual(expect.any(Object));

        expect(typeof response.data.blueTapeId).toEqual('string');
    });

    test(`Cannot update company with non-existent company id `, async () => {
        const nonExistentCompany = BaseTest.dateTimePrefix();
        const response = await updateCompany(nonExistentCompany, uniquePhone, currentDate);

        expect(response.response.status, `Status code 400`).toEqual(400);

        expect(response.response.data[0].code,
            `Error Code: ${constantsError.company.code.customerNonExistent}`)
            .toEqual(constantsError.company.code.customerNonExistent);

        expect(response.response.data[0].reason,
            `Error reason: Customer with id: "${nonExistentCompany}" does not exist.`)
            .toEqual(`Customer with id: "${nonExistentCompany}" does not exist.`);
    });

    for (const invalidPhoneNumber of phoneNumberArray) {
        test(`Cannot update Company with invalid Phone Number ${invalidPhoneNumber}`, async () => {
            const response = await updateCompany(companyId, invalidPhoneNumber, currentDate);

            expect(response.response.status, `Status code 400`).toEqual(400);

            expect(response.response.data,
                `Response contains Array with Error Object`).toEqual(expect.any(Array));

            expect(response.response.data[0].code,
                `Error Code: ${constantsError.company.code.invalidPhone}`)
                .toEqual(constantsError.company.code.invalidPhone);

            expect(response.response.data[0].reason,
                `Error Reason: BusinessPhoneNumber is not valid. Value: ${invalidPhoneNumber}`)
                .toContain(`BusinessPhoneNumber is not valid. Value:`);
        });
    }

    test(`Get credit information of customer`, async () => {
        customerId = `customerId${BaseTest.dateTimePrefix()}`;
        companyId = `companyId${BaseTest.dateTimePrefix()}`;
        await createCustomer(null, null, customerId);
        await createCompany(companyId);
        await linkCompanyAndCustomer(companyId, customerId);
        const response = await sendGenericApiRequest('get', `customer/${customerId}/creditInfo`);

        expect(response.status, `Status code 200`).toEqual(200);

        expect(response.data,
            `Response contains Object`).toEqual(expect.any(Object));
    });
});
