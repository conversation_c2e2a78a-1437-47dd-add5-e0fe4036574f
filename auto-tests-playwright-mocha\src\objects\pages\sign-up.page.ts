import { Page } from '@playwright/test';
import {BasePage} from '../base.page';

const pageLocator = '"Create a free account"';

export class SignUpPage extends BasePage {
    constructor(page: Page, locator = pageLocator){
        super(page, locator); 
    };
    
    inputFields = {
        email: this.page.locator('[data-testid="signUp_email_input"]'),
        password: this.page.locator('[data-testid="password"]'),
        firstName: this.page.locator('[data-testid="firstName"]'),
        lastName: this.page.locator('[data-testid="lastName"]'),
        cellPhoneNumber: this.page.locator('[data-testid="emailPhone"]'),
        businessName: this.page.locator('[data-testid="businessNameInput"]'),
    };

    buttons = {
        signUpEmail: this.page.locator('[data-testid="signUp_with_email_link"]'),
        signUpPhone: this.page.locator('[data-testid="signUp_with_phone_link"]'),
        agreeContinue: this.page.locator('"Agree & Continue"'),
        signUp: this.page.locator('"Sign Up"'),
        continue: this.page.locator('"Continue"'),
        logIn: this.page.locator('"Login"'),
    };

    async fillSignUpFields(email, password){
        await this.inputFields.email.fill(email);
        await this.buttons.agreeContinue.click();
        await this.inputFields.password.fill(password);
        await this.buttons.signUp.click();
    };

    async fillPersonalInformationFields(firstName, lastName, cellPhoneNumber, businessName){
        await this.inputFields.firstName.fill(firstName);
        await this.inputFields.lastName.fill(lastName);
        await this.inputFields.cellPhoneNumber.fill(cellPhoneNumber);
        await this.inputFields.businessName.fill(businessName);
        await this.buttons.continue.click();
    };
}