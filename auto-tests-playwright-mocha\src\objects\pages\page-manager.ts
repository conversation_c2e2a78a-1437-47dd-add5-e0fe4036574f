import {Page} from '@playwright/test';
import {InvoicesDetails} from '../components/pay/invoice/invoice-details';
import {InvoicesList} from '../components/pay/invoice/invoice-list';
import {CreditList} from '../components/pay/credit/credit-list';
import {NewInvoices} from '../components/pay/invoice/new-invoice';
import {SideMenu} from '../components/side-menu';
import {LoginPage} from './login.page';
import {SignUpPage} from './sign-up.page';
import {OnBoardingPage} from './onboarding.page';
import {NewPaymentMethodModal} from '../components/pay/invoice/modals/new-payment-method.modal';
import {PlaidModal} from '../components/pay/invoice/modals/plaid.modal';
import {FirstPlatypusBankModal} from '../components/pay/invoice/modals/first-platypus-bank.modal';
import {PayInvoiceSOModal} from '../components/pay/invoice/modals/pay-invoice-so.modal';
import {LinkBankAccount} from '../components/pay/invoice/link-bank-account';
import {SettingsTopMenu} from '../components/sales/settings/settings-top-menu';
import {UserManagement} from '../components/sales/settings/user-management';
import {AddUserModal} from '../components/sales/settings/modals/add-user.modal';
import {SalesTopMenu} from '../components/sales/sales-top-menu';
import {CustomersList} from '../components/sales/customers/customers-list';
import {AddCustomerModal} from '../components/sales/customers/modals/add-customer.modal';
import {AddInvoiceModal} from '../components/sales/customers/modals/add-invoice.modal';
import {CustomerDetailsModal} from '../components/sales/customers/modals/customer-details.modal';
import {LinkCard} from '../components/pay/invoice/link-card';
import {Home} from '../components/home/<USER>';
import {PartnershipApplication} from '../components/home/<USER>';
import {UnifiedApplication} from '../components/home/<USER>';
import {BuyNowPayLater} from '../components/home/<USER>';
import {BackOfficeLoginPage} from './back-office-login.page';
import {BackOfficeSideMenu} from '../back-office-components/back-office-side-menu';
import {SupplierApplication} from '../back-office-components/supplier-application/supplier-application';
import {SupplierApplicationDetailsModal} from '../back-office-components/supplier-application/modals/supplier-application-details.modlas';
import {ReceivablesInvoices} from '../components/sales/receivables/receivables-invoices';
import {ReceivablesInvoiceDetailsModal} from '../components/sales/receivables/modals/receivables-invoice-details.modal';
import {AddNewCardModal} from '../components/sales/customers/modals/add-new-card.modal';
import {CreditRequest} from '../components/pay/credit/credit-request';
import {ProfileDetails} from '../components/pay/profile/profile-details';
import {LoanApplications} from '../back-office-components/loan-applications/loan-applications';
import {Projects} from '../components/projects/projects';
import {ProjectPropertyAddress} from '../components/projects/add-new-project/project-property-address';
import {ProjectInformation} from '../components/projects/add-new-project/project-information';
import {ProjectType} from '../components/projects/add-new-project/project-type';
import {ProjectPropertyOwner} from '../components/projects/add-new-project/project-property-owner';
import {AddNewProjectModal} from '../components/projects/modals/add-new-project.modal';
import {ResultModal} from '../components/projects/modals/result.modal';
import {ApproveModal} from '../back-office-components/loan-applications/modals/approve.modal';
import {ApprovedLoanModal} from '../components/pay/invoice/modals/approved-loan.modal';
import {Loans} from '../back-office-components/loans/loans';
import {LoansStatusDetails} from '../back-office-components/loans/loan-status-details/loan-status-details';
import {EditPaymentScheduleModal} from '../back-office-components/loans/loan-status-details/modals/edit-payment-schedule.modal';
import {Payments} from '../back-office-components/payments/payments';
import {AddFeeModal} from '../back-office-components/loans/loan-status-details/modals/add-fee.modal';
import {CreditDetails} from '../components/pay/credit/credit-details';
import {MakePaymentModal} from '../components/pay/credit/modal/make-payment.modal';
import {WalletPage} from "./wallet.page";
import {NotificationsMain} from "../components/pay/notifications/notifications-main";
import {PayModal} from "../components/pay/pay-modal";
import {Suppliers} from "../back-office-components/company/suppliers";
import {IntegrationPaymentPage} from "./integrationPaymentPage";
import {DrawList} from "../components/trade-credit/draw/draw-list";
import {PayDrawModal} from "../components/trade-credit/draw/modal/pay-draw.modal";


export class PageManager {
    page: Page;

    loginPage: LoginPage;

    signUpPage: SignUpPage;

    onBoardingPage: OnBoardingPage;
    
    integrationPaymentPage: IntegrationPaymentPage;

    sideMenu: SideMenu;

    home: Home;

    partnershipApplication: PartnershipApplication;

    buyNowPayLater: BuyNowPayLater;

    unifiedApplication: UnifiedApplication;

    walletPage: WalletPage;

    notificationsMain: NotificationsMain;

    creditRequest: CreditRequest;

    invoicesList: InvoicesList;

    creditList: CreditList;

    newInvoice: NewInvoices;

    invoicesDetails: InvoicesDetails;

    linkBankAccount: LinkBankAccount;

    linkCard: LinkCard;

    creditDetails: CreditDetails;

    makePaymentModal: MakePaymentModal;

    //#region Invoice modals
    newPaymentMethodModal: NewPaymentMethodModal;

    plaidModal: PlaidModal;

    firstPlatypusBankModal: FirstPlatypusBankModal;

    payInvoiceSOModal: PayInvoiceSOModal;

    approvedLoanModal: ApprovedLoanModal;
    //#endregion

    //#SALES REGION
    salesTopMenu: SalesTopMenu;

    payModal: PayModal;

    customersList: CustomersList;

    //#Customers modals
    addCustomerModal: AddCustomerModal;

    addInvoiceModal: AddInvoiceModal;

    customerDetailsModal: CustomerDetailsModal;

    addNewCardModal: AddNewCardModal;
    //#end Customers modals

    receivablesInvoices: ReceivablesInvoices;

    //# region Receivables modals
    receivablesInvoiceDetailsModal: ReceivablesInvoiceDetailsModal;
    //# end region

    settingsTopMenu: SettingsTopMenu;

    userManagement: UserManagement;

    //#region Settings modals
    addUserModal: AddUserModal;
    //#endregion
    //#END SALES REGION

    // PROFILE REGION
    profileDetails: ProfileDetails;
    // END PROFILE REGION

    //#PROJECTS
    projects: Projects;

    projectPropertyAddress: ProjectPropertyAddress;

    projectInformation: ProjectInformation;

    projectType: ProjectType;

    projectPropertyOwner: ProjectPropertyOwner;

    //#projects modals
    addNewProjectModal: AddNewProjectModal;

    resultModal: ResultModal;
    //#end projects modals
    //#END PROJECTS

    //#BACK OFFICE REGION
    backOfficeLoginPage: BackOfficeLoginPage;

    backOfficeSideMenu: BackOfficeSideMenu;

    supplierApplication: SupplierApplication;

    supplierApplicationDetailsModal: SupplierApplicationDetailsModal;

    loanApplications: LoanApplications;

    approveModal: ApproveModal;

    loans: Loans;

    loansStatusDetails: LoansStatusDetails;

    editPaymentScheduleModal: EditPaymentScheduleModal;

    payments: Payments;

    addFeeModal: AddFeeModal;

    suppliers: Suppliers;
    //#END BACK OFFICE REGION

    drawList: DrawList;

    payDrawModal: PayDrawModal

    constructor(page: Page){
        this.page = page;
        this.loginPage = new LoginPage(page);
        this.signUpPage = new SignUpPage(page);
        this.onBoardingPage = new OnBoardingPage(page);
        this.integrationPaymentPage = new IntegrationPaymentPage(page);

        this.walletPage = new WalletPage(page);

        this.notificationsMain = new NotificationsMain(page);

        this.sideMenu = new SideMenu(page);
        this.home = new Home(page);
        this.partnershipApplication = new PartnershipApplication(page);
        this.buyNowPayLater = new BuyNowPayLater(page);
        this.unifiedApplication = new UnifiedApplication(page);

        this.creditRequest = new CreditRequest(page);

        this.invoicesList = new InvoicesList(page);
        this.creditList = new CreditList(page);
        this.newInvoice = new NewInvoices(page);
        this.invoicesDetails = new InvoicesDetails(page);
        this.linkBankAccount = new LinkBankAccount(page);
        this.linkCard = new LinkCard(page);
        this.creditDetails = new CreditDetails(page);
        this.makePaymentModal = new MakePaymentModal(page);
        //#region Invoice modals
        this.newPaymentMethodModal = new NewPaymentMethodModal(page);
        this.plaidModal = new PlaidModal(page);
        this.firstPlatypusBankModal = new FirstPlatypusBankModal(page);
        this.payInvoiceSOModal = new PayInvoiceSOModal(page);
        //#endregion

        //#SALES REGION
        this.salesTopMenu = new SalesTopMenu(page);
        this.customersList = new CustomersList(page);
        this.payModal = new PayModal(page);
        //#Customers modals
        this.addCustomerModal = new AddCustomerModal(page);
        this.addInvoiceModal = new AddInvoiceModal(page);
        this.customerDetailsModal = new CustomerDetailsModal(page);
        this.addNewCardModal = new AddNewCardModal(page);
        this.approvedLoanModal = new ApprovedLoanModal(page);
        //#end Customers modals

        this.receivablesInvoices = new ReceivablesInvoices(page);
        //# region Receivables modals
        this.receivablesInvoiceDetailsModal = new ReceivablesInvoiceDetailsModal(
            page
        );
        //# end region

        //#PROFILE REGION
        this.profileDetails = new ProfileDetails(page);
        //#end region

        //#SETTINGS REGION
        this.settingsTopMenu = new SettingsTopMenu(page);
        this.userManagement = new UserManagement(page);
        //#region Settings modals
        this.addUserModal = new AddUserModal(page);
        //#endregion
        //#END SALES REGION

        //#PROJECTS
        this.projects = new Projects(page);
        this.projectPropertyAddress = new ProjectPropertyAddress(page);
        this.projectInformation = new ProjectInformation(page);
        this.projectType = new ProjectType(page);
        this.projectPropertyOwner = new ProjectPropertyOwner(page);
        //#projects modals
        this.addNewProjectModal = new AddNewProjectModal(page);
        this.resultModal = new ResultModal(page);
        //#end projects modals
        //#END PROJECTS

        //#BACK OFFICE REGION
        this.backOfficeLoginPage = new BackOfficeLoginPage(page);
        this.backOfficeSideMenu = new BackOfficeSideMenu(page);
        this.supplierApplication = new SupplierApplication(page);
        this.supplierApplicationDetailsModal = new SupplierApplicationDetailsModal(page);
        this.loanApplications = new LoanApplications(page);
        this.approveModal = new ApproveModal(page);
        this.loans = new Loans(page);
        this.loansStatusDetails = new LoansStatusDetails(page);
        this.editPaymentScheduleModal = new EditPaymentScheduleModal(page);
        this.payments = new Payments(page);
        this.addFeeModal = new AddFeeModal(page);
        this.suppliers = new Suppliers(page);
        //#END BACK OFFICE REGION

        this.drawList = new DrawList(page);
        this.payDrawModal = new PayDrawModal(page);
    };
}
