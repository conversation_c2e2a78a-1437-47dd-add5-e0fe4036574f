openapi: 3.0.0
info:
  title: BlueTape Generic Connector API
  version: 1.1.0
  description: | 
    API definition for integrating suppliers
servers:
  - url: https://beta-api.bluetape.com/genericBthubService
    description: Sandbox server
  - url: https://api.bluetape.com/genericBthubService
    description: Production server
paths:
  /integration/company:
    post:
      tags:
        - Company
      summary: Create a company
      description: Creates a company.
      operationId: companyCreateCompany
      parameters:
        - name: X-BlueTape-Key
          description: BlueTape integration API key
          in: header
          required: true
          schema:
            type: string
        - name: X-Integration-AccountId
          description: Unique identifier received to track you in our system
          in: header
          required: true
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/CreateCompanyModel'
        required: true
      responses:
        201:
          description: Created, successful operation
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/CompanyServiceResult'
        400:
          description: Invalid request
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/CompanyServiceResult'
        401:
          description: Unauthorized
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/CompanyServiceResult'
        404:
          description: Not found
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/CompanyServiceResult'
        500:
          description: Unexpected error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/CompanyServiceResult'
  /integration/company/{id}:
    put:
      tags:
        - Company
      summary: Update a company
      description: Updates a company.
      operationId: companyUpdateCompany
      parameters:
        - name: X-BlueTape-Key
          description: BlueTape integration API key
          in: header
          required: true
          schema:
            type: string
        - name: X-Integration-AccountId
          description: Unique identifier received to track you in our system
          in: header
          required: true
          schema:
            type: string
        - name: id
          description: Identifier for the company to update
          in: path
          required: true
          schema:
            type: string
            nullable: true
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/UpdateCompanyModel'
        required: true
      responses:
        202:
          description: Accepted, successful operation
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/CompanyServiceResult'
        400:
          description: Invalid request
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/CompanyServiceResult'
        401:
          description: Unauthorized
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/CompanyServiceResult'
        404:
          description: Not found
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/CompanyServiceResult'
        500:
          description: Unexpected error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/CompanyServiceResult'
  /integration/company/{id}/customer:
    post:
      tags:
        - Company
      summary: Link company to a customer
      description: Links specified company to a customer.
      operationId: companyLinkCompanyToCustomer
      parameters:
        - name: X-BlueTape-Key
          description: BlueTape integration API key
          in: header
          required: true
          schema:
            type: string
        - name: X-Integration-AccountId
          description: Unique identifier received to track you in our system
          in: header
          required: true
          schema:
            type: string
        - name: id
          description: Identifier of the company for attach to.
          in: path
          required: true
          schema:
            type: string
            nullable: true
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/CustomerLinkModel'
        required: true
      responses:
        201:
          description: Successful operation
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/CompanyServiceResult'
        400:
          description: Invalid request
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/CompanyServiceResult'
        401:
          description: Unauthorized
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/CompanyServiceResult'
        404:
          description: Not found
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/CompanyServiceResult'
        500:
          description: Unexpected error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/CompanyServiceResult'
  /integration/customer/{id}/creditInfo:
    get:
      tags:
        - Customer
      summary: Get customer credit information
      description: Gets customer credit information.
      operationId: customerGetCustomerCreditInfo
      parameters:
        - name: X-BlueTape-Key
          description: BlueTape integration API key
          in: header
          required: true
          schema:
            type: string
        - name: X-Integration-AccountId
          description: Unique identifier received to track you in our system
          in: header
          required: true
          schema:
            type: string
        - name: id
          description: Identifier of the customer to get credit information
          in: path
          required: true
          schema:
            type: string
            nullable: true
      responses:
        200:
          description: Successful operation
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/BaseCreditModel'
        400:
          description: Invalid request
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/CustomerServiceResult'
        401:
          description: Unauthorized
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/CustomerServiceResult'
        404:
          description: Not found
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/CustomerServiceResult'
        500:
          description: Unexpected error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/CustomerServiceResult'
  /integration/customer:
    post:
      tags:
        - Customer
      summary: Create a customer
      description: Creates a customer.
      operationId: customerCreateCustomer
      parameters:
        - name: X-BlueTape-Key
          description: BlueTape integration API key
          in: header
          required: true
          schema:
            type: string
        - name: X-Integration-AccountId
          description: Unique identifier received to track you in our system
          in: header
          required: true
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/CreateIndividualCustomerModel'
        required: true
      responses:
        201:
          description: Created, successful operation
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/CustomerServiceResult'
        400:
          description: Invalid request
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/CustomerServiceResult'
        401:
          description: Unauthorized
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/CustomerServiceResult'
        404:
          description: Not found
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/CustomerServiceResult'
        500:
          description: Unexpected error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/CustomerServiceResult'
  /integration/customer/{id}:
    put:
      tags:
        - Customer
      summary: Update a customer
      description: Updates a customer.
      operationId: customerUpdateCustomer
      parameters:
        - name: X-BlueTape-Key
          description: BlueTape integration API key
          in: header
          required: true
          schema:
            type: string
        - name: X-Integration-AccountId
          description: Unique identifier received to track you in our system
          in: header
          required: true
          schema:
            type: string
        - name: id
          description: Identifier for the customer to update
          in: path
          required: true
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/UpdateIndividualCustomerModel'
        required: true
      responses:
        202:
          description: Accepted, successful operation
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/CustomerServiceResult'
        400:
          description: Invalid request
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/CustomerServiceResult'
        401:
          description: Unauthorized
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/CustomerServiceResult'
        404:
          description: Not found
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/CustomerServiceResult'
        500:
          description: Unexpected error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/CustomerServiceResult'
  /integration/invoice:
    post:
      tags:
        - Invoice
      summary: Create an invoice
      description: Creates an invoice.
      operationId: invoiceCreateInvoice
      parameters: 
        - name: X-BlueTape-Key
          description: BlueTape integration API key
          in: header
          required: true
          schema:
            type: string
        - name: X-Integration-AccountId
          description: Unique identifier received to track you in our system
          in: header
          required: true
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/CreateInvoiceModel'
        required: true
      responses:
        201:
          description: Created, successful operation
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/InvoiceServiceResult'
        400:
          description: Invalid request
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/InvoiceServiceResult'
        401:
          description: Unauthorized
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/InvoiceServiceResult'
        404:
          description: Not found
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/InvoiceServiceResult'
        500:
          description: Unexpected error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/InvoiceServiceResult'
  /integration/invoice/{id}:
    put:
      tags:
        - Invoice
      summary: Update an invoice
      description: Updates an invoice.
      operationId: invoiceUpdateInvoice
      parameters: 
        - name: X-BlueTape-Key
          description: BlueTape integration API key
          in: header
          required: true
          schema:
            type: string
        - name: X-Integration-AccountId
          description: Unique identifier received to track you in our system
          in: header
          required: true
          schema:
            type: string
        - name: id
          description: Identifier for the invoice to update
          in: path
          required: true
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/UpdateInvoiceModel'
        required: true
      responses:
        202:
          description: Accepted, successful operation
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/InvoiceServiceResult'
        400:
          description: Invalid request
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/InvoiceServiceResult'
        401:
          description: Unauthorized
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/InvoiceServiceResult'
        404:
          description: Not found
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/InvoiceServiceResult'
        500:
          description: Unexpected error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/InvoiceServiceResult'
  /integration/invoice/{id}/cancel:
    put:
      tags:
        - Invoice
      summary: Cancel an invoice
      description: Cancels an invoice.
      operationId: invoiceCancelInvoice
      parameters: 
        - name: X-BlueTape-Key
          description: BlueTape integration API key
          in: header
          required: true
          schema:
            type: string
        - name: X-Integration-AccountId
          description: Unique identifier received to track you in our system
          in: header
          required: true
          schema:
            type: string
        - name: id
          description: Identifier for the invoice to cancel
          in: path
          required: true
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/EmptyRequest'
        required: true
      responses:
        202:
          description: Accepted, successful operation
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/InvoiceServiceResult'
        400:
          description: Invalid request
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/InvoiceServiceResult'
        401:
          description: Unauthorized
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/InvoiceServiceResult'
        404:
          description: Not found
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/InvoiceServiceResult'
        500:
          description: Unexpected error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/InvoiceServiceResult'
  /integration/invoice/{id}/file:
    get:
      tags:
        - Invoice
      summary: Downloads file of an invoice
      description: Downloads file of an invoice
      operationId: downloadInvoiceFile
      parameters:
        - name: X-BlueTape-Key
          description: BlueTape integration API key
          in: header
          required: true
          schema:
            type: string
        - name: X-Integration-AccountId
          description: Unique identifier received to track you in our system
          in: header
          required: true
          schema:
            type: string
        - name: id
          description: Identifier of the invoice
          in: path
          required: true
          schema:
            type: string
      responses: 
        200:
          description: Successful operation
          content:
            application/pdf:
              schema:
                type: string
                format: binary
        401:
          description: Unauthorized
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/InvoiceServiceResult'
        404:
          description: Not found
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/InvoiceServiceResult'
        500:
          description: Unexpected error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/InvoiceServiceResult'
    post:
      tags:
        - Invoice
      summary: Uploads or replaces file of an invoice
      description: Uploads or replaces file of an invoice
      operationId: upsertInvoiceFile
      parameters:
        - name: X-BlueTape-Key
          description: BlueTape integration API key
          in: header
          required: true
          schema:
            type: string
        - name: X-Integration-AccountId
          description: Unique identifier received to track you in our system
          in: header
          required: true
          schema:
            type: string
        - name: id
          description: Identifier of the invoice to upload file for
          in: path
          required: true
          schema:
            type: string
      requestBody:
        content:
          application/pdf:
            schema:
              type: string
              format: binary
      responses:
        204:
          description: No content
        400:
          description: Invalid request
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/InvoiceServiceResult'
        401:
          description: Unauthorized
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/InvoiceServiceResult'
        404:
          description: Not found
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/InvoiceServiceResult'
        500:
          description: Unexpected error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/InvoiceServiceResult'
    delete:
      tags:
        - Invoice
      summary: Removes file of an invoice
      description: Removes file of an invoice
      operationId: deleteInvoiceFile
      parameters:
        - name: X-BlueTape-Key
          description: BlueTape integration API key
          in: header
          required: true
          schema:
            type: string
        - name: X-Integration-AccountId
          description: Unique identifier received to track you in our system
          in: header
          required: true
          schema:
            type: string
        - name: id
          description: Identifier of the invoice to delete file
          in: path
          required: true
          schema:
            type: string
      responses:
        200:
          description: Successful operation
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/InvoiceServiceResult'
        401:
          description: Unauthorized
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/InvoiceServiceResult'
        404:
          description: Not found
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/InvoiceServiceResult'
        500:
          description: Unexpected error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/InvoiceServiceResult'
  /integration/invoice/{id}/project/{projectId}:
    post:
      tags:
        - Invoice
      summary: Link invoice to project
      description: Links an invoice to a project
      operationId: invoiceLinkInvoiceToProject
      parameters: 
        - name: X-BlueTape-Key
          description: BlueTape integration API key
          in: header
          required: true
          schema:
            type: string
        - name: X-Integration-AccountId
          description: Unique identifier received to track you in our system
          in: header
          required: true
          schema:
            type: string
        - name: id
          description: Identifier for the invoice to attach
          in: path
          required: true
          schema:
            type: string
        - name: projectId
          description: Identifier for the project to attach
          in: path
          required: true
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/EmptyRequest'
        required: true
      responses:
        201:
          description: Created, successful operation
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/InvoiceServiceResult'
        400:
          description: Invalid request
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/InvoiceServiceResult'
        401:
          description: Unauthorized
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/InvoiceServiceResult'
        404:
          description: Not found
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/InvoiceServiceResult'
        500:
          description: Unexpected error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/InvoiceServiceResult'
    delete:
      tags:
        - Invoice
      summary: Unlink invoice from project
      description: Unlinks an invoice from a project
      operationId: invoiceUnLinkInvoiceFromProject
      parameters: 
        - name: X-BlueTape-Key
          description: BlueTape integration API key
          in: header
          required: true
          schema:
            type: string
        - name: X-Integration-AccountId
          description: Unique identifier received to track you in our system
          in: header
          required: true
          schema:
            type: string
        - name: id
          description: Identifier for the invoice to unattach
          in: path
          required: true
          schema:
            type: string
        - name: projectId
          description: Identifier for the project to unattach from
          in: path
          required: true
          schema:
            type: string
      responses:
        200:
          description: Successful operation
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/InvoiceServiceResult'
        400:
          description: Invalid request
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/InvoiceServiceResult'
        401:
          description: Unauthorized
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/InvoiceServiceResult'
        404:
          description: Not found
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/InvoiceServiceResult'
        500:
          description: Unexpected error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/InvoiceServiceResult'
  /integration/quote:
    post:
      tags:
        - Quote
      summary: Create a quote
      description: Creates a quote.
      operationId: quoteCreateQuote
      parameters: 
        - name: X-BlueTape-Key
          description: BlueTape integration API key
          in: header
          required: true
          schema:
            type: string
        - name: X-Integration-AccountId
          description: Unique identifier received to track you in our system
          in: header
          required: true
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/CreateQuoteModel'
        required: true
      responses:
        201:
          description: Created, successful operation
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/QuoteServiceResult'
        400:
          description: Invalid request
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/QuoteServiceResult'
        401:
          description: Unauthorized
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/QuoteServiceResult'
        404:
          description: Not found
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/QuoteServiceResult'
        500:
          description: Unexpected error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/QuoteServiceResult'
  /integration/quote/{id}:
    put:
      tags:
        - Quote
      summary: Update a quote
      description: Updates a quote.
      operationId: quoteUpdateQuote
      parameters:
        - name: X-BlueTape-Key
          description: BlueTape integration API key
          in: header
          required: true
          schema:
            type: string
        - name: X-Integration-AccountId
          description: Unique identifier received to track you in our system
          in: header
          required: true
          schema:
            type: string
        - name: id
          description: Identifier of the quote to update
          in: path
          required: true
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/UpdateQuoteModel'
        required: true
      responses:
        202:
          description: Accepted, successful operation
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/QuoteServiceResult'
        400:
          description: Invalid request
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/QuoteServiceResult'
        401:
          description: Unauthorized
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/QuoteServiceResult'
        404:
          description: Not found
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/QuoteServiceResult'
        500:
          description: Unexpected error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/QuoteServiceResult'
  /integration/quote/{id}/file:
    get:
      tags:
        - Quote
      summary: Downloads file of a quote
      description: Downloads file of a quote
      operationId: downloadQuoteFile
      parameters:
        - name: X-BlueTape-Key
          description: BlueTape integration API key
          in: header
          required: true
          schema:
            type: string
        - name: X-Integration-AccountId
          description: Unique identifier received to track you in our system
          in: header
          required: true
          schema:
            type: string
        - name: id
          description: Identifier of the quote
          in: path
          required: true
          schema:
            type: string
      responses: 
        200:
          description: Successful operation
          content:
            application/pdf:
              schema:
                type: string
                format: binary
        401:
          description: Unauthorized
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/QuoteServiceResult'
        404:
          description: Not found
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/QuoteServiceResult'
        500:
          description: Unexpected error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/QuoteServiceResult'
    post:
      tags:
        - Quote
      summary: Uploads or replaces file of a quote
      description: Uploads or replaces file of a quote
      operationId: upsertQuoteFile
      parameters:
        - name: X-BlueTape-Key
          description: BlueTape integration API key
          in: header
          required: true
          schema:
            type: string
        - name: X-Integration-AccountId
          description: Unique identifier received to track you in our system
          in: header
          required: true
          schema:
            type: string
        - name: id
          description: Identifier of the quote to upload file for
          in: path
          required: true
          schema:
            type: string
      requestBody:
        content:
          application/pdf:
            schema:
              type: string
              format: binary
      responses:
        204:
          description: No content
        400:
          description: Invalid request
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/QuoteServiceResult'
        401:
          description: Unauthorized
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/QuoteServiceResult'
        404:
          description: Not found
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/QuoteServiceResult'
        500:
          description: Unexpected error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/QuoteServiceResult'
    delete:
      tags:
        - Quote
      summary: Removes file of a quote
      description: Removes file of a quote
      operationId: deleteQuoteFile
      parameters:
        - name: X-BlueTape-Key
          description: BlueTape integration API key
          in: header
          required: true
          schema:
            type: string
        - name: X-Integration-AccountId
          description: Unique identifier received to track you in our system
          in: header
          required: true
          schema:
            type: string
        - name: id
          description: Identifier of the quote to delete file
          in: path
          required: true
          schema:
            type: string
      responses:
        200:
          description: Successful operation
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/QuoteServiceResult'
        401:
          description: Unauthorized
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/QuoteServiceResult'
        404:
          description: Not found
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/QuoteServiceResult'
        500:
          description: Unexpected error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/QuoteServiceResult'
  /integration/quote/{id}/project/{projectId}:
    post:
      tags:
        - Quote
      summary: Link quote to project
      description: Links a quote to a project
      operationId: quoteLinkQuoteToProject
      parameters:
        - name: X-BlueTape-Key
          description: BlueTape integration API key
          in: header
          required: true
          schema:
            type: string
        - name: X-Integration-AccountId
          description: Unique identifier received to track you in our system
          in: header
          required: true
          schema:
            type: string
        - name: id
          description: Identifier of the quote to attach
          in: path
          required: true
          schema:
            type: string
        - name: projectId
          description: Identifier for the project to attach
          in: path
          required: true
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/EmptyRequest'
        required: true
      responses:
        201:
          description: Created, successful operation
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/QuoteServiceResult'
        400:
          description: Invalid request
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/QuoteServiceResult'
        401:
          description: Unauthorized
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/QuoteServiceResult'
        404:
          description: Not found
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/QuoteServiceResult'
        500:
          description: Unexpected error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/QuoteServiceResult'
    delete:
      tags:
        - Quote
      summary: Unlink quote from project
      description: Unlinks a quote from a project
      operationId: quoteUnLinkQuoteFromProject
      parameters:
        - name: X-BlueTape-Key
          description: BlueTape integration API key
          in: header
          required: true
          schema:
            type: string
        - name: X-Integration-AccountId
          description: Unique identifier received to track you in our system
          in: header
          required: true
          schema:
            type: string
        - name: id
          description: Identifier of the quote to unattach
          in: path
          required: true
          schema:
            type: string
        - name: projectId
          description: Identifier of the project to unattach from
          in: path
          required: true
          schema:
            type: string
      responses:
        200:
          description: Successful operation
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/QuoteServiceResult'
        400:
          description: Invalid request
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/QuoteServiceResult'
        401:
          description: Unauthorized
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/QuoteServiceResult'
        404:
          description: Not found
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/QuoteServiceResult'
        500:
          description: Unexpected error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/QuoteServiceResult'                
  /integration/project:
    post:
      tags:
        - Project
      summary: Create a project
      description: Creates a project.
      operationId: projectCreateProject
      parameters:
        - name: X-BlueTape-Key
          description: BlueTape integration API key
          in: header
          required: true
          schema:
            type: string
        - name: X-Integration-AccountId
          description: Unique identifier received to track you in our system
          in: header
          required: true
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/CreateProjectModel'
        required: true
      responses:
        201:
          description: Created, successful operation
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ProjectServiceResult'
        400:
          description: Invalid request
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ProjectServiceResult'
        401:
          description: Unauthorized
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ProjectServiceResult'
        404:
          description: Not found
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ProjectServiceResult'
        500:
          description: Unexpected error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ProjectServiceResult'
  /integration/project/{id}:
    put:
      tags:
        - Project
      summary: Update a project
      description: Updates a project.
      operationId: projectUpdateProject
      parameters: 
        - name: X-BlueTape-Key
          description: BlueTape integration API key
          in: header
          required: true
          schema:
            type: string
        - name: X-Integration-AccountId
          description: Unique identifier received to track you in our system
          in: header
          required: true
          schema:
            type: string
        - name: id
          description: Identifier of the project to update
          in: path
          required: true
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/UpdateProjectModel'
        required: true
      responses:
        202:
          description: Accepted, successful operation
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ProjectServiceResult'
        400:
          description: Invalid request
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ProjectServiceResult'
        401:
          description: Unauthorized
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ProjectServiceResult'
        404:
          description: Not found
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ProjectServiceResult'
        500:
          description: Unexpected error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ProjectServiceResult'
  /webhook/quote/approval:
    post:
      tags:
        - Webhook
      summary: Approve or reject a quote
      description: Approves or rejects a quote.
      operationId: approveQuote
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/ApproveQuoteModel'
        required: true
      responses:
        200:
          description: Successful operation
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/IntegrationResult'
        400:
          description: Invalid request
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/IntegrationResult'
  /webhook/invoice/payment:
    post: 
      tags:
        - Webhook
      summary: Pay an invoice
      description: Pays an invoice, fully or partially.
      operationId: paymentInvoice
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/PaymentInvoiceModel'
        required: true
      responses:
        200:
          description: Successful operation
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/IntegrationResult'
        400:
          description: Invalid request
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/IntegrationResult'
components:
  schemas:
    ServiceResult:
      type: object
      additionalProperties: false
      properties:
        code:
          type: string
          nullable: true
          description: Unique error code. If is no error then is missing.
          example: 400
        reason:
          type: string
          nullable: true
          description: Describes the reason of the error.  If is no error then is missing.
          example: Error message.
    CompanyServiceResult:
      allOf:
        - type: object
          additionalProperties: false
          properties:
            blueTapeId:
              type: string
              nullable: true
              description: Company identifier in BlueTape.
              example: fb5637b2e5f3
        - $ref: '#/components/schemas/ServiceResult'
    CustomerServiceResult:
      allOf:
        - type: object
          additionalProperties: false
          properties:
            blueTapeId:
              type: string
              nullable: true
              description: Customer identifier in BlueTape.
              example: fb5637b2e5f3
        - $ref: '#/components/schemas/ServiceResult'
    InvoiceServiceResult:
      allOf:
        - type: object
          additionalProperties: false
          properties:
            blueTapeId:
              type: string
              nullable: true
              description: Invoice identifier in BlueTape.
              example: fb5637b2e5f3
        - $ref: '#/components/schemas/ServiceResult'
    QuoteServiceResult:
      allOf:
        - type: object
          additionalProperties: false
          properties:
            blueTapeId:
              type: string
              nullable: true
              description: Quote identifier in BlueTape.
              example: fb5637b2e5f3
        - $ref: '#/components/schemas/ServiceResult'
    ProjectServiceResult:
      allOf:
        - type: object
          additionalProperties: false
          properties:
            blueTapeId:
              type: string
              nullable: true
              description: Project identifier in BlueTape.
              example: fb5637b2e5f3
        - $ref: '#/components/schemas/ServiceResult'
    CreateCompanyModel:
      allOf:
        - type: object
          additionalProperties: false
          properties:
            id:
              type: string
              nullable: true
              description: Identifier for the company.
              example: a0b9cedc32ce
        - $ref: '#/components/schemas/UpdateCompanyModel'
    UpdateCompanyModel:
      allOf:
        - $ref: '#/components/schemas/BaseCompanyModel'
        - type: object
          additionalProperties: false
          required: 
            - sourceModifiedDate
          properties:
            sourceModifiedDate:
              type: string
              format: date-time
              description: Date the record was last changed in the accounting service provider.
              example: 2023-01-04T09:47:57.477Z
    BaseCompanyModel:
      type: object
      additionalProperties: false
      required: 
        - businessAddress
        - businessName
      properties:
        businessAddress:
          type: string
          nullable: true
          description: The company address.
          example: 1400 24th Ave, San Francisco, CA 94122, USA
        businessPhoneNumber:
          type: string
          nullable: true
          description: Phone number of the company.
          example: '+***********'
        businessName:
          type: string
          nullable: true
          description: Name of the company.
          example: Acme Inc.
        businessDisplayName:
          type: string
          nullable: true
          description: Company display name.
          example: Acme
        creditDetails:
          $ref: "#/components/schemas/CreditDetailsModel"
          description: The company's credit details
    BaseCreditModel:
      type: object
      additionalProperties: false
      properties:
        limit:
          type: number
          format: decimal
          description: The company approved credit limit.
          example: 1000
        balance:
          type: number
          format: decimal
          description: Available balance for the company.
          example: 5000
        pastDueAmount:
          type: number
          format: decimal
          description: Total past due amount for the company.
          example: 450
    CreateIndividualCustomerModel:
      allOf:
        - type: object
          additionalProperties: false
          required: 
            - id
          properties:
            id:
              type: string
              nullable: true
              description: Identifier for the customer.
              example: dc506b22c7fa
        - $ref: '#/components/schemas/UpdateIndividualCustomerModel'
    UpdateIndividualCustomerModel:
      type: object
      additionalProperties: false
      required: 
        - firstName
        - lastName
        - emailAddress
        - cellPhoneNumber
        - sourceModifiedDate
      properties:
        firstName:
          type: string
          nullable: true
          description: First name of the main contact for the customer / company.
          example: John
        lastName:
          type: string
          nullable: true
          description: Last name of the main contact for the customer / company.
          example: Doe
        emailAddress:
          type: string
          nullable: true
          description: Email address of the main contact for the customer / company.  This email address will be used to send the notifications out.
          example: <EMAIL>
        cellPhoneNumber:
          type: string
          nullable: true
          description: Cellphone number of the main contact for the customer / company. This phone number will be used to send the notifications out.
          example: '+***********'
        sourceModifiedDate:
          type: string
          format: date-time
          description: Date the record was last changed in the accounting service provider.
          example: 2023-01-04T09:47:57.477Z
    IndividualCustomerModel:
      allOf:
        - type: object
          additionalProperties: false
          properties:
            id:
              type: string
              nullable: true
              description: Identifier for the customer.
              example: dc506b22c7fa
            parentId:
              type: string
              nullable: true
              description: Parent identifier for the customer.
              example: 2d7e11d56137
            creditInfo:
              $ref: '#/components/schemas/BaseCreditModel'
        - $ref: '#/components/schemas/UpdateIndividualCustomerModel'
        - $ref: '#/components/schemas/BaseCompanyModel'
    CreateInvoiceModel:
      allOf:
        - type: object
          additionalProperties: false
          required: 
            - id
          properties:
            id:
              type: string
              nullable: true
              description: Identifier of the invoice.
              example: 2eb1acd98a86
        - $ref: '#/components/schemas/BaseInvoiceModel'
    UpdateInvoiceModel:
      allOf:
        - $ref: '#/components/schemas/InvoiceTransactionModel'
        - type: object
          additionalProperties: false
          required: 
            - invoiceNumber
            - amountDue
          properties:
            invoiceNumber:
              type: string
              description: Friendly reference for the invoice. If available, this appears in the file name of invoice attachments.
              example: INV/00003/2023
            amountDue:
              type: number
              format: decimal
              description: Amount outstanding on the invoice.
              example: 1100
    BaseInvoiceModel:
      allOf:
        - $ref: '#/components/schemas/InvoiceTransactionModel'
        - type: object
          additionalProperties: false
          required: 
            - invoiceNumber
            - amountDue
          properties:
            invoiceNumber:
              type: string
              description: Friendly reference for the invoice. If available, this appears in the file name of invoice attachments.
              example: INV/00003/2023
            quoteRefNumber:
              type: string
              nullable: true
              description: Reference to the quote.
              example: QUOT/0567/2022
            quoteId:
              type: string
              nullable: true
              description: Reference to the quote.
              example: cbabdcfecfeb
            amountDue:
              type: number
              format: decimal
              description: Amount outstanding on the invoice.
              example: 1100
    InvoiceTransactionModel:
      type: object
      additionalProperties: false
      required: 
        - customerId
        - invoiceDate
        - dueDate
        - expirationDate
        - subTotal
        - totalAmount
        - taxAmount
        - status
        - sourceModifiedDate
      properties:
        customerId:
          type: string
          nullable: true
          description: Reference to the customer the invoice has been issued to.
          example: 7ba4877cbf52
        invoiceDate:
          type: string
          format: date
          description: Date of the invoice as recorded in the accounting service provider.
          example: 2023-01-04
        dueDate:
          type: string
          format: date
          description: Date the invoice is due to be paid by.
          example: 2023-01-12
        expirationDate:
          type: string
          format: date
          description: Expiration date of the invoice.
          example: 2023-01-12
        subTotal:
          type: number
          format: decimal
          description: Value of the invoice, including discounts and excluding tax.
          example: 1000
        totalAmount:
          type: number
          format: decimal
          description: Amount of the invoice, inclusive of tax.
          example: 1100
        taxAmount:
          type: number
          format: decimal
          description: Any tax applied to the invoice amount.
          example: 100
        status:
          type: string
          enum:
            - open
            - draft
            - paid
            - partiallyPaid
            - unknown
            - void
          nullable: true
          description: Status of the invoice.
          example: open
        lines:
          type: array
          nullable: true
          description: An array of invoice lines.
          items:
            $ref: '#/components/schemas/LineModel'
        sourceModifiedDate:
          type: string
          format: date-time
          description: Date the record was last changed in the accounting service provider.
          example: 2023-01-04T09:47:57.477Z
    LineModel:
      type: object
      additionalProperties: false
      required: 
        - unitAmount
        - quantity
        - subTotal
        - taxAmount
        - totalAmount
      properties:
        description:
          type: string
          nullable: true
          description: Friendly name of the goods or services received.
          example: Item 1
        unitAmount:
          type: number
          format: decimal
          description: Price of each unit of goods or services.
          example: 500
        quantity:
          type: integer
          format: int32
          description: Number of units of goods or services.
          example: 2
        subTotal:
          type: number
          format: decimal
          description: Amount of the line, inclusive of discounts but exclusive of tax.
          example: 1000
        taxAmount:
          type: number
          format: decimal
          description: Amount of tax for the line.
          example: 100
        totalAmount:
          type: number
          format: decimal
          description: Total amount of the line, inclusive of discounts and tax.
          example: 1100
    CreateQuoteModel:
      allOf:
        - type: object
          additionalProperties: false
          required: 
            - id
          properties:
            id:
              type: string
              nullable: true
              description: Identifier of the quote.
              example: cbabdcfecfeb
        - $ref: '#/components/schemas/UpdateQuoteModel'
    UpdateQuoteModel:
      allOf:
        - $ref: '#/components/schemas/QuoteTransactionModel'
        - type: object
          additionalProperties: false
          required: 
            - quoteNumber
          properties:
            quoteNumber:
              type: string
              nullable: true
              description: Friendly reference for the quote. If available, this appears in the file name of quote attachments.
              example: QUOT/0567/2022
    QuoteTransactionModel:
      type: object
      additionalProperties: false
      required: 
        - customerId
        - quoteDate
        - dueDate
        - expirationDate
        - subTotal
        - totalAmount
        - taxAmount
        - sourceModifiedDate
      properties:
        customerId:
          type: string
          nullable: true
          description: Reference to the customer the quote has been issued to.
          example: 7ba4877cbf52
        quoteDate:
          type: string
          format: date
          description: Date of the quote as recorded in the accounting service provider.
          example: 2023-01-04
        dueDate:
          type: string
          format: date
          description: Date the quote is due to be paid by.
          example: 2023-01-12
        expirationDate:
          type: string
          format: date
          description: Expiration date of the quote.
          example: 2023-01-12
        subTotal:
          type: number
          format: decimal
          description: Value of the quote, including discounts and excluding tax.
          example: 1000
        totalAmount:
          type: number
          format: decimal
          description: Amount of the quote, inclusive of tax.
          example: 1100
        taxAmount:
          type: number
          format: decimal
          description: Any tax applied to the quote amount.
          example: 100
        lines:
          type: array
          nullable: true
          description: An array of quote lines.
          items:
            $ref: '#/components/schemas/LineModel'
        sourceModifiedDate:
          type: string
          format: date-time
          description: Date the record was last changed in the accounting service provider.
          example: 2023-01-04T09:47:57.477Z
    ApproveQuoteModel:
      type: object
      properties:
        quoteId:
          type: string
          nullable: false
          description: Unique identifier of the quote to be approved.
          example: a0b9cedc32ce
        result:
          type: string
          enum:
            - Approved
            - Rejected
          nullable: false
          description: Approval result.
          example: Approved
        creditInfo:
          $ref: '#/components/schemas/BaseCreditModel'
          description: The company credit information.
    IntegrationResult:
      type: object
    PaymentInvoiceModel:
      type: object
      properties: 
        invoiceId:
          type: string
          nullable: false
          description: Unique identifier of the invoice.
          example: 6c8f90e43e13
        amount:
          type: number
          format: decimal
          nullable: false
          description: Paid amount.
          example: 1100
        fee:
          type: number
          format: decimal
          nullable: false
          description: Fee amount.
          example: 0
        paymentMethod:
          type: string
          enum:
            - card
            - ach
            - loan
          nullable: false
          description: The applied payment method. Can be card, ach and loan.
          example: card
        status:
          type: string
          enum:
            - Paid
            - PartiallyPaid
          nullable: false
          description: Invoice payment status.
          example: paid
    CustomerLinkModel:
      type: object
      required: 
        - customerId
      properties: 
        customerId:
          type: string
          nullable: false
          description: Identifier of the customer which is attaching to company.
    CreditDetailsModel:
      type: object
      properties:
        accountType:
          type: string
          description: The account type
          enum:
            - credit
            - cash
          example: credit
        accountCreationDate:
          type: string
          format: datetime
          description: The account creation date
          example: 2023-03-09T00:10:20.300Z
        pricePackage:
          type: string
          description: The price package of business
          example: A
        creditProvider:
          type: string
          description: The credit provider
          enum:
            - inhouse
            - co
          example: inhouse
        creditStatus:
          type: string
          description: The actual credit status
          enum:
            - active
            - suspended
            - closed
          example: active
        creditAmount:
          type: number
          description: Overall credit amount
          example: 10000
        outstandingAmount:
          type: number
          description: The outstanding credit amount (current balance)
          example: 3000
        overdueBalance:
          type: number
          description: The overall overdue amount
          example: 500
        agingOverdueBalance:
          type: array
          items:
            $ref: '#/components/schemas/AgingOverdueBalance'
        invoicesOverallAmount:
          type: number
          description: Overall amount of project's invoices
          example: 7000
        invoicesOverallCount:
          type: integer
          format: int32
          description: Overall number of project's invoices
          example: 58
        invoicesLastYearAmount:
          type: number
          description: Amount of project's invoices last year.
          example: 5000
        invoicesLastYearCount:
          type: integer
          format: int32
          description: Number of invoices last year.
          example: 33
        invoicesYearToDateAmount:
          type: number
          description: Amount of invoices in this year until current date.
          example: 2000
        invoicesYearToDateCount:
          type: integer
          format: int32
          description: Number of invoices in this year until current date.
          example: 25
        invoicesLastMonthAmount:
          type: number
          description: Amount of invoices last month
          example: 300
        invoicesLastMonthCount:
          type: integer
          format: int32
          description: Number of invoices last month
          example: 5
        invoicesMonthToDateAmount:
          type: number
          description: Amount of invoices in this month until current date.
          example: 500
        invoicesMonthToDateCount:
          type: integer
          format: int32
          description: Number of invoices in this month until current date.
          example: 2
        invoicesMonthlyAverageAmount:
          type: number
          description: Average amount of invoices per month
          example: 888.33
        invoicesMonthlyAverageCount:
          type: number
          description: Average number of invoices per month
          example: 5.7
    BaseProjectModel:
      type: object
      properties:
        name:
          type: string
          description: Project name
          example: A new huge building
        address:
          type: string
          description: Project address
          example: 285 Fulton St, New York, NY 10007, United States
        creditAmount:
          type: number
          description: The total amount of contracted credit for the project
          example: 10000
        status:
          type: string
          description: Actual status
          enum:
            - active
            - inactive
          example: active
    CreateProjectModel:
      allOf:
        - type: object
          additionalProperties: false
          properties:
            id:
              type: string
              nullable: true
              description: Identifier for the project.
              example: a0b9cedc32ce
        - $ref: '#/components/schemas/UpdateProjectModel'
    UpdateProjectModel:
      allOf:
        - $ref: '#/components/schemas/BaseProjectModel'
        - type: object
          additionalProperties: false
          required: 
            - sourceModifiedDate
          properties:
            sourceModifiedDate:
              type: string
              format: date-time
              description: Date the record was last changed in the accounting service provider.
              example: 2023-01-04T09:47:57.477Z
    ProjectModel:
      allOf:
        - $ref: '#/components/schemas/CreateProjectModel'
        - type: object
          properties:
            invoiceIds:
              type: array
              description: The list of attached invoices ids.
              items:
                type: string
            quoteIds:
              type: array
              description: The list of attached quotes ids.
              items:
                type: string
    AgingOverdueBalance:
      type: object
      required: 
        - daysFrom
        - overdueBalance
      properties:
        name:
          type: string
          description: Description of aging range (eg. 1-30 days)
          example: 1-30 days
        daysFrom:
          type: integer
          format: int32
          description: Aging range start days
          example: 1
        daysTo:
          type: integer
          format: int32
          description: Aging range end days. If not set, consider as infinite.
          example: 30
        overdueBalance:
          type: number
          example: 3000
          description: Overdue balance for this period.
    EmptyRequest:
      type: object
      nullable: true
  securitySchemes:
    ApiKey:
      type: apiKey
      in: header
      name: X-BlueTape-Key
      description: Unique secret key to protect our API-s
security:
  - ApiKey: []
