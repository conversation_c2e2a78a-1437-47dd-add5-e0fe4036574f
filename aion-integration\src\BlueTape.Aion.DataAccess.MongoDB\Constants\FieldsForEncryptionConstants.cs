﻿namespace BlueTape.Aion.DataAccess.MongoDB.Constants;

internal static class FieldsForEncryptionConstants
{
    public static readonly List<FieldMaskInfo> MaskOutFields = new()
    {
        new FieldMaskInfo("userId", FieldMaskType.FullHide),
        new FieldMaskInfo("password", FieldMaskType.FullHide),
        new FieldMaskInfo("apiKey", FieldMaskType.FullHide),
        new FieldMaskInfo("secCode", FieldMaskType.FullHide),
        new FieldMaskInfo("accountNumber", FieldMaskType.LastDigits),
        new FieldMaskInfo("routingNumber", FieldMaskType.LastDigits),
        new FieldMaskInfo("authToken", FieldMaskType.FullHide),
        new FieldMaskInfo("FromAccountNumber", FieldMaskType.LastDigits),
        new FieldMaskInfo("fromAccountNumber", FieldMaskType.LastDigits),
        new FieldMaskInfo("AccountNumber", FieldMaskType.LastDigits),
        new FieldMaskInfo("RoutingNumber", FieldMaskType.LastDigits),
    };
}