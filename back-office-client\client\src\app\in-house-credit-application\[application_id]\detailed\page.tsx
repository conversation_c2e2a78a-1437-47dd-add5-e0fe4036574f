'use client'

import { Spin } from 'antd'
import type { BreadcrumbItemType } from 'antd/es/breadcrumb/Breadcrumb'
import { useParams } from 'next/navigation'
import { useMemo, useEffect } from 'react'
import { useTranslation } from 'react-i18next'

import Detail from '@/app/in-house-credit-application/[application_id]/detailed/_components/Detail'
import CentredSpinner from '@/components/common/CentredSpinner'
import PageWrapper from '@/components/common/PageWrapper'
import { AppRoutes } from '@/globals/routes'
import { useInHouseCreditApplicationQuery } from '@/lib/redux/api/in-house-credit-application'
import { getCompanyNameWithDBA } from '@/globals/utils'
import { useAppDispatch } from '@/lib/redux/hooks'
import { creditApplicationDraftApi } from '@/lib/redux/api/credit-application-draft'

const InHouseCreditApplicationDetailedPage = (): JSX.Element => {
  const { t } = useTranslation()
  const router = useParams()
  const applicationId = router.application_id as string

  const { data, isFetching, isLoading, isSuccess } =
    useInHouseCreditApplicationQuery({ id: applicationId })

  const dispatch = useAppDispatch()

  useEffect(() => {
    dispatch(
      creditApplicationDraftApi.util.prefetch(
        'creditApplicationDraft',
        { applicationId },
        {},
      ),
    )
  }, [applicationId, dispatch])

  const breadcrumbItems = useMemo(() => {
    const items: BreadcrumbItemType[] = [
      {
        href: AppRoutes.inHouseCreditApplication.main(),
        title: t('menu.inHouseCreditApplication'),
      },
    ]

    if (data) {
      items.push({
        title: getCompanyNameWithDBA(data, t),
      })
    }

    return items
  }, [t, data])

  return (
    <PageWrapper breadcrumbItems={breadcrumbItems}>
      {isLoading || !isSuccess ? (
        <CentredSpinner />
      ) : (
        <Spin spinning={isFetching} delay={0} size="large">
          <Detail data={data} />
        </Spin>
      )}
    </PageWrapper>
  )
}

export default InHouseCreditApplicationDetailedPage
