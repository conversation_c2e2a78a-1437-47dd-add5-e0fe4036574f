{"info": {"_postman_id": "741bc62f-e3d3-454c-b323-b17c65b9b3a5", "name": "PaymentService", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json", "_exporter_id": "33448278"}, "item": [{"name": "Transactions", "item": [{"name": "GetTransactionHistory", "event": [{"listen": "test", "script": {"exec": ["const response = pm.response.json();\r", "\r", "var template = `\r", "<style type=\"text/css\">\r", "    .tftable {font-size:14px;color:#333333;width:100%;border-width: 1px;border-color: #87ceeb;border-collapse: collapse;}\r", "    .tftable th {font-size:18px;background-color:#87ceeb;border-width: 1px;padding: 8px;border-style: solid;border-color: #87ceeb;text-align:left;}\r", "    .tftable tr {background-color:#ffffff;}\r", "    .tftable td {font-size:14px;border-width: 1px;padding: 8px;border-style: solid;border-color: #87ceeb;}\r", "    .tftable tr:hover {background-color:#e0ffff;}\r", "</style>\r", "\r", "<table class=\"tftable\" border=\"1\">\r", "    <tr>\r", "        <th>Transaction ID</th>\r", "        <th>From</th>\r", "        <th>To</th>\r", "        <th>Result Code</th>\r", "        <th>Result Text</th>\r", "        <th>Executed At</th>\r", "    </tr>\r", "    {{#each response}}\r", "    <tr>\r", "        <td>{{transactionId}}</td>\r", "        <td>{{oldStatus}}</td>\r", "        <td>{{newStatus}}</td>\r", "        <td>{{resultCode}}</td>\r", "        <td>{{resultText}}</td>\r", "        <td>{{executedAt}}</td>\r", "    </tr>\r", "    {{/each}}\r", "</table>\r", "`;\r", "\r", "function constructVisualizerPayload() {\r", "    var response = pm.response.json();\r", "    return {response: response}\r", "}\r", "\r", "pm.visualizer.set(template, constructVisualizerPayload());"], "type": "text/javascript"}}], "request": {"method": "GET", "header": [], "url": {"raw": "{{PaymentServiceUrl}}/transactions/b68769b7-368e-4a35-8235-76bb669d7251/payment-transaction-history", "host": ["{{PaymentServiceUrl}}"], "path": ["transactions", "b68769b7-368e-4a35-8235-76bb669d7251", "payment-transaction-history"]}}, "response": []}, {"name": "GetTransactions", "event": [{"listen": "test", "script": {"exec": ["const response = pm.response.json();\r", "\r", "var template = `\r", "<style type=\"text/css\">\r", "    .tftable {font-size:14px;color:#333333;width:100%;border-width: 1px;border-color: #87ceeb;border-collapse: collapse;}\r", "    .tftable th {font-size:18px;background-color:#87ceeb;border-width: 1px;padding: 8px;border-style: solid;border-color: #87ceeb;text-align:left;}\r", "    .tftable tr {background-color:#ffffff;}\r", "    .tftable td {font-size:14px;border-width: 1px;padding: 8px;border-style: solid;border-color: #87ceeb;}\r", "    .tftable tr:hover {background-color:#e0ffff;}\r", "    .button { background-color: #4CAF50; border: none; color: white; padding: 6px 12px; text-align: center; text-decoration: none; display: inline-block; font-size: 14px; margin: 2px 1px; cursor: pointer; }\r", "</style>\r", "\r", "<table class=\"tftable\" border=\"1\">\r", "    <tr>\r", "        <th>Count</th>\r", "    </tr>\r", "    <tr>\r", "        <td>{{response.totalCount}}</td>\r", "    </tr>\r", "</table>\r", "\r", "<table class=\"tftable\" border=\"1\">\r", "    <tr>\r", "        <th>Transaction ID</th>\r", "        <th>Sequence Number</th>\r", "        <th>Amount</th>\r", "        <th>Status</th>\r", "        <th>Transaction Number</th>\r", "        <th>Reference Number</th>\r", "    </tr>\r", "    {{#each response.result}}\r", "    <tr>\r", "        <td>{{id}}</td>\r", "        <td>{{sequenceNumber}}</td>\r", "        <td>{{amount}}</td>\r", "        <td>{{status}}</td>\r", "        <td>{{transactionNumber}}</td>\r", "        <td>{{referenceNumber}}</td>\r", "    </tr>\r", "    {{/each}}\r", "</table>\r", "`;\r", "\r", "function constructVisualizerPayload() {\r", "    return {response: pm.response.json()}\r", "}\r", "\r", "if (response.result.length === 1) {\r", "    pm.environment.set(\"TransactionPaymentRequestId\", response.result[0].paymentRequestId); \r", "    console.log('Payment request id : ' + pm.environment.get(\"TransactionPaymentRequestId\"))\r", "    pm.environment.set(\"TransactionToRetryId\", response.result[0].id);\r", "    console.log('Payment request id : ' + pm.environment.get(\"TransactionToRetryId\"))\r", "}\r", "\r", "pm.visualizer.set(template, constructVisualizerPayload());"], "type": "text/javascript"}}], "request": {"method": "GET", "header": [], "url": {"raw": "{{PaymentServiceUrl}}/transactions?TransactionNumber=BT381790664982002304", "host": ["{{PaymentServiceUrl}}"], "path": ["transactions"], "query": [{"key": "TransactionNumber", "value": "BT381790664982002304"}, {"key": "ReferenceNumber", "value": "SB1616605242393", "disabled": true}]}}, "response": []}, {"name": "GetTransactionById", "event": [{"listen": "test", "script": {"exec": ["const response = pm.response.json();\r", "\r", "console.log(response);\r", "const metaData = JSON.parse(response.metaData);\r", "console.log(metaData);\r", "\r", "var template = `\r", "<style type=\"text/css\">\r", "    .tftable {font-size:14px;color:#333333;width:100%;border-width: 1px;border-color: #87ceeb;border-collapse: collapse;}\r", "    .tftable th {font-size:18px;background-color:#87ceeb;border-width: 1px;padding: 8px;border-style: solid;border-color: #87ceeb;text-align:left;}\r", "    .tftable tr {background-color:#ffffff;}\r", "    .tftable td {font-size:14px;border-width: 1px;padding: 8px;border-style: solid;border-color: #87ceeb;}\r", "    .tftable tr:hover {background-color:#e0ffff;}\r", "</style>\r", "\r", "<table class=\"tftable\" border=\"1\">\r", "    <tr>\r", "        <th>Transaction ID</th>\r", "        <th>Amount</th>\r", "    </tr>\r", "    <tr>\r", "        <td>{{response.id}}</td>\r", "        <td>{{response.amount}}</td>\r", "    </tr>\r", "</table>\r", "\r", "<table class=\"tftable\" border=\"1\">\r", "    <tr>\r", "        <th>Metada<PERSON></th>\r", "        <th></th>\r", "    </tr>\r", "    <!-- Iterate over metadata object and display key-value pairs -->\r", "    {{#each metaData}}\r", "    <tr>\r", "        <td>{{@key}}</td>\r", "        <td>{{this}}</td>\r", "    </tr>\r", "    {{/each}}\r", "</table>\r", "`;\r", "\r", "function constructVisualizerPayload() {\r", "    var response = pm.response.json();\r", "    return { response: response, metaData: metaData }\r", "}\r", "\r", "pm.visualizer.set(template, constructVisualizerPayload());"], "type": "text/javascript"}}], "request": {"method": "GET", "header": [], "url": {"raw": "{{PaymentServiceUrl}}/transactions/cc324232-adcd-453c-af2b-218780096b82", "host": ["{{PaymentServiceUrl}}"], "path": ["transactions", "cc324232-adcd-453c-af2b-218780096b82"]}}, "response": []}, {"name": "RetryTransaction", "event": [{"listen": "test", "script": {"exec": ["const response = pm.response.json();\r", "\r", "var template = `\r", "<style type=\"text/css\">\r", "    .tftable {font-size:14px;color:#333333;width:100%;border-width: 1px;border-color: #87ceeb;border-collapse: collapse;}\r", "    .tftable th {font-size:18px;background-color:#87ceeb;border-width: 1px;padding: 8px;border-style: solid;border-color: #87ceeb;text-align:left;}\r", "    .tftable tr {background-color:#ffffff;}\r", "    .tftable td {font-size:14px;border-width: 1px;padding: 8px;border-style: solid;border-color: #87ceeb;}\r", "    .tftable tr:hover {background-color:#e0ffff;}\r", "</style>\r", "\r", "<table class=\"tftable\" border=\"1\">\r", "    <tr>\r", "        <th>Retried Transaction ID</th>\r", "        <th>Sequence Number</th>\r", "        <th>Amount</th>\r", "        <th>Status</th>\r", "        <th>Transaction Number</th>\r", "        <th>Reference Number</th>\r", "    </tr>\r", "    <tr>\r", "        <td>{{response.id}}</td>\r", "        <td>{{response.sequenceNumber}}</td>\r", "        <td>{{response.amount}}</td>\r", "        <td>{{response.status}}</td>\r", "        <td>{{response.transactionNumber}}</td>\r", "        <td>{{response.referenceNumber}}</td>\r", "    </tr>\r", "</table>\r", "`;\r", "\r", "function constructVisualizerPayload() {\r", "    return {response: pm.response.json()}\r", "}\r", "\r", "pm.visualizer.set(template, constructVisualizerPayload());"], "type": "text/javascript"}}], "request": {"method": "PATCH", "header": [{"key": "userId", "value": "Postman", "type": "text"}], "url": {"raw": "{{PaymentServiceUrl}}/paymentRequests/{{TransactionPaymentRequestId}}/transactions/{{TransactionToRetryId}}/retry", "host": ["{{PaymentServiceUrl}}"], "path": ["paymentRequests", "{{TransactionPaymentRequestId}}", "transactions", "{{TransactionToRetryId}}", "retry"]}}, "response": []}, {"name": "RetryTransactionPullPromCustomer", "event": [{"listen": "test", "script": {"exec": ["const response = pm.response.json();\r", "\r", "var template = `\r", "<style type=\"text/css\">\r", "    .tftable {font-size:14px;color:#333333;width:100%;border-width: 1px;border-color: #87ceeb;border-collapse: collapse;}\r", "    .tftable th {font-size:18px;background-color:#87ceeb;border-width: 1px;padding: 8px;border-style: solid;border-color: #87ceeb;text-align:left;}\r", "    .tftable tr {background-color:#ffffff;}\r", "    .tftable td {font-size:14px;border-width: 1px;padding: 8px;border-style: solid;border-color: #87ceeb;}\r", "    .tftable tr:hover {background-color:#e0ffff;}\r", "</style>\r", "\r", "<table class=\"tftable\" border=\"1\">\r", "    <tr>\r", "        <th>Retried Transaction ID</th>\r", "        <th>Sequence Number</th>\r", "        <th>Amount</th>\r", "        <th>Status</th>\r", "        <th>Transaction Number</th>\r", "        <th>Reference Number</th>\r", "    </tr>\r", "    <tr>\r", "        <td>{{response.id}}</td>\r", "        <td>{{response.sequenceNumber}}</td>\r", "        <td>{{response.amount}}</td>\r", "        <td>{{response.status}}</td>\r", "        <td>{{response.transactionNumber}}</td>\r", "        <td>{{response.referenceNumber}}</td>\r", "    </tr>\r", "</table>\r", "`;\r", "\r", "function constructVisualizerPayload() {\r", "    return {response: pm.response.json()}\r", "}\r", "\r", "pm.visualizer.set(template, constructVisualizerPayload());"], "type": "text/javascript"}}], "request": {"method": "PATCH", "header": [{"key": "userId", "value": "Postman", "type": "text"}], "url": {"raw": "{{PaymentServiceUrl}}/paymentRequests/{{TransactionPaymentRequestId}}/transactions/{{TransactionToRetryId}}/retry", "host": ["{{PaymentServiceUrl}}"], "path": ["paymentRequests", "{{TransactionPaymentRequestId}}", "transactions", "{{TransactionToRetryId}}", "retry"]}}, "response": []}]}, {"name": "ExecutePaymentRequestJob", "event": [{"listen": "test", "script": {"exec": [""], "type": "text/javascript"}}], "request": {"method": "POST", "header": [{"key": "created<PERSON>y", "value": "BlueTape", "type": "text"}], "body": {"mode": "raw", "raw": "", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{PaymentServiceUrl}}/queueEvents/payment-scheduled-job", "host": ["{{PaymentServiceUrl}}"], "path": ["queueEvents", "payment-scheduled-job"]}}, "response": []}, {"name": "Create Payment", "event": [{"listen": "test", "script": {"exec": ["var jsonData = JSON.parse(responseBody);", "postman.setEnvironmentVariable(\"PaymentRequestId\", jsonData.id);"], "type": "text/javascript"}}], "request": {"method": "POST", "header": [{"key": "created<PERSON>y", "value": "BlueTape", "type": "text"}], "body": {"mode": "raw", "raw": "\t\t{\r\n  \"flowTemplateCode\": \"CREATE.PAYNOW.INVOICE_PAYMENT\",\r\n  \"blueTapeCorrelationId\": \"correlation_id\",\r\n  \"createdBy\": \"Test1221\",\r\n  \"details\": {\r\n    \"date\": \"2024-01-16T10:23:53.958Z\",\r\n    \"currency\": \"USD\",\r\n    \"requestedAmount\": 3.36,\r\n    \"paymentMethod\": \"Ach\",\r\n    \"payablesDetails\": [\r\n      {\r\n        \"id\": \"6602d53af9a16227f6e81e5b\",\r\n        \"payableType\": \"Invoice\",\r\n        \"payableAmount\": 3.36,\r\n        \"requestedAmount\": 3.36,\r\n       \"discountAmount\": 0.00\r\n      }\r\n    ],\r\n    \"customerDetails\": {\r\n      \"id\": \"65c0e1cb76b3a9feb46710df\",\r\n      \"name\": \"customer\",\r\n      \"accountId\": \"65c0e308c83231cbfdeca91b\"\r\n\t\t    },\r\n    \"sellerDetails\": {\r\n      \"companyId\": \"65c0e0f4768f1dfb0c002109\",\r\n      \"name\": \"seller\"\r\n    },\r\n    \"feeDetails\": [\r\n      {\r\n        \"companyId\": \"d40b6919-9c26-4988-8e0c-38ab9924f156\",\r\n        \"amount\": 1.00,\r\n        \"description\": \"Merchant Fee\",\r\n        \"type\": \"Merchant\"\r\n      },\r\n      {\r\n        \"companyId\": \"d40b6919-9c26-4988-8e0c-38ab9924f156\",\r\n        \"amount\": 1.00,\r\n        \"description\": \"Purchaser Fee\",\r\n        \"type\": \"Purchaser\"\r\n      }\r\n    ],\r\n\t\t        \"discountDetails\": [\r\n          {\r\n            \"companyId\": \"657055c05f30e55efdc15129\", // PayerId\r\n            \"amount\": 0.0, // summarized discount for payer (positive)\r\n            \"description\": \"ACH Early Payment Discount\",\r\n            \"type\": \"achEarlyPaymentDiscount\"\r\n          }\r\n        ],\r\n\t\t    \"projectDetails\": {\r\n      \"id\": \"project_id\"\r\n    }\r\n  }\r\n}\r\n", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{PaymentServiceUrl}}/queueEvents/payment-request/pay-now", "host": ["{{PaymentServiceUrl}}"], "path": ["queueEvents", "payment-request", "pay-now"]}}, "response": []}, {"name": "Create Payment Repaymnets", "event": [{"listen": "test", "script": {"exec": ["var jsonData = JSON.parse(responseBody);", "postman.setEnvironmentVariable(\"PaymentRequestId\", jsonData.id);"], "type": "text/javascript"}}], "request": {"method": "POST", "header": [{"key": "created<PERSON>y", "value": "BlueTape", "type": "text"}], "body": {"mode": "raw", "raw": "{\r\n  \"flowTemplateCode\": \"DRAW.REPAYMENT\",\r\n  \"blueTapeCorrelationId\": \"657056ddff868bcdb89bb8f1\",\r\n  \"createdBy\": \"BlueTape.ProcessDue\",\r\n  \"details\": {\r\n    \"date\": \"2023-12-12T00:00:00.000Z\",\r\n    \"currency\": \"USD\",\r\n    \"requestedAmount\": 7.00,\r\n    \"paymentMethod\": \"ach\",\r\n    \"drawDetails\": {\r\n      \"id\": \"9b8b4b2d-7a82-4c90-b2d8-17e42da4c194\",\r\n      \"amount\": 7.00\r\n    },\r\n    \"customerDetails\": {\r\n      \"id\": \"65d4c80702bc3086bc7ec702\",\r\n      \"name\": \"Tom Smith & Sons\",\r\n      \"accountId\": \"65d4c9c102628f8462891e0c\"\r\n    },\r\n    \"compatibility\": {\r\n      \"lmsPaymentId\": \"50111f79-7390-472d-bc23-6d1f2b16d924\"\r\n    }\r\n  }\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{PaymentServiceUrl}}/queueEvents/payment-request/trade-credit", "host": ["{{PaymentServiceUrl}}"], "path": ["queueEvents", "payment-request", "trade-credit"]}}, "response": []}, {"name": "Transaction Update", "event": [{"listen": "test", "script": {"exec": [""], "type": "text/javascript"}}], "request": {"method": "POST", "header": [{"key": "userId", "value": "DemoTestUser", "type": "text"}], "body": {"mode": "raw", "raw": "{\r\n  \"blueTapeTransactionNumber\": \"{{TransactionNumberToUpdate}}\",\r\n  \"externalTransactionNumber\": \"{{TransactionReferenseNumberToUpdate}}\",\r\n  \"externalTransactionStatus\": \"cleared\",\r\n  \"errorCode\": \"\",\r\n  \"statusMessage\": \"Some test message\"\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{PaymentServiceUrl}}/queueEvents/transaction-status-update-job", "host": ["{{PaymentServiceUrl}}"], "path": ["queueEvents", "transaction-status-update-job"], "query": [{"key": "", "value": null, "disabled": true}]}}, "response": []}, {"name": "Transaction Update With Error", "event": [{"listen": "test", "script": {"exec": [""], "type": "text/javascript"}}], "request": {"method": "POST", "header": [{"key": "userId", "value": "DemoTestUser", "type": "text"}], "body": {"mode": "raw", "raw": "{\r\n  \"blueTapeTransactionNumber\": \"{{TransactionNumberToUpdate}}\",\r\n  \"externalTransactionNumber\": \"{{TransactionReferenseNumberToUpdate}}\",\r\n  \"externalTransactionStatus\": \"cleared\",\r\n  \"errorCode\": \"\",\r\n  \"statusMessage\": \"Some test message\"\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{PaymentServiceUrl}}/queueEvents/transaction-status-update-job", "host": ["{{PaymentServiceUrl}}"], "path": ["queueEvents", "transaction-status-update-job"], "query": [{"key": "", "value": null, "disabled": true}]}}, "response": []}, {"name": "PaymentRequestCommandById", "event": [{"listen": "test", "script": {"exec": ["const response = pm.response.json();\r", "\r", "function findPullFromMerchantEntry(steps) {\r", "    for (var i = 0; i < steps.length; i++) {\r", "        if (steps[i].stepName === \"PullFromMerchant\") {\r", "            return steps[i].id;\r", "        }\r", "    }\r", "    return null; // Return null if not found\r", "}\r", "\r", "function findCommandToExecute(steps) {\r", "    for (var i = 0; i < steps.length; i++) {\r", "        if (steps[i].stepName === \"PullFromMerchant\") {\r", "            return steps[i].id;\r", "        }\r", "    }\r", "    return null; // Return null if not found\r", "}\r", "\r", "pm.environment.set(\"CommandToManualExecuting\", findPullFromMerchantEntry(response));\r", "\r", "var template = `\r", "<style type=\"text/css\">\r", "    .tftable {font-size:14px;color:#333333;width:100%;border-width: 1px;border-color: #87ceeb;border-collapse: collapse;}\r", "    .tftable th {font-size:18px;background-color:#87ceeb;border-width: 1px;padding: 8px;border-style: solid;border-color: #87ceeb;text-align:left;}\r", "    .tftable tr {background-color:#ffffff;}\r", "    .tftable td {font-size:14px;border-width: 1px;padding: 8px;border-style: solid;border-color: #87ceeb;}\r", "    .tftable tr:hover {background-color:#e0ffff;}\r", "</style>\r", "\r", "<table class=\"tftable\" border=\"1\">\r", "    <tr>\r", "        <th>Command ID</th>\r", "        <th>Step Name</th>\r", "        <th>Status</th>\r", "        <th>Transaction ID</th>\r", "        <th>Created By</th>\r", "        <th>Updated By</th>\r", "    </tr>\r", "    \r", "    {{#each response}}\r", "        <tr>\r", "            <td>{{id}}</td>\r", "            <td>{{stepName}}</td>\r", "            <td>{{status}}</td>\r", "            <td>{{transactionId}}</td>\r", "            <td>{{createdBy}}</td>\r", "            <td>{{updatedBy}}</td>\r", "        </tr>\r", "    {{/each}}\r", "</table>\r", "`;\r", "\r", "function constructVisualizerPayload() {\r", "    return {response: pm.response.json()}\r", "}\r", "\r", "pm.visualizer.set(template, constructVisualizerPayload());"], "type": "text/javascript"}}], "request": {"method": "GET", "header": [], "url": {"raw": "{{PaymentServiceUrl}}/paymentRequests/{{PaymentRequestId}}/payment-request-execution-history", "host": ["{{PaymentServiceUrl}}"], "path": ["paymentRequests", "{{PaymentRequestId}}", "payment-request-execution-history"]}}, "response": []}, {"name": "PaymentRequestById", "event": [{"listen": "test", "script": {"exec": ["const response = pm.response.json();\r", "pm.environment.set(\"PaymentRequestId\", response.id);\r", "\r", "// Extract the 'transactions' array from the response body\r", "let transactionsArray = response.transactions.sort((a, b) => b.sequenceNumber - a.sequenceNumber);\r", "\r", "// Log the array for verification\r", "console.log('Transactions Array:', transactionsArray);\r", "\r", "let transaction1, transaction2, transaction3, transaction4;\r", "\r", "transactionsArray.forEach(transaction => {\r", "    switch (transaction.sequenceNumber) {\r", "        case 1:\r", "            pm.environment.set('transaction1', transaction.transactionNumber);\r", "            break;\r", "        case 2:\r", "            pm.environment.set('transaction2', transaction.transactionNumber);\r", "            break;\r", "        case 3:\r", "            pm.environment.set('transaction3', transaction.transactionNumber);\r", "            break;\r", "        case 4:\r", "            pm.environment.set('transaction4', transaction.transactionNumber);\r", "            break;\r", "        // Add more cases if needed for other sequenceNumbers\r", "        default:\r", "            // Handle default case if needed\r", "            break;\r", "    }\r", "    console.log('Transaction:', transaction);\r", "});\r", "\r", "function findTransactionToUxecute(transactions) {\r", "    for (var i = 0; i < transactions.length; i++) {\r", "        if (transactions[i].transactionNumber != null ) {\r", "            return transactions[i];\r", "        }\r", "    }\r", "    return null; // Return null if not found\r", "}\r", "\r", "var transactionToUpdate = findTransactionToExecute(response.transactions) ?? { transactionNumber: \"defaultTransactionNumber\", referenceNumber: \"defaultReferenceNumber\" };\r", "console.log('Transaction to update:', transactionToUpdate);\r", "postman.setEnvironmentVariable(\"TransactionNumberToUpdate\", transactionToUpdate.transactionNumber);\r", "postman.setEnvironmentVariable(\"TransactionReferenceNumberToUpdate\", transactionToUpdate.referenceNumber);\r", "\r", "var template = `\r", "<style type=\"text/css\">\r", "    .tftable {font-size:14px;color:#333333;width:100%;border-width: 1px;border-color: #87ceeb;border-collapse: collapse;}\r", "    .tftable th {font-size:18px;background-color:#87ceeb;border-width: 1px;padding: 8px;border-style: solid;border-color: #87ceeb;text-align:left;}\r", "    .tftable tr {background-color:#ffffff;}\r", "    .tftable td {font-size:14px;border-width: 1px;padding: 8px;border-style: solid;border-color: #87ceeb;}\r", "    .tftable tr:hover {background-color:#e0ffff;}\r", "</style>\r", "\r", "<table class=\"tftable\" border=\"1\">\r", "    <tr>\r", "        <th>Payment request ID</th>\r", "        <th>Status</th>\r", "    </tr>\r", "    <tr>\r", "        <td>{{response.id}}</td>\r", "        <td>{{response.status}}</td>\r", "    </tr>\r", "</table>\r", "\r", "<table class=\"tftable\" border=\"1\">\r", "    <tr>\r", "        <th>Transaction ID</th>\r", "        <th>Sequence Number</th>\r", "        <th>Amount</th>\r", "        <th>Status</th>\r", "        <th>Transaction Number</th>\r", "        <th>Reference Number</th>\r", "        <th>Public Number</th>\r", "    </tr>\r", "    {{#each response.transactions}}\r", "    <tr>\r", "        <td>{{id}}</td>\r", "        <td>{{sequenceNumber}}</td>\r", "        <td>{{amount}}</td>\r", "        <td>{{status}}</td>\r", "        <td>{{transactionNumber}}</td>\r", "        <td>{{referenceNumber}}</td>\r", "        <td>{{publicTransactionNumber}}</td>\r", "    </tr>\r", "    {{/each}}\r", "</table>\r", "`;\r", "\r", "function constructVisualizerPayload() {\r", "    var response = pm.response.json();\r", "    response.transactions.sort((a, b) => a.sequenceNumber - b.sequenceNumber);\r", "    return {response: response}\r", "}\r", "\r", "pm.visualizer.set(template, constructVisualizerPayload());"], "type": "text/javascript"}}], "request": {"method": "GET", "header": [], "url": {"raw": "{{PaymentServiceUrl}}/paymentRequests/ac529cb4-7bc4-4621-90a7-be990ce3dfb9", "host": ["{{PaymentServiceUrl}}"], "path": ["paymentRequests", "ac529cb4-7bc4-4621-90a7-be990ce3dfb9"]}}, "response": []}, {"name": "GetPaymentRequests", "event": [{"listen": "test", "script": {"exec": ["const response = pm.response.json();\r", "\r", "var template = `\r", "<style type=\"text/css\">\r", "    .tftable {font-size:14px;color:#333333;width:100%;border-width: 1px;border-color: #87ceeb;border-collapse: collapse;}\r", "    .tftable th {font-size:18px;background-color:#87ceeb;border-width: 1px;padding: 8px;border-style: solid;border-color: #87ceeb;text-align:left;}\r", "    .tftable tr {background-color:#ffffff;}\r", "    .tftable td {font-size:14px;border-width: 1px;padding: 8px;border-style: solid;border-color: #87ceeb;}\r", "    .tftable tr:hover {background-color:#e0ffff;}\r", "    .button { background-color: #4CAF50; border: none; color: white; padding: 6px 12px; text-align: center; text-decoration: none; display: inline-block; font-size: 14px; margin: 2px 1px; cursor: pointer; }\r", "</style>\r", "\r", "<table class=\"tftable\" border=\"1\">\r", "    <tr>\r", "        <th>Count</th>\r", "    </tr>\r", "    <tr>\r", "        <td>{{response.totalCount}}</td>\r", "    </tr>\r", "</table>\r", "\r", "<table class=\"tftable\" border=\"1\">\r", "    <tr>\r", "        <th>Payment request ID</th>\r", "        <th>Amount</th>\r", "        <th>Status</th>\r", "        <th>Date</th>\r", "    </tr>\r", "    {{#each response.result}}\r", "    <tr>\r", "        <td>{{id}}</td>\r", "        <td>{{amount}}</td>\r", "        <td>{{status}}</td>\r", "        <td>{{date}}</td>\r", "    </tr>\r", "    {{/each}}\r", "</table>\r", "`;\r", "\r", "function constructVisualizerPayload() {\r", "    var response = pm.response.json();\r", "    response.result.sort((a, b) => a.createdAt - b.createdAt);\r", "    return {response: response}\r", "}\r", "}\r", "\r", "pm.visualizer.set(template, constructVisualizerPayload());"], "type": "text/javascript"}}], "request": {"method": "GET", "header": [], "url": {"raw": "{{PaymentServiceUrl}}/paymentRequests", "host": ["{{PaymentServiceUrl}}"], "path": ["paymentRequests"]}}, "response": []}, {"name": "PaymentRequest ExecuteCommand", "event": [{"listen": "test", "script": {"exec": [""], "type": "text/javascript"}}], "request": {"method": "GET", "header": [{"key": "userId", "value": "DemoTestUser", "type": "text"}], "url": {"raw": "{{PaymentServiceUrl}}/transactions/test/ach/{{CommandToManualExecuting}}", "host": ["{{PaymentServiceUrl}}"], "path": ["transactions", "test", "ach", "{{CommandToManualExecuting}}"], "query": [{"key": "", "value": null, "disabled": true}]}}, "response": []}, {"name": "PaymentRequest ExecuteCommand Copy", "event": [{"listen": "test", "script": {"exec": [""], "type": "text/javascript"}}], "request": {"method": "PUT", "header": [{"key": "userId", "value": "DemoTestUser", "type": "text"}], "url": {"raw": "{{PaymentServiceUrl}}/transactions/command/executed/{{CommandToManualExecuting}}", "host": ["{{PaymentServiceUrl}}"], "path": ["transactions", "command", "executed", "{{CommandToManualExecuting}}"], "query": [{"key": "", "value": null, "disabled": true}]}}, "response": []}], "auth": {"type": "apikey", "apikey": [{"key": "value", "value": "{{x-api-key}}", "type": "string"}, {"key": "key", "value": "x-api-key", "type": "string"}]}, "event": [{"listen": "prerequest", "script": {"type": "text/javascript", "exec": [""]}}, {"listen": "test", "script": {"type": "text/javascript", "exec": [""]}}]}