import {expect} from '@playwright/test';
import {test, BaseTest} from '../../test-utils';

test.use({storageState: {cookies: [], origins: []}});

test.describe('Projects tests', async () => {
    let nameOfTheProject: string | RegExp | (string | RegExp)[];
    let customerName: string;
    let address: string;
    let value: string;
    let describe: string;

    test.beforeEach(async () => {
        nameOfTheProject = BaseTest.dateTimePrefix() + 'name';
        customerName = BaseTest.dateTimePrefix() + 'customerName';
        address = BaseTest.dateTimePrefix() + 'address';
        value = BaseTest.dateTimePrefix();
        describe = BaseTest.dateTimePrefix() + 'describe';
    });

    test.afterEach(async ({page}) => {
        await page.close();
    });

    async function createNewProject({pageManager}) {
        await pageManager.sideMenu.sideMenuTabs.projects.click();
        await pageManager.projects.buttons.addNewProject.click();
        await pageManager.addNewProjectModal.fillTheAddNewProjectForm(nameOfTheProject, customerName, address, value, describe);
    }

    test.skip(`Add new project. The result modal should be opened with ${nameOfTheProject}`, async ({pageManager}) => {
        await createNewProject({pageManager});
        await expect(pageManager.resultModal.textMessage.message, `The result modal should be opened with ${nameOfTheProject}`).toContainText(nameOfTheProject);
    });
});
