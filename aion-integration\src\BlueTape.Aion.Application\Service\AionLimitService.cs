using System.Diagnostics.CodeAnalysis;
using BlueTape.Aion.Application.Abstractions;
using BlueTape.Aion.DataAccess.External.Abstractions;
using BlueTape.Aion.DataAccess.External.Models.DailyLimmits;
using BlueTape.Integrations.Aion.Infrastructure.Enums;
using BueTape.Aion.Infrastructure.Extensions;
using BueTape.Aion.Infrastructure.Options;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Options;

namespace BlueTape.Aion.Application.Service;

[ExcludeFromCodeCoverage]
public class AionLimitService : IAionLimitService
{
    private readonly IAionHttpClient _aionHttpClient;
    private readonly IConfiguration _configuration;
    private AionInternalTransferOptions _internalTransferOptions;

    public AionLimitService(
        IAionHttpClient aionHttpClient,
        IConfiguration configuration,
        IOptions<AionInternalTransferOptions> options)
    {
        _aionHttpClient = aionHttpClient;
        _configuration = configuration;
        _internalTransferOptions = options.Value;
    }
    
    private string GetAccountNumber(AccountCodeType accountCodeType)
    {
        _internalTransferOptions.AccountNumberSecretNames
            .TryGetValue(accountCodeType, out var accountNumberSecret);

        if (string.IsNullOrEmpty(accountNumberSecret)) throw new VariableNullException(nameof(accountNumberSecret));

        var accountNumber = _configuration[accountNumberSecret] ?? throw new VariableNullException(nameof(accountNumberSecret));

        return accountNumber;
    }

    public async Task<AionAccountLimitResponse> GetAionAchPullLimit(decimal amount, AccountCodeType accountCodeType, string paymentSubscriptionType, CancellationToken ctx)
    {
        var accountNumber = GetAccountNumber(accountCodeType);
        var paymentSubscription = paymentSubscriptionType.ParseToPaymentSubscription();
        
        return await _aionHttpClient.GetAchPullLimit(
            new GetACHLimitRequest
            {
                ACHObj = new GenericLimiRequestBody
                {
                    AccountNumber = accountNumber,
                    Amount = amount,
                    TransferType = "ACH"
                }
            },
            paymentSubscription,
            ctx);
    }

    public async Task<AionAccountLimitResponse> GetAionAchPushLimit(decimal amount, AccountCodeType accountCodeType, string paymentSubscriptionType, CancellationToken ctx)
    {
        var accountNumber = GetAccountNumber(accountCodeType);
        var paymentSubscription = paymentSubscriptionType.ParseToPaymentSubscription();
        
        return await _aionHttpClient.GetAchPushLimit(
            new GetACHLimitRequest
            {
                ACHObj = new GenericLimiRequestBody
                {
                    AccountNumber = accountNumber,
                    Amount = amount,
                    TransferType = "ACH"
                }
            },
            paymentSubscription,
            ctx);
    }

    public async Task<AionAccountLimitResponse> GetAionWirePushLimit(decimal amount, AccountCodeType accountCodeType, string paymentSubscriptionType, CancellationToken ctx)
    {
        var accountNumber = GetAccountNumber(accountCodeType);
        var paymentSubscription = paymentSubscriptionType.ParseToPaymentSubscription();

        return await _aionHttpClient.GetWireLimit(
            new GetWireLimitRequest
            {
                GetWireLimitRequestBody = new GenericLimiRequestBody
                {
                    AccountNumber = accountNumber,
                    Amount = amount,
                    TransferType = "Wire"
                }
            },
            paymentSubscription,
            ctx);
    }

    public async Task<AionAccountLimitResponse> GetAionInstantPushLimit(decimal amount, AccountCodeType accountCodeType, string paymentSubscriptionType, CancellationToken ctx)
    {
        var accountNumber = GetAccountNumber(accountCodeType);
        var paymentSubscription = paymentSubscriptionType.ParseToPaymentSubscription();
        
        return await _aionHttpClient.GetInstantLimit(
            new GetInstantLimitRequest()
            {
                InstantTransferObj = new GenericLimiRequestBody
                {
                    AccountNumber = accountNumber,
                    Amount = amount,
                    TransferType = "Instant"
                }
            },
            paymentSubscription,
            ctx);
    }
}