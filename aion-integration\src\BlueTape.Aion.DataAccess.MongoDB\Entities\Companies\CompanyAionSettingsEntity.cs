﻿using MongoDB.Bson.Serialization.Attributes;

namespace BlueTape.Aion.DataAccess.MongoDB.Entities.Companies;

[BsonIgnoreExtraElements]
public class CompanyAionSettingsEntity
{
    [BsonElement("counterPartyId")]
    public string? CounterPartyId { get; set; }
    [BsonElement("counterPartyObjectId")]
    public string? CounterPartyObjectId { get; set; }

    [BsonElement("counterPartyId2")]
    public string? CounterPartyId2 { get; set; }
    [BsonElement("counterPartyObjectId2")]
    public string? CounterPartyObjectId2 { get; set; }

    [BsonElement("counterPartyId3")]
    public string? CounterPartyId3 { get; set; }
    [BsonElement("counterPartyObjectId3")]
    public string? CounterPartyObjectId3 { get; set; }
}