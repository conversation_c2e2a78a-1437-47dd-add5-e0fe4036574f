import {expect} from '@playwright/test';
import {test} from '../../test-utils';
import {sendCompanyRequest} from '../../../api/common/company-request';

const constants = JSON.parse(JSON.stringify(require('../../../constants/companyTestData.json')));

// CREATE LOAN APPLICATION VIA API. Now Loan Appilcation Id is hardcode for DEV end
// 1. Check there is NO document for new loan application empty array is returned
// 2. Generate Doc for new loan application
// 3. Check there is generated doc for loan application

const codesArray = constants.documentTemplates.map(template => template.code);
const codesIdArray = constants.documentTemplates.map(template => template.id);

test.describe(`Documents. CRUD API Tests @docs @API`, async () => {

    test(`Get Documents by Loan Application Id @docs`, async () => {
        const response = await sendCompanyRequest('get', `documents/loan-application/${constants.loanApplicationIds.validLoanId}`);
        expect(response.status, `Status code 200`).toEqual(200);
        expect(response.data, `Response contains Object`).toEqual(expect.any(Array));
    });

    for (const type of codesArray) {
        test.skip(`Get File Name for Loan Id @docs - ${type}`, async () => {
            const response = await sendCompanyRequest('get', `documents/loan-application/${constants.loanApplicationIds.validLoanId}/new?code=${type}`);
            expect(response.status, `Status code 200`).toEqual(200);
            expect(response.data, `Response contains Object with File Name`).toEqual(expect.any(Object));
        });
    }

    for (const templateId of codesIdArray) {

        test(`Create Document with ${templateId} @docs`, async () => {
            const requestBody = {
                'documentTemplateId': `${templateId}`,
                's3Url': `${constants.documentTemplates[0].s3Url}`
            };
            const response = await sendCompanyRequest('post', `documents/loan-application/${constants.loanApplicationIds.validLoanId}`, requestBody);
            expect(response.status, `Status code 201`).toEqual(201);
        });
    }

    // Negative Tests

    test(`Cannot create Document with Invalid TemplateID @docs`, async () => {
        const requestBody = {
            'documentTemplateId': `${constants.documentTemplateIds.invalidTemplateId}`,
            's3Url': 'invoices/639b191a837cea4dd1b8de9c/0.6wxi02pf2g4.pdf'
        };

        const response = await sendCompanyRequest('post',
            `documents/loan-application/${constants.loanApplicationIds.invalidLoanApplicationId}`, requestBody);

        expect(response.response.status, `Status code 400`).toEqual(400);
    });

});
