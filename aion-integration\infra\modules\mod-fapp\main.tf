# ## Create Environment Specific Function App
# ## Base image for Azure Funtions used:
# ## Ref.: https://hub.docker.com/_/microsoft-azure-functions-dotnet-isolated
resource "azurerm_linux_function_app" "aion-reports" {
  name                       = "func-${var.application_name.slug}-${var.environment}"
  location                   = var.location
  resource_group_name        = var.resource_group_name
  service_plan_id            = var.service_plan_core_id
  https_only                 = true
  storage_account_name       = var.storage_account_name
  storage_account_access_key = var.storage_account_access_key

  app_settings = {
    "FUNCTIONS_WORKER_RUNTIME"               = var.functions_worker_runtime
    "ASPNETCORE_ENVIRONMENT"                 = var.environment
    "KEYVAULT_URI"                           = var.key_vault_uri
    "AZURE_CLIENT_SECRET"                    = var.client_secret
    "AZURE_TENANT_ID"                        = var.tenant_id
    "AZURE_CLIENT_ID"                        = var.client_id
    "FUNCTIONS_EXTENSION_VERSION"            = var.function_extension_version
    "APPINSIGHTS_INSTRUMENTATIONKEY"         = var.app_insights_instrumentation_key
    "AzureWebJobsStorage"                    = var.storage_account_primary_connection_string
    "AWS_REGION"                             = var.aws_default_region
    "AWS_ACCESS_KEY_ID"                      = var.aws_access_key_id
    "AWS_SECRET_ACCESS_KEY"                  = var.aws_secret_access_key
    "LP_AWS_ACCOUNT"                         = var.lp_aws_account
    "aionExternalTransactionQueueName"       = var.external_transaction_queue_name
    "aionExternalTransactionQueueConnection" = var.external_transaction_queue_connection
    "aionInternalTransactionQueueName"       = var.internal_transaction_queue_name
    "aionInternalTransactionQueueConnection" = var.internal_transaction_queue_connection
    "aionExternalTransactionJobPeriod"       = var.aion_external_transaction_job_period
    "aionInternalTransactionJobPeriod"       = var.aion_internal_transaction_job_period
  }

  site_config {
    always_on                               = var.environment != "dev"
    ftps_state                              = "FtpsOnly"
    application_insights_key                = var.app_insights_instrumentation_key
    application_insights_connection_string  = var.app_insights_connection_string
    vnet_route_all_enabled                  = true

    application_stack {
      dotnet_version                        = var.dotnet_version
      use_dotnet_isolated_runtime           = true
    }

  }

  key_vault_reference_identity_id = var.user_assigned_identity_core_id

  identity {
      type = "UserAssigned"
      identity_ids = [var.user_assigned_identity_core_id]
  }

  tags = {
    Environment = title(var.environment)
    Application = "Core"
    Type        = "Function App"
    ManagedBy   = "Terraform"
  }

  lifecycle {
    ignore_changes = [
      tags["CreatedOn"],
    ]
  }

  virtual_network_subnet_id = var.environment != "dev" ? var.virtual_network_subnet_id : null
}