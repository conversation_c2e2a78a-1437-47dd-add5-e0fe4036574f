﻿using BlueTape.Aion.DataAccess.MongoDB.Abstractions.Contexts;
using BlueTape.MongoDB;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;

namespace BlueTape.Aion.DataAccess.MongoDB.Contexts;

public class AionMongoDBContext : MongoAzDbContext, IAionMongoDBContext
{
    public AionMongoDBContext(
        IConfiguration configuration,
        ILogger<AionMongoDBContext> logger) : base(configuration, logger)
    {
    }
}