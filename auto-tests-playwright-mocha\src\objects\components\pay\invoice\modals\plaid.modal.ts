import {BasePage} from '../../../../base.page';

export class PlaidModal extends BasePage {
    constructor(page){
        super(page);
    };

    containers = {
        iframe: this.page.frameLocator('[title="Plaid Link"]'),   
    };

    inputFields = {
        searchBank: this.containers.iframe.locator('[id="search-input"]'),
        userName: this.page.getByPlaceholder('Username'),
        password: this.page.locator('[id="password"]'),
    };

    buttons = {
        continue: this.containers.iframe.locator('[id="aut-button"]'),
        getCode: this.page.locator('[id="submit-device"]'),
        submitCode: this.page.locator('[id="submit-code"]'),
        plaidChecking: this.page.locator('[for="account_0"]'),
        plaidSaving: this.page.locator('[for="account_1"]'),
        submitAccounts: this.page.locator('[id="submit-accounts"]'),
        acceptTermsAndConditions: this.page.locator('[id="terms"]'),
        submitConfirmation: this.page.locator('[id="submit-confirmation"]'),
        signIn: this.page.locator('[id="submit-credentials"]'),
        firstAccount: this.containers.iframe.locator('[id="aut-selection-0"]'),
    };

    elements = {
        bankOfAmerica: this.containers.iframe.locator('[id="aut-ins_127989"]'),
    };

    async passPlaidModalBeforeFPB() {
        await this.buttons.continue.click();
        await this.elements.bankOfAmerica.click();
        await this.buttons.continue.click();
    };

    async passPlaidModalAfterFPB() {
        await this.buttons.firstAccount.click();
        await this.buttons.continue.click();
        await this.buttons.continue.click();
    };
}