import {BasePage} from '../../../base.page';

export class LinkBankAccount extends BasePage {
    constructor(page){
        super(page);
    };

    containers = {
        bankOptions: this.page.locator('[class = "css-1dbjc4n r-105ug2t"]'),
    };

    inputFields = {
        accountName: this.page.locator('//*[@data-testid="Name on account"]//input'),
        searchYourBank: this.page.locator('[placeholder="Search your bank"]'),
        routingNumber: this.page.locator('//*[@data-testid="Routing Number"]//input'),
        accountNumber: this.page.locator('//*[@data-testid="Account Number"]//input'),
    };

    buttons = {
        bankOption: this.containers.bankOptions.locator('.r-edyy15'),
        agreeLinkAccount: this.page.locator('"Link Bank Account"'),
        uploadVoidedCheck: this.page.locator('"voided check"'),
    };

    async fillUpBankAccountInformation(bankName, accountName, accountNumber, routingNumber){
        await this.inputFields.accountName.fill(accountName);
        await this.inputFields.searchYourBank.fill(bankName);
        await super.clickFistOption(this.buttons.bankOption);
        await this.inputFields.routingNumber.fill(accountNumber);
        await this.inputFields.accountNumber.fill(routingNumber);
        await super.uploadFile(this.buttons.uploadVoidedCheck);
        await this.buttons.agreeLinkAccount.click();
    };
}