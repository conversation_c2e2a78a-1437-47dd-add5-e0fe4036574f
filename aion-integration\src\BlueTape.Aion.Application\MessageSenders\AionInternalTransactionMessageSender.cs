﻿using BlueTape.Aion.Application.Abstractions.MessageSenders;
using BlueTape.Aion.Domain.Constants;
using BlueTape.ServiceBusMessaging;
using BueTape.Aion.Infrastructure.ServiceBusMessages;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using System.Diagnostics.CodeAnalysis;

namespace BlueTape.Aion.Application.MessageSenders;

[ExcludeFromCodeCoverage]
public class AionInternalTransactionMessageSender : ServiceBusMessageSender<AionInternalTransactionMessage>, IAionInternalTransactionMessageSender
{
    public AionInternalTransactionMessageSender(IConfiguration configuration, ILogger<AionInternalTransactionMessageSender> logger)
        : base(configuration, logger, AionFunctionConstants.AionInternalTransactionQueueName, AionFunctionConstants.AionInternalTransactionQueueConnection)
    {
    }
}