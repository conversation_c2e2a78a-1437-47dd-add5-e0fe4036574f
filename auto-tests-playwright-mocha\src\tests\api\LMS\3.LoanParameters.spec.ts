import {expect} from '@playwright/test';
import {test} from '../../test-utils';
import {sendLMSRequest, createLoan, deleteLoan} from '../../../api/common/lms-send-request';
import {validate} from "jsonschema";


const constants = JSON.parse(JSON.stringify(require('../../../constants/LMStestData.json')));

test.describe(`Loan Parameters API Tests @LMS @API`, async () => {

    let loanId: string;
    let loanParameterId: string;

    test.beforeAll(async () => {
        loanId = await createLoan();
    });

    test.afterAll(async () => {
        const response = await deleteLoan(loanId);

        expect(response.status, `Status code 200`)
            .toEqual(200);
    });

    /**
     * Positive tests
     */
    //todo check it
    test(`Get Loan Parameters by loan ID.`, async () => {
        const response = await sendLMSRequest('get', `LoanParameters?LoanId=${loanId}`);

        loanParameterId = response.data[0].id;

        expect(response.status, `Status code 200`)
            .toEqual(200);

        expect(response.data[0].loanId, `Correct Loan Parameters are returned by LoanID`)
            .toEqual(loanId);

        const validationResult = validate(response.data, constants.loanParametersSchema);

        expect(validationResult.valid, `JSON Schema is correct`).toBeTruthy();
    });

    test(`Get Loan Parameters by LoanParameterId.`, async () => {
        const getLoanParameter = await sendLMSRequest('get', `LoanParameters?LoanId=${loanId}`);

        loanParameterId = getLoanParameter.data[0].id;

        const response = await sendLMSRequest('get', `LoanParameters/${loanParameterId}`);

        expect(response.status, `Status code 200`)
            .toEqual(200);

        expect(response.data.id, `Correct Loan Parameters are returned by LoanParameterId`)
            .toEqual(loanParameterId);

        expect(response.data.loanId, `Correct Loan ID returned in Response`)
            .toEqual(loanId);
    });

    /**
     * Negative Tests
     */
    test(`Get failed data by invalid LoanParameterId. @lms`, async () => {
        const response = await sendLMSRequest('get', `LoanParameters/${constants.invalidLoanParameterIDs.id}`);

        expect(response.response.status, `Status code 404`)
            .toEqual(404);

        expect(response.code, `Error message for invalid LoanParameterId`)
            .toContain('ERR_BAD_REQUEST');
    });

    test(`Get empty data by invalid LoanId. @lms`, async () => {
        const response = await sendLMSRequest('get', `LoanParameters?LoanId=${constants.invalidLoanIDs.id}`);

        expect(response.status, `Status code 200`)
            .toEqual(200);

        expect(response.data, `Received answer contains an empty array`)
            .toEqual([]);
    });
});
