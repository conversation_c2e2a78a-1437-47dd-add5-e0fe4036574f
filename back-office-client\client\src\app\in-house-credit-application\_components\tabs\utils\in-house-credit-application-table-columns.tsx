import type { ColumnType } from 'antd/es/table'
import type { TFunction } from 'i18next'
import { isString } from 'lodash'

import { getInHouseCreditApplicationStatusTranslation } from '@/app/in-house-credit-application/_utils'
import { StyledLink } from '@/components/common/typography/Link'
import { AppRoutes } from '@/globals/routes'
import { AutomatedDecisionStatus } from '@/globals/types'
import {
  formatDateDay,
  getAutomatedDecisionTag,
  getBusinessCategoryTranslation,
} from '@/globals/utils'
import type { IInHouseCreditApplicationListItem } from '@/lib/redux/api/in-house-credit-application/types'

export const inHouseCreditApplicationTableColumns = {
  businessName: (t) =>
    ({
      title: t('inHouseCreditApplication.page.list.table.businessName'),
      dataIndex: 'businessName',
    }) as const,

  dba: (t) =>
    ({
      title: t('inHouseCreditApplication.page.list.table.dba'),
      dataIndex: 'dba',
      render: (_, record) => record.dba ?? '',
    }) as const,

  supplierName: (t) =>
    ({
      title: t('drawApplication.page.list.table.headers.supplier'),
      dataIndex: 'merchantName',
    }) as const,

  category: (t) =>
    ({
      title: t('inHouseCreditApplication.page.list.table.category'),
      dataIndex: 'category',
      render: (_, record) =>
        record.category
          ? getBusinessCategoryTranslation(record.category, t)
          : t('na'),
    }) as const,

  applicantName: (t) =>
    ({
      title: t('inHouseCreditApplication.page.list.table.applicantName'),
      dataIndex: 'applicantName',
      render: (_, record) => record.applicantName ?? t('na'),
    }) as const,

  submissionDate: (t) =>
    ({
      title: t('inHouseCreditApplication.page.list.table.submission'),
      dataIndex: 'submissionDate',
      render: (_, record) =>
        isString(record.submissionDate)
          ? formatDateDay(record.submissionDate)
          : '-',
    }) as const,

  automatedDecision: (t) =>
    ({
      title: t('inHouseCreditApplication.page.list.table.automatedDecision'),
      dataIndex: 'automatedDecision',
      render: (_, record) =>
        getAutomatedDecisionTag(
          record.automatedDecision,
          record.automatedDecisionStatus,
          t,
        ),
    }) as const,

  decisionMadeBy: (t) =>
    ({
      title: t('inHouseCreditApplication.page.list.table.decisionMadeBy'),
      dataIndex: 'decisionMadeBy',
      render: (_, record) =>
        isString(record.decisionMadeBy) ? record.decisionMadeBy : '-',
    }) as const,

  decisionDate: (t) =>
    ({
      title: t('inHouseCreditApplication.page.list.table.decisionDate'),
      dataIndex: 'decisionDate',
      render: (_, record) =>
        isString(record.decisionDate)
          ? formatDateDay(record.decisionDate)
          : '-',
    }) as const,

  status: (t) =>
    ({
      title: t('inHouseCreditApplication.page.list.table.status'),
      dataIndex: 'status',
      render: (_, record) =>
        getInHouseCreditApplicationStatusTranslation(record.status, t),
    }) as const,

  viewLink: (t) =>
    ({
      key: 'view',
      fixed: 'right',
      render: (_, record) => {
        return record.automatedDecisionStatus !==
          AutomatedDecisionStatus.PROCESSING ? (
          <StyledLink
            href={AppRoutes.inHouseCreditApplication.details(record.id)}
          >
            {t('inHouseCreditApplication.page.list.table.view')}
          </StyledLink>
        ) : (
          <></>
        )
      },
    }) as const,
} satisfies Record<
  string,
  (t: TFunction) => ColumnType<IInHouseCreditApplicationListItem>
>
