﻿export type PayerInfo = {
    firstName: string;
    lastName: string;
    emailAddress: string;
    cellPhoneNumber?: string; // Optional since it can be empty
    businessName: string;
};

export type LineItem = {
    description: string;
    unitAmount: number;
    quantity: number;
    subTotal: number;
    taxAmount: number;
    totalAmount: number;
};

export type CreateInvoiceSimplifyRequest = {
    payersInfo: PayerInfo[];
    customerId: string | number;
    id: string | number;
    invoiceNumber: string | number;
    subTotal: number;
    totalAmount: number;
    taxAmount: number;
    lines: LineItem[];
    invoiceDate: string;
    sourceModifiedDate: string;
    dueDate: string;
};