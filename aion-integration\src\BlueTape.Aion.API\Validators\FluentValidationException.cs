﻿using System.Diagnostics.CodeAnalysis;
using BlueTape.Aion.API.Models.Errors;

namespace BlueTape.Aion.API.Validators;

[ExcludeFromCodeCoverage]
public class FluentValidationException : Exception
{
    public List<ErrorModel> ErrorModels { get; } = new();

    public FluentValidationException(List<ErrorModel> errorModels) : base("Fluent validation error")
    {
        ErrorModels = errorModels;
    }
}