import {Page, expect} from '@playwright/test';
import {test} from '../../test-utils';
import {createCustomer, createInvoice} from '../../../api/common/send-generic-api-request';
import {PageManager} from '../../../objects/pages/page-manager';
import {BaseTest} from '../../test-utils';

test.use({storageState: {cookies: [], origins: []}});

test.describe(`Invoice E2E test`, async () => {
    let page: Page;
    let adminPage: Page;
    let customerId: string;
    let invoiceId: string;
    let email: string;
    let invoiceNumber: string;
    let businessName: string;

    test.beforeAll(async () => {
        customerId = `customer${BaseTest.dateTimePrefix()}`;
        invoiceId = `invoice${BaseTest.dateTimePrefix()}`;
        invoiceNumber = `invoiceNumber${BaseTest.dateTimePrefix()}`;
        email = `automation_user+${customerId}@bluetape.com`;
        businessName = `AutoTestBus${BaseTest.dateTimePrefix()}`;
        await createCustomer("", email, customerId);
        const blueTapeInvoiceId = await createInvoice({customerId: customerId, invoiceNumber: invoiceNumber});
    });

    test.skip(`Invoice Notification received to Gmail @generic @API`, async ({browser}) => {
        test.slow();
        page = await browser.newPage();
        await BaseTest.followPaymentRequestEmail(page, email);
        const pageManager = new PageManager(page);
        await pageManager.invoicesDetails.buttons.payWithBlueTapeCredit.click();
        await pageManager.signUpPage.fillSignUpFields(email, 'Aa!11111111');
        await pageManager.signUpPage.inputFields.businessName.fill(businessName);
        await BaseTest.clickWithDelay(pageManager.signUpPage.buttons.continue);
        await BaseTest.verificationLinkSignIn(page, email);
        await pageManager.unifiedApplication.completeUnifiedApp();
        await BaseTest.clickWithDelay(pageManager.unifiedApplication.buttons.next);
        await pageManager.unifiedApplication.fillUpBusinessAddress('51 W 51st St');
        await BaseTest.clickWithDelay(pageManager.unifiedApplication.buttons.next);
        await pageManager.unifiedApplication.fillUpCustomerDetailsForBusinessOwner('12/2000', '*********', '500000', '100');
        await pageManager.unifiedApplication.fillUpUsersDetails(BaseTest.constants.address.street, '********', BaseTest.constants.user.socialSecurityNumber);
        await pageManager.unifiedApplication.fillUpBankInformation('BANK MICHIGAN', '1234', '*********', '*********');
        await page.waitForLoadState('networkidle');
        await pageManager.unifiedApplication.bankInformation.bankTile.isVisible();
        await pageManager.page.waitForTimeout(2000);
        await BaseTest.clickWithDelay(pageManager.unifiedApplication.buttons.next);
        await pageManager.unifiedApplication.agreeAndPayWithLoan();
        await page.waitForLoadState('networkidle');
        await pageManager.unifiedApplication.buttons.okayThanks.click();
        //
        adminPage = await browser.newPage();
        await adminPage.goto(`${process.env.CI_ENVIRONMENT_BACKEND_URL}`);
        const adminPageManager = new PageManager(adminPage);
        await adminPageManager.backOfficeLoginPage.login(`${process.env.ADMIN_EMAIL}`, `${process.env.ADMIN_PASSWORD}`);
        await adminPageManager.backOfficeSideMenu.sideMenuTabs.loanApplications.click();
        await adminPageManager.loanApplications.verifyLoanApplication(businessName);
        await adminPageManager.loanApplications.waitLoanAppVerifyAndClickApprove(businessName);
        await adminPageManager.approveModal.fillCreditLimitAndClickApprove('100000');
        await adminPageManager.loanApplications.tabs.approved.click();
        await adminPageManager.loanApplications.waitForStatusChange(adminPageManager.loanApplications.elements.companyName(businessName), 'Approved');
        await expect(adminPageManager.loanApplications.elements.companyName(businessName)).toContainText('Approved');
        //
        await pageManager.loanApplications.waitForElementExist(pageManager.approvedLoanModal.containers.mainContainer);
        await pageManager.approvedLoanModal.buttons.close.click();
        await pageManager.sideMenu.sideMenuTabs.pay.click();
        await pageManager.sideMenu.sideMenuSubTabs.pay.credit.click();
        await expect(pageManager.creditList.loanList.loanListElement).toBeVisible();
    });
});
