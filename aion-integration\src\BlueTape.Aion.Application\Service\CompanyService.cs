﻿using BlueTape.Aion.Application.Abstractions;
using BlueTape.Aion.Application.Constants;
using BlueTape.Aion.Application.Enums;
using BlueTape.Aion.Application.Extensions;
using BlueTape.Aion.DataAccess.External.Abstractions;
using BlueTape.Aion.DataAccess.External.Models.CreateCounterParty;
using BlueTape.Aion.DataAccess.MongoDB.Abstractions;
using BlueTape.Aion.DataAccess.MongoDB.Entities.User;
using BlueTape.Aion.Domain.Constants;
using BlueTape.Aion.Domain.DTOs.Company;
using BlueTape.Common.ExceptionHandling.Extensions;
using BlueTape.CompanyClient.Abstractions;
using BlueTape.Integrations.Aion.Infrastructure.Enums;
using BueTape.Aion.Infrastructure.Exceptions;
using BueTape.Aion.Infrastructure.Extensions;
using Microsoft.Extensions.Logging;

namespace BlueTape.Aion.Application.Service;

public class CompanyService : ICompanyService
{
    private readonly IAionHttpClient _aionHttpClient;
    private readonly ICompanyRepository _companyRepository;
    private readonly IUserRepository _userRepository;
    private readonly IUserRoleRepository _userRoleRepository;
    private readonly ICompanyHttpClient _companyHttpClient;
    private readonly IErrorNotificationService _notificationService;

    private readonly ILogger<CompanyService> _logger;

    public CompanyService(
        IAionHttpClient aionHttpClient,
        ICompanyRepository companyRepository,
        IUserRepository userRepository,
        IUserRoleRepository userRoleRepository,
        ICompanyHttpClient companyHttpClient,
        IErrorNotificationService notificationService,
        ILogger<CompanyService> logger)
    {
        _aionHttpClient = aionHttpClient;
        _companyRepository = companyRepository;
        _userRepository = userRepository;
        _userRoleRepository = userRoleRepository;
        _companyHttpClient = companyHttpClient;
        _notificationService = notificationService;
        _logger = logger;
    }

    public async Task<CompanyDto> SyncCompanyWithAionAsync(string companyId, string paymentSubscriptionType, CancellationToken ctx)
    {
        var paymentSubscription = paymentSubscriptionType.ParseToPaymentSubscription();
        _logger.LogInformation("Start preprocessing AchPull by companyId: {companyId}, subscription type: {type}", companyId, paymentSubscription.ToString());

        var company = await _companyRepository.GetByCompanyId(companyId, ctx);

        if (company is null) throw new CompanyDoesNotExistException(companyId);

        if (company.AionSettings is null ||
            string.IsNullOrEmpty(company.AionSettings.GetCounterPartyId(paymentSubscription)) ||
            string.IsNullOrEmpty(company.AionSettings.GetCounterPartyObjectId(paymentSubscription)))
        {
            _logger.LogInformation("Aion settings does not exist. Start creation process");

            var accountName = company.LegalName ?? company.Name ?? await GenerateAndSetCompanyName(company, ctx);

            if (string.IsNullOrEmpty(accountName))
                throw new CompanyNameDoesNotExistException(company.BlueTapeCompanyId);

            try
            {
                var companyPaymentDetailsModel = await _companyHttpClient.GetCompanyPaymentDetailsByIdAsync(company.BlueTapeCompanyId, ctx);

                if (companyPaymentDetailsModel is not null && !string.IsNullOrEmpty(companyPaymentDetailsModel.PublicIdentifier))
                {
                    accountName += $"-{companyPaymentDetailsModel.PublicIdentifier}";
                }
            }
            catch (Exception ex)
            {
                await _notificationService.Notify(ex.GetSlackEventMessageBody(EnvironmentExtensions.GetExecutionEnvironment(), AionFunctionConstants.ProjectName), ctx);
            }

            var counterPartyResponse = await _aionHttpClient.CreateCounterParty(new CreateCounterpartyRequest()
            {
                Counterparty = new CounterpartyRequest
                {
                    Type = company.IsBusiness ? CounterPartyType.Business.ToString() : CounterPartyType.Individual.ToString(),
                    NameOnAccount = accountName,
                    Email = company.Email ?? (await GetCompanyEmail(company.BlueTapeCompanyId, ctx))!
                }
            }, paymentSubscription, ctx);

            company.AionSettings = GetCompanyAionSettingsDto(counterPartyResponse, paymentSubscription);

            await _companyRepository.UpdateAionSettingsAsync(company, ctx);
        }

        _logger.LogInformation("Finish preprocessing AchPull by companyId: {companyId}", companyId);

        return company;
    }

    private async Task<string?> GetCompanyEmail(string companyId, CancellationToken ct)
    {
        return (await GetUserByCompanyId(companyId, ct))?.Email;
    }

    private async Task<string?> GenerateAndSetCompanyName(CompanyDto company, CancellationToken ct)
    {
        var user = await GetUserByCompanyId(company.BlueTapeCompanyId, ct);

        if (user is null || (string.IsNullOrEmpty(user.FirstName) && string.IsNullOrEmpty(user.LastName)))
            return null;

        var companyName = $"{user.FirstName} {user.LastName}";
        company.Name = companyName;

        await _companyRepository.UpdateCompanyNameAsync(company, ct);

        return companyName;
    }

    private async Task<UserEntity?> GetUserByCompanyId(string companyId, CancellationToken ct)
    {
        var userRoles = await _userRoleRepository.GetByCompanyId(companyId, ct);

        if (userRoles == null || userRoles.Count == 0)
        {
            _logger.LogWarning("No user roles found for company: {companyId}", companyId);
            return null;
        }

        var externalIds = userRoles.Select(x => x.ExternalId).ToList();
        var users = await _userRepository.GetByExternalId(externalIds, ct);

        if (users == null || users.Count == 0)
        {
            _logger.LogWarning("No users found for the provided external ids.");
            return null;
        }

        var ownerUserExternalId = userRoles.Find(x => x.Role == UserConstants.UserRoleOwner)?.ExternalId;

        var user = (string.IsNullOrEmpty(ownerUserExternalId)
            ? users.FirstOrDefault(x => !string.IsNullOrEmpty(x.Email))
            : users.FirstOrDefault(x => x.ExternalId == ownerUserExternalId))
                   ?? users[0];

        return user;
    }

    private static CompanyAionSettingsDto GetCompanyAionSettingsDto(CounterpartyResponse response,
        PaymentSubscriptionType paymentSubscription)
    {
        return paymentSubscription switch
        {
            PaymentSubscriptionType.SUBSCRIPTION1 => new CompanyAionSettingsDto
            {
                CounterPartyId = response.CounterpartyObject.Id,
                CounterPartyObjectId = response.CounterpartyObject.ObjectId
            },
            PaymentSubscriptionType.SUBSCRIPTION2 => new CompanyAionSettingsDto
            {
                CounterPartyId2 = response.CounterpartyObject.Id,
                CounterPartyObjectId2 = response.CounterpartyObject.ObjectId
            },
            PaymentSubscriptionType.SUBSCRIPTION3 => new CompanyAionSettingsDto
            {
                CounterPartyId3 = response.CounterpartyObject.Id,
                CounterPartyObjectId3 = response.CounterpartyObject.ObjectId
            },
            _ => new CompanyAionSettingsDto(),
        };
    }
}
