import { Page } from '@playwright/test';
import {BasePage} from '../../../base.page';

export class InvoicesList extends BasePage {
    constructor(page: Page){
        super(page);
    };

    containers = {
        invoiceList: this.page.locator('_react=[key^="list"] >> _react=ScrollView'),
    };

    buttons = {
        addInvoice: this.page.locator('_react=[key^="list"] >> _react=TouchableWithoutFeedback'),
        invoiceListElement: this.containers.invoiceList.locator('.r-lrvibr'),
        invoiceElementById: (invoiceId) => this.containers.invoiceList.locator(`_react=[key="${invoiceId}"]`),
    };

    listOfInvoices = {
        firstInvoiceFromBusAcc: this.page.locator("//div[contains(text(), 'ABusName')]").first(),
    };

    async uploadInvoice(){
        await super.uploadFile(this.buttons.addInvoice);
    };

    async openFirstInvoiceDetails(){
        await super.clickFistOption(this.buttons.invoiceListElement);
    };

    async clickOnFirstRequiredInvoice() {
        await this.listOfInvoices.firstInvoiceFromBusAcc.click();
    }

    async clickOnInvoiceRow(invoiceName){
        await this.page.waitForLoadState('networkidle')
        const invoiceRow = this.page.locator('[role="row"]', { hasText: invoiceName })
        const viewButton = invoiceRow.locator('text=View');
        await viewButton.click();
    };
}