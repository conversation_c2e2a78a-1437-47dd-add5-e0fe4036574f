﻿using BlueTape.Integrations.Aion.Ach.CreateAchTransfer;
using FluentValidation;

namespace BlueTape.Aion.API.Validators.Ach;

public class CreateAchPullModelValidator : AbstractValidator<CreateAchModel>
{
    public CreateAchPullModelValidator()
    {
        RuleSet(ValidationConstant.ManualValidation, () =>
        {
            RuleFor(x => x.Receiver)
                .NotNull()
                .SetValidator(new ReceiverModelValidator());

            RuleFor(x => x.TransactionNumber)
                .NotNull().NotEmpty();

            RuleFor(x => x.Amount)
                .NotNull();
            RuleFor(x => x.Description)
                .MaximumLength(50);

            RuleFor(x => x.Addenda)
                .Must(array => array.Sum(s => s.Length) <= 80)
                .WithMessage("The total number of characters in the array must not exceed 80.");
        });
    }
}