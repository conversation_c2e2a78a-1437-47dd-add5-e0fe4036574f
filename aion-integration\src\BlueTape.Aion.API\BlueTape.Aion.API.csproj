﻿<Project Sdk="Microsoft.NET.Sdk.Web">

    <PropertyGroup>
        <TargetFramework>net8.0</TargetFramework>
        <ImplicitUsings>enable</ImplicitUsings>
        <DockerDefaultTargetOS>Linux</DockerDefaultTargetOS>
        <Nullable>disable</Nullable>
        <GenerateRuntimeConfigurationFiles>true</GenerateRuntimeConfigurationFiles>
        <AWSProjectType>Lambda</AWSProjectType>
        <CopyLocalLockFileAssemblies>true</CopyLocalLockFileAssemblies>
        <PlatformTarget>AnyCPU</PlatformTarget>
        <RootNamespace>BlueTape.Aion.API</RootNamespace>
        <GenerateDocumentationFile>True</GenerateDocumentationFile>
        <LangVersion>latestmajor</LangVersion>
    </PropertyGroup>

    <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|AnyCPU'">
        <NoWarn>1701;1702;1591</NoWarn>
    </PropertyGroup>

    <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|AnyCPU'">
        <NoWarn>1701;1702;1591</NoWarn>
    </PropertyGroup>

    <PropertyGroup Condition=" '$(RunConfiguration)' == 'DEV BlueTape.Aion.API' " />
    <PropertyGroup Condition=" '$(RunConfiguration)' == 'BETA BlueTape.Aion.API' " />
    <PropertyGroup Condition=" '$(RunConfiguration)' == 'QA BlueTape.Aion.API' " />
    <ItemGroup>
        <PackageReference Include="Azure.Extensions.AspNetCore.Configuration.Secrets" Version="1.3.0" />
        <PackageReference Include="Azure.Security.KeyVault.Secrets" Version="4.5.0" />
        <PackageReference Include="Azure.Identity" Version="1.10.4" />
        <PackageReference Include="Microsoft.ApplicationInsights.AspNetCore" Version="2.22.0" />
        <PackageReference Include="Serilog.Sinks.ApplicationInsights" Version="4.0.0" />
        
        <PackageReference Include="Amazon.Lambda.AspNetCoreServer.Hosting" Version="1.5.0" />
        <PackageReference Include="AWSSDK.Extensions.NETCore.Setup" Version="3.7.2" />
        <PackageReference Include="BlueTape.Logging" Version="1.0.0" />
        <PackageReference Include="Swashbuckle.AspNetCore" Version="6.2.3" />
        <PackageReference Include="NSwag.AspNetCore" Version="14.0.0" />
        <PackageReference Include="FluentValidation" Version="11.0.2" />
        <PackageReference Include="FluentValidation.AspNetCore" Version="11.0.2" />
        <PackageReference Include="FluentValidation.DependencyInjectionExtensions" Version="11.0.2" />
        <PackageReference Include="BlueTape.AWSMessaging" Version="2.0.5" />
        <PackageReference Include="BlueTape.Swagger" Version="1.0.0" />
    </ItemGroup>

    <ItemGroup>
        <None Update="appsettings.dev.json">
            <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
            <DependentUpon>appsettings.json</DependentUpon>
        </None>
        <None Update="appsettings.prod.json">
            <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
            <DependentUpon>appsettings.json</DependentUpon>
        </None>
        <None Update="appsettings.qa.json">
            <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
            <DependentUpon>appsettings.json</DependentUpon>
        </None>
        <None Update="appsettings.beta.json">
            <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
            <DependentUpon>appsettings.json</DependentUpon>
        </None>
        <None Update="appsettings.json">
            <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
        </None>
    </ItemGroup>

    <ItemGroup>
      <ProjectReference Include="..\BlueTape.Aion.Application\BlueTape.Aion.Application.csproj" />
    </ItemGroup>
</Project>
