@startuml

title CreditStatus step

participant "Previous Step" as ps #LightGray
participant "CreditStatus" as cr #LightGray
participant "OnBoarding\nService" as onbs #SkyBlue
participant "Company Service" as cs #SkyBlue
database "Mongo" as db #SkyBlue
participant "Experian\nintegration" as exi #LightGray
participant "Experian" as ex #Orange

autonumber

ps -> cr
== Event Wrapper ==
cr -> onbs : Check loan application
cr -> onbs : Create draft if not exists
cr -> onbs : Updating progress
cr -> onbs : Save previous outputs
== CreditStatus step ==
cr -> cs : Gets company details
cr --> cr : Map data to request
cr -> exi : Get Experian ReportsBop
cr -> exi : Get owners data
cr -> exi : Get bankruptcies
cr -> exi : Get judgments
cr -> exi : Get liens
cr -> exi : Get trades
cr -> exi : Get credit status
exi -> ex : Forwards request\neach time
cr --> cr : Calculate DBTTradeLines

@enduml