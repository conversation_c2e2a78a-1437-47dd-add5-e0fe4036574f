@startuml UserInteractionService
title UserInteractionService (component diagram)

!include <awslib/AWSCommon>

' Uncomment the following line to create simplified view
!include <awslib/AWSSimplified>

!include <awslib/Compute/Lambda>
!include <awslib/ApplicationIntegration/SQS>
!include <awslib/Database/DocumentDBwithMongoDBcompatibility>
!include <awslib/ApplicationIntegration/APIGateway>
!include <awslib/ApplicationIntegration/StepFunctions>

left to right direction
skinparam responseMessageBelowArrow true

component "Provider" as prov #LightSeaGreen
component "Templates" as templ #LightSeaGreen
component "Linqpal" as linq #SkyBlue
component "Linqpal" as linq2 #SkyBlue

SQS(sqs, "SQS.fifo", "")
DocumentDBwithMongoDBcompatibility(mongo, "MongoDB", "")
Lambda(lambdaReplies, "Replies\nreads SQS", "")
StepFunctions(stepFunctionLogic, "Logic\nhandles states", "")
Lambda(lambdaOperations, "Operations\nintegrates Linqpal", "")
Lambda(lambdaMessages, "Messages\nsends notifications", "")

sqs -d-> lambdaReplies
lambdaReplies -d-> stepFunctionLogic
stepFunctionLogic <-d-> lambdaOperations
lambdaOperations <-d-> linq
stepFunctionLogic -r-> lambdaMessages
lambdaMessages -r-> prov
lambdaMessages -u-> templ
linq -r-> mongo
linq2 -u-> lambdaMessages

@enduml