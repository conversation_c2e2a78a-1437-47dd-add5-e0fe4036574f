{"version": 2, "dgSpecHash": "qgUl5khWQkc=", "success": true, "projectFilePath": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\back-office-decision-engine\\src\\BlueTape.BackOffice.DecisionEngine.Api.Tests\\BlueTape.BackOffice.DecisionEngine.Api.Tests.csproj", "expectedPackageFiles": ["C:\\Users\\<USER>\\.nuget\\packages\\automapper\\12.0.1\\automapper.12.0.1.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\automapper.extensions.expressionmapping\\6.0.4\\automapper.extensions.expressionmapping.6.0.4.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\automapper.extensions.microsoft.dependencyinjection\\12.0.1\\automapper.extensions.microsoft.dependencyinjection.12.0.1.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\awssdk.core\\3.7.302.6\\awssdk.core.3.7.302.6.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\awssdk.extensions.netcore.setup\\3.7.2\\awssdk.extensions.netcore.setup.3.7.2.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\awssdk.keymanagementservice\\3.7.300.46\\awssdk.keymanagementservice.3.7.300.46.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\awssdk.s3\\3.7.10\\awssdk.s3.3.7.10.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\awssdk.secretsmanager\\3.7.302.21\\awssdk.secretsmanager.3.7.302.21.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\awssdk.secretsmanager.caching\\1.0.6\\awssdk.secretsmanager.caching.1.0.6.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\awssdk.securitytoken\\3.7.300.47\\awssdk.securitytoken.3.7.300.47.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\awssdk.simplenotificationservice\\3.7.200.52\\awssdk.simplenotificationservice.3.7.200.52.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\awssdk.stepfunctions\\3.7.100.8\\awssdk.stepfunctions.3.7.100.8.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\azure.core\\1.36.0\\azure.core.1.36.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\azure.core.amqp\\1.3.0\\azure.core.amqp.1.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\azure.extensions.aspnetcore.configuration.secrets\\1.3.0\\azure.extensions.aspnetcore.configuration.secrets.1.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\azure.identity\\1.10.4\\azure.identity.1.10.4.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\azure.messaging.servicebus\\7.17.1\\azure.messaging.servicebus.7.17.1.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\azure.security.keyvault.keys\\4.5.0\\azure.security.keyvault.keys.4.5.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\azure.security.keyvault.secrets\\4.5.0\\azure.security.keyvault.secrets.4.5.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\bluetape.awss3\\1.1.3\\bluetape.awss3.1.1.3.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\bluetape.awsstepfunction\\1.0.4\\bluetape.awsstepfunction.1.0.4.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\bluetape.azurekeyvault\\1.0.3\\bluetape.azurekeyvault.1.0.3.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\bluetape.common.exceptionhandling\\1.0.8\\bluetape.common.exceptionhandling.1.0.8.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\bluetape.common.extensions\\1.1.0\\bluetape.common.extensions.1.1.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\bluetape.common.fileservice\\1.0.6\\bluetape.common.fileservice.1.0.6.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\bluetape.common.validation\\1.0.4\\bluetape.common.validation.1.0.4.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\bluetape.companyservice\\1.3.4\\bluetape.companyservice.1.3.4.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\bluetape.companyservice.common\\1.1.21\\bluetape.companyservice.common.1.1.21.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\bluetape.firebase\\1.0.1\\bluetape.firebase.1.0.1.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\bluetape.integrations.aion\\1.0.17\\bluetape.integrations.aion.1.0.17.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\bluetape.integrations.aion.infrastructure\\1.0.17\\bluetape.integrations.aion.infrastructure.1.0.17.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\bluetape.integrations.giact\\1.0.3\\bluetape.integrations.giact.1.0.3.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\bluetape.integrations.plaid\\1.0.7\\bluetape.integrations.plaid.1.0.7.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\bluetape.integrations.plaid.infrastructure\\1.0.0\\bluetape.integrations.plaid.infrastructure.1.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\bluetape.invoiceclient\\1.0.24\\bluetape.invoiceclient.1.0.24.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\bluetape.invoiceservice\\1.0.43\\bluetape.invoiceservice.1.0.43.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\bluetape.invoiceservice.common\\1.1.3\\bluetape.invoiceservice.common.1.1.3.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\bluetape.logging\\1.0.0\\bluetape.logging.1.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\bluetape.ls\\1.1.76\\bluetape.ls.1.1.76.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\bluetape.ls.domain\\1.1.36\\bluetape.ls.domain.1.1.36.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\bluetape.notification.sender\\1.0.2\\bluetape.notification.sender.1.0.2.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\bluetape.obs\\1.6.72\\bluetape.obs.1.6.72.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\bluetape.paymentservice\\1.0.14\\bluetape.paymentservice.1.0.14.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\bluetape.servicebusmessaging\\1.0.9\\bluetape.servicebusmessaging.1.0.9.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\bluetape.sns\\1.0.2\\bluetape.sns.1.0.2.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\bluetape.utilities\\1.4.6\\bluetape.utilities.1.4.6.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\closedxml\\0.102.3\\closedxml.0.102.3.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\csvhelper\\32.0.3\\csvhelper.32.0.3.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\dateonlytimeonly.aspnet\\2.1.1\\dateonlytimeonly.aspnet.2.1.1.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\dateonlytimeonly.aspnet.swashbuckle\\2.1.1\\dateonlytimeonly.aspnet.swashbuckle.2.1.1.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\diffengine\\11.3.0\\diffengine.11.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\documentformat.openxml\\2.16.0\\documentformat.openxml.2.16.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\elastic.commonschema\\1.5.3\\elastic.commonschema.1.5.3.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\elastic.commonschema.serilog\\1.5.3\\elastic.commonschema.serilog.1.5.3.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\emptyfiles\\4.4.0\\emptyfiles.4.4.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\excelnumberformat\\1.1.0\\excelnumberformat.1.1.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\firebaseadmin\\2.4.0\\firebaseadmin.2.4.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\fluentvalidation\\11.9.1\\fluentvalidation.11.9.1.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\fluentvalidation.aspnetcore\\11.3.0\\fluentvalidation.aspnetcore.11.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\fluentvalidation.dependencyinjectionextensions\\11.9.1\\fluentvalidation.dependencyinjectionextensions.11.9.1.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\google.api.gax\\3.2.0\\google.api.gax.3.2.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\google.api.gax.rest\\3.2.0\\google.api.gax.rest.3.2.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\google.apis\\1.49.0\\google.apis.1.49.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\google.apis.auth\\1.49.0\\google.apis.auth.1.49.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\google.apis.core\\1.49.0\\google.apis.core.1.49.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\irony.netcore\\1.0.11\\irony.netcore.1.0.11.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\libphonenumber-csharp\\8.12.45\\libphonenumber-csharp.8.12.45.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.applicationinsights\\2.22.0\\microsoft.applicationinsights.2.22.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.applicationinsights.aspnetcore\\2.22.0\\microsoft.applicationinsights.aspnetcore.2.22.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.applicationinsights.dependencycollector\\2.22.0\\microsoft.applicationinsights.dependencycollector.2.22.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.applicationinsights.eventcountercollector\\2.22.0\\microsoft.applicationinsights.eventcountercollector.2.22.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.applicationinsights.perfcountercollector\\2.22.0\\microsoft.applicationinsights.perfcountercollector.2.22.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.applicationinsights.windowsserver\\2.22.0\\microsoft.applicationinsights.windowsserver.2.22.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.applicationinsights.windowsserver.telemetrychannel\\2.22.0\\microsoft.applicationinsights.windowsserver.telemetrychannel.2.22.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.authentication\\2.2.0\\microsoft.aspnetcore.authentication.2.2.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.authentication.abstractions\\2.2.0\\microsoft.aspnetcore.authentication.abstractions.2.2.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.authentication.core\\2.2.0\\microsoft.aspnetcore.authentication.core.2.2.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.cryptography.internal\\2.2.0\\microsoft.aspnetcore.cryptography.internal.2.2.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.dataprotection\\2.2.0\\microsoft.aspnetcore.dataprotection.2.2.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.dataprotection.abstractions\\2.2.0\\microsoft.aspnetcore.dataprotection.abstractions.2.2.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.hosting\\2.1.1\\microsoft.aspnetcore.hosting.2.1.1.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.hosting.abstractions\\2.2.0\\microsoft.aspnetcore.hosting.abstractions.2.2.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.hosting.server.abstractions\\2.2.0\\microsoft.aspnetcore.hosting.server.abstractions.2.2.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.http\\2.2.0\\microsoft.aspnetcore.http.2.2.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.http.abstractions\\2.2.0\\microsoft.aspnetcore.http.abstractions.2.2.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.http.extensions\\2.2.0\\microsoft.aspnetcore.http.extensions.2.2.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.http.features\\2.2.0\\microsoft.aspnetcore.http.features.2.2.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.openapi\\8.0.0\\microsoft.aspnetcore.openapi.8.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.webutilities\\2.2.0\\microsoft.aspnetcore.webutilities.2.2.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.azure.amqp\\2.6.4\\microsoft.azure.amqp.2.6.4.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.bcl.asyncinterfaces\\1.1.1\\microsoft.bcl.asyncinterfaces.1.1.1.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.codeanalysis.netanalyzers\\8.0.0\\microsoft.codeanalysis.netanalyzers.8.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.codecoverage\\17.10.0\\microsoft.codecoverage.17.10.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.csharp\\4.7.0\\microsoft.csharp.4.7.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.apidescription.server\\6.0.5\\microsoft.extensions.apidescription.server.6.0.5.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.caching.abstractions\\7.0.0\\microsoft.extensions.caching.abstractions.7.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.caching.memory\\7.0.0\\microsoft.extensions.caching.memory.7.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.configuration\\8.0.0\\microsoft.extensions.configuration.8.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.configuration.abstractions\\8.0.0\\microsoft.extensions.configuration.abstractions.8.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.configuration.binder\\8.0.0\\microsoft.extensions.configuration.binder.8.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.configuration.environmentvariables\\2.1.1\\microsoft.extensions.configuration.environmentvariables.2.1.1.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.configuration.fileextensions\\3.1.0\\microsoft.extensions.configuration.fileextensions.3.1.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.configuration.json\\3.1.0\\microsoft.extensions.configuration.json.3.1.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.dependencyinjection\\8.0.0\\microsoft.extensions.dependencyinjection.8.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.dependencyinjection.abstractions\\8.0.0\\microsoft.extensions.dependencyinjection.abstractions.8.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.dependencymodel\\3.0.0\\microsoft.extensions.dependencymodel.3.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.diagnostics\\8.0.0\\microsoft.extensions.diagnostics.8.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.diagnostics.abstractions\\8.0.0\\microsoft.extensions.diagnostics.abstractions.8.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.fileproviders.abstractions\\6.0.0\\microsoft.extensions.fileproviders.abstractions.6.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.fileproviders.physical\\3.1.0\\microsoft.extensions.fileproviders.physical.3.1.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.filesystemglobbing\\3.1.0\\microsoft.extensions.filesystemglobbing.3.1.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.hosting.abstractions\\6.0.0\\microsoft.extensions.hosting.abstractions.6.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.http\\8.0.0\\microsoft.extensions.http.8.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.http.polly\\8.0.0\\microsoft.extensions.http.polly.8.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.logging\\8.0.0\\microsoft.extensions.logging.8.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.logging.abstractions\\8.0.0\\microsoft.extensions.logging.abstractions.8.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.logging.applicationinsights\\2.22.0\\microsoft.extensions.logging.applicationinsights.2.22.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.objectpool\\2.2.0\\microsoft.extensions.objectpool.2.2.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.options\\8.0.1\\microsoft.extensions.options.8.0.1.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.options.configurationextensions\\8.0.0\\microsoft.extensions.options.configurationextensions.8.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.primitives\\8.0.0\\microsoft.extensions.primitives.8.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.webencoders\\2.2.0\\microsoft.extensions.webencoders.2.2.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.identity.client\\4.56.0\\microsoft.identity.client.4.56.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.identity.client.extensions.msal\\4.56.0\\microsoft.identity.client.extensions.msal.4.56.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.identitymodel.abstractions\\6.22.0\\microsoft.identitymodel.abstractions.6.22.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.net.http.headers\\2.2.0\\microsoft.net.http.headers.2.2.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.net.test.sdk\\17.10.0\\microsoft.net.test.sdk.17.10.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.netcore.platforms\\1.1.1\\microsoft.netcore.platforms.1.1.1.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.netcore.targets\\1.1.3\\microsoft.netcore.targets.1.1.3.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.openapi\\1.4.3\\microsoft.openapi.1.4.3.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.testplatform.objectmodel\\17.10.0\\microsoft.testplatform.objectmodel.17.10.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.testplatform.testhost\\17.10.0\\microsoft.testplatform.testhost.17.10.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.win32.primitives\\4.3.0\\microsoft.win32.primitives.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.win32.registry\\4.5.0\\microsoft.win32.registry.4.5.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.win32.systemevents\\6.0.0\\microsoft.win32.systemevents.6.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\mongodb.bson\\2.25.0\\mongodb.bson.2.25.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\netstandard.library\\1.6.1\\netstandard.library.1.6.1.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\newtonsoft.json\\13.0.1\\newtonsoft.json.13.0.1.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\polly\\7.2.4\\polly.7.2.4.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\polly.extensions.http\\3.0.0\\polly.extensions.http.3.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\runtime.debian.8-x64.runtime.native.system.security.cryptography.openssl\\4.3.2\\runtime.debian.8-x64.runtime.native.system.security.cryptography.openssl.4.3.2.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\runtime.fedora.23-x64.runtime.native.system.security.cryptography.openssl\\4.3.2\\runtime.fedora.23-x64.runtime.native.system.security.cryptography.openssl.4.3.2.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\runtime.fedora.24-x64.runtime.native.system.security.cryptography.openssl\\4.3.2\\runtime.fedora.24-x64.runtime.native.system.security.cryptography.openssl.4.3.2.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\runtime.native.system\\4.3.0\\runtime.native.system.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\runtime.native.system.io.compression\\4.3.0\\runtime.native.system.io.compression.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\runtime.native.system.net.http\\4.3.0\\runtime.native.system.net.http.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\runtime.native.system.security.cryptography.apple\\4.3.0\\runtime.native.system.security.cryptography.apple.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\runtime.native.system.security.cryptography.openssl\\4.3.2\\runtime.native.system.security.cryptography.openssl.4.3.2.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\runtime.opensuse.13.2-x64.runtime.native.system.security.cryptography.openssl\\4.3.2\\runtime.opensuse.13.2-x64.runtime.native.system.security.cryptography.openssl.4.3.2.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\runtime.opensuse.42.1-x64.runtime.native.system.security.cryptography.openssl\\4.3.2\\runtime.opensuse.42.1-x64.runtime.native.system.security.cryptography.openssl.4.3.2.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\runtime.osx.10.10-x64.runtime.native.system.security.cryptography.apple\\4.3.0\\runtime.osx.10.10-x64.runtime.native.system.security.cryptography.apple.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\runtime.osx.10.10-x64.runtime.native.system.security.cryptography.openssl\\4.3.2\\runtime.osx.10.10-x64.runtime.native.system.security.cryptography.openssl.4.3.2.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\runtime.rhel.7-x64.runtime.native.system.security.cryptography.openssl\\4.3.2\\runtime.rhel.7-x64.runtime.native.system.security.cryptography.openssl.4.3.2.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\runtime.ubuntu.14.04-x64.runtime.native.system.security.cryptography.openssl\\4.3.2\\runtime.ubuntu.14.04-x64.runtime.native.system.security.cryptography.openssl.4.3.2.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\runtime.ubuntu.16.04-x64.runtime.native.system.security.cryptography.openssl\\4.3.2\\runtime.ubuntu.16.04-x64.runtime.native.system.security.cryptography.openssl.4.3.2.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\runtime.ubuntu.16.10-x64.runtime.native.system.security.cryptography.openssl\\4.3.2\\runtime.ubuntu.16.10-x64.runtime.native.system.security.cryptography.openssl.4.3.2.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\serilog\\2.12.0\\serilog.2.12.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\serilog.aspnetcore\\6.0.1\\serilog.aspnetcore.6.0.1.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\serilog.enrichers.globallogcontext\\2.1.0\\serilog.enrichers.globallogcontext.2.1.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\serilog.extensions.hosting\\5.0.1\\serilog.extensions.hosting.5.0.1.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\serilog.extensions.logging\\3.1.0\\serilog.extensions.logging.3.1.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\serilog.formatting.compact\\1.1.0\\serilog.formatting.compact.1.1.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\serilog.settings.configuration\\3.3.0\\serilog.settings.configuration.3.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\serilog.sinks.applicationinsights\\4.0.0\\serilog.sinks.applicationinsights.4.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\serilog.sinks.console\\4.1.0\\serilog.sinks.console.4.1.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\serilog.sinks.debug\\2.0.0\\serilog.sinks.debug.2.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\serilog.sinks.file\\5.0.0\\serilog.sinks.file.5.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\serilog.sinks.http\\8.0.0\\serilog.sinks.http.8.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\serilog.sinks.logz.io\\7.1.0\\serilog.sinks.logz.io.7.1.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\serilog.sinks.periodicbatching\\3.1.0\\serilog.sinks.periodicbatching.3.1.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\shouldly\\4.2.1\\shouldly.4.2.1.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\sixlabors.fonts\\1.0.0\\sixlabors.fonts.1.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\swashbuckle.aspnetcore\\6.4.0\\swashbuckle.aspnetcore.6.4.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\swashbuckle.aspnetcore.redoc\\6.5.0\\swashbuckle.aspnetcore.redoc.6.5.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\swashbuckle.aspnetcore.swagger\\6.4.0\\swashbuckle.aspnetcore.swagger.6.4.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\swashbuckle.aspnetcore.swaggergen\\6.4.0\\swashbuckle.aspnetcore.swaggergen.6.4.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\swashbuckle.aspnetcore.swaggerui\\6.4.0\\swashbuckle.aspnetcore.swaggerui.6.4.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.appcontext\\4.3.0\\system.appcontext.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.buffers\\4.5.0\\system.buffers.4.5.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.codedom\\6.0.0\\system.codedom.6.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.collections\\4.3.0\\system.collections.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.collections.concurrent\\4.3.0\\system.collections.concurrent.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.collections.immutable\\1.7.1\\system.collections.immutable.1.7.1.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.configuration.configurationmanager\\6.0.0\\system.configuration.configurationmanager.6.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.console\\4.3.0\\system.console.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.diagnostics.debug\\4.3.0\\system.diagnostics.debug.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.diagnostics.diagnosticsource\\8.0.0\\system.diagnostics.diagnosticsource.8.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.diagnostics.performancecounter\\6.0.0\\system.diagnostics.performancecounter.6.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.diagnostics.tools\\4.3.0\\system.diagnostics.tools.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.diagnostics.tracing\\4.3.0\\system.diagnostics.tracing.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.drawing.common\\6.0.0\\system.drawing.common.6.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.globalization\\4.3.0\\system.globalization.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.globalization.calendars\\4.3.0\\system.globalization.calendars.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.globalization.extensions\\4.3.0\\system.globalization.extensions.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.io\\4.3.0\\system.io.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.io.compression\\4.3.0\\system.io.compression.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.io.compression.zipfile\\4.3.0\\system.io.compression.zipfile.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.io.filesystem\\4.3.0\\system.io.filesystem.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.io.filesystem.accesscontrol\\5.0.0\\system.io.filesystem.accesscontrol.5.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.io.filesystem.primitives\\4.3.0\\system.io.filesystem.primitives.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.io.packaging\\6.0.0\\system.io.packaging.6.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.linq\\4.3.0\\system.linq.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.linq.expressions\\4.3.0\\system.linq.expressions.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.management\\6.0.1\\system.management.6.0.1.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.memory\\4.5.5\\system.memory.4.5.5.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.memory.data\\1.0.2\\system.memory.data.1.0.2.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.net.http\\4.3.4\\system.net.http.4.3.4.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.net.primitives\\4.3.0\\system.net.primitives.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.net.sockets\\4.3.0\\system.net.sockets.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.numerics.vectors\\4.5.0\\system.numerics.vectors.4.5.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.objectmodel\\4.3.0\\system.objectmodel.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.reflection\\4.3.0\\system.reflection.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.reflection.emit\\4.3.0\\system.reflection.emit.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.reflection.emit.ilgeneration\\4.3.0\\system.reflection.emit.ilgeneration.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.reflection.emit.lightweight\\4.3.0\\system.reflection.emit.lightweight.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.reflection.extensions\\4.3.0\\system.reflection.extensions.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.reflection.metadata\\1.6.0\\system.reflection.metadata.1.6.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.reflection.primitives\\4.3.0\\system.reflection.primitives.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.reflection.typeextensions\\4.3.0\\system.reflection.typeextensions.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.resources.resourcemanager\\4.3.0\\system.resources.resourcemanager.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.runtime\\4.3.1\\system.runtime.4.3.1.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.runtime.compilerservices.unsafe\\5.0.0\\system.runtime.compilerservices.unsafe.5.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.runtime.extensions\\4.3.0\\system.runtime.extensions.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.runtime.handles\\4.3.0\\system.runtime.handles.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.runtime.interopservices\\4.3.0\\system.runtime.interopservices.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.runtime.interopservices.runtimeinformation\\4.3.0\\system.runtime.interopservices.runtimeinformation.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.runtime.numerics\\4.3.0\\system.runtime.numerics.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.security.accesscontrol\\6.0.0\\system.security.accesscontrol.6.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.security.cryptography.algorithms\\4.3.0\\system.security.cryptography.algorithms.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.security.cryptography.cng\\4.5.0\\system.security.cryptography.cng.4.5.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.security.cryptography.csp\\4.3.0\\system.security.cryptography.csp.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.security.cryptography.encoding\\4.3.0\\system.security.cryptography.encoding.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.security.cryptography.openssl\\4.3.0\\system.security.cryptography.openssl.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.security.cryptography.pkcs\\4.5.0\\system.security.cryptography.pkcs.4.5.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.security.cryptography.primitives\\4.3.0\\system.security.cryptography.primitives.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.security.cryptography.protecteddata\\6.0.0\\system.security.cryptography.protecteddata.6.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.security.cryptography.x509certificates\\4.3.0\\system.security.cryptography.x509certificates.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.security.cryptography.xml\\4.5.0\\system.security.cryptography.xml.4.5.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.security.permissions\\6.0.0\\system.security.permissions.6.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.security.principal.windows\\5.0.0\\system.security.principal.windows.5.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.text.encoding\\4.3.0\\system.text.encoding.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.text.encoding.extensions\\4.3.0\\system.text.encoding.extensions.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.text.encodings.web\\4.7.2\\system.text.encodings.web.4.7.2.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.text.json\\4.7.2\\system.text.json.4.7.2.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.text.regularexpressions\\4.3.1\\system.text.regularexpressions.4.3.1.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.threading\\4.3.0\\system.threading.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.threading.tasks\\4.3.0\\system.threading.tasks.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.threading.tasks.extensions\\4.5.4\\system.threading.tasks.extensions.4.5.4.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.threading.timer\\4.3.0\\system.threading.timer.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.windows.extensions\\6.0.0\\system.windows.extensions.6.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.xml.readerwriter\\4.3.0\\system.xml.readerwriter.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.xml.xdocument\\4.3.0\\system.xml.xdocument.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\xlparser\\1.5.2\\xlparser.1.5.2.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\xunit\\2.8.1\\xunit.2.8.1.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\xunit.abstractions\\2.0.3\\xunit.abstractions.2.0.3.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\xunit.analyzers\\1.14.0\\xunit.analyzers.1.14.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\xunit.assert\\2.8.1\\xunit.assert.2.8.1.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\xunit.core\\2.8.1\\xunit.core.2.8.1.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\xunit.extensibility.core\\2.8.1\\xunit.extensibility.core.2.8.1.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\xunit.extensibility.execution\\2.8.1\\xunit.extensibility.execution.2.8.1.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\xunit.runner.visualstudio\\2.8.1\\xunit.runner.visualstudio.2.8.1.nupkg.sha512"], "logs": []}