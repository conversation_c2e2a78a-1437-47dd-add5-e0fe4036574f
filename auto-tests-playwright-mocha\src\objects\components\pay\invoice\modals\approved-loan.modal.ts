import {BasePage} from '../../../../base.page';

export class ApprovedLoanModal extends BasePage {
    constructor(page){
        super(page);
    };

    containers = {
        mainContainer: this.page.locator('_react=[key=".0"]'),
    };

    buttons = {
        viewLoanInformation: this.containers.mainContainer.locator('_react=[key="0"]'),
        close: this.containers.mainContainer.locator('_react=[testID="close"]'),
    };
    
    label = { 
        loanHasStarted: this.containers.mainContainer.locator('"Loan has started!"'),
    };
}