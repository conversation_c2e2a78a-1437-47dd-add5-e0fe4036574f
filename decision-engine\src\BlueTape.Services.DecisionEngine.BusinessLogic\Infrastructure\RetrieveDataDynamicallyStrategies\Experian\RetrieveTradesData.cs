﻿using BlueTape.MongoDB.Constants;
using BlueTape.Services.DecisionEngine.BusinessLogic.Abstractions.Infrastructure.RetrieveDataDynamicallyStrategies;
using BlueTape.Services.DecisionEngine.BusinessLogic.Abstractions.Services;
using BlueTape.Services.DecisionEngine.BusinessLogic.Constants;
using BlueTape.Services.DecisionEngine.BusinessLogic.Models.BusinessTrades;
using BlueTape.Services.DecisionEngine.BusinessLogic.Models.BVI;
using BlueTape.Services.DecisionEngine.BusinessLogic.Models.CreditApplication;
using BlueTape.Services.DecisionEngine.BusinessLogic.Models.Draft;
using BlueTape.Services.DecisionEngine.BusinessLogic.Models.ExperianResponses;
using BlueTape.Services.DecisionEngine.BusinessLogic.Models.ExperianResponses.Trades;
using BlueTape.Services.DecisionEngine.DataAccess.External.Constants;
using BlueTape.Services.DecisionEngine.Domain.Models.AccountAuthorization;
using BlueTape.Services.DecisionEngine.Infrastructure.Exceptions;
using BlueTape.Services.Utilities.Configuration;
using BlueTape.Utilities.Models;
using BlueTape.Utilities.Security;
using Newtonsoft.Json;
using System.Text;

namespace BlueTape.Services.DecisionEngine.BusinessLogic.Infrastructure.RetrieveDataDynamicallyStrategies.Experian;

public class RetrieveTradesData : IRetrieveDataDynamicallyStrategy
{
    private readonly IExperianService _experianService;
    private readonly IKmsEncryptionService _kmsEncryptionService;
    private readonly IConfigurationService _configurationService;

    private readonly List<string> _applicableFields = new()
    {
        ExperianConstants.FirstReportedTradeLineDate,
        ExperianConstants.DBT60PlusPercentage,
        ExperianConstants.TradeLinesPercentage,
        ExperianConstants.TotalTradeLines,
        ExperianConstants.DBT60PlusAndRevenueRatio
    };

    public RetrieveTradesData(IExperianService experianService, IKmsEncryptionService kmsEncryptionService, IConfigurationService configurationService)
    {
        _experianService = experianService;
        _kmsEncryptionService = kmsEncryptionService;
        _configurationService = configurationService;
    }

    public bool IsApplicable(IEnumerable<string?> fieldNames) => _applicableFields.Intersect(fieldNames).Any();

    public async Task<List<BviResult>> RetrieveData(AccountAuthorizationModel accountAuthorizationModel, ParsedDraftModel parsedDraft, CreditApplicationModel creditApplication, CancellationToken ct)
    {
        if (parsedDraft is null) throw new StepExecutionException(nameof(parsedDraft));

        var bin = accountAuthorizationModel.BusinessDetails?.Bin;

        if (bin is null)
            return GetResult(default);
        var globalThirdPartiesCachingIgnoranceString = await _configurationService.GetByKey(ApplicationConstants.IgnoreThirdPartiesCaching);
        var globalThirdPartiesCachingIgnorance = !string.IsNullOrEmpty(globalThirdPartiesCachingIgnoranceString) &&
                                                 globalThirdPartiesCachingIgnoranceString.Equals("True",
                                                     StringComparison.InvariantCultureIgnoreCase);
        var shouldIgnoreCaching = RetrieveDataDynamicallyExtensions.ShouldIgnoreCaching(accountAuthorizationModel, creditApplication, globalThirdPartiesCachingIgnorance);
        var thirdPartyResponse = await SendRequest(shouldIgnoreCaching, bin.Cipher, ct);
        if (!thirdPartyResponse.IsSuccess) return GetResult(thirdPartyResponse);

        var result = JsonConvert.DeserializeObject<ExperianResponse<TradesResult>>(thirdPartyResponse.Response);
        var firstReportedTradeLineDate =
            result?.Results?.TradePaymentExperiences?.TradeNewAndContinuous.Min(x => x.DateReported);
        if (firstReportedTradeLineDate != null)
        {
            accountAuthorizationModel.BusinessDetails!.FirstReportedTradeLineDate = DateOnly.FromDateTime(firstReportedTradeLineDate.Value);
        }

        var businessOutstandingBalance = result?.Results?.TradePaymentTotals?.Tradelines?.TotalAccountBalance?.Amount;
        var dbt60PlusPercentage = CalculateDbt60PlusPercentage(result);

        accountAuthorizationModel.BusinessDetails!.DBT60PlusPercentage = dbt60PlusPercentage;
        accountAuthorizationModel.BusinessDetails.TotalTradeLines = result?.Results?.TradePaymentTotals?.Tradelines?.TotalAccountBalance?.Amount;
        accountAuthorizationModel.BusinessDetails.TradeLinesPercentage = result?.Results?.TradePaymentTotals?.Tradelines?.CurrentPercentage;
        accountAuthorizationModel.BusinessDetails.BusinessOutstandingBalance = businessOutstandingBalance;
        accountAuthorizationModel.BusinessDetails.DBT60PlusAmount = businessOutstandingBalance * dbt60PlusPercentage * 0.01m;
        accountAuthorizationModel.BusinessDetails.CreditUtilizationRatio = CalculateCreditUtilizationRatio(result);
        accountAuthorizationModel.BusinessDetails.DBT60PlusAndRevenueRatio = CalculateDbt60PlusAndRevenueRatio(result, accountAuthorizationModel.BusinessDetails);

        return GetResult(thirdPartyResponse);
    }

    private static decimal? CalculateDbt60PlusPercentage(ExperianResponse<TradesResult>? result)
    {
        return result?.Results?.TradePaymentTotals?.Tradelines?.Dbt90 +
               result?.Results?.TradePaymentTotals?.Tradelines?.Dbt91Plus;
    }

    private static decimal? CalculateCreditUtilizationRatio(ExperianResponse<TradesResult>? result)
    {
        var creditUtilizationRatio = result?.Results?.TradePaymentSummary?.AllTradeLineBalance
               / result?.Results?.TradePaymentTotals?.Tradelines?.TotalHighCreditAmount?.Amount;
        if (!creditUtilizationRatio.HasValue) return null;

        return Math.Round(creditUtilizationRatio.Value, StepsImplementationConstants.RatioFractionalDigitsNumber);
    }

    private static decimal? CalculateDbt60PlusAndRevenueRatio(ExperianResponse<TradesResult>? result, BusinessDetailsModel businessDetails)
    {
        decimal? dbt60PlusAndRevenueRatio;
        if (businessDetails.CompanyIncome == 0) dbt60PlusAndRevenueRatio = 0;
        else dbt60PlusAndRevenueRatio = businessDetails.DBT60PlusPercentage * 0.01m *
                                       result?.Results?.TradePaymentSummary?.AllTradeLineBalance /
                                       businessDetails.CompanyIncome;
        if (!dbt60PlusAndRevenueRatio.HasValue) return null;

        return Math.Round(dbt60PlusAndRevenueRatio.Value, StepsImplementationConstants.RatioFractionalDigitsNumber);
    }

    private async Task<ThirdPartyResponse<string>> SendRequest(bool? shouldIgnoreCaching, string encryptedBin, CancellationToken ct)
    {
        var bin = Encoding.UTF8.GetString(await _kmsEncryptionService.Decrypt(Convert.FromBase64String(encryptedBin)));
        var businessTradesRequest = new BusinessTradesRequestModel()
        {
            Bin = bin,
            TradePaymentTotals = true,
            TradePaymentExperiences = true,
            TradePaymentSummary = true
        };

        return await _experianService.GetBusinessTrades(businessTradesRequest, shouldIgnoreCaching, ct);
    }

    private static List<BviResult> GetResult(ThirdPartyResponse<string>? result)
    {
        return new List<BviResult>
        {
            new()
            {
                IntegrationSource = IntegrationServicesNamesConstants.Experian,
                IntegrationLogId = result?.ResponseId
            }
        };
    }
}