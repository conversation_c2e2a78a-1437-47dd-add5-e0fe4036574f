using System.Runtime.Serialization;
using System.Text.Json.Serialization;

namespace BlueTape.Aion.DataAccess.External.Models.DailyLimmits;

[DataContract]
public class AionAccountLimitResponse : BaseAionResponseModel
{
    [JsonPropertyName("dailyAvailableACHBalance")]
    public decimal? DailyAvailableACHBalance { get; set; }
    
    [Json<PERSON>ropertyName("dailyAvailableWireBalance")]
    public decimal? DailyAvailableWireBalance { get; set; }
    
    [JsonPropertyName("dailyAvailableACHDebitBalance")]
    public decimal? DailyAvailableACHDebitBalance { get; set; }
    
    [JsonPropertyName("dailyAvailableInstantXferBalance")]
    public decimal? DailyAvailableInstantXferBalance { get; set; }
    
    [JsonPropertyName("windowAmount")]
    public decimal? WindowAmount { get; set; }
}