import { Page } from '@playwright/test';
import {BasePage} from '../../../base.page';

export class DrawList extends BasePage {
    constructor(page: Page){
        super(page);
    };

    async clickOnDrawRow(invoiceName){
        await this.page.waitForLoadState('networkidle')
        const invoiceRow = this.page.locator('[role="row"]', { hasText: invoiceName })
        const viewButton = invoiceRow.locator('text=Pay Now');
        await viewButton.click();
    };
}