import {BaseTest, test} from "../../test-utils";
import {postBasisRequest, sendLMSRequest} from "../../../api/common/lms-send-request";
import {expect} from "@playwright/test";

test.describe('Delete basis point @LMS @API', async () => {
    const currentDate = `${BaseTest.getCurrentDate()}`;
    const basisPointValue: number = Math.floor(Math.random() * 300);

    test('Successful deleting basic point', async () => {
        const responsePost = await postBasisRequest(basisPointValue, currentDate);

        const basisId = await responsePost.data.id;

        await sendLMSRequest('delete', `BasisPoint/${basisId}`);

        const responseGet = await sendLMSRequest('get', `BasisPoint?Id=${basisId}`);

        expect(responseGet.data)
            .toEqual([null]);
    });
});
