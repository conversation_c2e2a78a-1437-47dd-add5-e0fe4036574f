﻿using AutoMapper;
using BlueTape.Application.Tests.FixtureHelpers.FixtureAttributes;
using BlueTape.Application.Tests.MapperForTests;
using BlueTape.Company.Application.Abstractions.Service;
using BlueTape.Company.Domain.DTOs.Companies;
using BlueTape.CompanyService.Common.Enums;
using BlueTape.CompanyService.Companies;
using BlueTape.DataAccess.MongoDB.Abstractions;
using BlueTape.Document.DataAccess.EF.Abstractions;
using BlueTape.Domain.Entities;
using BlueTape.Domain.Entities.Documents;
using BlueTape.Common.Exceptions.Companies;
using Moq;
using Shouldly;
using TinyHelpers.Extensions;
using Xunit;
using CompanyEntity = BlueTape.Domain.Entities.Documents.CompanyEntity;

namespace BlueTape.Application.Tests.Company;

public class CompanyServiceTests
{
    private readonly Mock<ICompanyRepository> _companyRepositoryMock = new();
    private readonly Mock<IEfCompanyRepository> _efCompanyRepositoryMock = new();
    private readonly IMapper _mapper;
    private readonly Mock<ICompanyIdentifierService> _companyIdentifierServiceMock = new();
    private readonly Mock<IEfCompanyNoteRepository> _efCompanyNoteRepositoryMock = new();

    private readonly BlueTape.Company.Application.Services.CompanyService _companyService;

    public CompanyServiceTests()
    {
        _mapper = MapperCreator.CreateWithApplicationProfileCompany();

        _companyService = new BlueTape.Company.Application.Services.CompanyService(_companyRepositoryMock.Object,
            _efCompanyRepositoryMock.Object, _companyIdentifierServiceMock.Object, _efCompanyNoteRepositoryMock.Object,
            _mapper);
    }

    [Theory, AutoDataWithDateOnly]
    public async Task GetByCompanyId_ValidId_ReturnsCompanyDto(CompanyDto companyDto, CompanyEntity companyEntity)
    {
        companyEntity.Account!.ManualStatus = null;
        companyDto.AccountStatus = null;

        _companyRepositoryMock.Setup(x => x.GetByCompanyId(companyDto.BlueTapeCompanyId, default))
            .ReturnsAsync(companyDto);
        _efCompanyRepositoryMock.Setup(x => x.GetByLegacyId(companyDto.BlueTapeCompanyId, default))
            .ReturnsAsync(companyEntity);

        var result = await _companyService.GetByCompanyId(companyDto.BlueTapeCompanyId, default);

        result.ShouldNotBeNull();
        result.BlueTapeCompanyId.ShouldBeEquivalentTo(companyDto.BlueTapeCompanyId);
        result.AccountStatus.ShouldBeEquivalentTo(companyEntity.Account.Status);
    }

    [Theory, AutoDataWithDateOnly]
    public async Task GetByCompanyId_InvalidId_ReturnsNull(string id)
    {
        _companyRepositoryMock.Setup(x => x.GetByCompanyId(id, default))
            .ReturnsAsync((CompanyDto?)null);

        var result = await _companyService.GetByCompanyId(id, default);

        result.ShouldBeNull();
    }

    [Theory, AutoDataWithDateOnly]
    public async Task GetByCompanyIds_ValidIds_ReturnsCompanies(string[] ids, CompanyDto company1, CompanyDto company2,
        CompanyEntity companyEntity1)
    {
        company1.AccountStatus = null;
        company2.AccountStatus = null;
        var companies = new[]
        {
            company1, company2
        };

        companyEntity1.LegacyId = company1.BlueTapeCompanyId;
        companyEntity1.Account!.ManualStatus = null;
        var companyEntities = new List<CompanyEntity>()
        {
            companyEntity1
        };

        _companyRepositoryMock.Setup(x => x.GetByCompanyIds(ids, default))
            .ReturnsAsync(companies);
        _efCompanyRepositoryMock.Setup(x => x.GetByLegacyIds(ids, default))
            .ReturnsAsync(companyEntities);

        var result = await _companyService.GetByCompanyIds(ids, default);

        var resultCompany1 = result!.First(x => x.BlueTapeCompanyId == company1.BlueTapeCompanyId);
        var resultCompany2 = result!.First(x => x.BlueTapeCompanyId == company2.BlueTapeCompanyId);

        resultCompany1.BlueTapeCompanyId.ShouldBeEquivalentTo(company1.BlueTapeCompanyId);
        resultCompany1.AccountStatus.ShouldBeEquivalentTo(companyEntity1.Account.Status);

        resultCompany2.BlueTapeCompanyId.ShouldBeEquivalentTo(company2.BlueTapeCompanyId);
        resultCompany2.AccountStatus.ShouldBeNull();
    }

    [Theory, AutoDataWithDateOnly]
    public async Task GetByCompanyIdV2_ValidId_ReturnsCompanyDto(CompanyDto companyDto, CompanyEntity companyEntity)
    {
        companyEntity.Account!.ManualStatus = null;
        companyEntity.LegacyId = companyDto.BlueTapeCompanyId;
        companyDto.AccountStatus = null;

        _companyRepositoryMock.Setup(x => x.GetByCompanyId(companyDto.BlueTapeCompanyId, default))
            .ReturnsAsync(companyDto);
        _efCompanyRepositoryMock.Setup(x => x.GetById(companyEntity.Id, default))
            .ReturnsAsync(companyEntity);

        var result = await _companyService.GetByCompanyIdV2(companyEntity.Id, default);

        result.ShouldNotBeNull();
        result.BlueTapeCompanyId.ShouldBeEquivalentTo(companyDto.BlueTapeCompanyId);
        result.AccountStatus.ShouldBeEquivalentTo(companyEntity.Account.Status);
        result.BlueTapeGuidCompanyId.ShouldBeEquivalentTo(companyEntity.Id);
    }

    [Theory, AutoDataWithDateOnly]
    public async Task GetByCompanyIdV2_InvalidId_ReturnsNull(Guid id)
    {
        _efCompanyRepositoryMock.Setup(x => x.GetById(id, default))
            .ReturnsAsync((CompanyEntity?)null);
        var result = await _companyService.GetByCompanyIdV2(id, default);

        result.ShouldBeNull();
    }

    [Theory, AutoDataWithDateOnly]
    public async Task GetByCompanyIdV2_LegacyIdEmpty_ReturnsNull(Guid id, CompanyEntity entity)
    {
        entity.LegacyId = null;
        _efCompanyRepositoryMock.Setup(x => x.GetById(id, default))
            .ReturnsAsync(entity);
        var result = await _companyService.GetByCompanyIdV2(id, default);

        result.ShouldBeNull();
    }

    [Theory, AutoDataWithDateOnly]
    public async Task GetByCompanyIdV2_CompanyDtoNull_ReturnsCompanyDto(CompanyDto companyDto,
        CompanyEntity companyEntity)
    {
        companyEntity.Account!.ManualStatus = null;
        companyEntity.LegacyId = companyDto.BlueTapeCompanyId;
        companyDto.AccountStatus = null;

        _companyRepositoryMock.Setup(x => x.GetByCompanyId(companyDto.BlueTapeCompanyId, default))
            .ReturnsAsync((CompanyDto?)null);
        _efCompanyRepositoryMock.Setup(x => x.GetById(companyEntity.Id, default))
            .ReturnsAsync(companyEntity);

        var result = await _companyService.GetByCompanyIdV2(companyEntity.Id, default);

        result.ShouldBeNull();
    }

    [Theory, AutoDataWithDateOnly]
    public async Task GetByCompanyIdsV2_ValidIds_ReturnsCompanies(Guid[] ids, CompanyDto company1, CompanyDto company2,
        CompanyEntity companyEntity1, CompanyEntity companyEntity2)
    {
        company1.AccountStatus = null;
        company2.AccountStatus = null;
        var companies = new[]
        {
            company1, company2
        };

        companyEntity1.LegacyId = company1.BlueTapeCompanyId;
        companyEntity1.Account!.ManualStatus = null;
        companyEntity2.LegacyId = company2.BlueTapeCompanyId;
        companyEntity2.Account!.ManualStatus = null;
        var companyEntities = new List<CompanyEntity>()
        {
            companyEntity1,
            companyEntity2
        };

        var bsonIds = companies.Select(x => x.BlueTapeCompanyId).ToArray();

        _companyRepositoryMock.Setup(x => x.GetByCompanyIds(bsonIds, default))
            .ReturnsAsync(companies);
        _efCompanyRepositoryMock.Setup(x => x.GetByIds(ids, default))
            .ReturnsAsync(companyEntities);

        var result = await _companyService.GetByCompanyIdsV2(ids, default);

        var resultCompany1 = result!.First(x => x.BlueTapeCompanyId == company1.BlueTapeCompanyId);
        var resultCompany2 = result!.First(x => x.BlueTapeCompanyId == company2.BlueTapeCompanyId);

        resultCompany1.BlueTapeCompanyId.ShouldBeEquivalentTo(company1.BlueTapeCompanyId);
        resultCompany1.AccountStatus.ShouldBeEquivalentTo(companyEntity1.Account.Status);
        resultCompany1.BlueTapeGuidCompanyId.ShouldBeEquivalentTo(companyEntity1.Id);

        resultCompany2.BlueTapeCompanyId.ShouldBeEquivalentTo(company2.BlueTapeCompanyId);
        resultCompany2.AccountStatus.ShouldBeEquivalentTo(companyEntity2.Account.Status);
        resultCompany2.BlueTapeGuidCompanyId.ShouldBeEquivalentTo(companyEntity2.Id);
    }

    [Theory, AutoDataWithDateOnly]
    public async Task GetByCompanyIdsV2_EntitiesEmpty_ReturnsCompanies(Guid[] ids, CompanyDto company1,
        CompanyDto company2)
    {
        var companies = new[]
        {
            company1, company2
        };

        var bsonIds = companies.Select(x => x.BlueTapeCompanyId).ToArray();

        _companyRepositoryMock.Setup(x => x.GetByCompanyIds(bsonIds, default))
            .ReturnsAsync(companies);
        _efCompanyRepositoryMock.Setup(x => x.GetByIds(ids, default))
            .ReturnsAsync([]);

        var result = await _companyService.GetByCompanyIdsV2(ids, default);

        result.ShouldBeEmpty();
    }


    [Theory, AutoDataWithDateOnly]
    public async Task GetByCompanyIdsV2_DocumentsFromMongoEmpty_ReturnsCompanies(Guid[] ids,
        CompanyEntity companyEntity1, CompanyEntity companyEntity2)
    {
        var companyEntities = new List<CompanyEntity>()
        {
            companyEntity1,
            companyEntity2
        };

        _companyRepositoryMock.Setup(x => x.GetByCompanyIds(It.IsAny<string[]>(), default))
            .ReturnsAsync([]);
        _efCompanyRepositoryMock.Setup(x => x.GetByIds(ids, default))
            .ReturnsAsync(companyEntities);

        var result = await _companyService.GetByCompanyIdsV2(ids, default);

        result.ShouldBeEmpty();
    }

    [Theory, AutoDataWithDateOnly]
    public async Task GetByCompanyIdsV2_DocumentsFromMongoNull_ReturnsCompanies(Guid[] ids,
        CompanyEntity companyEntity1, CompanyEntity companyEntity2)
    {
        var companyEntities = new List<CompanyEntity>()
        {
            companyEntity1,
            companyEntity2
        };

        _companyRepositoryMock.Setup(x => x.GetByCompanyIds(It.IsAny<string[]>(), default))
            .ReturnsAsync((CompanyDto[]?)null);
        _efCompanyRepositoryMock.Setup(x => x.GetByIds(ids, default))
            .ReturnsAsync(companyEntities);

        var result = await _companyService.GetByCompanyIdsV2(ids, default);

        result.ShouldBeEmpty();
    }

    [Theory, AutoDataWithDateOnly]
    public async Task Get_ValidData_ReturnsCompanies(CompanyEntity entity1, CompanyEntity entity2)
    {
        var companyEntities = new List<CompanyEntity>()
        {
            entity1,
            entity2
        };

        var companyDtos = new CompanyDto[]
        {
            new CompanyDto()
            {
                BlueTapeCompanyId = entity1.LegacyId!
            },
            new CompanyDto()
            {
                BlueTapeCompanyId = entity2.LegacyId!
            }
        };

        var ids = companyEntities.Select(x => x.LegacyId).ToArray();

        _companyRepositoryMock.Setup(x => x.GetByCompanyIds(ids!, default))
            .ReturnsAsync(companyDtos);

        _efCompanyRepositoryMock.Setup(x => x.Get(default, true, default))
            .ReturnsAsync(companyEntities);

        var result = await _companyService.Get(true, default);

        result.ShouldNotBeNull();
        result.Length.ShouldBe(2);
    }

    [Fact]
    public async Task Get_EmptyData_ReturnsEmpty()
    {
        _companyRepositoryMock.Setup(x => x.GetByCompanyIds(It.IsAny<string[]>(), default))
            .ReturnsAsync([]);

        _efCompanyRepositoryMock.Setup(x => x.Get(default, true, default))
            .ReturnsAsync([]);

        var result = await _companyService.Get(true, default);

        result.ShouldNotBeNull();
    }

    [Theory, AutoDataWithDateOnly]
    public async Task GetWithPagination_EmptyData_ReturnsEmpty(int pageSize, int pageNumber)
    {
        var resultWithPaginationEntity = new ResultWithPaginationEntity<CompanyEntity>();

        _companyRepositoryMock.Setup(x => x.GetByCompanyIds(It.IsAny<string[]>(), default))
            .ReturnsAsync([]);

        _efCompanyRepositoryMock.Setup(x => x.GetWithPagination(pageSize, pageNumber, default, true))
            .ReturnsAsync(resultWithPaginationEntity);

        var result = await _companyService.GetWithPagination(pageSize, pageNumber, true, default);

        result.ShouldNotBeNull();
        result.Result.ShouldNotBeNull();
    }

    [Theory, AutoDataWithDateOnly]
    public async Task GetAllByAccountStatuses_ValidData_ReturnsCompanies(AccountStatusEnum[] statuses,
        CompanyEntity companyEntity1, CompanyEntity companyEntity2)
    {
        var companyEntities = new List<CompanyEntity>()
        {
            companyEntity1,
            companyEntity2
        };

        var companyDtos = new CompanyDto[]
        {
            new CompanyDto()
            {
                BlueTapeCompanyId = companyEntity1.LegacyId!
            },
            new CompanyDto()
            {
                BlueTapeCompanyId = companyEntity2.LegacyId!
            }
        };

        _companyRepositoryMock.Setup(x => x.GetByCompanyIds(It.IsAny<string[]>(), default))
            .ReturnsAsync(companyDtos);

        _efCompanyRepositoryMock.Setup(x => x.Get(default, default, statuses))
            .ReturnsAsync(companyEntities);

        var result = await _companyService.GetAllByAccountStatuses(statuses, default);

        result.ShouldNotBeNull();

        result[0].ShouldNotBeNull();
        result[0].BlueTapeCompanyId.ShouldBe(companyEntity1.LegacyId);

        result[1].ShouldNotBeNull();
        result[1].BlueTapeCompanyId.ShouldBe(companyEntity2.LegacyId);
    }

    [Theory, AutoDataWithDateOnly]
    public async Task TryMigrateAllLegacyCompanyIdsAsync_ValidData_Migrate(List<string> legacyIds)
    {
        _companyRepositoryMock
            .Setup(x => x.GetAllIds(CancellationToken.None))
            .ReturnsAsync(legacyIds);

        _efCompanyRepositoryMock
            .Setup(x => x.TryMigrateAllLegacyCompanyIdsAsync(It.IsAny<string[]>(), default))
            .Verifiable();

        await _companyService.TryMigrateAllLegacyCompanyIdsAsync(default);

        _companyRepositoryMock
            .Verify(x => x.GetAllIds(default), Times.Once);
        _efCompanyRepositoryMock
            .Verify(x => x.TryMigrateAllLegacyCompanyIdsAsync(It.IsAny<string[]>(), default), Times.Once);
    }

    [Fact]
    public async Task TryMigrateAllLegacyCompanyIdsAsync_LegacyCompanyIdsIsNull_ShouldReturn()
    {
        _companyRepositoryMock
            .Setup(x => x.GetAllIds(CancellationToken.None))
            .ReturnsAsync([]);

        _efCompanyRepositoryMock
            .Setup(x => x.TryMigrateAllLegacyCompanyIdsAsync(It.IsAny<string[]>(), default))
            .Verifiable();

        await _companyService.TryMigrateAllLegacyCompanyIdsAsync(default);

        _companyRepositoryMock
            .Verify(x => x.GetAllIds(default), Times.Once);
        _efCompanyRepositoryMock
            .Verify(x => x.TryMigrateAllLegacyCompanyIdsAsync(It.IsAny<string[]>(), default), Times.Never);
    }

    [Theory, AutoDataWithDateOnly]
    public async Task CreateCompany_ValidInput_ReturnsCompanyDto(CreateCompanyDto createCompanyDto,
        CompanyDto createdCompanyDto)
    {
        // Arrange
        _companyRepositoryMock
            .Setup(x => x.CreateCompany(createCompanyDto, It.IsAny<CancellationToken>()))
            .ReturnsAsync(createdCompanyDto);

        _efCompanyRepositoryMock
            .Setup(x => x.TryMigrateLegacyCompanyIdAsync(createdCompanyDto.BlueTapeCompanyId,
                It.IsAny<CancellationToken>()))
            .Verifiable();

        // Act
        var result = await _companyService.CreateCompany(createCompanyDto, default);

        // Assert
        result.ShouldNotBeNull();
        result.BlueTapeCompanyId.ShouldBeEquivalentTo(createdCompanyDto.BlueTapeCompanyId);

        _companyRepositoryMock
            .Verify(x => x.CreateCompany(createCompanyDto, It.IsAny<CancellationToken>()), Times.Once);

        _efCompanyRepositoryMock
            .Verify(
                x => x.TryMigrateLegacyCompanyIdAsync(createdCompanyDto.BlueTapeCompanyId,
                    It.IsAny<CancellationToken>()), Times.Once);
    }

    [Fact]
    public async Task CreateCompany_NullResult_DoesNotCallMigration()
    {
        // Arrange
        _companyRepositoryMock
            .Setup(x => x.CreateCompany(It.IsAny<CreateCompanyDto>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync((CompanyDto?)null);

        _efCompanyRepositoryMock
            .Setup(x => x.TryMigrateLegacyCompanyIdAsync(It.IsAny<string>(), It.IsAny<CancellationToken>()))
            .Verifiable();

        var createCompanyDto = new CreateCompanyDto(); // Or use AutoData

        // Act
        var result = await _companyService.CreateCompany(createCompanyDto, default);

        // Assert
        result.ShouldBeNull();

        _companyRepositoryMock
            .Verify(x => x.CreateCompany(createCompanyDto, It.IsAny<CancellationToken>()), Times.Once);

        _efCompanyRepositoryMock
            .Verify(x => x.TryMigrateLegacyCompanyIdAsync(It.IsAny<string>(), It.IsAny<CancellationToken>()),
                Times.Never);
    }

    [Theory, AutoDataWithDateOnly]
    public async Task GetByFiltersWithPagination_EFDataPrimary_ValidFilters_ReturnsResults(
        CompanyQueryPaginated query,
        ResultWithPaginationEntity<CompanyEntity> efCompanies,
        CompanyDto companyDto)
    {
        // Arrange
        efCompanies.Result.ForEach(x => x.LegacyId = Guid.NewGuid().ToString()); // Ensure LegacyId is non-null
        var legacyIds = efCompanies.Result.Select(x => x.LegacyId!).ToArray();
        companyDto.BlueTapeCompanyId = legacyIds[0];
        var companiesFromMongo = new[] { companyDto };

        query.Name = null;
        query.SortBy = null;
        query.Type = null;
        query.Search = null;
        query.IsGuest = null;

        _efCompanyRepositoryMock
            .Setup(x => x.GetByFiltersWithPagination(query, It.IsAny<CancellationToken>()))
            .ReturnsAsync(efCompanies);

        _companyRepositoryMock
            .Setup(x => x.GetByCompanyIds(legacyIds, It.IsAny<CancellationToken>()))
            .ReturnsAsync(companiesFromMongo);

        // Act
        var result = await _companyService.GetByFiltersWithPagination(query, CancellationToken.None);

        // Assert
        result.ShouldNotBeNull();
        result.Result.ShouldNotBeEmpty();
        result.TotalCount.ShouldBe(efCompanies.TotalCount);
        result.Result.First().BlueTapeCompanyId.ShouldBeEquivalentTo(companyDto.BlueTapeCompanyId);
    }

    [Theory, AutoDataWithDateOnly]
    public async Task GetByFiltersWithPagination_MongoDBDataPrimary_ValidFilters_ReturnsResults(
        CompanyQueryPaginated query,
        ResultWithPaginationDto<CompanyDto> mongoCompanies,
        CompanyEntity efCompany)
    {
        // Arrange
        mongoCompanies.Result.ForEach(x => x.BlueTapeCompanyId = Guid.NewGuid().ToString()); // Ensure non-null
        var companyIds = mongoCompanies.Result.Select(x => x.BlueTapeCompanyId).ToArray();
        var efCompanies = new List<CompanyEntity> { efCompany };

        _companyRepositoryMock
            .Setup(x => x.GetByFiltersWithPagination(query, It.IsAny<CancellationToken>()))
            .ReturnsAsync(mongoCompanies);

        _efCompanyRepositoryMock
            .Setup(x => x.GetByLegacyIds(companyIds, It.IsAny<CancellationToken>()))
            .ReturnsAsync(efCompanies);

        // Act
        var result = await _companyService.GetByFiltersWithPagination(query, CancellationToken.None);

        // Assert
        result.ShouldNotBeNull();
        result.Result.ShouldNotBeEmpty();
        result.TotalCount.ShouldBe(mongoCompanies.TotalCount);
    }

    [Theory, AutoDataWithDateOnly]
    public async Task GetByFiltersWithPagination_InvalidFilters_ReturnsEmptyResult(
        CompanyQueryPaginated query)
    {
        // Arrange
        _efCompanyRepositoryMock
            .Setup(x => x.GetByFiltersWithPagination(query, It.IsAny<CancellationToken>()))
            .ReturnsAsync(new ResultWithPaginationEntity<CompanyEntity>());

        _companyRepositoryMock
            .Setup(x => x.GetByFiltersWithPagination(It.IsAny<CompanyQueryPaginated>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(new ResultWithPaginationDto<CompanyDto>());

        // Act
        var result = await _companyService.GetByFiltersWithPagination(query, CancellationToken.None);

        // Assert
        result.ShouldNotBeNull();
        result.Result.ShouldBeEmpty();
        result.TotalCount.ShouldBe(0);
    }

    [Theory, AutoDataWithDateOnly]
    public async Task GetByFiltersWithPagination_EFDataPrimary_NoResults_ReturnsEmpty(
        CompanyQueryPaginated query)
    {
        // Arrange
        _efCompanyRepositoryMock
            .Setup(x => x.GetByFiltersWithPagination(query, It.IsAny<CancellationToken>()))
            .ReturnsAsync(new ResultWithPaginationEntity<CompanyEntity>());

        _companyRepositoryMock
            .Setup(x => x.GetByCompanyIds(It.IsAny<string[]>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync([]);

        query.Name = null;
        query.SortBy = null;
        query.Type = null;
        query.Search = null;
        query.IsGuest = null;

        // Act
        var result = await _companyService.GetByFiltersWithPagination(query, CancellationToken.None);

        // Assert
        result.ShouldNotBeNull();
        result.Result.ShouldBeEmpty();
    }

    [Theory, AutoDataWithDateOnly]
    public async Task GetByFiltersWithPagination_MongoDBDataPrimary_NoResults_ReturnsEmpty(
        CompanyQueryPaginated query)
    {
        // Arrange
        _companyRepositoryMock
            .Setup(x => x.GetByFiltersWithPagination(query, It.IsAny<CancellationToken>()))
            .ReturnsAsync(new ResultWithPaginationDto<CompanyDto>());

        _efCompanyRepositoryMock
            .Setup(x => x.GetByLegacyIds(It.IsAny<string[]>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync([]);

        // Act
        var result = await _companyService.GetByFiltersWithPagination(query, CancellationToken.None);

        // Assert
        result.ShouldNotBeNull();
        result.Result.ShouldBeEmpty();
    }

    [Theory, AutoDataWithDateOnly]
    public async Task UpdateCompany_ValidInput_CreatesCompanyNote(
        string companyId,
        string userId,
        UpdateCompanyDto updateCompanyDto,
        CompanyDto existingCompanyDto,
        CompanyDto updatedCompanyDto,
        CompanyEntity efCompany)
    {
        // Arrange
        existingCompanyDto.BankAccounts = ["1", "2"];
        updatedCompanyDto.BankAccounts = ["1", "2"];
        updateCompanyDto.BankAccounts = null;
        updateCompanyDto.Settings!.LoanPricingPackageId = "NewPackageId";
        existingCompanyDto.Settings!.LoanPricingPackageId = "OldPackageId";
        updatedCompanyDto.Settings!.LoanPricingPackageId = "NewPackageId";
        updateCompanyDto.Address!.Address = null!;
        updatedCompanyDto.BlueTapeCompanyId = companyId;

        _companyRepositoryMock
            .Setup(x => x.GetByCompanyId(companyId, It.IsAny<CancellationToken>()))
            .ReturnsAsync(existingCompanyDto);

        _companyRepositoryMock
            .Setup(x => x.UpdateCompany(
                companyId,
                It.IsAny<UpdateCompanyDto>(),
                It.IsAny<CancellationToken>()))
            .ReturnsAsync(updatedCompanyDto);

        _efCompanyRepositoryMock
            .Setup(x => x.GetByLegacyId(companyId, It.IsAny<CancellationToken>()))
            .ReturnsAsync(efCompany);

        _efCompanyNoteRepositoryMock
            .Setup(x => x.Add(It.IsAny<CompanyNoteEntity>(), It.IsAny<CancellationToken>()))
            .Verifiable();

        // Act
        var result = await _companyService.UpdateCompany(companyId, userId, updateCompanyDto, CancellationToken.None);

        // Assert
        result.ShouldNotBeNull();
        result.BankAccounts.Length.ShouldBe(2);
        result.BlueTapeCompanyId.ShouldBe(updatedCompanyDto.BlueTapeCompanyId);
        _efCompanyNoteRepositoryMock.Verify(
            x => x.Add(It.Is<CompanyNoteEntity>(note =>
                    note.Note!.Contains("Trade Credit Package was updated by") && note.CompanyId == efCompany.Id),
                It.IsAny<CancellationToken>()),
            Times.Once);
        _companyRepositoryMock.Verify(
            x => x.UpdateCompany(
                It.Is<string>(id => id == companyId),
                It.IsAny<UpdateCompanyDto>(),
                It.IsAny<CancellationToken>()),
            Times.Once);
    }

    [Theory, AutoDataWithDateOnly]
    public async Task UpdateCompany_ValidInput_DoesNotCreateNoteIfPricingPackageUnchanged(
        string companyId,
        string userId,
        UpdateCompanyDto updateCompanyDto,
        CompanyDto existingCompanyDto,
        CompanyDto updatedCompanyDto)
    {
        // Arrange
        updateCompanyDto.Settings!.LoanPricingPackageId = null;
        existingCompanyDto.Settings!.LoanPricingPackageId = "SamePackageId";
        updatedCompanyDto.Settings!.LoanPricingPackageId = "SamePackageId";
        updateCompanyDto.Address = new AddressDto(); // Initialize address to avoid null reference

        _companyRepositoryMock
            .Setup(x => x.GetByCompanyId(companyId, It.IsAny<CancellationToken>()))
            .ReturnsAsync(existingCompanyDto);

        _companyRepositoryMock
            .Setup(x => x.UpdateCompany(companyId, It.IsAny<UpdateCompanyDto>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(updatedCompanyDto);

        // Act
        var result = await _companyService.UpdateCompany(companyId, userId, updateCompanyDto, CancellationToken.None);

        // Assert
        result.ShouldNotBeNull();
        result.BlueTapeCompanyId.ShouldBe(updatedCompanyDto.BlueTapeCompanyId);
        _efCompanyNoteRepositoryMock.Verify(
            x => x.Add(It.IsAny<CompanyNoteEntity>(), It.IsAny<CancellationToken>()),
            Times.Never);
        _companyRepositoryMock.Verify(
            x => x.UpdateCompany(companyId, It.IsAny<UpdateCompanyDto>(), It.IsAny<CancellationToken>()),
            Times.Once);
    }

    [Theory, AutoDataWithDateOnly]
    public async Task UpdateCompany_ValidInput_DoesNotCreateNoteIfPricingPackageUnchanged_BecauseSameValue(
        string companyId,
        string userId,
        UpdateCompanyDto updateCompanyDto,
        CompanyDto existingCompanyDto,
        CompanyDto updatedCompanyDto)
    {
        // Arrange
        updateCompanyDto.Settings!.LoanPricingPackageId = "SamePackageId";
        existingCompanyDto.Settings!.LoanPricingPackageId = "SamePackageId";
        updatedCompanyDto.Settings!.LoanPricingPackageId = "SamePackageId";

        _companyRepositoryMock
            .Setup(x => x.GetByCompanyId(companyId, It.IsAny<CancellationToken>()))
            .ReturnsAsync(existingCompanyDto);

        _companyRepositoryMock
            .Setup(x => x.UpdateCompany(companyId, It.IsAny<UpdateCompanyDto>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(updatedCompanyDto);

        // Act
        await _companyService.UpdateCompany(companyId, userId, updateCompanyDto, CancellationToken.None);

        // Assert
        _efCompanyNoteRepositoryMock.Verify(
            x => x.Add(It.IsAny<CompanyNoteEntity>(), It.IsAny<CancellationToken>()),
            Times.Never);
    }

    [Theory, AutoDataWithDateOnly]
    public async Task UpdateCompany_ValidInput_DoesNotCreateNoteIfPricingPackageUnchanged_BecauseAllNulls(
        string companyId,
        string userId,
        UpdateCompanyDto updateCompanyDto,
        CompanyDto existingCompanyDto,
        CompanyDto updatedCompanyDto)
    {
        // Arrange
        updateCompanyDto.Settings!.LoanPricingPackageId = null;
        existingCompanyDto.Settings!.LoanPricingPackageId = null;
        updatedCompanyDto.Settings!.LoanPricingPackageId = null;

        _companyRepositoryMock
            .Setup(x => x.GetByCompanyId(companyId, It.IsAny<CancellationToken>()))
            .ReturnsAsync(existingCompanyDto);

        _companyRepositoryMock
            .Setup(x => x.UpdateCompany(companyId, It.IsAny<UpdateCompanyDto>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(updatedCompanyDto);

        // Act
        await _companyService.UpdateCompany(companyId, userId, updateCompanyDto, CancellationToken.None);

        // Assert
        _efCompanyNoteRepositoryMock.Verify(
            x => x.Add(It.IsAny<CompanyNoteEntity>(), It.IsAny<CancellationToken>()),
            Times.Never);
    }

    [Theory, AutoDataWithDateOnly]
    public async Task UpdateCompany_ValidInput_DoesNotCreateNoteIfPricingPackageUnchanged_BecauseSettingsAreNull(
        string companyId,
        string userId,
        UpdateCompanyDto updateCompanyDto,
        CompanyDto existingCompanyDto,
        CompanyDto updatedCompanyDto)
    {
        // Arrange
        updateCompanyDto.Settings = null;
        existingCompanyDto.Settings!.LoanPricingPackageId = null;
        updatedCompanyDto.Settings!.LoanPricingPackageId = null;

        _companyRepositoryMock
            .Setup(x => x.GetByCompanyId(companyId, It.IsAny<CancellationToken>()))
            .ReturnsAsync(existingCompanyDto);

        _companyRepositoryMock
            .Setup(x => x.UpdateCompany(companyId, It.IsAny<UpdateCompanyDto>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(updatedCompanyDto);

        // Act
        await _companyService.UpdateCompany(companyId, userId, updateCompanyDto, CancellationToken.None);

        // Assert
        _efCompanyNoteRepositoryMock.Verify(
            x => x.Add(It.IsAny<CompanyNoteEntity>(), It.IsAny<CancellationToken>()),
            Times.Never);
    }

    [Theory, AutoDataWithDateOnly]
    public async Task UpdateCompany_CompanyDoesNotExist_ThrowsException(
        string companyId,
        string userId,
        UpdateCompanyDto updateCompanyDto)
    {
        // Arrange
        updateCompanyDto.Address = new AddressDto(); // Initialize address to avoid null reference

        _companyRepositoryMock
            .Setup(x => x.GetByCompanyId(companyId, It.IsAny<CancellationToken>()))
            .ReturnsAsync((CompanyDto?)null);

        // Act & Assert
        await Should.ThrowAsync<CompanyDoesNotExistException>(async () =>
            await _companyService.UpdateCompany(companyId, userId, updateCompanyDto, CancellationToken.None));
    }

    [Theory, AutoDataWithDateOnly]
    public async Task UpdateCompany_EFCompanyDoesNotExist_SkipsNoteCreation(
        string companyId,
        string userId,
        UpdateCompanyDto updateCompanyDto,
        CompanyDto existingCompanyDto,
        CompanyDto updatedCompanyDto)
    {
        // Arrange
        updateCompanyDto.Settings!.LoanPricingPackageId = "NewPackageId";
        existingCompanyDto.Settings!.LoanPricingPackageId = "OldPackageId";
        updatedCompanyDto.Settings!.LoanPricingPackageId = "NewPackageId";

        _companyRepositoryMock
            .Setup(x => x.GetByCompanyId(companyId, It.IsAny<CancellationToken>()))
            .ReturnsAsync(existingCompanyDto);

        _companyRepositoryMock
            .Setup(x => x.UpdateCompany(
                It.Is<string>(id => id == companyId),
                It.IsAny<UpdateCompanyDto>(),
                It.IsAny<CancellationToken>()))
            .ReturnsAsync(updatedCompanyDto);

        _efCompanyRepositoryMock
            .Setup(x => x.GetByLegacyId(companyId, It.IsAny<CancellationToken>()))
            .ReturnsAsync((CompanyEntity?)null);

        // Act
        await Assert.ThrowsAsync<Common.Exceptions.Companies.CompanyDoesNotExistException>(() =>
            _companyService.UpdateCompany(companyId, userId, updateCompanyDto, CancellationToken.None));

        // Assert
        _efCompanyNoteRepositoryMock.Verify(x => x.Add(It.IsAny<CompanyNoteEntity>(), It.IsAny<CancellationToken>()),
            Times.Never);
    }

    [Theory, AutoDataWithDateOnly]
    public async Task UpdateCompany_UpdateReturnsNull_NoNoteCreated(
        string companyId,
        string userId,
        UpdateCompanyDto updateCompanyDto,
        CompanyDto existingCompanyDto)
    {
        // Arrange
        updateCompanyDto.Settings!.LoanPricingPackageId = "NewPackageId";
        existingCompanyDto.Settings!.LoanPricingPackageId = "OldPackageId";

        _companyRepositoryMock
            .Setup(x => x.GetByCompanyId(companyId, It.IsAny<CancellationToken>()))
            .ReturnsAsync(existingCompanyDto);

        _companyRepositoryMock
            .Setup(x => x.UpdateCompany(
                It.Is<string>(id => id == companyId),
                It.IsAny<UpdateCompanyDto>(),
                It.IsAny<CancellationToken>()))
            .ReturnsAsync((CompanyDto?)null);

        // Act
        var result = await _companyService.UpdateCompany(companyId, userId, updateCompanyDto, CancellationToken.None);

        // Assert
        result.ShouldBeNull();
        _companyRepositoryMock.Verify(
            x => x.UpdateCompany(
                It.Is<string>(id => id == companyId),
                It.IsAny<UpdateCompanyDto>(),
                CancellationToken.None),
            Times.Once);
        _efCompanyNoteRepositoryMock.Verify(x => x.Add(It.IsAny<CompanyNoteEntity>(), It.IsAny<CancellationToken>()),
            Times.Never);
    }

    [Fact]
    public async Task AddNote_ValidInput_ReturnsCompanyNoteDto()
    {
        // Arrange
        var companyId = "company-123";
        var userId = "user-456";
        var noteContent = "This is a test note";

        var createNoteDto = new CreateCompanyNoteDto
        {
            Note = noteContent
        };

        var efCompanyEntity = new CompanyEntity
        {
            Id = Guid.NewGuid(),
            LegacyId = companyId
        };

        var expectedNoteEntity = new CompanyNoteEntity
        {
            Id = Guid.NewGuid(),
            CreatedAt = DateTime.UtcNow,
            CreatedBy = userId,
            Note = noteContent,
            CompanyId = efCompanyEntity.Id
        };

        // Mocking _efCompanyRepositoryMock.GetByLegacyId
        _efCompanyRepositoryMock
            .Setup(repo => repo.GetByLegacyId(
                It.Is<string>(legacyId => legacyId == companyId),
                It.IsAny<CancellationToken>()))
            .ReturnsAsync(efCompanyEntity);

        _efCompanyNoteRepositoryMock
            .Setup(repo => repo.Add(
                It.Is<CompanyNoteEntity>(entity =>
                    entity.Note == noteContent &&
                    entity.CreatedBy == userId &&
                    entity.CompanyId == efCompanyEntity.Id), It.IsAny<CancellationToken>()))
            .ReturnsAsync(expectedNoteEntity);

        // Act
        var result = await _companyService.AddNote(companyId, userId, createNoteDto, CancellationToken.None);

        // Assert
        Assert.NotNull(result);
        Assert.Equal(noteContent, result!.Note);
        Assert.Equal(userId, result.CreatedBy);

        _efCompanyRepositoryMock.Verify(
            repo => repo.GetByLegacyId(It.Is<string>(legacyId => legacyId == companyId), It.IsAny<CancellationToken>()),
            Times.Once);

        _efCompanyNoteRepositoryMock.Verify(
            repo => repo.Add(It.Is<CompanyNoteEntity>(entity =>
                entity.Note == noteContent &&
                entity.CompanyId == efCompanyEntity.Id), CancellationToken.None),
            Times.Once);
    }

    [Theory, AutoDataWithDateOnly]
    public async Task UpdateCompany_WithFullAddress_UpdatesAllAddressFields(
        string companyId,
        string userId,
        CompanyDto existingCompanyDto,
        CompanyDto updatedCompanyDto,
        CompanyEntity efCompany)
    {
        // Arrange
        var updateCompanyDto = new UpdateCompanyDto
        {
            Address = new AddressDto
            {
                Address = "123 Main St",
                UnitNumber = "Suite 100",
                City = "New York",
                State = "NY",
                Zip = "10001",
                Phone = "555-0123"
            },
            Settings = new CompanySettingsDto()
        };

        existingCompanyDto.Address = new AddressDto
        {
            Address = "456 Old St",
            UnitNumber = "Floor 2",
            City = "Boston",
            State = "MA",
            Zip = "02108",
            Phone = "555-9876"
        };

        _companyRepositoryMock
            .Setup(x => x.GetByCompanyId(companyId, It.IsAny<CancellationToken>()))
            .ReturnsAsync(existingCompanyDto);

        _companyRepositoryMock
            .Setup(x => x.UpdateCompany(companyId, It.IsAny<UpdateCompanyDto>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(updatedCompanyDto);

        _efCompanyRepositoryMock
            .Setup(x => x.GetByLegacyId(companyId, It.IsAny<CancellationToken>()))
            .ReturnsAsync(efCompany);

        // Act
        var result = await _companyService.UpdateCompany(companyId, userId, updateCompanyDto, CancellationToken.None);

        // Assert
        result.ShouldNotBeNull();
        _companyRepositoryMock.Verify(
            x => x.UpdateCompany(
                companyId,
                It.Is<UpdateCompanyDto>(dto =>
                    dto.Address!.Address == "123 Main St" &&
                    dto.Address.UnitNumber == "Suite 100" &&
                    dto.Address.City == "New York" &&
                    dto.Address.State == "NY" &&
                    dto.Address.Zip == "10001" &&
                    dto.Address.Phone == "555-0123"
                    && dto.Settings!.DefaultDebtInvestorTradeCredit == DebtInvestorType.Arcadia),
                It.IsAny<CancellationToken>()),
            Times.Once);
    }

    [Theory, AutoDataWithDateOnly]
    public async Task UpdateCompany_WithPartialAddress_UpdatesOnlyProvidedFields(
        string companyId,
        string userId,
        CompanyDto existingCompanyDto,
        CompanyDto updatedCompanyDto,
        CompanyEntity efCompany)
    {
        // Arrange
        var updateCompanyDto = new UpdateCompanyDto
        {
            Address = new AddressDto
            {
                Address = "123 Main St",
                City = "New York",
                // Omitting UnitNumber, State, Zip, and Phone
            },
            Settings = new CompanySettingsDto()
        };

        existingCompanyDto.Address = new AddressDto
        {
            Address = "456 Old St",
            UnitNumber = "Floor 2",
            City = "Boston",
            State = "MA",
            Zip = "02108",
            Phone = "555-9876"
        };

        _companyRepositoryMock
            .Setup(x => x.GetByCompanyId(companyId, It.IsAny<CancellationToken>()))
            .ReturnsAsync(existingCompanyDto);

        _companyRepositoryMock
            .Setup(x => x.UpdateCompany(companyId, It.IsAny<UpdateCompanyDto>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(updatedCompanyDto);

        _efCompanyRepositoryMock
            .Setup(x => x.GetByLegacyId(companyId, It.IsAny<CancellationToken>()))
            .ReturnsAsync(efCompany);

        // Act
        var result = await _companyService.UpdateCompany(companyId, userId, updateCompanyDto, CancellationToken.None);

        // Assert
        result.ShouldNotBeNull();
        _companyRepositoryMock.Verify(
            x => x.UpdateCompany(
                companyId,
                It.Is<UpdateCompanyDto>(dto =>
                    dto.Address!.Address == "123 Main St" &&
                    dto.Address.City == "New York" &&
                    dto.Address.UnitNumber == null! &&
                    dto.Address.State == null! &&
                    dto.Address.Zip == null! &&
                    dto.Address.Phone == null!),
                It.IsAny<CancellationToken>()),
            Times.Once);
    }

    [Theory, AutoDataWithDateOnly]
    public async Task UpdateCompany_WithNullAddress_SkipsAddressUpdate(
        string companyId,
        string userId,
        CompanyDto existingCompanyDto,
        CompanyDto updatedCompanyDto,
        CompanyEntity efCompany)
    {
        // Arrange
        var updateCompanyDto = new UpdateCompanyDto
        {
            Address = null,
            Settings = new CompanySettingsDto()
        };

        existingCompanyDto.Address = new AddressDto
        {
            Address = "456 Old St",
            UnitNumber = "Floor 2",
            City = "Boston",
            State = "MA",
            Zip = "02108",
            Phone = "555-9876"
        };

        _companyRepositoryMock
            .Setup(x => x.GetByCompanyId(companyId, It.IsAny<CancellationToken>()))
            .ReturnsAsync(existingCompanyDto);

        _companyRepositoryMock
            .Setup(x => x.UpdateCompany(companyId, It.IsAny<UpdateCompanyDto>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(updatedCompanyDto);

        _efCompanyRepositoryMock
            .Setup(x => x.GetByLegacyId(companyId, It.IsAny<CancellationToken>()))
            .ReturnsAsync(efCompany);

        // Act
        var result = await _companyService.UpdateCompany(companyId, userId, updateCompanyDto, CancellationToken.None);

        // Assert
        result.ShouldNotBeNull();
        _companyRepositoryMock.Verify(
            x => x.UpdateCompany(
                companyId,
                It.Is<UpdateCompanyDto>(dto => dto.Address == null),
                It.IsAny<CancellationToken>()),
            Times.Once);
    }

    [Theory, AutoDataWithDateOnly]
    public async Task UpdateCompany_WithDirectTermsSettings_UpdatesDirectTermsSettings(
        string companyId,
        string userId,
        CompanyDto existingCompanyDto,
        CompanyDto updatedCompanyDto,
        CompanyEntity efCompany)
    {
        // Arrange
        var updateCompanyDto = new UpdateCompanyDto
        {
            Settings = new CompanySettingsDto
            {
                DirectTerms = new DirectTermsDto
                {
                    LoanPlans = ["67265f1ce3233aa3", "6774e059a41a1c55"]
                }
            }
        };

        existingCompanyDto.Settings = new CompanySettingsDto
        {
            DirectTerms = null
        };

        _companyRepositoryMock
            .Setup(x => x.GetByCompanyId(companyId, It.IsAny<CancellationToken>()))
            .ReturnsAsync(existingCompanyDto);

        _companyRepositoryMock
            .Setup(x => x.UpdateCompany(companyId, It.IsAny<UpdateCompanyDto>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(updatedCompanyDto);

        _efCompanyRepositoryMock
            .Setup(x => x.GetByLegacyId(companyId, It.IsAny<CancellationToken>()))
            .ReturnsAsync(efCompany);

        // Act
        var result = await _companyService.UpdateCompany(companyId, userId, updateCompanyDto, CancellationToken.None);

        // Assert
        result.ShouldNotBeNull();
        _companyRepositoryMock.Verify(
            x => x.UpdateCompany(
                companyId,
                It.Is<UpdateCompanyDto>(dto =>
                    dto.Settings!.DirectTerms != null &&
                    dto.Settings.DirectTerms.LoanPlans!.Length == 2 &&
                    dto.Settings.DirectTerms.LoanPlans[0] == "67265f1ce3233aa3" &&
                    dto.Settings.DirectTerms.LoanPlans[1] == "6774e059a41a1c55"),
                It.IsAny<CancellationToken>()),
            Times.Once);
    }

    [Theory, AutoDataWithDateOnly]
    public async Task UpdateCompany_WithDownPaymentDetails_UpdatesDownPaymentDetails(
        string companyId,
        string userId,
        CompanyDto existingCompanyDto,
        CompanyDto updatedCompanyDto,
        CompanyEntity efCompany)
    {
        // Arrange
        var updateCompanyDto = new UpdateCompanyDto
        {
            Settings = new CompanySettingsDto
            {
                DownPaymentDetails = new DownPaymentDetailsDto
                {
                    IsRequired = true,
                    DownPaymentPercentage = 30.00m,
                    ExpireDays = 30
                }
            }
        };

        existingCompanyDto.Settings = new CompanySettingsDto
        {
            DownPaymentDetails = null
        };

        _companyRepositoryMock
            .Setup(x => x.GetByCompanyId(companyId, It.IsAny<CancellationToken>()))
            .ReturnsAsync(existingCompanyDto);

        _companyRepositoryMock
            .Setup(x => x.UpdateCompany(companyId, It.IsAny<UpdateCompanyDto>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(updatedCompanyDto);

        _efCompanyRepositoryMock
            .Setup(x => x.GetByLegacyId(companyId, It.IsAny<CancellationToken>()))
            .ReturnsAsync(efCompany);

        // Act
        var result = await _companyService.UpdateCompany(companyId, userId, updateCompanyDto, CancellationToken.None);

        // Assert
        result.ShouldNotBeNull();
        _companyRepositoryMock.Verify(
            x => x.UpdateCompany(
                companyId,
                It.Is<UpdateCompanyDto>(dto => 
                    dto.Settings!.DownPaymentDetails != null && 
                    dto.Settings.DownPaymentDetails.IsRequired &&
                    dto.Settings.DownPaymentDetails.DownPaymentPercentage == 30.00m &&
                    dto.Settings.DownPaymentDetails.ExpireDays == 30),
                It.IsAny<CancellationToken>()),
            Times.Once);
    }

    [Theory, AutoDataWithDateOnly]
    public async Task UpdateCompany_WithPartialDownPaymentDetails_UpdatesOnlyProvidedFields(
        string companyId,
        string userId,
        CompanyDto existingCompanyDto,
        CompanyDto updatedCompanyDto,
        CompanyEntity efCompany)
    {
        // Arrange
        var updateCompanyDto = new UpdateCompanyDto
        {
            Settings = new CompanySettingsDto
            {
                DownPaymentDetails = new DownPaymentDetailsDto
                {
                    IsRequired = true
                    // Not setting other properties, they should use default values
                }
            }
        };

        existingCompanyDto.Settings = new CompanySettingsDto
        {
            DownPaymentDetails = new DownPaymentDetailsDto
            {
                IsRequired = false,
                DownPaymentPercentage = 20.00m,
                ExpireDays = 15
            }
        };

        _companyRepositoryMock
            .Setup(x => x.GetByCompanyId(companyId, It.IsAny<CancellationToken>()))
            .ReturnsAsync(existingCompanyDto);

        // Capture the actual UpdateCompanyDto passed to the method
        UpdateCompanyDto capturedDto = null!;
        _companyRepositoryMock
            .Setup(x => x.UpdateCompany(companyId, It.IsAny<UpdateCompanyDto>(), It.IsAny<CancellationToken>()))
            .Callback<string, UpdateCompanyDto, CancellationToken>((_, dto, _) => capturedDto = dto)
            .ReturnsAsync(updatedCompanyDto);

        _efCompanyRepositoryMock
            .Setup(x => x.GetByLegacyId(companyId, It.IsAny<CancellationToken>()))
            .ReturnsAsync(efCompany);

        // Act
        var result = await _companyService.UpdateCompany(companyId, userId, updateCompanyDto, CancellationToken.None);

        // Assert
        result.ShouldNotBeNull();
        
        // Verify the repository was called
        _companyRepositoryMock.Verify(
            x => x.UpdateCompany(
                companyId,
                It.IsAny<UpdateCompanyDto>(),
                It.IsAny<CancellationToken>()),
            Times.Once);
        
        // Verify the captured DTO has the expected values
        capturedDto.ShouldNotBeNull();
        capturedDto.Settings.ShouldNotBeNull();
        capturedDto.Settings.DownPaymentDetails.ShouldNotBeNull();
        capturedDto.Settings.DownPaymentDetails.IsRequired.ShouldBeTrue();
        // Verify default values are used
        capturedDto.Settings.DownPaymentDetails.DownPaymentPercentage.ShouldBeNull();
        capturedDto.Settings.DownPaymentDetails.ExpireDays.ShouldBe(30); // Default value from DTO
    }

    [Theory, AutoDataWithDateOnly]
    public async Task UpdateCompany_WithExistingDownPaymentDetails_UpdatesAllFields(
        string companyId,
        string userId,
        CompanyDto existingCompanyDto,
        CompanyDto updatedCompanyDto,
        CompanyEntity efCompany)
    {
        // Arrange
        var updateCompanyDto = new UpdateCompanyDto
        {
            Settings = new CompanySettingsDto
            {
                DownPaymentDetails = new DownPaymentDetailsDto
                {
                    IsRequired = true,
                    DownPaymentPercentage = 30.00m,
                    ExpireDays = 30
                }
            }
        };

        existingCompanyDto.Settings = new CompanySettingsDto
        {
            DownPaymentDetails = new DownPaymentDetailsDto
            {
                IsRequired = false,
                DownPaymentPercentage = 20.00m,
                ExpireDays = 15
            }
        };

        _companyRepositoryMock
            .Setup(x => x.GetByCompanyId(companyId, It.IsAny<CancellationToken>()))
            .ReturnsAsync(existingCompanyDto);

        _companyRepositoryMock
            .Setup(x => x.UpdateCompany(companyId, It.IsAny<UpdateCompanyDto>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(updatedCompanyDto);

        _efCompanyRepositoryMock
            .Setup(x => x.GetByLegacyId(companyId, It.IsAny<CancellationToken>()))
            .ReturnsAsync(efCompany);

        // Act
        var result = await _companyService.UpdateCompany(companyId, userId, updateCompanyDto, CancellationToken.None);

        // Assert
        result.ShouldNotBeNull();
        _companyRepositoryMock.Verify(
            x => x.UpdateCompany(
                companyId,
                It.Is<UpdateCompanyDto>(dto => 
                    dto.Settings!.DownPaymentDetails != null && 
                    dto.Settings.DownPaymentDetails.IsRequired &&
                    dto.Settings.DownPaymentDetails.DownPaymentPercentage == 30.00m &&
                    dto.Settings.DownPaymentDetails.ExpireDays == 30),
                It.IsAny<CancellationToken>()),
            Times.Once);
    }
}
