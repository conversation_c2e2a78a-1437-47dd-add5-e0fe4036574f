# Architecture

## Description

Repository for BlueTape architecture diagrams and proposed OpenAPI definitions for backend services under design.

## Prequisities

Use your favourite text editor and/or use VSCode with [PlantUML](https://marketplace.visualstudio.com/items?itemName=jebbs.plantuml) and [Swagger Editor](https://marketplace.visualstudio.com/items?itemName=Arjun.swagger-viewer) extensions, but you're allowed to use any kind of extensions you prefer.

**Tip:**

* Use `Alt + D` shortcut to preview a PlantUML diagram.
* Use `Shift + Alt + P` shortcut to preview API definition, when using extension mentioned before.

## Large files

Please do not commit large asset files to this repository. Use optimized, compressed and more likely lossless image formats.

## Usage

The most appropriate place to have these design assets is to embed into Confluence Pages.

To embed, you can use `/diagram` shortcut to use PlantUML files and `/api` to do the same with OpenAPI definitions. After authorization, you're allowed to link files directly from a repository (this one).

## Contributing

Anyone can contribute, add new elements and make proposals to change existing designs. Please use feature branches and create merge requests to review and accept changes by more widely audience.
