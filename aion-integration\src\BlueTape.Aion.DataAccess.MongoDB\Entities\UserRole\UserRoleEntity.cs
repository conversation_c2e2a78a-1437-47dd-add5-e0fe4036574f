﻿using BlueTape.MongoDB.Attributes;
using MongoDB.Bson;
using MongoDB.Bson.Serialization.Attributes;

namespace BlueTape.Aion.DataAccess.MongoDB.Entities.UserRole;

[MongoCollection("userroles")]
[BsonIgnoreExtraElements]
public class UserRoleEntity
{
    [BsonId]
    [BsonRepresentation(BsonType.ObjectId)]
    [BsonElement("_id")]
    public string Id { get; set; } = null!;

    [BsonElement("sub")]
    public string ExternalId { get; set; } = null!;

    [BsonElement("company_id")]
    public string CompanyId { get; set; } = null!;

    [BsonElement("status")]
    public string Status { get; set; } = null!;

    [BsonElement("role")]
    public string Role { get; set; } = null!;
}
