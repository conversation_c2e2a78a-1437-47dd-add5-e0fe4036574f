import type { TFunction } from 'i18next'

import { DrawApplicationStatus } from '@/globals/types'
import { StyledTag } from '@/components/common/StyledTag'

export const getDrawApplicationStatusTag = (
  status: DrawApplicationStatus,
  t: TFunction,
): JSX.Element => {
  if (status === DrawApplicationStatus.IN_REVIEW) {
    return (
      <StyledTag color="purple">
        {t('drawApplication.status.inReview')}
      </StyledTag>
    )
  }

  if (status === DrawApplicationStatus.REJECTED) {
    return (
      <StyledTag color="red">{t('drawApplication.status.rejected')}</StyledTag>
    )
  }

  if (status === DrawApplicationStatus.CANCELLED) {
    return (
      <StyledTag color="gold">
        {t('drawApplication.status.cancelled')}
      </StyledTag>
    )
  }

  if (status === DrawApplicationStatus.AUTHORIZED) {
    return (
      <StyledTag color="purple">
        {t('drawApplication.status.authorized')}
      </StyledTag>
    )
  }

  if (status === DrawApplicationStatus.APPROVED) {
    return (
      <StyledTag color="green">
        {t('drawApplication.status.approved')}
      </StyledTag>
    )
  }

  if (status === DrawApplicationStatus.AUTO_APPROVED) {
    return (
      <StyledTag color="green">
        {t('drawApplication.status.autoApproved')}
      </StyledTag>
    )
  }

  return <>{status}</>
}
