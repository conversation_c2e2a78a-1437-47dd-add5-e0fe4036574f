import {sendPayNowApiRequest} from "./send-pay-now-request";

require('dotenv').config();

const tests = "tests";

export async function executePaymentRequest(paymentRequestId: string) {
    await sendPayNowApiRequest(`get`, `${tests}/execute-payment-request/${paymentRequestId}`);
}

export async function createPaymentRequest(payload: CreatePaymentRequestPayload) {
    await sendPayNowApiRequest(`post`, `${tests}/create-payment-request`, payload);
}

export interface CreatePaymentRequestPayload {
    amount: number;
    drawId?: string;
}