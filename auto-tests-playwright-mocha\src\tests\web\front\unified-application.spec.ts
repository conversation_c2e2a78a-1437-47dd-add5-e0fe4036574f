import {Page, expect} from '@playwright/test';
import {test, BaseTest} from '../../test-utils';
import {PageManager} from '../../../objects/pages/page-manager';
import {BaseAPI} from '../../../api/base-api';
import {deleteUser, getUserSub} from "../../../api/admin";

test.use({storageState: {cookies: [], origins: []}});

test.describe('Sign up Unified application.', async () => {
    let page: Page;
    let email: string;
    let firstName: string;
    let lastName: string;
    let businessName: string;
    let accountName: string;
    let cellPhoneNumber: string;

    test.beforeEach(async ({browser}) => {
        email = `automation_user+${BaseTest.dateTimePrefix()}@bluetape.com`;
        firstName = BaseTest.dateTimePrefix() + 'firstName';
        lastName = BaseTest.dateTimePrefix() + 'lastName';
        businessName = BaseTest.dateTimePrefix() + 'businessName';
        accountName = BaseTest.dateTimePrefix() + 'accountName';
        cellPhoneNumber = '************';

        const googleIDToken = await BaseAPI.googleSignUp(email, BaseTest.constants.password);
        if (googleIDToken === null) {
            test.fail(true, 'idToken is empty.');
        }
        await BaseAPI.userSignUp(businessName, email, firstName, googleIDToken, lastName, cellPhoneNumber);

        page = await browser.newPage();
        await BaseTest.verificationLinkSignIn(page, email);
    });

    test.afterEach(async ({adminIdToken}) => {
        const userSub = await getUserSub(adminIdToken, email);
        await deleteUser(adminIdToken, userSub);
        await page.close();
    });

    test(`Unified application for business owner or significant shareholder. @e2e `, async () => {
        const pageManager = new PageManager(page);
        await pageManager.loginPage.login(email, BaseTest.constants.password);
        await pageManager.onBoardingPage.chooseRole('Dealer / Retailer / Supplier');
        await pageManager.onBoardingPage.buttons.skip.click();

        await expect(pageManager.home.labels.emailVerified,
            'Email is verified on the main page should be visible.').toBeVisible();

        await pageManager.unifiedApplication.buttons.start.click();

        await expect(pageManager.unifiedApplication.businessDetails.businessName,
            `Business name field should be pre-filled with ${businessName}`).toHaveValue(businessName);

        await pageManager.unifiedApplication.buttons.next.click();

        await expect(pageManager.unifiedApplication.businessDetails.businessPhoneNumber,
            `Business phone number field should be pre-filled with ${cellPhoneNumber}`).toHaveValue(cellPhoneNumber);

        await pageManager.unifiedApplication.buttons.next.click();
        await pageManager.unifiedApplication.fillUpBusinessAddress(BaseTest.constants.address.street);

        await expect(pageManager.unifiedApplication.businessDetails.businessZipCode)
            .toHaveValue(BaseTest.constants.address.zipCode);

        await expect(pageManager.unifiedApplication.businessDetails.businessCity)
            .toHaveValue(BaseTest.constants.address.city);

        await expect(pageManager.unifiedApplication.businessDetails.businessState)
            .toHaveValue((BaseTest.constants.address.state));

        await pageManager.unifiedApplication.fillUpBusinessDetailsForBusinessOwner('12/2000', '666111111', '500000', '40');
    });

    test(`Unified application for individual business owner. @e2e @test`, async () => {
        const pageManager = new PageManager(page);
        await pageManager.loginPage.login(email, BaseTest.constants.password);
        await pageManager.onBoardingPage.chooseRole('Dealer / Retailer / Supplier');
        await pageManager.onBoardingPage.buttons.skip.click();

        await expect(pageManager.home.labels.emailVerified,
            'Email is verified on the main page should be visible.').toBeVisible();

        await pageManager.unifiedApplication.buttons.start.click();

        await expect(pageManager.unifiedApplication.businessDetails.businessName,
            `Business name field should be pre-filled with ${businessName}`).toHaveValue(businessName);

        await pageManager.unifiedApplication.buttons.next.click();

        await expect(pageManager.unifiedApplication.businessDetails.businessPhoneNumber,
            `Business phone number field should be pre-filled with ${cellPhoneNumber}`).toHaveValue(cellPhoneNumber);

        await pageManager.unifiedApplication.buttons.next.click();

        await pageManager.unifiedApplication.fillUpBusinessAddress(BaseTest.constants.address.street);

        await expect(pageManager.unifiedApplication.businessDetails.businessZipCode)
            .toHaveValue(BaseTest.constants.address.zipCode);

        await expect(pageManager.unifiedApplication.businessDetails.businessCity)
            .toHaveValue(BaseTest.constants.address.city);

        await expect(pageManager.unifiedApplication.businessDetails.businessState)
            .toHaveValue((BaseTest.constants.address.state));

        await pageManager.unifiedApplication.fillUpBusinessDetailsForNoBusinessOwner('yes,individual', '12/2000', '666111111', '500000', BaseTest.constants.address.street);
    });

    test(`Unified application for another entity business owner. @e2e`, async () => {
        const pageManager = new PageManager(page);
        await pageManager.loginPage.login(email, BaseTest.constants.password);
        await pageManager.onBoardingPage.chooseRole('Dealer / Retailer / Supplier');
        await pageManager.onBoardingPage.buttons.skip.click();

        await expect(pageManager.home.labels.emailVerified,
            'Email is verified on the main page should be visible.').toBeVisible();

        await pageManager.unifiedApplication.buttons.start.click();

        await expect(pageManager.unifiedApplication.businessDetails.businessName,
            `Business name field should be pre-filled with ${businessName}`)
            .toHaveValue(businessName);

        await pageManager.unifiedApplication.buttons.next.click();

        await expect(pageManager.unifiedApplication.businessDetails.businessPhoneNumber,
            `Business phone number field should be pre-filled with ${cellPhoneNumber}`)
            .toHaveValue(cellPhoneNumber);

        await pageManager.unifiedApplication.buttons.next.click();

        await pageManager.unifiedApplication.fillUpBusinessAddress(BaseTest.constants.address.street);

        await expect(pageManager.unifiedApplication.businessDetails.businessZipCode)
            .toHaveValue(BaseTest.constants.address.zipCode);

        await expect(pageManager.unifiedApplication.businessDetails.businessCity)
            .toHaveValue(BaseTest.constants.address.city);

        await expect(pageManager.unifiedApplication.businessDetails.businessState)
            .toHaveValue((BaseTest.constants.address.state));

        await pageManager.unifiedApplication.fillUpBusinessDetailsForNoBusinessOwner('yes,another entity', '12/2000', '666111111', '500000', BaseTest.constants.address.street);
        await pageManager.unifiedApplication.fillAuthorizedSignerDetails('no', BaseTest.constants.address.street, '11112000', BaseTest.constants.user.socialSecurityNumber);
    });

    test(`Unified application for no business owner. @e2e`, async () => {
        const pageManager = new PageManager(page);
        await pageManager.loginPage.login(email, BaseTest.constants.password);
        await pageManager.onBoardingPage.chooseRole('Dealer / Retailer / Supplier');
        await pageManager.onBoardingPage.buttons.skip.click();

        await expect(pageManager.home.labels.emailVerified,
            'Email is verified on the main page should be visible.').toBeVisible();

        await pageManager.unifiedApplication.buttons.start.click();

        await expect(pageManager.unifiedApplication.businessDetails.businessName,
            `Business name field should be pre-filled with ${businessName}`).toHaveValue(businessName);

        await pageManager.unifiedApplication.buttons.next.click();

        await expect(pageManager.unifiedApplication.businessDetails.businessPhoneNumber,
            `Business phone number field should be pre-filled with ${cellPhoneNumber}`).toHaveValue(cellPhoneNumber);

        await pageManager.unifiedApplication.buttons.next.click();
        await pageManager.unifiedApplication.fillUpBusinessAddress(BaseTest.constants.address.street);

        await expect(pageManager.unifiedApplication.businessDetails.businessZipCode)
            .toHaveValue(BaseTest.constants.address.zipCode);

        await expect(pageManager.unifiedApplication.businessDetails.businessCity)
            .toHaveValue(BaseTest.constants.address.city);

        await expect(pageManager.unifiedApplication.businessDetails.businessState)
            .toHaveValue((BaseTest.constants.address.state));

        await pageManager.unifiedApplication.fillUpBusinessDetailsForNoBusinessOwner('no', '12/2000', '666111111', '500000', BaseTest.constants.address.street);
        await pageManager.unifiedApplication.fillAuthorizedSignerDetails('yes', BaseTest.constants.address.street, '11112000', BaseTest.constants.user.socialSecurityNumber);
    });
});
