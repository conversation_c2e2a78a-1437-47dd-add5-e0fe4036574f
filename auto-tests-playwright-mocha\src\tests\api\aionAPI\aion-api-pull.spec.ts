import { expect } from '@playwright/test';
import { test } from '../../test-utils';
import { pullAion, sendAionApiRequest } from '../../../api/common/send-aion-api-request';

const error = JSON.parse(JSON.stringify(require('../../../constants/aion-errors.json')));
const constants = JSON.parse(JSON.stringify(require('../../../constants/constants.json')));

test.describe(`Aion pull tests @API`, async () => {
    const companyId = process.env.COMPANY_ID;
    const bankAccountId = process.env.BANK_ACCOUNT_ID;
    const amount: number = constants.aion.amount;
    const description: string = constants.aion.validDescription;
    const addenda: Array<string> = constants.aion.validAddenda;
    const invalidDescription: string = constants.aion.invalidDescriptionWirhMoreThanTenSymbols;
    const invalidAddenda: string = constants.aion.invalidAddendaWithMoreThanEightySymbols;
    const invalidCompanyId: string = constants.aion.invalidCompanyId;
    const invalidBankAccountId: string = constants.aion.invalidBankAccountId;

    test.beforeEach(async () => {
    });

    test(`Ach pull @aion`, async () => {
        const response = await pullAion(amount, description, addenda, companyId, bankAccountId);

        expect(response.status, `Status code 200`).toEqual(200);
        expect(response.data, `Response contains Array of Template's Versions`).toEqual(expect.any(Object));
    });

    test(`Can not ach pull with invalid company id @aion`, async () => {
        const response = await pullAion(amount, description, addenda, invalidCompanyId, bankAccountId);

        expect(response.response.status, `Status code 500`).toEqual(500);
    });

    test(`Can not ach pull with invalid bank account id @aion`, async () => {
        const response = await pullAion(amount, description, addenda, companyId, invalidBankAccountId);

        expect(response.response.status, `Status code 400`).toEqual(400);
        expect(response.response.data[0].code, `Status code 400`).toEqual(error.pull.code.invalidBankAccount);
    });

    test(`Can not ach pull with description symbols more than 10 @aion`, async () => {
        const response = await pullAion(amount, invalidDescription, addenda, companyId, bankAccountId);

        expect(response.response.status, `Status code 400`).toEqual(400);

        expect(response.response.data[0].code,
            `The length of 'Description' must be 10 characters or fewer.`).toEqual(error.pull.code.descriptionMoreThanTenCharacters);
    });

    test(`Can not ach pull with addenda more than 80 @aion`, async () => {
        const response = await pullAion(amount, description, invalidAddenda, companyId, bankAccountId);

        expect(response.response.status, `Status code 400`).toEqual(400);

        expect(response.response.data[0].code,
            `PredicateValidator`).toEqual(error.pull.code.addendaMoreThanEightyCharacters);

        expect(response.response.data[0].reason,
            `The total number of characters in the array must not exceed 80.`)
            .toEqual(error.pull.reason.addendaMoreThanEightyCharacters);
    });

    test(`Can not ach pull with amount equal -1 @aion`, async () => {
        const amount = -1;
        const response = await pullAion(amount, description, addenda, companyId, bankAccountId);

        expect(response.response.status, `Status code 500`).toEqual(500);
    });

    test(`Can not ach pull with description equal null @aion`, async () => {
        const description = null;
        const response = await pullAion(amount, description, addenda, companyId, bankAccountId);

        expect(response.response.status, `Status code 500`).toEqual(500);
    });

    test(`Can not ach pull with addenda equal null @aion`, async () => {
        const addenda = null;
        const response = await pullAion(amount, description, addenda, companyId, bankAccountId);

        expect(response.response.status, `Status code 500`).toEqual(500);
    });

    test(`Can not ach pull with companyId equal null @aion`, async () => {
        const companyId = null;
        const response = await pullAion(amount, description, addenda, companyId, bankAccountId);

        expect(response.response.status, `Status code 400`).toEqual(400);

        expect(response.response.data[0].code,
            `NotNullValidator`).toEqual(error.pull.code.notNullValidator);

        expect(response.response.data[0].reason,
            `'Company Id' must not be empty.`).toEqual(error.pull.reason.emptyCompanyId);
    });

    test(`Can not ach pull with bankAccountId equal null @aion`, async () => {
        const bankAccountId = null;
        const response = await pullAion(amount, description, addenda, companyId, bankAccountId);

        expect(response.response.status, `Status code 400`).toEqual(400);

        expect(response.response.data[0].code,
            `NotNullValidator`).toEqual(error.pull.code.notNullValidator);

        expect(response.response.data[0].reason,
            `'Company Id' must not be empty.`).toEqual(error.pull.reason.emptyBankAccountId);
    });

    test(`Can not ach pull with amount equal null @aion`, async () => {
        const amount = null;
        const response = await pullAion(amount, description, addenda, companyId, bankAccountId);

        expect(response.response.status, `Status code 400`).toEqual(400);

        expect(response.response.data[0].code,
            `InvalidModelState`).toEqual(error.pull.code.invalidModelState);
    });

    test(`Can not ach pull with invalid secret key @aion`, async () => {
        const requestBody = {
            "amount": amount,
            "description": description,
            "addenda": addenda,
            "receiver": {
                "companyId": companyId,
                "bankAccountId": bankAccountId
            }
        };
        const response = await sendAionApiRequest('post', `api/ach/pull`, requestBody, '1111111');
        
        expect(response.response.status, `Status code 401`).toEqual(401);
    });
});
