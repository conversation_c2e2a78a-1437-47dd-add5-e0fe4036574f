﻿using BlueTape.Company.Domain.DTOs.Companies;
using BlueTape.Company.Domain.DTOs.GuestSuppliers;
using BlueTape.CompanyService.Companies;
using BlueTape.CompanyService.GuestSuppliers;

namespace BlueTape.DataAccess.MongoDB.Abstractions;

public interface ICompanyRepository
{
    Task<CompanyDto?> GetByBankAccountId(string bankAccountId, CancellationToken cancellationToken);
    Task<CompanyDto?> GetByCompanyId(string companyId, CancellationToken cancellationToken);

    Task<ResultWithPaginationDto<CompanyDto>> GetByFiltersWithPagination(CompanyQueryPaginated query,
        CancellationToken cancellationToken);

    Task<ResultWithPaginationDto<GuestSupplierInvoiceDto>> GetGuestSuppliersInvoicesAsync(string companyId,
        GuestSupplierInvoiceQueryPaginated query,
        CancellationToken cancellationToken);

    Task<ResultWithPaginationDto<GuestSupplierDto>> GetGuestSupplierByFiltersWithPagination(
        GuestSupplierQueryPaginated query, CancellationToken cancellationToken);

    Task<CompanyDto[]?> GetByCompanyIds(string[] companyIds, CancellationToken cancellationToken);
    Task<List<string>> GetAllIds(CancellationToken ct);
    Task AddBankAccountIds(string companyId, IEnumerable<string> bankAccountIds, CancellationToken cancellationToken);
    Task UpdateAionSettingsAsync(CompanyDto company, CancellationToken cancellationToken);
    Task UpdatePublicIdentifierAsync(string companyId, string companyIdentifier, CancellationToken cancellationToken);

    Task<CompanyDto?> CreateCompany(CreateCompanyDto companyDto, CancellationToken cancellationToken);

    Task<CompanyDto?> UpdateCompany(string blueTapeCompanyId, UpdateCompanyDto companyUpdateDto, CancellationToken cancellationToken);
}
