import {BasePage} from '../../../base.page';

export class SupplierApplicationDetailsModal extends BasePage {
    constructor(page){
        super(page);
    };

    containers = {
        supplierApplicationModalContainer: this.page.locator('[class="modal-dialog modal-xl"]'),
    };

    buttons = {
        sendBack: this.containers.supplierApplicationModalContainer.locator('"Send back"'),
        reject: this.containers.supplierApplicationModalContainer.locator('"Reject"'),
        approve: this.containers.supplierApplicationModalContainer.locator('"Approve"'),
        ok: this.page.locator('"Ok" >> nth = 0'), // TODO: find container
    };
}