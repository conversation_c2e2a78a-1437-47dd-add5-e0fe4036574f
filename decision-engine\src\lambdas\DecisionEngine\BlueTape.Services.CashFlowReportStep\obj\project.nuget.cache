{"version": 2, "dgSpecHash": "LSyzI+JYQw8=", "success": true, "projectFilePath": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\decision-engine\\src\\lambdas\\DecisionEngine\\BlueTape.Services.CashFlowReportStep\\BlueTape.Services.CashFlowReportStep.csproj", "expectedPackageFiles": ["C:\\Users\\<USER>\\.nuget\\packages\\amazon.lambda.core\\2.2.0\\amazon.lambda.core.2.2.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\amazon.lambda.serialization.systemtextjson\\2.4.0\\amazon.lambda.serialization.systemtextjson.2.4.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\amazon.lambda.sqsevents\\2.1.0\\amazon.lambda.sqsevents.2.1.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\autofac\\6.4.0\\autofac.6.4.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\autofac.configuration\\6.0.0\\autofac.configuration.6.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\autofac.extensions.dependencyinjection\\8.0.0\\autofac.extensions.dependencyinjection.8.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\automapper\\12.0.1\\automapper.12.0.1.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\automapper.extensions.microsoft.dependencyinjection\\12.0.1\\automapper.extensions.microsoft.dependencyinjection.12.0.1.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\awssdk.core\\3.7.302.15\\awssdk.core.3.7.302.15.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\awssdk.extensions.netcore.setup\\3.7.300\\awssdk.extensions.netcore.setup.3.7.300.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\awssdk.keymanagementservice\\3.7.300.54\\awssdk.keymanagementservice.3.7.300.54.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\awssdk.s3\\3.7.10\\awssdk.s3.3.7.10.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\awssdk.secretsmanager\\3.7.302.29\\awssdk.secretsmanager.3.7.302.29.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\awssdk.secretsmanager.caching\\1.0.6\\awssdk.secretsmanager.caching.1.0.6.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\awssdk.securitytoken\\3.7.300.47\\awssdk.securitytoken.3.7.300.47.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\awssdk.simplenotificationservice\\3.7.200.52\\awssdk.simplenotificationservice.3.7.200.52.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\awssdk.sqs\\3.7.2.121\\awssdk.sqs.3.7.2.121.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\azure.core\\1.36.0\\azure.core.1.36.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\azure.core.amqp\\1.3.0\\azure.core.amqp.1.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\azure.identity\\1.10.4\\azure.identity.1.10.4.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\azure.messaging.servicebus\\7.17.1\\azure.messaging.servicebus.7.17.1.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\azure.security.keyvault.keys\\4.5.0\\azure.security.keyvault.keys.4.5.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\azure.security.keyvault.secrets\\4.5.0\\azure.security.keyvault.secrets.4.5.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\bluetape.awsmessaging\\2.0.5\\bluetape.awsmessaging.2.0.5.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\bluetape.awss3\\1.1.6\\bluetape.awss3.1.1.6.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\bluetape.azurekeyvault\\1.0.3\\bluetape.azurekeyvault.1.0.3.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\bluetape.cashflow.domain\\1.0.2\\bluetape.cashflow.domain.1.0.2.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\bluetape.common.extensions\\1.1.0\\bluetape.common.extensions.1.1.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\bluetape.common.validation\\1.0.4\\bluetape.common.validation.1.0.4.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\bluetape.companyservice\\1.2.42\\bluetape.companyservice.1.2.42.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\bluetape.companyservice.common\\1.1.21\\bluetape.companyservice.common.1.1.21.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\bluetape.emailsender\\3.0.7\\bluetape.emailsender.3.0.7.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\bluetape.integrations.experian\\1.0.2\\bluetape.integrations.experian.1.0.2.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\bluetape.integrations.giact\\1.0.3\\bluetape.integrations.giact.1.0.3.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\bluetape.integrations.lexisnexis\\1.0.6\\bluetape.integrations.lexisnexis.1.0.6.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\bluetape.integrations.plaid\\1.0.7\\bluetape.integrations.plaid.1.0.7.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\bluetape.integrations.plaid.infrastructure\\1.0.0\\bluetape.integrations.plaid.infrastructure.1.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\bluetape.invoiceservice\\1.0.38\\bluetape.invoiceservice.1.0.38.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\bluetape.invoiceservice.common\\1.1.2\\bluetape.invoiceservice.common.1.1.2.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\bluetape.lambdabase\\1.1.1\\bluetape.lambdabase.1.1.1.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\bluetape.ls\\1.1.69\\bluetape.ls.1.1.69.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\bluetape.ls.domain\\1.1.33\\bluetape.ls.domain.1.1.33.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\bluetape.mongodb\\1.1.31\\bluetape.mongodb.1.1.31.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\bluetape.obs\\1.6.69\\bluetape.obs.1.6.69.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\bluetape.paymentservice\\1.0.3\\bluetape.paymentservice.1.0.3.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\bluetape.servicebusmessaging\\1.0.8\\bluetape.servicebusmessaging.1.0.8.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\bluetape.sns\\1.0.2\\bluetape.sns.1.0.2.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\bluetape.utilities\\1.4.6\\bluetape.utilities.1.4.6.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\dateonlytimeonly.aspnet\\2.1.1\\dateonlytimeonly.aspnet.2.1.1.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\dnsclient\\1.6.1\\dnsclient.1.6.1.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\elastic.commonschema\\1.5.3\\elastic.commonschema.1.5.3.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\elastic.commonschema.serilog\\1.5.3\\elastic.commonschema.serilog.1.5.3.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\f23.stringsimilarity\\5.1.0\\f23.stringsimilarity.5.1.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\fluentvalidation\\11.0.2\\fluentvalidation.11.0.2.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\libphonenumber-csharp\\8.13.24\\libphonenumber-csharp.8.13.24.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\macross.json.extensions\\3.0.0\\macross.json.extensions.3.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.http.abstractions\\2.2.0\\microsoft.aspnetcore.http.abstractions.2.2.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.http.features\\2.2.0\\microsoft.aspnetcore.http.features.2.2.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.azure.amqp\\2.6.4\\microsoft.azure.amqp.2.6.4.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.bcl.asyncinterfaces\\1.1.1\\microsoft.bcl.asyncinterfaces.1.1.1.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.csharp\\4.7.0\\microsoft.csharp.4.7.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.caching.abstractions\\7.0.0\\microsoft.extensions.caching.abstractions.7.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.caching.memory\\7.0.0\\microsoft.extensions.caching.memory.7.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.configuration\\6.0.1\\microsoft.extensions.configuration.6.0.1.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.configuration.abstractions\\8.0.0\\microsoft.extensions.configuration.abstractions.8.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.configuration.binder\\8.0.0\\microsoft.extensions.configuration.binder.8.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.configuration.environmentvariables\\6.0.1\\microsoft.extensions.configuration.environmentvariables.6.0.1.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.configuration.fileextensions\\6.0.0\\microsoft.extensions.configuration.fileextensions.6.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.configuration.json\\6.0.0\\microsoft.extensions.configuration.json.6.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.configuration.usersecrets\\6.0.1\\microsoft.extensions.configuration.usersecrets.6.0.1.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.dependencyinjection\\8.0.0\\microsoft.extensions.dependencyinjection.8.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.dependencyinjection.abstractions\\8.0.0\\microsoft.extensions.dependencyinjection.abstractions.8.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.dependencymodel\\3.0.0\\microsoft.extensions.dependencymodel.3.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.fileproviders.abstractions\\6.0.0\\microsoft.extensions.fileproviders.abstractions.6.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.fileproviders.physical\\6.0.0\\microsoft.extensions.fileproviders.physical.6.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.filesystemglobbing\\6.0.0\\microsoft.extensions.filesystemglobbing.6.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.hosting.abstractions\\6.0.0\\microsoft.extensions.hosting.abstractions.6.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.http\\6.0.0\\microsoft.extensions.http.6.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.http.polly\\6.0.9\\microsoft.extensions.http.polly.6.0.9.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.logging\\6.0.0\\microsoft.extensions.logging.6.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.logging.abstractions\\8.0.0\\microsoft.extensions.logging.abstractions.8.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.logging.configuration\\6.0.0\\microsoft.extensions.logging.configuration.6.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.options\\8.0.1\\microsoft.extensions.options.8.0.1.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.options.configurationextensions\\8.0.0\\microsoft.extensions.options.configurationextensions.8.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.primitives\\8.0.0\\microsoft.extensions.primitives.8.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.identity.client\\4.56.0\\microsoft.identity.client.4.56.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.identity.client.extensions.msal\\4.56.0\\microsoft.identity.client.extensions.msal.4.56.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.identitymodel.abstractions\\6.22.0\\microsoft.identitymodel.abstractions.6.22.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.netcore.platforms\\5.0.0\\microsoft.netcore.platforms.5.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.win32.registry\\5.0.0\\microsoft.win32.registry.5.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\mongodb.bson\\2.25.0\\mongodb.bson.2.25.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\mongodb.driver\\2.25.0\\mongodb.driver.2.25.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\mongodb.driver.core\\2.25.0\\mongodb.driver.core.2.25.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\mongodb.libmongocrypt\\1.8.2\\mongodb.libmongocrypt.1.8.2.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\newtonsoft.json\\13.0.1\\newtonsoft.json.13.0.1.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\polly\\7.2.3\\polly.7.2.3.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\polly.extensions.http\\3.0.0\\polly.extensions.http.3.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\sendgrid\\9.28.1\\sendgrid.9.28.1.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\sendgrid.extensions.dependencyinjection\\1.0.1\\sendgrid.extensions.dependencyinjection.1.0.1.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\serilog\\2.12.0\\serilog.2.12.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\serilog.aspnetcore\\6.0.1\\serilog.aspnetcore.6.0.1.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\serilog.enrichers.globallogcontext\\2.1.0\\serilog.enrichers.globallogcontext.2.1.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\serilog.extensions.hosting\\5.0.1\\serilog.extensions.hosting.5.0.1.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\serilog.extensions.logging\\3.1.0\\serilog.extensions.logging.3.1.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\serilog.formatting.compact\\1.1.0\\serilog.formatting.compact.1.1.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\serilog.settings.configuration\\3.3.0\\serilog.settings.configuration.3.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\serilog.sinks.console\\4.1.0\\serilog.sinks.console.4.1.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\serilog.sinks.debug\\2.0.0\\serilog.sinks.debug.2.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\serilog.sinks.file\\5.0.0\\serilog.sinks.file.5.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\serilog.sinks.http\\8.0.0\\serilog.sinks.http.8.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\serilog.sinks.logz.io\\7.1.0\\serilog.sinks.logz.io.7.1.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\serilog.sinks.periodicbatching\\3.1.0\\serilog.sinks.periodicbatching.3.1.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\sharpcompress\\0.30.1\\sharpcompress.0.30.1.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\snappier\\1.0.0\\snappier.1.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\starkbank-ecdsa\\1.3.3\\starkbank-ecdsa.1.3.3.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.buffers\\4.5.1\\system.buffers.4.5.1.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.diagnostics.diagnosticsource\\6.0.1\\system.diagnostics.diagnosticsource.6.0.1.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.io.filesystem.accesscontrol\\5.0.0\\system.io.filesystem.accesscontrol.5.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.memory\\4.5.5\\system.memory.4.5.5.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.memory.data\\1.0.2\\system.memory.data.1.0.2.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.numerics.vectors\\4.5.0\\system.numerics.vectors.4.5.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.runtime.compilerservices.unsafe\\6.0.0\\system.runtime.compilerservices.unsafe.6.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.security.accesscontrol\\5.0.0\\system.security.accesscontrol.5.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.security.cryptography.protecteddata\\4.7.0\\system.security.cryptography.protecteddata.4.7.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.security.principal.windows\\5.0.0\\system.security.principal.windows.5.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.text.encodings.web\\8.0.0\\system.text.encodings.web.8.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.text.json\\8.0.5\\system.text.json.8.0.5.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.threading.tasks.extensions\\4.5.4\\system.threading.tasks.extensions.4.5.4.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\tinyhelpers\\3.1.18\\tinyhelpers.3.1.18.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\zstdsharp.port\\0.7.3\\zstdsharp.port.0.7.3.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.netcore.app.crossgen2.win-x64\\6.0.36\\microsoft.netcore.app.crossgen2.win-x64.6.0.36.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.windowsdesktop.app.ref\\6.0.36\\microsoft.windowsdesktop.app.ref.6.0.36.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.netcore.app.ref\\6.0.36\\microsoft.netcore.app.ref.6.0.36.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.app.ref\\6.0.36\\microsoft.aspnetcore.app.ref.6.0.36.nupkg.sha512"], "logs": []}