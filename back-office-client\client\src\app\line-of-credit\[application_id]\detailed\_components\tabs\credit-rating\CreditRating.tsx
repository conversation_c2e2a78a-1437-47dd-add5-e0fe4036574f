import React from 'react'
import { type DescriptionsProps, Col, Flex } from 'antd'
import { useTranslation } from 'react-i18next'
import { styled } from 'styled-components'
import { isNumber } from 'lodash'
import numbro from 'numbro'
import type { TFunction } from 'i18next'

import StyledDescription from '@/components/common/Description'
import {
  currencyMask,
  formatPercentage,
  getAutomatedDecisionResultBadge,
  getYesNoTranslation,
} from '@/globals/utils'
import type { ILineOfCreditDetailCreditRating } from '@/lib/redux/api/line-of-credit/types'
import ExperianDataViewer from '@/app/line-of-credit/[application_id]/detailed/_components/external/ExperianDataViewer'
import Spacer from '@/components/common/Spacer'
import HintIcon from '@/components/common/HintIcon'
import {
  DecisionScoreCell,
  ScoreRenderers,
} from '@/components/common/DecisionResults/DecisionScoreCell'

interface IProps {
  creditRating: ILineOfCreditDetailCreditRating
  applicationId: string
}

const CreditRating = ({ creditRating, applicationId }: IProps): JSX.Element => {
  const { t } = useTranslation<string | undefined>()

  const ratingItemsFirstBlock: DescriptionsProps['items'] = [
    {
      label: t('lineOfCredit.detailPage.tabs.experianSearchReliabilityCode'),
      children: (
        <DecisionScoreCell
          score={creditRating.experianReliabilityCode}
          value={creditRating.experianReliabilityCode.value ?? t('na')}
          overrideOptions={{
            ruleName: t(
              'approvedBuyers.page.details.overridableScores.reliabilityCode',
            ),
          }}
        />
      ),
    },
    {
      label: t('lineOfCredit.detailPage.tabs.daysSinceFirstReported'),
      children: (
        <DecisionScoreCell
          score={creditRating.firstReportedTradeLineDays}
          value={
            isNumber(creditRating.firstReportedTradeLineDays.value)
              ? String(creditRating.firstReportedTradeLineDays.value)
              : t('na')
          }
        />
      ),
    },
    {
      label: t('lineOfCredit.detailPage.tabs.companyBankruptcy24months'),
      children: (
        <DecisionScoreCell
          score={creditRating.companyBankruptcy}
          value={getYesNoTranslation(creditRating.companyBankruptcy.value, t)}
          overrideOptions={{
            ruleName: t(
              'approvedBuyers.page.details.overridableScores.companyBankruptcy',
            ),
          }}
        />
      ),
    },
    {
      label: (
        <>
          {t('lineOfCredit.detailPage.tabs.judgementAmountRequiresReview')}
          <HintIcon
            content={t(
              'lineOfCredit.detailPage.tabs.judgementAmountRequiresReviewHint',
            )}
            $marginLeft={8}
          />
        </>
      ),
      children: (
        <DecisionScoreCell
          score={creditRating.anyJudgement}
          value={creditRating.judgmentAndRevenueRatio}
          valueRenderer={ScoreRenderers.PercentageRatio}
          overrideOptions={{
            ruleName: t(
              'approvedBuyers.page.details.overridableScores.judgments',
            ),
          }}
        />
      ),
    },
    {
      label: (
        <>
          {t('lineOfCredit.detailPage.tabs.liensAmountRequiresReview')}
          <HintIcon
            content={t(
              'lineOfCredit.detailPage.tabs.liensAmountRequiresReviewHint',
            )}
            $marginLeft={8}
          />
        </>
      ),
      children: (
        <DecisionScoreCell
          score={creditRating.anyLien}
          value={creditRating.lienAndRevenueRatio}
          valueRenderer={ScoreRenderers.PercentageRatio}
          overrideOptions={{
            ruleName: t('approvedBuyers.page.details.overridableScores.liens'),
          }}
        />
      ),
    },
  ]

  const ratingItemsSecondBlock: DescriptionsProps['items'] = [
    {
      label: (
        <>
          {t('lineOfCredit.detailPage.tabs.outstandingAccountBalance')}
          <HintIcon
            content={t(
              'lineOfCredit.detailPage.tabs.outstandingAccountBalanceHint',
            )}
            $marginLeft={8}
          />
        </>
      ),
      children: isNumber(creditRating.outstandingAccountBalance)
        ? currencyMask(creditRating.outstandingAccountBalance)
        : t('na'),
    },
    {
      label: (
        <>
          % {t('lineOfCredit.detailPage.tabs.ofAccountDBT')}
          <HintIcon
            content={t('lineOfCredit.detailPage.tabs.ofAccountDBTHint')}
            $marginLeft={8}
          />
        </>
      ),
      children: (
        <DecisionScoreCell
          score={creditRating.sixtyPlusDebtPercentage}
          value={
            isNumber(creditRating.sixtyPlusDebtPercentage.value)
              ? formatPercentage(creditRating.sixtyPlusDebtPercentage.value)
              : t('na')
          }
        />
      ),
    },
    {
      label: (
        <>
          {t('lineOfCredit.detailPage.tabs.amountOfAccountDBT')}
          <HintIcon
            content={t('lineOfCredit.detailPage.tabs.amountOfAccountDBTHint')}
            $marginLeft={8}
          />
        </>
      ),
      children: (
        <>
          {isNumber(creditRating.sixtyPlusDebtAmount)
            ? currencyMask(creditRating.sixtyPlusDebtAmount)
            : t('na')}
        </>
      ),
    },
    {
      label: t('lineOfCredit.detailPage.tabs.liensAmount'),
      children: getAutomatedDecisionResultBadge(
        creditRating.anyLien.result,
        isNumber(creditRating.anyLien.amount)
          ? currencyMask(creditRating.anyLien.amount)
          : t('na'),
      ),
    },
    {
      label: t('lineOfCredit.detailPage.tabs.judgementAmount'),
      children: getAutomatedDecisionResultBadge(
        creditRating.anyJudgement.result,
        isNumber(creditRating.anyJudgement.amount)
          ? currencyMask(creditRating.anyJudgement.amount)
          : t('na'),
      ),
    },
    {
      label: (
        <>
          {t('lineOfCredit.detailPage.tabs.creditUtilizationRatio')} %
          <HintIcon
            content={t(
              'lineOfCredit.detailPage.tabs.creditUtilizationRatioHint',
            )}
            $marginLeft={8}
          />
        </>
      ),
      children: isNumber(creditRating.creditUtilizationRatio)
        ? formatPercentage(creditRating.creditUtilizationRatio)
        : t('na'),
    },
    {
      label: (
        <>
          {t('lineOfCredit.detailPage.tabs.dbt60PlusAndRevenueRatio')}
          <HintIcon
            content={t(
              'lineOfCredit.detailPage.tabs.dbt60PlusAndRevenueRatioHint',
            )}
            $marginLeft={8}
          />
        </>
      ),
      children: (
        <DecisionScoreCell
          score={creditRating.dbT60PlusAndRevenueRatio}
          value={
            isNumber(creditRating.dbT60PlusAndRevenueRatio.value)
              ? formatPercentage(creditRating.dbT60PlusAndRevenueRatio.value)
              : t('na')
          }
          overrideOptions={{
            ruleName: t(
              'approvedBuyers.page.details.overridableScores.dbT60PlusAndRevenueRatio',
            ),
          }}
        />
      ),
    },
  ]

  const ratingItemsThirdBlock: DescriptionsProps['items'] = [
    {
      label: `${t('lineOfCredit.detailPage.tabs.revenueVariance')}`,
      children: (
        <>
          {isNumber(creditRating.revenueVariancePercentage)
            ? formatPercentage(creditRating.revenueVariancePercentage)
            : t('na')}
        </>
      ),
    },
    {
      label: (
        <>
          {t('lineOfCredit.detailPage.tabs.DTI2Value')}
          <HintIcon
            content={t('lineOfCredit.detailPage.tabs.DTI2ValueHint')}
            $marginLeft={8}
          />
        </>
      ),
      children: isNumber(creditRating.dti2Value)
        ? numbro(creditRating.dti2Value).format({
            output: 'percent',
            trimMantissa: true,
            mantissa: 2,
          })
        : t('na'),
    },
    {
      label: <>{t('lineOfCredit.detailPage.tabs.judgementRevenue')}</>,
      children: isNumber(creditRating.anyJudgement.revenue)
        ? currencyMask(creditRating.anyJudgement.revenue)
        : t('na'),
    },
    {
      label: <>{t('lineOfCredit.detailPage.tabs.liensRevenue')}</>,
      children: isNumber(creditRating.anyLien.revenue)
        ? currencyMask(creditRating.anyLien.revenue)
        : t('na'),
    },
  ]

  return (
    <>
      <Spacer height={36} />
      <ExperianDataViewer applicationId={applicationId} />

      <Spacer height={24} />

      <StyledFlex justify="space-between" gap={40}>
        <Col flex="1">
          <StyledDescription
            items={ratingItemsFirstBlock}
            data-testid="lineOfCreditDetailedCreditRatingFirstBlock"
          />
          <Spacer height={40} />
          <StyledDescription
            items={ratingItemsThirdBlock}
            data-testid="lineOfCreditDetailedCreditRatingThirdBlock"
          />
        </Col>
        <Col flex="1">
          <StyledDescription
            items={ratingItemsSecondBlock}
            data-testid="lineOfCreditDetailedCreditRatingSecondBlock"
          />
        </Col>
      </StyledFlex>
    </>
  )
}

const StyledFlex = styled(Flex)`
  margin-bottom: 35px;
`

export default CreditRating
