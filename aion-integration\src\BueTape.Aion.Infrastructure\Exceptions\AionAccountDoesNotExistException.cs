﻿using System.Net;
using System.Runtime.Serialization;
using BlueTape.Common.ExceptionHandling.Exceptions.Base;

namespace BueTape.Aion.Infrastructure.Exceptions;

[Serializable]
public class AionAccountDoesNotExistException : DomainException
{
    public AionAccountDoesNotExistException(string aionAccountId, HttpStatusCode statusCode = HttpStatusCode.BadRequest) :
        base(BuildErrorMessage(aionAccountId), statusCode)
    {
    }

    protected AionAccountDoesNotExistException(SerializationInfo info, StreamingContext context) : base(info, context)
    {
    }

    private static string BuildErrorMessage(string aionAccountId)
    {
        return $"AionAccount with id: {aionAccountId} does not exist";
    }

    public override string Code => ErrorCodes.BankAccountDoesNotExist;
}