import {client} from "../client";
import {ObjectId} from "mongodb";
import sendUserRequest from '../../../src/api/common/send-user-request';


export async function deleteBankAccounts(bankAccountIDs) {
    if (bankAccountIDs) {
        const database = client.db(`${process.env.CI_ENVIRONMENT_URL == 'https://dev.bluetape.com' ? 'dev' : 'beta'}`);
        const bankAccounts = database.collection('bankaccounts');
        for (let i = 0; i < bankAccountIDs.length; i++) {
            const query = {"_id": new ObjectId(bankAccountIDs[i])};
            await bankAccounts.deleteOne(query);
        }
    }
}

export async function deleteAccounts(bankAccountIDs, session: string, challenge: string) {
    for (const accountId of bankAccountIDs) {
        const endpoint = `v1/company/bank-account?id=${accountId}`;
        try {
            await sendUserRequest('delete', endpoint, session, challenge);
        } catch (error) {
            console.log(error);
            return error;
        }
    }
}