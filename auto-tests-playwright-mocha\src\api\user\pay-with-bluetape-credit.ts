import {BaseAPI} from '../base-api';
import sendUserRequest from '../common/send-user-request';

export async function payWithBluetapeCredit(session: string, challenge: string, id: string) {
    const endpoint = 'v2/user/loan-application?silent=false';
    const body = {
        "invoiceDetails": {
            "invoiceId": [`${id}`],
            "isSentBack": false,
            "paymentPlan": "634ceb3914d965ab637013e7" // for qa: 6346d671594f79d2d51657e8
        }
    };
    try {
        const response = await sendUserRequest('post', endpoint, session, challenge, body);
        const responseBody = await BaseAPI.convertToJson(response);
        return responseBody;
    } catch (error) {
        console.log(error);
        return error;
    }
};

export async function callDecision(session: string, challenge: string, id: string) {
    const endpoint = 'v1/contractor/call-decision-engine?silent=false';
    const body: { id: string } = {
        "id": `${id}`,
    };
    try {
        await sendUserRequest('post', endpoint, session, challenge, body);
    } catch (error) {
        console.log(error);
        return error;
    }
}
