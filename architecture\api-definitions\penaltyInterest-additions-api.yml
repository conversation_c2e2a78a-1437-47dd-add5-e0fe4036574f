openapi: '3.0.0'
info:
  version: '0.0.1'
  title: 'Penalty Interest API (additions to LMS and LS)'
  description: | 
    API definition proposal for penalty interest functions
servers:
- url: TBD-Dev
  description: Development server
- url: TBD-QA
  description: QA server
- url: TBD-Prod
  description: Production server
paths:
  /Bps:
    get:
      tags:
        - LMSandLS
      summary: Gets basis point history, or a single one by query parameter
      description: Gets basis point history, or a single one by query parameter
      operationId: getBasisPoints
      parameters:
        - name: id
          description: Identifier of the basis point
          example: 6df30249-c7f0-4f17-b06f-c29b2fee0a62
          in: query
          required: false
          schema:
            type: string
            nullable: false
        - name: onDate
          description: The date of bps, when it was valid
          example: 2023-08-25
          in: query
          required: false
          schema:
            type: string
            format: date
            nullable: false
      responses:
        200:
          description: The list of bps
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/Bps'
  /Admin/Bps:
    post:
      tags:
        - LMSandLS
      summary: Adds a new basis point, valid from specified date
      description: Adds a new basis point, valid from specified date
      operationId: createBasisPoint
      requestBody:
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/CreateBpsRequest"
      responses: 
        201:
          description: The created bps
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Bps'
  /Admin/Bps/{id}:
    patch:
      tags:
        - LMSandLS
      summary: Updates a bps by its id
      description: Updates a bps by its id
      operationId: updateBasisPoint
      parameters:
        - name: id
          description: Identifier of the basis point
          example: 6df30249-c7f0-4f17-b06f-c29b2fee0a62
          in: path
          required: true
          schema:
            type: string
            nullable: false
      requestBody:
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/UpdateBpsRequest"
      responses: 
        200:
          description: The updated bps
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Bps'
    delete:
      tags:
        - LMSandLS
      summary: Deletes a bps by its id
      description: Deletes a bps by its id
      operationId: deleteBasisPoint
      parameters:
        - name: id
          description: Identifier of the basis point
          example: 6df30249-c7f0-4f17-b06f-c29b2fee0a62
          in: path
          required: true
          schema:
            type: string
            nullable: false
      responses:
        200:
          description: No result returned
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/EmptyResponse'
  /Admin/PenaltyInterest:
    post:
      tags:
        - LMSandLS
      summary: Creates a penalty interest manually
      description: Creates a penalty interest manually. All the not listed fields like bpo, sofr and outstanding principal balance should be filled automatically in the background.
      operationId: createPenaltyInterest
      requestBody:
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/CreatePenaltyInterestRequest"
      responses: 
        201:
          description: The created penalty interest
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/LoanReceivableViewModel'
  /Admin/PenaltyInterest/{id}:
    patch:  
      tags:
        - LMSandLS
      summary: Updates a penalty interest by its receivable id
      description: Updates a penalty interest by its receivable id.  All the not listed fields like bpo, sofr and outstanding principal balance should be filled automatically in the background. If status set, the receivable status will be patched to the give value.
      operationId: updatePenaltyInterest
      parameters:
        - name: id
          description: Identifier of the penalty interest
          example: 6df30249-c7f0-4f17-b06f-c29b2fee0a62
          in: path
          required: true
          schema:
            type: string
            nullable: false
      requestBody:
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/UpdatePenaltyInterestRequest"
      responses: 
        200:
          description: The updated penalty interest
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/LoanReceivableViewModel'
    delete:
      tags:
        - LMSandLS
      summary: Deletes a penalty interest by its receivable id
      description: Deletes a penalty interest by its receivable id.
      operationId: deletePenaltyInterest
      parameters:
        - name: id
          description: Identifier of the penalty interest
          example: 6df30249-c7f0-4f17-b06f-c29b2fee0a62
          in: path
          required: true
          schema:
            type: string
            nullable: false
      responses:
        200:
          description: No result returned
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/EmptyResponse'
  /PenaltyDetector:
    patch:
      tags: 
        - LMSandLS
      summary: Start penalty interest process
      description: Start penalty interest process
      operationId: startPenaltyInterestProcess
      requestBody:
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/EmptyRequest"
      responses: 
        200:
          description: No response
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/EmptyResponse'
components:
  schemas:
    Bps:
      type: object
      required: 
        - id
        - basisPoint
        - validFrom
      properties:
        id:
          type: string
          description: The id of bps. Not referenced anywhere.
          example: 0e770026-593e-4fc9-b10b-b52b2d05436c
        basisPoint:
          type: number
          format: decimal
          pattern: '[0-9]*\.[0-9]{5}'
          multipleOf: 0.00001
          example: 2.80000
          description: The basis point as a percentage
        validFrom:
          type: string
          format: date
          description: The date where from basis point is activated (field is unique)
          example: 2023-08-25
    CreateBpsRequest:
      type: object
      required: 
        - basisPoint
        - validFrom
      properties:
        basisPoint:
          type: number
          format: decimal
          pattern: '[0-9]*\.[0-9]{5}'
          multipleOf: 0.00001
          example: 2.80000
          description: The basis point as a percentage
        validFrom:
          type: string
          format: date
          description: The date where from basis point is activated (field is unique)
          example: 2023-08-25
    UpdateBpsRequest:
      type: object
      required: 
        - basisPoint
        - validFrom
      properties:
        basisPoint:
          type: number
          format: decimal
          pattern: '[0-9]*\.[0-9]{5}'
          multipleOf: 0.00001
          example: 2.80000
          description: The basis point as a percentage
        validFrom:
          type: string
          format: date
          description: The date where from basis point is activated (field is unique)
          example: 2023-08-25
    CreatePenaltyInterestRequest:
      type: object
      properties:
        loanId:
          type: string
          description: The loan's id
          example: e917cc8f-7ab6-4edb-b746-78ed5d2309ef
        expectedAmount:
          type: number
          format: decimal
          pattern: '[0-9]*\.[0-9]{2}'
          multipleOf: 0.01
          example: 12.85
        expectedDate:
          type: string
          format: date
          description: The receivable's expected date
          example: 2023-08-25
        penaltyStartDate:
          type: string
          format: date
          description: The beginning period of this penalty
          example: 2023-08-01
        penaltyEndDate:
          type: string
          format: date
          description: The ending period of this penalty
          example: 2023-08-25
        note:
          type: string
          description: Note from the user
    UpdatePenaltyInterestRequest:
      type: object
      properties:
        status:
          type: string
          enum:
            - canceled
          description: If set, the receivable status will be patched to canceled
        expectedAmount:
          type: number
          format: decimal
          pattern: '[0-9]*\.[0-9]{2}'
          multipleOf: 0.01
          example: 12.85
        expectedDate:
          type: string
          format: date
          description: The receivable's expected date
          example: 2023-08-25
        penaltyStartDate:
          type: string
          format: date
          description: The beginning period of this penalty
          example: 2023-08-01
        penaltyEndDate:
          type: string
          format: date
          description: The ending period of this penalty
          example: 2023-08-25
        note:
          type: string
          description: Note from the user
    LoanReceivableViewModel:
      type: object
    EmptyRequest:
      type: object
      nullable: true
    EmptyResponse:
      type: object
      nullable: true
    ErrorResponse:
      type: object
      required:
        - statusCode
        - message
        - errorDescription
      properties:
        statusCode:
          description: HTTP status code corresponding to the error.
          type: integer
          example: 400
        message:
          description: A human readable error message.
          type: string
          example: Invalid model.
        errorDescription:
          description: Detailed information.
          type: string
  securitySchemes:
    ApiKey:
      type: apiKey
      in: header
      name: X-Api-Key
security:
  - ApiKey: []