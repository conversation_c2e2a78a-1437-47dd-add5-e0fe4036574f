﻿<Project Sdk="Microsoft.NET.Sdk">

    <PropertyGroup>
        <TargetFramework>net8.0</TargetFramework>
        <ImplicitUsings>enable</ImplicitUsings>
        <Nullable>enable</Nullable>
        <RootNamespace>BlueTape.Document.DataAccess.EF</RootNamespace>
    </PropertyGroup>

    <ItemGroup>
        <PackageReference Include="Microsoft.AspNetCore.Http.Abstractions" Version="2.2.0" />
        <PackageReference Include="Microsoft.Data.SqlClient" Version="5.1.5" />
        <PackageReference Include="Microsoft.EntityFrameworkCore" Version="7.0.4" />
        <PackageReference Include="Microsoft.EntityFrameworkCore.InMemory" Version="7.0.4" />
        <PackageReference Include="Microsoft.EntityFrameworkCore.Relational" Version="7.0.4" />
        <PackageReference Include="Microsoft.EntityFrameworkCore.Tools" Version="7.0.4">
            <PrivateAssets>all</PrivateAssets>
            <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
        </PackageReference>
        <PackageReference Include="Npgsql.EntityFrameworkCore.PostgreSQL" Version="7.0.3" />
        <PackageReference Include="TinyHelpers.EntityFrameworkCore" Version="2.0.14" />
        <PackageReference Include="AutoMapper" Version="12.0.0" />
        <PackageReference Include="AutoMapper.Extensions.Microsoft.DependencyInjection" Version="12.0.0" />
		<PackageReference Include="BlueTape.Utilities" Version="1.4.5" />
		<PackageReference Include="BlueTape.CompanyService" Version="1.3.2" />
    </ItemGroup>

    <ItemGroup>
      <ProjectReference Include="..\BlueTape.Domain\BlueTape.Domain.csproj" />
    </ItemGroup>
    
</Project>
