import {Page, expect} from '@playwright/test';
import {test, BaseTest} from '../../../test-utils';
import {PageManager} from '../../../../objects/pages/page-manager';
import {BaseAPI} from '../../../../api/base-api';
import {deleteUser, getUserSub} from "../../../../api/admin";

test.use({storageState: {cookies: [], origins: []}});

test.describe('Sign up integration tests.', async () => {
    let page: Page;
    let email: string;
    let firstName: string | RegExp;
    let lastName: string | RegExp;
    let businessName: string | RegExp | (string | RegExp)[];
    let invoiceNumber: string;
    let dueDate: string;
    let accountName: string;

    test.beforeEach(async ({browser}) => {
        email = `automation_user+${BaseTest.dateTimePrefix()}@bluetape.com`;
        firstName = BaseTest.dateTimePrefix() + 'firstName';
        lastName = BaseTest.dateTimePrefix() + 'lastName';
        businessName = BaseTest.dateTimePrefix() + 'businessName';
        invoiceNumber = BaseTest.dateTimePrefix() + 'invoiceNumber';
        dueDate = BaseTest.getDueDate();
        accountName = BaseTest.dateTimePrefix() + 'accountName';

        const googleIDToken = await BaseAPI.googleSignUp(email, BaseTest.constants.password);
        if (googleIDToken === null) {
            test.fail(true, 'idToken is empty.');
        }
        await BaseAPI.userSignUp(businessName, email, firstName, googleIDToken, lastName, BaseTest.constants.user.cellPhoneNumber);
        page = await browser.newPage();
        await BaseTest.verificationLinkSignIn(page, email);
    });

    test.afterEach(async ({adminIdToken}) => {
        const userSub = await getUserSub(adminIdToken, email);
        await deleteUser(adminIdToken, userSub);
        await page.close();
    });

    test.skip(`Success General contractor sign up. Send Partnership application. @smoke`, async () => {
        // test skipped due to problem with locators
        const pageManager = new PageManager(page);
        await pageManager.loginPage.login(email, BaseTest.constants.password);
        await pageManager.onBoardingPage.chooseRole('General Contractor');
        await pageManager.onBoardingPage.buttons.setupAccount.click();

        await expect(pageManager.home.labels.emailVerified, 'Email is verified on the main page should be visible.').toBeVisible();
        await expect(pageManager.partnershipApplication.businessOwnerInformation.firstName, `First name field should be pre-filled with ${firstName}`).toHaveValue(firstName);
        await expect(pageManager.partnershipApplication.businessOwnerInformation.lastName, `Last name field should be pre-filled with ${lastName}`).toHaveValue(lastName);
        await expect(pageManager.partnershipApplication.businessOwnerInformation.emailAddress, `Email address field should be pre-filled with ${email}`).toHaveValue(email);
        await expect(pageManager.partnershipApplication.businessOwnerInformation.phoneNumber, `Cell phone number field should be pre-filled with ${BaseTest.constants.use}`).toHaveValue(BaseTest.constants.user.cellPhoneNumber);

        await pageManager.partnershipApplication.fillUpBusinessDetails(businessName, '12/2000', '511111111');
        await pageManager.partnershipApplication.fillUpBusinessAddress(BaseTest.constants.address.street, BaseTest.constants.address.city, BaseTest.constants.address.zipCode, BaseTest.constants.address.state, BaseTest.constants.user.cellPhoneNumber);
        await pageManager.partnershipApplication.fillUpBusinessOwnerInformation('100', BaseTest.constants.address.street, BaseTest.constants.address.city, BaseTest.constants.address.zipCode, BaseTest.constants.address.state, '********', BaseTest.constants.user.socialSecurityNumber);
        await pageManager.partnershipApplication.fillUpBankInformation(BaseTest.constants.bankAccount.bankName, accountName, BaseTest.constants.bankAccount.accountNumber, BaseTest.constants.bankAccount.routingNumber);
        await pageManager.buyNowPayLater.fillUpBuyNowPayLater();

        await expect(pageManager.home.labels.applicationPendingApproval, `Application pengind approval label on the main page should be visible.`).toBeVisible();
    });

    test.skip(`Success Dealer/Retailer/Supplier sign up. Upload SO or invoice. @smoke`, async () => {
        //test marked as skipped due to bug with incorrect redirrect
        const pageManager = new PageManager(page);
        await pageManager.loginPage.login(email, BaseTest.constants.password);
        await pageManager.onBoardingPage.chooseRole('Dealer / Retailer / Supplier');

        await pageManager.onBoardingPage.uploadInvoice();
        await pageManager.newInvoice.fillUpInvoiceDetails(invoiceNumber, BaseTest.constants.invoice.invoiceAmount, businessName, dueDate);

        await expect(pageManager.invoicesDetails.inputFields.businessName,
            'New invoice business name should be visible.').toContainText(businessName);
    });

    test(`Success Manufacturer/Distributor sign up. Labels should be visible. @regression`, async () => {
        const pageManager = new PageManager(page);
        await pageManager.loginPage.login(email, BaseTest.constants.password);
        await pageManager.onBoardingPage.chooseRole('Manufacturer / Distributor');

        await expect(pageManager.onBoardingPage.labels.setupAccountSales, 'Setup account label should be visible').toBeVisible();
        await expect(pageManager.onBoardingPage.labels.requestCredit, 'Request credit label should be visible').toBeVisible();
        await expect(pageManager.onBoardingPage.labels.uploadInvoiceSOSales, 'Upload invoice label should be visible').toBeVisible();
    });

    // test(`Success Architect/Interior Designer sign up. Labels should be visible. @regression`, async () => {
    //     const pageManager = new PageManager(page);
    //     await pageManager.loginPage.login(email, BaseTest.constants.password);
    //     await pageManager.onBoardingPage.chooseRole('Architect / Interior Designer');

    //     await expect(pageManager.onBoardingPage.labels.setupAccount, 'Setup account label should be visible').toBeVisible();
    //     await expect(pageManager.onBoardingPage.labels.requestCredit, 'Request credit label should be visible').toBeVisible();
    //     await expect(pageManager.onBoardingPage.labels.uploadInvoiceSO, 'Upload invoice label should be visible').toBeVisible();
    // });

    // test(`Success Engineer/Consultant sign up. Labels should be visible. @regression`, async () => {
    //     const pageManager = new PageManager(page);
    //     await pageManager.loginPage.login(email, BaseTest.constants.password);
    //     await pageManager.onBoardingPage.chooseRole('Engineer / Consultant');

    //     await expect(pageManager.onBoardingPage.labels.setupAccount, 'Setup account label should be visible').toBeVisible();
    //     await expect(pageManager.onBoardingPage.labels.requestCredit, 'Request credit label should be visible').toBeVisible();
    //     await expect(pageManager.onBoardingPage.labels.uploadInvoiceSO, 'Upload invoice label should be visible').toBeVisible();
    // });

    // test(`Success Developer / Property Owner sign up. Labels should be visible. @regression`, async () => {
    //     const pageManager = new PageManager(page);
    //     await pageManager.loginPage.login(email, BaseTest.constants.password);
    //     await pageManager.onBoardingPage.chooseRole('Developer / Property Owner');

    //     await expect(pageManager.onBoardingPage.labels.setupAccount, 'Setup account label should be visible').toBeVisible();
    //     await expect(pageManager.onBoardingPage.labels.requestCredit, 'Request credit label should be visible').toBeVisible();
    //     await expect(pageManager.onBoardingPage.labels.uploadInvoiceSO, 'Upload invoice label should be visible').toBeVisible();

    //     await pageManager.onBoardingPage.radioButtons.no.click();

    //     await expect(pageManager.onBoardingPage.labels.setupAccount, 'Setup account label should not be visible').not.toBeVisible();
    //     await expect(pageManager.onBoardingPage.labels.requestCredit, 'Request credit label should not be visible').not.toBeVisible();
    //     await expect(pageManager.onBoardingPage.labels.uploadInvoiceSO, 'Upload invoice label should be visible').toBeVisible();
    // });

    // test(`Success Other sign up. Labels should be visible. @regression`, async () => {
    //     const pageManager = new PageManager(page);
    //     await pageManager.loginPage.login(email, BaseTest.constants.password);
    //     await pageManager.onBoardingPage.chooseRole('Other');

    //     await expect(pageManager.onBoardingPage.disabledFields.setupAccountDisabled, 'Setup account field should be disabled').toBeVisible();
    //     await expect(pageManager.onBoardingPage.disabledFields.requestCreditDisabled, 'Request credit field should be disabled').toBeVisible();
    //     await expect(pageManager.onBoardingPage.disabledFields.uploadInvoiceDisabled, 'Upload invoice field should be disabled').toBeVisible();

    //     await pageManager.onBoardingPage.inputFields.typeOfBusiness.fill('Business type');
    //     await expect(pageManager.onBoardingPage.labels.setupAccount, 'Setup account label should be visible').toBeVisible();
    //     await expect(pageManager.onBoardingPage.labels.requestCredit, 'Request credit label should be visible').toBeVisible();
    //     await expect(pageManager.onBoardingPage.labels.uploadInvoiceSO, 'Upload invoice label should be visible').toBeVisible();

    //     await pageManager.onBoardingPage.radioButtons.no.click();

    //     await expect(pageManager.onBoardingPage.labels.setupAccount, 'Setup account label should not be visible').not.toBeVisible();
    //     await expect(pageManager.onBoardingPage.labels.requestCredit, 'Request credit label should not be visible').not.toBeVisible();
    //     await expect(pageManager.onBoardingPage.labels.uploadInvoiceSO, 'Upload invoice label should be visible').toBeVisible();
    // });
});
