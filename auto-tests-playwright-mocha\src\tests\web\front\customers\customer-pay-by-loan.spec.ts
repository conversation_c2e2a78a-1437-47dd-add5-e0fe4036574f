import {expect} from '@playwright/test';
import {test} from '../../../test-utils';
import {BaseAPI} from '../../../../api/base-api';
import {createCustomerInvoice} from '../../../../api/user/create-invoice';
import {payWithBluetapeCredit, callDecision} from '../../../../api/user';
import {delay} from '../../../../utils/waiters';

test.use({storageState: {cookies: [], origins: []}});

test.describe('Customers tests', async () => {
    let userAuth: { session: any; challenge: any; };
    let secondUserAuth: { session: any; challenge: any; };
    let invoice: { id: string; };
    let loanRequest: { id: string; };
    const userInfo_id = '64ab0e039f99b8dca4eea135';

    test.beforeEach(async ({userIdToken, userLoanIdToken}) => {
        userAuth = await BaseAPI.getAuth(userIdToken);
        invoice = await createCustomerInvoice(userAuth.session, userAuth.challenge, userInfo_id);
        secondUserAuth = await BaseAPI.getAuth(userLoanIdToken);
        loanRequest = await payWithBluetapeCredit(secondUserAuth.session, secondUserAuth.challenge, invoice.id);
        await callDecision(secondUserAuth.session, secondUserAuth.challenge, loanRequest.id);
    });

    test.afterEach(async ({page}) => {
        await page.close();
    });

    async function delayAndReload({page}, time) {
        await delay(time);
        await page.reload();
    }

    async function waitForChangeStatus({page, pageManager}, userName, status, seconds) {
        for (let i = 0; i < 20; i += 1) {
            const elementText = await pageManager.loanApplications.elements.supplier(userName).textContent();
            if (elementText.includes(status)) {
                break;
            } else {
                await delayAndReload({page}, seconds);
                await pageManager.backOfficeSideMenu.sideMenuTabs.loanApplications.click();
            }
        }
    }

    test.skip('Add new customer. Send invoice to customer. Pay with loan @smoke', async ({page, pageManager}) => {
        await test.slow();
        await page.goto(`${process.env.CI_ENVIRONMENT_BACKEND_URL}`);
        await pageManager.backOfficeLoginPage.login(`${process.env.ADMIN_EMAIL}`, `${process.env.ADMIN_PASSWORD}`);
        await page.pause();
        await waitForChangeStatus({page, pageManager}, 'loantest1', 'In Review', 3000);
        await pageManager.backOfficeSideMenu.sideMenuTabs.loanApplications.click();
        await pageManager.loanApplications.clickOnLoanApplication('loantest1');
        await pageManager.loanApplications.buttons.verify.click();
        await delayAndReload({page}, 5000);
        await pageManager.loanApplications.clickOnLoanApplication('loantest1');
        await pageManager.loanApplications.buttons.approve.click();
        await pageManager.approveModal.buttons.approve.click();
        await waitForChangeStatus({page, pageManager}, 'loantest1', 'Approved', 3000);
        await pageManager.loanApplications.tabs.approved.click();

        // await expect(pageManager.loanApplications.clickOnLoanApplication).toContainText('loantest1');
        // await expect(pageManager.loanApplications.clickOnLoanApplication).toContainText('Approved');
    });
});
