import {BasePage} from "../../base.page";

export class PayModal extends BasePage {
    constructor(page) {
        super(page);
    }

    sideButtons = {
        payDropdown: this.page.getByTestId('menu_link_pay'),
        invoicesFromPayDropdown: this.page.getByTestId('menu_link_pay-invoices'),
    };

    async openInvoicesPage() {
        await this.sideButtons.payDropdown.click();
        await this.sideButtons.invoicesFromPayDropdown.click();
    }
}
