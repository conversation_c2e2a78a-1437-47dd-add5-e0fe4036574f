﻿using AutoMapper;
using BlueTape.Aion.DataAccess.MongoDB.Abstractions;
using BlueTape.Aion.DataAccess.MongoDB.Entities.Companies;
using BlueTape.Aion.Domain.DTOs.Company;
using BlueTape.MongoDB.Abstractions;
using MongoDB.Driver;
using System.Diagnostics.CodeAnalysis;

namespace BlueTape.Aion.DataAccess.MongoDB.Repositories;

[ExcludeFromCodeCoverage]
public class CompanyRepository : ICompanyRepository
{
    private readonly IMongoDbContext _dbContext;
    private readonly IMapper _mapper;

    public CompanyRepository(IMongoDbContext dbContext, IMapper mapper)
    {
        ArgumentNullException.ThrowIfNull(dbContext);

        _dbContext = dbContext;
        _mapper = mapper;
    }

    public async Task<CompanyDto> GetByCompanyId(string companyId, CancellationToken cancellationToken)
    {
        var company = await _dbContext.GetCollection<CompanyEntity>()
            .Find(x => x.BlueTapeCompanyId.Equals(companyId))
            .FirstOrDefaultAsync(cancellationToken);

        return _mapper.Map<CompanyDto>(company);
    }

    public async Task UpdateAionSettingsAsync(CompanyDto company, CancellationToken cancellationToken)
    {
        if (company.AionSettings == null)
            return;

        var filter = Builders<CompanyEntity>
            .Filter.Where(x => x.BlueTapeCompanyId == company.BlueTapeCompanyId);

        var existingCompany = await _dbContext.GetCollection<CompanyEntity>()
            .Find(filter)
            .FirstOrDefaultAsync(cancellationToken);

        if (existingCompany == null)
            return;

        var updatedAionSettings = existingCompany.AionSettings ?? new CompanyAionSettingsEntity();

        if (company.AionSettings.CounterPartyId != null)
            updatedAionSettings.CounterPartyId = company.AionSettings.CounterPartyId;

        if (company.AionSettings.CounterPartyObjectId != null)
            updatedAionSettings.CounterPartyObjectId = company.AionSettings.CounterPartyObjectId;

        if (company.AionSettings.CounterPartyId2 != null)
            updatedAionSettings.CounterPartyId2 = company.AionSettings.CounterPartyId2;

        if (company.AionSettings.CounterPartyObjectId2 != null)
            updatedAionSettings.CounterPartyObjectId2 = company.AionSettings.CounterPartyObjectId2;

        if (company.AionSettings.CounterPartyId3 != null)
            updatedAionSettings.CounterPartyId3 = company.AionSettings.CounterPartyId3;

        if (company.AionSettings.CounterPartyObjectId3 != null)
            updatedAionSettings.CounterPartyObjectId3 = company.AionSettings.CounterPartyObjectId3;

        var update = Builders<CompanyEntity>.Update
            .Set(x => x.AionSettings, updatedAionSettings);

        await _dbContext.GetCollection<CompanyEntity>()
            .UpdateOneAsync(filter, update, cancellationToken: cancellationToken);
    }

    public async Task UpdateCompanyNameAsync(CompanyDto company, CancellationToken cancellationToken)
    {
        var filter = Builders<CompanyEntity>
            .Filter.Where(x => x.BlueTapeCompanyId == company.BlueTapeCompanyId);

        var update = Builders<CompanyEntity>.Update
            .Set(inv => inv.Name, company.Name);

        await _dbContext.GetCollection<CompanyEntity>().UpdateOneAsync(filter, update, cancellationToken: cancellationToken);
    }
}