openapi: '3.0.0'
info:
  version: '1.0.0'
  title: 'Tabapay API'
  description: | 
    API definition of Tabapay banking services, made manually by documentation.

    API version: v1

    Reference: https://developers.tabapay.com/reference/getting-started
servers:
- url: https://api.sandbox.tabapay.net:10443
  description: Sandbox
- url: https://api-use1.tabapay.net:10443
  description: Production
paths:
  /v1/clients/{clientId}/transactions:
    post:
      tags:
        - Transaction
      summary: Creates a Transaction.
      description: Creates a Transaction.
      operationId: createTransaction
      parameters:
        - name: clientId
          description: 22-character ClientID. Ask TabaPay Support if you need to specify a SubClientID.
          in: path
          required: true
          schema:
            type: string
            minLength: 22
            maxLength: 22
            nullable: false
      requestBody:
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/CreateTransactionRequest"
      responses:
        200:
          description: A Transaction is created and processing is completed.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/TransactionResponse'
        201:
          description: A Transaction is created, but the transaction is waiting to be processed (batch).
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/TransactionResponse'
        207:
          description: One or more Failures occurred while processing the Request.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/TransactionResponse'
        429:
          description: Over your Daily (24-hour rolling) Approximation Limit.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/TabapayErrorResponse'
  /v1/clients/{clientId}/transactions/{transactionId}:
    delete:
      tags:
        - Transaction
      summary: Tries to request a reverse of a previous Pull Transaction.
      description: Tries to request a reverse of a previous Pull Transaction.
      operationId: deleteTransaction
      parameters:
        - name: clientId
          description: 22-character ClientID. Ask TabaPay Support if you need to specify a SubClientID.
          in: path
          required: true
          schema:
            type: string
            minLength: 22
            maxLength: 22
            nullable: false
        - name: transactionId
          description: 22-character Transaction ID.
          in: path
          required: true
          schema:
            type: string
            minLength: 22
            maxLength: 22
            nullable: false
        - name: reversal
          description: Use for reversing a transaction. (i.e. if Delete Transaction Request is after settlement of original pull transaction.) Required to use void or reversal on every pull transaction DELETE request (non RTP).
          in: query
          schema:
            type: boolean
            nullable: true
        - name: void
          description: Use for voiding a transaction. (i.e. if Delete Transaction request is before settlement of original pull transaction.) Required to use void or reversal on every pull transaction DELETE request (non RTP).
          in: query
          schema:
            type: boolean
            nullable: true
      requestBody:
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/DeleteTransactionRequest"
      responses:
        200:
          description: A Request for a Reversal of the previous Transaction is successful.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/DeleteTransactionResponse'
        207:
          description: One or more Failures occurred while processing the Request.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/TransactionResponse'           
  /v1/clients/{clientId}:
    get:
      tags:
        - Client
      summary: Retrieves the attributes of a Client.
      description: Retrieves the attributes of a Client.
      operationId: getClientAttributes
      parameters:
        - name: clientId
          description: 22-character ClientID. Ask TabaPay Support if you need to specify a SubClientID.
          in: path
          required: true
          schema:
            type: string
            minLength: 22
            maxLength: 22
            nullable: false
      responses:
        200:
          description: Client's attributes are returned.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ClientAttributesResponse'
        404:
          description: Client not found.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/TabapayErrorResponse'
        423:
          description: Client locked.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/TabapayErrorResponse'
  /v1/clients/{clientId}/accounts:
    post:
      tags:
        - Account
      summary: Creates an Account.
      description: Creates an Account.
      operationId: createAccount
      parameters:
        - name: clientId
          description: 22-character ClientID. Ask TabaPay Support if you need to specify a SubClientID.
          in: path
          required: true
          schema:
            type: string
            minLength: 22
            maxLength: 22
            nullable: false
        - name: RejectDuplicateCard
          description: Use to disallow account creation if an account with card already exists.
          in: query
          required: false
          schema:
            type: string
        - name: OKToAddDuplicateCard
          description: Use to allow account creation if an account with card already exists.
          in: query
          required: false
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/CreateAccountRequest"
      responses:
        200:
          description: An Account is Created.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/CreateAccountResponse'
        207:
          description: Account created, but Duplicate Card Check Failed.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/TabapayErrorResponse'
        409:
          description: Duplicate Card Check Matched.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/CreateAccountDuplicationErrorResponse'
  /v1/clients/{clientId}/cards:
    post:
      tags:
        - Card
      summary: Returns the attributes for the requested Payment Card.
      description: Returns the attributes for the requested Payment Card.
      operationId: getCardDetails
      parameters:
        - name: clientId
          description: 22-character ClientID. Ask TabaPay Support if you need to specify a SubClientID.
          in: path
          required: true
          schema:
            type: string
            minLength: 22
            maxLength: 22
            nullable: false
        - name: AVS
          description: Use if you would like to perform AVS. If using in tandem with Fees query string, append ?AVS+Fees or ?Fees+AVS to your URL.
          in: query
          required: false
          schema:
            type: string
        - name: Fees
          description: Use to check fees. If using in tandem with AVS query string, append ?Fees+AVS or ?AVS+Fees to your URL.
          in: query
          required: false
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/CardDetailsRequest"
      responses:
        200:
          description: The Payment Card's Attributes are returned.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/CardDetailsResponse'
        207:
          description: One or more Failures occurred while processing the Request.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/TabapayErrorResponse'     
components:
  schemas:
    CreateTransactionRequest:
      type: object
      required: 
        - referenceID
        - type
        - accounts
        - amount
      properties:
        referenceID:
          type: string
          minLength: 1
          maxLength: 15
          pattern: ^[a-zA-Z0-9_-]{1,15}$
          description: 1-15 character unique referenceID.
        correspondingID:
          type: string
          description: The transactionID of the corresponding transaction. For a *pull*, the corresponding *push* transactionID. For a *push*, the corresponding *pull* transactionID. Incompatible with the "corresponding" JSON object.
        corresponding:
          $ref: "#/components/schemas/CorrespondingObject"
        type:
          type: string
          enum:
            - push
            - pull
          description: 4-character Transaction Type. push and pull are the only valid values. This is used to verify that your Source and Destination Accounts are valid
          example: push
        accounts:
          $ref: "#/components/schemas/AccountsObject"
        currency:
          type: string
          description: ISO 4217 3-Digit Currency Code.
          default: USD
          example: USD
        amount:
          type: string
          description: Amount of transaction. See https://developers.tabapay.com/reference/faq#how-to-specify-an-amount-value
          example: 1.00
        ofacValue:
          type: string
          description: OFAC Value from Query OFAC.
        purposeOfPayment:
          type: string
          description: Code describing the purpose of payment. Conditional. May be required, dependent on the country of the issuing BIN.
        memo:
          type: string
          description: 1-32 character Memo. Optional. Refer to our convenience fee guide if you need to set fees per transaction.
          minLength: 1
          maxLength: 32
          nullable: true
        achOptions:
          type: string
          enum:
            - R
            - N
          description: R RTP, N Next Day (coming soon). Required for RTP or ACH, else optional.
        achEntryType:
          type: string
          enum:
            - CCD
            - PPD
            - WEB
          description: Required for ACH, else optional.
        pullOptions:
          $ref: "#/components/schemas/PullOptionsObject"
        softDescriptor:
          $ref: "#/components/schemas/SoftDescriptorObject"
        location:
          $ref: "#/components/schemas/LocationObject"
    CorrespondingObject:
      type: object
      description: Corresponding Transaction Information. For a push transaction, this would be the corresponding pull transaction. For a pull transaction, this would be the corresponding push transaction. Incompatible with correspondingID.
      required: 
        - name
      properties:
        ofacValue:
          type: string
          description: Sender ofacValue. Received from Query OFAC in response.
          example: 7nGfHHedKNelaw
        name:
          $ref: "#/components/schemas/NameObject"
        address:
          $ref: "#/components/schemas/AddressObject"
        accountNumber:
          type: string
          description: Sender Account Number
        sourceOfFunds:
          type: string
          description: Sender Source of Funds.
          enum:
            - Debit Card
            - PrePaid Card
            - Credit Card
            - Cash
            - Deposit Account
            - Credit Account
            - Mobile Money Account
    NameObject:
      type: object
      required: 
        - first
      properties:
        first:
          type: string
          description: First Name
        last:
          type: string
          description: Last Name
    AddressObject:
      type: object
      properties:
        line:
          type: string
          description: Address Line
        city:
          type: string
          description: City
        state:
          type: string
          description: State Code. State Code must be a valid 2-character code if country is 840 or 124.
          example: 840
        zipcode:
          type: string
          description: Zip Code. If country is 840, zipcode must be 5 or 9 digits. If country is 124, zipcode must be in the A1A 1A1 format. Zip codes from other countries will not be checked.
          example: 11100
        country:
          type: string
          description: Country code. TabaPay uses ISO 3166-1 numeric (or numeric-3) codes.
          default: 840
          example: 840
    AccountsObject:
      type: object
      properties:
        sourceAccountID:
          type: string
          description: 22 characters TabaPay AccountID. Use your Settlement Account ID here on all push transactions.
          minLength: 22
          maxLength: 22
        sourceAccount:
          $ref: "#/components/schemas/AccountObject"
        destinationAccountID:
          type: string
          description: 22 characters TabaPay AccountID. Use your Settlement Account ID here on all pull transactions.
        destinationAccount:
          $ref: "#/components/schemas/AccountObject"
    AccountObject:
      type: object
      required: 
        - owner
      properties:
        card:
          $ref: "#/components/schemas/CardObject"
        bank:
          $ref: "#/components/schemas/BankObject"
        owner:
          $ref: "#/components/schemas/OwnerObject"
    CardObject:
      type: object
      properties:
        accountNumber:
          type: string
          description: Required field of Payment Card Not Encrypted. 13-19 digit Primary Account Number (PAN/Card Number). Compatible with expirationDate and securityCode, nothing else.
          minLength: 13
          maxLength: 19
        expirationDate:
          type: string
          description: Conditionally required field of Payment Card Not Encrypted. Expiration Date in YYYYMM format. Compatible with accountNumber and securityCode, nothing else.
        securityCode:
          type: string
          description: Optional field of Payment Card Not Encrypted. 3-4 digit Security Code. Compatible with accountNumber and expirationDate, nothing else.
          minLength: 3
          maxLength: 4
        keyID:
          type: string
          description: Required field of Payment Card Encrypted. 22 character KeyID. Ensure the RSA key you're using to encrypt the card data is consistent with this keyID. Compatible with data and nothing else.
        data:
          type: string
          description: Required field of Payment Card Encrypted. RSA-encrypted card data using RSA key with KeyID keyID. Compatible with keyID and nothing else.
        token:
          type: string
          description: Card Token (from our PCI-compliant iFrame). Incompatible with any other card field.
        device:
          $ref: "#/components/schemas/DeviceObject"
        mobilePay:
          $ref: "#/components/schemas/MobilePayObject"
        processor:
          $ref: "#/components/schemas/ProcessorObject"
    DeviceObject:
      type: object
      required: 
        - id
        - blob
      properties:
        id:
          type: string
          description: Either a - TabaPay-assigned device ID, - GooglePay|gatewayMerchantId.
        blob:
          type: string
          description: URL-safe, Base64 encoded card data.
    MobilePayObject:
      type: object
      description: A Decrypted Apple Pay Token.
      required: 
        - accountNumber
        - expirationDate
        - cryptogram
        - transactionID
        - network
        - type
      properties:
        accountNumber:
          type: string
          description: Pseudo Payment Card Account Number
        expirationDate:
          type: string
          description: Expiration Date in YYYYMM. Convert YYMMDD format to YYYYMM.
        cryptogram:
          type: string
          description: Payment Data Cryptogram.
        transactionID:
          type: string
          description: Transaction Identifier in Hex.
        eciIndicator:
          type: string
          description: 1-digit ECI Indicator. For example if received ECI is 05 convert to 5 and populate this field. Usually only present when network is Visa.
          example: 5
        network:
          type: string
          enum:
            - Visa
            - MasterCard
            - Discover
            - Amex
          description: Card Network.
        type:
          type: string
          enum:
            - Debit
            - Credit
            - PrePaid
          description: Card Type.
    ProcessorObject:
      type: object
      description: Tokenized Card Payment Data From an Issuing Processor. Incompatible with all other card objects and fields.
      properties:
        name:
          type: string
          enum:
            - Galileo
            - Q2
        token:
          type: string
          description: Token from issuing processor. Galileo integrations use a CAD (preferred) or PRN. Q2 integrations use a CardID.
        customerID:
          type: string
          description: Customer Identifier. Required if name is Q2, omit otherwise.
    PullOptionsObject:
      type: object
      properties:
        securityCode:
          type: string
          description: Security Code. Allowed only if using sourceAccountID.
          pattern: ^[0-9]{3,4}$
          minLength: 3
          maxLength: 4
        recurring:
          type: boolean
          description: Recurring Pull Transaction. Set value to true if security code is not present.
        3DSecure:
          $ref: "#/components/schemas/3DSecureObject"
        level2TaxExempt:
          type: boolean
        level2TaxAmount:
          type: string
          description: Level 2 Tax Amount (Currency is same as the Transaction Amount).
        level3:
          $ref: "#/components/schemas/Level3Object"
    SoftDescriptorObject:
      type: object
      description: Soft Descriptor. Restricted Usage. Card Network Mandates for Soft Descriptor. To avoid fines or to avoid declines, when providing zipCode and state, please ensure they match i.e. zipCode must correspond to the state values.
      required: 
        - name
        - address
      properties:
        name:
          type: string
        address:
          $ref: "#/components/schemas/AddressObject"
        phone:
          $ref: "#/components/schemas/PhoneObject"
        email:
          type: string
          description: Email Address. For American Express Bill Pay Provider program Seller Email Address (max of 40 characters).
    PhoneObject:
      type: object
      required: 
        - number
      properties:
        countryCode:
          type: string
          description: 1-3 digit Country Calling Code. Optional. Default is 1.
          default: 1
          minLength: 1
          maxLength: 3
        number:
          type: string
          description: 4-14 digit Phone Number. For example, 10 digits if US phone code is 1 (840) and 4-14 if foreign phone code is 101(non 840).
          minLength: 4
          maxLength: 14
          nullable: false
    LocationObject:
      type: object
      required: 
        - name
        - address
      properties:
        name:
          type: string
          description: Location Name.
        address:
          $ref: "#/components/schemas/AddressObject"
    BankObject:
      type: object
      required: 
        - routingNumber
        - accountNumber
        - accountType
      properties:
        routingNumber:
          type: string
          description: ABA routing transit number.
        accountNumber:
          type: string
          description: Bank Account Number.
        accountType:
          type: string
          enum:
            - S
            - C
            - A
            - B
            - L
          description: Bank Account Type. S Savings, C Checking, A Business Savings, B Business Checking, L Loan
    OwnerObject:
      type: object
      required: 
        - name
      properties:
        name:
          type: string
          description: Name. Exclusively use company OR first, middle, last, suffix(middle and suffix are optional)
          nullable: false
    3DSecureObject:
      type: object
      required: 
        - ECI
        - UCAF
      properties:
        version:
          type: string
          description: Version (Integer) Required if card network is MasterCard. Otherwise, optional
          example: 2
        ECI:
          type: string
          description: Electronic Commerce Indicator (ECI). 1-digit
          example: 5
        UCAF:
          type: string
          description: Universal Cardholder Authentication Field (UCAF)
        XID:
          type: string
          description: Transaction ID
        dsTransactionID:
          type: string
          description: Directory Server TransactionID. Required if card network is MasterCard. Otherwise, optional
    Level3Object:
      type: object
      properties:
        amountTax:
          type: string
          description: Tax Amount
        tax:
          type: string
          enum:
            - "0"
            - "1"
            - "2"
          description: 1 digit. 0 Sales Tax Not Included, 1 Sales Tax Included, 2 Tax Exempt
        taxRate:
          type: number
          description: Tax Rate. Max 2 decimal places.
        amountDiscount:
          type: string
          description: Discount Amount. Max 2 decimal places.
        amountShipping:
          type: string
          description: Shipping Amount. Max 2 decimal places.
        amountDuty:
          type: string
          description: Duty Amount. Max 2 decimal places.
        itemCommodityCode:
          type: string
          description: Item Commodity Code. Max length 12 characters.
          maxLength: 12
        itemDescription:
          type: string
          description: Item Description. Max length 26 characters.
          maxLength: 26
        productCode:
          type: string
          description: Product Code.
        quantity:
          type: number
        unitOfMeasure:
          type: string
          description: Unit of Measure.
        amountUnitCost:
          type: string
          description: Unit Cost. Max 2 decimal places.
        amountItemDiscount:
          type: string
          description: Discount Per Line Item.
        amountTotal:
          type: string
          description: Total. Refer to our Amount Guidelines. You Total Amount should match the amount for this transaction and should include Sub-total + Duty Amount + Shipping Amount + Tax Amount - Discount Amount.
        poNumber:
          type: string
          description: Purchase Order Number. Do NOT populate the field with all 0s or all 9s.
    CreateAccountRequest:
      type: object
      properties:
        referenceID:
          type: string
          description: 1-15 character unique referenceID.
        card:
          $ref: "#/components/schemas/CardObject"
        owner:
          $ref: "#/components/schemas/OwnerObject"
    AVSObject:
      type: object
      description: AVS Results
      properties:
        avsID:
          type: string
          description: AVS Transaction Identifier
        networkRC:
          type: string
          description: Network Response Code
        networkID:
          type: string
          description: The network id.
        authorizeID:
          type: string
        resultText:
          type: string
        codeAVS:
          type: string
          description: AVS Response Code
        codeSecurityCode:
          type: string
          description: Security Code Response Code
        EC:
          type: string
          description: Internal Error Code. This is used to help TabaPay team members trace an error.
    FeesObject:
      type: object
      required: 
        - interchange
        - network
        - tabapay
      description: Estimated Fees
      properties:
        interchange:
          type: string
          description: Interchange Fees
        network:
          type: string
          description: Network Fees
        tabapay:
          type: string
          description: TabaPay Fees
    FeesBiDirectionalObject:
      type: object
      properties:
        pull:
          $ref: "#/components/schemas/FeesObject"
        push:
          $ref: "#/components/schemas/FeesObject"
    CardResponseObject:
      type: object
      required: 
        - last4
      description: Card Details
      properties:
        nameFI:
          type: string
          description: Issuer Name
        last4:
          type: string
          description: Last 4 of Card Account Number (PAN)
        expirationDate:
          type: string
          description: Expiration Date. YYYYMM Format.
    TransactionResponse:
      type: object
      properties:
        SC:
          type: number
          format: int32
          description: HTTP Status Code
        EC:
          type: string
          description: Internal Error Code. This is used to help TabaPay team members trace an error.
        transactionID:
          type: string
          description: 22 character Transaction ID
        network:
          type: string
        networkRC:
          type: string
          description: 2-3 character Network Response Code
        networkID:
          type: string
          description: Network TransactionID. Not returned by every network.
        status:
          type: string
          description: Resource Status
        approvalCode:
          type: string
          description: 6-digit Approval Code
        errors:
          type: array
          items:
            type: string
        AVS:
          $ref: "#/components/schemas/AVSObject"
        fees:
          $ref: "#/components/schemas/FeesObject"
        card:
          $ref: "#/components/schemas/CardResponseObject"
    TabapayErrorResponse:
      type: object
      properties:
        SC:
          type: number
          format: int32
          description: HTTP Status Code
        EC:
          type: string
          description: Internal Error Code. This is used to help TabaPay team members trace an error.
        EM:
          type: string
          description: Short description of the error if an error occurred.
    CreateAccountResponse:
      type: object
      properties:      
        SC:
          type: number
          format: int32
          description: HTTP Status Code
        EC:
          type: string
          description: Internal Error Code. This is used to help TabaPay team members trace an error.
        accountID:
          type: string
          description: Account ID
          minLength: 22
          maxLength: 22
        card:
          $ref: "#/components/schemas/CardObject"
        notices:
          type: string
          description: Important Notices
    CreateAccountDuplicationErrorResponse:
      type: object
      properties:
        SC:
          type: number
          format: int32
          description: HTTP Status Code
        EC:
          type: string
          description: Internal Error Code. This is used to help TabaPay team members trace an error.
        duplicateAccountIDs:
          type: array
          description: AccountIDs using the same Card Account Number (PAN). If more than 3 AccountIDs exist, only the 3 most recently created will be present.
          items:
            type: string
    CardDetailsRequest:
      type: object
      properties:
        account:
          $ref: "#/components/schemas/AccountObject"
        card:
          $ref: "#/components/schemas/CardObject"
        owner:
          $ref: "#/components/schemas/OwnerObject"
        currency:
          type: string
          description: ISO 4217 3-Digit Currency Code.
          default: 840
        amount:
          type: string
          description: Required if using Fees query string.
        timeout:
          type: number
          format: int32
          description: Maximum time to wait for AVS and/or Verify Response.
          default: 39
    CardDetailsResponse:
      type: object
      properties:
        SC:
          type: number
          format: int32
          description: HTTP Status Code
        EC:
          type: string
          description: Internal Error Code. This is used to help TabaPay team members trace an error.
        EM:
          type: string
          description: Short description of the error if an error occurred.
        card:
          $ref: "#/components/schemas/CardObject"
        AVS:
          $ref: "#/components/schemas/AVSObject"
        fees:
          $ref: "#/components/schemas/FeesBiDirectionalObject"
    ClientAttributesResponse:
      type: object
      properties:
        SC:
          type: number
          format: int32
          description: HTTP Status Code
        EC:
          type: string
          description: Internal Error Code. This is used to help TabaPay team members trace an error.
        label:
          type: string
          description: The Client Label is the human readable identifier used to identify you versus using your ClientID. It may be used in part of the file name for various Reports we generate for you, and in part of the URL for access to the Client WebSite.
        networks:
          $ref: "#/components/schemas/NetworksObject"
        limits:
          $ref: "#/components/schemas/LimitsObject"
    NetworksObject:
      type: object
      description: List of Available Networks.
      properties:
        pull:
          type: array
          items:
            type: object
          description: For Pull Transactions. Array can be empty or is a List of Network Names.
        push:
          type: array
          items:
            type: object
          description: For Push Transactions. Array can be empty or is a List of Network Names.
    LimitsObject:
      type: object
      properties:
        currency:
          type: string
          description: ISO 4217 Currency Number.
        pull:
          $ref: "#/components/schemas/LimitObject"
        push:
          $ref: "#/components/schemas/LimitObject"
    LimitObject:
      type: object
      properties:
        transaction:
          type: string
          description: Transaction Limit.
        daily:
          type: string
          description: Approximate Daily Limit.
        networks:
          type: array
          description: List of Network Limits. Network is listed only if different from above Limits.
          items:
            $ref: "#/components/schemas/NetworkLimitObject"
    NetworkLimitObject:
      type: object
      properties:
        network:
          type: string
          description: Network Name.
        transaction:
          type: string
          description: Network Transaction Limit.
        daily:
          type: string
          description: Approximate Network Daily Limit.
    DeleteTransactionRequest:
      type: object
      properties:
        currency:
          type: string
          description: ISO 4217 3-Digit Currency Code.
          default: 840
        amount:
          type: string
          description: Partial Reversal Amount. If amount is omitted, the reversal request will be for the original transaction amount.
    DeleteTransactionResponse:
      type: object
      properties:
        SC:
          type: number
          format: int32
          description: HTTP Status Code
        EC:
          type: string
          description: Internal Error Code. This is used to help TabaPay team members trace an error.
        status:
          type: string
          description: Status of the reversal.
        reversal:
          $ref: "#/components/schemas/ReversalObject"
    ReversalObject:
      type: object
      properties:
        networkRC:
          type: string
          description: 2-3 character Network Response Code of Void.
        networkRC2:
          type: string
          description: 2-3 character Network Response Code of Refund (after failed void). Present if Dual-Message Network.
