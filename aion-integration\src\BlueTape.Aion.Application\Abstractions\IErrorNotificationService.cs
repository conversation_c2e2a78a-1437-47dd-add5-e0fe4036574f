﻿using BlueTape.Aion.DataAccess.External.Models.CreateAchTransfer.Response;
using BlueTape.SNS.SlackNotification.Models;

namespace BlueTape.Aion.Application.Abstractions;

public interface IErrorNotificationService
{
    Task Notify(string message, string eventName, string eventSource, CancellationToken ctx);
    Task Notify(EventMessageBody message, CancellationToken ctx);
    Task NotifyUnmatchedReturn(AchResponseObj achResponseObj, CancellationToken ctx);
    Task NotifyReturnedTransaction(AchResponseObj achResponseObj, CancellationToken ctx);
}