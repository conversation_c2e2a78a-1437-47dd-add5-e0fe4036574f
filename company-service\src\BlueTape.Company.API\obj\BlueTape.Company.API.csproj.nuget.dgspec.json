{"format": 1, "restore": {"C:\\Users\\<USER>\\source\\repos\\BlueTape\\company-service\\src\\BlueTape.Company.API\\BlueTape.Company.API.csproj": {}}, "projects": {"C:\\Users\\<USER>\\source\\repos\\BlueTape\\company-service\\src\\BlueTape.Company.API\\BlueTape.Company.API.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\company-service\\src\\BlueTape.Company.API\\BlueTape.Company.API.csproj", "projectName": "BlueTape.Company.API", "projectPath": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\company-service\\src\\BlueTape.Company.API\\BlueTape.Company.API.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\company-service\\src\\BlueTape.Company.API\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\source\\repos\\BlueTape\\company-service\\NuGet.Config", "C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net8.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}, "https://nuget.pkg.github.com/bluetape-org/index.json": {}}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "projectReferences": {"C:\\Users\\<USER>\\source\\repos\\BlueTape\\company-service\\src\\cashflow\\BlueTape.CashFlow.Application\\BlueTape.CashFlow.Application.csproj": {"projectPath": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\company-service\\src\\cashflow\\BlueTape.CashFlow.Application\\BlueTape.CashFlow.Application.csproj"}, "C:\\Users\\<USER>\\source\\repos\\BlueTape\\company-service\\src\\company\\BlueTape.Company.Application\\BlueTape.Company.Application.csproj": {"projectPath": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\company-service\\src\\company\\BlueTape.Company.Application\\BlueTape.Company.Application.csproj"}, "C:\\Users\\<USER>\\source\\repos\\BlueTape\\company-service\\src\\document\\BlueTape.Document.Application\\BlueTape.Document.Application.csproj": {"projectPath": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\company-service\\src\\document\\BlueTape.Document.Application\\BlueTape.Document.Application.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "dependencies": {"AWSSDK.Extensions.NETCore.Setup": {"target": "Package", "version": "[3.7.300, )"}, "AWSSDK.S3": {"target": "Package", "version": "[3.7.305, )"}, "Amazon.Lambda.AspNetCoreServer.Hosting": {"target": "Package", "version": "[1.6.1, )"}, "Azure.Extensions.AspNetCore.Configuration.Secrets": {"target": "Package", "version": "[1.3.0, )"}, "Azure.Identity": {"target": "Package", "version": "[1.11.3, )"}, "Azure.Security.KeyVault.Secrets": {"target": "Package", "version": "[4.5.0, )"}, "BlueTape.CompanyService": {"target": "Package", "version": "[1.3.2, )"}, "BlueTape.Logging": {"target": "Package", "version": "[1.0.0, )"}, "FluentValidation": {"target": "Package", "version": "[11.9.0, )"}, "FluentValidation.AspNetCore": {"target": "Package", "version": "[11.0.2, )"}, "FluentValidation.DependencyInjectionExtensions": {"target": "Package", "version": "[11.0.2, )"}, "Microsoft.ApplicationInsights.AspNetCore": {"target": "Package", "version": "[2.22.0, )"}, "Microsoft.EntityFrameworkCore.Design": {"include": "Runtime, Build, Native, ContentFiles, Analyzers, BuildTransitive", "suppressParent": "All", "target": "Package", "version": "[7.0.4, )"}, "Microsoft.NETCore.Targets": {"target": "Package", "version": "[6.0.0-preview.4.21253.7, )"}, "NSwag.AspNetCore": {"target": "Package", "version": "[14.0.0, )"}, "Serilog.Sinks.ApplicationInsights": {"target": "Package", "version": "[4.0.0, )"}, "Swashbuckle.AspNetCore.SwaggerGen": {"target": "Package", "version": "[6.5.0, )"}, "Swashbuckle.AspNetCore.SwaggerUI": {"target": "Package", "version": "[6.5.0, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "downloadDependencies": [{"name": "Microsoft.NETCore.App.Crossgen2.win-x64", "version": "[8.0.17, 8.0.17]"}], "frameworkReferences": {"Microsoft.AspNetCore.App": {"privateAssets": "none"}, "Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.301/PortableRuntimeIdentifierGraph.json"}}}, "C:\\Users\\<USER>\\source\\repos\\BlueTape\\company-service\\src\\cashflow\\BlueTape.CashFlow.Application\\BlueTape.CashFlow.Application.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\company-service\\src\\cashflow\\BlueTape.CashFlow.Application\\BlueTape.CashFlow.Application.csproj", "projectName": "BlueTape.CashFlow.Application", "projectPath": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\company-service\\src\\cashflow\\BlueTape.CashFlow.Application\\BlueTape.CashFlow.Application.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\company-service\\src\\cashflow\\BlueTape.CashFlow.Application\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\source\\repos\\BlueTape\\company-service\\NuGet.Config", "C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net8.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}, "https://nuget.pkg.github.com/bluetape-org/index.json": {}}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "projectReferences": {"C:\\Users\\<USER>\\source\\repos\\BlueTape\\company-service\\src\\cashflow\\BlueTape.CashFlow.Domain\\BlueTape.CashFlow.Domain.csproj": {"projectPath": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\company-service\\src\\cashflow\\BlueTape.CashFlow.Domain\\BlueTape.CashFlow.Domain.csproj"}, "C:\\Users\\<USER>\\source\\repos\\BlueTape\\company-service\\src\\shared\\BlueTape.DataAccess.EF\\BlueTape.DataAccess.EF.csproj": {"projectPath": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\company-service\\src\\shared\\BlueTape.DataAccess.EF\\BlueTape.DataAccess.EF.csproj"}, "C:\\Users\\<USER>\\source\\repos\\BlueTape\\company-service\\src\\shared\\BlueTape.DataAccess.MongoDB\\BlueTape.DataAccess.MongoDB.csproj": {"projectPath": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\company-service\\src\\shared\\BlueTape.DataAccess.MongoDB\\BlueTape.DataAccess.MongoDB.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "dependencies": {"AutoMapper": {"target": "Package", "version": "[12.0.1, )"}, "AutoMapper.Extensions.Microsoft.DependencyInjection": {"target": "Package", "version": "[12.0.1, )"}, "BlueTape.AWSS3": {"target": "Package", "version": "[1.1.5, )"}, "BlueTape.Common.FileService": {"target": "Package", "version": "[1.0.6, )"}, "BlueTape.Utilities": {"target": "Package", "version": "[1.4.5, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.301/PortableRuntimeIdentifierGraph.json"}}}, "C:\\Users\\<USER>\\source\\repos\\BlueTape\\company-service\\src\\cashflow\\BlueTape.CashFlow.Domain\\BlueTape.CashFlow.Domain.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\company-service\\src\\cashflow\\BlueTape.CashFlow.Domain\\BlueTape.CashFlow.Domain.csproj", "projectName": "BlueTape.CashFlow.Domain", "projectPath": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\company-service\\src\\cashflow\\BlueTape.CashFlow.Domain\\BlueTape.CashFlow.Domain.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\company-service\\src\\cashflow\\BlueTape.CashFlow.Domain\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\source\\repos\\BlueTape\\company-service\\NuGet.Config", "C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net8.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}, "https://nuget.pkg.github.com/bluetape-org/index.json": {}}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "projectReferences": {"C:\\Users\\<USER>\\source\\repos\\BlueTape\\company-service\\src\\shared\\BlueTape.Common\\BlueTape.Common.csproj": {"projectPath": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\company-service\\src\\shared\\BlueTape.Common\\BlueTape.Common.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.301/PortableRuntimeIdentifierGraph.json"}}}, "C:\\Users\\<USER>\\source\\repos\\BlueTape\\company-service\\src\\company\\BlueTape.Company.Application\\BlueTape.Company.Application.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\company-service\\src\\company\\BlueTape.Company.Application\\BlueTape.Company.Application.csproj", "projectName": "BlueTape.Company.Application", "projectPath": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\company-service\\src\\company\\BlueTape.Company.Application\\BlueTape.Company.Application.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\company-service\\src\\company\\BlueTape.Company.Application\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\source\\repos\\BlueTape\\company-service\\NuGet.Config", "C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net8.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}, "https://nuget.pkg.github.com/bluetape-org/index.json": {}}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "projectReferences": {"C:\\Users\\<USER>\\source\\repos\\BlueTape\\company-service\\src\\shared\\BlueTape.DataAccess.EF\\BlueTape.DataAccess.EF.csproj": {"projectPath": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\company-service\\src\\shared\\BlueTape.DataAccess.EF\\BlueTape.DataAccess.EF.csproj"}, "C:\\Users\\<USER>\\source\\repos\\BlueTape\\company-service\\src\\shared\\BlueTape.DataAccess.MongoDB\\BlueTape.DataAccess.MongoDB.csproj": {"projectPath": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\company-service\\src\\shared\\BlueTape.DataAccess.MongoDB\\BlueTape.DataAccess.MongoDB.csproj"}, "C:\\Users\\<USER>\\source\\repos\\BlueTape\\company-service\\src\\shared\\BlueTape.Domain\\BlueTape.Domain.csproj": {"projectPath": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\company-service\\src\\shared\\BlueTape.Domain\\BlueTape.Domain.csproj"}, "C:\\Users\\<USER>\\source\\repos\\BlueTape\\company-service\\src\\shared\\services\\BlueTape.LoanService.Client\\BlueTape.LoanService.Client.csproj": {"projectPath": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\company-service\\src\\shared\\services\\BlueTape.LoanService.Client\\BlueTape.LoanService.Client.csproj"}, "C:\\Users\\<USER>\\source\\repos\\BlueTape\\company-service\\src\\shared\\services\\BlueTape.OBS.Client\\BlueTape.OBS.Client.csproj": {"projectPath": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\company-service\\src\\shared\\services\\BlueTape.OBS.Client\\BlueTape.OBS.Client.csproj"}, "C:\\Users\\<USER>\\source\\repos\\BlueTape\\company-service\\src\\shared\\services\\BlueTape.Plaid.Client\\BlueTape.Plaid.Client.csproj": {"projectPath": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\company-service\\src\\shared\\services\\BlueTape.Plaid.Client\\BlueTape.Plaid.Client.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "dependencies": {"BlueTape.CompanyService": {"target": "Package", "version": "[1.3.2, )"}, "BlueTape.LMS": {"target": "Package", "version": "[1.0.2, )"}, "BlueTape.Utilities": {"target": "Package", "version": "[1.4.5, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.301/PortableRuntimeIdentifierGraph.json"}}}, "C:\\Users\\<USER>\\source\\repos\\BlueTape\\company-service\\src\\company\\BlueTape.Company.Domain\\BlueTape.Company.Domain.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\company-service\\src\\company\\BlueTape.Company.Domain\\BlueTape.Company.Domain.csproj", "projectName": "BlueTape.Company.Domain", "projectPath": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\company-service\\src\\company\\BlueTape.Company.Domain\\BlueTape.Company.Domain.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\company-service\\src\\company\\BlueTape.Company.Domain\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\source\\repos\\BlueTape\\company-service\\NuGet.Config", "C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net8.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}, "https://nuget.pkg.github.com/bluetape-org/index.json": {}}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "projectReferences": {"C:\\Users\\<USER>\\source\\repos\\BlueTape\\company-service\\src\\shared\\BlueTape.Common\\BlueTape.Common.csproj": {"projectPath": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\company-service\\src\\shared\\BlueTape.Common\\BlueTape.Common.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "dependencies": {"BlueTape.LS": {"target": "Package", "version": "[1.1.68, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.301/PortableRuntimeIdentifierGraph.json"}}}, "C:\\Users\\<USER>\\source\\repos\\BlueTape\\company-service\\src\\document\\BlueTape.Document.Application\\BlueTape.Document.Application.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\company-service\\src\\document\\BlueTape.Document.Application\\BlueTape.Document.Application.csproj", "projectName": "BlueTape.Document.Application", "projectPath": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\company-service\\src\\document\\BlueTape.Document.Application\\BlueTape.Document.Application.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\company-service\\src\\document\\BlueTape.Document.Application\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\source\\repos\\BlueTape\\company-service\\NuGet.Config", "C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net8.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}, "https://nuget.pkg.github.com/bluetape-org/index.json": {}}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "projectReferences": {"C:\\Users\\<USER>\\source\\repos\\BlueTape\\company-service\\src\\document\\BlueTape.Document.Domain\\BlueTape.Document.Domain.csproj": {"projectPath": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\company-service\\src\\document\\BlueTape.Document.Domain\\BlueTape.Document.Domain.csproj"}, "C:\\Users\\<USER>\\source\\repos\\BlueTape\\company-service\\src\\shared\\BlueTape.DataAccess.EF\\BlueTape.DataAccess.EF.csproj": {"projectPath": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\company-service\\src\\shared\\BlueTape.DataAccess.EF\\BlueTape.DataAccess.EF.csproj"}, "C:\\Users\\<USER>\\source\\repos\\BlueTape\\company-service\\src\\shared\\BlueTape.DataAccess.MongoDB\\BlueTape.DataAccess.MongoDB.csproj": {"projectPath": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\company-service\\src\\shared\\BlueTape.DataAccess.MongoDB\\BlueTape.DataAccess.MongoDB.csproj"}, "C:\\Users\\<USER>\\source\\repos\\BlueTape\\company-service\\src\\shared\\services\\BlueTape.LoanService.Client\\BlueTape.LoanService.Client.csproj": {"projectPath": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\company-service\\src\\shared\\services\\BlueTape.LoanService.Client\\BlueTape.LoanService.Client.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "dependencies": {"AWSSDK.S3": {"target": "Package", "version": "[3.7.305, )"}, "BlueTape.AWSS3": {"target": "Package", "version": "[1.1.5, )"}, "BlueTape.EmailSender": {"target": "Package", "version": "[2.0.1, )"}, "System.IdentityModel.Tokens.Jwt": {"target": "Package", "version": "[7.0.3, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.301/PortableRuntimeIdentifierGraph.json"}}}, "C:\\Users\\<USER>\\source\\repos\\BlueTape\\company-service\\src\\document\\BlueTape.Document.Domain\\BlueTape.Document.Domain.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\company-service\\src\\document\\BlueTape.Document.Domain\\BlueTape.Document.Domain.csproj", "projectName": "BlueTape.Document.Domain", "projectPath": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\company-service\\src\\document\\BlueTape.Document.Domain\\BlueTape.Document.Domain.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\company-service\\src\\document\\BlueTape.Document.Domain\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\source\\repos\\BlueTape\\company-service\\NuGet.Config", "C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net8.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}, "https://nuget.pkg.github.com/bluetape-org/index.json": {}}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "projectReferences": {"C:\\Users\\<USER>\\source\\repos\\BlueTape\\company-service\\src\\shared\\BlueTape.Common\\BlueTape.Common.csproj": {"projectPath": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\company-service\\src\\shared\\BlueTape.Common\\BlueTape.Common.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.301/PortableRuntimeIdentifierGraph.json"}}}, "C:\\Users\\<USER>\\source\\repos\\BlueTape\\company-service\\src\\shared\\BlueTape.Common\\BlueTape.Common.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\company-service\\src\\shared\\BlueTape.Common\\BlueTape.Common.csproj", "projectName": "BlueTape.Common", "projectPath": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\company-service\\src\\shared\\BlueTape.Common\\BlueTape.Common.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\company-service\\src\\shared\\BlueTape.Common\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\source\\repos\\BlueTape\\company-service\\NuGet.Config", "C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net8.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}, "https://nuget.pkg.github.com/bluetape-org/index.json": {}}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "dependencies": {"BlueTape.CompanyService.Common": {"target": "Package", "version": "[1.1.21, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.301/PortableRuntimeIdentifierGraph.json"}}}, "C:\\Users\\<USER>\\source\\repos\\BlueTape\\company-service\\src\\shared\\BlueTape.DataAccess.EF\\BlueTape.DataAccess.EF.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\company-service\\src\\shared\\BlueTape.DataAccess.EF\\BlueTape.DataAccess.EF.csproj", "projectName": "BlueTape.DataAccess.EF", "projectPath": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\company-service\\src\\shared\\BlueTape.DataAccess.EF\\BlueTape.DataAccess.EF.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\company-service\\src\\shared\\BlueTape.DataAccess.EF\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\source\\repos\\BlueTape\\company-service\\NuGet.Config", "C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net8.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}, "https://nuget.pkg.github.com/bluetape-org/index.json": {}}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "projectReferences": {"C:\\Users\\<USER>\\source\\repos\\BlueTape\\company-service\\src\\shared\\BlueTape.Domain\\BlueTape.Domain.csproj": {"projectPath": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\company-service\\src\\shared\\BlueTape.Domain\\BlueTape.Domain.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "dependencies": {"AutoMapper": {"target": "Package", "version": "[12.0.0, )"}, "AutoMapper.Extensions.Microsoft.DependencyInjection": {"target": "Package", "version": "[12.0.0, )"}, "BlueTape.CompanyService": {"target": "Package", "version": "[1.3.2, )"}, "BlueTape.Utilities": {"target": "Package", "version": "[1.4.5, )"}, "Microsoft.AspNetCore.Http.Abstractions": {"target": "Package", "version": "[2.2.0, )"}, "Microsoft.Data.SqlClient": {"target": "Package", "version": "[5.1.5, )"}, "Microsoft.EntityFrameworkCore": {"target": "Package", "version": "[7.0.4, )"}, "Microsoft.EntityFrameworkCore.InMemory": {"target": "Package", "version": "[7.0.4, )"}, "Microsoft.EntityFrameworkCore.Relational": {"target": "Package", "version": "[7.0.4, )"}, "Microsoft.EntityFrameworkCore.Tools": {"include": "Runtime, Build, Native, ContentFiles, Analyzers, BuildTransitive", "suppressParent": "All", "target": "Package", "version": "[7.0.4, )"}, "Npgsql.EntityFrameworkCore.PostgreSQL": {"target": "Package", "version": "[7.0.3, )"}, "TinyHelpers.EntityFrameworkCore": {"target": "Package", "version": "[2.0.14, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.301/PortableRuntimeIdentifierGraph.json"}}}, "C:\\Users\\<USER>\\source\\repos\\BlueTape\\company-service\\src\\shared\\BlueTape.DataAccess.MongoDB\\BlueTape.DataAccess.MongoDB.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\company-service\\src\\shared\\BlueTape.DataAccess.MongoDB\\BlueTape.DataAccess.MongoDB.csproj", "projectName": "BlueTape.DataAccess.MongoDB", "projectPath": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\company-service\\src\\shared\\BlueTape.DataAccess.MongoDB\\BlueTape.DataAccess.MongoDB.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\company-service\\src\\shared\\BlueTape.DataAccess.MongoDB\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\source\\repos\\BlueTape\\company-service\\NuGet.Config", "C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net8.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}, "https://nuget.pkg.github.com/bluetape-org/index.json": {}}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "projectReferences": {"C:\\Users\\<USER>\\source\\repos\\BlueTape\\company-service\\src\\company\\BlueTape.Company.Domain\\BlueTape.Company.Domain.csproj": {"projectPath": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\company-service\\src\\company\\BlueTape.Company.Domain\\BlueTape.Company.Domain.csproj"}, "C:\\Users\\<USER>\\source\\repos\\BlueTape\\company-service\\src\\document\\BlueTape.Document.Domain\\BlueTape.Document.Domain.csproj": {"projectPath": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\company-service\\src\\document\\BlueTape.Document.Domain\\BlueTape.Document.Domain.csproj"}, "C:\\Users\\<USER>\\source\\repos\\BlueTape\\company-service\\src\\shared\\BlueTape.Domain\\BlueTape.Domain.csproj": {"projectPath": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\company-service\\src\\shared\\BlueTape.Domain\\BlueTape.Domain.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "dependencies": {"AutoMapper": {"target": "Package", "version": "[12.0.0, )"}, "AutoMapper.Extensions.Microsoft.DependencyInjection": {"target": "Package", "version": "[12.0.0, )"}, "BlueTape.CompanyService": {"target": "Package", "version": "[1.3.2, )"}, "BlueTape.MongoDB": {"target": "Package", "version": "[1.1.30, )"}, "BlueTape.Utilities": {"target": "Package", "version": "[1.4.5, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.301/PortableRuntimeIdentifierGraph.json"}}}, "C:\\Users\\<USER>\\source\\repos\\BlueTape\\company-service\\src\\shared\\BlueTape.Domain\\BlueTape.Domain.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\company-service\\src\\shared\\BlueTape.Domain\\BlueTape.Domain.csproj", "projectName": "BlueTape.Domain", "projectPath": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\company-service\\src\\shared\\BlueTape.Domain\\BlueTape.Domain.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\company-service\\src\\shared\\BlueTape.Domain\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\source\\repos\\BlueTape\\company-service\\NuGet.Config", "C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net8.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}, "https://nuget.pkg.github.com/bluetape-org/index.json": {}}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "projectReferences": {"C:\\Users\\<USER>\\source\\repos\\BlueTape\\company-service\\src\\shared\\BlueTape.Common\\BlueTape.Common.csproj": {"projectPath": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\company-service\\src\\shared\\BlueTape.Common\\BlueTape.Common.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "dependencies": {"Z.EntityFramework.Plus.EFCore": {"target": "Package", "version": "[7.20.0, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.301/PortableRuntimeIdentifierGraph.json"}}}, "C:\\Users\\<USER>\\source\\repos\\BlueTape\\company-service\\src\\shared\\services\\BlueTape.LoanService.Client\\BlueTape.LoanService.Client.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\company-service\\src\\shared\\services\\BlueTape.LoanService.Client\\BlueTape.LoanService.Client.csproj", "projectName": "BlueTape.LoanService.Client", "projectPath": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\company-service\\src\\shared\\services\\BlueTape.LoanService.Client\\BlueTape.LoanService.Client.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\company-service\\src\\shared\\services\\BlueTape.LoanService.Client\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\source\\repos\\BlueTape\\company-service\\NuGet.Config", "C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net8.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}, "https://nuget.pkg.github.com/bluetape-org/index.json": {}}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "projectReferences": {"C:\\Users\\<USER>\\source\\repos\\BlueTape\\company-service\\src\\shared\\BlueTape.Common\\BlueTape.Common.csproj": {"projectPath": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\company-service\\src\\shared\\BlueTape.Common\\BlueTape.Common.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "dependencies": {"BlueTape.AzureKeyVault": {"target": "Package", "version": "[1.0.3, )"}, "BlueTape.LS": {"target": "Package", "version": "[1.1.68, )"}, "BlueTape.Utilities": {"target": "Package", "version": "[1.4.5, )"}, "Microsoft.Extensions.Configuration.Abstractions": {"target": "Package", "version": "[8.0.0, )"}, "Microsoft.Extensions.DependencyInjection.Abstractions": {"target": "Package", "version": "[8.0.0, )"}, "Microsoft.Extensions.Http": {"target": "Package", "version": "[8.0.0, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.301/PortableRuntimeIdentifierGraph.json"}}}, "C:\\Users\\<USER>\\source\\repos\\BlueTape\\company-service\\src\\shared\\services\\BlueTape.OBS.Client\\BlueTape.OBS.Client.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\company-service\\src\\shared\\services\\BlueTape.OBS.Client\\BlueTape.OBS.Client.csproj", "projectName": "BlueTape.OBS.Client", "projectPath": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\company-service\\src\\shared\\services\\BlueTape.OBS.Client\\BlueTape.OBS.Client.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\company-service\\src\\shared\\services\\BlueTape.OBS.Client\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\source\\repos\\BlueTape\\company-service\\NuGet.Config", "C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net8.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}, "https://nuget.pkg.github.com/bluetape-org/index.json": {}}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "dependencies": {"BlueTape.OBS": {"target": "Package", "version": "[1.6.56, )"}, "BlueTape.Utilities": {"target": "Package", "version": "[1.4.5, )"}, "Newtonsoft.Json": {"target": "Package", "version": "[13.0.1, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.301/PortableRuntimeIdentifierGraph.json"}}}, "C:\\Users\\<USER>\\source\\repos\\BlueTape\\company-service\\src\\shared\\services\\BlueTape.Plaid.Client\\BlueTape.Plaid.Client.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\company-service\\src\\shared\\services\\BlueTape.Plaid.Client\\BlueTape.Plaid.Client.csproj", "projectName": "BlueTape.Plaid.Client", "projectPath": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\company-service\\src\\shared\\services\\BlueTape.Plaid.Client\\BlueTape.Plaid.Client.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\company-service\\src\\shared\\services\\BlueTape.Plaid.Client\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\source\\repos\\BlueTape\\company-service\\NuGet.Config", "C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net8.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}, "https://nuget.pkg.github.com/bluetape-org/index.json": {}}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "dependencies": {"BlueTape.Integrations.Plaid": {"target": "Package", "version": "[1.0.7, )"}, "BlueTape.Utilities": {"target": "Package", "version": "[1.4.5, )"}, "Microsoft.AspNetCore.WebUtilities": {"target": "Package", "version": "[2.2.0, )"}, "Microsoft.Extensions.Configuration.Abstractions": {"target": "Package", "version": "[8.0.0, )"}, "Microsoft.Extensions.DependencyInjection.Abstractions": {"target": "Package", "version": "[8.0.0, )"}, "Microsoft.Extensions.Http": {"target": "Package", "version": "[8.0.0, )"}, "Microsoft.Extensions.Logging.Abstractions": {"target": "Package", "version": "[8.0.0, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.301/PortableRuntimeIdentifierGraph.json"}}}}}