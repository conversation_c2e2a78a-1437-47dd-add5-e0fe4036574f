{"info": {"_postman_id": "8113f9c1-0a55-4e76-8683-e33335216150", "name": "Aion API", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json", "_exporter_id": "24833147"}, "item": [{"name": "API User Login", "event": [{"listen": "test", "script": {"exec": ["const response = JSON.parse(responseBody);\r", "pm.environment.set(\"token\", response.authToken);"], "type": "text/javascript"}}], "request": {"auth": {"type": "<PERSON><PERSON><PERSON>"}, "method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\r\n  \"userId\" : \"{{userId}}\",\r\n  \"password\" : \"{{userPassword}}\",\r\n  \"apiKey\" : \"{{apiKey}}\"\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{host<PERSON>h}}/api/login", "host": ["{{host<PERSON><PERSON>}}"], "path": ["api", "login"]}}, "response": []}, {"name": "API Partner Login", "request": {"auth": {"type": "<PERSON><PERSON><PERSON>"}, "method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\r\n  \"UserId\" : null,\r\n  \"Password\" : null,\r\n  \"PartnerKey\" : null\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{host<PERSON><PERSON>}}/partner/login", "host": ["{{host<PERSON><PERSON>}}"], "path": ["partner", "login"]}}, "response": []}, {"name": "Get Accounts", "request": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{host}}/api/bb/getAccounts", "host": ["{{host}}"], "path": ["api", "bb", "getAccounts"]}}, "response": []}, {"name": "Get Account Info", "request": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\r\n  \"accountId\": null\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{host}}/api/bb/getAccountInfo", "host": ["{{host}}"], "path": ["api", "bb", "getAccountInfo"]}}, "response": []}, {"name": "Get Transactions", "request": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\r\n      \"accountId\": null,\r\n      \"fromDate\": null,\r\n      \"toDate\": null,\r\n      \"size\": 20,\r\n      \"providerStatus\": null,\r\n      \"page\": null\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{host}}/api/bb/getTransactions", "host": ["{{host}}"], "path": ["api", "bb", "getTransactions"]}}, "response": []}, {"name": "Create Counterparty", "request": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\r\n    \"counterparty\": {\r\n        \"type\": null,\r\n        \"nameOnAccount\": null,\r\n        \"email\": null\r\n    }\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{host}}/api/bb/createCounterparty", "host": ["{{host}}"], "path": ["api", "bb", "createCounterparty"]}}, "response": []}, {"name": "Get Counterparties", "request": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\r\n    \"page\": 0,\r\n    \"size\": 20\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{host}}/api/bb/getCounterparties", "host": ["{{host}}"], "path": ["api", "bb", "getCounterparties"]}}, "response": []}, {"name": "Add Transfer Method", "request": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\r\n    \"counterpartyId\": null,\r\n    \"accountId\": null,\r\n    \"nickName\": null,\r\n    \"bankDetail\": {\r\n        \"accountNumber\": null,\r\n        \"routingNumber\": null,\r\n        \"accountType\": null,\r\n        \"type\": null\r\n    }\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{host}}/api/bb/addTransferMethod", "host": ["{{host}}"], "path": ["api", "bb", "addTransferMethod"]}}, "response": []}, {"name": "Get Transfer Methods", "request": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\r\n    \"counterpartyId\": null\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{host}}/api/bb/getTransferMethods", "host": ["{{host}}"], "path": ["api", "bb", "getTransferMethods"]}}, "response": []}, {"name": "Create ACH Transfer", "request": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\r\n    \"achObj\": {\r\n        \"transferMethodId\": null,\r\n        \"accountId\": null,\r\n        \"accountNumber\": null,\r\n        \"amount\": null,\r\n        \"counterpartyId\": null,\r\n        \"counterpartyName\": null,\r\n        \"transactionType\": null,\r\n        \"sendEmail\": null,\r\n        \"addenda\": [ ],\r\n        \"description\": null,\r\n        \"serviceType\": null,\r\n        \"secCode\": null\r\n    }\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{host}}/api/bb/createACHTransfer", "host": ["{{host}}"], "path": ["api", "bb", "createACHTransfer"]}}, "response": []}, {"name": "Get ACH Transfers", "request": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\r\n    \"page\": 0,\r\n    \"size\": 20\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{host}}/api/bb/getACHTransfers", "host": ["{{host}}"], "path": ["api", "bb", "getACHTransfers"]}}, "response": []}], "auth": {"type": "apikey", "apikey": [{"key": "value", "value": "{{token}}", "type": "string"}, {"key": "key", "value": "AionAuth", "type": "string"}]}, "event": [{"listen": "prerequest", "script": {"type": "text/javascript", "exec": [""]}}, {"listen": "test", "script": {"type": "text/javascript", "exec": [""]}}]}