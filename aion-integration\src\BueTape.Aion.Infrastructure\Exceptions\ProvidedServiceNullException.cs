﻿using BlueTape.Common.ExceptionHandling.Exceptions.Base;
using System.Net;
using System.Runtime.Serialization;

namespace BueTape.Aion.Infrastructure.Exceptions;

[Serializable]
public class ProvidedServiceNullException : DomainException
{
    public ProvidedServiceNullException(string message, HttpStatusCode statusCode = HttpStatusCode.BadRequest) : base(message, statusCode)
    {
    }

    protected ProvidedServiceNullException(SerializationInfo info, StreamingContext context) : base(info, context)
    {
    }

    public override string Code => ErrorCodes.ProvidedServiceNull;
}