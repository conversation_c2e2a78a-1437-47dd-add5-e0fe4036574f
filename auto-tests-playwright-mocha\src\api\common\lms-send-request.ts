import axios from "axios";
import moment from 'moment';

require('dotenv').config();

const constants = JSON.parse(JSON.stringify(require('../../constants/LMStestData.json')));

export async function sendLMSRequest(method: string, endpoint: string, body: any = null) {
    try {
        const url = `${process.env.LMS_BASE_URL}/${endpoint}`;
        const commonHeaders = {
            'Content-Type': 'application/json'
        };

        console.log(`Sending ${method} request to: ${url}`);
        console.log('Headers:', commonHeaders);
        if (body) console.log('Body:', JSON.stringify(body));

        let response;
        switch (method) {
            case 'get':
                response = await axios.get(url, { headers: commonHeaders });
                break;
            case 'post':
                response = await axios.post(url, body, { headers: commonHeaders });
                break;
            case 'put':
                response = await axios.put(url, body, { headers: commonHeaders });
                break;
            case 'delete':
                response = await axios.delete(url, { headers: commonHeaders });
                break;
            case 'patch':
                response = await axios.patch(url, body, { headers: commonHeaders });
                break;
        }


        return response;
    } catch (error) {
        console.log(error);
        return error;
    }
}

interface ILoanQuery {
    fromDate?: Date;
    toDate?: Date;
    einHash?: string;
    payableId?: string;
    projectId?: string;
    product?: string;
    loanStatus?: string;
    showLateOnly?: boolean;
    detailed?: boolean;
}

interface ILoan {
    id: string;
    // ...other properties...
}

export async function findLoans(filter: ILoanQuery): Promise<ILoan[]> {
    try {
        const params = new URLSearchParams({
            FromDate: filter.fromDate ? moment(filter.fromDate).format('YYYY-MM-DD') : '',
            ToDate: filter.toDate ? moment(filter.toDate).format('YYYY-MM-DD') : '',
            EinHash: filter.einHash || '',
            PayableId: filter.payableId || '',
            ProjectId: filter.projectId || '',
            Product: filter.product || '',
            LoanStatus: filter.loanStatus || '',
            ShowLateOnly: filter.showLateOnly ? 'true' : 'false',
            Detailed: (filter.detailed ?? false) ? 'true' : 'false'
        });

        const response = await sendLMSRequest('get', `Loans?${params.toString()}`);
        return response.data;
    } catch (error) {
        console.error(error);
        throw error;
    }
}

// Create Loan
export async function createLoan() {
    try {
        // Create Template
        const requestBody = constants.loanTemplates.TemplateWithTwoInstallments;
        const responseTemplate = await sendLMSRequest('post', 'LoanTemplates', requestBody);
        const templateId = responseTemplate.data.id;
        // Create Loan
        const loanBody = {
            "companyId": constants.loans.ownerId,
            "amount": 100,
            "einHash": constants.loans.einHash,
            "loanTemplateId": templateId,
        };
        const loanResponse = await sendLMSRequest('post', 'Loans', loanBody);
        const loanId = loanResponse.data.id;
        return loanId;
    } catch (error) {
        return (error);
    }
}

export async function createLoanByTemplate(templateId: string) {
    try {
        // Create Loan
        const loanBody = {
            "companyId": constants.loans.ownerId,
            "amount": 100,
            "einHash": constants.loans.einHash,
            "loanTemplateId": templateId,
        };
        const loanResponse = await sendLMSRequest('post', 'Loans', loanBody);
        const loanId = loanResponse.data.id;
        return loanId;
    } catch (error) {
        return (error);
    }
}

export async function deleteLoan(loanId: string) {
    try {
        const response = await sendLMSRequest('delete', `Loans/${loanId}`);
        return response;
    } catch (error) {
        return (error);
    }
}

export async function postLoanRequest(ownerId: string, amount: number, hash: string, loanTemplateId: string) {
    const loanBody = {
        "companyId": ownerId,
        "amount": amount,
        "einHash": hash,
        "loanTemplateId": loanTemplateId,
    };
    try {
        return await sendLMSRequest('post', 'Loans', loanBody);
    } catch (error) {
        console.error(error);
        throw error;
    }
}

export async function postReplaceLoanReceivables(loanId, loanReceivables) {
    const loanReceivablesBody = {
        "loanReceivables": loanReceivables
    };
    try {
        return await sendLMSRequest('post', `Qa/Loans/${loanId}`, loanReceivablesBody);
    } catch (error) {
        console.error(error);
        throw error;
    }
}

export async function postAdminPayment(loanId: string, paymentAmount, dateOfPayment, userId) { //todo types
    const adminPaymentBody = {
        "LoanId": loanId,
        "Amount": paymentAmount,
        "Date": dateOfPayment,
        "Note": "Reason for manual payment"
    };
    try {
        const url = `${process.env.LMS_BASE_URL}/Admin/Payments`;
        const response = await axios.post(url, adminPaymentBody, {
            headers: {
                accept: 'text/plain',
                'Content-Type': 'application/json-patch+json',
                'userId': userId
            }
        });
        return response;
    } catch (error) {
        console.error(error);
        throw error;
    }
}

export async function postAdminLatePaymentFee(loanId, expectedAmount, expectedDate, userId) {
    const adminPaymentBody = {
        "expectedDate": expectedDate,
        "expectedAmount": expectedAmount,
        "loanId": loanId,
        "note": "string"
    };
    try {
        const url = `${process.env.LMS_BASE_URL}/Admin/LatePaymentFee`;
        const response = await axios.post(url, adminPaymentBody, {
            headers: {
                accept: 'text/plain',
                'Content-Type': 'application/json-patch+json',
                'userId': userId
            }
        });
        return response;
    } catch (error) {
        console.error(error);
        throw error;
    }
}

export async function postAdminExtensionFee(loanId, expectedAmount, expectedDate, userId) {
    const adminPaymentBody = {
        "expectedDate": expectedDate,
        "expectedAmount": expectedAmount,
        "loanId": loanId,
        "note": "string"
    };
    try {
        const url = `${process.env.LMS_BASE_URL}/Admin/LatePaymentFee`;
        const response = await axios.post(url, adminPaymentBody, {
            headers: {
                accept: 'text/plain',
                'Content-Type': 'application/json-patch+json',
                'userId': userId
            }
        });
        return response;
    } catch (error) {
        console.error(error);
        throw error;
    }
}

export async function postBasisRequest(basisPointValue, date) {
    try {
        const basisBody = {
            "basisPointValue": basisPointValue,
            "validFrom": date,
        };
        return await sendLMSRequest('post', 'BasisPoint', basisBody);
    } catch (error) {
        return (error);
    }
}


export async function calculateReplanDate(daysNumber) {
    const date = new Date();
    date.setDate(date.getDate() + daysNumber);
    const day = ("0" + (date.getDate())).slice(-2);
    const month = ("0" + (date.getMonth() + 1)).slice(-2);
    const year = date.getFullYear();
    const currentDate = `${year}-${month}-${day}`;
    return currentDate;
}
