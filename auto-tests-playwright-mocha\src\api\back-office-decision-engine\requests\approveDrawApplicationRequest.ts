﻿export class ApproveDrawApplicationRequest {
    newStatus: string;
    approvedCreditLimit: number;
    debtInvestor: string;
    code: string;
    note: string;

    constructor(newStatus: string, approvedCreditLimit: number, debtInvestor: string, code: string, note: string) {
        this.newStatus = newStatus;
        this.approvedCreditLimit = approvedCreditLimit;
        this.debtInvestor = debtInvestor;
        this.code = code;
        this.note = note;
    }
}

export class AttachSupplierToDrawApplicationRequest {
    supplierId: string;

    constructor(supplierId: string) {
        this.supplierId = supplierId;
    }
}