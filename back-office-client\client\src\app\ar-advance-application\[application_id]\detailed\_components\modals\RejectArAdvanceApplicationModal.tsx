import { useCallback } from 'react'
import { useTranslation } from 'react-i18next'

import { StyledModal } from '@/components/common/Modal'
import StyledTitle from '@/components/common/typography/StyledTitle'
import type { IModalProps } from '@/globals/types'
import type { ArAdvanceApplicationRejectData } from '@/app/ar-advance-application/[application_id]/detailed/_data-models/ar-advance-application-reject-data'
import { useRejectArAdvanceApplicationMutation } from '@/lib/redux/api/ar-advance-application'
import RejectArAdvanceApplicationForm from '@/app/ar-advance-application/[application_id]/detailed/_components/forms/RejectArAdvanceApplicationForm'
import { useAppSelector } from '@/lib/redux/hooks'

interface IProps extends IModalProps {
  applicationId: string
}

const RejectArAdvanceApplicationModal = ({
  isOpened,
  onClose: close,
  applicationId,
}: Readonly<IProps>): JSX.Element => {
  const { t } = useTranslation()
  const userId = useAppSelector((state) => state.user.user?.uid) ?? ''

  const [rejectApplication, { isLoading: isMutationLoading }] =
    useRejectArAdvanceApplicationMutation()

  const submit = useCallback(
    async (data: ArAdvanceApplicationRejectData) => {
      await rejectApplication({
        applicationId,
        code: data.code,
        note: data.note,
        userId,
      }).unwrap()

      close()
    },
    [applicationId, rejectApplication, close],
  )

  return (
    <StyledModal
      open={isOpened}
      title={
        <StyledTitle level={4}>
          {t('arAdvanceApplication.page.detailed.reject.wantToReject')}
        </StyledTitle>
      }
      onCancel={close}
      destroyOnClose
      footer={null}
    >
      <RejectArAdvanceApplicationForm
        onSubmit={submit}
        isSubmitLoading={isMutationLoading}
        onClose={close}
      />
    </StyledModal>
  )
}

export default RejectArAdvanceApplicationModal
