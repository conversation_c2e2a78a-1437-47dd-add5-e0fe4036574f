{"id": "498d4695-09a9-4aee-94f5-4647ddd4076e", "name": "LexisNexis PROD", "values": [{"key": "OrgId", "value": "", "type": "secret", "enabled": true}, {"key": "<PERSON><PERSON><PERSON><PERSON>", "value": "", "type": "secret", "enabled": true}, {"key": "url", "value": "https://h-api.online-metrix.net", "type": "default", "enabled": true}], "_postman_variable_scope": "environment", "_postman_exported_at": "2023-07-17T09:16:44.993Z", "_postman_exported_using": "Postman/10.16.0"}