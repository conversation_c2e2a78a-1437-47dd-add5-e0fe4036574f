using BlueTape.Aion.DataAccess.MongoDB.Abstractions;
using BlueTape.Aion.DataAccess.MongoDB.Entities.User;
using BlueTape.MongoDB.Abstractions;
using MongoDB.Driver;
using System.Diagnostics.CodeAnalysis;

namespace BlueTape.Aion.DataAccess.MongoDB.Repositories;

[ExcludeFromCodeCoverage]
public class UserRepository(IMongoDbContext dbContext) : IUserRepository
{

    public Task<List<UserEntity>?> GetByExternalId(IEnumerable<string> externalIds, CancellationToken ct)
    {
        return dbContext.GetCollection<UserEntity>()
            .Find(x => externalIds.Contains(x.ExternalId))
            .ToListAsync(ct);
    }
}
