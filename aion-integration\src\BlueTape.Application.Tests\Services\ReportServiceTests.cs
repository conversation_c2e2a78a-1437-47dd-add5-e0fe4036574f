﻿using AutoFixture;
using BlueTape.Aion.Application.Abstractions;
using BlueTape.Aion.Application.Abstractions.MessageSenders;
using BlueTape.Aion.Application.Service;
using BlueTape.Aion.DataAccess.External.Models.CreateAchTransfer.Response;
using BlueTape.Aion.DataAccess.External.Models.InternalTransfer;
using BlueTape.Application.Tests.AutoFixture.Attributes;
using BlueTape.Common.Extensions.Abstractions;
using BlueTape.Integrations.Aion.Infrastructure.Enums;
using BlueTape.ServiceBusMessaging.Attributes;
using BueTape.Aion.Infrastructure.Options;
using BueTape.Aion.Infrastructure.ServiceBusMessages;
using BueTape.Aion.Infrastructure.ServiceBusMessages.Report;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Options;
using Moq;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace BlueTape.Application.Tests.Services;

public class ReportServiceTests
{
    private readonly ReportService _reportService;

    private readonly IAchService _achServiceMock = Substitute.For<IAchService>();
    private readonly ITransactionService _transactionServiceMock = Substitute.For<ITransactionService>();
    private readonly IConfiguration _configurationMock = Substitute.For<IConfiguration>();
    private readonly ITraceIdAccessor _traceIdAccessor = Substitute.For<ITraceIdAccessor>();

    private readonly IAionExternalTransactionMessageSender _aionExternalTransactionMessageSender =
        Substitute.For<IAionExternalTransactionMessageSender>();

    private readonly IAionInternalTransactionMessageSender _aionInternalTransactionMessageSender =
        Substitute.For<IAionInternalTransactionMessageSender>();

    private Fixture _fixture = new Fixture();

    private const int DateRange = 5;

    public ReportServiceTests()
    {
        var options = Options.Create(new AionReportOptions()
        {
            DateRange = DateRange
        });

        _reportService = new ReportService(
            _achServiceMock,
            _transactionServiceMock,
            options,
            _aionExternalTransactionMessageSender,
            _aionInternalTransactionMessageSender);
    }

    [Theory, AutoFixtureCustom]
    public async Task RunInternalTransactionStatusReportAsync_Execute_Success_With_Next_Iteration(List<BookTransferObj> bookTransferObjs)
    {
        var internalPage = 1;

        _achServiceMock.GetAchInternalTransactionsByPageAsync(internalPage, DateRange, PaymentSubscriptionType.SUBSCRIPTION1.ToString(), default)
            .Returns((bookTransferObjs, 2));

        await _reportService.RunInternalTransactionStatusReportAsync(new AionInternalTransactionReportRequest()
        {
            InternalTransactionReportRequests = new List<InternalTransactionReportRequest>()
            {
                new ()
                {
                    InternalTransactionPage = internalPage,
                    PaymentSubscriptionType = PaymentSubscriptionType.SUBSCRIPTION1
                }
            }
        }, default);

        await _transactionServiceMock.ReceivedWithAnyArgs(1).SaveAionInternalTransactionsAsync(bookTransferObjs, default);
        await _aionInternalTransactionMessageSender.ReceivedWithAnyArgs(1).SendMessage(It.IsAny<ServiceBusMessageBt<AionInternalTransactionMessage>>());
    }

    [Theory, AutoFixtureCustom]
    public async Task RunInternalTransactionStatusReportAsync_Execute_Success_Without_Next_Iteration(List<BookTransferObj> bookTransferObjs)
    {
        var internalPage = 1;

        _achServiceMock.GetAchInternalTransactionsByPageAsync(internalPage, DateRange, PaymentSubscriptionType.SUBSCRIPTION1.ToString(), default)
            .Returns((bookTransferObjs, null));

        await _reportService.RunInternalTransactionStatusReportAsync(new AionInternalTransactionReportRequest()
        {
            InternalTransactionReportRequests = new List<InternalTransactionReportRequest>()
            {
                new()
                {
                    PaymentSubscriptionType = PaymentSubscriptionType.SUBSCRIPTION1,
                    InternalTransactionPage = internalPage
                }
            }
        }, default);

        await _transactionServiceMock.ReceivedWithAnyArgs(1).SaveAionInternalTransactionsAsync(bookTransferObjs, default);
        await _aionInternalTransactionMessageSender.ReceivedWithAnyArgs(0).SendMessage(It.IsAny<ServiceBusMessageBt<AionInternalTransactionMessage>>());
    }

    [Theory]
    [InlineData(2, 2)]
    [InlineData(2, null)]
    [InlineData(null, 2)]
    public async Task RunExternalTransactionStatusReportAsync_Execute_Success_With_Next_Iterration(int? achNextPage, int? achReturnNextPage)
    {
        var achResponseObjs = _fixture.Create<List<AchResponseObj>>();
        var achResponseReturnObjs = _fixture.Create<List<AchResponseObj>>();

        var internalPage = 1;

        _achServiceMock.GetAchTransactionsByPageAsync(internalPage, DateRange, PaymentSubscriptionType.SUBSCRIPTION1.ToString(), default)
            .Returns((achResponseObjs, achNextPage));

        _achServiceMock.GetAchReturnTransactionsByPageAsync(internalPage, DateRange, PaymentSubscriptionType.SUBSCRIPTION1.ToString(), default)
            .Returns((achResponseReturnObjs, achReturnNextPage));

        await _reportService.RunExternalTransactionStatusReportAsync(new AionExternalAchTransactionReportRequest()
        {
            ExternalAchReportRequests = new List<ExternalAchReportRequest>()
            {
                new()
                {
                    PaymentSubscriptionType = PaymentSubscriptionType.SUBSCRIPTION1,
                    AchReturnTransactionPage = internalPage,
                    AchTransactionPage = internalPage
                }
            }
        }, default);

        await _transactionServiceMock.ReceivedWithAnyArgs(1).SaveAionAchTransactionsAsync(achResponseObjs, default);
        await _aionExternalTransactionMessageSender.ReceivedWithAnyArgs(1).SendMessage(It.IsAny<ServiceBusMessageBt<AionExternalTransactionMessage>>());
    }

    [Theory]
    [InlineData(null, null)]
    public async Task RunExternalTransactionStatusReportAsync_Execute_Success_Without_Next_Iteration(int? achNextPage, int? achReturnNextPage)
    {
        var achResponseObjs = _fixture.Create<List<AchResponseObj>>();
        var achResponseReturnObjs = _fixture.Create<List<AchResponseObj>>();

        var internalPage = 1;

        _achServiceMock.GetAchTransactionsByPageAsync(internalPage, DateRange, PaymentSubscriptionType.SUBSCRIPTION1.ToString(), default)
            .Returns((achResponseObjs, achNextPage));

        _achServiceMock.GetAchReturnTransactionsByPageAsync(internalPage, DateRange, PaymentSubscriptionType.SUBSCRIPTION1.ToString(), default)
            .Returns((achResponseReturnObjs, achReturnNextPage));

        await _reportService.RunExternalTransactionStatusReportAsync(new AionExternalAchTransactionReportRequest()
        {
            ExternalAchReportRequests = new List<ExternalAchReportRequest>()
            {
                new()
                {
                    PaymentSubscriptionType = PaymentSubscriptionType.SUBSCRIPTION1,
                    AchReturnTransactionPage = achReturnNextPage,
                    AchTransactionPage = achNextPage
                }
            }
        }, default);

        await _transactionServiceMock.ReceivedWithAnyArgs(1).SaveAionAchTransactionsAsync(achResponseObjs, default);
        await _aionExternalTransactionMessageSender.ReceivedWithAnyArgs(0).SendMessage(It.IsAny<ServiceBusMessageBt<AionExternalTransactionMessage>>());
    }
}