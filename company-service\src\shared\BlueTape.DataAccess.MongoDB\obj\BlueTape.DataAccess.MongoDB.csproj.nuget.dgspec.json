{"format": 1, "restore": {"C:\\Users\\<USER>\\source\\repos\\BlueTape\\company-service\\src\\shared\\BlueTape.DataAccess.MongoDB\\BlueTape.DataAccess.MongoDB.csproj": {}}, "projects": {"C:\\Users\\<USER>\\source\\repos\\BlueTape\\company-service\\src\\company\\BlueTape.Company.Domain\\BlueTape.Company.Domain.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\company-service\\src\\company\\BlueTape.Company.Domain\\BlueTape.Company.Domain.csproj", "projectName": "BlueTape.Company.Domain", "projectPath": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\company-service\\src\\company\\BlueTape.Company.Domain\\BlueTape.Company.Domain.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\company-service\\src\\company\\BlueTape.Company.Domain\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\source\\repos\\BlueTape\\company-service\\NuGet.Config", "C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net8.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}, "https://nuget.pkg.github.com/bluetape-org/index.json": {}}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "projectReferences": {"C:\\Users\\<USER>\\source\\repos\\BlueTape\\company-service\\src\\shared\\BlueTape.Common\\BlueTape.Common.csproj": {"projectPath": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\company-service\\src\\shared\\BlueTape.Common\\BlueTape.Common.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "dependencies": {"BlueTape.LS": {"target": "Package", "version": "[1.1.68, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.301/PortableRuntimeIdentifierGraph.json"}}}, "C:\\Users\\<USER>\\source\\repos\\BlueTape\\company-service\\src\\document\\BlueTape.Document.Domain\\BlueTape.Document.Domain.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\company-service\\src\\document\\BlueTape.Document.Domain\\BlueTape.Document.Domain.csproj", "projectName": "BlueTape.Document.Domain", "projectPath": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\company-service\\src\\document\\BlueTape.Document.Domain\\BlueTape.Document.Domain.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\company-service\\src\\document\\BlueTape.Document.Domain\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\source\\repos\\BlueTape\\company-service\\NuGet.Config", "C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net8.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}, "https://nuget.pkg.github.com/bluetape-org/index.json": {}}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "projectReferences": {"C:\\Users\\<USER>\\source\\repos\\BlueTape\\company-service\\src\\shared\\BlueTape.Common\\BlueTape.Common.csproj": {"projectPath": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\company-service\\src\\shared\\BlueTape.Common\\BlueTape.Common.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.301/PortableRuntimeIdentifierGraph.json"}}}, "C:\\Users\\<USER>\\source\\repos\\BlueTape\\company-service\\src\\shared\\BlueTape.Common\\BlueTape.Common.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\company-service\\src\\shared\\BlueTape.Common\\BlueTape.Common.csproj", "projectName": "BlueTape.Common", "projectPath": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\company-service\\src\\shared\\BlueTape.Common\\BlueTape.Common.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\company-service\\src\\shared\\BlueTape.Common\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\source\\repos\\BlueTape\\company-service\\NuGet.Config", "C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net8.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}, "https://nuget.pkg.github.com/bluetape-org/index.json": {}}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "dependencies": {"BlueTape.CompanyService.Common": {"target": "Package", "version": "[1.1.21, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.301/PortableRuntimeIdentifierGraph.json"}}}, "C:\\Users\\<USER>\\source\\repos\\BlueTape\\company-service\\src\\shared\\BlueTape.DataAccess.MongoDB\\BlueTape.DataAccess.MongoDB.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\company-service\\src\\shared\\BlueTape.DataAccess.MongoDB\\BlueTape.DataAccess.MongoDB.csproj", "projectName": "BlueTape.DataAccess.MongoDB", "projectPath": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\company-service\\src\\shared\\BlueTape.DataAccess.MongoDB\\BlueTape.DataAccess.MongoDB.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\company-service\\src\\shared\\BlueTape.DataAccess.MongoDB\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\source\\repos\\BlueTape\\company-service\\NuGet.Config", "C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net8.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}, "https://nuget.pkg.github.com/bluetape-org/index.json": {}}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "projectReferences": {"C:\\Users\\<USER>\\source\\repos\\BlueTape\\company-service\\src\\company\\BlueTape.Company.Domain\\BlueTape.Company.Domain.csproj": {"projectPath": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\company-service\\src\\company\\BlueTape.Company.Domain\\BlueTape.Company.Domain.csproj"}, "C:\\Users\\<USER>\\source\\repos\\BlueTape\\company-service\\src\\document\\BlueTape.Document.Domain\\BlueTape.Document.Domain.csproj": {"projectPath": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\company-service\\src\\document\\BlueTape.Document.Domain\\BlueTape.Document.Domain.csproj"}, "C:\\Users\\<USER>\\source\\repos\\BlueTape\\company-service\\src\\shared\\BlueTape.Domain\\BlueTape.Domain.csproj": {"projectPath": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\company-service\\src\\shared\\BlueTape.Domain\\BlueTape.Domain.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "dependencies": {"AutoMapper": {"target": "Package", "version": "[12.0.0, )"}, "AutoMapper.Extensions.Microsoft.DependencyInjection": {"target": "Package", "version": "[12.0.0, )"}, "BlueTape.CompanyService": {"target": "Package", "version": "[1.3.2, )"}, "BlueTape.MongoDB": {"target": "Package", "version": "[1.1.30, )"}, "BlueTape.Utilities": {"target": "Package", "version": "[1.4.5, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.301/PortableRuntimeIdentifierGraph.json"}}}, "C:\\Users\\<USER>\\source\\repos\\BlueTape\\company-service\\src\\shared\\BlueTape.Domain\\BlueTape.Domain.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\company-service\\src\\shared\\BlueTape.Domain\\BlueTape.Domain.csproj", "projectName": "BlueTape.Domain", "projectPath": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\company-service\\src\\shared\\BlueTape.Domain\\BlueTape.Domain.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\company-service\\src\\shared\\BlueTape.Domain\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\source\\repos\\BlueTape\\company-service\\NuGet.Config", "C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net8.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}, "https://nuget.pkg.github.com/bluetape-org/index.json": {}}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "projectReferences": {"C:\\Users\\<USER>\\source\\repos\\BlueTape\\company-service\\src\\shared\\BlueTape.Common\\BlueTape.Common.csproj": {"projectPath": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\company-service\\src\\shared\\BlueTape.Common\\BlueTape.Common.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "dependencies": {"Z.EntityFramework.Plus.EFCore": {"target": "Package", "version": "[7.20.0, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.301/PortableRuntimeIdentifierGraph.json"}}}}}