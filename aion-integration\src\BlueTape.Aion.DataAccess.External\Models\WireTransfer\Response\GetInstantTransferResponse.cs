using System.Text.Json.Serialization;
using BlueTape.Aion.DataAccess.External.Models.InstantTransfer.Response;

namespace BlueTape.Aion.DataAccess.External.Models.WireTransfer.Response;

public class GetInstantTransferResponse : BaseAionResponseModel
{
    [JsonPropertyName("count")]
    public int Count { get; set; }

    [JsonPropertyName("numPages")]
    public int NumPages { get; set; }

    [JsonPropertyName("pageNumber")]
    public int PageNumber { get; set; }

    [JsonPropertyName("instantTransferList")]
    public List<InstantTransferObjectItemResponse> InstantList { get; set; } = new();
}