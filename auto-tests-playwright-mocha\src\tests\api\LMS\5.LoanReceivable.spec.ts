import {expect} from '@playwright/test';
import {test} from '../../test-utils';
import {sendLMSRequest, createLoan, deleteLoan} from '../../../api/common/lms-send-request';


const constants = JSON.parse(JSON.stringify(require('../../../constants/LMStestData.json')));

test.describe(`Loan Receivable @LMS @API`, async () => {

    let loanId: string;

    test.beforeAll(async () => {
        loanId = await createLoan();
    });

    test.afterAll(async () => {
        const response = await deleteLoan(loanId);

        expect(response.status, `Status code 200`)
            .toEqual(200);
    });

    test(`Get array of Receivables by query params: LoanID. @lms`, async () => {
        const response = await sendLMSRequest('get', `LoanReceivables?LoanId=${loanId}`);

        expect(response.status, `Status code 200`)
            .toEqual(200);

        expect(response.data, `Array is returned in Response`)
            .toEqual(expect.any(Array));
    });

    test(`Get array of all Receivables. @lms`, async () => { //skip
        const response = await sendLMSRequest('get', `LoanReceivables`);

        expect(response.status, `Status code 200`)
            .toEqual(200);

        expect(response.data, `Array is returned in Response`)
            .toEqual(expect.any(Array));
    });

    test(`Number of Receivables is equal to 'Installments Number + 1'. @lms`, async () => {
        const response = await sendLMSRequest('get', `LoanReceivables?LoanId=${loanId}`);

        expect(response.status, `Status code 200`)
            .toEqual(200);

        expect(response.data.length, `Receivables Number are equal to Installments Number`)
            .toEqual(constants.loanTemplates.TemplateWithTwoInstallments.InstallmentsNumber + 1);
    });

    /**
     * Negative Tests
     */

    test(`Getting empty Loan Receivables by invalid loan Id. @lms`, async () => {
        const response = await sendLMSRequest('get', `LoanReceivables?LoanId=${constants.invalidLoanIDs.id}`);

        expect(response.status, `Status code 200`)
            .toEqual(200);

        expect(response.data, `Received answer contains an empty array`)
            .toEqual([]);
    });
});
