import { Page } from '@playwright/test';
import {BasePage} from '../../base.page';

export class PartnershipApplication extends BasePage {
    constructor(page: Page){
        super(page);
    };

    containers = {
        businessTypeListContainer: this.page.locator('[data-testid="supplier_application_business_type_picker"]'),
        businessCategoryContainer: this.page.locator('[data-testid="supplier_application_business_category_picker"]'),
        refundPolicyContainer: this.page.locator('[data-testid="supplier_application_refund_policy_picker"]'),
        annualVolumeOfSalesContainer: this.page.locator('[data-testid="supplier_application_annual_volume_of_sales_picker"]'),
        averageTransactionSizeContainer: this.page.locator('[data-testid="supplier_application_avarage_transaction_size_picker"]'),
        highestTransactionSizeContainer: this.page.locator('[data-testid="supplier_application_highest_transaction_size_picker"]'),
        stateContainer: this.page.locator(''), // need to create locator
        titleOfOwnerContainer: this.page.locator('[data-testid="supplier_application_title_of_owner"]'),
    };

    locators = {
        dropdownField: '[class="css-1dbjc4n r-1awozwy r-18u37iz"]',
        dropdownListItem: '[class="css-1dbjc4n r-1awozwy r-1loqt21 r-18u37iz r-1otgn73 r-1i6wzkk r-lrvibr"]',
    };

    buttons = {
        submitApplication: this.page.locator('"Submit Application"'),
        next: this.page.locator('"Next"'),
    };

    businessDetails = {
        legalBusinessName: this.page.locator('//*[@data-testid="Legal business name"]//input'),
        businessStartDate: this.page.locator('//*[@data-testid="Business start date"]//input'),
        businessTaxID: this.page.locator('//*[@data-testid="Business Tax ID (e.g. TIN/EIN)"]//input'),

        businessTypeField: this.page.locator(`${this.locators.dropdownField} >> nth=2`), // need to ask for dropdown locators
        businessCategoryField: this.page.locator(`${this.locators.dropdownField} >> nth=3`), 
        refundPolicyField: this.page.locator(`${this.locators.dropdownField} >> nth=4`),
        annualVolumeOfSalesField: this.page.locator(`${this.locators.dropdownField} >> nth=5`),
        averageTransactionSizeField: this.page.locator(`${this.locators.dropdownField} >> nth=6`),
        highestTransactionSizeField: this.page.locator(`${this.locators.dropdownField} >> nth=7`),

        businessTypeList: this.containers.businessTypeListContainer.locator(this.locators.dropdownListItem),
        businessCategoryList: this.containers.businessCategoryContainer.locator(this.locators.dropdownListItem),
        refundPolicyList: this.containers.refundPolicyContainer.locator(this.locators.dropdownListItem),
        annualVolumeOfSalesList: this.containers.annualVolumeOfSalesContainer.locator(this.locators.dropdownListItem),
        averageTransactionSizeList: this.containers.averageTransactionSizeContainer.locator(this.locators.dropdownListItem),
        highestTransactionSizeList: this.containers.highestTransactionSizeContainer.locator(this.locators.dropdownListItem),
    };

    businessAddress = {
        streetAddress: this.page.locator('//*[@data-testid="Street Address"]//input >> nth=0'), // create unique id for business address
        city: this.page.locator('//*[@data-testid="City"]//input >> nth=0'),
        zipCode: this.page.locator('//*[@data-testid="Zip Code"]//input >> nth=0'),
        businessPhoneNumber: this.page.locator('//*[@data-testid="Business Phone Number"]//input'),

        stateField: this.page.locator(`${this.locators.dropdownField} >> nth=8`),
    };

    businessOwnerInformation = {
        percentageOfBusiness: this.page.locator('//*[@data-testid="What % of the business do they own?"]//input'),
        streetAddress: this.page.locator('//*[@data-testid="Street Address"]//input'),
        city: this.page.locator('//*[@data-testid="City"]//input'),
        zipCode: this.page.locator('//*[@data-testid="Zip Code"]//input'),
        dateOfBirth: this.page.locator('//*[@data-testid="Date of Birth"]//input'),
        socialSecurityNumber: this.page.locator('//*[@data-testid="Social security number"]//input'),
        firstName: this.page.locator('//*[@data-testid="First Name"]//input'),
        lastName: this.page.locator('//*[@data-testid="Last Name"]//input'),
        emailAddress: this.page.locator('//*[@data-testid="Email Address"]//input'),
        phoneNumber: this.page.locator('//*[@data-testid="Cell Phone Number"]//input'),

        titleOfOwnerField: this.page.locator(`${this.locators.dropdownField} >> nth=8`),
        titleOfOwnerList: this.containers.titleOfOwnerContainer.locator(this.locators.dropdownListItem),
        stateField: this.page.locator(`${this.locators.dropdownField} >> nth=9`),
        howDidYourFindUsField: this.page.locator(`${this.locators.dropdownField} >> nth=10`),
        howDidYourFindUsList: this.page.locator('"Suppliers"'), // add locator
    };

    bankInformation = {
        nameOnBankAccount: this.page.locator('//*[@data-testid="Name on the bank account"]//input'),
        accountNumber: this.page.locator('//*[@data-testid="Enter business checking/savings account number"]//input'),
        routingNumber: this.page.locator('//*[@data-testid="Enter routing number"]//input'),

        searchYourBank: this.page.locator('[placeholder="Search your bank"]'),
        searchYourBankList: this.page.locator('.r-edyy15'),

        enterBankManually: this.page.locator('text="Enter bank details manually"'),
        enterBankManuallyModal: this.page.locator('"Enter Bank Manually"'),
    };

    async fillUpBusinessDetails(legalBusinessName, businessStartDate, businessTaxID){
        await this.page.waitForTimeout(1000); // checklater
        await this.businessDetails.legalBusinessName.fill(legalBusinessName);
        await this.businessDetails.businessStartDate.fill(businessStartDate);
        await this.businessDetails.businessTypeField.click();
        await this.businessDetails.businessTypeList.first().click();
        await this.businessDetails.businessCategoryField.click();
        await this.businessDetails.businessCategoryList.first().click();
        await this.businessDetails.refundPolicyField.click();
        await this.businessDetails.refundPolicyList.first().click();
        await this.businessDetails.annualVolumeOfSalesField.click();
        await this.businessDetails.annualVolumeOfSalesList.first().click();
        await this.businessDetails.averageTransactionSizeField.click();
        await this.businessDetails.averageTransactionSizeList.first().click();
        await this.businessDetails.highestTransactionSizeField.click();
        await this.businessDetails.highestTransactionSizeList.first().click();
        await this.businessDetails.businessTaxID.fill(businessTaxID);
    };

    async fillUpBusinessDetailsCustomer(){
        await this.page.waitForTimeout(1000); // checklater
        await this.businessDetails.legalBusinessName.fill('autotester');
        await this.buttons.next.click();
        await this.page.locator('"No"').click();
    };

    async fillUpBusinessAddress(streetAddress, city, zipCode, state, cellPhoneNumber){
        await this.businessAddress.streetAddress.fill(streetAddress);
        await this.businessAddress.city.fill(city);
        await this.businessAddress.zipCode.fill(zipCode);
        await this.businessAddress.stateField.click(); 
        await this.page.locator(state).first().click();
        await this.businessAddress.businessPhoneNumber.fill(cellPhoneNumber);
    };

    async fillUpBusinessAddressCustomer(){
        await this.businessAddress.streetAddress.fill('test');
        await this.businessAddress.city.fill('test');
        await this.businessAddress.zipCode.fill('224444');
        await this.businessAddress.stateField.click(); 
        await this.page.locator('alabama').first().click();
        await this.buttons.next.click();
        await this.businessAddress.businessPhoneNumber.fill('2025555555');
        await this.buttons.next.click();
        await this.page.locator('"LLC"').click();
        await this.businessDetails.businessTaxID.fill('*********');
        await this.buttons.next.click();
        await this.businessDetails.businessStartDate.fill('022022');
        await this.buttons.next.click();
        await this.page.locator('"CEO"').click();
        await this.page.locator('"Yes"').click();
        await this.page.locator('"Yes"').click();
        await this.businessOwnerInformation.percentageOfBusiness.fill('100');
        await this.buttons.next.click();
        await this.businessOwnerInformation.dateOfBirth.fill('02021988');
        await this.buttons.next.click();
        //await this.businessAddress.businessPhoneNumber.fill(cellPhoneNumber);
    };

    async fillUpBusinessOwnerInformation(percentOfBusiness, streetAddress, city, zipCode, state, dateOfBirth, socialSecurityNumber){
        await this.businessOwnerInformation.percentageOfBusiness.fill(percentOfBusiness);
        await this.businessOwnerInformation.titleOfOwnerField.click();
        await this.businessOwnerInformation.titleOfOwnerList.first().click();
        await this.businessOwnerInformation.streetAddress.fill(streetAddress);
        await this.businessOwnerInformation.city.fill(city);
        await this.businessOwnerInformation.zipCode.fill(zipCode);
        await this.businessOwnerInformation.stateField.click();
        await this.page.locator(state).first().click();
        await this.businessOwnerInformation.dateOfBirth.fill(dateOfBirth);
        await this.businessOwnerInformation.socialSecurityNumber.fill(socialSecurityNumber);
        await this.businessOwnerInformation.howDidYourFindUsField.click();
        await this.businessOwnerInformation.howDidYourFindUsList.click();
    };

    async fillUpBankInformation(bankName, accountName, accountNumber, routingNumber){
        await this.bankInformation.searchYourBank.fill(bankName);
        await this.bankInformation.searchYourBankList.first().click();
        await this.bankInformation.enterBankManually.click();
        await this.bankInformation.enterBankManuallyModal.click();
        await this.bankInformation.nameOnBankAccount.fill(accountName);
        await this.bankInformation.accountNumber.fill(accountNumber);
        await this.bankInformation.routingNumber.fill(routingNumber);
        await this.page.waitForTimeout(1000);// checklater
        await this.buttons.submitApplication.click();
    };
}
