import type { TFunction } from 'i18next'

import { InHouseCreditApplicationStatus } from '@/app/in-house-credit-application/_types'
import { StyledTag } from '@/components/common/StyledTag'
import { getInHouseCreditApplicationStatusTranslation } from '@/app/in-house-credit-application/_utils'

export const getInHouseCreditApplicationStatusTag = (
  status: InHouseCreditApplicationStatus,
  t: TFunction,
): JSX.Element => {
  const text = getInHouseCreditApplicationStatusTranslation(status, t)

  if (status === InHouseCreditApplicationStatus.IN_REVIEW) {
    return <StyledTag color="purple">{text}</StyledTag>
  }

  if (status === InHouseCreditApplicationStatus.REJECTED) {
    return <StyledTag color="red">{text}</StyledTag>
  }

  if (status === InHouseCreditApplicationStatus.SENT_BACK) {
    return <StyledTag color="blue">{text}</StyledTag>
  }

  if (status === InHouseCreditApplicationStatus.CANCELLED) {
    return <StyledTag color="gold">{text}</StyledTag>
  }

  if (status === InHouseCreditApplicationStatus.APPROVED) {
    return <StyledTag color="green">{text}</StyledTag>
  }

  return <>{status}</>
}
