@startuml Regular/Custom Draw Issue Process (.NET)

title Regular/Custom Draw Issue Process (.NET)

participant "OnBoarding Service" as obs #SkyBlue
queue "Draw Events Queue" as dq #PaleVioletRed
participant "Loan Flow Service" as lfs #SkyBlue
participant "1st parties" as other #LightGrey
participant "LMS" as lms #SkyBlue
queue "LMS Payments Queue" as lmsq #PaleVioletRed
queue "Payment Queue" as pq #PaleVioletRed
participant "Payment Domain Services" as payment #SkyBlue

autonumber

== Approve, issue and disburse draw ==

obs --> obs : Draw approved
obs -> dq : Place event\n""Draw.Approved""\nwith type\n""regular/custom""
dq -> lfs : Consume draw events
lfs -> other : Get necessary details
other --> lfs
lfs --> lfs : Validates request
lfs -> lms : Create and start draw
lfs -> pq : Place payment request\n""CREATE.DRAW.DISBURSEMENT""
pq -> payment : Consume payment events
payment --> payment : Create necessary transactions\n(not detailed here)
payment --> lmsq : Notify LMS about disbursement success
lmsq -> lms : Consume LMS events

@enduml