@startuml

title KYC step

participant "Previous Step" as ps #LightGray
participant "KYC" as kyc #LightGray
participant "OnBoarding\nService" as onbs #SkyBlue
participant "UserService" as us #SkyBlue
database "Mongo" as db #SkyBlue
participant "LexisNexis\nintegration" as lni #LightGray
participant "LexisNexis" as ln #Orange

autonumber

ps -> kyc
== Event Wrapper ==
kyc -> onbs : Check loan application
kyc -> onbs : Create draft if not exists
kyc -> onbs : Updating progress
kyc -> onbs : Save previous outputs
== KYC step ==
kyc -> us : Read user and business owner data
kyc --> kyc : Map data to request
kyc -> lni : Gets emailage and fraudpoint for business owner
lni -> ln : Forwards request
ln --> lni
lni --> kyc

@enduml