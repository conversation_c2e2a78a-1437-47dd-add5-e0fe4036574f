name: Remove Serverless Infrastructure

on:
  workflow_dispatch:
    inputs:
      environment:
        type: environment
        description: Select the environment
        required: true

env:
  NUGET_PACKAGES_DIRECTORY: ".nuget"
  LP_AWS_ACCOUNT: ${{ secrets.AWS_ACCOUNT_ID }}
  AWS_REGION: us-west-1
  BRANCH: "${{ github.ref }}"
  AWS_ACCESS_KEY_ID: ${{ secrets.AWS_ACCESS_KEY_ID_DEV }}
  AWS_SECRET_ACCESS_KEY: ${{ secrets.AWS_SECRET_ACCESS_KEY_DEV }}
  STAGE: ${{ vars.STAGE }}
  ASPNETCORE_ENVIRONMENT: ${{ vars.STAGE }}
  LAMBDA_AION_REPORT_PACKAGE_LOCATION: ${{ github.run_id }}.zip
  API_KEY: ${{ secrets.API_KEY }}

jobs:
  remove:
    runs-on: ubuntu-latest

    steps:
      - name: Checkout code
        uses: actions/checkout@v4.1.1
        with:
          fetch-depth: 0

      - name: Use latest Node.js
        uses: actions/setup-node@v3.8.1
        with:
          node-version: latest

      - name: Install Serverless Framework
        run: npm install -g serverless

      - name: Remove Serverless infrastructure
        run: serverless remove --stage $STAGE --verbose
        working-directory: ./scripts/deploy
