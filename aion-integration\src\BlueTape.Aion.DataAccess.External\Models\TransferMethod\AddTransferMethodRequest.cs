﻿using System.Runtime.Serialization;
using System.Text.Json.Serialization;

namespace BlueTape.Aion.DataAccess.External.Models.TransferMethod;

[DataContract]
public class AddTransferMethodRequest
{
    [JsonPropertyName("counterpartyId")]
    public string CounterpartyId { get; set; } = null!;
    
    [JsonPropertyName("accountId")]
    public string AccountId { get; set; } = null!;
    
    [JsonPropertyName("nickName")]
    public string NickName { get; set; } = null!;
    
    [JsonPropertyName("bankDetail")]
    public BankDetail BankDetail { get; set; } = null!;
}