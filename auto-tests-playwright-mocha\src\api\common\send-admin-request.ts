import {request} from '@playwright/test';

require('dotenv').config();

export default async function sendAdminRequest(method: string, endpoint: string, token: string, body: any = null) {
    const url = `${process.env.ADMIN_BACKEND_URL}/${endpoint}`;
    const apiContext = await request.newContext();
    let response;
    try {
        switch (method) {
            case 'post':
                response = await apiContext.post(url,
                    {
                        headers: {authorization: `Bearer ${token}`},
                        data: body,
                    });
                break;
            case 'get':
                response = await apiContext.get(url,
                    {
                        headers: {authorization: `Bearer ${token}`}
                    });
                break;
        }
        if (response.status() === 200) {
            console.log(`\nRequest ${endpoint} was successful.\n`);
            const responseBody = await response.body();
            return responseBody;
        } else {
            throw new Error(`Request failed. Endpoint: ${endpoint} Status code: ${response.status()} ${response.statusText()}`);
        }
    } catch (error) {
        return (error);
    }
}
