@startuml

title Loan repayment event processing

participant "Payment Service\n(Linqpal)" as lp #LightGray
queue "Payment queue" as paysqs #Pink
queue "Loan queue" as loansqs #Pink
queue "Ledger queue" as ledgersqs #Pink
participant "Eventbridge" as eb #Orange
participant "Loan\nService" as ls #SkyBlue
participant "Ledger\nService" as ledger #SkyBlue

autonumber

== Successful payment ==

lp -> paysqs : loan.Repayment.Success
paysqs -> ls : Consume loan.Repayment.Success
ls --> ls : Connect payment\nwith installments

== Notify Payment Service ==

ls -> eb : loan.Repayment\nAcknowledged
eb -> loansqs
loansqs -> lp : Consume loan.Repayment.Acknowledged
lp --> lp : Continue processing

== Notify Ledger Service ==

eb -> ledgersqs
ledgersqs -> ledger: Consume\nloan.Repayment\nAcknowledged
ledger -> ls : Get loan\nrepayment details
ls --> ledger
ledger --> ledger : Store ledger\noperation items

@enduml