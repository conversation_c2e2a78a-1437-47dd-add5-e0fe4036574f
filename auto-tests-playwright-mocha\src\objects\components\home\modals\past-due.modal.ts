import {BasePage} from '../../../base.page';

export class PastDueModal extends BasePage {
    constructor(page){
        super(page);
    };

    containers = {
        mainContainer: this.page.locator('_react=[key=".0"]'),
    };

    buttons = {
        pastDueModalCloseButton: this
            .containers
            .mainContainer
            .locator("//div[@class='css-1dbjc4n r-1awozwy r-ilb8sh r-1xfd6ze r-1loqt21 r-eu3ka r-1777fci r-1otgn73 r-1i6wzkk r-lrvibr r-1aockid']")
    };
}
