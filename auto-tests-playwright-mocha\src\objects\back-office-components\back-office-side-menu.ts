import {Page} from '@playwright/test';
import {BasePage} from '../base.page';

export class BackOfficeSideMenu extends BasePage {
    constructor(page: Page) {
        super(page);
    };

    private containers = {
        sideBarMenuContainer: this.page.locator('.c-sidebar-fixed'),
    };

    sideMenuTabs = {
        supplierApplication: this.containers.sideBarMenuContainer.locator('"Supplier Applications"'),
        loanApplications: this.containers.sideBarMenuContainer.locator('"Loan Applications"'),
        loans: this.containers.sideBarMenuContainer.locator('"Loans"'),
        payments: this.containers.sideBarMenuContainer.locator('"Payments"'),

        companies: {
            suppliers: this.page.getByRole('link', {name: 'Suppliers'}),
        },
    };

    async clickOnSupplierButton() {
        await this.sideMenuTabs.companies.suppliers.click();
    }

    async clickOnLoanApplicationsButton() {
        await this.sideMenuTabs.loanApplications.click();
    }
}
