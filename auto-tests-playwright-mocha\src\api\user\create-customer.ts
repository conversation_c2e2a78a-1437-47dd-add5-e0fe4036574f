import sendUserRequest from '../common/send-user-request';

export default async function createCustomer(session: string, challenge: string, email: string, firstName: string,
    lastName: string, companyName: string, phoneNumber: string) {
    const endpoint = `v1/supplier/account`;
    const body = {
        "account": {
            "email": `${email}`,
            "first_name": `${firstName}`,
            "last_name": `${lastName}`,
            "name": `${companyName}`,
            "phone": `${phoneNumber}`,
            "status": "SAVED"
        },
        "sendInvite": "false"
    };
    try {
        await sendUserRequest('post', endpoint, session, challenge, body);
    } catch (error) {
        console.log(error);
        return error;
    }
}