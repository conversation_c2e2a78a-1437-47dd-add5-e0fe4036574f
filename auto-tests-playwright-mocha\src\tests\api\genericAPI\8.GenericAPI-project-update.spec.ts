import {expect} from '@playwright/test';
import {test} from '../../test-utils';
import {createProject, updateProject} from '../../../api/common/send-generic-api-request';
import {BaseTest} from '../../test-utils';
import {changeDateByDayISO} from '../../../utils/change-date';

const error = JSON.parse(JSON.stringify(require('../../../constants/generic-api-errors.json')));

test.describe(`Project tests @generic @API`, async () => {

    let projectName: string;
    let projectId: string;
    let projectAddress: string;
    let creditAmount: number;
    let dateDayAgoISO: string;

    test.beforeAll(async () => {
        projectName = `projectName${BaseTest.dateTimePrefix()}`;
        projectId = `projectId${BaseTest.dateTimePrefix()}`;
        projectAddress = `projectAddress${BaseTest.dateTimePrefix()}`;
        creditAmount = Math.floor(Math.random() * 10);
        dateDayAgoISO = await changeDateByDayISO(-1);
        const projectData = {
            projectId
        };
        const response = await createProject(projectData);
    });

    test(`Cannot update Project with Contract Amount = -1`, async () => {
        const projectData = {
            contractAmount: -1,
        };
        const response = await updateProject(projectData, projectId);
        expect(response.status, `Status code 400`).toEqual(400);
        expect(response.data, `Response contains Object`).toEqual(expect.any(Object));
        expect(response.data[0].code, `Error message: ${error.common.invalidData}`).toEqual(error.common.invalidData);
        expect(response.data[0].reason, `Error message: ${error.project.reason.invalidContractAmount}`).toEqual(error.project.reason.invalidContractAmount);
    });

    test(`Cannot update Project with Contract Amount = null`, async () => {
        const projectData = {
            contractAmount: null,
        };
        const response = await updateProject(projectData, projectId);
        expect(response.status, `Status code 400`).toEqual(400);
        expect(response.data, `Response contains Object`).toEqual(expect.any(Object));
        expect(response.data[0].code, `Error message: ${error.project.code.invalidDataFormat}`).toEqual(error.project.code.invalidDataFormat);
        expect(response.data[0].reason, `Error message: ${error.project.reason.invalidDataFormat}`).toEqual(error.project.reason.invalidDataFormat);
    });

    test(`Cannot update Project with Contract Amount = string`, async () => {
        const projectData = {
            contractAmount: 'aaa',
        };
        const response = await updateProject(projectData, projectId);
        expect(response.status, `Status code 400`).toEqual(400);
        expect(response.data, `Response contains Object`).toEqual(expect.any(Object));
        expect(response.data[0].code, `Error message: ${error.project.code.invalidDataFormat}`).toEqual(error.project.code.invalidDataFormat);
    });

    test(`Cannot update Project with Start Date = null`, async () => {
        const projectData = {
            startDate: null,
        };
        const response = await updateProject(projectData, projectId);
        expect(response.status, `Status code 400`).toEqual(400);
        expect(response.data, `Response contains Object`).toEqual(expect.any(Object));
        expect(response.data[0].code, `Error message: ${error.project.code.invalidDataFormat}`).toEqual(error.project.code.invalidDataFormat);
        expect(response.data[0].reason, `Error message: ${error.project.reason.invalidDataFormat}`).toEqual(error.project.reason.invalidDataFormat);
    });

    test(`Cannot update Project with empty Start Date`, async () => {
        const projectData = {
            startDate: ' ',
        };
        const response = await updateProject(projectData, projectId);
        expect(response.status, `Status code 400`).toEqual(400);
        expect(response.data, `Response contains Object`).toEqual(expect.any(Object));
        expect(response.data[0].code, `Error message: ${error.project.code.invalidDataFormat}`).toEqual(error.project.code.invalidDataFormat);
        expect(response.data[0].reason, `Error message: ${error.project.reason.invalidDataFormat}`).toEqual(error.project.reason.invalidDataFormat);
    });

    test(`Cannot update Project with End Date = null`, async () => {
        const projectData = {
            endDate: null,
        };
        const response = await updateProject(projectData, projectId);
        expect(response.status, `Status code 400`).toEqual(400);
        expect(response.data, `Response contains Object`).toEqual(expect.any(Object));
        expect(response.data[0].code, `Error message: ${error.project.code.invalidDataFormat}`).toEqual(error.project.code.invalidDataFormat);
        expect(response.data[0].reason, `Error message: ${error.project.reason.invalidDataFormat}`).toEqual(error.project.reason.invalidDataFormat);
    });

    test(`Cannot update Project with empty End Date`, async () => {
        const projectData = {
            endDate: ' ',
        };
        const response = await updateProject(projectData, projectId);
        expect(response.status, `Status code 400`).toEqual(400);
        expect(response.data, `Response contains Object`).toEqual(expect.any(Object));
        expect(response.data[0].code, `Error message: ${error.project.code.invalidDataFormat}`).toEqual(error.project.code.invalidDataFormat);
        expect(response.data[0].reason, `Error message: ${error.project.reason.invalidDataFormat}`).toEqual(error.project.reason.invalidDataFormat);
    });

    test(`Cannot update Project with End Date = day ago`, async () => {
        const projectData = {
            endDate: dateDayAgoISO,
        };
        const response = await updateProject(projectData, projectId);
        expect(response.status, `Status code 400`).toEqual(400);
        expect(response.data, `Response contains Object`).toEqual(expect.any(Object));
        expect(response.data[0].code, `Error message: ${error.common.invalidData}`).toEqual(error.common.invalidData);
    });

    test(`Cannot update Project with Role = null`, async () => {
        const projectData = {
            role: null,
        };
        const response = await updateProject(projectData, projectId);
        expect(response.status, `Status code 400`).toEqual(400);
        expect(response.data, `Response contains Object`).toEqual(expect.any(Object));
        expect(response.data[0].code, `Error message: ${error.project.code.invalidDataFormat}`).toEqual(error.project.code.invalidDataFormat);
        expect(response.data[0].reason, `Error message: ${error.project.reason.invalidDataFormat}`).toEqual(error.project.reason.invalidDataFormat);
    });

    test(`Cannot update empty Project with Role `, async () => {
        const projectData = {
            role: ' ',
        };
        const response = await updateProject(projectData, projectId);
        expect(response.status, `Status code 400`).toEqual(400);
        expect(response.data, `Response contains Object`).toEqual(expect.any(Object));
        expect(response.data[0].code, `Error message: ${error.project.code.invalidDataFormat}`).toEqual(error.project.code.invalidDataFormat);
        expect(response.data[0].reason, `Error message: ${error.project.reason.invalidDataFormat}`).toEqual(error.project.reason.invalidDataFormat);
    });

    test(`Cannot update Project with Type = null`, async () => {
        const projectData = {
            type: null,
        };
        const response = await updateProject(projectData, projectId);
        expect(response.status, `Status code 400`).toEqual(400);
        expect(response.data, `Response contains Object`).toEqual(expect.any(Object));
        expect(response.data[0].code, `Error message: ${error.project.code.invalidDataFormat}`).toEqual(error.project.code.invalidDataFormat);
        expect(response.data[0].reason, `Error message: ${error.project.reason.invalidDataFormat}`).toEqual(error.project.reason.invalidDataFormat);
    });

    test(`Cannot update empty Project with Type `, async () => {
        const projectData = {
            type: ' ',
        };
        const response = await updateProject(projectData, projectId);
        expect(response.status, `Status code 400`).toEqual(400);
        expect(response.data, `Response contains Object`).toEqual(expect.any(Object));
        expect(response.data[0].code, `Error message: ${error.project.code.invalidDataFormat}`).toEqual(error.project.code.invalidDataFormat);
        expect(response.data[0].reason, `Error message: ${error.project.reason.invalidDataFormat}`).toEqual(error.project.reason.invalidDataFormat);
    });

    test(`Cannot update Project with Type = Default `, async () => {
        const projectData = {
            type: 'Default',
        };
        const response = await updateProject(projectData, projectId);
        expect(response.status, `Status code 400`).toEqual(400);
        expect(response.data, `Response contains Object`).toEqual(expect.any(Object));
        expect(response.data[0].code, `Error message: ${error.common.invalidData}`).toEqual(error.common.invalidData);
        expect(response.data[0].reason, `Error message: ${error.project.reason.invalidType}`).toEqual(error.project.reason.invalidType);
    });

    test(`Cannot update Project with privateProjectDetails = null`, async () => {
        const projectData = {
            privateProjectDetails: null,
        };
        const response = await updateProject(projectData, projectId);
        expect(response.status, `Status code 400`).toEqual(400);
        expect(response.data, `Response contains Object`).toEqual(expect.any(Object));
        expect(response.data[0].code, `Error message: ${error.common.invalidData}`).toEqual(error.common.invalidData);
        expect(response.data[0].reason, `Error message: ${error.project.reason.nullInPrivateProjectDetails}`).toEqual(error.project.reason.nullInPrivateProjectDetails);
    });

    test(`Cannot update Project with Job Address = null`, async () => {
        const projectData = {
            jobAddress: null,
        };
        const response = await updateProject(projectData, projectId);
        expect(response.status, `Status code 400`).toEqual(400);
        expect(response.data, `Response contains Object`).toEqual(expect.any(Object));
        expect(response.data[0].code, `Error message: ${error.project.code.invalidDataFormat}`).toEqual(error.project.code.invalidDataFormat);
        expect(response.data[0].reason, `Error message: ${error.project.reason.nullInAddressField}`).toEqual(error.project.reason.nullInAddressField);
    });

    test(`Cannot update Project with Job State = null`, async () => {
        const projectData = {
            jobState: null,
        };
        const response = await updateProject(projectData, projectId);
        expect(response.status, `Status code 400`).toEqual(400);
        expect(response.data, `Response contains Object`).toEqual(expect.any(Object));
        expect(response.data[0].code, `Error message: ${error.project.code.invalidDataFormat}`).toEqual(error.project.code.invalidDataFormat);
        expect(response.data[0].reason, `Error message: ${error.project.reason.nullInJobState}`).toEqual(error.project.reason.nullInJobState);
    });

    test(`Cannot update Project with Job Zip Code = null`, async () => {
        const projectData = {
            jobZipCode: null,
        };
        const response = await updateProject(projectData, projectId);
        expect(response.status, `Status code 400`).toEqual(400);
        expect(response.data, `Response contains Object`).toEqual(expect.any(Object));
        expect(response.data[0].code, `Error message: ${error.project.code.invalidDataFormat}`).toEqual(error.project.code.invalidDataFormat);
        expect(response.data[0].reason, `Error message: ${error.project.reason.nullInJobZipCode}`).toEqual(error.project.reason.nullInJobZipCode);
    });

    test(`Cannot update Project with Job Zip Code less than 5 characters `, async () => {
        const projectData = {
            jobZipCode: '1234',
        };
        const response = await updateProject(projectData, projectId);
        expect(response.status, `Status code 400`).toEqual(400);
        expect(response.data, `Response contains Object`).toEqual(expect.any(Object));
        expect(response.data[0].code, `Error message: ${error.common.invalidData}`).toEqual(error.common.invalidData);
    });

    test(`Cannot update Project with Job Zip Code more than 5 characters `, async () => {
        const projectData = {
            jobZipCode: '123456',
        };
        const response = await updateProject(projectData, projectId);
        expect(response.status, `Status code 400`).toEqual(400);
        expect(response.data, `Response contains Object`).toEqual(expect.any(Object));
        expect(response.data[0].code, `Error message: ${error.common.invalidData}`).toEqual(error.common.invalidData);
    });

    test(`Cannot update Project with Job City = null`, async () => {
        const projectData = {
            jobCity: null,
        };
        const response = await updateProject(projectData, projectId);
        expect(response.status, `Status code 400`).toEqual(400);
        expect(response.data, `Response contains Object`).toEqual(expect.any(Object));
        expect(response.data[0].code, `Error message: ${error.project.code.invalidDataFormat}`).toEqual(error.project.code.invalidDataFormat);
        expect(response.data[0].reason, `Error message: ${error.project.reason.nullInJobCity}`).toEqual(error.project.reason.nullInJobCity);
    });

    test(`Cannot update Project without phone number in individual owner`, async () => {
        const projectData = {
            individualPhone: null,
        };
        const response = await updateProject(projectData, projectId);
        expect(response.status, `Status code 400`).toEqual(400);
        expect(response.data, `Response contains Object`).toEqual(expect.any(Object));
        expect(response.data[0].code, `Error message: ${error.project.code.invalidDataFormat}`).toEqual(error.project.code.invalidDataFormat);
    });

    test(`Cannot update Project with non american phone number`, async () => {
        const projectData = {
            individualPhone: '888555888',
        };
        const response = await updateProject(projectData, projectId);
        expect(response.status, `Status code 400`).toEqual(400);
        expect(response.data, `Response contains Object`).toEqual(expect.any(Object));
        expect(response.data[0].code, `Error message: ${error.common.invalidData}`).toEqual(error.common.invalidData);
    });

    test(`Cannot update Project without first name in individual owner`, async () => {
        const projectData = {
            individualFristName: null,
        };
        const response = await updateProject(projectData, projectId);
        expect(response.status, `Status code 400`).toEqual(400);
        expect(response.data, `Response contains Object`).toEqual(expect.any(Object));
        expect(response.data[0].code, `Error message: ${error.project.code.invalidDataFormat}`).toEqual(error.project.code.invalidDataFormat);
    });

    test(`Cannot update Project without last name in individual owner`, async () => {
        const projectData = {
            individualLastName: null,
        };
        const response = await updateProject(projectData, projectId);
        expect(response.status, `Status code 400`).toEqual(400);
        expect(response.data, `Response contains Object`).toEqual(expect.any(Object));
        expect(response.data[0].code, `Error message: ${error.project.code.invalidDataFormat}`).toEqual(error.project.code.invalidDataFormat);
    });

    test(`Cannot update Project without home address in individual owner`, async () => {
        const projectData = {
            individualHomeAddress: null,
        };
        const response = await updateProject(projectData, projectId);
        expect(response.status, `Status code 400`).toEqual(400);
        expect(response.data, `Response contains Object`).toEqual(expect.any(Object));
        expect(response.data[0].code, `Error message: ${error.project.code.invalidDataFormat}`).toEqual(error.project.code.invalidDataFormat);
    });

    test(`Cannot update Project without state in individual owner`, async () => {
        const projectData = {
            individualState: null,
        };
        const response = await updateProject(projectData, projectId);
        expect(response.status, `Status code 400`).toEqual(400);
        expect(response.data, `Response contains Object`).toEqual(expect.any(Object));
        expect(response.data[0].code, `Error message: ${error.project.code.invalidDataFormat}`).toEqual(error.project.code.invalidDataFormat);
    });

    test(`Cannot update Project without city in individual owner`, async () => {
        const projectData = {
            individualCity: null,
        };
        const response = await updateProject(projectData, projectId);
        expect(response.status, `Status code 400`).toEqual(400);
        expect(response.data, `Response contains Object`).toEqual(expect.any(Object));
        expect(response.data[0].code, `Error message: ${error.project.code.invalidDataFormat}`).toEqual(error.project.code.invalidDataFormat);
    });

    test(`Cannot update Project without zip code in individual owner`, async () => {
        const projectData = {
            individualZipCode: null,
        };
        const response = await updateProject(projectData, projectId);
        expect(response.status, `Status code 400`).toEqual(400);
        expect(response.data, `Response contains Object`).toEqual(expect.any(Object));
        expect(response.data[0].code, `Error message: ${error.project.code.invalidDataFormat}`).toEqual(error.project.code.invalidDataFormat);
    });

    test(`Update project with valid data`, async () => {
        const projectData = {};
        const response = await updateProject(projectData, projectId);
        expect(response.status, `Status code 202`).toEqual(202);
        expect(response.data, `Response contains Object`).toEqual(expect.any(Object));
    });

    test(`Update project from individual to business`, async () => {
        const projectData = {
            businessFristName: 'Business',
        };
        const response = await updateProject(projectData, projectId);
        expect(response.status, `Status code 202`).toEqual(202);
        expect(response.data, `Response contains Object`).toEqual(expect.any(Object));
    });

    // test(`Update Project Name`, async () => {
    //     projectName = `projectName${BaseTest.dateTimePrefix()}`;
    //     const projectData = {
    //         projectName,
    //         projectId,
    //         projectAddress,
    //         creditAmount,
    //         sourceModifiedDate,
    //     };
    //     const response = await updateProject(projectData);
    //     expect(response.status, `Status code 202`).toEqual(202);
    //     expect(response.data, `Response contains Object`).toEqual(expect.any(Object));
    // });

    // test(`Update Project Address`, async () => {
    //     projectAddress = `projectAddress${BaseTest.dateTimePrefix()}`;
    //     const projectData = {
    //         projectName,
    //         projectId,
    //         projectAddress,
    //         creditAmount,
    //         sourceModifiedDate,
    //     };
    //     const response = await updateProject(projectData);
    //     expect(response.status, `Status code 202`).toEqual(202);
    //     expect(response.data, `Response contains Object`).toEqual(expect.any(Object));
    // });

    // test(`Update Credit Amount`, async () => {
    //     creditAmount = Math.floor(Math.random() * 10);
    //     const projectData = {
    //         projectName,
    //         projectId,
    //         projectAddress,
    //         creditAmount,
    //         sourceModifiedDate,
    //     };
    //     const response = await updateProject(projectData);
    //     expect(response.status, `Status code 202`).toEqual(202);
    //     expect(response.data, `Response contains Object`).toEqual(expect.any(Object));
    // });

    // test(`Cannot update Project with Name = null`, async () => {
    //     const projectData = {
    //         projectName: null,
    //         projectId,
    //         projectAddress,
    //         creditAmount,
    //         sourceModifiedDate,
    //     };
    //     const response = await updateProject(projectData);
    //     expect(response.status, `Status code 400`).toEqual(400);
    //     expect(response.data, `Response contains Object`).toEqual(expect.any(Object));
    //     expect(response.data[0].code, `Error message: ${error.project.code.invalidDataFormat}`).toEqual(error.project.code.invalidDataFormat);
    //     expect(response.data[0].reason, `Error message: ${error.project.reason.nullInNameField}`).toEqual(error.project.reason.nullInNameField);
    // });

    // test(`Cannot update Project with empty value in the Name field`, async () => {
    //     const projectData = {
    //         projectName: '',
    //         projectId,
    //         projectAddress,
    //         creditAmount,
    //         sourceModifiedDate,
    //     };
    //     const response = await updateProject(projectData);
    //     expect(response.status, `Status code 400`).toEqual(400);
    //     expect(response.data, `Response contains Object`).toEqual(expect.any(Object));
    //     expect(response.data[0].code, `Error message: ${error.project.code.emptyValue}`).toEqual(error.project.code.emptyValue);
    //     expect(response.data[0].reason, `Error message: ${error.project.reason.emptyNameField}`).toEqual(error.project.reason.emptyNameField);
    // });

    // test(`Cannot update Project with non-existent ID`, async () => {
    //     const projectData = {
    //         projectName,
    //         projectId: "UnicProjectId123123",
    //         projectAddress,
    //         creditAmount,
    //         sourceModifiedDate,
    //     };
    //     const response = await updateProject(projectData);
    //     expect(response.status, `Status code 400`).toEqual(400);
    //     expect(response.data, `Response contains Object`).toEqual(expect.any(Object));
    //     expect(response.data[0].code, `Error message: ${error.project.code.invalidId}`).toEqual(error.project.code.invalidId);
    //     expect(response.data[0].reason, `Error message: ${error.project.reason.invalidId}`).toEqual(error.project.reason.invalidId);
    // });

    // test(`Cannot update Project with Address = null`, async () => {
    //     const projectData = {
    //         projectName,
    //         projectId,
    //         projectAddress: null,
    //         creditAmount,
    //         sourceModifiedDate,
    //     };
    //     const response = await updateProject(projectData);
    //     expect(response.status, `Status code 400`).toEqual(400);
    //     expect(response.data, `Response contains Object`).toEqual(expect.any(Object));
    //     expect(response.data[0].code, `Error message: ${error.project.code.invalidDataFormat}`).toEqual(error.project.code.invalidDataFormat);
    //     expect(response.data[0].reason, `Error message: ${error.project.reason.nullInAddressField}`).toEqual(error.project.reason.nullInAddressField);
    // });

    // test(`Cannot update Project with empty value in the Address field`, async () => {
    //     const projectData = {
    //         projectName,
    //         projectId,
    //         projectAddress: '',
    //         creditAmount,
    //         sourceModifiedDate,
    //     };
    //     const response = await updateProject(projectData);
    //     expect(response.status, `Status code 400`).toEqual(400);
    //     expect(response.data, `Response contains Object`).toEqual(expect.any(Object));
    //     expect(response.data[0].code, `Error message: ${error.project.code.emptyValue}`).toEqual(error.project.code.emptyValue);
    //     expect(response.data[0].reason, `Error message: ${error.project.reason.emptyAddressField}`).toEqual(error.project.reason.emptyAddressField);
    // });

    // test(`Cannot update Project with Credit Amount = -1`, async () => {
    //     const projectData = {
    //         projectName,
    //         projectId,
    //         projectAddress,
    //         creditAmount: -1,
    //         sourceModifiedDate,
    //     };
    //     const response = await updateProject(projectData);
    //     expect(response.status, `Status code 400`).toEqual(400);
    //     expect(response.data, `Response contains Object`).toEqual(expect.any(Object));
    //     expect(response.data[0].code, `Error message: ${error.project.code.invalidAmount}`).toEqual(error.project.code.invalidAmount);
    //     expect(response.data[0].reason, `Error message: ${error.project.reason.invalidCreditAmount}`).toEqual(error.project.reason.invalidCreditAmount);
    // });
});
