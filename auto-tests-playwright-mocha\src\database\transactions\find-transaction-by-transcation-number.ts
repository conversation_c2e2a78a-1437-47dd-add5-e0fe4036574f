import {client} from "../client";

export async function findTranscationByTransactionNumber(transactionNumber) {
    const database = client.db(`${process.env.CI_ENVIRONMENT_URL == 'https://dev.bluetape.com' ? 'dev' : 'beta'}`);
    const transactions = database.collection('transactions');

    const query = {"metadata.transactionNumber": `${transactionNumber}`};

    const transaction = await transactions.findOne(query);
    return transaction;
}
