openapi: "3.0.0"
info:
  version: "0.0.1"
  title: "Backoffice Backend for Frontend API definition design"
  description: |
    Backoffice Backend for Frontend API definition design
servers:
  - url: TBD-Dev
    description: Development server
  - url: TBD-Prod
    description: Production server
paths:
  /creditApplications:
    get: 
      tags:
        - creditApplications
      summary: Gets paginated creditapplications
      description: Gets paginated creditapplications
      operationId: getCreditApplications
      parameters: 
        - name: name
          description: The business name
          in: query
          required: false
          schema:
            type: string
          example: Acme Inc.
        - name: appDateFrom
          description: The credit application date from
          in: query
          required: false
          schema:
            type: string
            format: date
          example: 2024-04-19
        - name: appDateTo
          description: The credit application date to
          in: query
          required: false
          schema:
            type: string
            format: date
          example: 2024-04-20
        - name: status
          description: The status, inReview means new/processing/processed/executionFailed
          in: query
          required: false
          schema:
            type: string
            enum:
              - inReview
              - approved
              - rejected
              - canceled
              - sentBack
          example: inReview
        - name: automatedDecision
          description: The automated decision
          in: query
          required: false
          schema:
            type: string
            enum:
              - pass
              - softFail
              - hardFail
          example: pass
        - name: category
          description: The business category
          in: query
          required: false
          schema:
            type: string
            enum:
              - subContractor
              - generalContractor
              - dealerRetailerSupplier
              - manufacturerDistributor
              - architectInteriorDesigner
              - engineerConsultant
              - developerPropertyOwner
              - other
          example: subContractor
        - name: sortBy
          description: Sort by
          in: query
          required: false
          schema:
            type: string
            enum:
              - submissionDate
              - requestedAmount
              - decisionDate
          example: submissionDate
        - name: sortOrder
          description: The sort order to use, default asc
          in: query
          required: false
          schema:
            type: string
            enum:
              - asc
              - desc
          example: asc
        - name: pageNumber
          description: The page number
          in: query
          required: false
          schema:
            type: number
            format: int64
          example: 5
        - name: pageSize
          description: The page size
          in: query
          required: false
          schema:
            type: number
            format: int64
          example: 50
      responses: 
        200:
          description: The credit applications
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/PagedCreditApplication'
        400:
          description: Bad request
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
  /creditApplications/{applicationId}/detailed:
    get: 
      tags:
        - creditApplications
      summary: Gets a creditapplication detailed
      description: Gets a creditapplication detailed
      operationId: getCreditApplicationsDetailed
      parameters:
        - name: applicationId
          description: The credit application id
          in: path
          required: true
          schema:
            type: string
          example: a5ba58e38e2f281a
      responses:
        200:
          description: The credit application with evaluation results
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/CreditApplicationDetailed'
        400:
          description: Bad request
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
  /creditApplications/{applicationId}/cashflow:
    get: 
      tags:
        - creditApplications
      summary: Gets creditapplication cash flow
      description: Gets creditapplication cash flow
      operationId: getCreditApplicationsCashFlow
      parameters:
        - name: applicationId
          description: The credit application id
          in: path
          required: true
          schema:
            type: string
          example: a5ba58e38e2f281a
      responses:
        200:
          description: The credit application cash flow
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/CashFlowItem'
        400:
          description: Bad request
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
  /creditApplications/{applicationId}/review:
    patch:
      tags: 
        - creditApplications
      summary: Approves CrApp, updates limits and details of credit application
      description: Approves CrApp, updates limits and details of credit application
      operationId: updateCreditApplicationDetails
      parameters:
        - name: applicationId
          description: The credit application id
          in: path
          required: true
          schema:
            type: string
          example: a5ba58e38e2f281a
        - name: userId
          description: The user reviewed
          in: header
          required: true
          schema:
            type: string
          example: 8064684bc4dba83d
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/UpdateCreditApplicationDetails'
      responses:
        200:
          description: The credit application
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/CreditApplication'
        400:
          description: Bad request
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
  /creditApplications/{applicationId}/notes:
    get:
      tags:
        - creditApplicationNotes
      summary: Gets creditapplication notes
      description: Gets creditapplication notes
      operationId: getCreditApplicationNotes
      parameters:
        - name: applicationId
          description: The credit application id
          in: path
          required: true
          schema:
            type: string
          example: a5ba58e38e2f281a
      responses:
        200:
          description: The credit application notes
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/NoteItem'
        400:
          description: Bad request
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
    post:
      tags:
        - creditApplicationNotes
      summary: Adds a creditapplication note
      description: Adds a creditapplication note
      operationId: createCreditApplicationNote
      parameters:
        - name: applicationId
          description: The credit application id
          in: path
          required: true
          schema:
            type: string
          example: a5ba58e38e2f281a
        - name: userId
          description: The user who created the note
          in: header
          required: true
          schema:
            type: string
          example: 8064684bc4dba83d
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/AddCreditApplicationNote'
      responses:
        201:
          description: The note which was created
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/NoteItem'
        400:
          description: Bad request
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
  /drawApprovals:
    get:
      tags: 
        - drawApprovals
      summary: Gets draw approvals
      description: Gets draw approvals
      operationId: getDrawApprovals
      parameters: 
        - name: id
          description: The draw approval id
          in: query
          required: false
          schema:
            type: string
          example: b38a1981616a348f
        - name: companyId
          description: The company id
          in: query
          required: false
          schema:
            type: string
          example: 7b6df64f-709f-4f87-84b7-f694cf3ec7a8
        - name: name
          description: The company name
          in: query
          required: false
          schema:
            type: string
          example: Acme Trading and Marketing Inc.
        - name: companyDba
          description: The company dba
          in: query
          required: false
          schema:
            type: string
          example: Acme Inc.
        - name: status
          description: The status
          in: query
          required: false
          schema:
            type: string
            enum:
              - inReview
              - approved
              - rejected
              - canceled
          example: inReview
        - name: type
          description: The draw type
          in: query
          required: false
          schema:
            type: string
            enum:
              - regular
              - virtualCard
              - noSupplier
              - express
              - quote
          example: regular
        - name: typeStr
          description: The draw type by string
          in: query
          required: false
          schema:
            type: string
          example: VC
        - name: automatedDecision
          description: The automated decision
          in: query
          required: false
          schema:
            type: string
            enum:
              - passed
              - softFailed
              - hardFailed
          example: passed
        - name: accountStatus
          description: The account status
          in: query
          required: false
          schema:
            type: string
            enum:
              - incomplete
              - underReview
              - underStipulation
              - goodStandingConnected
              - goodStandingManual
              - pastDue
              - onHold
              - inCollection
              - inactive
              - potentiallyFraud
              - closed
          example: goodStandingConnected
        - name: appDateFrom
          description: The application date from date
          in: query
          required: false
          schema:
            type: string
            format: date
          example: 2024-04-19
        - name: appDateTo
          description: The application date to date
          in: query
          required: false
          schema:
            type: string
            format: date
          example: 2024-04-20
        - name: merchantName
          description: The merchant name
          in: query
          required: false
          schema:
            type: string
          example: Best Lumber
        - name: invoiceNumber
          description: The invoice number
          in: query
          required: false
          schema:
            type: string
          example: IIV00011/2024
        - name: search
          description: Search for type, merchant name or invoice number
          in: query
          required: false
          schema:
            type: string
          example: Best
        - name: pageNumber
          description: The page number
          in: query
          required: false
          schema:
            type: number
            format: int64
          example: 5
        - name: pageSize
          description: The page size
          in: query
          required: false
          schema:
            type: number
            format: int64
          example: 50
        - name: sortBy
          description: The field to sort by
          in: query
          required: false
          schema:
            type: string
            enum:
              - drawAmount
              - creditLimit
              - automatedDecision
              - availableBalance
              - isProjectLimitExceeds
          example: creditLimit
        - name: sortOrder
          description: The sort order to use, default asc
          in: query
          required: false
          schema:
            type: string
            enum:
              - asc
              - desc
          example: asc
      responses:
        200:
          description: The paginated list of draw approvals
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/PagedDrawApproval'
        400:
          description: Bad request
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'     
  /drawApprovals/{id}:
    get:
      tags: 
        - drawApprovals
      summary: Gets draw approval by id (just for compatibility)
      description: Gets draw approval by id (just for compatibility)
      operationId: getDrawApprovalById
      parameters: 
        - name: id
          description: The draw approval id
          in: path
          required: true
          schema:
            type: string
          example: a5d98c9d488492fd
      responses:
        200:
          description: The draw approval by id
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/DrawApproval'
        404:
          description: Bad request
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
  /drawApprovals/{id}/detailed:
    get:
      tags: 
        - drawApprovals
      summary: Gets draw approval details
      description: Gets draw approval details
      operationId: getDrawApprovalDetailsById
      parameters: 
        - name: id
          description: The draw approval id
          in: path
          required: true
          schema:
            type: string
          example: a5d98c9d488492fd
      responses:
        200:
          description: The draw approval with details by id
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/DrawApprovalDetails'
        404:
          description: Bad request
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
  /drawApprovals/{id}/review:
    patch:
      tags: 
        - drawApprovals
      summary: Approves or rejects DrApp, updates status
      description: Approves or rejects DrApp, updates status
      operationId: updateDrawApprovalStatus
      parameters: 
        - name: id
          description: The draw approval id
          in: path
          required: true
          schema:
            type: string
          example: a5d98c9d488492fd
        - name: userId
          description: The user reviewed
          in: header
          required: true
          schema:
            type: string
          example: 8064684bc4dba83d
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/UpdateDrawApprovalStatus'
      responses:
        200:
          description: The updated drawapproval
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/DrawApproval'
        400:
          description: Bad request
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
  /drawApprovals/{id}/paymentplan:
    patch:
      tags: 
        - drawApprovals
      summary: Changes draw application payment plan
      description: Changes draw application payment plan
      operationId: updateDrawApprovalPaymentPlan
      parameters: 
        - name: id
          description: The draw approval id
          in: path
          required: true
          schema:
            type: string
          example: a5d98c9d488492fd
        - name: userId
          description: The user who changed
          in: header
          required: true
          schema:
            type: string
          example: 8064684bc4dba83d
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/UpdateDrawApprovalPaymentPlan'
      responses:
        200:
          description: The updated drawapproval
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/DrawApproval'
        400:
          description: Bad request
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
  /accounts:
    get: 
      tags: 
        - accounts
      summary: Gets paginated accounts (approved borrowers)
      description: Gets paginated accounts (approved borrowers)
      operationId: getAccounts
      parameters:
        - name: id
          description: The account id (do we need this?)
          in: query
          required: false
          schema:
            type: string
          example: 8b568659-b0a7-4ed7-b780-fde87b1a49bb
        - name: companyId
          description: The company id
          in: query
          required: false
          schema:
            type: string
          example: 172050f6-1ed7-4ac3-bf2e-680510cff044
        - name: businessName
          description: The business name
          in: query
          required: false
          schema:
            type: string
          example: Best Lumber and Foresting Inc.
        - name: companyDba
          description: The company dba
          in: query
          required: false
          schema:
            type: string
          example: Best Lumber Inc.
        - name: applicantName
          description: The applicant name
          in: query
          required: false
          schema:
            type: string
          example: Tom Ace
        - name: category
          description: The business category
          in: query
          required: false
          schema: 
            type: string
            enum:
              - subContractor
              - generalContractor
              - dealerRetailerSupplier
              - manufacturerDistributor
              - architectInteriorDesigner
              - engineerConsultant
              - developerPropertyOwner
              - other
          example: subContractor
        - name: accountStatus
          description: The account status (for approved borrowers only)
          in: query
          required: false
          schema:
            type: string
            enum:
              - goodStandingConnected
              - goodStandingManual
              - pastDue
              - onHold
              - inCollection
              - inactive
              - potentiallyFraud
              - closed
          example: goodStandingConnected
        - name: search
          description: Search for business name or dba
          in: query
          required: false
          schema:
            type: string
          example: Acme
        - name: pageNumber
          description: The page number
          in: query
          required: false
          schema:
            type: number
            format: int64
          example: 5
        - name: pageSize
          description: The page size
          in: query
          required: false
          schema:
            type: number
            format: int64
          example: 50
        - name: sortBy
          description: The field to sort by
          in: query
          required: false
          schema:
            type: string
            enum:
              - businessName
              - dba
              - category
              - accountStatus
          example: businessName
        - name: sortOrder
          description: The sort order to use, default asc
          in: query
          required: false
          schema:
            type: string
            enum:
              - asc
              - desc
          example: asc
      responses:
        200:
          description: The paginated accounts
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/PagedAccount'
        404:
          description: Not found
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
  /accounts/{id}/detailed:
    get:
      tags: 
        - accounts
      summary: Gets a single account with details
      description: Gets a single account with details
      operationId: getAccountDetailed
      parameters:
        - name: id
          description: The account id (or company, lets agree)
          in: path
          required: true
          schema:
            type: string
          example: 8b568659-b0a7-4ed7-b780-fde87b1a49bb
      responses:
        200:
          description: The detailed account
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/AccountDetailed'
        404:
          description: Not found
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
  /draws:
    get:
      tags: 
        - draws
      summary: Gets draws by various filters (draws list for BFF)
      description: Gets draws by various filters (draws list for BFF)
      operationId: getDraws
      parameters:
        - name: id
          description: The id of a draw
          in: query
          required: false
          schema:
            type: string
          example: 1203f6ad-5009-4e9a-8a5f-6b222e378898
        - name: companyId
          description: The companyId of draws
          in: query
          required: false
          schema:
            type: string
          example: e054da25-34d4-43b9-b3e8-79f79b141b72
        - name: einHash
          description: The EIN hash
          in: query
          required: false
          schema:
            type: string
          example: abcdefg123456789s
        - name: creditId
          description: The creditId of draws
          in: query
          required: false
          schema:
            type: string
          example: edc0febb-def7-4a0c-ae2f-6c2d3530b6f4
        - name: type
          description: The type of draw
          in: query
          required: false
          schema:
            type: string
            enum:
              - regular
              - virtualCard
              - noSupplier
              - express
              - quote
          example: regular
        - name: status
          description: The status of draw
          in: query
          required: false
          schema:
            type: string
            enum:
            - Created
            - Started
            - Pending
            - Canceled
            - Closed
            - Defaulted
            - Recovered
            - Refinanced
          example: Started
        - name: nextPaymentDateFrom
          description: The next payment date from
          in: query
          required: false
          schema:
            type: string
            format: date
          example: 2024-04-10
        - name: nextPaymentDateTo
          description: The next payment date to
          in: query
          required: false
          schema:
            type: string
            format: date
          example: 2024-04-20
        - name: search
          description: Search for merchant name or invoice number
          in: query
          required: false
          schema:
            type: string
          example: Best
        - name: pageNumber
          description: The page number
          in: query
          required: false
          schema:
            type: number
            format: int64
          example: 5
        - name: pageSize
          description: The page size
          in: query
          required: false
          schema:
            type: number
            format: int64
          example: 50
        - name: sortBy
          description: Sort by
          in: query
          required: false
          schema:
            type: string
            enum:
              - amount
              - nextPaymentAmount
              - nextPaymentDate
          example: amount
        - name: sortOrder
          description: The sort order to use, default asc
          in: query
          required: false
          schema:
            type: string
            enum:
              - asc
              - desc
          example: asc
      responses:
        200:
          description: The draws
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/Draw'
        400:
          description: Bad request
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
  /draws/{id}:
    get:
      tags:
        - draws
      summary: Gets a draws by id (for compatibility)
      description: Gets a draws by id (for compatibility)
      operationId: getDrawById
      parameters:
        - name: id
          description: The id of a draw
          in: path
          required: true
          schema:
            type: string
          example: 1203f6ad-5009-4e9a-8a5f-6b222e378898
      responses:
        200:
          description: The draw
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Draw'
        404:
          description: Not found
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
  /projects:
    get:
      tags:
      - projects
      summary: Gets projects by various filters (projects list for BFF)
      description: Gets projects by various filters (projects list for BFF)
      operationId: getProjects
      parameters:
        - name: id
          description: The id of a project
          in: query
          required: false
          schema:
            type: string
          example: a1e4eff7c4f20bd3
        - name: companyId
          description: The company id
          in: query
          required: false
          schema:
            type: string
          example: a89b1c74-4180-4050-8b59-fb48f32e5610
        - name: search
          description: Searches in Job ID and Address fields
          in: query
          required: false
          schema:
            type: string
          example: Foster 185
        - name: jobEndDateFrom
          description: The job end date from filter
          in: query
          required: false
          schema:
            type: string
            format: date
          example: 2020-01-01
        - name: jobEndDateTo
          description: The job end date to filter
          in: query
          required: false
          schema:
            type: string
            format: date
          example: 2029-12-31
        - name: pageNumber
          description: The page number
          in: query
          required: false
          schema:
            type: number
            format: int64
          example: 5
        - name: pageSize
          description: The page size
          in: query
          required: false
          schema:
            type: number
            format: int64
          example: 50
        - name: sortBy
          description: Sort by
          in: query
          required: false
          schema:
            type: string
            enum:
              - contractValue
          example: contractValue
        - name: sortOrder
          description: The sort order to use, default asc
          in: query
          required: false
          schema:
            type: string
            enum:
              - asc
              - desc
          example: asc
      responses:
        200:
          description: The projects
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/Project'
        400:
          description: Bad request
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
  /projects/{id}:
    get:
      tags:
      - projects
      summary: Gets a project by id (just for compatibility)
      description: Gets a project by id (just for compatibility)
      operationId: getProjectById
      parameters:
        - name: id
          description: The id of a project
          in: path
          required: true
          schema:
            type: string
          example: a1e4eff7c4f20bd3
      responses:
        200:
          description: The project
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Project'
        404:
          description: Not found
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
  /admin/credits/{id}/details:
    patch:
      tags:
      - admin functions
      summary: Updates credit details (admin function)
      description: Updates credit details (admin function) All fields are optional. Which field is sent, that field to update. Returns credit details only.
      operationId: updateCreditDetailsById
      parameters:
        - name: id
          description: The id of credit
          in: path
          required: true
          schema:
            type: string
          example: ab558e47-be3f-4f42-b356-7f506cf580d3
        - name: userId
          description: Identifier of the user who updated the credit details
          example: 62c5e900cd65d31d3f25c34c
          in: header
          required: true
          schema:
            type: string
            nullable: false
      requestBody:
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/PatchCreditDetails"
      responses:
        200:
          description: The credit details
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/CreditDetails2'
        400:
          description: Bad request
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
  /accounts/{id}/cashflow:
    get:
      tags:
        - accounts
      summary: Get cashflow by account (by default gets all available cashflow)
      description: Get cashflow by account (by default gets all available cashflow)
      operationId: getCashFlow
      parameters:
        - name: id
          description: The id of account
          in: path
          required: true
          schema:
            type: string
          example: 96f7636d787542f9
        - name: from
          description: The filter from date
          in: query
          required: false
          schema:
            type: string
          example: 2024-01-01
        - name: to
          description: The filter to date
          in: query
          required: false
          schema:
            type: string
          example: 2024-12-31
        - name: grouping
          description: The grouping (default daily)
          in: query
          required: false
          schema:
            type: string
            enum:
              - daily
              - monthly
          example: daily
      responses:
        200:
          description: The cashflow items
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/CashFlowItem'
        404:
          description: Not found
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
  /accounts/{id}/cashflow/files:
    get: 
      tags: 
        - accounts
      summary: Lists manually uploaded transaction files
      description: Lists manually uploaded transaction files
      operationId: getTransactionFiles
      parameters:
        - name: id
          description: The id of account
          in: path
          required: true
          schema:
            type: string
          example: 96f7636d787542f9
      responses:
        200:
          description: The manually uploaded transaction files
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/ManuallyUploadedTransactionFile'
        404:
          description: Not found
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
  /admin/bank-accounts/{id}/cashFlow:
    patch: 
      tags: 
        - admin functions
      summary: Updates involvement of bankaccount in cashflow calculations
      description: Updates involvement of bankaccount in cashflow calculations
      operationId: updateBankAccountCashFlow
      parameters:
        - name: id
          description: The id of bankaccount
          in: path
          required: true
          schema:
            type: string
          example: 87a02679ba39da61
      requestBody:
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/PatchBankAccountCashFlow"
      responses:
        200:
          description: No response body
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/EmptyResponse'
        404:
          description: Not found
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
  /admin/companies/{companyId}/cashflow/{accountId}/file:
    post:
      tags:
        - admin functions
      summary: Uploads a transaction list and calculates cashflow by it
      description: Uploads a transaction list and calculates cashflow by it
      operationId: createCashFlowByTransactionFile
      parameters:
        - name: companyId
          description: The company id
          in: path
          required: true
          schema:
            type: string
        - name: accountId
          description: The account id
          in: path
          required: true
          schema:
            type: string
        - name: userId
          description: Identifier of the user who manually uploaded
          example: 62c5e900cd65d31d3f25c34c
          in: header
          required: true
          schema:
            type: string
            nullable: false
      requestBody:
        content:
          text/csv:
            schema:
              type: string
              format: binary
      responses:
        201:
          description: The cashflow was calculated.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/AddManualCashFlowResponse'
        400:
          description: Bad request
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        404:
          description: Not found
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
components:
  schemas:
    PagedCreditApplication:
      type: object
      properties:
        pageNumber:
          type: number
          format: int64
          description: The page number
          example: 5
        pagesCount:
          type: number
          format: int64
          description: The pages count
          example: 50
        totalCount:
          type: number
          format: int64
          description: The total count of items
          example: 3319
        result:
          type: array
          items:
            $ref: '#/components/schemas/CreditApplication'
    CreditApplication:
      type: object
      properties:
        id:
          type: string
          description: The credit application id
          example: 8b13f2ba3fa9bd95
        companyId:
          type: string
          description: The applicant company id
          example: 1af57a88-0e23-4b46-8c25-3630c66b28bd
        businessName:
          type: string
          description: The business name
          example: Acme Trading and Marketing Inc.
        dba:
          type: string
          description: The doing business as name
          example: Acme Inc.
        category:
          type: string
          description: The business category
          enum:
            - subContractor
            - generalContractor
            - dealerRetailerSupplier
            - manufacturerDistributor
            - architectInteriorDesigner
            - engineerConsultant
            - developerPropertyOwner
            - other
          example: subContractor
        applicantName:
          type: string
          description: The applicant person name
          example: Tom Ace
        requestedAmount:
          type: number
          format: decimal
          example: 10000.00
          pattern: '[0-9]*\.[0-9]{2}'
          multipleOf: 0.01
          description: The requested line of credit amount
        approvedAmount:
          type: number
          format: decimal
          example: 9000.00
          pattern: '[0-9]*\.[0-9]{2}'
          multipleOf: 0.01
          nullable: true
          description: The approved line of credit amount. Null if not approved or not prequalified.
        currency:
          type: string
          maxLength: 3
          example: USD
        status:
          type: string
          enum:
            - inReview
            - approved
            - rejected
            - canceled
            - sentBack
          description: The status of credit application, inReview means new/processing/processed/executionFailed
          example: inReview
        automatedDecisionStatus:
          type: string
          enum:
            - processing
            - done
            - error
          example: processing
          description: The decision evaluation status, the step function status
        automatedDecision:
          type: string
          enum:
            - pass
            - softFail
            - hardFail
          nullable: true
          example: pass
          description: The automated decision, calculated decision. Null if not fully run yet.
        submissionDate:
          type: string
          format: date
          example: 2024-04-15
          description: The application submission date.
        decisionDate:
          type: string
          format: date
          nullable: true
          example: 2024-04-19
          description: The approval or rejection date.
        decisionMadeBy:
          type: string
          nullable: true
          example: BackofficeUser
          description: Who made the approval or rejection, a backoffice user.
    CreditApplicationDetailed:
      type: object
      properties:
        id:
          type: string
          description: The credit application id
          example: 87e8460ef52c3f6a
        companyName:
          type: string
          description: The business name
          example: Acme Inc.
        status:
          type: string
          enum:
            - inReview
            - approved
            - rejected
            - canceled
            - sentBack
          description: The status of credit application, inReview means new/processing/processed/executionFailed
          example: inReview
        lastStatusChangedBy:
          type: string
          nullable: true
          example: BackofficeUser1
          description: The last user or application name who changed the status.
        lastStatusChangedAt:
          type: string
          format: date-time
          nullable: true
          example: 2024-03-08T12:49:24.907Z
          description: The last status change time.
        automatedDecisionStatus:
          type: string
          enum:
            - processing
            - done
            - error
          example: processing
          description: The decision evaluation status, the step function status
        automatedDecision:
          type: string
          enum:
            - pass
            - softFail
            - hardFail
          nullable: true
          example: pass
          description: The automated decision, calculated decision. Null if not fully run yet.
        requestedAmount:
          type: number
          format: decimal
          example: 10000.00
          pattern: '[0-9]*\.[0-9]{2}'
          multipleOf: 0.01
          description: The requested credit amount.
        approvedAmount:
          type: number
          format: decimal
          example: 8000.00
          pattern: '[0-9]*\.[0-9]{2}'
          multipleOf: 0.01
          nullable: true
          description: The approved credit limit amount by backoffice user.
        creditLimit:
          type: number
          format: decimal
          example: 9000.00
          pattern: '[0-9]*\.[0-9]{2}'
          multipleOf: 0.01
          description: The CALCULATED credit amount by the decision engine. Null, until last step of evaluation passes and writes it.
        preliminaryChecks:
          allOf: 
            - $ref: "#/components/schemas/PreliminaryChecksResult"
          nullable: true
        creditRating:
          allOf: 
            - $ref: "#/components/schemas/CreditRatingResult"
          nullable: true
        kyb:
          allOf: 
            - $ref: "#/components/schemas/KYBResult"
          nullable: true
        kyc:
          allOf: 
            - $ref: "#/components/schemas/KYCResult"
          nullable: true
        bankDetails:
          allOf:
            - $ref: "#/components/schemas/BankDetailsResult"
          nullable: true
    PreliminaryChecksResult:
      type: object
      properties:
        lastEinRejectionDate3Months:
          type: string
          enum:
            - pass
            - softFail
            - hardFail
            - executionFailed
          nullable: true
          example: pass
        lastEinRejectionDate18Months:
          type: string
          enum:
            - pass
            - softFail
            - hardFail
            - executionFailed
          nullable: true
          example: pass
        loansLastDefaultedDate12Months:
          type: string
          enum:
            - pass
            - softFail
            - hardFail
            - executionFailed
          nullable: true
          example: pass
        businessStartDate24Months:
          type: string
          enum:
            - pass
            - softFail
            - hardFail
            - executionFailed
          nullable: true
          example: pass
        result:
          type: string
          enum:
            - pass
            - softFail
            - hardFail
            - executionFailed
          nullable: true
          example: pass
    KYBResult:
      type: object
      properties:
        result:
          type: string
          enum:
            - pass
            - softFail
            - hardFail
            - executionFailed
          nullable: true
          example: pass
        bvi:
          allOf: 
            - $ref: "#/components/schemas/VerificationScore"
          nullable: true
        bri:
          type: array
          items:
            $ref: "#/components/schemas/BriItem"
    KYCResult:
      type: object
      properties:
        result:
          type: string
          enum:
            - pass
            - softFail
            - hardFail
            - executionFailed
          nullable: true
          example: pass
        individualOwnersData:
          type: array
          items:
            $ref: "#/components/schemas/IndividualOwnerItem"
        entityOwnersData:
          type: array
          items:
            $ref: "#/components/schemas/EntityOwnerItem"
    CreditRatingResult:
      type: object
      properties:
        experianReliabilityCode:
          $ref: "#/components/schemas/CheckResultString"
        firstReportedTradeLineDays:
          $ref: "#/components/schemas/CheckResultInt"
        companyBankruptcy:
          $ref: "#/components/schemas/CheckResultBool"
        anyJudgement:
          $ref: "#/components/schemas/CheckResultBoolAndNumber"
        anyLien:
          $ref: "#/components/schemas/CheckResultBoolAndNumber"
        tradeLinesPercentage:
          $ref: "#/components/schemas/CheckResultNumber"
        revenueVariancePercentage:
          type: number
          nullable: true
          example: 88.1
        dti2Value:
          type: number
          nullable: true
          example: 55.3
        inquiries:
          type: number
          format: int64
          nullable: true
          example: 5
        outstandingAccountBalance:
          type: number
          nullable: true
          example: 4000
        sixtyPlusDebtPercentage:
          type: number
          nullable: true
          example: 51.3
        sixtyPlusDebtAmount:
          type: number
          nullable: true
          example: 3560
    CheckResultString:
      type: object
      properties:
        value:
          type: string
          nullable: true
        result:
          type: string
          enum:
            - pass
            - softFail
            - hardFail
            - executionFailed
          nullable: true
          example: pass
    CheckResultDate:
      type: object
      properties:
        value:
          type: string
          format: date
          nullable: true
        result:
          type: string
          enum:
            - pass
            - softFail
            - hardFail
            - executionFailed
          nullable: true
          example: pass
    CheckResultInt:
      type: object
      properties:
        value:
          type: number
          format: int64
          nullable: true
        result:
          type: string
          enum:
            - pass
            - softFail
            - hardFail
            - executionFailed
          nullable: true
          example: pass
    CheckResultBool:
      type: object
      properties:
        value:
          type: boolean
          nullable: true
        result:
          type: string
          enum:
            - pass
            - softFail
            - hardFail
            - executionFailed
          nullable: true
          example: pass
    CheckResultBoolAndNumber:
      type: object
      properties:
        value:
          type: boolean
          nullable: true
        amount:
          type: number
          nullable: true
        result:
          type: string
          enum:
            - pass
            - softFail
            - hardFail
            - executionFailed
          nullable: true
          example: pass
    CheckResultNumber:
      type: object
      properties:
        value:
          type: number
          nullable: true
        result:
          type: string
          enum:
            - pass
            - softFail
            - hardFail
            - executionFailed
          nullable: true
          example: pass
    CheckBankAccountTypeResult:
      type: object
      properties:
        value:
          type: boolean
        result:
          type: string
          enum:
            - pass
            - softFail
            - hardFail
            - executionFailed
          nullable: true
          example: pass
    VerificationScore:
      type: object
      properties:
        code:
          type: string
        description:
          type: string
    BriItem:
      type: object
      properties:
        code:
          type: string
        description:
          type: string
    IndividualOwnerItem:
      type: object
      properties:
        ownerName:
          type: string
        percentOwned:
          type: number
        principalOwner:
          type: boolean
        business2ExecLinkIndex:
          $ref: "#/components/schemas/CheckResultString"
        cri:
          $ref: "#/components/schemas/CriResult"
        cvi:
          $ref: "#/components/schemas/CviResult"
        ssnRejection3Months:
          $ref: "#/components/schemas/CheckResultBool"
        ssnRejection18Months:
          $ref: "#/components/schemas/CheckResultBool"
        fraudPointScore:
          $ref: "#/components/schemas/CheckResultString"
        eaScore:
          $ref: "#/components/schemas/CheckResultString"
        ipRiskLevel:
          $ref: "#/components/schemas/CheckResultString"
        domainRiskLevel:
          $ref: "#/components/schemas/CheckResultString"
        ficoScore:
          $ref: "#/components/schemas/CheckResultString"
        lastPersonalBankruptcy:
          $ref: "#/components/schemas/CheckResultDate"
    CriResult:
      type: object
      properties:
        value:
          type: array
          items:
            $ref: "#/components/schemas/VerificationScore"
        result:
          type: string
          enum:
            - pass
            - softFail
            - hardFail
            - executionFailed
          nullable: true
          example: pass
    CviResult:
      type: object
      properties: 
        value:
          $ref: "#/components/schemas/VerificationScore"
        result:
          type: string
          enum:
            - pass
            - softFail
            - hardFail
            - executionFailed
          nullable: true
          example: pass
    EntityOwnerItem:
      type: object
      properties:
        ownerName:
          type: string
          description: Entity name
          example: Microsoft Inc.
        percentOwned:
          type: number
          example: 25
          description: The owned percentage in this company
        principalOwner:
          type: boolean
          description: Is this entity a principal owner
          example: true
        bvi:
          allOf:
            - $ref: "#/components/schemas/VerificationScore"
          nullable: true
          description: The business verification index evaluation results
        bri:
          type: array
          items:
            $ref: "#/components/schemas/BriItem"
          nullable: true
          description: The business risk indicator evaluation results
        sixtyPlusDebtPercentage:
          type: number
          nullable: true
          example: 45.2
          description: Debts ratio percentage from Experian 60+ days
        sixtyPlusDebtAmount:
          type: number
          nullable: true
          example: 4500
          description: Debts amount from Experian 60+ days
        tradeLinesPercentage:
          type: number
          nullable: true
          example: 66.1
          description: Tradelines percentage from Experian
        experianReliabilityCode:
          type: string
          nullable: true
          example: 100
          description: Reliability code from Experian
        firstReportedTradeLineDays:
          type: number
          nullable: true
          description: Days number from the first reported tradeline from Experian
          example: 8
        companyBankruptcy:
          type: boolean
          nullable: true
          description: Has the company bankruptcy (ever)
          example: false
        anyJudgement:
          type: boolean
          nullable: true
          description: Has the company judgment (ever)
          example: false
        anyLien:
          type: boolean
          description: Has the company lien (ever)
          nullable: true
          example: false
        revenueVariancePercentage:
          type: number
          nullable: true
          example: 55.1
          description: Ratio of revenues and debts
        dti2Value:
          type: number
          nullable: true
          description: The DTI2 value (see formula in Confluence)
        inquiries:
          type: number
          format: int64
          nullable: true
          description: The inquiries number of the company
        lastEinRejectionDate6Months:
          type: string
          enum:
            - pass
            - softFail
            - hardFail
            - executionFailed
          nullable: true
          example: pass
        isPartOfLiveBTAccount:
          type: string
          enum:
            - pass
            - softFail
            - hardFail
            - executionFailed
          nullable: true
          example: pass
          description: Is this EIN is an active borrower of BlueTape
    BankDetailsResult:
      type: object
      properties:
        result:
          type: string
          enum:
            - pass
            - softFail
            - hardFail
            - executionFailed
          nullable: true
        bankCreditStatus:
          allOf:
            - $ref: "#/components/schemas/BankCreditStatusResult"
          nullable: true
        bankAccounts:
          type: array
          description: The list of manually of Plaid connected bank accounts
          items:
            $ref: "#/components/schemas/BankAccountItem"
        giact:
          type: array
          description: The manual bank accounts' Giact verification results
          items:
            $ref: "#/components/schemas/GiactBankAccountVerificationResult"
          nullable: true
    BankCreditStatusResult:
      type: object
      properties:
        businessOutstandingBalance:
          type: number
          nullable: true
          description: Business Outstanding BlueTape Balance
          example: 6000
        annualRevenue:
          type: number
          nullable: true
          description: Bank Statement Annual Revenue Estimate
          example: 700000
        revenueByCustomer:
          type: number
          nullable: true
          description: Revenue Estimate Provided by customer
          example: 800000
        companyIncome:
          type: number
          nullable: true
          description: Revenue For Loan Calculation
          example: 750000
        currentDbt:
          type: number
          nullable: true
          description: Debt Estimate from Experian
          example: 550000
        debtByCustomer:
          type: number
          nullable: true
          description: Debt Estimate provided by Customer
          example: 450000
        debtAdjustor:
          type: number
          nullable: true
          description: Debt Adjustor
          example: 1.2
        loanDebt:
          type: number
          nullable: true
          description: Debt Estimate for Loan Calculation
          example: 340000
        acceptablePercentRevenue:
          type: number
          nullable: true
          description: Acceptable Debt % of Revenue
          example: 80
        totalAcceptableDebtAmount:
          type: number
          nullable: true
          description: Total acceptable debt amount
          example: 500000
        availableCreditLimit:
          type: number
          nullable: true
          description: Decision Engine Potential BlueTape credit amount
          example: 80000
    BankAccountItem:
      type: object
      properties:
        identifier:
          type: string
          example: 64d0cf6d3ffda590eb3baf3e
          description: The identifier of bank account, possibly coming from draft
        name:
          type: string
          description: The bank account concatenated name
          example: Finbank Profiles - A ************0161
        connectionType:
          type: string
          enum:
            - plaid
            - manual
          example: plaid
          description: The account connection type
        isIncludeInCashFlow:
          type: boolean
          description: Is included in cash flow calculation
          default: true
          example: true
        plaidConnectionStatus:
          type: string
          enum:
            - active
            - expired
            - expiring
            - disconnected
          description: Current status of Plaid connection
          example: active
        isCheckingAccount:
          $ref: "#/components/schemas/CheckBankAccountTypeResult"
        businessNameVerification:
          type: string
          enum:
            - pass
            - softFail
            - hardFail
            - executionFailed
          example: pass
          description: The business name and bank account holder name verification result
    BankAccountItem2:
      type: object
      properties:
        identifier:
          type: string
          example: 64d0cf6d3ffda590eb3baf3e
          description: The identifier of bank account, possibly coming from draft
        name:
          type: string
          description: The bank account concatenated name
          example: Finbank Profiles - A ************0161
        connectionType:
          type: string
          enum:
            - plaid
            - manual
          description: The account connection type
        isIncludeInCashFlow:
          type: boolean
          description: Is included in cash flow calculation
          default: true
          example: true
        plaidConnectionStatus:
          type: string
          enum:
            - active
            - expired
            - expiring
            - disconnected
          description: Current status of Plaid connection
          example: active
    GiactBankAccountVerificationResult:
      type: object
      properties:
        identifier:
          type: string
          example: 64d0cf6d3ffda590eb3baf3e
          description: The identifier of bank account, possibly coming from draft
        name:
          type: string
          description: The bank account concatenated name
          example: Finbank Profiles - A ************0161
        isVerified:
          type: boolean
          example: true
          description: Is the Giact verification has run and passed
    CashFlowItem:
      type: object
      properties:
        date:
          type: string
          format: date
          example: 2024-04-19
          description: The date of cash flow item (grouped all transactions for one day)
        debit:
          type: number
          format: decimal
          example: 700.00
          pattern: '[0-9]*\.[0-9]{2}'
          multipleOf: 0.01
          description: All the debits of date
        credit:
          type: number
          format: decimal
          example: -900.00
          pattern: '[0-9]*\.[0-9]{2}'
          multipleOf: 0.01
          description: All the credits of date
        balance:
          type: number
          format: decimal
          example: 11000.00
          pattern: '[0-9]*\.[0-9]{2}'
          multipleOf: 0.01
          description: The cumulated balance (all the debit plus credit added to previous day's balance)
        cashFlow:
          type: number
          format: decimal
          example: -200.00
          pattern: '[0-9]*\.[0-9]{2}'
          multipleOf: 0.01
          description: Sum of debit and credit
    NoteItem:
      type: object
      properties:
        id:
          type: string
          example: 42701570-dcb6-4d50-b7ab-33699061aa7f
          description: Id of note
        creditApplicationId:
          type: string
          description: The credit application id
          example: acced8ca3a4385c6
        note:
          type: string
          description: The note
          example: This application should be rejected!
        createdAt:
          type: string
          format: date-time
          description: The creation time
          example: 2024-03-08T12:49:24.907Z
        createdBy:
          type: string
          description: Who created this note (application or a backoffice user id)
          example: BackofficeUserId
    AddCreditApplicationNote:
      type: object
      properties:
        note:
          type: string
          description: The note
          example: This application should be rejected!
    PagedDrawApproval:
      type: object
      properties:
        pageNumber:
          type: number
          format: int64
          description: The page number
          example: 5
        pagesCount:
          type: number
          format: int64
          description: The pages count
          example: 50
        totalCount:
          type: number
          format: int64
          description: The total count of items
          example: 3340
        result:
          type: array
          items:
            $ref: '#/components/schemas/DrawApproval'
    DrawApproval:
      type: object
      properties:
        id:
          type: string
          example: 8592a01fa0920b12
          description: The draw application (approval) id
        createdAt:
          type: string
          format: date-time
          description: The creation time
          example: 2024-03-08T12:49:24.907Z
        createdBy:
          type: string
          description: Who created the draw application (application)
          example: BlueTape.DecisionEngine.DrawApproval
        updatedAt:
          type: string
          format: date-time
          description: Last update time
          example: 2024-03-08T12:49:24.907Z
        updatedBy:
          type: string
          description: Who updated the draw application last time (application or user)
          example: BackofficeUserId
        companyId:
          type: string
          description: The applicant company id
          example: 9d3189947e858166
        businessName:
          type: string
          description: The business name
          example: Acme Trading and Marketing Inc.
        dba:
          type: string
          description: The doing business as name
          example: Acme Inc.
        applicationDate:
          type: string
          format: date
          example: 2024-04-25
          description: Submission date
        type:
          type: string
          enum:
            - regular
            - virtualCard
            - noSupplier
            - express
            - quote
          example: regular
          description: The draw approval type, basically defines the process of issue
        merchantId:
          type: string
          description: The merchant company id (from invoices)
          example: 85f0add75331b5e1
        merchantName:
          type: string
          example: Best Lumber
          description: The merchant name. Redundant field to help filtering
        drawAmount:
          type: number
          format: decimal
          example: 5000.00
          pattern: '[0-9]*\.[0-9]{2}'
          multipleOf: 0.01
          description: The draw's required amount. Equals to invoices sum amount.
        drawAmountRiskLevel:
          type: string
          enum:
            - level1
            - level2
            - level3
            - level4
          description: The draw's risk level (based on amount). Null if not evaluated.
          example: level1
          nullable: true
        automatedDecision:
          type: string
          enum:
            - passed
            - softFailed
            - hardFailed
          nullable: true
          description: The automated decision, calculated decision. Null if not fully run yet.
          example: passed
        status:
          type: string
          enum:
            - inReview
            - approved
            - rejected
            - canceled
            - sentback
          description: The status of draw application, inReview means new/processing/processed/executionFailed
          example: inReview
        lastStatusChangedAt:
          type: string
          format: date-time
          example: 
          description: The last status change time.
          nullable: true
        lastStatusChangedBy:
          type: string
          description: The last user or application name who changed the status.
          example: BackofficeUser1
          nullable: true
        statusCode:
          type: string
          description: Generic status code, now using for rejection levels.
          nullable: true
          example: P07
        statusNote:
          type: string
          description: Generic status note, now using for rejection levels.
          nullable: true
          example: Illegible Invoice
        accountDetails:
          $ref: "#/components/schemas/AccountDetails"
        paymentPlanDetails:
          $ref: "#/components/schemas/PaymentPlanDetails"
        projectDetails:
          allOf:
            - $ref: "#/components/schemas/ProjectDetails"
          nullable: true
        payablesDetails:
          $ref: "#/components/schemas/PayablesDetails"
    AccountDetails:
      type: object
      description: The account details
      properties:
        status:
          type: string
          enum: 
            - incomplete
            - underReview
            - underStipulation
            - goodStandingConnected
            - goodStandingManual
            - pastDue
            - onHold
            - inCollection
            - inactive
            - potentiallyFraud
            - closed
          description: The actual account status
          example: goodStandingConnected
        statusText:
          type: string
          enum: 
            - CreditApplication.incomplete
            - CreditApplication.underReview
            - CreditApplication.underStipulation
            - CreditApplication.approved
            - CreditApplication.rejected
            - CreditApplication.canceled
            - BankAccount.connected
            - BankAccount.disconnected
            - BankAccount.manuallyAdded
            - Credit.pastDue
            - Credit.goodStanding
            - Company.creditRatingFails
            - CompanyOwners.creditRatingFails
            - Company.annualRevenueFails
            - Credit.inactivityCheckFails
            - Company.KYBFails
            - CompanyOwners.KYCFails
            - Credit.creditLimitChanged
          description: Which event changed last time the account status automatically
          example: Credit.goodStanding
        availableBalance:
          type: number
          format: decimal
          example: 5500.00
          pattern: '[0-9]*\.[0-9]{2}'
          multipleOf: 0.01
          description: The available credit balance
    PaymentPlanDetails:
      type: object
      properties:
        id:
          type: string
          description: The selected payment plan id (LoanTemplate in LMS)
          example: d74ec499-c11d-4d66-a572-dc47298ad0cf
        terms:
          type: number
          format: int32
          example: 30
          description: The term (how many days to delay before first payment)
    ProjectDetails:
      type: object
      properties:
        id:
          type: string
          description: The project id
          example: b140dbcae888676c
        status:
          type: string
          enum:
            - incomplete
            - inreview
            - approved
            - rejected
          description: The project approval status
          example: incomplete
        isProjectLimitExceeds:
          type: boolean
          example: false
          description: Is the amount exceeds project limit
    PayablesDetails:
      type: object
      description: Details of invoices (payables)
      properties:
        sumAmount:
          type: number
          format: decimal
          example: 5000.00
          pattern: '[0-9]*\.[0-9]{2}'
          multipleOf: 0.01
          description: The sum amount of invoices
        payablesCount:
          type: number
          format: int64
          example: 1
          description: The number of invoices
        number:
          type: string
          example: 0000001/2024
          nullable: true
          description: Filled only if a single invoice is attached, otherwise null
        dueDate:
          type: string
          format: date
          nullable: true
          example: 2024-04-25
          description: Filled only if a single invoice is attached, otherwise null
    DrawApprovalDetails:
      type: object
      properties:
        id:
          type: string
          example: b140dbcae888676c
          description: The id of draw approval, draw application
        createdAt:
          type: string
          format: date-time
          description: The creation time
          example: 2024-03-08T12:49:24.907Z
        createdBy:
          type: string
          description: Who created (application)
          example: BlueTape.DecisionEngine.DrawApproval
        updatedAt:
          type: string
          format: date-time
          description: Last update time
          example: 2024-03-08T12:49:24.907Z
        updatedBy:
          type: string
          description: Who updated the draw application last time (application or user)
          example: BackofficeUserId
        companyId:
          type: string
          description: The applicant company id
          example: 9d3189947e858166
        businessName:
          type: string
          description: The business name
          example: Acme Trading and Marketing Inc.
        dba:
          type: string
          description: The doing business as name
          example: Acme Inc.
        applicationDate:
          type: string
          format: date
          example: 2024-04-25
          description: Submission date
        type:
          type: string
          enum:
            - regular
            - virtualCard
            - noSupplier
            - express
            - quote
          example: regular
          description: The draw approval type, basically defines the process of issue
        merchantId:
          type: string
          description: The merchant company id (from invoices)
          example: 85f0add75331b5e1
        merchantName:
          type: string
          example: Best Lumber
          description: The merchant name. Redundant field to help filtering
        drawAmount:
          type: number
          format: decimal
          example: 5000.00
          pattern: '[0-9]*\.[0-9]{2}'
          multipleOf: 0.01
          description: The draw's required amount. Equals to invoices sum amount.
        drawAmountRiskLevel:
          type: string
          enum:
            - level1
            - level2
            - level3
            - level4
          description: The draw's risk level (based on amount). Null if not evaluated.
          example: level1
          nullable: true
        automatedDecision:
          type: string
          enum:
            - passed
            - softFailed
            - hardFailed
          nullable: true
          description: The automated decision, calculated decision. Null if not fully run yet.
          example: passed
        status:
          type: string
          enum:
            - inReview
            - approved
            - rejected
            - canceled
            - sentback
          description: The status of draw application, inReview means new/processing/processed/executionFailed
          example: inReview
        lastStatusChangedAt:
          type: string
          format: date-time
          nullable: true
          example: 2024-03-08T12:49:24.907Z
          description: The last status change time.
        lastStatusChangedBy:
          type: string
          nullable: true
          example: BackofficeUser1
          description: The last user or application name who changed the status.
        statusCode:
          type: string
          description: Generic status code, now using for rejection levels.
          nullable: true
          example: P07
        statusNote:
          type: string
          description: Generic status note, now using for rejection levels.
          nullable: true
          example: Illegible Invoice
        approvalDetails:
          $ref: "#/components/schemas/ApprovalDetails"
        accountDetails:
          $ref: "#/components/schemas/AccountDetails"
        creditDetails:
          $ref: "#/components/schemas/CreditDetails"
        paymentPlanDetails:
          $ref: "#/components/schemas/PaymentPlanDetails"
        projectDetails:
          allOf:
            - $ref: "#/components/schemas/ProjectApprovalDetails"
          nullable: true
        preliminaryChecks:
          allOf: 
            - type: object
              properties:
                result:
                  type: string
                  enum:
                    - pass
                    - softFail
                    - hardFail
                    - executionFailed
                  nullable: true
            - $ref: "#/components/schemas/PreliminaryChecks"
              nullable: true
        payablesDetails:
          type: array
          items:
            $ref: "#/components/schemas/PayablesData"
    ProjectApprovalDetails:
      type: object
      properties:
        value:
          type: string
          description: The project approval status
        result:
          type: string
          enum:
            - pass
            - softFail
            - hardFail
          nullable: true
        details:
          $ref: "#/components/schemas/ProjectData"
    ProjectData:
      type: object
      properties:
        id:
          type: string
          format: guid
          description: Id of project
          example: b87bf816ce5e7b6e
        companyId:
          type: string
          description: The company id of the project, should be the same as borrower and invoices companyId
          example: b01848a2212fd419
        status:
          type: string
          enum:
            - incomplete
            - inreview
            - approved
            - rejected
          description: The project approval status
          example: incomplete
        statusCode:
          type: string
        statusNote:
          type: string
        approvedAt:
          type: string
          format: date-time
          nullable: true
          example: 2024-03-08T12:49:24.907Z
          default: The approval time. Null if not approved.
        approvedBy:
          type: string
          nullable: true
          description: The approver user id.
          example: BackofficeUser1
        rejectedAt:
          type: string
          format: date-time
          nullable: true
          example: 2024-03-08T12:49:24.907Z
          default: The rejection time. Null if not rejected.
        rejectedBy:
          type: string
          nullable: true
          description: The rejector user id.
          example: BackofficeUser1
        name:
          type: string
          description: The project name.
        isDeactivated:
          type: boolean
          example: false
          default: false
          description: Is the project deactivated
        address:
          type: string
          example: 285 Fulton St, New York, NY 10007
          description: The project address
        requestedValue:
          type: number
          format: decimal
          example: 5500.00
          pattern: '[0-9]*\.[0-9]{2}'
          multipleOf: 0.01
          description: The project requested amount.
        contractValue:
          type: number
          format: decimal
          example: 5500.00
          pattern: '[0-9]*\.[0-9]{2}'
          multipleOf: 0.01
          description: The project approved amount.
        startDate:
          type: string
          format: date
          example: 2024-04-25
          description: The project start date.
        endDate:
          type: string
          format: date
          example: 2025-12-31
          description: The project end date.
        isExpired:
          type: boolean
          example: false
          description: Calculated field by endDate and today
        jobId:
          type: string
          description: The job id
          example: 445aab/40
        creditDetails:
          $ref: '#/components/schemas/ProjectCreditDetails'
    ProjectCreditDetails:
      type: object
      properties:
        numberOfDraws:
          type: number
          format: int32
          example: 5
          description: Number of attached draws to the project
        numberOfPayables:
          type: number
          format: int32
          example: 8
          description: Number of invoices (payables) attached to the project
        availableCredit:
          type: number
          format: decimal
          example: 10000.00
          pattern: '[0-9]*\.[0-9]{2}'
          multipleOf: 0.01
          description: Available spending amount. Contract amount minus used amount. One of these fields is calculated (see contractValue)
        usedCredit:
          type: number
          format: decimal
          example: 4555.00
          pattern: '[0-9]*\.[0-9]{2}'
          multipleOf: 0.01
          description: Used spending amount. Sum of the attached invoices. One of these fields is calculated (see contractValue)
        usedCreditPercentage:
          type: number
          format: decimal
          example: 45.55
          pattern: '[0-9]*\.[0-9]{2}'
          multipleOf: 0.01
          description: The ratio of contract value and the used amount. Calculated field
    CreditDetails:
      type: object
      properties:
        id:
          type: string
          description: The credit id
          example: 04afaeb4-4aed-430c-b390-67e2d5f7210a
        numberOfDraws:
          type: number
          format: int32
          example: 5
          description: Calculated field. Count of all draws for this credit.
        usedCreditPercentage:
          type: number
          example: 66.6
          pattern: '[0-9]*\.[0-9]{2}'
          multipleOf: 0.01
          description: Calculated field. Percent ratio of available and used credit. 100 if all is used, zero if nothing.
        businessDaysLate:
          type: number
          format: int32
          example: 32
          description: Calculated field. The maximum of business days late for a draw in this credit.
        lateAmount:
          type: number
          example: 443.26
          pattern: '[0-9]*\.[0-9]{2}'
          multipleOf: 0.01
          description: Calculated field. The sum amount of all late draw amounts for this credit.
        totalCredit:
          type: number
          description: The field name is wrong, it's creditLimit. The total amount of the credit.
        principalBalance:
          type: number
          example: 4000.00
          pattern: '[0-9]*\.[0-9]{2}'
          multipleOf: 0.01
          description: Calculated field. The sum amount of all draws principal amount in this credit.
        availableCredit:
          type: number
          format: decimal
          example: 5500.00
          pattern: '[0-9]*\.[0-9]{2}'
          multipleOf: 0.01
          description: Calculated field. The total remaining amount available on the credit (creditLimit - active loans' principalBalance)
        drawOutstandingAmount:
          type: number
          example: 4500.00
          pattern: '[0-9]*\.[0-9]{2}'
          multipleOf: 0.01
          description: Calculated field. The sum amount of all draws outstanding amount in this credit.
        processingAmount:
          type: number
          example: 500.00
          pattern: '[0-9]*\.[0-9]{2}'
          multipleOf: 0.01
          description: Calculated field. The sum amount of all draws processing amount in this credit.
        totalPaid:
          type: number
          example: 3500.00
          pattern: '[0-9]*\.[0-9]{2}'
          multipleOf: 0.01
          description: Calculated field. The sum amount of all draws successfully paid amount in this credit.
        totalDrawAmount:
          type: number
          example: 3500.00
          pattern: '[0-9]*\.[0-9]{2}'
          multipleOf: 0.01
          description: Calculated field. The sum amount of all active draws installments and loan fees, but no penalties in this credit.
        isAnyReceivableLate:
          type: boolean
          example: false
          description: Calculated field. Is any active draw late with a receivable in this credit.
        isFullyPaid:
          type: boolean
          example: false
          description: Calculated field. Is this credit has 1 draw at least and all of them are paid.
        oldestDueOrPastDueDate:
          type: string
          format: date
          example: 2024-01-01
          description: Calculated field. The oldest due or past due date of active draws in this credit.
        totalDailyPenaltyInterest:
          type: number
          example: 112.88
          pattern: '[0-9]*\.[0-9]{2}'
          multipleOf: 0.01
          description: Calculated field. The sum amount of penalty interests of active draws in this credit.
        hadPenalty:
          type: boolean
          example: false
          description: Calculated field. Is any active draw has a penalty (paid or unpaid) in this credit.
    PayablesData:
      type: object
      properties:
        number:
          type: string
          description: The invoice number.
          example: 0000001/2024
        dateOfUpload:
          type: string
          format: date
          default: The date when invoice was uploaded.
          example: 2024-03-08T12:49:24.907Z
        date:
          type: string
          format: date
          description: The invoice date.
          example: 2024-03-08T00:00:00.000Z
        dueDate: 
          type: string
          format: date
          description: The invoice due date.
          example: 2024-03-15T00:00:00.000Z
        status:
          type: string
          description: The invoice status
        materialSubtotal:
          type: number
          format: decimal
          example: 700.00
          pattern: '[0-9]*\.[0-9]{2}'
          multipleOf: 0.01
          description: The material subtotal from the total amount.
        taxAmount:
          type: number
          format: decimal
          example: 700.00
          pattern: '[0-9]*\.[0-9]{2}'
          multipleOf: 0.01
          description: The tax part of total amount 
        totalAmount:
          type: number
          format: decimal
          example: 700.00
          pattern: '[0-9]*\.[0-9]{2}'
          multipleOf: 0.01
          description: The invoice total amount
        url:
          type: string
          description: The url to download
        supplierDetails:
          $ref: "#/components/schemas/SupplierDetails"
    SupplierDetails:
      type: object
      properties:
        id: 
          type: string
          description: The merchant id
          example: 9d073cd2e3844e33
        name:
          type: string
          description: The merchant name
          example: Best Lumber
        phone:
          type: string
          description: The merchant phone
          example: +11234567890
        email:
          type: string
          description: The merchant email
          example: <EMAIL>
    ApprovalDetails:
      type: object
      description: Contains data from the time of application evaluation.
      properties:
        LoansLastDefaultedDate:
          type: string
          format: date
          nullable: true
        MaxPastDueDays:
          type: number
          format: integer
          nullable: true
        CurrentCreditLimitPercentage:
          type: number
          nullable: true
        CreditPurchaseType:
          type: string
          enum:
            - inventory
            - project
            - both
          nullable: true
        CreditAvailableBalance:
          type: number
          nullable: true
        ProjectAvailableBalance:
          type: number
          nullable: true
        ProjectContractValue:
          type: number
          nullable: true
        ProjectEndDate:
          type: string
          format: date
          nullable: true
        OutstandingBalance:
          type: number
          nullable: true
        AccountStatus:
          type: string
          enum:
            - incomplete
            - underReview
            - underStipulation
            - goodStandingConnected
            - goodStandingManual
            - pastDue
            - onHold
            - inCollection
            - inactive
            - potentiallyFraud
            - closed
          nullable: true
        ProjectApprovalStatus:
          type: string
          enum:
            - incomplete
            - inreview
            - approved
            - rejected
          nullable: true
    PreliminaryChecks:
      type: object
      description: Preliminary checks step results.
      properties:
        loansLastDefaultedDate12Months:
          $ref: "#/components/schemas/CheckResultBool"
        loansLate30DaysPlus:
          $ref: "#/components/schemas/CheckResultBool"
        creditLimitPercentage90:
          $ref: "#/components/schemas/CheckResultBool"
        businessExceeds50KBalance:
          $ref: "#/components/schemas/CheckResultBool"
        principalExceeds50KBalance:
          $ref: "#/components/schemas/CheckResultBool"
    UpdateCreditApplicationDetails:
      type: object
      properties:
        newStatus:
          type: string
          enum:
            - approved
            - rejected
            - canceled
          description: The new approval status.
        approvedCreditLimit:
          type: number
          format: decimal
          example: 5000.00
          pattern: '[0-9]*\.[0-9]{2}'
          multipleOf: 0.01
          nullable: true
          description: The approved credit limit. If not sent, not updated.
        purchaseType:
          type: string
          enum:
            - inventory
            - project
            - both
          description: The credit's purchase type.
        revenueFallPercentage:
          type: number
          format: decimal
          example: 80.00
          pattern: '[0-9]*\.[0-9]{2}'
          multipleOf: 0.01
          description: The revenue fall percentage (threshold)
        code:
          type: string
          description: Rejection code. In the future could be other codes.
        note:
          type: string
          description: Rejection note. In the future could be used for note.
    UpdateDrawApprovalStatus:
      type: object
      properties: 
        newStatus:
          type: string
          enum:
            - approved
            - rejected
            - canceled
            - sentback
          description: The new approval status.
        code:
          type: string
          description: Rejection code. In the future could be other codes.
        note:
          type: string
          description: Rejection note. In the future could be used for note.
    UpdateDrawApprovalPaymentPlan:
      type: object
      properties:
        newPaymentPlanId:
          type: string
          example: d530c590-9855-4cec-91ad-611d5eafa005
          description: The new payment plan id.
    PagedAccount:
      type: object
      properties:
        pageNumber:
          type: number
          format: int64
          example: 1
          description: The page number
        pagesCount:
          type: number
          format: int64
          example: 8
          description: The pages count
        totalCount:
          type: number
          format: int64
          example: 450
          description: The total count of items
        result:
          type: array
          items:
            $ref: '#/components/schemas/Account'
    Account:
      type: object
      properties:
        id:
          type: string
          description: The account id
          example: 03b9b9c1-69c1-41ff-951a-074ed5fa0a3c
        companyId:
          type: string
          description: The company id
          example: 2d83d673-9b3f-4ace-a90c-107b0f23c9fd
        einHash:
          type: string
          description: The ein hash
          example: 332484gbaaefed
        creditId:
          type: string
          description: The credit id
          example: 82694e8b-7ba7-47e8-9d4b-931f11c4492f
        businessName:
          type: string
          description: The business name
          example: Best Lumber
        dba:
          type: string
          description: Doing business as
          example: Best Lumber
        category:
          type: string
          description: The business category, type
          enum:
            - subContractor
            - generalContractor
            - dealerRetailerSupplier
            - manufacturerDistributor
            - architectInteriorDesigner
            - engineerConsultant
            - developerPropertyOwner
            - other
          example: subContractor
        applicantName:
          type: string
          example: Tom George
          description: The applicant name
        creditLimit:
          type: number
          format: decimal
          example: 10000.00
          pattern: '[0-9]*\.[0-9]{2}'
          multipleOf: 0.01
          description: The credit limit
        outstandingBalance:
          type: number
          format: decimal
          example: 500.00
          pattern: '[0-9]*\.[0-9]{2}'
          multipleOf: 0.01
          description: The outstanding credit balance
        availableBalance:
          type: number
          format: decimal
          example: 9500.00
          pattern: '[0-9]*\.[0-9]{2}'
          multipleOf: 0.01
          description: The available credit balance
        processingAmount:
          type: number
          format: decimal
          example: 0.00
          pattern: '[0-9]*\.[0-9]{2}'
          multipleOf: 0.01
          description: The processing amount
        numberOfDraws:
          type: number
          format: int32
          example: 5
          description: Number of all draws attached to account (credit)
        accountStatus:
          type: string
          description: The account status (for approved borrowers only)
          enum:
            - goodStandingConnected
            - goodStandingManual
            - pastDue
            - onHold
            - inCollection
            - inactive
            - potentiallyFraud
            - closed
          example: goodStandingConnected
    AccountDetailed:
      type: object
      properties:
        id:
          type: string
          description: The account id
          example: 03b9b9c1-69c1-41ff-951a-074ed5fa0a3c
        companyId:
          type: string
          description: The company id
          example: 2d83d673-9b3f-4ace-a90c-107b0f23c9fd
        einHash:
          type: string
          description: The ein hash
          example: 332484gbaaefed
        businessName:
          type: string
          description: The business name
          example: Best Lumber
        accountStatus:
          type: string
          enum:
            - goodStandingConnected
            - goodStandingManual
            - pastDue
            - onHold
            - inCollection
            - inactive
            - potentiallyFraud
            - closed
          description: Statuses for Approved Borrowers only
        accountStatusText:
          type: string
          enum: 
            - CreditApplication.approved
            - BankAccount.connected
            - BankAccount.disconnected
            - BankAccount.manuallyAdded
            - Credit.pastDue
            - Credit.goodStanding
            - Company.creditRatingFails
            - CompanyOwners.creditRatingFails
            - Company.annualRevenueFails
            - Credit.inactivityCheckFails
            - Company.KYBFails
            - CompanyOwners.KYCFails
            - Credit.creditLimitChanged
          description: Statuses for Approved Borrowers only
        lastValidatedAt:
          type: string
          format: date-time
          nullable: true
          description: The last execution time of DEADRS
        creditDetails:
          $ref: '#/components/schemas/CreditDetails2'
        bankCreditStatus:
          $ref: '#/components/schemas/BankCreditStatusResult'
        bankAccounts:
          type: array
          items:
            $ref: "#/components/schemas/BankAccountItem2"
    CreditDetails2:
      type: object
      properties:
        id:
          type: string
          description: Id of the credit
          example: e86da27c-a0e3-451a-9bc6-dae50ed8e052
        purchaseType:
          type: string
          enum:
            - inventory
            - project
            - both
        creditLimit:
          type: number
          format: decimal
          example: 10000.00
          pattern: '[0-9]*\.[0-9]{2}'
          multipleOf: 0.01
          description: The credit limit
        outstandingBalance:
          type: number
          format: decimal
          example: 500.00
          pattern: '[0-9]*\.[0-9]{2}'
          multipleOf: 0.01
          description: The outstanding credit balance
        availableBalance:
          type: number
          format: decimal
          example: 9500.00
          pattern: '[0-9]*\.[0-9]{2}'
          multipleOf: 0.01
          description: The available credit balance
        numberOfDraws:
          type: number
          format: int32
          description: Number of all draws attached to account (credit)
          example: 5
        revenueFallPercentage:
          type: number
          format: decimal
          example: 80.00
          pattern: '[0-9]*\.[0-9]{2}'
          multipleOf: 0.01
          description: The revenue fall percentage (threshold)
    PatchCreditDetails:
      type: object
      properties:
        creditLimit:
          type: number
          format: decimal
          example: 10000.00
          pattern: '[0-9]*\.[0-9]{2}'
          multipleOf: 0.01
          description: The new credit limit.
        purchaseType:
          type: string
          enum:
            - inventory
            - project
            - both
          description: The new purchase type.
        revenueFallPercentage:
          type: number
          format: decimal
          example: 80.00
          pattern: '[0-9]*\.[0-9]{2}'
          multipleOf: 0.01
          description: The new revenue fall percentage.
        notes:
          type: string
          description: Notes of change
    Draw:
      type: object
      properties:
        id:
          type: string
          description: The draw (loan) id.
          example: 788905c0-8955-4f09-afb3-ac119805281a
        creditId:
          type: string
          description: The credit id.
          example: 1167eb9d-94a8-41f5-9cbc-a8498b9929fa
        companyId:
          type: string
          description: The company id.
          example: 9d3189947e858166
        einHash:
          type: string
          description: The ein hash
          example: 332484gbaaefed        
        type:
          type: string
          description: The type of draw
          enum:
            - regular
            - virtualCard
            - noSupplier
            - express
            - quote
          example: regular
        decisionDate:
          type: string
          description: The approval date
          format: date
        merchantName:
          type: string
          description: The merchant name
        amount:
          type: number
          format: decimal
          example: 10000.00
          pattern: '[0-9]*\.[0-9]{2}'
          multipleOf: 0.01
          description: The draw amount.
        nextPaymentAmount:
          type: number
          format: decimal
          example: 500.00
          pattern: '[0-9]*\.[0-9]{2}'
          multipleOf: 0.01
          nullable: true
          description: The next payment amount.
        nextPaymentDate:
          type: string
          format: date
          nullable: true
          example: 2024-04-25
          description: The next payment date.
        status:
          type: string
          enum:
            - Created
            - Started
            - Pending
            - Canceled
            - Closed
            - Defaulted
            - Recovered
            - Refinanced
          example: Started
          description: The draw status.
        payablesAmount:
          type: number
          format: decimal
          example: 10000.00
          pattern: '[0-9]*\.[0-9]{2}'
          multipleOf: 0.01
          description: The sum of invoices amount.
        payablesCount:
          type: number
          format: int32
          example: 5
          description: The count of invoices.
        payablesNumber:
          type: string
          nullable: true
          example: 2004/00001
          description: The number of invoices. Null if multiple.
    Project:
      type: object
      properties:
        id:
          type: string
          example: 36b9da96-7d24-436f-92aa-0a4e8742a3de
          description: The project id.
        companyId:
          type: string
          example: 7df1a7e9-938f-4cf7-9fc1-9402475c7795
          description: The project's company id.
        address:
          type: string
          example: 285 Fulton St, New York, NY 10007
          description: The project address.
        contractValue:
          type: number
          format: decimal
          example: 1000000.00
          pattern: '[0-9]*\.[0-9]{2}'
          multipleOf: 0.01
          description: The project contract amount. A spending limit.
        endDate:
          type: string
          format: date
          example: 2024-12-31
          description: The project's end date.
        jobId:
          type: string
          example: OWTC00001
          description: The project's job id, job number.
        creditDetails:
          $ref: "#/components/schemas/ProjectCreditDetails2"
    ProjectCreditDetails2:
      type: object
      properties:
        numberOfPayables:
          type: number
          format: int32
          example: 5
          description: Number of attached draws to the project
        availableCredit:
          type: number
          format: decimal
          example: 865000.00
          pattern: '[0-9]*\.[0-9]{2}'
          multipleOf: 0.01
          description: Available spending amount. Contract amount minus used amount. One of these fields is calculated (see contractValue)
        usedCredit:
          type: number
          format: decimal
          example: 135000.00
          pattern: '[0-9]*\.[0-9]{2}'
          multipleOf: 0.01
          description: Used spending amount. Sum of the attached invoices. One of these fields is calculated (see contractValue)
    PatchBankAccountCashFlow:
      type: object
      required: 
        - includeInCashFlow
      properties:
        includeInCashFlow:
          type: boolean
          example: true
          description: Determines is the bank account involved in cash flow calculations
    AddManualCashFlowResponse:
      type: object
      properties:
        result:
          type: boolean
        daysAvailableFrom:
          type: string
          format: date
        daysAvailableTo:
          type: string
          format: date
    ManuallyUploadedTransactionFile:
      type: object
      properties:
        accountId:
          type: string
        daysAvailableFrom:
          type: string
          format: date
        daysAvailableTo:
          type: string
          format: date
        fileName:
          type: string
          description: Filename to display on UI
        uploadedBy:
          type: string
        uploadedAt:
          type: string
          format: date-time
          description: The upload time
          example: 2024-03-08T12:49:24.907Z
    EmptyResponse:
      type: object
      nullable: true
    ErrorResponse:
      type: object
      nullable: true
  securitySchemes:
    ApiKey:
      type: apiKey
      in: header
      name: X-Api-Key
security:
  - ApiKey: []  
