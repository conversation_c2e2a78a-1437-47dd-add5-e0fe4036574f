﻿using System.Runtime.Serialization;
using System.Text.Json.Serialization;

namespace BlueTape.Aion.DataAccess.External.Models.InternalTransfer;

[DataContract]
public class BookTransferObj
{
    [JsonPropertyName("objectId")]
    public string ObjectId { get; set; } = null!;
    
    [JsonPropertyName("id")]
    public string Id { get; set; } = null!;
    
    [JsonPropertyName("amount")]
    public decimal Amount { get; set; }
    
    [JsonPropertyName("createdAt")]
    public string CreatedAt { get; set; } = null!;
    
    [JsonPropertyName("updatedAt")]
    public string UpdatedAt { get; set; } = null!;
    
    [JsonPropertyName("status")]
    public string Status { get; set; } = null!;
    
    [JsonPropertyName("description")]
    public string Description { get; set; } = null!;
    
    [JsonPropertyName("senderDescription")]
    public string SenderDescription { get; set; } = null!;
    
    [JsonPropertyName("receiverDescription")]
    public string ReceiverDescription { get; set; } = null!;
    
    [JsonPropertyName("userNote")]
    public string UserNote { get; set; } = null!;

    [JsonPropertyName("contextIdentifier")]
    public string ContextIdentifier { get; set; } = null!;

    [JsonPropertyName("statusMessage")]
    public string StatusMessage { get; set; } = null!;
    
    [JsonPropertyName("providerStatus")]
    public string ProviderStatus { get; set; } = null!;
    
    [JsonPropertyName("bankProvider")]
    public string BankProvider { get; set; } = null!;
    
    [JsonPropertyName("transactionType")]
    public string TransactionType { get; set; } = null!;
    
    [JsonPropertyName("lastModifiedAt")]
    public string LastModifiedAt { get; set; } = null!;
    
    [JsonPropertyName("railId")]
    public string RailId { get; set; } = null!;
    
    [JsonPropertyName("rail")]
    public string Rail { get; set; } = null!;
    
    [JsonPropertyName("proposedAt")]
    public string ProposedAt { get; set; } = null!;
    
    [JsonPropertyName("currency")]
    public string Currency { get; set; } = null!;
    
    [JsonPropertyName("traceNumber")]
    public string TraceNumber { get; set; } = null!;
    
    [JsonPropertyName("clientIdentifier")]
    public string ClientIdentifier { get; set; } = null!;
}