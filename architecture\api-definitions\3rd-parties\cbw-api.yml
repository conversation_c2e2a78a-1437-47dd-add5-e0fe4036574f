openapi: '3.0.0'
info:
  version: '1.0.0'
  title: 'CBW API'
  description: | 
    API definition of CBW banking services, made manually by documentation.

    No version number nor release date provided by CBW.

    Reference: https://apidocs.cbwpayments.com/

    **API endpoints just for separation, each request should call root.**
servers:
- url: https://sb.cbwpayments.com/gateway/rpc
  description: Sandbox
- url: https://gateway2.cbwpayments.com/rpc
  description: Production
paths:
  /ach/pull:
    post:
      tags:
        - ACH
      summary: Create ACH_PULL transaction
      description: This API is invoked to pull the instructed amount from the sender’s bank account.
      operationId: createAchPull
      requestBody:
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/AchRequestPullTransaction"
      responses:
        200:
          description: Successful response
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/AchResponse'
        404:
          description: Not found
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
  /ach/out:
    post:
      tags:
        - ACH
      summary: Create ACH_OUT transaction
      description: This API is used to send instructed amount to the beneficiary ‘s bank account.
      operationId: createAchOut
      requestBody:
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/AchRequestOutTransaction"
      responses:
        200:
          description: Successful response
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/AchResponse'
        404:
          description: Not found
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
  /ach/internal:
    post:
      tags:
        - ACH
      summary: Create INTERNAL transaction
      description: Transfer of funds internally within two accounts of the same customer.
      operationId: createInternal
      requestBody:
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/AchRequestInternalTransaction"
      responses:
        200:
          description: Successful response
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/AchResponse'
        404:
          description: Not found
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
  /ach/transactionstatus:
    post:
      tags:
        - ACH
      summary: Gets a transaction status
      description: This API is used to check the status of a particular transaction.
      operationId: getAchTransactionStatus
      requestBody:
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/AchRequestTransactionStatus"
      responses:
        200:
          description: Successful response
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/AchResponse'
        404:
          description: Not found
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
  /ach/balanceenquiry:
    post:
      tags:
        - ACH
      summary: Enquiries a balance
      description: This API is used to check the balance in an account.
      operationId: getBalanceEnquiry
      requestBody:
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/AchRequestBalanceEnquiry"
      responses:
        200:
          description: Successful response
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/AchBalanceEnquiryResult'
        404:
          description: Not found
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
  /ach/transactionhistory:
    post:
      tags:
        - ACH
      summary: Gets account transaction history in a date range.
      description: This API is used to check the transaction history of an account.
      operationId: getTransactionHistory
      requestBody:
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/AchRequestTransactionHistory"
      responses:
        200:
          description: Successful response
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/AchTransactionHistoryResult'
        404:
          description: Not found
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
components:
  schemas:
    AchRequestTransactionStatus:
      type: object
      required:
        - method
        - id
        - params
      properties:
        method:
          description: API name
          type: string
          enum:
            - ledger.ach.transfer
            - ledger.admin.request
          example: ledger.ach.transfer
        id:
          description: Unique ID for Request
          type: string
          example: 1
        params:
          $ref: '#/components/schemas/AchParamsTransactionStatus'
    AchParamsTransactionStatus:
      type: object
      properties: 
        payload:
          $ref: '#/components/schemas/AchTransactionStatusPayload'
        api:
          $ref: '#/components/schemas/AuthPayload'
    AchRequestPullTransaction:
      type: object
      required:
        - method
        - id
        - params
      properties:
        method:
          description: API name
          type: string
          enum:
            - ledger.ach.transfer
            - ledger.admin.request
          example: ledger.ach.transfer
        id:
          description: Unique ID for Request
          type: string
          example: 1
        params:
          $ref: '#/components/schemas/AchParamsPullTransaction'
    AchParamsPullTransaction:
      type: object
      properties: 
        payload:
          $ref: '#/components/schemas/AchPullParamsPayload'
        api:
          $ref: '#/components/schemas/AuthPayload'
    AchPullParamsPayload:
      type: object
      required:
        - channel
        - transactionType
        - product
        - program
        - transactionDateTime
        - reference
        - transactionAmount
        - debtor
        - debtorPostalAddress
        - debtorContact
        - debtorAccount
        - creditorAccount
      properties:
        channel:
          description: Channel of transaction
          type: string
          example: ACH
        transactionType:
          description: Type of transaction
          type: string
          enum:
            - ACH_PULL
            - ACH_OUT
            - INTERNAL_TRANSFER
            - TRANSACTION_STATUS
            - BALANCE_ENQUIRY
            - TRANSACTION_HISTORY
            - FEE
          example: ACH_PULL
        product:
          description: Product in the transaction
          type: string
          example: LEDGER
        program:
          description: Program in the transaction
          type: string
          example: LEDGER
        transactionDateTime:
          description: Transaction timestamp (format YYYY-MM-DD-HH-MM-SS)
          type: string
          format: date
          example: 2021-01-19 06:20:25
        reference:
          description: Unique reference ID of transaction request
          type: string
          example: REF1617049508172:0
        reason:
          description: Reason for the transaction
          type: string
          example: Settlement
        transactionAmount:
          $ref: '#/components/schemas/TransactionAmount'
        debtor:
          $ref: '#/components/schemas/TransactionParticipant'
        debtorPostalAddress:
          $ref: '#/components/schemas/PostalAddress'
        debtorContact:
          $ref: '#/components/schemas/Contact'
        debtorAccount:
          $ref: '#/components/schemas/Account'
        creditorAccount:
          $ref: '#/components/schemas/Account'
    AchRequestOutTransaction:
      type: object
      required:
        - method
        - id
        - params
      properties:
        method:
          description: API name
          type: string
          enum:
            - ledger.ach.transfer
            - ledger.admin.request
          example: ledger.ach.transfer
        id:
          description: Unique ID for Request
          type: string
          example: 1
        params:
          $ref: '#/components/schemas/AchParamsOutTransaction'
    AchParamsOutTransaction:
      type: object
      properties: 
        payload:
          $ref: '#/components/schemas/AchOutParamsPayload'
        api:
          $ref: '#/components/schemas/AuthPayload'    
    AchOutParamsPayload:
      type: object
      required:
        - channel
        - transactionType
        - product
        - program
        - transactionDateTime
        - reference
        - transactionAmount
        - debtorAccount
        - creditor
        - creditorPostalAddress
        - creditorContact
        - creditorAccount
      properties:
        channel:
          description: Channel of transaction
          type: string
          example: ACH
        transactionType:
          description: Type of transaction
          type: string
          enum:
            - ACH_PULL
            - ACH_OUT
            - INTERNAL_TRANSFER
            - TRANSACTION_STATUS
            - BALANCE_ENQUIRY
            - TRANSACTION_HISTORY
            - FEE
          example: ACH_OUT
        product:
          description: Product in the transaction
          type: string
          example: LEDGER
        program:
          description: Program in the transaction
          type: string
          example: LEDGER
        transactionDateTime:
          description: Transaction timestamp (format YYYY-MM-DD-HH-MM-SS)
          type: string
          format: date
          example: 2021-01-19 06:20:25
        reference:
          description: Unique reference ID of transaction request
          type: string
          example: REF1617049508172:0
        reason:
          description: Reason for the transaction
          type: string
          example: Settlement
        transactionAmount:
          $ref: '#/components/schemas/TransactionAmount'
        debtorAccount:
          $ref: '#/components/schemas/Account'
        creditor:
          $ref: '#/components/schemas/TransactionParticipant'
        creditorPostalAddress:
          $ref: '#/components/schemas/PostalAddress'
        creditorContact:
          $ref: '#/components/schemas/Contact'
        creditorAccount:
          $ref: '#/components/schemas/Account'
    TransactionAmount:
      type: object
      required:
        - amount
        - currency
      properties:
        amount:
          description: Transaction amount (String??)
          type: string
          example: 2000
        currency:
          description: Currency in the transaction
          type: string
          example: USD
    TransactionParticipant:
      type: object
      required:
        - userType
        - identification
        - identificationType
        - firstName
        - middleName
        - lastName
      properties:
        userType:
          description: Type of debtor
          type: string
          example: INDIVIDUAL
        identification:
          description: Identification number of debtor
          type: string
          example: *********
        identificationType:
          description: Identification type of the debtor
          type: string
          example: SSN
        firstName:
          description: First name of the debtor
          type: string
          example: Austin
        middleName:
          description: Middle name of the debtor
          type: string
          example: James
        lastName:
          description: Last name of the debtor
          type: string
          example: Wesley
    PostalAddress:
      type: object
      required:
        - addressType
        - addressLine1
        - addressLine2
        - city
        - state
        - zipCode
        - countryCode
      properties:
        addressType:
          description: Address type of debtor
          type: string
          example: HOUSE
        addressLine1:
          description: Line 1 in address of debtor
          type: string
          example: 200 E
        addressLine2:
          description: Line 2 in address of debtor
          type: string
          example: Main Street
        city:
          description: City in address of debtor
          type: string
          example: PHOENIX
        state:
          description: State in address of debtor
          type: string
          example: AZ
        zipCode:
          description: Zip code in address of debtor
          type: string
          example: 85123
        countryCode:
          description: 3 digit country code of address of debtor
          type: string
          example: 840
    Contact:
      type: object
      required:
        - primaryEmail
        - primaryPhone
      properties:
        primaryEmail:
          description: Primary email ID of debtor
          type: string
          example: <EMAIL>
        primaryPhone:
          description: Primary phone number of debtor
          type: string
          example: **********
    Account:
      type: object
      required:
        - identification
        - identificationType
      properties:
        identification:
          description: Debtor/creditor account identification number
          type: string
          example: ****************
        identificationType:
          description: Type 1 of identification - debtor account number
          type: string
          example: ACCOUNT_NUMBER
        identificationType2:
          description: Type 2 of identification (type of account) |
            Mandatory on ACH_PULL (Debtor), on ACH_OUT (Creditor)
          type: string
          enum:
            - CHECKING
            - SAVINGS
          example: CHECKING
        institution:
          description: Mandatory on ACH_PULL (Debtor), on ACH_OUT (Creditor)
          $ref: '#/components/schemas/DebtorAccountInstitution'
    DebtorAccountInstitution:
      type: object
      required:
        - name
        - identification
        - identificationType
      properties:
        name:
          description: Name of debtor bank
          type: string
          example: ACME BANK
        identification:
          description: Unique Identification number of debtor bank
          type: string
          example: *********
        identificationType:
          description: Type of identification of debtor bank
          type: string
          example: ABA
    CreditorAccount:
      type: object
      required:
        - identification
        - identificationType
      properties:
        identification:
          description: Creditor account number
          type: string
          example: ***************
        identificationType:
          description: Creditor account type
          type: string
          example: ACCOUNT_NUMBER
    AuthPayload:
      type: object
      required:
        - type
        - credential
        - signature
        - apiKey
      properties:
        type:
          description: Type of transaction request in API
          type: string
          example: TRANSFER
        credential:
          description: Property of authentication
          type: string
          example: Basic ************************************************************************************************************
        signature:
          description: Property of authentication
          type: string
          example: MIGUAkgA6RrNvnEnWVBLb+S5NeXefLaRRxUsCorWjl6GCem5HrscP5H8NYBMSowN8avQvut30qmWQhwm6Ff/0G7Tw1bZoXv8dt4BthMCSAMcj/eSbhRaX+RdCn+oyecdC1aFu1THYWyhG0OYZnvk+3OUNAZfBSU+NNti3aDlr2VakpXEUWtdAJkO3rSTsSWPun9nYld85Q==
        apiKey:
          description: Property of authentication
          type: string
          example: 4895034b29389d29875359e0d4c4d3b51866d7de77aab79da152967083668ddf
    AchResponse:
      type: object
      properties:
        id:
          description: Unique response ID
          type: string
          example: 1
        result:
          $ref: '#/components/schemas/AchResult'
    AchBalanceEnquiryResult:
      type: object
      properties:
        id:
          description: Unique response ID
          type: string
          example: 1
        result:
          $ref: '#/components/schemas/AchResultBalanceEnquiry'
    AchResult:
      type: object
      properties:
        api:
          $ref: '#/components/schemas/TransactionReference'
        account:
          $ref: '#/components/schemas/AccountBalance'
        transactionNumber:
          description: The status of the account
          type: string
          example: ACTIVE
        transactionStatus:
          description: Description of status
          type: string
          example: PENDING
        transactionAmountCents:
          description: Amount transferred in Cents
          type: number
          example: 100
    AchResultBalanceEnquiry:
      type: object
      properties:
        api:
          $ref: '#/components/schemas/TransactionReference'
        account:
          $ref: '#/components/schemas/AccountBalance'
    AchTransactionHistoryResult:
      type: object
      properties:
        id:
          description: Unique response ID
          type: string
          example: 1
        result:
          $ref: '#/components/schemas/AchTransactionHistory'
    AchTransactionHistory:
      type: object
      properties:
        api:
          $ref: '#/components/schemas/TransactionReference'
        query:
          $ref: '#/components/schemas/DateRangeParam'
        totalCount:
          description: Total Count
          type: number
          example: 10
        transactions:
          type: array
          items:
            $ref: '#/components/schemas/TransactionHistoryItem'
    TransactionHistoryItem:
      type: object
      properties:
        date:
          description: Date and time of transaction in milliseconds
          type: string
          format: date
          example: 2021-07-26T07:49:47+0000
        updatedDate:
          description: Date and time of transaction updated in milliseconds
          type: string
          format: date
          example: 2021-07-26T07:49:48+0000
        transactionType:
          description: Type of transaction
          type: string
          enum:
            - ACH_PULL
            - ACH_OUT
            - INTERNAL_TRANSFER
            - TRANSACTION_STATUS
            - BALANCE_ENQUIRY
            - TRANSACTION_HISTORY
            - FEE
          example: FEE
        transactionNumber:
          description: Transaction number
          type: string
          example: DEV1566187918001
        originalTransactionNumber:
          description: Original transaction number
          type: string
          example: DEV1566187918000
        referenceNumber:
          description: Original reference number
          type: string
          example: 1627285783909
        transactionAmount:
          description: Amount transferred in Cents (String??? Why have decimal fraction if cents?)
          type: string
          example: 2.00
        transactionCurrencyCode:
          description: Code of transaction currency
          type: string
          example: 840
        status:
          description: Description of status
          type: string
          example: VERIFIED
        transactionStatus:
          description: Status for transaction
          type: string
          example: PROCESSED
        reason:
          description: Reason for transaction
          type: string
          example: Settlement
        debtor:
          $ref: '#/components/schemas/TransactionParticipant'
        debtorAccount:
          $ref: '#/components/schemas/Account'
        creditor:
          $ref: '#/components/schemas/TransactionParticipant'
        creditorAccount:
          $ref: '#/components/schemas/Account'
    TransactionReference:
      type: object
      properties:
        type:
          description: Type of response
          type: string
          enum:
            - ACH_PULL_ACK
            - ACH_OUT_ACK
            - INTERNAL_TRANSFER_ACK
            - TRANSACTION_STATUS_ACK
            - BALANCE_ENQUIRY_ACK
            - TRANSACTION_STATUS_ACK
            - TRANSACTION_HISTORY_ACK
          example: ACH_PULL_ACK/ACH_OUT_ACK/INTERNAL_TRANSFER_ACK
        reference:
          description: Unique reference number provided by the client in the request
          type: string
          example: REF1621236391959
        dateTime:
          description: Date and time of transaction in milliseconds
          type: string
          format: date
          example: 2021-05-17 02:33:13
        originalReference:
          description: Original reference number provided by the client in the request
          type: string
          example: 393965490567122MCPULL2021052300000017
    AccountBalance:
      type: object
      properties:
        accountId:
          description: The unique account ID
          type: string
          example: ***************
        balanceCents:
          description: The amount available in the accountId
          type: number
          example: 5754646
        holdBalanceCents:
          description: Balance Amount in Cents
          type: number
          example: 7150
        status:
          description: The status of the account
          type: string
          example: ACTIVE
    AchTransactionStatusPayload:
      type: object
      required:
        - reference
        - transactionType
        - originalReference
        - originalExternalReference
        - debtorAccount
        - product
        - channel
        - program
      properties:
        reference:
          description: Unique reference ID of transaction request
          type: string
          example: REF1617049508172:0
        transactionType:
          description: Type of transaction
          type: string
          enum:
            - ACH_PULL
            - ACH_OUT
            - INTERNAL_TRANSFER
            - TRANSACTION_STATUS
            - BALANCE_ENQUIRY
          example: TRANSACTION_STATUS
        originalReference:
          description: The original reference of the transaction for which status is sought
          type: string
          example: LEDGER1558130492492
        originalExternalReference:
          description: The original external reference of the transaction for which status is sought
          type: string
          example: REF1617316443042
        debtorAccount:
          $ref: '#/components/schemas/Account'
        product:
          description: Product in the transaction
          type: string
          example: LEDGER
        channel:
          description: Channel of transaction
          type: string
          example: ACH
        program:
          description: Program in the transaction
          type: string
          example: LEDGER
    AchRequestBalanceEnquiry:
      type: object
      required:
        - method
        - id
        - params
      properties:
        method:
          description: API name
          type: string
          enum:
            - ledger.ach.transfer
            - ledger.admin.request
          example: ledger.ach.transfer
        id:
          description: Unique ID for Request
          type: string
          example: 1
        params:
          $ref: '#/components/schemas/AchParamsBalanceEnquiry'
    AchParamsBalanceEnquiry:
      type: object
      properties: 
        payload:
          $ref: '#/components/schemas/AchBalanceEnquiryPayload'
        api:
          $ref: '#/components/schemas/AuthPayload'
    AchBalanceEnquiryPayload:
      type: object
      required:
        - reference
        - transactionType
        - originalReference
        - originalExternalReference
        - debtorAccount
        - product
        - channel
        - program
      properties:
        reference:
          description: Unique reference ID of transaction request
          type: string
          example: REF1617049508172:0
        transactionType:
          description: Type of transaction
          type: string
          enum:
            - ACH_PULL
            - ACH_OUT
            - INTERNAL_TRANSFER
            - TRANSACTION_STATUS
            - BALANCE_ENQUIRY
          example: BALANCE_ENQUIRY
        originalReference:
          description: The original reference of the transaction for which status is sought
          type: string
          example: LEDGER1558130492492
        originalExternalReference:
          description: The original external reference of the transaction for which status is sought
          type: string
          example: REF1617316443042
        debtorAccount:
          $ref: '#/components/schemas/Account'
        product:
          description: Product in the transaction
          type: string
          example: LEDGER
        channel:
          description: Channel of transaction
          type: string
          example: ADMIN
        program:
          description: Program in the transaction
          type: string
          example: ***************
    AchRequestTransactionHistory:
      type: object
      required:
        - method
        - id
        - params
      properties:
        method:
          description: API name
          type: string
          enum:
            - ledger.ach.transfer
            - ledger.admin.request
          example: ledger.ach.transfer
        id:
          description: Unique ID for Request
          type: string
          example: 1
        params:
          $ref: '#/components/schemas/AchParamsTransactionHistory'
    AchParamsTransactionHistory:
      type: object
      properties: 
        payload:
          $ref: '#/components/schemas/AchTransactionHistoryPayload'
        api:
          $ref: '#/components/schemas/AuthPayload'
    AchTransactionHistoryPayload:
      type: object
      required:
        - reference
        - transactionType
        - debtorAccount
        - product
        - channel
        - program
      properties:
        reference:
          description: Unique reference ID of transaction request
          type: string
          example: REF1617049508172:0
        transactionType:
          description: Type of transaction
          type: string
          enum:
            - ACH_PULL
            - ACH_OUT
            - INTERNAL_TRANSFER
            - TRANSACTION_STATUS
            - BALANCE_ENQUIRY
            - TRANSACTION_HISTORY
          example: TRANSACTION_HISTORY
        query:
          $ref: '#/components/schemas/DateRangeParam'
        debtorAccount:
          $ref: '#/components/schemas/Account'
        product:
          description: Product in the transaction
          type: string
          example: LEDGER
        channel:
          description: Channel of transaction
          type: string
          example: ADMIN
        program:
          description: Program in the transaction
          type: string
          example: ***************
    DateRangeParam:
      type: object
      required:
        - dateFrom
        - dateTo
        - pagination
      properties:
        dateFrom:
          description: Transaction history start date (in YYYY-MM-DD format)
          type: string
          format: date
          example: 2021-08-20
        dateTo:
          description: Transaction history end date (in format YYYY-MM-DD)
          type: string
          format: date
          example: 2021-08-21
        pagination:
          $ref: '#/components/schemas/PaginationParam'
    PaginationParam:
      type: object
      required:
        - offset
      properties:
        offset:
          description: Transaction history details record count
          type: number
          example: 0
    Success:
      type: object
    Error:
      type: object
      properties:
        id:
          description: Unique error response ID
          type: string
          example: 1
        error:
          $ref: "#/components/schemas/ErrorDetails"
    ErrorDetails:
      type: object
      properties:
        code:
          type: string
          description: Indicates the type of error
          example: A112
        message:
          type: string
          description: The error message
          example: INVALID_AMOUNT
        data:
          $ref: "#/components/schemas/ErrorData"
    ErrorData:
      type: object
      properties:
        type:
          type: string
          description: Type of response
          example: ACH_PULL_ACK/ACH_OUT_ACK/INTERNAL_TRANSFER_ACK
        reference:
          type: string
          description: Unique reference number provided by the client in the request
          example: REF1621330734154
        dateTime:
          type: string
          format: date-time
          description: Date and time of transaction in milliseconds
          example: 2021-05-18 04:38:54
        originalReference:
          type: string
          description: Original reference number provided by the client in the request
          example: REF1617049508172
    AchRequestInternalTransaction:
      type: object
      required: 
        - method
        - id
        - params
      properties:
        method:
          description: API name
          type: string
          enum:
            - ledger.ach.transfer
            - ledger.admin.request
            - ledger.internal.transfer
          example: ledger.internal.transfer
        id:
          description: Unique ID for Request
          type: string
          example: 1
        params:
          $ref: '#/components/schemas/AchParamsInternalTransaction'
    AchParamsInternalTransaction:
      type: object
      properties: 
        payload:
          $ref: '#/components/schemas/AchInternalParamsPayload'
        api:
          $ref: '#/components/schemas/AuthPayload'
    AchInternalParamsPayload:
      type: object
      required:
        - channel
        - transactionType
        - product
        - program
        - transactionDateTime
        - reference
        - transactionAmount
        - debtorAccount
        - creditor
        - creditorPostalAddress
        - creditorContact
        - creditorAccount
      properties:
        channel:
          description: Channel of transaction
          type: string
          example: INTERNAL
        transactionType:
          description: Type of transaction
          type: string
          enum:
            - ACH_PULL
            - ACH_OUT
            - INTERNAL_TRANSFER
            - TRANSACTION_STATUS
            - BALANCE_ENQUIRY
            - TRANSACTION_HISTORY
            - FEE
          example: INTERNAL_TRANSFER
        product:
          description: Product in the transaction
          type: string
          example: LEDGER
        program:
          description: Program in the transaction
          type: string
          example: LEDGER
        transactionDateTime:
          description: Transaction timestamp (format YYYY-MM-DD-HH-MM-SS)
          type: string
          format: date
          example: 2021-01-19 06:20:25
        reference:
          description: Unique reference ID of transaction request
          type: string
          example: REF1617049508172:0
        reason:
          description: Reason for the transaction
          type: string
          example: Settlement
        transactionAmount:
          $ref: '#/components/schemas/TransactionAmount'
        debtorAccount:
          $ref: '#/components/schemas/Account'
        creditorAccount:
          $ref: '#/components/schemas/Account'