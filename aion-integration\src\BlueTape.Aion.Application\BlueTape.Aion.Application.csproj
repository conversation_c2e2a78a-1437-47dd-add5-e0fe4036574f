﻿<Project Sdk="Microsoft.NET.Sdk">

    <PropertyGroup>
        <TargetFramework>net8.0</TargetFramework>
        <ImplicitUsings>enable</ImplicitUsings>
        <Nullable>enable</Nullable>
    </PropertyGroup>

    <ItemGroup>
        <PackageReference Include="BlueTape.CompanyClient" Version="1.0.51" />
        <PackageReference Include="BlueTape.Integrations.Aion" Version="1.0.20" />
        <PackageReference Include="BlueTape.Integrations.Aion.AzureTableStorage" Version="1.1.3" />
        <PackageReference Include="BlueTape.SNS" Version="1.0.2" />
        <PackageReference Include="BlueTape.AWSS3" Version="1.1.3" />
        <PackageReference Include="BlueTape.AzureKeyVault" Version="1.0.3" />
        <PackageReference Include="BlueTape.ServiceBusMessaging" Version="1.0.8" />
		<PackageReference Include="BlueTape.Utilities" Version="1.4.6" />
    </ItemGroup>
    
    <ItemGroup>
        <PackageReference Include="AutoMapper" Version="12.0.1" />
        <PackageReference Include="AutoMapper.Extensions.Microsoft.DependencyInjection" Version="12.0.1" />
        <PackageReference Include="AWSSDK.SimpleNotificationService" Version="3.7.300.25" />
        <PackageReference Include="System.IdentityModel.Tokens.Jwt" Version="8.0.2" />
    </ItemGroup>

    <ItemGroup>
      <ProjectReference Include="..\BlueTape.Aion.DataAccess.External\BlueTape.Aion.DataAccess.External.csproj" />
    </ItemGroup>

    <ItemGroup>
      <Folder Include="Models\Wire\" />
    </ItemGroup>
    
</Project>
