import {Page} from "@playwright/test";
import {BasePage} from "../../../base.page";

export class ProjectInformation extends BasePage {
    constructor(page: Page) {
        super(page);
    };

    containers = {};

    buttons = {
        next: this.page.locator('_react=[testID="ProjectNextButton"]'),
        back: this.page.locator('_react=[testID="ProjectBackButton"]'),
    };

    input = {
        contractValue: this.page.locator('_react=l[testID="projectInfo_contractValue"] >> _react=TextInput'),
        firstDayOnTheJob: this.page.locator('//div[text()=\'First day on the job\']/following-sibling::div//input[@placeholder]'),
        expectedLastDateOnTheJob: this.page.locator('//div[text()=\'Expected last day on the job\']/following-sibling::div//input[@placeholder]').first(),
        businessName: this.page.locator('_react=l[testID="businessName"] >> _react=TextInput'),
        firstName: this.page.locator('_react=l[testID="firstName"] >> _react=TextInput'),
        lastName: this.page.locator('_react=l[testID="lastName"] >> _react=TextInput'),
        contractorPhone: this.page.locator('_react=l[testID="contractorPhone"] >> _react=TextInput'),
        contractorEmail: this.page.locator('_react=l[testID="contractorEmail"] >> _react=TextInput'),
        jobId: this.page.locator('_react=He[label="Business Address"] >> _react=TextInput'),
    };

    elements = {
        roleInput: this.page.locator('[data-testid="RoleInput"]'),
    };

    rolesOnTheProject = {
        primeContractor: this.page.locator('[data-testid="RoleInput-0"]'),
        subContractor: this.page.locator('[data-testid="RoleInput-1"]'),
        contractorForAnotherSubcontractor: this.page.locator('[data-testid="RoleInput-2"]'),
        materialSupplier: this.page.locator('[data-testid="RoleInput-3"]'),
        propertyDeveloper: this.page.locator('[data-testid="RoleInput-4"]'),
        owner: this.page.locator('[data-testid="RoleInput-5"]'),
        tenant: this.page.locator('[data-testid="RoleInput-6"]'),
    };

    async fillRequiredFields(contractAmount: string, firstDate: string, expectedLastDate: string) {
        await this.input.contractValue.fill(contractAmount);
        await this.input.firstDayOnTheJob.fill(firstDate);
        await this.input.expectedLastDateOnTheJob.fill(expectedLastDate);
    };

    async chooseRole(element) {
        await this.elements.roleInput.click();
        await element.click();
    };

    clickRoleAndSelect = {
        primeContractor: async () => await this.chooseRole(this.rolesOnTheProject.primeContractor),
        subContractor: async () => await this.chooseRole(this.rolesOnTheProject.subContractor),
        contractorForAnotherSubcontractor: async () => await this.chooseRole(this.rolesOnTheProject.contractorForAnotherSubcontractor),
        materialSupplier: async () => await this.chooseRole(this.rolesOnTheProject.materialSupplier),
        propertyDeveloper: async () => await this.chooseRole(this.rolesOnTheProject.propertyDeveloper),
        owner: async () => await this.chooseRole(this.rolesOnTheProject.owner),
        tenant: async () => await this.chooseRole(this.rolesOnTheProject.tenant),
    };
}