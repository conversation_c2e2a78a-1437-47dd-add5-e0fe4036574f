﻿using MongoDB.Bson.Serialization.Attributes;

namespace BlueTape.Aion.DataAccess.MongoDB.Entities.BanksAccounts;

[BsonIgnoreExtraElements]
public class BankAccountAionSettingsEntity
{
    [BsonElement("blueTapeAccountId")]
    public string? BlueTapeAccountId { get; set; }

    [BsonElement("transferMethodId")]
    public string? TransferMethodId { get; set; }

    [BsonElement("transferMethodId2")]
    public string? TransferMethodId2 { get; set; }

    [BsonElement("transferMethodId3")]
    public string? TransferMethodId3 { get; set; }
    
    [BsonElement("wireTransferMethodId")]
    public string WireTransferMethodId { get; set; }
    
    [BsonElement("wireTransferMethodId2")]
    public string WireTransferMethodId2 { get; set; }
    
    [BsonElement("wireTransferMethodId3")]
    public string WireTransferMethodId3 { get; set; }
    
    [BsonElement("instantTransferMethodId")]
    public string InstantTransferMethodId { get; set; }
    
    [BsonElement("instantTransferMethodId2")]
    public string InstantTransferMethodId2 { get; set; }
    
    [BsonElement("instantTransferMethodId3")]
    public string InstantTransferMethodId3 { get; set; }
}