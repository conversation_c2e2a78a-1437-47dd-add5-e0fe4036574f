@startuml

title KYB step

participant "Previous Step" as ps #LightGray
participant "KYB" as kyb #LightGray
participant "OnBoarding\nService" as onbs #SkyBlue
database "Mongo" as db #SkyBlue
participant "LexisNexis/CBW\nintegration" as lni #LightGray
participant "LexisNexis/CBW" as ln #Orange

autonumber

ps -> kyb
== Event Wrapper ==
kyb -> onbs : Check loan application
kyb -> onbs : Create draft if not exists
kyb -> onbs : Updating progress
kyb -> onbs : Save previous outputs
== KYB step ==
kyb --> kyb : Map business owner and all the co-owners
kyb -> onbs : Legacy co-owners function
kyb --> kyb : Map data to request
kyb -> lni : Gets business instant id
lni -> ln : Forwards request
ln --> lni
lni --> kyb


@enduml