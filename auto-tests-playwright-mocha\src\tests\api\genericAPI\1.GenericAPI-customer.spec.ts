import {expect} from '@playwright/test';
import {test} from '../../test-utils';
import {sendGenericApiRequest, generateCustomerRequestBody} from '../../../api/common/send-generic-api-request';
import {BaseTest} from '../../test-utils';
import {wipeCustomerFields} from "../../../database/customers/customer-fields-wiper";

const constantsError = JSON.parse(JSON.stringify(require('../../../constants/generic-api-errors.json')));
const constants = JSON.parse(JSON.stringify(require('../../../constants/constants.json')));

test.describe(`Customer tests @generic @API`, async () => {
    let firstCustomerEmailAddress: string;
    let firstCustomerCellPhoneNumber: string;
    let firstCustomerId: string;
    let uniqueEmail: string;
    let uniquePhone: string;
    let uniqueCustomerId: string;
    let blueTapeIdString: string;

    test.beforeEach(async () => {
        uniqueEmail = `sergei+genericapicustomer${BaseTest.dateTimePrefix()}@bluetape.com`;
        uniquePhone = BaseTest.getCellPhoneNumber();
        uniqueCustomerId = `${BaseTest.dateTimePrefix()}`;
    });

    test.afterAll(async () => {
        await wipeCustomerFields(blueTapeIdString);
    });

    test(`Create Customer with unique id, phone, email`, async () => {
        firstCustomerEmailAddress = `sergei+genericapicustomer${BaseTest.dateTimePrefix()}@bluetape.com`;
        firstCustomerCellPhoneNumber = BaseTest.getCellPhoneNumber();
        firstCustomerId = `${BaseTest.dateTimePrefix()}`;
        const requestBody = await generateCustomerRequestBody(constants.generic.firstName, constants.generic.lastName, firstCustomerCellPhoneNumber, firstCustomerEmailAddress, firstCustomerId);
        const response = await sendGenericApiRequest('post', `customer`, requestBody);
        blueTapeIdString = await response.data.blueTapeId;

        expect(response.status, `Status code 201`).toEqual(201);

        expect(response.data,
            `Response contains Array of Template's Versions`).toEqual(expect.any(Object));
    });

    test(`Cannot create Customer with non-unique ID`, async () => {
        const requestBody = await generateCustomerRequestBody(constants.generic.firstName, constants.generic.lastName, uniquePhone, uniqueEmail, firstCustomerId);
        const response = await sendGenericApiRequest('post', `customer`, requestBody);

        expect(response.response.status, `Status code 400`).toEqual(400);

        //expect(response.response.data, `Response contains Array of Template's Versions`).toEqual(expect.any(Object)); //todo check

        expect(response.response.data[0].code,
            `Error message: ${constantsError.customer.code.errorMessageNonUnicId}`)
            .toEqual(constantsError.customer.code.errorMessageNonUnicId);
    });

    test(`Cannot create Customer with non-unique Phone number`, async () => {
        const response = await sendGenericApiRequest('post', `customer`, await generateCustomerRequestBody(constants.generic.firstName, constants.generic.lastName, firstCustomerCellPhoneNumber, uniqueEmail, uniqueCustomerId));

        expect(response.response.status, `Status code 400`).toEqual(400);

        expect(response.response.data,
            `Response contains Object`).toEqual(expect.any(Object));

        expect(response.response.data[0].code,
            `Error message: ${constantsError.customer.code.errorMessageNonUnicPhoneOrEmail}`)
            .toEqual(constantsError.customer.code.errorMessageNonUnicPhoneOrEmail);
    });

    test(`Cannot create Customer with a non-unique Email`, async () => {
        const response = await sendGenericApiRequest('post', `customer`, await generateCustomerRequestBody(constants.generic.firstName, constants.generic.lastName, uniquePhone, firstCustomerEmailAddress, uniqueCustomerId));

        expect(response.response.status, `Status code 400`).toEqual(400);

        expect(response.response.data,
            `Response contains Object`).toEqual(expect.any(Object));

        expect(response.response.data[0].code,
            `Error message: ${constantsError.customer.code.errorMessageNonUnicPhoneOrEmail}`)
            .toEqual(constantsError.customer.code.errorMessageNonUnicPhoneOrEmail);
    });

    test(`Cannot create Customer with null in the first name field`, async () => {
        const response = await sendGenericApiRequest('post', `customer`, await generateCustomerRequestBody(null, constants.generic.lastName, uniquePhone, uniqueEmail, uniqueCustomerId));

        expect(response.response.status, `Status code 400`).toEqual(400);

        expect(response.response.data,
            `Response contains Object`).toEqual(expect.any(Object));

        expect(response.response.data[0].code,
            `Error message: ${constantsError.customer.code.errorMessageInvalidModelState}`)
            .toEqual(constantsError.customer.code.errorMessageInvalidModelState);

        expect(response.response.data[0].reason,
            `Error message: ${constantsError.customer.reason.errorWithIgnoredFirstName}`)
            .toEqual(constantsError.customer.reason.errorWithIgnoredFirstName);
    });

    test(`Cannot create Customer with empty string in the first name field`, async () => {
        const response = await sendGenericApiRequest('post', `customer`, await generateCustomerRequestBody('', constants.generic.lastName, uniquePhone, uniqueEmail, uniqueCustomerId));

        expect(response.response.status, `Status code 400`).toEqual(400);

        expect(response.response.data,
            `Response contains Object`).toEqual(expect.any(Object));

        expect(response.response.data[0].code,
            `Error message: ${constantsError.customer.code.errorMessageInvalidData}`)
            .toEqual(constantsError.customer.code.errorMessageInvalidData);

        expect(response.response.data[0].reason,
            `Error message: ${constantsError.customer.reason.errorWithEmptyFirstName}`)
            .toEqual(constantsError.customer.reason.errorWithEmptyFirstName);
    });

    test(`Cannot create Customer with null in last name field`, async () => {
        const response = await sendGenericApiRequest('post', `customer`, await generateCustomerRequestBody(constants.generic.firstName, null, uniquePhone, uniqueEmail, uniqueCustomerId));

        expect(response.response.status, `Status code 400`).toEqual(400);

        expect(response.response.data,
            `Response contains Object`).toEqual(expect.any(Object));

        expect(response.response.data[0].code,
            `Error message: ${constantsError.customer.code.errorMessageInvalidModelState}`)
            .toEqual(constantsError.customer.code.errorMessageInvalidModelState);

        expect(response.response.data[0].reason,
            `Error message: ${constantsError.customer.reason.errorWithIgnoredLastName}`)
            .toEqual(constantsError.customer.reason.errorWithIgnoredLastName);
    });

    test(`Cannot create Customer with empty string in the last name`, async () => {
        const response = await sendGenericApiRequest('post', `customer`, await generateCustomerRequestBody(constants.generic.firstName, '', uniquePhone, uniqueEmail, uniqueCustomerId));

        expect(response.response.status, `Status code 400`).toEqual(400);

        expect(response.response.data,
            `Response contains Object`).toEqual(expect.any(Object));

        expect(response.response.data[0].code,
            `Error message: ${constantsError.customer.code.errorMessageInvalidData}`)
            .toEqual(constantsError.customer.code.errorMessageInvalidData);

        expect(response.response.data[0].reason,
            `Error message: ${constantsError.customer.reason.errorWithEmptyLastName}`).toEqual(constantsError.customer.reason.errorWithEmptyLastName);
    });

    test(`Cannot create Customer with invalid Phone Number`, async () => {
        const response = await sendGenericApiRequest('post', `customer`, await generateCustomerRequestBody(constants.generic.firstName, constants.generic.lastName, constants.generic.invalidCellPhoneNumber, uniqueEmail, uniqueCustomerId));

        expect(response.response.status, `Status code 400`).toEqual(400);

        expect(response.response.data,
            `Response contains Object`).toEqual(expect.any(Object));

        expect(response.response.data[0].code,
            `Error message: ${constantsError.customer.code.errorMessageInvalidData}`)
            .toEqual(constantsError.customer.code.errorMessageInvalidData);

        expect(response.response.data[0].reason,
            `Error message: ${constantsError.customer.reason.errorWithInvalidPhoneNumber} +${constants.generic.invalidCellPhoneNumber}`)
            .toEqual(`${constantsError.customer.reason.errorWithInvalidPhoneNumber} +${constants.generic.invalidCellPhoneNumber}`);
    });

    test(`Cannot create Customer with null in Phone field`, async () => {
        const response = await sendGenericApiRequest('post', `customer`, await generateCustomerRequestBody(constants.generic.firstName, constants.generic.lastName, null, uniqueEmail, uniqueCustomerId));

        expect(response.response.status, `Status code 400`).toEqual(400);

        expect(response.response.data,
            `Response contains Object`).toEqual(expect.any(Object));

        expect(response.response.data[0].code,
            `Error message: ${constantsError.customer.code.errorMessageInvalidModelState}`)
            .toEqual(constantsError.customer.code.errorMessageInvalidModelState);

        expect(response.response.data[0].reason,
            `Error message: ${constantsError.customer.reason.errorWithIgnoredPhone}`)
            .toEqual(constantsError.customer.reason.errorWithIgnoredPhone);
    });

    test(`Cannot create Customer with null in Email field`, async () => {
        const response = await sendGenericApiRequest('post', `customer`, await generateCustomerRequestBody(constants.generic.firstName, constants.generic.lastName, uniquePhone, null, uniqueCustomerId));

        expect(response.response.status, `Status code 400`).toEqual(400);

        expect(response.response.data,
            `Response contains Object`).toEqual(expect.any(Object));

        expect(response.response.data[0].code,
            `Error message: ${constantsError.customer.code.errorMessageInvalidModelState}`)
            .toEqual(constantsError.customer.code.errorMessageInvalidModelState);

        expect(response.response.data[0].reason,
            `Error message: ${constantsError.customer.reason.errorWithIgnoredEmail}`)
            .toEqual(constantsError.customer.reason.errorWithIgnoredEmail);
    });

    test(`Cannot create Customer with invalid Email`, async () => {
        const email = 'katsiaryna+genericapicustomerbluetape.com';
        const response = await sendGenericApiRequest('post', `customer`, await generateCustomerRequestBody(constants.generic.firstName, constants.generic.lastName, uniquePhone, email, uniqueCustomerId));

        expect(response.response.status, `Status code 400`).toEqual(400);

        expect(response.response.data,
            `Response contains Object`).toEqual(expect.any(Object));

        expect(response.response.data[0].code,
            `Error message: ${constantsError.customer.code.errorMessageInvalidData}`)
            .toEqual(constantsError.customer.code.errorMessageInvalidData);

        expect(response.response.data[0].reason,
            `Error message: ${constantsError.customer.reason.errorWithInvalidEmail}`)
            .toEqual(constantsError.customer.reason.errorWithInvalidEmail);
    });

    test(`Cannot create Customer with empty string in 'Id' field`, async () => {
        const requestBody = await generateCustomerRequestBody(constants.generic.firstName, constants.generic.lastName, uniquePhone, uniqueEmail, '');
        const response = await sendGenericApiRequest('post', `customer`, requestBody);

        expect(response.response.status, `Status code 400`).toEqual(400);

        expect(response.response.data,
            `Response contains Object`).toEqual(expect.any(Object));

        expect(response.response.data[0].code,
            `Error message: ${constantsError.customer.code.errorMessageInvalidData}`)
            .toEqual(constantsError.customer.code.errorMessageInvalidData);

        expect(response.response.data[0].reason,
            `Error message: ${constantsError.customer.reason.errorWithEmptyCustomerId}`)
            .toEqual(`${constantsError.customer.reason.errorWithEmptyCustomerId}`);
    });

    test(`Cannot create Customer with null in 'Id' field`, async () => {
        const response = await sendGenericApiRequest('post', `customer`, await generateCustomerRequestBody(constants.generic.firstName, constants.generic.lastName, uniquePhone, uniqueEmail, null));

        expect(response.response.status, `Status code 400`).toEqual(400);

        expect(response.response.data,
            `Response contains Object`).toEqual(expect.any(Object));

        expect(response.response.data[0].code,
            `Error message: ${constantsError.customer.code.errorMessageInvalidModelState}`)
            .toEqual(constantsError.customer.code.errorMessageInvalidModelState);

        expect(response.response.data[0].reason,
            `Error message: ${constantsError.customer.reason.errorWithIgnoredCustomerId}`)
            .toEqual(`${constantsError.customer.reason.errorWithIgnoredCustomerId}`);
    });

    // UPDATE CUSTOMER REQUEST

    test(`Update the user's "first name" field `, async () => {
        const requestBody = await generateCustomerRequestBody(constants.generic.lastName, constants.generic.lastName, firstCustomerCellPhoneNumber, firstCustomerEmailAddress);
        const response = await sendGenericApiRequest('put', `customer/${firstCustomerId}`, requestBody);

        expect(response.status, `Status code 202`).toEqual(202);

        expect(response.data,
            `Response contains Object`).toEqual(expect.any(Object));
    });

    test(`Update the user's "last name" field `, async () => {
        const requestBody = await generateCustomerRequestBody(constants.generic.lastName, constants.generic.firstName, firstCustomerCellPhoneNumber, firstCustomerEmailAddress);
        const response = await sendGenericApiRequest('put', `customer/${firstCustomerId}`, requestBody);

        expect(response.status, `Status code 202`).toEqual(202);

        expect(response.data,
            `Response contains Object`).toEqual(expect.any(Object));
    });

    test(`Update the user's "phone number" field `, async () => {
        const requestBody = await generateCustomerRequestBody(constants.generic.lastName, constants.generic.firstName, uniquePhone, firstCustomerEmailAddress);
        const response = await sendGenericApiRequest('put', `customer/${firstCustomerId}`, requestBody);

        expect(response.status, `Status code 202`).toEqual(202);

        expect(response.data,
            `Response contains Object`).toEqual(expect.any(Object));
    });

    test(`Update the user's "email" field `, async () => {
        const requestBody = await generateCustomerRequestBody(constants.generic.lastName, constants.generic.firstName, uniquePhone, uniqueEmail);
        const response = await sendGenericApiRequest('put', `customer/${firstCustomerId}`, requestBody);

        expect(response.status, `Status code 202`).toEqual(202);

        expect(response.data,
            `Response contains Object`).toEqual(expect.any(Object));
    });

    test(`Return all fields to their original value `, async () => {
        const requestBody = await generateCustomerRequestBody(constants.generic.firstName, constants.generic.lastName, firstCustomerCellPhoneNumber, firstCustomerEmailAddress);
        const response = await sendGenericApiRequest('put', `customer/${firstCustomerId}`, requestBody);

        expect(response.status, `Status code 202`).toEqual(202);

        expect(response.data,
            `Response contains Object`).toEqual(expect.any(Object));
    });

    test(`Cannot update customer with empty first name field`, async () => {
        const requestBody = await generateCustomerRequestBody('', constants.generic.lastName, firstCustomerCellPhoneNumber, firstCustomerEmailAddress);
        const response = await sendGenericApiRequest('put', `customer/${firstCustomerId}`, requestBody);

        expect(response.response.status, `Status code 400`).toEqual(400);

        expect(response.response.data,
            `Response contains Object`).toEqual(expect.any(Object));

        expect(response.response.data[0].code, `Error message: ${constantsError.customer.code.errorMessageInvalidData}`)
            .toEqual(constantsError.customer.code.errorMessageInvalidData);

        expect(response.response.data[0].reason,
            `Error message: ${constantsError.customer.reason.errorWithEmptyFirstName}`)
            .toEqual(constantsError.customer.reason.errorWithEmptyFirstName);
    });

    test(`Cannot update customer with null in the first name field`, async () => {
        const requestBody = await generateCustomerRequestBody(null, constants.generic.lastName, firstCustomerCellPhoneNumber, firstCustomerEmailAddress);
        const response = await sendGenericApiRequest('put', `customer/${firstCustomerId}`, requestBody);

        expect(response.response.status, `Status code 400`).toEqual(400);

        expect(response.response.data,
            `Response contains Object`).toEqual(expect.any(Object));

        expect(response.response.data[0].code,
            `Error message: ${constantsError.customer.code.errorMessageInvalidModelState}`)
            .toEqual(constantsError.customer.code.errorMessageInvalidModelState);

        expect(response.response.data[0].reason,
            `Error message: ${constantsError.customer.reason.errorWithIgnoredFirstName}`)
            .toEqual(constantsError.customer.reason.errorWithIgnoredFirstName);
    });

    test(`Cannot update customer with empty last name field`, async () => {
        const requestBody = await generateCustomerRequestBody(constants.generic.firstName, '', firstCustomerCellPhoneNumber, firstCustomerEmailAddress);
        const response = await sendGenericApiRequest('put', `customer/${firstCustomerId}`, requestBody);

        expect(response.response.status, `Status code 400`).toEqual(400);

        expect(response.response.data,
            `Response contains Object`).toEqual(expect.any(Object));

        expect(response.response.data[0].code, `Error message: ${constantsError.customer.code.errorMessageInvalidData}`)
            .toEqual(constantsError.customer.code.errorMessageInvalidData);

        expect(response.response.data[0].reason,
            `Error message: ${constantsError.customer.reason.errorWithEmptyLastName}`)
            .toEqual(constantsError.customer.reason.errorWithEmptyLastName);
    });

    test(`Cannot update customer with null in the last name field`, async () => {
        const requestBody = await generateCustomerRequestBody(constants.generic.firstName, null, firstCustomerCellPhoneNumber, firstCustomerEmailAddress);
        const response = await sendGenericApiRequest('put', `customer/${firstCustomerId}`, requestBody);

        expect(response.response.status, `Status code 400`).toEqual(400);

        expect(response.response.data,
            `Response contains Object`).toEqual(expect.any(Object));

        expect(response.response.data[0].code,
            `Error message: ${constantsError.customer.code.errorMessageInvalidModelState}`)
            .toEqual(constantsError.customer.code.errorMessageInvalidModelState);

        expect(response.response.data[0].reason,
            `Error message: ${constantsError.customer.reason.errorWithIgnoredLastName}`)
            .toEqual(constantsError.customer.reason.errorWithIgnoredLastName);
    });

    test(`Cannot update customer with invalid phone number`, async () => {
        const requestBody = await generateCustomerRequestBody(constants.generic.firstName, constants.generic.lastName, constants.generic.invalidCellPhoneNumber, firstCustomerEmailAddress);
        const response = await sendGenericApiRequest('put', `customer/${firstCustomerId}`, requestBody);

        expect(response.response.status, `Status code 400`).toEqual(400);

        expect(response.response.data, `Response contains Object`).toEqual(expect.any(Object));

        expect(response.response.data[0].code, `Error message: ${constantsError.customer.code.errorMessageInvalidData}`)
            .toEqual(constantsError.customer.code.errorMessageInvalidData);

        expect(response.response.data[0].reason,
            `Error message: ${constantsError.customer.reason.errorWithInvalidPhoneNumber} +${constants.generic.invalidCellPhoneNumber}`)
            .toEqual(`${constantsError.customer.reason.errorWithInvalidPhoneNumber} +${constants.generic.invalidCellPhoneNumber}`);
    });

    test(`Cannot update customer with null in the phone number field`, async () => {
        const requestBody = await generateCustomerRequestBody(constants.generic.firstName, constants.generic.lastName, null, firstCustomerEmailAddress);
        const response = await sendGenericApiRequest('put', `customer/${firstCustomerId}`, requestBody);

        expect(response.response.status, `Status code 400`).toEqual(400);

        expect(response.response.data,
            `Response contains Object`).toEqual(expect.any(Object));

        expect(response.response.data[0].code,
            `Error message: ${constantsError.customer.code.errorMessageInvalidModelState}`)
            .toEqual(constantsError.customer.code.errorMessageInvalidModelState);

        expect(response.response.data[0].reason,
            `Error message: ${constantsError.customer.reason.errorWithIgnoredPhone}`)
            .toEqual(constantsError.customer.reason.errorWithIgnoredPhone);
    });

    test(`Cannot update customer with null in the email address field`, async () => {
        const requestBody = await generateCustomerRequestBody(constants.generic.firstName, constants.generic.lastName, firstCustomerEmailAddress, null);
        const response = await sendGenericApiRequest('put', `customer/${firstCustomerId}`, requestBody);

        expect(response.response.status, `Status code 400`).toEqual(400);

        expect(response.response.data,
            `Response contains Object`).toEqual(expect.any(Object));

        expect(response.response.data[0].code,
            `Error message: ${constantsError.customer.code.errorMessageInvalidModelState}`)
            .toEqual(constantsError.customer.code.errorMessageInvalidModelState);

        expect(response.response.data[0].reason,
            `Error message: ${constantsError.customer.reason.errorWithIgnoredEmail}`)
            .toEqual(constantsError.customer.reason.errorWithIgnoredEmail);
    });

    test(`Cannot update customer with invalid email address`, async () => {
        const requestBody = await generateCustomerRequestBody(constants.generic.firstName, constants.generic.lastName, firstCustomerEmailAddress, constants.generic.invalidEmail);
        const response = await sendGenericApiRequest('put', `customer/${firstCustomerId}`, requestBody);

        expect(response.response.status, `Status code 400`).toEqual(400);

        expect(response.response.data,
            `Response contains Object`).toEqual(expect.any(Object));

        expect(response.response.data[0].code,
            `Error message: ${constantsError.customer.code.errorMessageInvalidData}`)
            .toEqual(constantsError.customer.code.errorMessageInvalidData);

        expect(response.response.data[0].reason,
            `Error message: ${constantsError.customer.reason.errorWithInvalidEmail}`)
            .toEqual(constantsError.customer.reason.errorWithInvalidEmail);
    });
});


// ADD TESTS
// 2. CREATE SUPPLIER ACC >> lOGIN AS SUPPLIER BLUETAPE >> ADD CUSTOMER TO SUPPLIER >> AND TRY TO CREATE CUSTOMER VIA API RESULT: SHADOW OF CUSTOMER IS CREATED >> CHECK RESPONSE 200
// 
// 
// 
// COMPANY: LINK: CUSTOMER BY COMPANY GET REQUEST
// !!COMPANY B can't link with customer A from company A
// 
// 
// GET /company ID >> retun list of customers >> customer id and customer details are returned
// 
// INVOICES ///////////////////////////////////
// 2 customer types: WITH COMPANY CONNECTION and WITHOUT COMPANY CONNECTION
// 
// 
// 2 invoices types:
//  // 1. general invoice WITHOUT QUOTE >> Date shoudl be >= current date
//  2. CREATE INVOICE >> 
//  3. invoice ID is required 
//   "quoteRefNumber": "string" (THIS IS JUST INVOICE NUMBER), "quoteId": "string" CAN BE NULL !!!!!
// unic invoice id >> check double
// customer ID should EXIST ! (CHECK WITH iNVALID CUSTOMER NUMBER)
// 
// 
// 
// 
// 
// 
// 
// 2. QUOTE invoice WITHOUT QUOUTE >> 
// 
// 
// 
// 
// invoice update ///////////////////////////////////
// PUT request (update invoice)>> IF invoice is updaiting 1) shoudl customer id exist 2) invoice id exist 3) cannot reassign income to another customer when we updated invoice (cannot use another "customerId": "string")
// !!!!!!!!!!!!!!!! DON'T TUCH!!! in invoice has QUOT >> CANNOT
// MUST HAVE: CREATE INVOICE >> USER GET EMAIL >> REGISTER EMAIL IN SYSTEM >>LOAN APPLICATION >> APPROVE IN BACK OFFICE 
// CANNOT UPDATE INVOICE IF INVOICE STATE PROCESSING!!!
// UPDATE: UPDATE OF MAIN FIELDS: TOTAL AMUNT, SUBTOTAL, DATE/TIME, COMPARE DB state BEFORE/AFTER
// 
// 




