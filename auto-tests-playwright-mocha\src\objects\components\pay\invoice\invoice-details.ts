import {BasePage} from '../../../base.page';

export class InvoicesDetails extends BasePage {
    constructor(page) {
        super(page);
    };

    containers = {
        addPayment: this.page.locator('[data-testid="addPaymentMethods"]'),
    };

    fields = {
        payables: this.page.getByText('Payables'),
    };

    buttons = {
        addNewPaymentMethod: this.page.locator('"Add new payment method"'),
        connectYourBank: this.containers.addPayment.locator('"Connect your bank"'),
        linkYourCard: this.containers.addPayment.locator('"Link your card"'),
        bankAccount: this.page.locator('.r-1xfd6ze.r-rs99b7.r-117bsoe'),
        payWithBlueTapeCredit: this.page.locator('"BlueTape Credit"'),

        //dev
        connectYourBankAccount: this.page.locator('#root').getByText('Connect your bank'),
        connectYourBankAccountSecond: this.page.getByTestId('connectBank').getByText('Connect your bank'),

        continue: this.page.locator('"Continue"').first(),
        payWithBankOfAmerica: this.page.locator('"Bank of America"').first(),
        agreeAndPay: this.page.locator('"Agree & Pay"'),
        payWithBTC: this.page.locator('"No fees & payments until day 30"'),
        firstOptionDays: this.page.locator('"30 days"'),
        thirdOptionDays: this.page.locator('"90 days"'),
        downPaymentWireRadionButton: this.page.locator('[data-testid="down-payment-type.wire"]'),
        btcContinue: this.page.locator('"Continue"'),
        inventoryRadioButton: this.page.locator('"Inventory"'),
        okayGotItButton: this.page.locator('"Okay, Got It"'),
        makePayment: this.page.locator('[data-testid="footer-container"] >> text="Make a payment"'),
    };

    loanApplication = {
        ninteeDays: this.page.locator('"90 Days"'),
        continue: this.page.locator('"Continue"'),
        agreePay: this.page.locator('"Agree & Pay"'),
    };

    chosenInvoiceDetails = {
        bankOfAmericaOption: this.page.getByText('Bank of America'),
        payWithCardOption: this.page.locator('text=/.*- Card$/').first(),
        agreeAndPayButton: this.page.locator('div').filter({ hasText: /^Agree & Pay$/ }).first(),
        payIsSuccessful: this.page.locator('text=/Make a payment/')
    };

    inputFields = {
        businessName: this.page.locator('.r-1cvrl1u.r-1i10wst >> nth=0'),
    };

    async clickOnConnectYourBankAccountSecond() {
        await this.buttons.connectYourBankAccountSecond.click();
    }

    async payForLatestInvoice() {
        await this.page.waitForLoadState("networkidle");
        await this.page.waitForTimeout(1000);
        // Check if Make Payment button exists and click it
        //if (await this.buttons.makePayment.isVisible()) {
            await this.buttons.makePayment.click();
            await this.page.waitForTimeout(1000);
        //}
        
        await this.chosenInvoiceDetails.bankOfAmericaOption.click();
        await this.page.waitForTimeout(3000); //todo: create more stable waiters
        if(await this.page.locator('text=/Pay Remaining Amount/').isVisible()) {
            await this.page.locator('text=/Pay Remaining Amount/').click();
            await this.page.waitForTimeout(1000);
            await this.page.locator('[data-testid="button-text"]').click();
            await this.page.waitForTimeout(3000);
        } else {
            await this.chosenInvoiceDetails.agreeAndPayButton.click();
            await this.page.waitForTimeout(3000);
        }
        await this.chosenInvoiceDetails.payIsSuccessful.isVisible();
        await this.page.waitForTimeout(3000)
    }

    async payForLatestInvoiceWithCard() {
        await this.page.waitForLoadState("networkidle");
        
        // Check if Make Payment button exists and click it
        if (await this.buttons.makePayment.isVisible()) {
            await this.buttons.makePayment.click();
            await this.page.waitForTimeout(1000);
        }
        
        await this.chosenInvoiceDetails.payWithCardOption.click();
        await this.page.waitForTimeout(3000); //todo: create more stable waiters
        await this.chosenInvoiceDetails.agreeAndPayButton.click();
        await this.page.waitForTimeout(3000);
        await this.chosenInvoiceDetails.payIsSuccessful.isVisible();
        await this.page.waitForTimeout(3000)
    }

    async payForLatestInvoiceWithBTC() {
        await this.page.waitForLoadState("networkidle");
        await this.buttons.payWithBTC.click({delay: 3000})
        await this.buttons.inventoryRadioButton.click()
        await this.buttons.continue.click({delay: 1000})
        if (await this.buttons.thirdOptionDays.isVisible()) {
            await this.buttons.thirdOptionDays.click({delay: 3000})
        }
        else {
            await this.buttons.firstOptionDays.click({delay: 3000})
        }
        await this.buttons.btcContinue.click({delay: 3000})
        if (await this.buttons.downPaymentWireRadionButton.isVisible()) {
            await this.buttons.downPaymentWireRadionButton.click({delay: 1000});
            await this.buttons.continue.click({delay: 1000})
        }
        await this.buttons.agreeAndPay.click({delay: 3000})
        await this.chosenInvoiceDetails.payIsSuccessful.isVisible();
        await this.page.waitForTimeout(3000)
    }
}
