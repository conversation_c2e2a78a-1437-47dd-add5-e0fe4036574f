import {expect} from '@playwright/test';
import {BaseTest, test} from '../../test-utils';
import {
    addInvoice, cancelInvoice, createInvoiceR,
    sendGenericApiRequest
} from "../../../api/common/send-generic-api-request";
import {generateCompanyRequestBody} from "./genericAPI-request-body-functions-and-storage";
import {searchInvoiceByObjectId} from "../../../database/invoices/invoiceSearcherByObjectId";

const constants = JSON.parse(JSON.stringify(require('../../../constants/constants.json')));

test.describe(`Tests for invoice put endpoint @PUT @genericR @API @invoice`, async () => {

    let beforeAllResponse;
    let customerId: string;
    let customerEmail: string;
    let customerPhone: string;
    let uniqueBusinessPhone: string;
    let mongoIdOfCreatedCustomer: string;

    test.beforeAll(`Customer creation, getting required fields`, async () => {
        customerEmail = `sergei+genericapicustomer${BaseTest.dateTimePrefix()}@bluetape.com`;
        customerPhone = BaseTest.getCellPhoneNumber();
        customerId = `customerId_${BaseTest.getGUID()}`;

        uniqueBusinessPhone = BaseTest.getCellPhoneNumber();
        let companyId = BaseTest.getGUID();

        const customerFirstName = constants["generic"].firstName;
        const customerLastName = constants["generic"].lastName;


        const companyRequestBody = await generateCompanyRequestBody({
            companyId: companyId,
            businessPhoneNumber: uniqueBusinessPhone,
            customerCellPhoneNumber: customerPhone,
            customerEmailAddress: customerEmail,
            customerFirstName: customerFirstName,
            customerId: customerId,
            customerLastName: customerLastName,
            isCustomerUsed: true
        });

        beforeAllResponse = await sendGenericApiRequest('post', 'company', companyRequestBody);

        expect(beforeAllResponse.status).toEqual(201);

        mongoIdOfCreatedCustomer = await beforeAllResponse.data.customers[0].blueTapeCustomerId;
    });

    test(`Replace invoice`, async () => {
        const invoiceId = BaseTest.getGUID();
        const newTotalAmount = BaseTest.getRandomNumber(100, 225)

        const firstInvoiceResponse = await createInvoiceR({
            customerId: customerId,
            invoiceId: invoiceId
        });

        const oldTotalAmount = firstInvoiceResponse.data.details.totalAmount;

        const invoiceNumber = BaseTest.getGUID();

        const replacedInvoiceResponse = await addInvoice({
            customerId: customerId,
            invoiceId: invoiceId,
            invoiceNumber: invoiceNumber,
            totalAmount: newTotalAmount
        });

        console.log(replacedInvoiceResponse.response.data[0])

        expect(replacedInvoiceResponse.status)
            .toEqual(202);

        expect(replacedInvoiceResponse.data.details.totalAmount)
            .toEqual(newTotalAmount);

        const bluetapeIdOfInvoice = await replacedInvoiceResponse.data.blueTapeId;

        const invoiceDBObject = await searchInvoiceByObjectId(bluetapeIdOfInvoice);

        expect(invoiceDBObject.status)
            .toEqual('PLACED');

        expect(invoiceDBObject.total_amount)
            .toEqual(newTotalAmount);

        expect(invoiceDBObject.total_amount)
            .not.toEqual(oldTotalAmount);

        expect(invoiceDBObject.invoice_number)
            .toEqual(invoiceNumber);
    });

    test(`Successfully cancel created invoice`, async () => {
        const invoiceId = BaseTest.getGUID();

        const createdInvoice = await createInvoiceR({
            customerId: customerId,
            invoiceId: invoiceId
        });

        const bluetapeIdOfInvoice = await createdInvoice.data.blueTapeId;

        const cancelInvoiceResponse = await cancelInvoice(invoiceId);

        const invoiceDBObject = await searchInvoiceByObjectId(bluetapeIdOfInvoice)

        expect(cancelInvoiceResponse.status)
            .toEqual(202);

        expect(invoiceDBObject.status)
            .toEqual('CANCELLED');
    });

    test(`Invoice cannot be cancelled twice.`, async () => {
        const invoiceId = BaseTest.getGUID();

        const createdInvoice = await createInvoiceR({
            customerId: customerId,
            invoiceId: invoiceId
        });

        const bluetapeIdOfInvoice = await createdInvoice.data.blueTapeId;

        await cancelInvoice(invoiceId);

        const invoiceDBObject = await searchInvoiceByObjectId(bluetapeIdOfInvoice);

        expect(invoiceDBObject.status)
            .toEqual('CANCELLED');

        const cancelAfterCancelResponse = await cancelInvoice(invoiceId);

        const invoiceDBObjectAfterDoubleCancel = await searchInvoiceByObjectId(bluetapeIdOfInvoice);

        expect(cancelAfterCancelResponse.status)
            .toEqual(400);

        expect(cancelAfterCancelResponse.data[0].code)
            .toEqual('invoice_already_cancelled');

        expect(cancelAfterCancelResponse.data[0].reason)
            .toEqual('Invoice is already cancelled and can not be cancelled twice.');

        expect(invoiceDBObjectAfterDoubleCancel.status)
            .toEqual('CANCELLED');
    });

    test(`Cannot cancel invoice that not exist`, async () => {
        const invoiceId = BaseTest.getGUID();

        await createInvoiceR({
            customerId: customerId,
            invoiceId: invoiceId
        });

        const canceledInvoiceResponse =
            await cancelInvoice(BaseTest.getGUID());

        expect(canceledInvoiceResponse.status)
            .toEqual(400);

        expect(canceledInvoiceResponse.data[0].code)
            .toEqual('invoice_does_not_exist');

        expect(canceledInvoiceResponse.data[0].reason)
            .toEqual('Invoice does not exist');
    });

    test(`Cannot update invoice after cancelling`, async () => {
        const invoiceId = BaseTest.getGUID();

        await createInvoiceR({
            customerId: customerId,
            invoiceId: invoiceId
        });

        await cancelInvoice(invoiceId);

        const replacedInvoiceResponse = await addInvoice({
            customerId: customerId,
            invoiceId: invoiceId
        });

        expect(replacedInvoiceResponse.response.status)
            .toEqual(400);

        expect(replacedInvoiceResponse.response.data[0].code)
            .toEqual('invoice_can_be_updated_only_in_placed_status');

        expect(replacedInvoiceResponse.response.data[0].reason)
            .toEqual('Invoice can be updated only in status placed.')
    });
});
