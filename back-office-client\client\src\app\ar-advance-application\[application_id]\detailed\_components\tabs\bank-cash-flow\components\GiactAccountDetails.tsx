import { useTranslation } from 'react-i18next'
import { useState } from 'react'

import { StyledButton } from '@/components/common/Button'
import { GiactAccountDetailsModal } from '@/app/ar-advance-application/[application_id]/detailed/_components/tabs/bank-cash-flow/components/GiactAccountDetailsModal'

interface IProps {
  name: string | null
  routingNumber: string | null
  accountNumber: string | null
  accountHolderName: string | null
}

const GiactAccountDetails = ({
  name,
  routingNumber,
  accountNumber,
  accountHolderName,
}: Readonly<IProps>): JSX.Element => {
  const { t } = useTranslation()
  const [isModalOpened, setIsModalOpened] = useState(false)

  return (
    <>
      <StyledButton
        type="link"
        onClick={() => setIsModalOpened(true)}
        size="small"
      >
        {t(
          'arAdvanceApplication.page.detailed.tabs.bankAccounts.giactAccountDetailsModal.showGiactDetails',
        )}
      </StyledButton>

      <GiactAccountDetailsModal
        isOpened={isModalOpened}
        onClose={() => setIsModalOpened(false)}
        name={name}
        routingNumber={routingNumber}
        accountNumber={accountNumber}
        accountHolderName={accountHolderName}
      />
    </>
  )
}

export default GiactAccountDetails
