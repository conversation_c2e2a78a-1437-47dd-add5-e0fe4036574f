import axios, {AxiosResponse} from "axios";
import {expect} from "@playwright/test";
import {getCurrentDay} from "../base-api";
import {ApproveDrawApplicationRequest, AttachSupplierToDrawApplicationRequest} from "./requests/approveDrawApplicationRequest";

//require('dotenv').config();

let baseUrl: string = process.env.BACK_OFFICE_ADMIN_URL ?? '';
let configuredHeaders = {
    accept: 'application/json',
    'Content-Type': 'application/json; charset=utf-8'
};

export class BackOfficeClient {
    async approveDrawApplication(drawApplicationId: string, request: ApproveDrawApplicationRequest, adminIdToken: string) {
        // override baseUrl with the one from environment variable
        baseUrl = process.env.BACK_OFFICE_ADMIN_URL;
        return await this.sendBackOfficeRequestRequest('patch', `drawApprovals/${drawApplicationId}/review`, adminIdToken, request)
    }

    async attachSupplierToDrawApplication(drawApplicationId: string, request: AttachSupplierToDrawApplicationRequest, adminIdToken: string) {
        // override baseUrl with the one from environment variable
        baseUrl = process.env.BACK_OFFICE_ADMIN_URL;
        return await this.sendBackOfficeRequestRequest('post', `admin/drawApprovals/${drawApplicationId}/attachsupplier`, adminIdToken, request)
    }   

    async sendBackOfficeRequestRequest(method: string, endpoint: string, adminIdToken: string, body: any = null): Promise<AxiosResponse<any,any>> {
        try {
            const url = `${baseUrl}/${endpoint}`;
            let response;

            switch (method) {
                case 'post':
                    response = await axios.post(url, body, {
                        headers: {'Authorization': `Bearer ${adminIdToken}`, ...configuredHeaders},
                    });
                    break;
                case 'get':
                    response = await axios.get(url, {
                        headers: {'Authorization': `Bearer ${adminIdToken}`, ...configuredHeaders},
                    });
                    break;
                case 'put':
                    response = await axios.put(url, body, {
                        headers: {'Authorization': `Bearer ${adminIdToken}`, ...configuredHeaders},
                    });
                    break;
                case 'patch':
                    response = await axios.patch(url, body, {
                        headers: {'Authorization': `Bearer ${adminIdToken}`, ...configuredHeaders},
                    });
                    break;
                case 'delete':
                    response = await axios.delete(url, {
                        headers: {'Authorization': `Bearer ${adminIdToken}`, ...configuredHeaders},
                    });
                    break;
            }
            return response;
        } catch (error) {
            return error;
        }
    }
}

