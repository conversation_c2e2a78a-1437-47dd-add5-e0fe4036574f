﻿using System.Runtime.Serialization;
using System.Text.Json.Serialization;

namespace BlueTape.Aion.DataAccess.External.Models.Login;

[DataContract]
public class LoginResponseModel : BaseAionResponseModel
{
    [JsonPropertyName("authToken")]
    public string AuthToken { get; set; } = null!;

    [JsonPropertyName("additionalContext")]
    public string AdditionalContext { get; set; } = null!;
    
    [JsonPropertyName("additionalInfo")]
    public string AdditionalInfo { get; set; } = null!;
}