@startuml Decision Engine State Machine
title Decision Engine State Machine (sequence diagram)

skinparam roundcorner 20
skinparam ComponentBorderThickness 2

(*) --> "Validate"
"Validate" --> "ValidateStatus"
"ValidateStatus" -l-> " IssueLoan "
" IssueLoan " --> (*)
"ValidateStatus" --> "KYC"
"ValidateStatus" -r-> " RejectLoan "
" RejectLoan " --> (*)
"KYC" --> "KYB"
"KYB" --> "CreditStatus"
"CreditStatus" --> "BlueTape"
"BlueTape" --> "CheckFinicityData"
"CheckFinicityData" --> "IsFinicityDataReady"
"IsFinicityDataReady" --> [Wait] "IsFinicityDataReady"
"IsFinicityDataReady" --> "LoanDecision"
"LoanDecision" --> "KnockOutScore"
"KnockOutScore" --> "PassedKnockout"
"PassedKnockout" --> "HumanApproval"
"PassedKnockout" --> "VerifyAccount"
"VerifyAccount" --> if "Is Account Verified" then
--> [Yes] "HumanApproval"
else
--> [No] "ManualVerify"
endif
"PassedKnockout" --> "RejectLoan"
"ManualVerify" --> "RejectLoan"
"ManualVerify" --> if "Is Manually Verified" then
--> [Yes] "HumanApproval"
else
--> [No] "ManualBank"
endif
"ManualBank" --> if "Is Manual Bank Uploaded" then
--> [Yes] "ProcessManualBank"
else
--> [No] "RejectLoan"
endif
"HumanApproval" --> if "Is Human Approved" then
--> [Yes] "IssueLoan"
else
--> [No] "RejectLoan"
endif
"ProcessManualBank" --> "LoanDecision"
"PassedKnockout" --> "RejectLoan"
"RejectLoan" ----> (*)
"IssueLoan" ----> (*)

@enduml