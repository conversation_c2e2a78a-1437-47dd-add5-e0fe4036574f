openapi: '3.0.0'
info:
  version: '0.0.1'
  title: 'OUTDATED - Payments Operations API (high level)'
  description: API definition for Payment operations
paths:
  /payments/loans/{id}/repayment:
    post:
      tags:
        - Payments (Loan)
      summary: Creates a loan repayment
      description: Creates a loan repayment
      operationId: createLoanRepayment
      parameters:
        - name: id
          description: Identifier of loan
          example: 6df30249-c7f0-4f17-b06f-c29b2fee0a62
          in: path
          required: true
          schema:
            type: string
            nullable: false
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/RepaymentRequest'
      responses:
        201:
          description: Repayment created
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/RepaymentResponse'
        400:
          description: Invalid request
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
  /payments/loans/issue:
    post:
      tags:
        - Payments (Loan)
      summary: Issues a loan
      description: Issues a loan
      operationId: createLoanIssue
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/LoanIssueRequest'
      responses:
        201:
          description: Repayment created
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/LoanIssueResponse'
        400:
          description: Invalid request
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
  /payments/loans/{id}/buyback:
    post:
      tags:
        - Payments (Loan)
      summary: Buys back a loan
      description: Buys back a loan
      operationId: createLoanBuyback
      parameters:
        - name: id
          description: Identifier of loan
          example: 6df30249-c7f0-4f17-b06f-c29b2fee0a62
          in: path
          required: true
          schema:
            type: string
            nullable: false
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/LoanBuybackRequest'
      responses:
        201:
          description: Buyback created
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/LoanBuybackResponse'
        400:
          description: Invalid request
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
  /payments/loans/{id}/close:
    post:
      tags:
        - Payments (Loan)
      summary: Closes a loan
      description: Closes a loan
      operationId: createLoanClose
      parameters:
        - name: id
          description: Identifier of loan
          example: 6df30249-c7f0-4f17-b06f-c29b2fee0a62
          in: path
          required: true
          schema:
            type: string
            nullable: false
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/LoanCloseRequest'
      responses:
        201:
          description: Close created
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/LoanCloseResponse'
        400:
          description: Invalid request
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
  /payments/loans/{id}/revenuecollection:
    post:
      tags:
        - Payments (Loan)
      summary: Collects loan revenue
      description: Collects loan revenue
      operationId: createLoanRevenueCollection
      parameters:
        - name: id
          description: Identifier of loan
          example: 6df30249-c7f0-4f17-b06f-c29b2fee0a62
          in: path
          required: true
          schema:
            type: string
            nullable: false
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/LoanRevenueCollectionRequest'
      responses:
        201:
          description: Revenue collection created
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/LoanRevenueCollectionResponse'
        400:
          description: Invalid request
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
  /payments/loans/{id}/final:
    post:
      tags:
        - Payments (Loan)
      summary: Creates loan final payment
      description: Creates loan final payment
      operationId: createLoanFinalPayment
      parameters:
        - name: id
          description: Identifier of loan
          example: 6df30249-c7f0-4f17-b06f-c29b2fee0a62
          in: path
          required: true
          schema:
            type: string
            nullable: false
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/LoanFinalPaymentRequest'
      responses:
        201:
          description: Final payment created
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/LoanFinalPaymentResponse'
        400:
          description: Invalid request
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
  /payments/transactions/{id}/refund:
    post:
      tags:
        - Payments
      summary: Creates a payment refund
      description: Creates a payment refund. Now triggers Tabapay Card Refund.
      operationId: createPaymentRefund
      parameters:
        - name: id
          description: Identifier of transaction
          example: 6df30249-c7f0-4f17-b06f-c29b2fee0a62
          in: path
          required: true
          schema:
            type: string
            nullable: false
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/PaymentRefundRequest'
      responses:
        201:
          description: Refund created
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/PaymentRefundResponse'
        400:
          description: Invalid request
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
  /payments/transactions/{id}/statusupdate:
    put:
      tags:
        - Payments
      summary: Updates payment status by transaction
      description: Updates payment status by transaction. Endpoint for ACH check transaction status updates.
      operationId: updatePaymentTransaction
      parameters:
        - name: id
          description: Identifier of the transaction
          example: 6df30249-c7f0-4f17-b06f-c29b2fee0a62
          in: path
          required: true
          schema:
            type: string
            nullable: false
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/PaymentTransactionStatusUpdateRequest'
      responses:
        200:
          description: Payment transaction updated
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/PaymentTransactionStatusUpdateResponse'
        400:
          description: Invalid request
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
components:
  schemas:
    RepaymentRequest:
      type: object
    RepaymentResponse:
      type: object
    LoanIssueRequest:
      type: object
    LoanIssueResponse:
      type: object
    LoanBuybackRequest:
      type: object
    LoanBuybackResponse:
      type: object
    LoanCloseRequest:
      type: object
    LoanCloseResponse:
      type: object
    LoanRevenueCollectionRequest:
      type: object
    LoanRevenueCollectionResponse:
      type: object
    LoanFinalPaymentRequest:
      type: object
    LoanFinalPaymentResponse:
      type: object
    PaymentTransactionStatusUpdateRequest:
      type: object
    PaymentTransactionStatusUpdateResponse:
      type: object
    PaymentRefundRequest:
      type: object
    PaymentRefundResponse:
      type: object
    Error:
      type: object
      required:
        - message
      properties:
        message:
          description: A human readable error message
          type: string
    EmptyResponse:
      type: object
      nullable: true
  securitySchemes:
    ApiKey:
      type: apiKey
      in: header
      name: X-Api-Key
security:
  - ApiKey: []