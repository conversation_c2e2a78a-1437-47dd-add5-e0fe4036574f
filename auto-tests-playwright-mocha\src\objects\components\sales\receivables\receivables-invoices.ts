import {BasePage} from '../../../base.page';

export class ReceivablesInvoices extends BasePage {
    constructor(page){
        super(page);
    };

    containers = {
        recievablesRowContainer: this.page.locator('.rdt_TableRow'),
        dotsDropDownContainer: this.page.locator('[class="css-1dbjc4n r-14lw9ot"] >> nth=1'),
    };

    dropDowns = {
        collectPayment: this.containers.dotsDropDownContainer.locator('//*[text()="Collect Payment"]/parent::div/parent::div/parent::div'), // ask for data-testid
        cancel: this.containers.dotsDropDownContainer.locator('[data-testid="cancel_btn"]'),
        edit: this.containers.dotsDropDownContainer.locator('[data-testid="edit"]'),
        resend: this.containers.dotsDropDownContainer.locator('[data-testid="send_btn"]'),
    };
    
    rowElements = {
        clickOnRow: async (businessName) => ((await this.getRow(businessName)).click()),
        clickDotsDropDown: async (businessName) => ((await this.getRow(businessName)).locator(`[data-testid="dropdown_dots"]`).click()),
        getStatus: async (businessName) => {
            return (await this.getRow(businessName)).locator(`[data-testid="invoice_status_badge"]>div`);
        },
    };

    async getRow(businessName){
        return this.containers.recievablesRowContainer.locator(`//div[text()="${businessName}"]/parent::div/parent::div`);
    };
}