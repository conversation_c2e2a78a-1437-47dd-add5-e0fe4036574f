import {client} from "../client";
import {Db, ObjectId} from "mongodb";


export async function wipeCustomerFields(blueTapeId: string) {
    let database: Db;

    if (process.env.CI_ENVIRONMENT_URL == 'https://dev.bluetape.com') {
        database = client.db(`dev`);
    } else {
        database = client.db(`beta`);
    }

    const allCustomers = database.collection('customeraccounts');

    const query = {"_id": new ObjectId(blueTapeId)};

    await allCustomers.updateOne(
        query,
        {$set: {phone: " "}}
    );

    // await allCustomers.updateOne(
    //     query,
    //     {$set: {email: " "}}
    // );
}
