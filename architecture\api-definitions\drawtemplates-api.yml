openapi: "3.0.0"
info:
  version: "0.0.1"
  title: "Loan Templates (as a part of LMS and LS)"
  description: |
    API definition of Loan/Draw Templates (Loan/Draw Payment Plans) 
servers:
  - url: TBD-Dev
    description: Development server
  - url: TBD-Prod
    description: Production server
paths:
  /loantemplates:
    get:
      tags:
        - loanTemplates
      summary: Gets all loan/draw templates (payment plans)
      description: Gets all loan/draw templates (payment plans)
      operationId: getLoanTemplates
      parameters: 
        - name: id
          description: The id of template
          in: query
          required: false
          schema:
            type: string
        - name: legacyId
          description: The legacy id of template
          in: query
          required: false
          schema:
            type: string
      responses:
        200:
          description: The templates
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/LoanTemplate'
        404:
          description: Not found
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
  /loantemplates/{id}:
    get:
      tags:
        - loanTemplates
      summary: Gets loan/draw template by id (for compatibility)
      description: Gets loan/draw template by id (for compatibility)
      operationId: getLoanTemplateById
      parameters: 
        - name: id
          description: The id of template
          in: path
          required: true
          schema:
            type: string
      responses:
        200:
          description: The template
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/LoanTemplate'
        404:
          description: Not found
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
  /loantemplates/legacy/{id}:
    get:
      tags:
        - loanTemplates
      summary: Gets loan/draw template by legacy id (for compatibility)
      description: Gets loan/draw template by legacy id (for compatibility)
      operationId: getLoanTemplateByLegacyId
      parameters: 
        - name: id
          description: The legacy id of template
          in: path
          required: true
          schema:
            type: string
      responses:
        200:
          description: The template
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/LoanTemplate'
        404:
          description: Not found
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
  /admin/loantemplates:
    post:
      tags:
        - loanTemplates Admin
      summary: Adds a new loan template
      description: Adds a new loan template
      operationId: createLoanTemplate
      parameters:
        - name: userId
          description: The user added this template
          in: header
          required: true
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/CreateOrPatchLoanTemplate"
      responses:
        201:
          description: The created template
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/LoanTemplate'
        400:
          description: Bad request
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
  /admin/loantemplates/{id}:
    patch:
      tags:
        - loanTemplates Admin
      summary: Updates loan template (admin function)
      description: Updates loan template (admin function) All fields are optional. Which field is sent, that field to update.
      operationId: updateLoanTemplateById
      parameters:
        - name: id
          description: The id of loan template
          in: path
          required: true
          schema:
            type: string
        - name: userId
          description: Identifier of the user who updated the loan template
          example: 62c5e900cd65d31d3f25c34c
          in: header
          required: true
          schema:
            type: string
            nullable: false
      requestBody:
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/CreateOrPatchLoanTemplate"
      responses:
        200:
          description: The patched loan template
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/LoanTemplate'
        400:
          description: Bad request
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        404:
          description: Not found
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
components:
  schemas:
    ErrorResponse:
      type: object
      required:
        - statusCode
        - message
        - errorDescription
      properties:
        statusCode:
          description: HTTP status code corresponding to the error.
          type: integer
          example: 400
        message:
          description: A human readable error message.
          type: string
          example: Invalid model.
        errorDescription:
          description: Detailed information.
          type: string
    LoanTemplate:
      allOf:
      - type: object
        properties:
          id:
            type: string
            example: bdafd605-fb56-4d32-9c9b-3b58c174dd79
          createdAt:
            type: string
            format: date-time
          createdBy:
            type: string
          updatedAt:
            type: string
            format: date-time
          updatedBy:
            type: string
      - $ref: "#/components/schemas/CreateOrPatchLoanTemplate"
    CreateOrPatchLoanTemplate:
      type: object
      properties:
        legacyId:
          type: string
          example: fb564d329c9b
          description: Id from MongoDb LoanPaymentPlans
        loanFeePercentage:
          type: number
          example: 2
          description: MongoDb - fee
        installmentsNumber:
          type: number
          example: 13
          description: MongoDb - term
        paymentDelayInDays:
          type: number
          example: 30
          description: MongoDb - firstPaymentDelayDays
        paymentIntervalInDays:
          type: number
          example: 7
          description: MongoDb - frequency enum
        minimumLateFeeAmount:
          type: number
          example: 35
          description: MongoDb - not exists
        gracePeriodInDays:
          type: number
          example: 3
          description: MongoDb - not exists
        lateFeePercentage:
          type: number
          example: 0.01
          description: MongoDb - not exists
        earlyPayPeriod:
          type: number
          example: 0
          description: MongoDb - not exists
        totalDurationInDays:
          type: number
          example: 120
          description: MongoDb - days
        lateFeeCollectionDelayInDays:
          type: number
          example: 1
          description: MongoDb - not exists
        penaltyInterestTriggerRule:
          type: string
          enum:
            - twoTimesThreeDaysOrOneTimeSevenDaysRule
          description: MongoDb - not exists
        code:
          type: string
          example: 120vc
          description: MongoDb - name
        name:
          type: string
          example: Virtual Card 120 days
          description: MongoDb - not exists like this
        status:
          type: string
          enum:
            - active
            - inactive
          example: active
          default: active
          description: MongoDb - not exists
        type:
          type: string
          enum:
            - regular
            - virtualcard
            - nosupplier
            - express
          example: regular
          default: regular
          description: MongoDb - type enum
  securitySchemes:
    ApiKey:
      type: apiKey
      in: header
      name: X-Api-Key
security:
  - ApiKey: []
