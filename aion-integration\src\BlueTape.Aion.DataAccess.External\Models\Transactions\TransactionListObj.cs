﻿using System.Runtime.Serialization;
using System.Text.Json.Serialization;

namespace BlueTape.Aion.DataAccess.External.Models.Transactions;

[DataContract]
public class TransactionListObj
{
    [JsonPropertyName("id")]
    public string? Id { get; set; } = null;

    [JsonPropertyName("accountId")]
    public string? AccountId { get; set; } = null;

    [JsonPropertyName("achId")]
    public string? AchId { get; set; } = null;

    [JsonPropertyName("amount")]
    public decimal? Amount { get; set; }

    [JsonPropertyName("amountStr")]
    public string? AmountStr { get; set; } = null;

    [JsonPropertyName("balance")]
    public decimal? Balance { get; set; }

    [JsonPropertyName("balanceStr")]
    public string? BalanceStr { get; set; } = null;

    [JsonPropertyName("txnDate")]
    public DateTime? TxnDate { get; set; }

    [JsonPropertyName("wireId")]
    public string? WireId { get; set; } = null;

    [JsonPropertyName("displayDescription")]
    public string? DisplayDescription { get; set; } = null;

    [JsonPropertyName("transactionId")]
    public Guid TransactionId { get; set; }

    [JsonPropertyName("traceNumber")]
    public string? TraceNumber { get; set; } = null;

    [JsonPropertyName("transactionType")]
    public string? TransactionType { get; set; } = null;

    [JsonPropertyName("providerStatus")]
    public string? ProviderStatus { get; set; } = null;

    [JsonPropertyName("accountNumber")]
    public string? AccountNumber { get; set; } = null;

    [JsonPropertyName("transactionCode")]
    public string? TransactionCode { get; set; } = null;

    [JsonPropertyName("rail")]
    public string? Rail { get; set; } = null;

    [JsonPropertyName("flags")]
    public List<string> Flags { get; set; } = new();

    [JsonPropertyName("schedule")]
    public List<int> Schedule { get; set; } = new();

    [JsonPropertyName("clearing")]
    public bool? Clearing { get; set; }
}