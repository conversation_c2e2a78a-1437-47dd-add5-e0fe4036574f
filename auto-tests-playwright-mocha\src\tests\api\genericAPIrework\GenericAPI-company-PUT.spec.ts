import {BaseTest, test} from "../../test-utils";
import {
    sendGenericApiRequest,
    updateCompany
} from "../../../api/common/send-generic-api-request";
import {expect} from "@playwright/test";
import {generateCompanyRequestBody} from "./genericAPI-request-body-functions-and-storage";

/**
 * Imported files with repeating strings
 */
const testMessages = JSON.parse(JSON.stringify(require('../../../constants/testMessages.json')));
const response201 = testMessages.genericApi['201'];
const response400 = testMessages.genericApi['400'];

test.describe(`Company tests @PUT @genericR @API @company`, async () => {

    /**
     * @param currentDate - equal to source modified date in request body
     */
    let currentDate: string;

    /**
     * Values for 'beforeAll', where account with valid data is created
     * for getting tests data
     */
    let uniqueBusinessPhone: string;
    let uniqueCompanyId: string;

    /**
     * Values for 'beforeEach', where a valid data for tests is initialized
     */
    let businessPhone: string;
    let companyId: string;

    /**
     * Values which get after company created and required for other tests
     */
    let blueTapeCompanyId: string;

    /**
     * String which represent invalid id
     */
    const invalidId = BaseTest.dateTimePrefix() + '1';

    test.beforeAll(`Values init and company creating.`, async () => {
        currentDate = new Date().toISOString();
        uniqueBusinessPhone = BaseTest.getCellPhoneNumber();
        uniqueCompanyId = BaseTest.dateTimePrefix();

        const body = await generateCompanyRequestBody({
            companyId: uniqueCompanyId,
            sourceModifiedDate: currentDate,
            businessPhoneNumber: uniqueBusinessPhone,
            isCustomerUsed: false
        });

        const response = await sendGenericApiRequest('post', 'company', body);

        blueTapeCompanyId = await response.data.blueTapeCompanyId;

        expect(await response.status,
            `'${response201}', generation of company with id '${blueTapeCompanyId}' is successful.`)
            .toEqual(201);
    });

    test.beforeEach(`Generate valid values for negative tests`, async () => {
        businessPhone = BaseTest.getCellPhoneNumber();
        companyId = BaseTest.dateTimePrefix();
    })

    test(`Update company @positive`, async () => {
        const response = await updateCompany(uniqueCompanyId, uniqueBusinessPhone, currentDate);

        expect(response.status, `Status code 202`)
            .toEqual(202);

        expect(response.data, `Response contains Object with BlueTapeID`)
            .toEqual(expect.any(Object));

        expect(typeof response.data.blueTapeId)
            .toEqual('string');

        expect(await response.data.blueTapeId)
            .toEqual(blueTapeCompanyId);
    });

    test(`Cannot update company with non-existent company id @negative`, async () => {
        const response = await updateCompany(invalidId, businessPhone, currentDate);

        expect(response.response.status, response400)
            .toEqual(400);

        expect(response.response.data, `Response contains Object with BlueTapeID`)
            .toEqual(expect.any(Object));
    });
})
