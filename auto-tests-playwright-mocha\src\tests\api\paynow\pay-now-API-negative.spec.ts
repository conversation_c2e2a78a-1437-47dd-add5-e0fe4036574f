import {test} from "../../test-utils";
import {expect} from "@playwright/test";
import {
    getPaymentRequestExist,
    getPaymentTransactionHistory,
    getRequiredPaymentRequestById,
    getRequiredTransactionById, isOperationExist
} from "../../../api/paynow/send-pay-now-request";


const payNowData = JSON.parse(JSON.stringify(require('../../../constants/PayNow-data.json')));

test.describe(`Pay now API negative. @paynow`, async () => {

    /*
    Tests to verify that we cannot work with non-existent data
    */

    test(`Cannot get details of non-existent payment request. @non-existent`, async () => {
        const response = await getRequiredPaymentRequestById(payNowData.invalidId);
        expect(await response.status).toEqual(204);
    });

    test(`Cannot get details of non-existent transaction. @non-existent`, async () => {
        const response = await getRequiredTransactionById(payNowData.invalidId);
        expect(await response.status).toEqual(204);
    });

    test.skip(`Cannot get history of non-existent transaction. @non-existent`, async () => {
        const response = await getPaymentTransactionHistory(payNowData.invalidId);
        expect(await response.status).toEqual(204);
    });

    test.skip(`Cannot get history of non-existent transaction by special endpoint. @non-existent`, async () => {
        const response = await getPaymentRequestExist(payNowData.invalidId);
        expect(await response.status).toEqual(404);
    });

    test(`Payment request for non-existent invoice is not exist. @non-existent`, async () => {
        const response = await isOperationExist(null);
        expect(await response.status).toEqual(200);
        expect(await response.data).toEqual(false);
    });
});
