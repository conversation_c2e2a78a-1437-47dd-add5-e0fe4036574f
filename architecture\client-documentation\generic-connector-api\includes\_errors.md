# Errors

<aside class="notice">
Generic documentation about error responses
</aside>

The BlueTape Generic Integration API uses the following error codes:

Error Code | Meaning
---------- | -------
```400``` | Bad Request - Your request is invalid.
```401``` | Unauthorized - Your API key might be wrong.
```404``` | Not Found - The specified api endpoint could not be found.
```405``` | Method Not Allowed - You tried to access our api with an invalid method.
```429``` | Too Many Requests - Rate limiting error. Please slow down.
```500``` | Internal Server Error - We had a problem with our server. Try again later.
```503``` | Service Unavailable - We're temporarily offline for maintenance. Please try again later.
