﻿using AutoMapper;
using BlueTape.Company.API.DataAccess;
using BlueTape.Company.Domain.DTOs.Customers;
using BlueTape.Company.Domain.DTOs.GuestSuppliers;
using BlueTape.CompanyService.Customers;
using BlueTape.DataAccess.MongoDB.Abstractions;
using BlueTape.DataAccess.MongoDB.Entities.Customer;
using MongoDB.Bson;
using MongoDB.Driver;
using System.Diagnostics.CodeAnalysis;
using InHouseCreditEntity = BlueTape.DataAccess.MongoDB.Entities.Customer.CustomerInHouseCreditEntity;

namespace BlueTape.DataAccess.MongoDB.Repositories;

[ExcludeFromCodeCoverage]
public class CustomerRepository : ICustomerRepository
{
    private readonly CompanyDbContext _dbContext;
    private readonly IMapper _mapper;

    public CustomerRepository(CompanyDbContext dbContext, IMapper mapper)
    {
        _dbContext = dbContext ?? throw new ArgumentNullException(nameof(dbContext));
        _mapper = mapper ?? throw new ArgumentNullException(nameof(mapper));
    }

    public async Task<CustomerDto?> GetByIdAsync(string btCustomerId, CancellationToken cancellationToken)
    {
        var customer = await _dbContext.Customers
            .Find(x => x.BlueTapeCustomerId == btCustomerId)
            .FirstOrDefaultAsync(cancellationToken);

        if (customer == null)
            return default;

        return _mapper.Map<CustomerDto>(customer);
    }

    public async Task<CustomerDto[]?> GetByCompanyIdAsync(string companyId, CancellationToken cancellationToken)
    {
        var customer = await _dbContext.Customers
            .Find(x => x.CompanyId == companyId)
            .ToListAsync(cancellationToken);

        return customer == null ? default : _mapper.Map<CustomerDto[]>(customer);
    }

    public async Task<CustomerDto[]?> GetCustomersByMerchantIdAndCompanyIdAsync(string merchantCompanyId, string customerCompanyId, CancellationToken cancellationToken)
    {
        var pipeline = new BsonDocument[]
        {
            // Stage 1: Start with CustomerAccount collection filtering by merchant/supplier ID
            new BsonDocument("$match", new BsonDocument
            {
                { "company_id", merchantCompanyId },
                { "isDeleted", new BsonDocument("$ne", true) }
            }),
            
            // Stage 2: Look up user information based on customer email or phone
            new BsonDocument("$lookup", new BsonDocument
            {
                { "from", "users" },
                { "let", new BsonDocument
                    {
                        { "customerEmail", "$email" },
                        { "customerPhone", "$phone" }
                    }
                },
                { "pipeline", new BsonArray
                    {
                        new BsonDocument("$match", new BsonDocument
                        {
                            { "$expr", new BsonDocument("$or", new BsonArray
                                {
                                    new BsonDocument("$eq", new BsonArray { "$email", "$$customerEmail" }),
                                    new BsonDocument("$eq", new BsonArray { "$login", "$$customerEmail" }),
                                    new BsonDocument("$eq", new BsonArray { "$login", "$$customerPhone" })
                                })
                            }
                        })
                    }
                },
                { "as", "matchedUsers" }
            }),
            
            // Stage 3: Unwind matched users
            new BsonDocument("$unwind", new BsonDocument
            {
                { "path", "$matchedUsers" },
                { "preserveNullAndEmptyArrays", false }
            }),
            
            // Stage 4: Look up user roles to filter by customer company ID
            new BsonDocument("$lookup", new BsonDocument
            {
                { "from", "userroles" },
                { "let", new BsonDocument("userSub", "$matchedUsers.sub") },
                { "pipeline", new BsonArray
                    {
                        new BsonDocument("$match", new BsonDocument
                        {
                            { "$expr", new BsonDocument("$and", new BsonArray
                                {
                                    new BsonDocument("$eq", new BsonArray { "$sub", "$$userSub" }),
                                    new BsonDocument("$eq", new BsonArray { "$company_id", customerCompanyId })
                                })
                            }
                        })
                    }
                },
                { "as", "userRoles" }
            }),
            
            // Stage 5: Keep only customers with matching user roles for the specific company
            new BsonDocument("$match", new BsonDocument
            {
                { "userRoles.0", new BsonDocument("$exists", true) }
            }),
            
            // Stage 6: Project the final result with all needed fields from the database
            new BsonDocument("$project", new BsonDocument
            {
                { "_id", 1 },
                { "id", new BsonDocument("$toString", "$_id") },
                { "company_id", 1 },
                { "name", 1 },
                { "first_name", 1 },
                { "last_name", 1 },
                { "display_name", 1 },
                { "email", 1 },
                { "phone", 1 },
                { "address", 1 },
                { "business_phone", 1 },
                { "invited", 1 },
                { "type", 1 },
                { "notes", 1 },
                { "dateAdded", 1 },
                { "last_purchase_date", 1 },
                { "house_credit_info", 1 },
                { "credit_info", 1 },
                { "isDeleted", 1 },
                { "salesRepId", 1 },
                { "settings", 1 },
                { "status", 1 },
                { "bankAccounts", 1 },
                { "merchant_id", merchantCompanyId },
                { "customer_company_id", new BsonDocument("$arrayElemAt", new BsonArray { "$userRoles.company_id", 0 }) },
                { "created_at", "$createdAt" },
                { "updated_at", "$updatedAt" }
            }),
            
            // Stage 7: Sort the results by createdAt date in descending order
            new BsonDocument("$sort", new BsonDocument
            {
                { "createdAt", -1 } // -1 for descending order (newest first)
            })
        };

        var results = await _dbContext.Customers.Aggregate<CustomerEntity>(pipeline).ToListAsync(cancellationToken);

        return results == null ? default : _mapper.Map<CustomerDto[]>(results);
    }

    public async Task<GuestSupplierCustomerCompanyDto[]> GetGuestSupplierCustomers(
        string companyId,
        CancellationToken cancellationToken)
    {
        var invoices = await _dbContext.Invoice.Find(x => x.CompanyId.Equals(companyId))
            .ToListAsync(cancellationToken);

        var customersGroup = invoices.Where(x => !string.IsNullOrWhiteSpace(x.PayerId))
            .GroupBy(x => x.PayerId)
            .Select(x => new GuestSupplierCustomerCompanyDto
            {
                CompanyId = x.Key,
                AmountOfInvoices = x.Sum(i => i.TotalAmount),
                NumberOfInvoices = x.Count()
            }).ToArray();

        return customersGroup;
    }

    public async Task<CustomerDto?> GetByIntegrationContactIdAsync(string contactId, string integrationId, CancellationToken cancellationToken)
    {
        var customer = await _dbContext.Customers
            .Find(x => x.Integration != null &&
                       !string.IsNullOrEmpty(x.Integration.ConnectorContactId) &&
                       !string.IsNullOrEmpty(x.Integration.IntegrationId) &&
                       x.Integration.ConnectorContactId.Equals(contactId) &&
                       x.Integration.IntegrationId.Equals(integrationId))
            .FirstOrDefaultAsync(cancellationToken);

        if (customer == null)
            return default;

        return _mapper.Map<CustomerDto>(customer);
    }

    public async Task<CustomerDto[]?> GetByIdsAsync(string[] btCustomerIds, CancellationToken cancellationToken)
    {
        var customer = await _dbContext.Customers
            .Find(x => btCustomerIds.Contains(x.BlueTapeCustomerId))
            .ToListAsync(cancellationToken);

        if (customer == null)
            return default;

        return _mapper.Map<CustomerDto[]>(customer);
    }

    public async Task<CustomerDto[]?> GetByCompanyIdsAsync(string[] companyIds, CancellationToken cancellationToken)
    {
        var customer = await _dbContext.Customers
            .Find(x => companyIds.Contains(x.CompanyId))
            .ToListAsync(cancellationToken);

        return customer == null ? default : _mapper.Map<CustomerDto[]>(customer);
    }


    public async Task<CustomerDto[]?> GetBillingContactsAsync(string btCustomerId, CancellationToken cancellationToken)
    {
        var customers = await _dbContext.Customers
            .Find(x => !string.IsNullOrEmpty(x.ParentId) && x.ParentId.Equals(btCustomerId))
            .ToListAsync(cancellationToken);

        return customers == null ? null : _mapper.Map<CustomerDto[]>(customers);
    }

    public async Task<CustomerDto?> SetCustomerIhcSettings(string btCustomerId, UpdateCustomerModel updateCustomerModel, CancellationToken cancellationToken)
    {
        var customer = await _dbContext.Customers
            .Find(x => x.BlueTapeCustomerId.Equals(btCustomerId))
            .FirstOrDefaultAsync(cancellationToken);

        if (customer == null)
            return null;

        var update = Builders<CustomerEntity>.Update
            .Set(doc => doc.Type, "ihc")
            .Set(doc => doc.Status, "IN-HOUSE CREDIT");

        if (customer.Settings is null)
            update = update.Set(doc => doc.Settings, new CustomerSettingsEntity()
            {
                InHouseCredit = new InHouseCreditEntity()
                {
                    IsEnabled = true,
                    RecoursePercentage = updateCustomerModel.ResourcePercentage
                }
            });
        else if (customer.Settings.InHouseCredit is null)
        {
            update = update.Set(doc => doc.Settings!.InHouseCredit, new InHouseCreditEntity()
            {
                IsEnabled = true,
                RecoursePercentage = updateCustomerModel.ResourcePercentage,
                FactoringTerm = updateCustomerModel.FactoringTerm,
                SupplierPackage = updateCustomerModel.SupplierPackage,
                Limit = updateCustomerModel.Limit,
            });
        }
        else
        {
            update = update.Set(doc => doc.Settings!.InHouseCredit, new InHouseCreditEntity()
            {
                IsEnabled = true,
                RecoursePercentage = updateCustomerModel.ResourcePercentage,
                FactoringTerm = customer.Settings.InHouseCredit.FactoringTerm ?? updateCustomerModel.FactoringTerm,
                SupplierPackage = customer.Settings.InHouseCredit.SupplierPackage ?? updateCustomerModel.SupplierPackage,
                Limit = updateCustomerModel.Limit ?? customer.Settings.InHouseCredit.Limit,
            });
        }

        var filter = Builders<CustomerEntity>.Filter.Where(doc => doc.BlueTapeCustomerId == btCustomerId);

        var updateResult = await _dbContext.Customers.UpdateOneAsync(filter, update, cancellationToken: cancellationToken);

        if (updateResult.MatchedCount == 0)
        {
            throw new InvalidOperationException("No document was updated, check the filter criteria.");
        }

        return _mapper.Map<CustomerDto>(customer);
    }
}