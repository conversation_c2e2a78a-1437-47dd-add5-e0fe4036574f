using Amazon;
using Amazon.Extensions.NETCore.Setup;
using Amazon.KeyManagementService;
using Amazon.SecretsManager;
using Azure.Extensions.AspNetCore.Configuration.Secrets;
using Azure.Identity;
using BlueTape.Aion.Application.DI;
using BlueTape.Aion.Domain.Constants;
using BlueTape.AzureKeyVault.Abstractions;
using BlueTape.AzureKeyVault.Services;
using BlueTape.Common.ExceptionHandling.Exceptions;
using BlueTape.Common.Extensions.Abstractions;
using BlueTape.Integrations.Aion.AzureTableStorage.DI;
using BlueTape.LambdaBase;
using BlueTape.Services.Utilities.AWS;
using BlueTape.Services.Utilities.Options;
using Microsoft.Azure.Functions.Worker;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;
using Serilog;

var host = new HostBuilder()
    .ConfigureFunctionsWorkerDefaults()
    .ConfigureAppConfiguration(x =>
    {
        x.SetBasePath(Environment.CurrentDirectory);
        x.AddJsonFile("appsettings.json", optional: false);
        x.AddJsonFile($"appsettings.{Environment.GetEnvironmentVariable(EnvironmentConstants.Environment)}.json", optional: true);
        var keyVaultUri = new Uri(Environment.GetEnvironmentVariable(LoggerConstants.KeyVaultUri) ??
                                  throw new VariableNullException(nameof(LoggerConstants.KeyVaultUri)));
        var azureCredentials = new DefaultAzureCredential();
        x.AddAzureKeyVault(keyVaultUri, azureCredentials, new AzureKeyVaultConfigurationOptions
        {
            ReloadInterval = TimeSpan.FromMinutes(EnvironmentConstants.MinutestKeyVaultReload)
        });
        x.AddEnvironmentVariables();
    })
    .UseSerilog((hostingContext, loggerConfiguration) =>
    {
        var config = hostingContext.Configuration;
        loggerConfiguration
            .ReadFrom.Configuration(hostingContext.Configuration)
            .Enrich.FromGlobalLogContext()
            .Enrich.WithProperty(LoggerConstants.ProjectName, LoggerConstants.ProjectValue)
            .Enrich.WithProperty("EnvironmentName", hostingContext.HostingEnvironment.EnvironmentName)
            .Enrich.WithProperty("ContentRootPath", hostingContext.HostingEnvironment.ContentRootPath)
            .WriteTo.ApplicationInsights(config.GetSection(LoggerConstants.AppInsightsConnection).Value,
                TelemetryConverter.Traces);

        loggerConfiguration.WriteTo.Console();
    })
    .ConfigureServices((hostBuilderContext, services) =>
    {
        var configuration = hostBuilderContext.Configuration;
        services.AddOptions();
        services.Configure<BlueTapeOptions>(configuration.GetSection(nameof(BlueTapeOptions)));
        services.AddDefaultAWSOptions(configuration.GetAWSOptions());
        services.AddAWSService<IAmazonSecretsManager>(new AWSOptions { Region = RegionEndpoint.USWest1 });
        services.AddAWSService<IAmazonKeyManagementService>(new AWSOptions { Region = RegionEndpoint.USWest1 });
        services.AddSingleton<ISecretsManagerService, AwsSecretsManagerService>();
        services.AddSingleton<IKeyVaultService, KeyVaultService>();
        services.AddApplicationDependencies(configuration);
        services.AddScoped<ITraceIdAccessor, LambdaTraceIdAccessor>();
        services.AddMemoryCache();

        services.AddAzureDataTableDependencies(configuration);

        var env = Environment.GetEnvironmentVariable("ASPNETCORE_ENVIRONMENT");
        if (!string.IsNullOrEmpty(env))
        {
            var serviceProvider = services.BuildServiceProvider();
            var keyVaultService = serviceProvider.GetRequiredService<IKeyVaultService>();
            var appInsightsConnectionString = keyVaultService.GetSecret(LoggerConstants.AppInsightsConnection).GetAwaiter().GetResult();

            services.AddApplicationInsightsTelemetryWorkerService(x => x.ConnectionString = appInsightsConnectionString);
            services.ConfigureFunctionsApplicationInsights();
        }

        services.Configure<LoggerFilterOptions>(options =>
        {
            var toRemove = options.Rules.FirstOrDefault(rule => rule.ProviderName
                                                                == "Microsoft.Extensions.Logging.ApplicationInsights.ApplicationInsightsLoggerProvider");

            if (toRemove is not null)
            {
                options.Rules.Remove(toRemove);
            }
        });
    })
    .Build();

await host.RunAsync();