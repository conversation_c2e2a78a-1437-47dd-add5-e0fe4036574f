﻿<Project Sdk="Microsoft.NET.Sdk">

    <PropertyGroup>
        <TargetFramework>net8.0</TargetFramework>
        <Nullable>enable</Nullable>
        <IsPackable>false</IsPackable>
        <SonarQubeExclude>true</SonarQubeExclude>
        <PlatformTarget>AnyCPU</PlatformTarget>
    </PropertyGroup>

    <ItemGroup>
        <PackageReference Include="AutoFixture" Version="4.18.1" />
        <PackageReference Include="AutoFixture.Xunit2" Version="4.18.1" />
        <PackageReference Include="Microsoft.NET.Test.Sdk" Version="17.8.0" />
        <PackageReference Include="Moq" Version="4.20.70" />
        <PackageReference Include="NSubstitute" Version="5.1.0" />
        <PackageReference Include="Shouldly" Version="4.2.1" />
        <PackageReference Include="xunit" Version="2.6.4" />
        <PackageReference Include="xunit.runner.visualstudio" Version="2.5.6">
            <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
            <PrivateAssets>all</PrivateAssets>
        </PackageReference>
        <PackageReference Include="coverlet.collector" Version="6.0.0">
            <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
            <PrivateAssets>all</PrivateAssets>
        </PackageReference>
    </ItemGroup>

    <ItemGroup>
      <ProjectReference Include="..\BlueTape.Aion.Application\BlueTape.Aion.Application.csproj" />
    </ItemGroup>

</Project>
