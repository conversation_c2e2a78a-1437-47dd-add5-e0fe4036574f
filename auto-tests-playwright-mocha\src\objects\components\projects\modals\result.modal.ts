import {BasePage} from '../../../base.page';

export class ResultModal extends BasePage {
    constructor(page){
        super(page);
    };

    containers = {
        mainContainer: this.page.locator('_react=[key=".0"]'),
    };

    textMessage = {
        message: this.containers.mainContainer.locator('_react=[i18nKey]'),
    };

    buttons = {
        close: this.containers.mainContainer.locator('_react=[testID="close"]'),
    };
}