﻿using BlueTape.Aion.DataAccess.External.Constants;
using BlueTape.Integrations.Aion.Infrastructure.Enums;
using Microsoft.OpenApi.Any;
using Microsoft.OpenApi.Models;
using Swashbuckle.AspNetCore.SwaggerGen;
using System.Diagnostics.CodeAnalysis;

namespace BlueTape.Aion.API.Swagger;

[ExcludeFromCodeCoverage]
public class PaymentSubscriptionHeaderFilter : IOperationFilter
{
    public void Apply(OpenApiOperation operation, OperationFilterContext context)
    {
        var existingHeaderParameter = operation.Parameters
            .FirstOrDefault(p => p.Name == ClientConstants.PaymentSubscriptionTypeHeader && p.In == ParameterLocation.Header);

        if (existingHeaderParameter is null) return;

        operation.Parameters.Remove(existingHeaderParameter);
        var subscriptionTypes = Enum.GetNames(typeof(PaymentSubscriptionType)).ToList();
        operation.Parameters.Add(new OpenApiParameter
        {
            Name = ClientConstants.PaymentSubscriptionTypeHeader,
            In = ParameterLocation.Header,
            Description = "Select payment subscription type",
            Required = true,
            Schema = new OpenApiSchema
            {
                Type = "string",
                Enum = subscriptionTypes.Select(value => (IOpenApiAny)new OpenApiString(value)).ToList()
            }
        });
    }
}