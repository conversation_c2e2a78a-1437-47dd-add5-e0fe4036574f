import {expect} from '@playwright/test';
import {test} from '../../test-utils';
import {sendLMSRequest} from '../../../api/common/lms-send-request';
import {validate} from 'jsonschema';

const constants = JSON.parse(JSON.stringify(require('../../../constants/LMStestData.json')));

test.describe(`Loan Templates. CRUD API Tests @LMS @API`, async () => {

    let templateId: string;

    /**
     * Positive tests
     */

    test(`Create Loan Template.`, async () => {
        const requestBody = constants.loanTemplates.TemplateWithTwoInstallments;

        const response = await sendLMSRequest('post', 'LoanTemplates', requestBody);

        templateId = response.data.id;

        expect(response.status, `Status code 200`)
            .toEqual(200);

        expect(response.data, `Response contains Object`)
            .toEqual(expect.any(Object));

        const validationResult = validate(response.data, constants.loanTemplateSchema);

        expect(validationResult.valid, `JSON Schema is correct`)
            .toBeTruthy();
    });

    test(`Read Loan Template by ID.`, async () => {
        const response = await sendLMSRequest('get', `LoanTemplates/${templateId}`);

        expect(response.status, `Status code 200`)
            .toEqual(200);

        expect(response.data, `Response contains Object`)
            .toEqual(expect.any(Object));

        expect(response.data.id, `Loan created by expected Template`)
            .toBe(templateId);

        const validationResult = validate(response.data, constants.loanTemplateSchema);

        expect(validationResult.valid, `JSON Schema is correct`)
            .toBeTruthy();
    });

    test(`Update Loan Template by ID.`, async () => {
        const requestBody = constants.loanTemplates.TemplateWithThreeInstallments;

        const response = await sendLMSRequest('put', `LoanTemplates/${templateId}`, requestBody);

        expect(response.status, `Status code 200`)
            .toEqual(200);

        expect(response.data, `Response Data contains Object`)
            .toEqual(expect.any(Object));

        expect(response.data.installmentsNumber, `Response contains correct Number of Installments`)
            .toBe(constants.loanTemplates.TemplateWithThreeInstallments.InstallmentsNumber);
    });

    test(`Delete Loan Template by ID.`, async () => {
        const response = await sendLMSRequest('delete', `LoanTemplates/${templateId}`);

        expect(response.status, `Status code 200`)
            .toEqual(200);
    });

    test(`Deleted Template isn't returned`, async () => {
        const response = await sendLMSRequest('get', `LoanTemplates/${templateId}`);

        expect(response.status, `Status code 204`)
            .toEqual(204);

        expect(response.statusText, `Status test id 'No Content'`)
            .toEqual('No Content');
    });

    test(`Get all Loan Templates.`, async () => {
        const response = await sendLMSRequest('get', 'LoanTemplates');

        expect(response.status, `Status code 200`)
            .toBe(200);

        expect(response.data, `Response data exists`)
            .toBeDefined();

        expect(Array.isArray(response.data), `Response data is returned as Array`)
            .toBe(true);

        expect(response.data.length, `Response Array greater than 0`)
            .toBeGreaterThan(0);

        expect(response.data[0], `Loan Template contains ID`)
            .toHaveProperty('id');
    });

    /**
     * Negative Tests
     */

    test(`Server validation for GET request by invalid Template ID.`, async () => {
        const response = await sendLMSRequest('get', `Loans/${constants.invalidLoanTemplateIDs.id}`);

        expect(response.response.status, `Status code 500. Response Body is empty`)
            .toEqual(500);
    });

    test(`Cannot Create Loan Template with Zero Installments.`, async () => {
        const requestBody = constants.loanTemplates.TemplateWithZeroInstallments;

        const response = await sendLMSRequest('post', 'LoanTemplates', requestBody);

        expect(response.response.status, `InstallmentsNumber should be greater than 0`)
            .toEqual(400);
    });
});
