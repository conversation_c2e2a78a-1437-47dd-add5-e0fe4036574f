import {BasePage} from '../../base.page';

export class BuyNowPayLater extends BasePage {
    constructor(page){
        super(page);
    };

    buttons = {
        continue: this.page.locator('"Continue"'),
        customerPaysChoosePlan: this.page.locator('"Choose plan" >> nth=0'),
        agreeContinue: this.page.locator('"Agree & Sign"'),
    };

    async fillUpBuyNowPayLater(){
        await this.buttons.continue.click();
        await this.buttons.customerPaysChoosePlan.click();
        await this.buttons.continue.click();
        await this.buttons.customerPaysChoosePlan.click();
        await this.buttons.continue.click();
        await this.buttons.agreeContinue.click();
    };
}