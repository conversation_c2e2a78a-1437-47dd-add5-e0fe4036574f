using Azure.Extensions.AspNetCore.Configuration.Secrets;
using Azure.Identity;
using BlueTape.Aion.API.Constants;
using BlueTape.Aion.API.Extensions;
using BlueTape.Aion.API.Handlers;
using BlueTape.Aion.API.Mappers;
using BlueTape.Aion.API.Middlewares;
using BlueTape.Aion.API.Models.Errors;
using BlueTape.Aion.API.Swagger;
using BlueTape.Aion.Application.DI;
using BlueTape.Aion.Domain.Constants;
using BlueTape.AzureKeyVault.Abstractions;
using BlueTape.AzureKeyVault.Services;
using BlueTape.Services.Utilities.AspNetCore.Tracing;
using BlueTape.Services.Utilities.Options;
using BueTape.Aion.Infrastructure.Extensions;
using FluentValidation.AspNetCore;
using Microsoft.AspNetCore.Authentication;
using Microsoft.AspNetCore.HttpLogging;
using Microsoft.AspNetCore.HttpOverrides;
using Microsoft.AspNetCore.Mvc;
using Microsoft.OpenApi.Models;
using NSwag.Generation.Processors.Security;
using Serilog;
using System.Reflection;
using System.Text.Json;
using System.Text.Json.Serialization;

var builder = WebApplication.CreateBuilder(args);

var configuration = builder.Configuration;
var env = builder.Environment;

var keyVaultUri = new Uri(Environment.GetEnvironmentVariable(ConfigConstants.KeyVaultUri) ??
                          throw new VariableNullException(nameof(ConfigConstants.KeyVaultUri)));

var azureCredentials = new DefaultAzureCredential();
builder.Configuration.AddAzureKeyVault(keyVaultUri, azureCredentials, new AzureKeyVaultConfigurationOptions
{
    ReloadInterval = TimeSpan.FromMinutes(EnvironmentConstants.MinutestKeyVaultReload)
});

var appInsightsConnection = configuration.GetSection(ConfigConstants.AppInsightsConnection).Value;

builder.Services.AddApplicationInsightsTelemetry(cfg =>
{
    cfg.ConnectionString = appInsightsConnection;
});

builder.Host.UseSerilog((hostingContext, loggerConfiguration) =>
{
    loggerConfiguration
        .ReadFrom.Configuration(configuration)
        .Enrich.FromGlobalLogContext()
        .Enrich.WithProperty(LoggerConstants.ProjectName, LoggerConstants.ProjectValue)
        .Enrich.WithProperty(LoggerConstants.EnvironmentName, hostingContext.HostingEnvironment.EnvironmentName)
        .Enrich.WithProperty(LoggerConstants.ContentRootPath, hostingContext.HostingEnvironment.ContentRootPath)
        .WriteTo.ApplicationInsights(appInsightsConnection, TelemetryConverter.Traces);

    loggerConfiguration.WriteTo.Console();
});

// Add services to the container.
builder.Services.AddControllers()
    .AddJsonOptions(op =>
    {
        op.JsonSerializerOptions.ReferenceHandler = ReferenceHandler.IgnoreCycles;
        op.JsonSerializerOptions.Converters.Add(new JsonStringEnumConverter());
        op.JsonSerializerOptions.PropertyNamingPolicy = JsonNamingPolicy.CamelCase;
    })
    .ConfigureApiBehaviorOptions(options =>
    {
        options.InvalidModelStateResponseFactory = context =>
        {
            var errors = context.ModelState.Values
                .SelectMany(v => v.Errors)
                .Select(e => new ErrorModel()
                {
                    ErrorType = ErrorType.ValidationError,
                    Reason = e.ErrorMessage,
                    Code = "InvalidModelState"
                });

            return new BadRequestObjectResult(errors);
        };
    });

builder.Services.AddFluentValidation(fv =>
{
    fv.RegisterValidatorsFromAssemblyContaining<Program>();
});

builder.Services.AddAutoMapper(typeof(ApiProfile).GetTypeInfo().Assembly);

builder.Services.AddAuthentication(o =>
{
    o.DefaultScheme = AuthenticationConstants.ApiKeyAuthScheme;
    o.DefaultChallengeScheme = AuthenticationConstants.ApiKeyAuthScheme;
})
    .AddScheme<AuthenticationSchemeOptions, ApiKeyHandler>(AuthenticationConstants.ApiKeyAuthScheme, o => { });

builder.Services.AddCors(options =>
{
    var origins = builder.Configuration.GetSection("AllowOrigins").Get<string[]>();
    options.AddPolicy("AllowOrigins", policyBuilder =>
    {
        if (origins != null) policyBuilder.WithOrigins(origins).AllowAnyHeader().AllowAnyMethod();
    });
});

// Add AWS Lambda support. When application is run in Lambda Kestrel is swapped out as the web server with Amazon.Lambda.AspNetCoreServer. This
// package will act as the webserver translating request and responses between the Lambda event source and ASP.NET Core.
builder.Services.AddAWSLambdaHosting(LambdaEventSource.RestApi); // NOTE: This appear to be the new way of initializing lambdas
builder.Services.AddDefaultAWSOptions(configuration.GetAWSOptions());
builder.Services.AddOptions();
builder.Services.Configure<BlueTapeOptions>(builder.Configuration.GetSection(nameof(BlueTapeOptions)));

builder.Services.AddEndpointsApiExplorer();
builder.Services.AddSwaggerGen(c =>
{
    var xmlFilename = $"{Assembly.GetExecutingAssembly().GetName().Name}.xml";
    c.IncludeXmlComments(Path.Combine(AppContext.BaseDirectory, xmlFilename));
    c.AddSecurityDefinition(AuthenticationConstants.ApiKeyAuthScheme,
        new OpenApiSecurityScheme
        {
            Description = "API key used in the Authorization header.",
            Name = AuthenticationConstants.ApiKeyName,
            In = ParameterLocation.Header,
            Type = SecuritySchemeType.ApiKey
        });
    c.AddSecurityRequirement(new OpenApiSecurityRequirement
        {
            {
                new OpenApiSecurityScheme
                {
                    Reference = new OpenApiReference
                    {
                        Type = ReferenceType.SecurityScheme,
                        Id = AuthenticationConstants.ApiKeyAuthScheme
                    }
                },
                new List<string>()
            }
    });
    c.OperationFilter<PaymentSubscriptionHeaderFilter>();
});

builder.Services.AddHttpLogging(c =>
{
    c.LoggingFields = HttpLoggingFields.All;
});

builder.Services.AddBlueTapeTracing();

builder.Services.AddMemoryCache();

builder.Services.AddAWSServices(builder.Configuration);
builder.Services.AddUtilities();

builder.Services.AddTransient<TraceIdDelegatingHandler>();
builder.Services.AddApplicationDependencies(configuration);
builder.Services.AddSingleton<IKeyVaultService, KeyVaultService>();

builder.Services.AddOpenApiDocument(c =>
{
    c.Title = "BlueTape Aion API";
    c.OperationProcessors.Add(new AspNetCoreOperationSecurityScopeProcessor("ApiKey"));
});
builder.Services.AddHealthChecks();

var app = builder.Build();

app.UseBlueTapeTracing();
app.UseMiddleware<AionLoggingMiddleware>();
app.UseMiddleware<ExceptionMiddleware>();

var forwardedHeaderOptions = new ForwardedHeadersOptions
{
    ForwardedHeaders = ForwardedHeaders.All
};
forwardedHeaderOptions.KnownNetworks.Clear();
forwardedHeaderOptions.KnownProxies.Clear();

app.UseForwardedHeaders(forwardedHeaderOptions);

app.UseHttpLogging();

app.UseHttpsRedirection();

app.UseCors("AllowOrigins");

if (!env.IsProduction())
{
    app.UseSwagger(opt => opt.PreSerializeFilters.Add((swagger, httpReq) =>
    {
        if (!httpReq.Host.Host.Contains(RouteConstants.LocalHost) && !env.IsEnvironment(ConfigConstants.Dev))
        {
            swagger.Servers = new List<OpenApiServer> { new() { Url = RouteConstants.Suffix } };
        }
    }));
    app.UseSwaggerUI();
}

app.UseRouting();

app.UseAuthentication();
app.UseAuthorization();

app.MapControllers();

app.MapHealthChecks("/health");

app.Run();

Log.CloseAndFlush();