import { useTranslation } from 'react-i18next'
import type { DescriptionsProps } from 'antd'
import { isString } from 'lodash'

import { getDaysFromNow } from '@/globals/utils/masks'
import StyledDescription from '@/components/common/Description'
import type { IGetPaidApplicationOwnersData } from '@/lib/redux/api/get-paid-application/types'
import { formatDateDay, getAutomatedDecisionResultBadge } from '@/globals/utils'

interface IProps {
  data: IGetPaidApplicationOwnersData
}

const RiskDescription = ({ data }: IProps): JSX.Element => {
  const { t } = useTranslation<string | undefined>()

  const instantIdSsnItems: DescriptionsProps['items'] = [
    {
      label: `${t('getPaidApplication.page.detailed.tabs.kyc.fraudpoint')}:`,
      children: getAutomatedDecisionResultBadge(
        data.fraudPointScore.result,
        data.fraudPointScore.value ?? t('na'),
      ),
    },
    {
      label: `${t('getPaidApplication.page.detailed.tabs.kyc.emailageEaScore')}:`,
      children: getAutomatedDecisionResultBadge(
        data.eaScore.result,
        data.eaScore.value ?? t('na'),
      ),
    },
    {
      label: `${t('getPaidApplication.page.detailed.tabs.kyc.emailageIpRiskScore')}:`,
      children: data.ipRiskLevel.value ?? t('na'),
    },
    {
      label: `${t('getPaidApplication.page.detailed.tabs.kyc.emailageDomainRiskScore')}:`,
      children: data.domainRiskLevel.value ?? t('na'),
    },
    {
      label: `${t('getPaidApplication.page.detailed.tabs.kyc.fico')}:`,
      children: getAutomatedDecisionResultBadge(
        data.ficoScore.result,
        data.ficoScore.value ?? t('na'),
      ),
    },
    {
      label: `${t('getPaidApplication.page.detailed.tabs.kyc.personalBankruptcy')}:`,
      children: getAutomatedDecisionResultBadge(
        data.lastPersonalBankruptcy.result,
        isString(data.lastPersonalBankruptcy.value)
          ? `${formatDateDay(data.lastPersonalBankruptcy.value)} (${t('day', {
              count: getDaysFromNow(data.lastPersonalBankruptcy.value),
            })})`
          : t('na'),
      ),
    },
    {
      label: `${t('getPaidApplication.page.detailed.tabs.kyc.inquiriesDuringLast6Months')}:`,
      children: data.inquiries.value ?? t('na'),
    },
  ]

  return <StyledDescription items={instantIdSsnItems} />
}

export default RiskDescription
