﻿using BlueTape.Aion.Application.Abstractions;
using BlueTape.Aion.Application.Models.InternalTransfer;
using BlueTape.Aion.DataAccess.External.Abstractions;
using BlueTape.Aion.DataAccess.External.Models.InternalTransfer;
using BlueTape.Aion.DataAccess.MongoDB.Abstractions;
using BlueTape.Integrations.Aion;
using BueTape.Aion.Infrastructure.Exceptions;
using BueTape.Aion.Infrastructure.Extensions;
using BueTape.Aion.Infrastructure.Options;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Options;
using VariableNullException = BlueTape.Common.ExceptionHandling.Exceptions.VariableNullException;

namespace BlueTape.Aion.Application.Service;

public class InternalTransferService : IInternalTransferService
{
    private readonly IAionHttpClient _aionHttpClient;
    private readonly ITransactionService _transactionService;
    private readonly IConfiguration _configuration;
    private readonly IAionLoggingService _aionLoggingService;
    private readonly AionInternalTransferOptions _internalTransferOptions;

    public InternalTransferService(
        IAionHttpClient aionHttpClient,
        IOptions<AionInternalTransferOptions> options,
        ITransactionService transactionService,
        IConfiguration configuration,
        IAionLoggingService aionLoggingService)
    {
        _aionHttpClient = aionHttpClient;
        _transactionService = transactionService;
        _configuration = configuration;
        _aionLoggingService = aionLoggingService;
        _internalTransferOptions = options.Value;
    }

    public async Task<BookTransferObj> CreateInternalTransferAsync(CreateInternal createInternal, string paymentSubscriptionType, CancellationToken ctx)
    {
        _internalTransferOptions.AccountNumberSecretNames
            .TryGetValue(createInternal.Originator.AccountCode, out var originatorSecret);
        _internalTransferOptions.AccountNumberSecretNames
            .TryGetValue(createInternal.Receiver.AccountCode, out var receiverSecret);

        if (string.IsNullOrEmpty(originatorSecret))
            throw new ParameterNotExistException(nameof(createInternal.Originator.AccountCode));

        if (string.IsNullOrEmpty(receiverSecret))
            throw new ParameterNotExistException(nameof(createInternal.Receiver.AccountCode));

        var originatorAccountNumber = _configuration[originatorSecret] ?? throw new VariableNullException(nameof(originatorSecret));
        var receiverAccountNumber = _configuration[receiverSecret] ?? throw new VariableNullException(nameof(receiverSecret));

        if (string.IsNullOrEmpty(originatorAccountNumber))
            throw new ParameterNotExistException(nameof(originatorAccountNumber));

        if (string.IsNullOrEmpty(receiverAccountNumber))
            throw new ParameterNotExistException(nameof(receiverAccountNumber));

        await _transactionService.EnsureTransactionDoesNotExist(createInternal.TransactionId, createInternal.TransactionNumber, ctx);

        var paymentSubscription = paymentSubscriptionType.ParseToPaymentSubscription();
        var request = new InternalTransferRequest()
        {
            FromAccountNumber = receiverAccountNumber,
            ToAccountNumber = originatorAccountNumber,
            SenderName = createInternal.Originator.Name,
            SenderDescription = createInternal.Originator.Description,
            ReceiverName = createInternal.Receiver.Name,
            ReceiverDescription = createInternal.Receiver.Description,
            ContextIdentifier = createInternal.TransactionNumber,
            Amount = createInternal.Amount.RoundDecimalToString()
        };

        var result = await _aionHttpClient.CreateInternalTransfer(request, paymentSubscription, ctx);
        await _transactionService.SaveAionCreatedInternalTransactionAsync(result.BookTransferObj, ctx);
        return result.BookTransferObj;
    }

    public async Task<BlueTapeTransactionResponseModel> CreateInternalTransferAsyncV2(CreateInternal createInternal, string paymentSubscriptionType,
        CancellationToken ctx)
    {
        var result = await CreateInternalTransferAsync(createInternal, paymentSubscriptionType, ctx);
        return new BlueTapeTransactionResponseModel()
        {
            AionFee = 0,
            AionReferenceId = result.TraceNumber,
            AionResponse = _aionLoggingService.GetMaskedJson(result)
        };
    }
}