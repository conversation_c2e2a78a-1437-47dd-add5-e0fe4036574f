import {BasePage} from '../../../../base.page';

export class CustomerDetailsModal extends BasePage {
    constructor(page){
        super(page);
    };

    labels = {
        transactionNumber: this.page.locator('[data-testid="Transaction number_item"] > .r-16dba41')
    };

    buttons = {
        addInvoice: this.page.locator('[data-testid="account_sidebar_footer_add_invoice_button"]'),
        editCustomer: this.page.locator('[data-testid="account_sidebar_footer_edit_customer_button"]'),
        resendInvite: this.page.locator('[data-testid="account_sidebar_footer_resend_button"]'),
        addPaymentMethod: this.page.locator('[data-testid="add-payment"]'),
    };
}