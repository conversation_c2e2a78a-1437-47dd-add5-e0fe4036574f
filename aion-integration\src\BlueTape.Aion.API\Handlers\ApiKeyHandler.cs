﻿using BlueTape.Aion.API.Constants;
using Microsoft.AspNetCore.Authentication;
using Microsoft.Extensions.Options;
using System.Diagnostics.CodeAnalysis;
using System.Security.Claims;
using System.Text.Encodings.Web;

namespace BlueTape.Aion.API.Handlers;

[ExcludeFromCodeCoverage(Justification = "Will be covered when moved to package")]
public class ApiKeyHandler : AuthenticationHandler<AuthenticationSchemeOptions>
{
    private readonly IConfiguration _configuration;
    private readonly ILogger<ApiKeyHandler> _logger;

    public ApiKeyHandler(
        IOptionsMonitor<AuthenticationSchemeOptions> options,
        ILoggerFactory logger,
        UrlEncoder encoder,
        ISystemClock clock,
        IConfiguration configuration
    ) : base(options, logger, encoder, clock)
    {
        _configuration = configuration;
        _logger = logger.CreateLogger<ApiKeyHandler>();
    }

    protected override Task<AuthenticateResult> HandleAuthenticateAsync()
    {
        var principal = new ClaimsPrincipal(new ClaimsIdentity(Enumerable.Empty<Claim>(), AuthenticationConstants.ApiKeyAuthScheme));
        var ticket = new AuthenticationTicket(principal, Scheme.Name);

#if DEBUG
        return Task.FromResult(AuthenticateResult.Success(ticket));
#else

        if (!Request.Headers.TryGetValue(AuthenticationConstants.ApiKeyName, out var extractedApiKey))
        {
            return Task.FromResult(AuthenticateResult.NoResult());
        }

        var apiKey = _configuration[ConfigConstants.AionApiKey] ??
                     throw new ArgumentNullException(nameof(ConfigConstants.AionApiKey));

        if (!apiKey.Equals(extractedApiKey))
        {
            _logger.LogWarning("Authentication failed");
            return Task.FromResult(AuthenticateResult.Fail("Authentication failed"));
        }

        return Task.FromResult(AuthenticateResult.Success(ticket));
#endif
    }
}