﻿using BlueTape.Services.DecisionEngine.BusinessLogic.Abstractions.Services;
using BlueTape.Services.DecisionEngine.BusinessLogic.Abstractions.Steps.DecisionEngine;
using BlueTape.Services.DecisionEngine.BusinessLogic.Constants;
using BlueTape.Services.DecisionEngine.BusinessLogic.Extensions;
using BlueTape.Services.DecisionEngine.BusinessLogic.Models.CreditApplication;
using BlueTape.Services.DecisionEngine.BusinessLogic.Models.DecisionEngineStep;
using BlueTape.Services.DecisionEngine.BusinessLogic.Models.StepInputs.DecisionEngine;
using BlueTape.Services.DecisionEngine.DataAccess.External.Abstractions.Providers;
using BlueTape.Services.DecisionEngine.Infrastructure.Exceptions;
using BlueTape.Services.DecisionEngine.PolicyRulesFramework.Domain.Enums;
using BlueTape.Services.DecisionEngine.PolicyRulesFramework.Domain.Models.Options;
using BlueTape.Services.DecisionEngine.PolicyRulesFramework.PolicyRulesEngine.Abstractions.Providers;
using BlueTape.Services.DecisionEngine.PolicyRulesFramework.PolicyRulesEngine.Comparisons;
using BlueTape.Utilities.Extensions;
using Microsoft.Extensions.Logging;
using System.Diagnostics.CodeAnalysis;

namespace BlueTape.Services.DecisionEngine.BusinessLogic.Steps.DecisionEngine;

/// <summary>
/// This step is responsible for re-evaluating the account/credit status of the company based on the latest results of the decision engine steps (including manually ignored ones)
/// </summary>
public class AccountStatusEvaluationStep : DecisionEngineGenericStep<StepInputBase, DecisionEngineStepModel>, IAccountStatusEvaluationStep
{
    private readonly IOnBoardingService _onBoardingService;
    private readonly IPolicyRulesProvider _policyRulesProvider;
    private readonly IStepHandlingService _stepHandlingService;
    private readonly ILoanService _loanService;
    private readonly ILogger<AccountStatusEvaluationStep> _logger;

    public AccountStatusEvaluationStep(ITraceIdProvider traceIdProvider,
        IStepHandlingService stepHandlingService,
        ILogger<AccountStatusEvaluationStep> logger,
        IPolicyRulesProvider policyRulesProvider,
        ILoanService loanService,
        IOnBoardingService onBoardingService) : base(traceIdProvider, policyRulesProvider, stepHandlingService, onBoardingService)
    {
        _onBoardingService = onBoardingService;
        _logger = logger;
        _policyRulesProvider = policyRulesProvider;
        _loanService = loanService;
        _stepHandlingService = stepHandlingService;
    }

    public async Task<DecisionEngineStepModel> Process(StepInputBase input, CancellationToken ct)
    {
        IGenericStep<StepInputBase, DecisionEngineStepModel>.ProcessStepDelegate processStep = ProcessStep;
        var result = await Process(processStep, input, StepsImplementationConstants.AccountStatusEvaluation, ct);

        return result;
    }

    private async Task<DecisionEngineStepModel> ProcessStep(StepInputBase input, CancellationToken ct)
    {
        _logger.LogInformation("Started AccountStatusEvaluation step execution");

        var executionId = input.ExecutionId;
        var stepNameWithExecutionId = $"{executionId}:{StepsImplementationConstants.AccountStatusEvaluation}";

        var creditApplication = await _onBoardingService.GetCreditApplicationById(input.CreditApplicationId, ct);
        if (creditApplication is null) throw new DtoNotFoundException($"Credit application with id {input.CreditApplicationId} not found");

        var policy = GetPolicyConfiguration(creditApplication!, input);
        if (policy is null)
        {
            return new DecisionEngineStepModel
            {
                CreditApplicationId = creditApplication.Id,
                ExecutionId = executionId,
                Status = ComparisonResult.Pass.ToString()
            };
        }

        //TODO: extract to enum
        var createDecisionStepDto = await CreateDecisionEngineStepByExecutionType(creditApplication, input, policy, "AccountStatusEvaluation", ct);

        _logger.LogInformation(
            "Started creating AccountStatusEvaluation step {stepNameWithExecutionId} for credit application {creditApplicationId}",
            stepNameWithExecutionId,
            input.CreditApplicationId);

        var currentStep = await _onBoardingService.CreateDecisionEngineStep(createDecisionStepDto, ct);

        _logger.LogInformation("Created AccountStatusEvaluation step {stepId}. Started calculating and updating account status",
            currentStep.Id);

        var latestScheduledExecutions =
            (await _onBoardingService.GetLatestDecisionEngineExecutionsByCompanyId(creditApplication.CompanyId!, ct));
        var statusComparer = new EvaluationResultComparer();
        ComparisonResult? finalStatus = null;
        var credit = (await _loanService.GetCreditsByCompanyId(creditApplication!.CompanyId!, false, ct))!
            .FirstOrDefault(x => x.CreditApplicationId == creditApplication.Id);
        foreach (var step in latestScheduledExecutions)
        {
            var stepStatus = GetStepResult(step);
            if (!stepStatus.HasValue) continue;

            if (IsStepStatusLessThanFinalOne(statusComparer, stepStatus, finalStatus))
                finalStatus = stepStatus;

            var stepName = AccountStatusConstants.StepNamesDictionary.GetValueOrDefault(step.Step!);
            _logger.LogInformation("Step {step} with execution id: {executionId} has status {status}", step.Step, step.ExecutionId, stepStatus);
            await _stepHandlingService.HandleRefreshStatus(stepStatus.ToString()!, credit, creditApplication.CompanyId!, stepNameWithExecutionId, stepName ?? string.Empty, ct);
        }

        _logger.LogInformation("Finished calculating and updating account status. Started updating AccountStatusEvaluation step {id}", currentStep.Id);

        var patchDecisionEngineStep = new UpdateDecisionEngineStepModel()
        {
            NewStatus = finalStatus.ToString(),
            UpdatedBy = stepNameWithExecutionId,
        };
        var updatedStep = await _onBoardingService.PatchDecisionEngineStep(currentStep.Id!, patchDecisionEngineStep, ct);

        _logger.LogInformation("Finished updating AccountStatusEvaluation step {id}", currentStep.Id);

        return updatedStep;
    }

    private static bool IsStepStatusLessThanFinalOne(EvaluationResultComparer statusComparer, [DisallowNull] ComparisonResult? stepStatus, ComparisonResult? finalStatus)
    {
        return !finalStatus.HasValue || statusComparer.Compare(stepStatus.Value, finalStatus.Value) == -1;
    }

    protected override PolicyConfigurationOptions? GetPolicyConfiguration(CreditApplicationModel creditApplication, StepInputBase input)
    {
        var policyType = creditApplication.Type.GetPolicyTypeByCreditApplicationType();
        var policy = _policyRulesProvider.GetPolicyConfiguration(policyType, string.Empty);
        if (policy is null || !policy.Enabled) return null;
        policy.ScheduledUpdate = input.ScheduledUpdate;

        return policy;
    }

    private static ComparisonResult? GetStepResult(DecisionEngineStepModel step)
    {
        var results = step.Results?.Select(GetStepResultStatus);
        if (results == null || !results.Any()) return null;

        return results.MinBy(s => s, new EvaluationResultComparer());
    }

    // Manual status has more priority, so we check it at first
    private static ComparisonResult GetStepResultStatus(DecisionEngineStepResultModel stepResult)
    {
        var status = stepResult.ManualResult;
        if (!string.IsNullOrEmpty(status) &&
            (status.Equals(ComparisonResult.Pass.ToString(), StringComparison.InvariantCultureIgnoreCase) ||
             status.Equals("passed", StringComparison.InvariantCultureIgnoreCase)))
        {
            return ComparisonResult.Pass;
        }

        return stepResult.Result.ParseToEnum<ComparisonResult>() ?? ComparisonResult.Pass;
    }
}
