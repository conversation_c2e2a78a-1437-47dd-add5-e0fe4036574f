﻿using AutoFixture;
using BlueTape.Aion.Application.Abstractions;
using BlueTape.Aion.DataAccess.External.Abstractions;
using BlueTape.Aion.DataAccess.External.Models.CreateCounterParty;
using BlueTape.Aion.DataAccess.MongoDB.Abstractions;
using BlueTape.Aion.DataAccess.MongoDB.Entities.User;
using BlueTape.Aion.DataAccess.MongoDB.Entities.UserRole;
using BlueTape.Aion.Domain.DTOs.Company;
using BlueTape.Application.Tests.Helper;
using BlueTape.CompanyClient.Abstractions;
using BlueTape.Integrations.Aion.Infrastructure.Enums;
using BlueTape.SNS.SlackNotification.Models;
using BueTape.Aion.Infrastructure.Exceptions;
using Microsoft.Extensions.Logging;
using Moq;
using System;
using System.Collections.Generic;
using System.Threading;
using System.Threading.Tasks;

using AionCompanyService = BlueTape.Aion.Application.Service.CompanyService;

namespace BlueTape.Application.Tests.Services;

public class CompanyServiceTests
{
    private readonly Mock<IAionHttpClient> _aionHttpClientMock = new();
    private readonly Mock<ICompanyRepository> _companyRepositoryMock = new();
    private readonly Mock<IUserRepository> _userRepositoryMock = new();
    private readonly Mock<IUserRoleRepository> _userRoleRepositoryMock = new();
    private readonly Mock<ILogger<AionCompanyService>> _loggerMock = new();
    private readonly Mock<ICompanyHttpClient> _companyHttpClientMock = new();
    private readonly Mock<IErrorNotificationService> _errorNotificationServiceMock = new();
    private Fixture _fixture = new();

    private AionCompanyService GetService()
    {
        return new AionCompanyService(
            _aionHttpClientMock.Object,
            _companyRepositoryMock.Object,
            _userRepositoryMock.Object,
            _userRoleRepositoryMock.Object,
            _companyHttpClientMock.Object,
            _errorNotificationServiceMock.Object,
            _loggerMock.Object);
    }

    private void VerifyNoOtherCalls()
    {
        _aionHttpClientMock.VerifyNoOtherCalls();
        _companyRepositoryMock.VerifyNoOtherCalls();
        _loggerMock.VerifyNoOtherCalls();
    }

    [Fact]
    public async Task PreProcessAchPullAsync_Throws_CompanyDoesNotExistException()
    {
        var companyId = _fixture.Create<string>();

        var service = GetService();

        await service.SyncCompanyWithAionAsync(companyId, PaymentSubscriptionType.SUBSCRIPTION1.ToString(), CancellationToken.None)
            .ShouldThrowAsync<CompanyDoesNotExistException>();

        _loggerMock.VerifyLogging(LogLevel.Information, Times.Once());

        _companyRepositoryMock.Verify(x =>
            x.GetByCompanyId(companyId, It.IsAny<CancellationToken>()), Times.Once);

        VerifyNoOtherCalls();
    }

    [Fact]
    public async Task PreProcessAchPullAsync_Execute_Success_With_Update()
    {
        var companyId = _fixture.Create<string>();
        var companyDto = _fixture.Create<CompanyDto>();
        companyDto.BlueTapeCompanyId = companyId;
        companyDto.AionSettings = null;
        var counterPartyResponse = _fixture.Create<CounterpartyResponse>();

        CompanyDto updateCompanyDtoCallbAck = null;

        _companyRepositoryMock.Setup(x =>
            x.GetByCompanyId(It.IsAny<string>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(companyDto);

        _aionHttpClientMock.Setup(x =>
            x.CreateCounterParty(It.IsAny<CreateCounterpartyRequest>(), PaymentSubscriptionType.SUBSCRIPTION1, It.IsAny<CancellationToken>()))
            .ReturnsAsync(counterPartyResponse);

        _companyRepositoryMock.Setup(x =>
            x.UpdateAionSettingsAsync(
                It.IsAny<CompanyDto>(),
                It.IsAny<CancellationToken>()))
            .Callback<CompanyDto, CancellationToken>((companyDto, ctx) => updateCompanyDtoCallbAck = companyDto);

        var exception = new Exception("Test exception");
        _companyHttpClientMock.Setup(x =>
            x.GetCompanyPaymentDetailsByIdAsync(companyId, It.IsAny<CancellationToken>()))
            .ThrowsAsync(exception);

        _errorNotificationServiceMock.Setup(x =>
            x.Notify(It.IsAny<EventMessageBody>(), It.IsAny<CancellationToken>()))
            .Returns(Task.CompletedTask)
            .Verifiable();

        var service = GetService();

        await service.SyncCompanyWithAionAsync(companyId, PaymentSubscriptionType.SUBSCRIPTION1.ToString(), CancellationToken.None);

        updateCompanyDtoCallbAck!.AionSettings.ShouldNotBeNull();
        updateCompanyDtoCallbAck!.AionSettings.CounterPartyId.ShouldBeEquivalentTo(counterPartyResponse.CounterpartyObject.Id);
        updateCompanyDtoCallbAck!.AionSettings.CounterPartyObjectId.ShouldBeEquivalentTo(counterPartyResponse.CounterpartyObject.ObjectId);

        _loggerMock.VerifyLogging(LogLevel.Information, Times.Exactly(3));

        _companyRepositoryMock.Verify(x =>
            x.GetByCompanyId(companyId, It.IsAny<CancellationToken>()), Times.Once());

        _aionHttpClientMock.Verify(x =>
            x.CreateCounterParty(It.IsAny<CreateCounterpartyRequest>(), PaymentSubscriptionType.SUBSCRIPTION1, It.IsAny<CancellationToken>()),
            Times.Once);

        _companyRepositoryMock.Verify(x =>
            x.UpdateAionSettingsAsync(
                It.IsAny<CompanyDto>(),
                It.IsAny<CancellationToken>()), Times.Once);

        _errorNotificationServiceMock.Verify();

        VerifyNoOtherCalls();
    }

    [Fact]
    public async Task PreProcessAchPullAsync_NoCompanyEmail_Success_With_Update()
    {
        var companyId = _fixture.Create<string>();
        var companyDto = _fixture.Create<CompanyDto>();
        companyDto.BlueTapeCompanyId = companyId;
        companyDto.AionSettings = null;
        companyDto.Email = null;
        var counterPartyResponse = _fixture.Create<CounterpartyResponse>();

        CompanyDto updateCompanyDtoCallback = null;

        _companyRepositoryMock.Setup(x =>
            x.GetByCompanyId(It.IsAny<string>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(companyDto);

        _aionHttpClientMock.Setup(x =>
                x.CreateCounterParty(It.IsAny<CreateCounterpartyRequest>(), PaymentSubscriptionType.SUBSCRIPTION1, It.IsAny<CancellationToken>()))
            .ReturnsAsync(counterPartyResponse);

        _companyRepositoryMock.Setup(x =>
            x.UpdateAionSettingsAsync(
                It.IsAny<CompanyDto>(),
                It.IsAny<CancellationToken>()))
            .Callback<CompanyDto, CancellationToken>((companyDto, ctx) => updateCompanyDtoCallback = companyDto);

        _userRoleRepositoryMock.Setup(x => x.GetByCompanyId(It.IsAny<string>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(new List<UserRoleEntity>() { new() { ExternalId = Guid.NewGuid().ToString() } });

        _userRepositoryMock.Setup(x => x.GetByExternalId(It.IsAny<IEnumerable<string>>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(new List<UserEntity>() { new() { Email = "<EMAIL>" } });

        var service = GetService();

        await service.SyncCompanyWithAionAsync(companyId, PaymentSubscriptionType.SUBSCRIPTION1.ToString(), CancellationToken.None);

        updateCompanyDtoCallback!.AionSettings.ShouldNotBeNull();
        updateCompanyDtoCallback!.AionSettings.CounterPartyId.ShouldBeEquivalentTo(counterPartyResponse.CounterpartyObject.Id);
        updateCompanyDtoCallback!.AionSettings.CounterPartyObjectId.ShouldBeEquivalentTo(counterPartyResponse.CounterpartyObject.ObjectId);

        _loggerMock.VerifyLogging(LogLevel.Information, Times.Exactly(3));

        _companyRepositoryMock.Verify(x =>
            x.GetByCompanyId(companyId, It.IsAny<CancellationToken>()), Times.Once());

        _aionHttpClientMock.Verify(x =>
            x.CreateCounterParty(It.IsAny<CreateCounterpartyRequest>(), PaymentSubscriptionType.SUBSCRIPTION1, It.IsAny<CancellationToken>()),
            Times.Once);

        _companyRepositoryMock.Verify(x =>
            x.UpdateAionSettingsAsync(
                It.IsAny<CompanyDto>(),
                It.IsAny<CancellationToken>()), Times.Once);

        VerifyNoOtherCalls();
    }

    [Fact]
    public async Task PreProcessAchPullAsync_NoCompanyName_Success_With_Update()
    {
        var companyId = _fixture.Create<string>();
        var companyDto = _fixture.Create<CompanyDto>();
        companyDto.BlueTapeCompanyId = companyId;
        companyDto.AionSettings = null;
        companyDto.Name = null;
        companyDto.LegalName = null;
        var counterPartyResponse = _fixture.Create<CounterpartyResponse>();

        var firstName = "testuser";
        var lastName = "SuName";

        CompanyDto updateCompanyDtoCallback = null;

        _companyRepositoryMock.Setup(x =>
            x.GetByCompanyId(It.IsAny<string>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(companyDto);

        _aionHttpClientMock.Setup(x =>
                x.CreateCounterParty(It.IsAny<CreateCounterpartyRequest>(), PaymentSubscriptionType.SUBSCRIPTION1, It.IsAny<CancellationToken>()))
            .ReturnsAsync(counterPartyResponse);

        _companyRepositoryMock.Setup(x =>
            x.UpdateAionSettingsAsync(
                It.IsAny<CompanyDto>(),
                It.IsAny<CancellationToken>()))
            .Callback<CompanyDto, CancellationToken>((companyDto, ctx) => updateCompanyDtoCallback = companyDto);

        _userRoleRepositoryMock.Setup(x => x.GetByCompanyId(It.IsAny<string>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(new List<UserRoleEntity>() { new() { ExternalId = Guid.NewGuid().ToString() } });

        _userRepositoryMock.Setup(x => x.GetByExternalId(It.IsAny<IEnumerable<string>>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(new List<UserEntity>() { new() { FirstName = firstName, LastName = lastName } });

        var service = GetService();

        await service.SyncCompanyWithAionAsync(companyId, PaymentSubscriptionType.SUBSCRIPTION1.ToString(), CancellationToken.None);

        updateCompanyDtoCallback!.AionSettings.ShouldNotBeNull();
        updateCompanyDtoCallback!.AionSettings.CounterPartyId.ShouldBeEquivalentTo(counterPartyResponse.CounterpartyObject.Id);
        updateCompanyDtoCallback!.AionSettings.CounterPartyObjectId.ShouldBeEquivalentTo(counterPartyResponse.CounterpartyObject.ObjectId);
        updateCompanyDtoCallback!.Name.ShouldBeEquivalentTo($"{firstName} {lastName}");

        _loggerMock.VerifyLogging(LogLevel.Information, Times.Exactly(3));

        _companyRepositoryMock.Verify(x =>
            x.GetByCompanyId(companyId, It.IsAny<CancellationToken>()), Times.Once());

        _aionHttpClientMock.Verify(x =>
            x.CreateCounterParty(It.IsAny<CreateCounterpartyRequest>(), PaymentSubscriptionType.SUBSCRIPTION1, It.IsAny<CancellationToken>()),
            Times.Once);

        _companyRepositoryMock.Verify(x =>
            x.UpdateAionSettingsAsync(
                It.IsAny<CompanyDto>(),
                It.IsAny<CancellationToken>()), Times.Once);

        _companyRepositoryMock.Verify(x =>
            x.UpdateAionSettingsAsync(
                It.IsAny<CompanyDto>(),
                It.IsAny<CancellationToken>()), Times.Once);

        _aionHttpClientMock.VerifyNoOtherCalls();
        _loggerMock.VerifyNoOtherCalls();
    }

    [Fact]
    public async Task PreProcessAchPullAsync_CantGenerateName_Exception()
    {
        var companyId = _fixture.Create<string>();
        var companyDto = _fixture.Create<CompanyDto>();
        companyDto.BlueTapeCompanyId = companyId;
        companyDto.AionSettings = null;
        companyDto.Name = null;
        companyDto.LegalName = null;
        var counterPartyResponse = _fixture.Create<CounterpartyResponse>();

        var firstName = "testuser";
        var lastName = "SuName";

        CompanyDto updateCompanyDtoCallback = null;

        _companyRepositoryMock.Setup(x =>
            x.GetByCompanyId(It.IsAny<string>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(companyDto);

        _aionHttpClientMock.Setup(x =>
                x.CreateCounterParty(It.IsAny<CreateCounterpartyRequest>(), PaymentSubscriptionType.SUBSCRIPTION1, It.IsAny<CancellationToken>()))
            .ReturnsAsync(counterPartyResponse);

        _companyRepositoryMock.Setup(x =>
            x.UpdateAionSettingsAsync(
                It.IsAny<CompanyDto>(),
                It.IsAny<CancellationToken>()))
            .Callback<CompanyDto, CancellationToken>((companyDto, ctx) => updateCompanyDtoCallback = companyDto);

        _userRoleRepositoryMock.Setup(x => x.GetByCompanyId(It.IsAny<string>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(new List<UserRoleEntity>() { new() { ExternalId = Guid.NewGuid().ToString() } });

        _userRepositoryMock.Setup(x => x.GetByExternalId(It.IsAny<IEnumerable<string>>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(new List<UserEntity>() { new() });

        var service = GetService();

        await Should.ThrowAsync<CompanyNameDoesNotExistException>(service.SyncCompanyWithAionAsync(companyId, PaymentSubscriptionType.SUBSCRIPTION1.ToString(), CancellationToken.None));
    }
}