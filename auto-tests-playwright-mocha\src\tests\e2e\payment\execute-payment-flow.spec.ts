import {BaseTest, test} from '../../test-utils';
import {insertTestReport} from "../../../database/autoTests/AutoTestReportRepository";
import {Action, ITestReport} from "../../../database/autoTests/entities/AutoTestReport";
import {GenericIntegrationService} from "../../../services/genericIntegrationService";
import {paymentService, TestParams} from "../../../services/paymentService";
import {CustomerService} from "../../../services/customerService";
import {BackOfficeClient} from "../../../api/back-office-decision-engine/backOfficeClient";
import { DrawApprovalsRepository } from '../../../database/drawApplication/drawApprovalsRepository';
import { InvoiceRepository } from '../../../database/invoices/invoiceRepository';
import { BackOfficeService } from '../../../services/backOfficeService';
import { SupplierService } from '../../../services/supplierService';

test.use({storageState: {cookies: [], origins: []}});

const testGroup = `@makeCustomerPayment Create invoice via generic API, and pay via all possible methods.`

test.describe.parallel(testGroup, async () => {
    
    let testGroupId: string
    let resultPerTest: boolean
    let actionsPerTest: Action[]
    let report: ITestReport
    let _genericIntegrationService:GenericIntegrationService
    let _paymentService: paymentService
    let _supplierService: SupplierService
    let _customerService: CustomerService
    let _invoiceRepository: InvoiceRepository
    let _backOfficeService: BackOfficeService
    
    test.beforeAll(async () => {
        _genericIntegrationService = new GenericIntegrationService()
        _supplierService = new SupplierService()
        _customerService =  new CustomerService()
        _invoiceRepository = new InvoiceRepository()
        _backOfficeService = new BackOfficeService(new DrawApprovalsRepository(), new BackOfficeClient())
        _paymentService = new paymentService(_invoiceRepository, _backOfficeService, new CustomerService())
        resetReportToDefault()
    })
    
    function resetReportToDefault() {
        resultPerTest = true;
        testGroupId = process.env.TestGroupId
        actionsPerTest = []
        
        report = {
            testGroup: testGroup,
            testGroupId: testGroupId,
        } as ITestReport
    }

    async function finalizeSingleTestReport() {
        report.createdAt = new Date()
        report.result = resultPerTest
        report.action = actionsPerTest;

        try {
            await insertTestReport(report);
        }
        catch (e) {
            console.log(e)
        }

        resetReportToDefault()
    }

    test.afterEach(async ({}, testInfo) => {
        if (testInfo.status !== 'passed') {
            actionsPerTest.push({ description: `Test failed with error: ${testInfo.error?.message}`});
            resultPerTest = false;
        }
        
        await finalizeSingleTestReport();
        report._id = null;
    });

    const defaultLocalParams: TestParams = {
        customerEmail: "<EMAIL>",
        customerPassword: "Ss@22222222",
        invoiceNumber: "eerrtt",
        environment: "beta"
    };

    test("@makeCustomerPayment Check payment type and make a payment", async ({ browser, adminIdToken }) => {
        report.testName = "@payments Create 2 invoice via UI and pay it with BTC"
        test.setTimeout(550000);

                // Parse test parameters
                const params: TestParams = JSON.parse(process.env.TEST_PARAMS || '{}');
/*
                const isLocalEnv = !process.env.GITHUB_ACTIONS;
                const providedParams = JSON.parse(process.env.TEST_PARAMS || '{}');
                const params: TestParams = isLocalEnv 
                    ? { ...defaultLocalParams, ...providedParams }
                    : providedParams;
        */
                // Validate required parameters
                const missingParams = [];

                if (!params.customerEmail) missingParams.push('customerEmail');
                if (!params.customerPassword) missingParams.push('customerPassword');
                if (!params.invoiceNumber) missingParams.push('invoiceNumber');
                if (!params.environment) missingParams.push('environment');
                
                if (missingParams.length > 0) {
                    throw new Error(`Missing required test parameters: ${missingParams.join(', ')}`);
                }

                await _paymentService.executePaymentRequestFlow(params, browser, actionsPerTest);
    });

    test("@createInvoiceGithubActions Create invoice for IHC/Draw via supplier service", async ({ browser }) => {
        report.testName = "@payments Create invoice via supplier service for IHC/Draw"
        test.setTimeout(300000);

        const params: TestParams = JSON.parse(process.env.TEST_PARAMS || '{}');
        const missingParams = [];

        if (!params.supplierEmail) missingParams.push('supplierEmail');
        if (!params.customerEmail) missingParams.push('customerEmail');
        if (!params.invoiceAmount) missingParams.push('invoiceAmount');
        if (!params.environment) missingParams.push('environment');
        
        if (missingParams.length > 0) {
            throw new Error(`Missing required test parameters: ${missingParams.join(', ')}`);
        }

        try {
            const invoiceData = await _supplierService.createInvoice({
                supplierEmail: params.supplierEmail,
                customerEmail: params.customerEmail,
                amount: params.invoiceAmount,
                environment: params.environment
            });

            const createInvoiceResponse = await _supplierService.addArAdvanceInvoice(
                config.baseUrl,
                browser,
                config.supplier.email,
                config.supplier.password,
                config.customer.name,
                actionsPerTest);
    
            // Invoice not found by ID, so try to find by number
            var invoice = await _invoiceRepository.getInvoiceByNumber(createInvoiceResponse.invoiceNumber);
            var invoiceId = invoice!._id.toString();
    
            await BaseTest.delayOperation(10000)
    
            await _backOfficeService.approveDrawApprovalViaBackOffice(invoiceId, browser, actionsPerTest, adminIdToken);
            
            actionsPerTest.push({ 
                description: `Successfully created invoice: ${invoiceData.invoiceNumber}`,
                data: invoiceData
            });
        } catch (error) {
            actionsPerTest.push({ 
                description: `Failed to create invoice: ${error.message}`,
                error: error
            });
            throw error;
        }
    });
});