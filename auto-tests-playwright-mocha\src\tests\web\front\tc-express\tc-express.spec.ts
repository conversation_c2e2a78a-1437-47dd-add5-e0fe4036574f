import {test} from "../../../test-utils";
import {expect} from "@playwright/test";

test.describe('TC express @tcExpress', async () => {

    //todo create and delete new supplier acc
    const supplierEmail = `<EMAIL>`;

    test.afterEach(async ({adminPageManager}) => {
        await adminPageManager.page.close();
    });

    test('Check Automated Trade Credit button existence.', async ({adminPageManager}) => {
        await adminPageManager.backOfficeSideMenu.clickOnSupplierButton();
        await adminPageManager.suppliers.findSupplier(supplierEmail);
        await adminPageManager.suppliers.clickOnSettingsButton();
        await expect(await adminPageManager.suppliers.toggles.automatedTradeCredit).toBeVisible();
    });

    test('Check possibility to turn on/turn off apply custom plan toggle.', async ({adminPageManager}) => {
        await adminPageManager.backOfficeSideMenu.clickOnSupplierButton();
        await adminPageManager.suppliers.findSupplier(supplierEmail);
        await adminPageManager.suppliers.clickOnSettingsButton();
        await adminPageManager.suppliers.clickOnAutomatedTradeCreditToggle();
        await expect(await adminPageManager.suppliers.elements.defaultPlans).toBeVisible();
        await expect(await adminPageManager.suppliers.elements.maxInvoiceAmount).toBeVisible();
    });

    test('Check apply custom plan toggle is off by default.', async ({adminPageManager}) => {
        await adminPageManager.backOfficeSideMenu.clickOnSupplierButton();
        await adminPageManager.suppliers.findSupplier(supplierEmail);
        await adminPageManager.suppliers.clickOnSettingsButton();
        await expect(await adminPageManager.suppliers.elements.defaultPlans.isVisible()).toBe(false);
        await expect(await adminPageManager.suppliers.elements.maxInvoiceAmount.isVisible()).toBe(false);
    });

    test.skip('Unable to save changes without adding threshold amount.', async ({adminPageManager}) => {
        await adminPageManager.backOfficeSideMenu.clickOnSupplierButton();
        await adminPageManager.suppliers.findSupplier(supplierEmail);
        await adminPageManager.suppliers.clickOnSettingsButton();
        await adminPageManager.suppliers.clickOnAutomatedTradeCreditToggle();
        await adminPageManager.suppliers.wipeInvoiceThresholdField();
        expect(await adminPageManager.suppliers.buttons.save.isClickable);
    });

    test('Check existence of plans dropdown.', async ({adminPageManager}) => {
        await adminPageManager.backOfficeSideMenu.clickOnSupplierButton();
        await adminPageManager.suppliers.findSupplier(supplierEmail);
        await adminPageManager.suppliers.clickOnSettingsButton();
        await adminPageManager.suppliers.clickOnAutomatedTradeCreditToggle();
        await adminPageManager.suppliers.clickOnPlanDropdown();
        await expect(await adminPageManager.suppliers.dropdowns.plans).toBeVisible();
    });

    test('Check default plan dropdown options.', async ({adminPageManager}) => {
        await adminPageManager.backOfficeSideMenu.clickOnSupplierButton();
        await adminPageManager.suppliers.findSupplier(supplierEmail);
        await adminPageManager.suppliers.clickOnSettingsButton();
        await adminPageManager.suppliers.clickOnAutomatedTradeCreditToggle();
        await adminPageManager.suppliers.clickOnPlanDropdown();
        await expect(await adminPageManager.suppliers.buttons.thirtyDaysPayment).toBeVisible();
        await expect(await adminPageManager.suppliers.buttons.sixtyDaysPayment).toBeVisible();
        await expect(await adminPageManager.suppliers.buttons.ninetyDaysPayment).toBeVisible();
    });

    test('Check thirty days payment plan dropdown option.', async ({adminPageManager}) => {
        await adminPageManager.backOfficeSideMenu.clickOnSupplierButton();
        await adminPageManager.suppliers.findSupplier(supplierEmail);
        await adminPageManager.suppliers.clickOnSettingsButton();
        await adminPageManager.suppliers.clickOnAutomatedTradeCreditToggle();
        await adminPageManager.suppliers.clickOnPlanDropdown();
        await adminPageManager.suppliers.buttons.thirtyDaysPayment.click();
        expect(await adminPageManager.suppliers.dropdowns.chosenPlan.textContent()).toBe('1 payment - 30 day - 0%');
    });

    test('Check ninety days payment days payment plan dropdown option.', async ({adminPageManager}) => {
        await adminPageManager.backOfficeSideMenu.clickOnSupplierButton();
        await adminPageManager.suppliers.findSupplier(supplierEmail);
        await adminPageManager.suppliers.clickOnSettingsButton();
        await adminPageManager.suppliers.clickOnAutomatedTradeCreditToggle();
        await adminPageManager.suppliers.clickOnPlanDropdown();
        await adminPageManager.suppliers.buttons.ninetyDaysPayment.click();
        expect(await adminPageManager.suppliers.dropdowns.chosenPlan.textContent()).toBe('9 payments - 90 day - 4%');
    });

    test('Check sixty days payment days payment plan dropdown option.', async ({adminPageManager}) => {
        await adminPageManager.backOfficeSideMenu.clickOnSupplierButton();
        await adminPageManager.suppliers.findSupplier(supplierEmail);
        await adminPageManager.suppliers.clickOnSettingsButton();
        await adminPageManager.suppliers.clickOnAutomatedTradeCreditToggle();
        await adminPageManager.suppliers.clickOnPlanDropdown();
        await adminPageManager.suppliers.buttons.sixtyDaysPayment.click();
        expect(await adminPageManager.suppliers.dropdowns.chosenPlan.textContent()).toBe('5 payments - 60 day - 2%');
    });

    test('Check default payment plan.', async ({adminPageManager}) => {
        await adminPageManager.backOfficeSideMenu.clickOnSupplierButton();
        await adminPageManager.suppliers.findSupplier(supplierEmail);
        await adminPageManager.suppliers.clickOnSettingsButton();
        await adminPageManager.suppliers.clickOnAutomatedTradeCreditToggle();
        expect(await adminPageManager.suppliers.dropdowns.chosenPlan.textContent()).toBe('1 payment - 30 day - 0%');
    });

    //todo ask frontend dev about more stable locator
    test.skip('Check default max amount.', async ({adminPageManager}) => {
        await adminPageManager.backOfficeSideMenu.clickOnSupplierButton();
        await adminPageManager.suppliers.findSupplier(supplierEmail);
        await adminPageManager.suppliers.clickOnSettingsButton();
        await adminPageManager.suppliers.clickOnAutomatedTradeCreditToggle();
        await adminPageManager.page.waitForLoadState('networkidle');
        expect(await adminPageManager.suppliers.inputFields.maxInvoiceAmount.value).toBe("$10,000");
    });

    test('Check Regular Trade Credit Terms.', async ({adminPageManager}) => {
        await adminPageManager.backOfficeSideMenu.clickOnSupplierButton();
        await adminPageManager.suppliers.findSupplier(supplierEmail);
        await adminPageManager.suppliers.clickOnSettingsButton();
        await expect(await adminPageManager.suppliers.elements.tradeCreditTerms).toBeVisible();
    });
});
