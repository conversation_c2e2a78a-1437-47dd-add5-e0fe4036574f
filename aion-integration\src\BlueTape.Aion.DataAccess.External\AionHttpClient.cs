﻿using BlueTape.Aion.DataAccess.External.Abstractions;
using BlueTape.Aion.DataAccess.External.Constants;
using BlueTape.Aion.DataAccess.External.Models;
using BlueTape.Aion.DataAccess.External.Models.Accounts;
using BlueTape.Aion.DataAccess.External.Models.AchTransfer;
using BlueTape.Aion.DataAccess.External.Models.AchTransfer.Response;
using BlueTape.Aion.DataAccess.External.Models.CreateAchTransfer;
using BlueTape.Aion.DataAccess.External.Models.CreateAchTransfer.Response;
using BlueTape.Aion.DataAccess.External.Models.CreateCounterParty;
using BlueTape.Aion.DataAccess.External.Models.InternalTransfer;
using BlueTape.Aion.DataAccess.External.Models.Login;
using BlueTape.Aion.DataAccess.External.Models.Transactions;
using BlueTape.Aion.DataAccess.External.Models.TransferMethod;
using BlueTape.Aion.DataAccess.External.Models.WireTransfer.Response;
using BlueTape.Aion.DataAccess.MongoDB.Abstractions;
using BlueTape.Integrations.Aion.Infrastructure.Enums;
using BueTape.Aion.Infrastructure;
using BueTape.Aion.Infrastructure.Exceptions;
using Microsoft.Extensions.Logging;
using Polly;
using Polly.Retry;
using System.Diagnostics.CodeAnalysis;
using System.Net;
using System.Net.Http.Json;
using System.Text;
using System.Text.Json;
using BlueTape.Aion.DataAccess.External.Models.DailyLimmits;
using BlueTape.Aion.DataAccess.External.Models.InstantTransfer.Request;
using BlueTape.Aion.DataAccess.External.Models.InstantTransfer.Response;
using BlueTape.Aion.DataAccess.External.Models.WireTransfer.Requests;

namespace BlueTape.Aion.DataAccess.External;

[ExcludeFromCodeCoverage]
public class AionHttpClient : IAionHttpClient
{
    private readonly IHttpClientFactory _httpClientFactory;
    private readonly IAionLoggingService _aionLoggingService;
    private readonly ILogger<AionHttpClient> _logger;
    private readonly IAionMemoryCache _aionMemoryCache;
    private readonly IAionCredentialsManager _aionCredentialsManager;

    public AionHttpClient(IHttpClientFactory httpClientFactory,
        IAionLoggingService aionLoggingService,
        ILogger<AionHttpClient> logger,
        IAionMemoryCache aionMemoryCache,
        IAionCredentialsManager credentialsManager)
    {
        _httpClientFactory = httpClientFactory;
        _aionLoggingService = aionLoggingService;
        _logger = logger;
        _aionMemoryCache = aionMemoryCache;
        _aionCredentialsManager = credentialsManager;
    }

    public async Task<string> Login(PaymentSubscriptionType paymentSubscription, CancellationToken ctx)
    {
        _logger.LogInformation("Start Aion login request");

        var loginData = _aionCredentialsManager.GetAionCredentials(paymentSubscription);
        using var httpClient = _httpClientFactory.CreateClient(ClientConstants.AionAuthHttpClientName);

        var request = new HttpRequestMessage(HttpMethod.Post, PathConstants.LoginPath)
        {
            Content = JsonContent.Create(loginData)
        };

        var response = await httpClient.SendAsync(request, ctx);
        var responseJson = await response.Content.ReadAsStringAsync(ctx);

        byte[] byteArray = Encoding.UTF8.GetBytes(responseJson);
        var responseSizeInMegabytes = ((double)byteArray.Length) / SizeConstants.NumberOfBytesInOneMegabyte;

        if (responseSizeInMegabytes < SizeConstants.MaxResponseSizeThreshold)
        {
            var requestJson = JsonSerializer.Serialize(loginData);

            await _aionLoggingService.Log(requestJson, responseJson, PathConstants.LoginPath, response.IsSuccessStatusCode, ctx);
        }

        if (!response.IsSuccessStatusCode)
        {
            var message = $"{response.StatusCode} Error happened during login operation: response: {responseJson}";
            _logger.LogError(message);
            var errorResult = JsonSerializer.Deserialize<object>(responseJson)!;
            throw new AionLoginRequestException(message, PathConstants.LoginPath, response.StatusCode, errorResult);
        }

        var result = JsonSerializer.Deserialize<LoginResponseModel>(responseJson)!;
        if (!result.Result)
        {
            var badRequestMessage = $"{response.StatusCode} Error happened during login operation: response: {responseJson}";
            _logger.LogError(badRequestMessage);
            throw new AionLoginRequestException(result.ResponseMessage ?? badRequestMessage, PathConstants.LoginPath, response.StatusCode, result);
        }

        _logger.LogInformation("Finish Aion login request");

        return result.AuthToken;
    }

    public Task<GetACHTransferResponse> GetACHTransfers(GetACHTransfersRequest request, PaymentSubscriptionType paymentSubscription, CancellationToken ctx)
    {
        var retryPolicy = Policy
            .HandleResult<HttpResponseMessage>(msg => msg.StatusCode == HttpStatusCode.TooManyRequests) // Handle only HTTP 429 error
            .WaitAndRetryAsync(new[]
                {
                    TimeSpan.FromMilliseconds(5000),
                    TimeSpan.FromMilliseconds(10000),
                    TimeSpan.FromMilliseconds(15000),
                    TimeSpan.FromMilliseconds(31000)
                },
                (response, timespan, retryCount, context) =>
                {
                    _logger.LogWarning("Polly retried after {ElapsedTime} milliseconds for the {RetryCount}-th time.", timespan.TotalMilliseconds, retryCount);
                });

        return PostAsync<GetACHTransferResponse>(PathConstants.GetAchTransfersPath, request, ctx, paymentSubscription, retryPolicy: retryPolicy);
    }

    public Task<GetInternalTransferResponse> GetInternalTransfers(GetACHTransfersRequest request, PaymentSubscriptionType paymentSubscription, CancellationToken ctx)
    {
        var retryPolicy = Policy
            .HandleResult<HttpResponseMessage>(msg => msg.StatusCode == HttpStatusCode.TooManyRequests) // Handle only HTTP 429 error
            .WaitAndRetryAsync(new[]
                {
                    TimeSpan.FromMilliseconds(5000),
                    TimeSpan.FromMilliseconds(10000),
                    TimeSpan.FromMilliseconds(15000),
                    TimeSpan.FromMilliseconds(31000)
                },
                (response, timespan, retryCount, context) =>
                {
                    _logger.LogWarning("Polly retried after {ElapsedTime} milliseconds for the {RetryCount}-th time.", timespan.TotalMilliseconds, retryCount);
                });

        return PostAsync<GetInternalTransferResponse>(PathConstants.GetInternalTransfersPath, request, ctx, paymentSubscription, retryPolicy: retryPolicy);
    }

    public Task<GetACHTransferResponse> GetACHTransferReturns(GetACHTransfersReturnsRequest request, PaymentSubscriptionType paymentSubscription, CancellationToken ctx)
    {
        var retryPolicy = Policy
            .HandleResult<HttpResponseMessage>(msg => msg.StatusCode == HttpStatusCode.TooManyRequests) // Handle only HTTP 429 error
            .WaitAndRetryAsync(new[]
                {
                    TimeSpan.FromMilliseconds(5000),
                    TimeSpan.FromMilliseconds(10000),
                    TimeSpan.FromMilliseconds(15000),
                    TimeSpan.FromMilliseconds(31000)
                },
                (response, timespan, retryCount, context) =>
                {
                    _logger.LogWarning("Polly retried after {ElapsedTime} milliseconds for the {RetryCount}-th time.", timespan.TotalMilliseconds, retryCount);
                });

        return PostAsync<GetACHTransferResponse>(PathConstants.GetAchTransferReturnsPath, request, ctx, paymentSubscription, retryPolicy: retryPolicy);
    }

    public Task<CreateAchResponse> CreateAchTransfer(CreateAchTransferRequest request, PaymentSubscriptionType paymentSubscription, CancellationToken ctx)
        => PostAsync<CreateAchResponse>(PathConstants.CreateAchTransferPath, request, ctx, paymentSubscription);

    public Task<CreateWireTransferResponse> CreateWireTransfer(CreateWireTransferRequest request, PaymentSubscriptionType paymentSubscription,
        CancellationToken ctx)
        => PostAsync<CreateWireTransferResponse>(PathConstants.CreateWireTransferPath, request, ctx, paymentSubscription);

    public Task<CreateInstantTransferResponse> CreateInstantTransfer(CreateInstantTransferRequest request, PaymentSubscriptionType paymentSubscription,
        CancellationToken ctx)
        => PostAsync<CreateInstantTransferResponse>(PathConstants.CreateInstantTransferPath, request, ctx, paymentSubscription);

    public Task<CounterpartyResponse> CreateCounterParty(CreateCounterpartyRequest request, PaymentSubscriptionType paymentSubscription, CancellationToken ctx)
        => PostAsync<CounterpartyResponse>(PathConstants.CreateCounterPartyPath, request, ctx, paymentSubscription);

    public Task<AddTransferMethodResponse> AddTransferMethod(AddTransferMethodRequest request, PaymentSubscriptionType paymentSubscription, CancellationToken ctx)
        => PostAsync<AddTransferMethodResponse>(PathConstants.AddTransferMethodPath, request, ctx, paymentSubscription);

    public Task<GetAccountsResponse> GetAccounts(PaymentSubscriptionType paymentSubscription, CancellationToken ctx)
        => PostAsync<GetAccountsResponse>(PathConstants.GetAccountsPath, new GetAccountsRequest(), ctx, paymentSubscription);

    public Task<GetTransferMethodResponse> GetTransferMethods(GetTransferMethodRequest request, PaymentSubscriptionType paymentSubscription, CancellationToken ctx)
        => PostAsync<GetTransferMethodResponse>(PathConstants.GetTransferMethodsPath, request, ctx, paymentSubscription);

    public Task<InternalTransferResponse> CreateInternalTransfer(InternalTransferRequest request, PaymentSubscriptionType paymentSubscription, CancellationToken ctx)
        => PostAsync<InternalTransferResponse>(PathConstants.CreateInternalTransferPath, request, ctx, paymentSubscription);
    
    public Task<GetTransactionsResponseModel> GetTransactions(GetTransactionsRequest request, PaymentSubscriptionType paymentSubscription, CancellationToken ctx)
        => PostAsync<GetTransactionsResponseModel>(PathConstants.GetTransactionsPath, request, ctx, paymentSubscription);

    public async Task<GetWireTransferResponse> GetWireTransfers(BaseGetACHTransfers request,
        PaymentSubscriptionType paymentSubscription, CancellationToken ctx)
    {
        var retryPolicy = Policy
            .HandleResult<HttpResponseMessage>(msg => msg.StatusCode == HttpStatusCode.TooManyRequests) // Handle only HTTP 429 error
            .WaitAndRetryAsync(new[]
                {
                    TimeSpan.FromMilliseconds(5000),
                    TimeSpan.FromMilliseconds(10000),
                    TimeSpan.FromMilliseconds(15000),
                    TimeSpan.FromMilliseconds(31000)
                },
                (response, timespan, retryCount, context) =>
                {
                    _logger.LogWarning("Polly retried after {ElapsedTime} milliseconds for the {RetryCount}-th time.", timespan.TotalMilliseconds, retryCount);
                });
        
        return await PostAsync<GetWireTransferResponse>(PathConstants.GetWireTransfersPath, request, ctx, paymentSubscription, retryPolicy: retryPolicy);
        
    }

    public async Task<GetInstantTransferResponse> GetInstantTransfers(BaseGetACHTransfers request,
        PaymentSubscriptionType paymentSubscription,
        CancellationToken ctx)
    {
        var retryPolicy = Policy
            .HandleResult<HttpResponseMessage>(msg => msg.StatusCode == HttpStatusCode.TooManyRequests) // Handle only HTTP 429 error
            .WaitAndRetryAsync(new[]
                {
                    TimeSpan.FromMilliseconds(5000),
                    TimeSpan.FromMilliseconds(10000),
                    TimeSpan.FromMilliseconds(15000),
                    TimeSpan.FromMilliseconds(31000)
                },
                (response, timespan, retryCount, context) =>
                {
                    _logger.LogWarning("Polly retried after {ElapsedTime} milliseconds for the {RetryCount}-th time.", timespan.TotalMilliseconds, retryCount);
                });
        
        return await PostAsync<GetInstantTransferResponse>(PathConstants.GetInstantTransfersPath, request, ctx, paymentSubscription, retryPolicy: retryPolicy);
    }
    
    public Task<AionAccountLimitResponse> GetWireLimit(GetWireLimitRequest request, PaymentSubscriptionType paymentSubscription, CancellationToken ctx)
        => PostAsync<AionAccountLimitResponse>(PathConstants.GetDailyLimitsWire, request, ctx, paymentSubscription);
    
    public Task<AionAccountLimitResponse> GetInstantLimit(GetInstantLimitRequest request, PaymentSubscriptionType paymentSubscription, CancellationToken ctx)
        => PostAsync<AionAccountLimitResponse>(PathConstants.GetDailyLimitsInstantTransfer, request, ctx, paymentSubscription);
    
    public Task<AionAccountLimitResponse> GetAchPushLimit(GetACHLimitRequest request, PaymentSubscriptionType paymentSubscription, CancellationToken ctx)
        => PostAsync<AionAccountLimitResponse>(PathConstants.GetAchPushLimit, request, ctx, paymentSubscription);
    
    public Task<AionAccountLimitResponse> GetAchPullLimit(GetACHLimitRequest request, PaymentSubscriptionType paymentSubscription, CancellationToken ctx)
        => PostAsync<AionAccountLimitResponse>(PathConstants.GetAchPullLimit, request, ctx, paymentSubscription);

    private async Task<T> PostAsync<T>(
        string url,
        object basicGetDataModel,
        CancellationToken ctx,
        PaymentSubscriptionType paymentSubscription,
        int maxLoginErrorRetries = 3,
        AsyncRetryPolicy<HttpResponseMessage>? retryPolicy = null)
        where T : BaseAionResponseModel
    {
        _logger.LogInformation("Start Aion request for {url}", url);

        using var httpClient = _httpClientFactory.CreateClient(ClientConstants.AionHttpClientName);

        var request = new HttpRequestMessage(HttpMethod.Post, url)
        {
            Content = JsonContent.Create(basicGetDataModel)
        };

        if (string.IsNullOrEmpty(_aionMemoryCache.GetAuthApiKey(paymentSubscription)))
        {
            var apiKey = await Login(paymentSubscription, ctx);
            _aionMemoryCache.SetAuthApiKey(apiKey, paymentSubscription);
        }

        request.Content!.Headers.Add(ClientConstants.AionAuthHeaderName, _aionMemoryCache.GetAuthApiKey(paymentSubscription));

        var requestJson = JsonSerializer.Serialize(basicGetDataModel);
        HttpResponseMessage? response = null;
        string responseJson;
        var retryCount = -1;
        ExternalErrorModel? errorData = null;
        T result;

        do
        {
            retryCount++;

            try
            {
                if (retryPolicy != null)
                {
                    response = await retryPolicy.ExecuteAsync(async () =>
                    {
                        var clonedRequest = await CloneRequest(request); // Create a clone of the request
                        return await httpClient.SendAsync(clonedRequest, ctx);
                    });
                }
                else
                {
                    response = await httpClient.SendAsync(request, ctx);

                    if (response.StatusCode == HttpStatusCode.TooManyRequests)
                    {
                        throw new AionTooManyRequestException(url);
                    }

                    if (response.StatusCode == HttpStatusCode.RequestTimeout)
                    {
                        throw new AionTimeOutRequestException(url, response.StatusCode);
                    }
                }

                responseJson = await response.Content.ReadAsStringAsync(ctx);

                byte[] byteArray = Encoding.UTF8.GetBytes(responseJson);
                var responseSizeInMegabytes = ((double)byteArray.Length) / SizeConstants.NumberOfBytesInOneMegabyte;

                try
                {
                    result = JsonSerializer.Deserialize<T>(responseJson)!;
                }
                catch (JsonException ex)
                {
                    _logger.LogError(ex, "Error occurred during JSON deserialization");

                    if (!string.IsNullOrEmpty(responseJson))
                    {
                        _logger.LogInformation("Response JSON: {responseJson}", responseJson);
                        ex.Data.Add("json", responseJson);
                    }

                    if (responseSizeInMegabytes < SizeConstants.MaxResponseSizeThreshold)
                    {
                        await _aionLoggingService.Log(requestJson, responseJson, url, false, ctx);
                    }

                    throw;
                }

                if (responseSizeInMegabytes < SizeConstants.MaxResponseSizeThreshold)
                {
                    await _aionLoggingService.Log(requestJson, responseJson, url, result.Result, ctx);
                }

                if (response.IsSuccessStatusCode) break;

                // If status code notfound and errorCode 1101, It means that our auth token expired and need to be refreshed
                if (response.StatusCode == HttpStatusCode.NotFound)
                {
                    errorData = JsonSerializer.Deserialize<ExternalErrorModel>(responseJson)!;
                    errorData.StatusCode = response.StatusCode.ToString();

                    if (string.IsNullOrEmpty(errorData.ErrorCode) &&
                        !string.IsNullOrEmpty(errorData.ErrorCode) &&
                        errorData.ErrorCode.Equals(AionErrorCodes.AccessTokenError) &&
                        retryCount < maxLoginErrorRetries)
                    {
                        _aionMemoryCache.SetAuthApiKey(string.Empty, paymentSubscription);
                        _logger.LogInformation("Unable to process request due to invalid AionAuth token. RetryCount: {retryCount}", retryCount);
                        continue;
                    }
                }

                if (response.StatusCode == HttpStatusCode.TooManyRequests)
                {
                    throw new AionTooManyRequestException(url);
                }

                if (response.StatusCode == HttpStatusCode.RequestTimeout)
                {
                    throw new AionTimeOutRequestException(url, response.StatusCode);
                }

                break;
            }
            catch (OperationCanceledException) when (ctx.IsCancellationRequested)
            {
                throw new AionTimeOutRequestException(url, HttpStatusCode.RequestTimeout);
            }
        } while (retryCount < maxLoginErrorRetries);

        _logger.LogInformation("End Aion request for {url} with status: {StatusCode}", url, response.StatusCode.ToString());

        if (!response.IsSuccessStatusCode)
        {
            var unhandledError = $"Aion request is failed. Path: ${url}  statusCode: ${response.StatusCode.ToString()}";
            _logger.LogError(unhandledError);
            throw new AionUnexpectedRequestException(unhandledError, url, response.StatusCode, errorData);
        }

        if (!result.Result)
        {
            var badRequestError = "Request is failed due to Aion error.";
            _logger.LogError(badRequestError);
            
            if(!string.IsNullOrEmpty(result.ResponseMessage))
            {
                if (result.ResponseMessage.Contains("Exceeded daily limit", StringComparison.OrdinalIgnoreCase))
                {
                    throw new AionDailyLimitRequestException(result.ResponseMessage, url, response.StatusCode, result);
                }
                
                if (result.ResponseMessage.Contains("daily", StringComparison.OrdinalIgnoreCase) && result.ResponseMessage.Contains("limit", StringComparison.OrdinalIgnoreCase))
                {
                    throw new AionDailyLimitRequestException(result.ResponseMessage, url, response.StatusCode, result);
                }
            }
            
            throw new AionRequestException(result.ResponseMessage ?? badRequestError, url, response.StatusCode, result);
        }

        return result;
    }

    private static async Task<HttpRequestMessage> CloneRequest(HttpRequestMessage request)
    {
        var clone = new HttpRequestMessage(request.Method, request.RequestUri);

        // Copy request content
        if (request.Content != null)
        {
            var ms = new MemoryStream();
            await request.Content.CopyToAsync(ms);
            ms.Position = 0;
            clone.Content = new StreamContent(ms);

            // Copy headers
            if (request.Content?.Headers != null)
            {
                foreach (var header in request.Content.Headers)
                {
                    clone.Content.Headers.TryAddWithoutValidation(header.Key, header.Value);
                }
            }
        }

        // Copy headers
        foreach (var header in request.Headers)
        {
            clone.Headers.TryAddWithoutValidation(header.Key, header.Value);
        }

        return clone;
    }

}