@startuml AccountStatus

title Account Status Update

participant "First party\nservices" as first #SkyBlue
queue "AccountStatus\nUpdates" as asqueue #LightSalmon
participant "Company\n(Account)\nService" as cs #SkyBlue
database "Company Db" as cdb
participant "First party\nservices" as first2 #SkyBlue

autonumber

== Update Account Statuses ==

first --> first : Something changed in service data
first -> asqueue : Places status change event
asqueue -> cs : Reads events
cs -> first2 : Reads necessary service
cs -> first2 : Reads necessary service
cs -> first2 : Reads necessary service
cs --> cs : Calculates Account Status
cs -> cdb : Writes actual status

@enduml