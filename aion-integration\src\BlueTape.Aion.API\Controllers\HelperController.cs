﻿using BlueTape.Aion.API.Models.Errors;
using BlueTape.Aion.Application.Abstractions;
using BlueTape.Integrations.Aion.AzureTableStorage.Abstractions;
using BueTape.Aion.Infrastructure;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using System.Diagnostics.CodeAnalysis;
using System.Text.Json;

namespace BlueTape.Aion.API.Controllers;

[ApiController]
[Route("/helper")]
[ExcludeFromCodeCoverage]
[Authorize]
[ProducesResponseType(typeof(List<ErrorModel>), StatusCodes.Status400BadRequest)]
[ProducesResponseType(typeof(void), StatusCodes.Status401Unauthorized)]
[ProducesResponseType(typeof(List<ErrorModel>), StatusCodes.Status500InternalServerError)]
public class HelperController : ControllerBase
{
    private readonly IAzureStorageTransactionRepository _azureStorageTransactionRepository;
    private readonly IHelperService _helperService;
    private readonly ILogger<HelperController> _logger;

    public HelperController(
        IAzureStorageTransactionRepository azureStorageTransactionRepository,
        IHelperService helperService,
        ILogger<HelperController> logger)
    {
        _azureStorageTransactionRepository = azureStorageTransactionRepository;
        _helperService = helperService;
        _logger = logger;
    }

    [HttpGet("/errorCodes")]
    public IActionResult GetErrorCodes()
    {
        return Ok(JsonSerializer.Serialize(ErrorCodes.Errors));
    }

    [HttpGet("/git")]
    public IActionResult Git()
    {
        return Ok(Environment.GetEnvironmentVariable("Branch"));
    }

    [HttpGet("azure-storage/transactions/get-all")]
    public async Task<IActionResult> GetAllAionTransactions(CancellationToken ctx)
    {
        var result = await _azureStorageTransactionRepository.GetAllEntitiesAsync(ctx);
        return Ok(result);
    }

    [HttpGet("/health")]
    public IActionResult HealthCheck()
    {
        _helperService.CheckConfigurationSecrets();
        _logger.LogInformation("AionService healthcheck endpoint has been called");
        _logger.LogWarning("AionService healthcheck endpoint has been called");
        _logger.LogError("AionService healthcheck endpoint has been called");
        return Ok();
    }
}