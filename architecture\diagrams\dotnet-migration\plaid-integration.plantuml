@startuml

title Plaid integration\n(process diagram)

participant "<PERSON><PERSON><PERSON>" as lq #LightGreen
database "MongoDb" as mongo #LightGray
participant "Plaid Proxy" as pp #SkyBlue
participant "Plaid" as pl #LightGrey

autonumber

lq -> pp : Create link token
pp -> pl
pl --> pp
pp --> lq
lq -> pl : Plaid Link is used with link token
pl --> lq : Plaid Callback to OAuth Landing Page\n//with oauth_state_id param//
lq -> pl : usePlaidLink with link token + receivedRedirectUri (React)
pl --> lq : //onSuccess// will give back public_token
lq -> pp : Exchange public token to access token
pp -> pl
pl --> pp
pp --> mongo : Store access_token
pp --> lq : Success (do not send access_token to frontend directly)

@enduml