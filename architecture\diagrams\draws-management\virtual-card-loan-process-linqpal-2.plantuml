@startuml Virtual Card Loan Process (with BlueTape + CBW) /2

title Virtual Card Loan Process (with BlueTape + CBW) /2

participant "BlueTape" as bt #SkyBlue
participant "CBW" as cbw #LightGray
database "Mongo" as mongo
participant "OnBoarding Service" as obs #SkyBlue
queue "Loan Events" as lmsq #PaleVioletRed
participant "LMS" as lms #SkyBlue

autonumber

== Check virtual card usage ==

cbw -> bt : Transactions\nstatus updates (S3)
bt --> bt : Check CBW transactions\nFind VC use

alt #LightGreen CARD IS USED - Issue loan by used amount
    bt -> obs : Update draw
    obs -> lmsq : Place event\n""Loan.Issue""
    lmsq -> lms : Read events
    bt -> cbw : Deactivate card
    bt -> mongo : Update card
else #LightCoral CARD IS EXPIRED (5 Business days)
    bt -> cbw : Deactivate expired cards
    bt -> mongo : Update cards
end

@enduml