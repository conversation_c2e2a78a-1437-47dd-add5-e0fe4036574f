﻿using BlueTape.Aion.Application.Abstractions;
using BlueTape.Aion.Application.Constants;
using BueTape.Aion.Infrastructure.Exceptions;
using Microsoft.Extensions.Configuration;

namespace BlueTape.Aion.Application.Service;

public class HelperService(IConfiguration configuration) : IHelperService
{
    private readonly Dictionary<string, bool> _existingSecrets = [];

    public void CheckConfigurationSecrets()
    {
        foreach (var secretName in KeyVaultKeysConstants.SecretNames)
        {
            var secret = configuration[secretName];
            _existingSecrets.Add(secretName, secret is not null);
        }

        var missedSecrets = _existingSecrets.Where(x => x.Value is false);

        if (missedSecrets.Count() != 0)
        {
            throw new MissedSecretsException(missedSecrets.Select(x => x.Key));
        }
    }
}