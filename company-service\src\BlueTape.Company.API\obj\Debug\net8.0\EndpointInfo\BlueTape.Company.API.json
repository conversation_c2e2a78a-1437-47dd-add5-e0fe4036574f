{"openapi": "3.0.1", "info": {"title": "BlueTape.Company.API", "version": "1.0"}, "paths": {"/account/companies/{id}": {"patch": {"tags": ["Account"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"maxLength": 24, "minLength": 24, "type": "string"}}, {"name": "userId", "in": "header", "required": true, "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/BlueTape.Company.Application.Models.AccountStatus.ChangeAccountStatusManuallyModel"}}, "text/json": {"schema": {"$ref": "#/components/schemas/BlueTape.Company.Application.Models.AccountStatus.ChangeAccountStatusManuallyModel"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/BlueTape.Company.Application.Models.AccountStatus.ChangeAccountStatusManuallyModel"}}}}, "responses": {"400": {"description": "Bad Request", "content": {"text/plain": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BlueTape.Company.API.Models.Errors.ErrorResponse"}}}, "application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BlueTape.Company.API.Models.Errors.ErrorResponse"}}}, "text/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BlueTape.Company.API.Models.Errors.ErrorResponse"}}}}}, "401": {"description": "Unauthorized", "content": {"text/plain": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BlueTape.Company.API.Models.Errors.ErrorResponse"}}}, "application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BlueTape.Company.API.Models.Errors.ErrorResponse"}}}, "text/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BlueTape.Company.API.Models.Errors.ErrorResponse"}}}}}, "404": {"description": "Not Found"}, "500": {"description": "Server Error", "content": {"text/plain": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BlueTape.Company.API.Models.Errors.ErrorResponse"}}}, "application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BlueTape.Company.API.Models.Errors.ErrorResponse"}}}, "text/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BlueTape.Company.API.Models.Errors.ErrorResponse"}}}}}, "200": {"description": "Success"}}}}, "/account/companies": {"post": {"tags": ["Account"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/BlueTape.CompanyService.Common.Functions.AccountStatus.ChangeAccountStatusModel"}}, "text/json": {"schema": {"$ref": "#/components/schemas/BlueTape.CompanyService.Common.Functions.AccountStatus.ChangeAccountStatusModel"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/BlueTape.CompanyService.Common.Functions.AccountStatus.ChangeAccountStatusModel"}}}}, "responses": {"400": {"description": "Bad Request", "content": {"text/plain": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BlueTape.Company.API.Models.Errors.ErrorResponse"}}}, "application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BlueTape.Company.API.Models.Errors.ErrorResponse"}}}, "text/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BlueTape.Company.API.Models.Errors.ErrorResponse"}}}}}, "401": {"description": "Unauthorized", "content": {"text/plain": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BlueTape.Company.API.Models.Errors.ErrorResponse"}}}, "application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BlueTape.Company.API.Models.Errors.ErrorResponse"}}}, "text/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BlueTape.Company.API.Models.Errors.ErrorResponse"}}}}}, "404": {"description": "Not Found"}, "500": {"description": "Server Error", "content": {"text/plain": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BlueTape.Company.API.Models.Errors.ErrorResponse"}}}, "application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BlueTape.Company.API.Models.Errors.ErrorResponse"}}}, "text/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BlueTape.Company.API.Models.Errors.ErrorResponse"}}}}}, "200": {"description": "Success"}}}}, "/account/companies/{legacyCompanyId}/account-status": {"get": {"tags": ["Account"], "parameters": [{"name": "legacyCompanyId", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"400": {"description": "Bad Request", "content": {"text/plain": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BlueTape.Company.API.Models.Errors.ErrorResponse"}}}, "application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BlueTape.Company.API.Models.Errors.ErrorResponse"}}}, "text/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BlueTape.Company.API.Models.Errors.ErrorResponse"}}}}}, "401": {"description": "Unauthorized", "content": {"text/plain": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BlueTape.Company.API.Models.Errors.ErrorResponse"}}}, "application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BlueTape.Company.API.Models.Errors.ErrorResponse"}}}, "text/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BlueTape.Company.API.Models.Errors.ErrorResponse"}}}}}, "404": {"description": "Not Found"}, "500": {"description": "Server Error", "content": {"text/plain": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BlueTape.Company.API.Models.Errors.ErrorResponse"}}}, "application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BlueTape.Company.API.Models.Errors.ErrorResponse"}}}, "text/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BlueTape.Company.API.Models.Errors.ErrorResponse"}}}}}, "200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/BlueTape.CompanyService.Common.Enums.AccountStatusEnum"}}, "application/json": {"schema": {"$ref": "#/components/schemas/BlueTape.CompanyService.Common.Enums.AccountStatusEnum"}}, "text/json": {"schema": {"$ref": "#/components/schemas/BlueTape.CompanyService.Common.Enums.AccountStatusEnum"}}}}}}}, "/admin/companies/{id}/status": {"patch": {"tags": ["Admin"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"maxLength": 24, "minLength": 24, "type": "string"}}, {"name": "userId", "in": "header", "required": true, "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/BlueTape.Company.API.Models.UpdateManualAccountStatusModel"}}, "text/json": {"schema": {"$ref": "#/components/schemas/BlueTape.Company.API.Models.UpdateManualAccountStatusModel"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/BlueTape.Company.API.Models.UpdateManualAccountStatusModel"}}}}, "responses": {"400": {"description": "Bad Request", "content": {"text/plain": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BlueTape.Company.API.Models.Errors.ErrorResponse"}}}, "application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BlueTape.Company.API.Models.Errors.ErrorResponse"}}}, "text/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BlueTape.Company.API.Models.Errors.ErrorResponse"}}}}}, "401": {"description": "Unauthorized", "content": {"text/plain": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BlueTape.Company.API.Models.Errors.ErrorResponse"}}}, "application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BlueTape.Company.API.Models.Errors.ErrorResponse"}}}, "text/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BlueTape.Company.API.Models.Errors.ErrorResponse"}}}}}, "404": {"description": "Not Found"}, "500": {"description": "Server Error", "content": {"text/plain": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BlueTape.Company.API.Models.Errors.ErrorResponse"}}}, "application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BlueTape.Company.API.Models.Errors.ErrorResponse"}}}, "text/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BlueTape.Company.API.Models.Errors.ErrorResponse"}}}}}, "200": {"description": "Success"}}}}, "/admin/companies/{id}/status/delete": {"patch": {"tags": ["Admin"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"maxLength": 24, "minLength": 24, "type": "string"}}, {"name": "userId", "in": "header", "required": true, "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/BlueTape.Company.API.Models.NoteModel"}}, "text/json": {"schema": {"$ref": "#/components/schemas/BlueTape.Company.API.Models.NoteModel"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/BlueTape.Company.API.Models.NoteModel"}}}, "required": true}, "responses": {"400": {"description": "Bad Request", "content": {"text/plain": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BlueTape.Company.API.Models.Errors.ErrorResponse"}}}, "application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BlueTape.Company.API.Models.Errors.ErrorResponse"}}}, "text/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BlueTape.Company.API.Models.Errors.ErrorResponse"}}}}}, "401": {"description": "Unauthorized", "content": {"text/plain": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BlueTape.Company.API.Models.Errors.ErrorResponse"}}}, "application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BlueTape.Company.API.Models.Errors.ErrorResponse"}}}, "text/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BlueTape.Company.API.Models.Errors.ErrorResponse"}}}}}, "404": {"description": "Not Found"}, "500": {"description": "Server Error", "content": {"text/plain": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BlueTape.Company.API.Models.Errors.ErrorResponse"}}}, "application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BlueTape.Company.API.Models.Errors.ErrorResponse"}}}, "text/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BlueTape.Company.API.Models.Errors.ErrorResponse"}}}}}, "200": {"description": "Success"}}}}, "/bank-account/{id}": {"get": {"tags": ["BankAccount"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"400": {"description": "Bad Request", "content": {"text/plain": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BlueTape.Company.API.Models.Errors.ErrorResponse"}}}, "application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BlueTape.Company.API.Models.Errors.ErrorResponse"}}}, "text/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BlueTape.Company.API.Models.Errors.ErrorResponse"}}}}}, "401": {"description": "Unauthorized", "content": {"text/plain": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BlueTape.Company.API.Models.Errors.ErrorResponse"}}}, "application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BlueTape.Company.API.Models.Errors.ErrorResponse"}}}, "text/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BlueTape.Company.API.Models.Errors.ErrorResponse"}}}}}, "404": {"description": "Not Found"}, "500": {"description": "Server Error", "content": {"text/plain": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BlueTape.Company.API.Models.Errors.ErrorResponse"}}}, "application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BlueTape.Company.API.Models.Errors.ErrorResponse"}}}, "text/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BlueTape.Company.API.Models.Errors.ErrorResponse"}}}}}, "200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/BlueTape.CompanyService.BankAccounts.BankAccountModel"}}, "application/json": {"schema": {"$ref": "#/components/schemas/BlueTape.CompanyService.BankAccounts.BankAccountModel"}}, "text/json": {"schema": {"$ref": "#/components/schemas/BlueTape.CompanyService.BankAccounts.BankAccountModel"}}}}}}, "patch": {"tags": ["BankAccount"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string"}}, {"name": "userId", "in": "header", "required": true, "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/BlueTape.CompanyService.BankAccounts.UpdateBankAccountNumberModel"}}, "text/json": {"schema": {"$ref": "#/components/schemas/BlueTape.CompanyService.BankAccounts.UpdateBankAccountNumberModel"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/BlueTape.CompanyService.BankAccounts.UpdateBankAccountNumberModel"}}}}, "responses": {"400": {"description": "Bad Request", "content": {"text/plain": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BlueTape.Company.API.Models.Errors.ErrorResponse"}}}, "application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BlueTape.Company.API.Models.Errors.ErrorResponse"}}}, "text/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BlueTape.Company.API.Models.Errors.ErrorResponse"}}}}}, "401": {"description": "Unauthorized", "content": {"text/plain": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BlueTape.Company.API.Models.Errors.ErrorResponse"}}}, "application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BlueTape.Company.API.Models.Errors.ErrorResponse"}}}, "text/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BlueTape.Company.API.Models.Errors.ErrorResponse"}}}}}, "404": {"description": "Not Found"}, "500": {"description": "Server Error", "content": {"text/plain": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BlueTape.Company.API.Models.Errors.ErrorResponse"}}}, "application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BlueTape.Company.API.Models.Errors.ErrorResponse"}}}, "text/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BlueTape.Company.API.Models.Errors.ErrorResponse"}}}}}, "200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/BlueTape.CompanyService.BankAccounts.BankAccountModel"}}, "application/json": {"schema": {"$ref": "#/components/schemas/BlueTape.CompanyService.BankAccounts.BankAccountModel"}}, "text/json": {"schema": {"$ref": "#/components/schemas/BlueTape.CompanyService.BankAccounts.BankAccountModel"}}}}}}}, "/bank-account/{id}/bankAccountIdentity": {"get": {"tags": ["BankAccount"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"400": {"description": "Bad Request", "content": {"text/plain": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BlueTape.Company.API.Models.Errors.ErrorResponse"}}}, "application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BlueTape.Company.API.Models.Errors.ErrorResponse"}}}, "text/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BlueTape.Company.API.Models.Errors.ErrorResponse"}}}}}, "401": {"description": "Unauthorized", "content": {"text/plain": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BlueTape.Company.API.Models.Errors.ErrorResponse"}}}, "application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BlueTape.Company.API.Models.Errors.ErrorResponse"}}}, "text/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BlueTape.Company.API.Models.Errors.ErrorResponse"}}}}}, "404": {"description": "Not Found"}, "500": {"description": "Server Error", "content": {"text/plain": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BlueTape.Company.API.Models.Errors.ErrorResponse"}}}, "application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BlueTape.Company.API.Models.Errors.ErrorResponse"}}}, "text/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BlueTape.Company.API.Models.Errors.ErrorResponse"}}}}}, "200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/BlueTape.CompanyService.BankAccounts.BankAccountModel"}}, "application/json": {"schema": {"$ref": "#/components/schemas/BlueTape.CompanyService.BankAccounts.BankAccountModel"}}, "text/json": {"schema": {"$ref": "#/components/schemas/BlueTape.CompanyService.BankAccounts.BankAccountModel"}}}}}}}, "/bank-account": {"get": {"tags": ["BankAccount"], "parameters": [{"name": "Id", "in": "query", "schema": {"type": "string"}}, {"name": "CompanyId", "in": "query", "schema": {"type": "string"}}, {"name": "PlaidAccountId", "in": "query", "schema": {"type": "string"}}, {"name": "ShowDeactivated", "in": "query", "schema": {"type": "boolean"}}], "responses": {"400": {"description": "Bad Request", "content": {"text/plain": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BlueTape.Company.API.Models.Errors.ErrorResponse"}}}, "application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BlueTape.Company.API.Models.Errors.ErrorResponse"}}}, "text/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BlueTape.Company.API.Models.Errors.ErrorResponse"}}}}}, "401": {"description": "Unauthorized", "content": {"text/plain": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BlueTape.Company.API.Models.Errors.ErrorResponse"}}}, "application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BlueTape.Company.API.Models.Errors.ErrorResponse"}}}, "text/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BlueTape.Company.API.Models.Errors.ErrorResponse"}}}}}, "404": {"description": "Not Found"}, "500": {"description": "Server Error", "content": {"text/plain": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BlueTape.Company.API.Models.Errors.ErrorResponse"}}}, "application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BlueTape.Company.API.Models.Errors.ErrorResponse"}}}, "text/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BlueTape.Company.API.Models.Errors.ErrorResponse"}}}}}, "200": {"description": "Success", "content": {"text/plain": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BlueTape.CompanyService.BankAccounts.BankAccountModel"}}}, "application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BlueTape.CompanyService.BankAccounts.BankAccountModel"}}}, "text/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BlueTape.CompanyService.BankAccounts.BankAccountModel"}}}}}}}}, "/bank-account/company/{id}": {"get": {"tags": ["BankAccount"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string"}}, {"name": "showDeactivated", "in": "query", "schema": {"type": "boolean", "default": false}}], "responses": {"400": {"description": "Bad Request", "content": {"text/plain": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BlueTape.Company.API.Models.Errors.ErrorResponse"}}}, "application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BlueTape.Company.API.Models.Errors.ErrorResponse"}}}, "text/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BlueTape.Company.API.Models.Errors.ErrorResponse"}}}}}, "401": {"description": "Unauthorized", "content": {"text/plain": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BlueTape.Company.API.Models.Errors.ErrorResponse"}}}, "application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BlueTape.Company.API.Models.Errors.ErrorResponse"}}}, "text/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BlueTape.Company.API.Models.Errors.ErrorResponse"}}}}}, "404": {"description": "Not Found"}, "500": {"description": "Server Error", "content": {"text/plain": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BlueTape.Company.API.Models.Errors.ErrorResponse"}}}, "application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BlueTape.Company.API.Models.Errors.ErrorResponse"}}}, "text/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BlueTape.Company.API.Models.Errors.ErrorResponse"}}}}}, "200": {"description": "Success", "content": {"text/plain": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BlueTape.CompanyService.BankAccounts.BankAccountModel"}}}, "application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BlueTape.CompanyService.BankAccounts.BankAccountModel"}}}, "text/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BlueTape.CompanyService.BankAccounts.BankAccountModel"}}}}}}}}, "/bank-account/getByIds": {"post": {"tags": ["BankAccount"], "requestBody": {"content": {"application/json": {"schema": {"type": "array", "items": {"type": "string"}}}, "text/json": {"schema": {"type": "array", "items": {"type": "string"}}}, "application/*+json": {"schema": {"type": "array", "items": {"type": "string"}}}}}, "responses": {"400": {"description": "Bad Request", "content": {"text/plain": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BlueTape.Company.API.Models.Errors.ErrorResponse"}}}, "application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BlueTape.Company.API.Models.Errors.ErrorResponse"}}}, "text/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BlueTape.Company.API.Models.Errors.ErrorResponse"}}}}}, "401": {"description": "Unauthorized", "content": {"text/plain": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BlueTape.Company.API.Models.Errors.ErrorResponse"}}}, "application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BlueTape.Company.API.Models.Errors.ErrorResponse"}}}, "text/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BlueTape.Company.API.Models.Errors.ErrorResponse"}}}}}, "404": {"description": "Not Found"}, "500": {"description": "Server Error", "content": {"text/plain": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BlueTape.Company.API.Models.Errors.ErrorResponse"}}}, "application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BlueTape.Company.API.Models.Errors.ErrorResponse"}}}, "text/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BlueTape.Company.API.Models.Errors.ErrorResponse"}}}}}, "200": {"description": "Success", "content": {"text/plain": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BlueTape.CompanyService.BankAccounts.BankAccountModel"}}}, "application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BlueTape.CompanyService.BankAccounts.BankAccountModel"}}}, "text/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BlueTape.CompanyService.BankAccounts.BankAccountModel"}}}}}}}}, "/bank-account/v3/getByIds": {"post": {"tags": ["BankAccount"], "parameters": [{"name": "showDeactivated", "in": "query", "schema": {"type": "boolean"}}], "requestBody": {"content": {"application/json": {"schema": {"type": "array", "items": {"type": "string"}}}, "text/json": {"schema": {"type": "array", "items": {"type": "string"}}}, "application/*+json": {"schema": {"type": "array", "items": {"type": "string"}}}}}, "responses": {"400": {"description": "Bad Request", "content": {"text/plain": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BlueTape.Company.API.Models.Errors.ErrorResponse"}}}, "application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BlueTape.Company.API.Models.Errors.ErrorResponse"}}}, "text/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BlueTape.Company.API.Models.Errors.ErrorResponse"}}}}}, "401": {"description": "Unauthorized", "content": {"text/plain": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BlueTape.Company.API.Models.Errors.ErrorResponse"}}}, "application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BlueTape.Company.API.Models.Errors.ErrorResponse"}}}, "text/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BlueTape.Company.API.Models.Errors.ErrorResponse"}}}}}, "404": {"description": "Not Found"}, "500": {"description": "Server Error", "content": {"text/plain": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BlueTape.Company.API.Models.Errors.ErrorResponse"}}}, "application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BlueTape.Company.API.Models.Errors.ErrorResponse"}}}, "text/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BlueTape.Company.API.Models.Errors.ErrorResponse"}}}}}, "200": {"description": "Success", "content": {"text/plain": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BlueTape.CompanyService.BankAccounts.BankAccountModel"}}}, "application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BlueTape.CompanyService.BankAccounts.BankAccountModel"}}}, "text/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BlueTape.CompanyService.BankAccounts.BankAccountModel"}}}}}}}}, "/bank-account/batch/{companyId}": {"post": {"tags": ["BankAccount"], "parameters": [{"name": "companyId", "in": "path", "required": true, "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BlueTape.CompanyService.BankAccounts.CreateBankAccountModel"}}}, "text/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BlueTape.CompanyService.BankAccounts.CreateBankAccountModel"}}}, "application/*+json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BlueTape.CompanyService.BankAccounts.CreateBankAccountModel"}}}}}, "responses": {"400": {"description": "Bad Request", "content": {"text/plain": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BlueTape.Company.API.Models.Errors.ErrorResponse"}}}, "application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BlueTape.Company.API.Models.Errors.ErrorResponse"}}}, "text/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BlueTape.Company.API.Models.Errors.ErrorResponse"}}}}}, "401": {"description": "Unauthorized", "content": {"text/plain": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BlueTape.Company.API.Models.Errors.ErrorResponse"}}}, "application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BlueTape.Company.API.Models.Errors.ErrorResponse"}}}, "text/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BlueTape.Company.API.Models.Errors.ErrorResponse"}}}}}, "404": {"description": "Not Found"}, "500": {"description": "Server Error", "content": {"text/plain": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BlueTape.Company.API.Models.Errors.ErrorResponse"}}}, "application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BlueTape.Company.API.Models.Errors.ErrorResponse"}}}, "text/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BlueTape.Company.API.Models.Errors.ErrorResponse"}}}}}, "200": {"description": "Success"}}}}, "/bank-account/{companyId}": {"post": {"tags": ["BankAccount"], "parameters": [{"name": "companyId", "in": "path", "required": true, "schema": {"type": "string"}}, {"name": "userId", "in": "header", "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/BlueTape.CompanyService.BankAccounts.CreateBankAccountModel"}}, "text/json": {"schema": {"$ref": "#/components/schemas/BlueTape.CompanyService.BankAccounts.CreateBankAccountModel"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/BlueTape.CompanyService.BankAccounts.CreateBankAccountModel"}}}}, "responses": {"400": {"description": "Bad Request", "content": {"text/plain": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BlueTape.Company.API.Models.Errors.ErrorResponse"}}}, "application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BlueTape.Company.API.Models.Errors.ErrorResponse"}}}, "text/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BlueTape.Company.API.Models.Errors.ErrorResponse"}}}}}, "401": {"description": "Unauthorized", "content": {"text/plain": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BlueTape.Company.API.Models.Errors.ErrorResponse"}}}, "application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BlueTape.Company.API.Models.Errors.ErrorResponse"}}}, "text/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BlueTape.Company.API.Models.Errors.ErrorResponse"}}}}}, "404": {"description": "Not Found"}, "500": {"description": "Server Error", "content": {"text/plain": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BlueTape.Company.API.Models.Errors.ErrorResponse"}}}, "application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BlueTape.Company.API.Models.Errors.ErrorResponse"}}}, "text/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BlueTape.Company.API.Models.Errors.ErrorResponse"}}}}}, "200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/BlueTape.CompanyService.BankAccounts.BankAccountModel"}}, "application/json": {"schema": {"$ref": "#/components/schemas/BlueTape.CompanyService.BankAccounts.BankAccountModel"}}, "text/json": {"schema": {"$ref": "#/components/schemas/BlueTape.CompanyService.BankAccounts.BankAccountModel"}}}}}}}, "/bank-account/plaid/item/{itemId}/account/{accountId}/status/{status}": {"put": {"tags": ["BankAccount"], "parameters": [{"name": "itemId", "in": "path", "required": true, "schema": {"type": "string"}}, {"name": "accountId", "in": "path", "required": true, "schema": {"type": "string"}}, {"name": "status", "in": "path", "required": true, "schema": {"$ref": "#/components/schemas/BlueTape.Common.Enums.PlaidBankAccountStatus"}}], "responses": {"400": {"description": "Bad Request", "content": {"text/plain": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BlueTape.Company.API.Models.Errors.ErrorResponse"}}}, "application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BlueTape.Company.API.Models.Errors.ErrorResponse"}}}, "text/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BlueTape.Company.API.Models.Errors.ErrorResponse"}}}}}, "401": {"description": "Unauthorized", "content": {"text/plain": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BlueTape.Company.API.Models.Errors.ErrorResponse"}}}, "application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BlueTape.Company.API.Models.Errors.ErrorResponse"}}}, "text/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BlueTape.Company.API.Models.Errors.ErrorResponse"}}}}}, "404": {"description": "Not Found"}, "500": {"description": "Server Error", "content": {"text/plain": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BlueTape.Company.API.Models.Errors.ErrorResponse"}}}, "application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BlueTape.Company.API.Models.Errors.ErrorResponse"}}}, "text/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BlueTape.Company.API.Models.Errors.ErrorResponse"}}}}}, "200": {"description": "Success"}}}}, "/bank-account/plaid/item/{itemId}/status/{status}": {"put": {"tags": ["BankAccount"], "parameters": [{"name": "itemId", "in": "path", "required": true, "schema": {"type": "string"}}, {"name": "status", "in": "path", "required": true, "schema": {"$ref": "#/components/schemas/BlueTape.Common.Enums.PlaidBankAccountStatus"}}], "responses": {"400": {"description": "Bad Request", "content": {"text/plain": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BlueTape.Company.API.Models.Errors.ErrorResponse"}}}, "application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BlueTape.Company.API.Models.Errors.ErrorResponse"}}}, "text/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BlueTape.Company.API.Models.Errors.ErrorResponse"}}}}}, "401": {"description": "Unauthorized", "content": {"text/plain": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BlueTape.Company.API.Models.Errors.ErrorResponse"}}}, "application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BlueTape.Company.API.Models.Errors.ErrorResponse"}}}, "text/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BlueTape.Company.API.Models.Errors.ErrorResponse"}}}}}, "404": {"description": "Not Found"}, "500": {"description": "Server Error", "content": {"text/plain": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BlueTape.Company.API.Models.Errors.ErrorResponse"}}}, "application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BlueTape.Company.API.Models.Errors.ErrorResponse"}}}, "text/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BlueTape.Company.API.Models.Errors.ErrorResponse"}}}}}, "200": {"description": "Success"}}}}, "/bank-account/{id}/settings": {"patch": {"tags": ["BankAccount"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/BlueTape.CompanyService.BankAccounts.PatchBankAccountSettingsModel"}}, "text/json": {"schema": {"$ref": "#/components/schemas/BlueTape.CompanyService.BankAccounts.PatchBankAccountSettingsModel"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/BlueTape.CompanyService.BankAccounts.PatchBankAccountSettingsModel"}}}}, "responses": {"400": {"description": "Bad Request", "content": {"text/plain": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BlueTape.Company.API.Models.Errors.ErrorResponse"}}}, "application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BlueTape.Company.API.Models.Errors.ErrorResponse"}}}, "text/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BlueTape.Company.API.Models.Errors.ErrorResponse"}}}}}, "401": {"description": "Unauthorized", "content": {"text/plain": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BlueTape.Company.API.Models.Errors.ErrorResponse"}}}, "application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BlueTape.Company.API.Models.Errors.ErrorResponse"}}}, "text/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BlueTape.Company.API.Models.Errors.ErrorResponse"}}}}}, "404": {"description": "Not Found"}, "500": {"description": "Server Error", "content": {"text/plain": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BlueTape.Company.API.Models.Errors.ErrorResponse"}}}, "application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BlueTape.Company.API.Models.Errors.ErrorResponse"}}}, "text/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BlueTape.Company.API.Models.Errors.ErrorResponse"}}}}}, "200": {"description": "Success"}}}}, "/bank-account/{id}/cashFlow": {"patch": {"tags": ["BankAccount"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/BlueTape.CompanyService.BankAccounts.PatchBankAccountCashFlow"}}, "text/json": {"schema": {"$ref": "#/components/schemas/BlueTape.CompanyService.BankAccounts.PatchBankAccountCashFlow"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/BlueTape.CompanyService.BankAccounts.PatchBankAccountCashFlow"}}}}, "responses": {"400": {"description": "Bad Request", "content": {"text/plain": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BlueTape.Company.API.Models.Errors.ErrorResponse"}}}, "application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BlueTape.Company.API.Models.Errors.ErrorResponse"}}}, "text/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BlueTape.Company.API.Models.Errors.ErrorResponse"}}}}}, "401": {"description": "Unauthorized", "content": {"text/plain": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BlueTape.Company.API.Models.Errors.ErrorResponse"}}}, "application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BlueTape.Company.API.Models.Errors.ErrorResponse"}}}, "text/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BlueTape.Company.API.Models.Errors.ErrorResponse"}}}}}, "404": {"description": "Not Found"}, "500": {"description": "Server Error", "content": {"text/plain": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BlueTape.Company.API.Models.Errors.ErrorResponse"}}}, "application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BlueTape.Company.API.Models.Errors.ErrorResponse"}}}, "text/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BlueTape.Company.API.Models.Errors.ErrorResponse"}}}}}, "200": {"description": "Success"}}}}, "/bank-account/{id}/giact": {"patch": {"tags": ["BankAccount"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/BlueTape.CompanyService.BankAccounts.Giact.BankAccountGiactModel"}}, "text/json": {"schema": {"$ref": "#/components/schemas/BlueTape.CompanyService.BankAccounts.Giact.BankAccountGiactModel"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/BlueTape.CompanyService.BankAccounts.Giact.BankAccountGiactModel"}}}}, "responses": {"400": {"description": "Bad Request", "content": {"text/plain": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BlueTape.Company.API.Models.Errors.ErrorResponse"}}}, "application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BlueTape.Company.API.Models.Errors.ErrorResponse"}}}, "text/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BlueTape.Company.API.Models.Errors.ErrorResponse"}}}}}, "401": {"description": "Unauthorized", "content": {"text/plain": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BlueTape.Company.API.Models.Errors.ErrorResponse"}}}, "application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BlueTape.Company.API.Models.Errors.ErrorResponse"}}}, "text/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BlueTape.Company.API.Models.Errors.ErrorResponse"}}}}}, "404": {"description": "Not Found"}, "500": {"description": "Server Error", "content": {"text/plain": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BlueTape.Company.API.Models.Errors.ErrorResponse"}}}, "application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BlueTape.Company.API.Models.Errors.ErrorResponse"}}}, "text/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BlueTape.Company.API.Models.Errors.ErrorResponse"}}}}}, "200": {"description": "Success"}}}}, "/bank-account/{id}/aion/settings": {"put": {"tags": ["BankAccount"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/BlueTape.CompanyService.BankAccounts.BankAccountAionSettingsModel"}}, "text/json": {"schema": {"$ref": "#/components/schemas/BlueTape.CompanyService.BankAccounts.BankAccountAionSettingsModel"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/BlueTape.CompanyService.BankAccounts.BankAccountAionSettingsModel"}}}}, "responses": {"400": {"description": "Bad Request", "content": {"text/plain": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BlueTape.Company.API.Models.Errors.ErrorResponse"}}}, "application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BlueTape.Company.API.Models.Errors.ErrorResponse"}}}, "text/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BlueTape.Company.API.Models.Errors.ErrorResponse"}}}}}, "401": {"description": "Unauthorized", "content": {"text/plain": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BlueTape.Company.API.Models.Errors.ErrorResponse"}}}, "application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BlueTape.Company.API.Models.Errors.ErrorResponse"}}}, "text/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BlueTape.Company.API.Models.Errors.ErrorResponse"}}}}}, "404": {"description": "Not Found"}, "500": {"description": "Server Error", "content": {"text/plain": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BlueTape.Company.API.Models.Errors.ErrorResponse"}}}, "application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BlueTape.Company.API.Models.Errors.ErrorResponse"}}}, "text/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BlueTape.Company.API.Models.Errors.ErrorResponse"}}}}}, "200": {"description": "Success"}}}}, "/bank-account/v2": {"get": {"tags": ["BankAccount"], "parameters": [{"name": "Id", "in": "query", "schema": {"type": "string"}}, {"name": "CompanyId", "in": "query", "schema": {"type": "string"}}, {"name": "PlaidAccountId", "in": "query", "schema": {"type": "string"}}, {"name": "ShowDeactivated", "in": "query", "schema": {"type": "boolean"}}], "responses": {"400": {"description": "Bad Request", "content": {"text/plain": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BlueTape.Company.API.Models.Errors.ErrorResponse"}}}, "application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BlueTape.Company.API.Models.Errors.ErrorResponse"}}}, "text/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BlueTape.Company.API.Models.Errors.ErrorResponse"}}}}}, "401": {"description": "Unauthorized", "content": {"text/plain": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BlueTape.Company.API.Models.Errors.ErrorResponse"}}}, "application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BlueTape.Company.API.Models.Errors.ErrorResponse"}}}, "text/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BlueTape.Company.API.Models.Errors.ErrorResponse"}}}}}, "404": {"description": "Not Found"}, "500": {"description": "Server Error", "content": {"text/plain": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BlueTape.Company.API.Models.Errors.ErrorResponse"}}}, "application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BlueTape.Company.API.Models.Errors.ErrorResponse"}}}, "text/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BlueTape.Company.API.Models.Errors.ErrorResponse"}}}}}, "200": {"description": "Success", "content": {"text/plain": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BlueTape.CompanyService.BankAccounts.BankAccountModel"}}}, "application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BlueTape.CompanyService.BankAccounts.BankAccountModel"}}}, "text/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BlueTape.CompanyService.BankAccounts.BankAccountModel"}}}}}}}}, "/bank-account/v2/{id}": {"get": {"tags": ["BankAccount"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid"}}], "responses": {"400": {"description": "Bad Request", "content": {"text/plain": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BlueTape.Company.API.Models.Errors.ErrorResponse"}}}, "application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BlueTape.Company.API.Models.Errors.ErrorResponse"}}}, "text/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BlueTape.Company.API.Models.Errors.ErrorResponse"}}}}}, "401": {"description": "Unauthorized", "content": {"text/plain": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BlueTape.Company.API.Models.Errors.ErrorResponse"}}}, "application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BlueTape.Company.API.Models.Errors.ErrorResponse"}}}, "text/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BlueTape.Company.API.Models.Errors.ErrorResponse"}}}}}, "404": {"description": "Not Found"}, "500": {"description": "Server Error", "content": {"text/plain": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BlueTape.Company.API.Models.Errors.ErrorResponse"}}}, "application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BlueTape.Company.API.Models.Errors.ErrorResponse"}}}, "text/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BlueTape.Company.API.Models.Errors.ErrorResponse"}}}}}, "200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/BlueTape.CompanyService.BankAccounts.BankAccountModel"}}, "application/json": {"schema": {"$ref": "#/components/schemas/BlueTape.CompanyService.BankAccounts.BankAccountModel"}}, "text/json": {"schema": {"$ref": "#/components/schemas/BlueTape.CompanyService.BankAccounts.BankAccountModel"}}}}}}}, "/bank-account/v2/getByIds": {"post": {"tags": ["BankAccount"], "requestBody": {"content": {"application/json": {"schema": {"type": "array", "items": {"type": "string", "format": "uuid"}}}, "text/json": {"schema": {"type": "array", "items": {"type": "string", "format": "uuid"}}}, "application/*+json": {"schema": {"type": "array", "items": {"type": "string", "format": "uuid"}}}}}, "responses": {"400": {"description": "Bad Request", "content": {"text/plain": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BlueTape.Company.API.Models.Errors.ErrorResponse"}}}, "application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BlueTape.Company.API.Models.Errors.ErrorResponse"}}}, "text/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BlueTape.Company.API.Models.Errors.ErrorResponse"}}}}}, "401": {"description": "Unauthorized", "content": {"text/plain": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BlueTape.Company.API.Models.Errors.ErrorResponse"}}}, "application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BlueTape.Company.API.Models.Errors.ErrorResponse"}}}, "text/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BlueTape.Company.API.Models.Errors.ErrorResponse"}}}}}, "404": {"description": "Not Found"}, "500": {"description": "Server Error", "content": {"text/plain": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BlueTape.Company.API.Models.Errors.ErrorResponse"}}}, "application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BlueTape.Company.API.Models.Errors.ErrorResponse"}}}, "text/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BlueTape.Company.API.Models.Errors.ErrorResponse"}}}}}, "200": {"description": "Success", "content": {"text/plain": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BlueTape.CompanyService.BankAccounts.BankAccountModel"}}}, "application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BlueTape.CompanyService.BankAccounts.BankAccountModel"}}}, "text/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BlueTape.CompanyService.BankAccounts.BankAccountModel"}}}}}}}}, "/bank-account/v2/company/{id}": {"get": {"tags": ["BankAccount"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid"}}], "responses": {"400": {"description": "Bad Request", "content": {"text/plain": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BlueTape.Company.API.Models.Errors.ErrorResponse"}}}, "application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BlueTape.Company.API.Models.Errors.ErrorResponse"}}}, "text/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BlueTape.Company.API.Models.Errors.ErrorResponse"}}}}}, "401": {"description": "Unauthorized", "content": {"text/plain": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BlueTape.Company.API.Models.Errors.ErrorResponse"}}}, "application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BlueTape.Company.API.Models.Errors.ErrorResponse"}}}, "text/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BlueTape.Company.API.Models.Errors.ErrorResponse"}}}}}, "404": {"description": "Not Found"}, "500": {"description": "Server Error", "content": {"text/plain": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BlueTape.Company.API.Models.Errors.ErrorResponse"}}}, "application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BlueTape.Company.API.Models.Errors.ErrorResponse"}}}, "text/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BlueTape.Company.API.Models.Errors.ErrorResponse"}}}}}, "200": {"description": "Success", "content": {"text/plain": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BlueTape.CompanyService.BankAccounts.BankAccountModel"}}}, "application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BlueTape.CompanyService.BankAccounts.BankAccountModel"}}}, "text/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BlueTape.CompanyService.BankAccounts.BankAccountModel"}}}}}}}}, "/bank-account/v2/try-migrate": {"post": {"tags": ["BankAccount"], "requestBody": {"content": {"application/json": {"schema": {"type": "array", "items": {"type": "string"}}}, "text/json": {"schema": {"type": "array", "items": {"type": "string"}}}, "application/*+json": {"schema": {"type": "array", "items": {"type": "string"}}}}}, "responses": {"400": {"description": "Bad Request", "content": {"text/plain": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BlueTape.Company.API.Models.Errors.ErrorResponse"}}}, "application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BlueTape.Company.API.Models.Errors.ErrorResponse"}}}, "text/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BlueTape.Company.API.Models.Errors.ErrorResponse"}}}}}, "401": {"description": "Unauthorized", "content": {"text/plain": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BlueTape.Company.API.Models.Errors.ErrorResponse"}}}, "application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BlueTape.Company.API.Models.Errors.ErrorResponse"}}}, "text/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BlueTape.Company.API.Models.Errors.ErrorResponse"}}}}}, "404": {"description": "Not Found"}, "500": {"description": "Server Error", "content": {"text/plain": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BlueTape.Company.API.Models.Errors.ErrorResponse"}}}, "application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BlueTape.Company.API.Models.Errors.ErrorResponse"}}}, "text/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BlueTape.Company.API.Models.Errors.ErrorResponse"}}}}}, "200": {"description": "Success", "content": {"text/plain": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BlueTape.CompanyService.Companies.MigrateResultModel"}}}, "application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BlueTape.CompanyService.Companies.MigrateResultModel"}}}, "text/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BlueTape.CompanyService.Companies.MigrateResultModel"}}}}}}}}, "/env": {"get": {"tags": ["BlueTape.Company.API"], "responses": {"200": {"description": "Success", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Microsoft.AspNetCore.Hosting.IWebHostEnvironment"}}}}}}}, "/companies": {"post": {"tags": ["Company"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/BlueTape.CompanyService.Companies.CreateCompanyModel"}}, "text/json": {"schema": {"$ref": "#/components/schemas/BlueTape.CompanyService.Companies.CreateCompanyModel"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/BlueTape.CompanyService.Companies.CreateCompanyModel"}}}}, "responses": {"400": {"description": "Bad Request", "content": {"text/plain": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BlueTape.Company.API.Models.Errors.ErrorResponse"}}}, "application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BlueTape.Company.API.Models.Errors.ErrorResponse"}}}, "text/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BlueTape.Company.API.Models.Errors.ErrorResponse"}}}}}, "401": {"description": "Unauthorized", "content": {"text/plain": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BlueTape.Company.API.Models.Errors.ErrorResponse"}}}, "application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BlueTape.Company.API.Models.Errors.ErrorResponse"}}}, "text/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BlueTape.Company.API.Models.Errors.ErrorResponse"}}}}}, "404": {"description": "Not Found"}, "500": {"description": "Server Error", "content": {"text/plain": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BlueTape.Company.API.Models.Errors.ErrorResponse"}}}, "application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BlueTape.Company.API.Models.Errors.ErrorResponse"}}}, "text/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BlueTape.Company.API.Models.Errors.ErrorResponse"}}}}}, "200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/BlueTape.CompanyService.Companies.CompanyModel"}}, "application/json": {"schema": {"$ref": "#/components/schemas/BlueTape.CompanyService.Companies.CompanyModel"}}, "text/json": {"schema": {"$ref": "#/components/schemas/BlueTape.CompanyService.Companies.CompanyModel"}}}}}}, "get": {"tags": ["Company"], "parameters": [{"name": "Id", "in": "query", "schema": {"type": "string"}}, {"name": "ActiveAccountOnly", "in": "query", "schema": {"type": "boolean"}}, {"name": "ValidAccountOnly", "in": "query", "schema": {"type": "boolean"}}, {"name": "Status", "in": "query", "schema": {"type": "array", "items": {"type": "string"}}}, {"name": "Account<PERSON><PERSON><PERSON>", "in": "query", "schema": {"type": "array", "items": {"type": "string"}}}, {"name": "Name", "in": "query", "schema": {"type": "string"}}, {"name": "Type", "in": "query", "schema": {"$ref": "#/components/schemas/BlueTape.CompanyService.Common.Enums.CompanyTypeEnum"}}, {"name": "IsGuest", "in": "query", "schema": {"type": "boolean"}}, {"name": "Search", "in": "query", "schema": {"type": "string"}}, {"name": "SortBy", "in": "query", "schema": {"$ref": "#/components/schemas/BlueTape.CompanyService.Enums.AccountSortingParameter"}}, {"name": "SortOrder", "in": "query", "schema": {"$ref": "#/components/schemas/BlueTape.Utilities.Enums.SortOrderType"}}, {"name": "PageNumber", "in": "query", "schema": {"type": "integer", "format": "int32"}}, {"name": "PageSize", "in": "query", "schema": {"type": "integer", "format": "int32"}}], "responses": {"400": {"description": "Bad Request", "content": {"text/plain": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BlueTape.Company.API.Models.Errors.ErrorResponse"}}}, "application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BlueTape.Company.API.Models.Errors.ErrorResponse"}}}, "text/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BlueTape.Company.API.Models.Errors.ErrorResponse"}}}}}, "401": {"description": "Unauthorized", "content": {"text/plain": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BlueTape.Company.API.Models.Errors.ErrorResponse"}}}, "application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BlueTape.Company.API.Models.Errors.ErrorResponse"}}}, "text/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BlueTape.Company.API.Models.Errors.ErrorResponse"}}}}}, "404": {"description": "Not Found"}, "500": {"description": "Server Error", "content": {"text/plain": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BlueTape.Company.API.Models.Errors.ErrorResponse"}}}, "application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BlueTape.Company.API.Models.Errors.ErrorResponse"}}}, "text/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BlueTape.Company.API.Models.Errors.ErrorResponse"}}}}}, "200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/BlueTape.CompanyService.Companies.PaginatedCompanyResponse"}}, "application/json": {"schema": {"$ref": "#/components/schemas/BlueTape.CompanyService.Companies.PaginatedCompanyResponse"}}, "text/json": {"schema": {"$ref": "#/components/schemas/BlueTape.CompanyService.Companies.PaginatedCompanyResponse"}}}}}}}, "/companies/{id}": {"patch": {"tags": ["Company"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string"}}, {"name": "userId", "in": "header", "required": true, "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/BlueTape.CompanyService.Companies.UpdateCompanyModel"}}, "text/json": {"schema": {"$ref": "#/components/schemas/BlueTape.CompanyService.Companies.UpdateCompanyModel"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/BlueTape.CompanyService.Companies.UpdateCompanyModel"}}}}, "responses": {"400": {"description": "Bad Request", "content": {"text/plain": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BlueTape.Company.API.Models.Errors.ErrorResponse"}}}, "application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BlueTape.Company.API.Models.Errors.ErrorResponse"}}}, "text/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BlueTape.Company.API.Models.Errors.ErrorResponse"}}}}}, "401": {"description": "Unauthorized", "content": {"text/plain": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BlueTape.Company.API.Models.Errors.ErrorResponse"}}}, "application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BlueTape.Company.API.Models.Errors.ErrorResponse"}}}, "text/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BlueTape.Company.API.Models.Errors.ErrorResponse"}}}}}, "404": {"description": "Not Found"}, "500": {"description": "Server Error", "content": {"text/plain": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BlueTape.Company.API.Models.Errors.ErrorResponse"}}}, "application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BlueTape.Company.API.Models.Errors.ErrorResponse"}}}, "text/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BlueTape.Company.API.Models.Errors.ErrorResponse"}}}}}, "200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/BlueTape.CompanyService.Companies.CompanyModel"}}, "application/json": {"schema": {"$ref": "#/components/schemas/BlueTape.CompanyService.Companies.CompanyModel"}}, "text/json": {"schema": {"$ref": "#/components/schemas/BlueTape.CompanyService.Companies.CompanyModel"}}}}}}, "get": {"tags": ["Company"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"400": {"description": "Bad Request", "content": {"text/plain": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BlueTape.Company.API.Models.Errors.ErrorResponse"}}}, "application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BlueTape.Company.API.Models.Errors.ErrorResponse"}}}, "text/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BlueTape.Company.API.Models.Errors.ErrorResponse"}}}}}, "401": {"description": "Unauthorized", "content": {"text/plain": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BlueTape.Company.API.Models.Errors.ErrorResponse"}}}, "application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BlueTape.Company.API.Models.Errors.ErrorResponse"}}}, "text/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BlueTape.Company.API.Models.Errors.ErrorResponse"}}}}}, "404": {"description": "Not Found"}, "500": {"description": "Server Error", "content": {"text/plain": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BlueTape.Company.API.Models.Errors.ErrorResponse"}}}, "application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BlueTape.Company.API.Models.Errors.ErrorResponse"}}}, "text/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BlueTape.Company.API.Models.Errors.ErrorResponse"}}}}}, "200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/BlueTape.CompanyService.Companies.CompanyModel"}}, "application/json": {"schema": {"$ref": "#/components/schemas/BlueTape.CompanyService.Companies.CompanyModel"}}, "text/json": {"schema": {"$ref": "#/components/schemas/BlueTape.CompanyService.Companies.CompanyModel"}}}}}}}, "/companies/{id}/paymentDetails": {"get": {"tags": ["Company"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"400": {"description": "Bad Request", "content": {"text/plain": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BlueTape.Company.API.Models.Errors.ErrorResponse"}}}, "application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BlueTape.Company.API.Models.Errors.ErrorResponse"}}}, "text/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BlueTape.Company.API.Models.Errors.ErrorResponse"}}}}}, "401": {"description": "Unauthorized", "content": {"text/plain": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BlueTape.Company.API.Models.Errors.ErrorResponse"}}}, "application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BlueTape.Company.API.Models.Errors.ErrorResponse"}}}, "text/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BlueTape.Company.API.Models.Errors.ErrorResponse"}}}}}, "404": {"description": "Not Found"}, "500": {"description": "Server Error", "content": {"text/plain": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BlueTape.Company.API.Models.Errors.ErrorResponse"}}}, "application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BlueTape.Company.API.Models.Errors.ErrorResponse"}}}, "text/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BlueTape.Company.API.Models.Errors.ErrorResponse"}}}}}, "200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/BlueTape.CompanyService.Companies.CompanyModel"}}, "application/json": {"schema": {"$ref": "#/components/schemas/BlueTape.CompanyService.Companies.CompanyModel"}}, "text/json": {"schema": {"$ref": "#/components/schemas/BlueTape.CompanyService.Companies.CompanyModel"}}}}}}}, "/companies/getByIds": {"post": {"tags": ["Company"], "requestBody": {"content": {"application/json": {"schema": {"type": "array", "items": {"type": "string"}}}, "text/json": {"schema": {"type": "array", "items": {"type": "string"}}}, "application/*+json": {"schema": {"type": "array", "items": {"type": "string"}}}}}, "responses": {"400": {"description": "Bad Request", "content": {"text/plain": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BlueTape.Company.API.Models.Errors.ErrorResponse"}}}, "application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BlueTape.Company.API.Models.Errors.ErrorResponse"}}}, "text/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BlueTape.Company.API.Models.Errors.ErrorResponse"}}}}}, "401": {"description": "Unauthorized", "content": {"text/plain": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BlueTape.Company.API.Models.Errors.ErrorResponse"}}}, "application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BlueTape.Company.API.Models.Errors.ErrorResponse"}}}, "text/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BlueTape.Company.API.Models.Errors.ErrorResponse"}}}}}, "404": {"description": "Not Found"}, "500": {"description": "Server Error", "content": {"text/plain": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BlueTape.Company.API.Models.Errors.ErrorResponse"}}}, "application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BlueTape.Company.API.Models.Errors.ErrorResponse"}}}, "text/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BlueTape.Company.API.Models.Errors.ErrorResponse"}}}}}, "200": {"description": "Success", "content": {"text/plain": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BlueTape.CompanyService.Companies.CompanyModel"}}}, "application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BlueTape.CompanyService.Companies.CompanyModel"}}}, "text/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BlueTape.CompanyService.Companies.CompanyModel"}}}}}}}}, "/companies/{id}/aion/settings": {"put": {"tags": ["Company"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/BlueTape.CompanyService.Companies.CompanyAionSettingsModel"}}, "text/json": {"schema": {"$ref": "#/components/schemas/BlueTape.CompanyService.Companies.CompanyAionSettingsModel"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/BlueTape.CompanyService.Companies.CompanyAionSettingsModel"}}}}, "responses": {"400": {"description": "Bad Request", "content": {"text/plain": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BlueTape.Company.API.Models.Errors.ErrorResponse"}}}, "application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BlueTape.Company.API.Models.Errors.ErrorResponse"}}}, "text/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BlueTape.Company.API.Models.Errors.ErrorResponse"}}}}}, "401": {"description": "Unauthorized", "content": {"text/plain": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BlueTape.Company.API.Models.Errors.ErrorResponse"}}}, "application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BlueTape.Company.API.Models.Errors.ErrorResponse"}}}, "text/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BlueTape.Company.API.Models.Errors.ErrorResponse"}}}}}, "404": {"description": "Not Found"}, "500": {"description": "Server Error", "content": {"text/plain": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BlueTape.Company.API.Models.Errors.ErrorResponse"}}}, "application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BlueTape.Company.API.Models.Errors.ErrorResponse"}}}, "text/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BlueTape.Company.API.Models.Errors.ErrorResponse"}}}}}, "200": {"description": "Success"}}}}, "/companies/{companyId}/cashFlow": {"post": {"tags": ["Company"], "parameters": [{"name": "companyId", "in": "path", "required": true, "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/BlueTape.Company.API.Models.CashFlow.AddPlaidCashFlow"}}, "text/json": {"schema": {"$ref": "#/components/schemas/BlueTape.Company.API.Models.CashFlow.AddPlaidCashFlow"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/BlueTape.Company.API.Models.CashFlow.AddPlaidCashFlow"}}}}, "responses": {"400": {"description": "Bad Request", "content": {"text/plain": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BlueTape.Company.API.Models.Errors.ErrorResponse"}}}, "application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BlueTape.Company.API.Models.Errors.ErrorResponse"}}}, "text/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BlueTape.Company.API.Models.Errors.ErrorResponse"}}}}}, "401": {"description": "Unauthorized", "content": {"text/plain": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BlueTape.Company.API.Models.Errors.ErrorResponse"}}}, "application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BlueTape.Company.API.Models.Errors.ErrorResponse"}}}, "text/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BlueTape.Company.API.Models.Errors.ErrorResponse"}}}}}, "404": {"description": "Not Found"}, "500": {"description": "Server Error", "content": {"text/plain": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BlueTape.Company.API.Models.Errors.ErrorResponse"}}}, "application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BlueTape.Company.API.Models.Errors.ErrorResponse"}}}, "text/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BlueTape.Company.API.Models.Errors.ErrorResponse"}}}}}, "200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/BlueTape.Company.API.Models.CashFlow.Responses.AddPlaidCashFlowResponse"}}, "application/json": {"schema": {"$ref": "#/components/schemas/BlueTape.Company.API.Models.CashFlow.Responses.AddPlaidCashFlowResponse"}}, "text/json": {"schema": {"$ref": "#/components/schemas/BlueTape.Company.API.Models.CashFlow.Responses.AddPlaidCashFlowResponse"}}}}}}, "get": {"tags": ["Company"], "parameters": [{"name": "companyId", "in": "path", "required": true, "schema": {"type": "string"}}, {"name": "AccountId", "in": "query", "schema": {"type": "string"}}, {"name": "From", "in": "query", "schema": {"type": "string", "format": "date-time"}}, {"name": "To", "in": "query", "schema": {"type": "string", "format": "date-time"}}, {"name": "Grouping", "in": "query", "schema": {"$ref": "#/components/schemas/BlueTape.Common.Enums.CashFlowGrouping"}}], "responses": {"400": {"description": "Bad Request", "content": {"text/plain": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BlueTape.Company.API.Models.Errors.ErrorResponse"}}}, "application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BlueTape.Company.API.Models.Errors.ErrorResponse"}}}, "text/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BlueTape.Company.API.Models.Errors.ErrorResponse"}}}}}, "401": {"description": "Unauthorized", "content": {"text/plain": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BlueTape.Company.API.Models.Errors.ErrorResponse"}}}, "application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BlueTape.Company.API.Models.Errors.ErrorResponse"}}}, "text/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BlueTape.Company.API.Models.Errors.ErrorResponse"}}}}}, "404": {"description": "Not Found"}, "500": {"description": "Server Error", "content": {"text/plain": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BlueTape.Company.API.Models.Errors.ErrorResponse"}}}, "application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BlueTape.Company.API.Models.Errors.ErrorResponse"}}}, "text/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BlueTape.Company.API.Models.Errors.ErrorResponse"}}}}}, "200": {"description": "Success", "content": {"text/plain": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BlueTape.Company.API.Models.CashFlow.Responses.CashFlowItemResponse"}}}, "application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BlueTape.Company.API.Models.CashFlow.Responses.CashFlowItemResponse"}}}, "text/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BlueTape.Company.API.Models.CashFlow.Responses.CashFlowItemResponse"}}}}}}}}, "/companies/{companyId}/cashFlow/manual": {"post": {"tags": ["Company"], "parameters": [{"name": "companyId", "in": "path", "required": true, "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/BlueTape.Company.API.Models.CashFlow.AddManualCashFlow"}}, "text/json": {"schema": {"$ref": "#/components/schemas/BlueTape.Company.API.Models.CashFlow.AddManualCashFlow"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/BlueTape.Company.API.Models.CashFlow.AddManualCashFlow"}}}}, "responses": {"400": {"description": "Bad Request", "content": {"text/plain": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BlueTape.Company.API.Models.Errors.ErrorResponse"}}}, "application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BlueTape.Company.API.Models.Errors.ErrorResponse"}}}, "text/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BlueTape.Company.API.Models.Errors.ErrorResponse"}}}}}, "401": {"description": "Unauthorized", "content": {"text/plain": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BlueTape.Company.API.Models.Errors.ErrorResponse"}}}, "application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BlueTape.Company.API.Models.Errors.ErrorResponse"}}}, "text/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BlueTape.Company.API.Models.Errors.ErrorResponse"}}}}}, "404": {"description": "Not Found"}, "500": {"description": "Server Error", "content": {"text/plain": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BlueTape.Company.API.Models.Errors.ErrorResponse"}}}, "application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BlueTape.Company.API.Models.Errors.ErrorResponse"}}}, "text/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BlueTape.Company.API.Models.Errors.ErrorResponse"}}}}}, "200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/BlueTape.Company.API.Models.CashFlow.Responses.AddPlaidCashFlowResponse"}}, "application/json": {"schema": {"$ref": "#/components/schemas/BlueTape.Company.API.Models.CashFlow.Responses.AddPlaidCashFlowResponse"}}, "text/json": {"schema": {"$ref": "#/components/schemas/BlueTape.Company.API.Models.CashFlow.Responses.AddPlaidCashFlowResponse"}}}}}}}, "/companies/{id}/notes": {"post": {"tags": ["Company"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string"}}, {"name": "userId", "in": "header", "required": true, "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/BlueTape.CompanyService.Companies.CreateCompanyNoteModel"}}, "text/json": {"schema": {"$ref": "#/components/schemas/BlueTape.CompanyService.Companies.CreateCompanyNoteModel"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/BlueTape.CompanyService.Companies.CreateCompanyNoteModel"}}}}, "responses": {"400": {"description": "Bad Request", "content": {"text/plain": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BlueTape.Company.API.Models.Errors.ErrorResponse"}}}, "application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BlueTape.Company.API.Models.Errors.ErrorResponse"}}}, "text/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BlueTape.Company.API.Models.Errors.ErrorResponse"}}}}}, "401": {"description": "Unauthorized", "content": {"text/plain": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BlueTape.Company.API.Models.Errors.ErrorResponse"}}}, "application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BlueTape.Company.API.Models.Errors.ErrorResponse"}}}, "text/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BlueTape.Company.API.Models.Errors.ErrorResponse"}}}}}, "404": {"description": "Not Found"}, "500": {"description": "Server Error", "content": {"text/plain": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BlueTape.Company.API.Models.Errors.ErrorResponse"}}}, "application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BlueTape.Company.API.Models.Errors.ErrorResponse"}}}, "text/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BlueTape.Company.API.Models.Errors.ErrorResponse"}}}}}, "200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/BlueTape.CompanyService.Companies.CompanyNoteModel"}}, "application/json": {"schema": {"$ref": "#/components/schemas/BlueTape.CompanyService.Companies.CompanyNoteModel"}}, "text/json": {"schema": {"$ref": "#/components/schemas/BlueTape.CompanyService.Companies.CompanyNoteModel"}}}}}}}, "/companies/{companyId}/cashFlow/paginated": {"get": {"tags": ["Company"], "parameters": [{"name": "companyId", "in": "path", "required": true, "schema": {"type": "string"}}, {"name": "PageNumber", "in": "query", "required": true, "schema": {"type": "integer", "format": "int32"}}, {"name": "PageSize", "in": "query", "required": true, "schema": {"type": "integer", "format": "int32"}}], "responses": {"400": {"description": "Bad Request", "content": {"text/plain": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BlueTape.Company.API.Models.Errors.ErrorResponse"}}}, "application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BlueTape.Company.API.Models.Errors.ErrorResponse"}}}, "text/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BlueTape.Company.API.Models.Errors.ErrorResponse"}}}}}, "401": {"description": "Unauthorized", "content": {"text/plain": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BlueTape.Company.API.Models.Errors.ErrorResponse"}}}, "application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BlueTape.Company.API.Models.Errors.ErrorResponse"}}}, "text/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BlueTape.Company.API.Models.Errors.ErrorResponse"}}}}}, "404": {"description": "Not Found"}, "500": {"description": "Server Error", "content": {"text/plain": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BlueTape.Company.API.Models.Errors.ErrorResponse"}}}, "application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BlueTape.Company.API.Models.Errors.ErrorResponse"}}}, "text/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BlueTape.Company.API.Models.Errors.ErrorResponse"}}}}}, "200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/BlueTape.Company.API.Models.PaginatedList`1[BlueTape.Company.API.Models.CashFlow.Responses.CashFlowItemResponse]"}}, "application/json": {"schema": {"$ref": "#/components/schemas/BlueTape.Company.API.Models.PaginatedList`1[BlueTape.Company.API.Models.CashFlow.Responses.CashFlowItemResponse]"}}, "text/json": {"schema": {"$ref": "#/components/schemas/BlueTape.Company.API.Models.PaginatedList`1[BlueTape.Company.API.Models.CashFlow.Responses.CashFlowItemResponse]"}}}}}}}, "/companies/{companyId}/cashFlow/aggregate": {"get": {"tags": ["Company"], "parameters": [{"name": "companyId", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"400": {"description": "Bad Request", "content": {"text/plain": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BlueTape.Company.API.Models.Errors.ErrorResponse"}}}, "application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BlueTape.Company.API.Models.Errors.ErrorResponse"}}}, "text/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BlueTape.Company.API.Models.Errors.ErrorResponse"}}}}}, "401": {"description": "Unauthorized", "content": {"text/plain": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BlueTape.Company.API.Models.Errors.ErrorResponse"}}}, "application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BlueTape.Company.API.Models.Errors.ErrorResponse"}}}, "text/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BlueTape.Company.API.Models.Errors.ErrorResponse"}}}}}, "404": {"description": "Not Found"}, "500": {"description": "Server Error", "content": {"text/plain": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BlueTape.Company.API.Models.Errors.ErrorResponse"}}}, "application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BlueTape.Company.API.Models.Errors.ErrorResponse"}}}, "text/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BlueTape.Company.API.Models.Errors.ErrorResponse"}}}}}, "200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/BlueTape.Company.API.Models.CashFlow.Responses.CashFlowAggregatedResponse"}}, "application/json": {"schema": {"$ref": "#/components/schemas/BlueTape.Company.API.Models.CashFlow.Responses.CashFlowAggregatedResponse"}}, "text/json": {"schema": {"$ref": "#/components/schemas/BlueTape.Company.API.Models.CashFlow.Responses.CashFlowAggregatedResponse"}}}}}}}, "/companies/{companyId}/cashFlow/details": {"get": {"tags": ["Company"], "parameters": [{"name": "companyId", "in": "path", "required": true, "schema": {"type": "string"}}, {"name": "AccountId", "in": "query", "schema": {"type": "string"}}, {"name": "From", "in": "query", "schema": {"type": "string", "format": "date-time"}}, {"name": "To", "in": "query", "schema": {"type": "string", "format": "date-time"}}, {"name": "Grouping", "in": "query", "schema": {"$ref": "#/components/schemas/BlueTape.Common.Enums.CashFlowGrouping"}}], "responses": {"400": {"description": "Bad Request", "content": {"text/plain": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BlueTape.Company.API.Models.Errors.ErrorResponse"}}}, "application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BlueTape.Company.API.Models.Errors.ErrorResponse"}}}, "text/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BlueTape.Company.API.Models.Errors.ErrorResponse"}}}}}, "401": {"description": "Unauthorized", "content": {"text/plain": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BlueTape.Company.API.Models.Errors.ErrorResponse"}}}, "application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BlueTape.Company.API.Models.Errors.ErrorResponse"}}}, "text/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BlueTape.Company.API.Models.Errors.ErrorResponse"}}}}}, "404": {"description": "Not Found"}, "500": {"description": "Server Error", "content": {"text/plain": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BlueTape.Company.API.Models.Errors.ErrorResponse"}}}, "application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BlueTape.Company.API.Models.Errors.ErrorResponse"}}}, "text/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BlueTape.Company.API.Models.Errors.ErrorResponse"}}}}}, "200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/BlueTape.Company.API.Models.CashFlow.Responses.CashFlowResponse"}}, "application/json": {"schema": {"$ref": "#/components/schemas/BlueTape.Company.API.Models.CashFlow.Responses.CashFlowResponse"}}, "text/json": {"schema": {"$ref": "#/components/schemas/BlueTape.Company.API.Models.CashFlow.Responses.CashFlowResponse"}}}}}}}, "/companies/{companyId}/cashFlow/files": {"get": {"tags": ["Company"], "parameters": [{"name": "companyId", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"400": {"description": "Bad Request", "content": {"text/plain": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BlueTape.Company.API.Models.Errors.ErrorResponse"}}}, "application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BlueTape.Company.API.Models.Errors.ErrorResponse"}}}, "text/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BlueTape.Company.API.Models.Errors.ErrorResponse"}}}}}, "401": {"description": "Unauthorized", "content": {"text/plain": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BlueTape.Company.API.Models.Errors.ErrorResponse"}}}, "application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BlueTape.Company.API.Models.Errors.ErrorResponse"}}}, "text/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BlueTape.Company.API.Models.Errors.ErrorResponse"}}}}}, "404": {"description": "Not Found"}, "500": {"description": "Server Error", "content": {"text/plain": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BlueTape.Company.API.Models.Errors.ErrorResponse"}}}, "application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BlueTape.Company.API.Models.Errors.ErrorResponse"}}}, "text/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BlueTape.Company.API.Models.Errors.ErrorResponse"}}}}}, "200": {"description": "Success", "content": {"text/plain": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BlueTape.CompanyService.Documents.Responses.BankStatementResponse"}}}, "application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BlueTape.CompanyService.Documents.Responses.BankStatementResponse"}}}, "text/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BlueTape.CompanyService.Documents.Responses.BankStatementResponse"}}}}}}}}, "/companies/cashFlow/validate-file": {"post": {"tags": ["Company"], "requestBody": {"content": {"multipart/form-data": {"schema": {"type": "object", "properties": {"ContentType": {"type": "string"}, "ContentDisposition": {"type": "string"}, "Headers": {"type": "object", "additionalProperties": {"type": "array", "items": {"type": "string"}}}, "Length": {"type": "integer", "format": "int64"}, "Name": {"type": "string"}, "FileName": {"type": "string"}}}, "encoding": {"ContentType": {"style": "form"}, "ContentDisposition": {"style": "form"}, "Headers": {"style": "form"}, "Length": {"style": "form"}, "Name": {"style": "form"}, "FileName": {"style": "form"}}}}}, "responses": {"400": {"description": "Bad Request", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/BlueTape.Company.API.Models.Errors.ErrorResponse"}}, "application/json": {"schema": {"$ref": "#/components/schemas/BlueTape.Company.API.Models.Errors.ErrorResponse"}}, "text/json": {"schema": {"$ref": "#/components/schemas/BlueTape.Company.API.Models.Errors.ErrorResponse"}}}}, "401": {"description": "Unauthorized", "content": {"text/plain": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BlueTape.Company.API.Models.Errors.ErrorResponse"}}}, "application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BlueTape.Company.API.Models.Errors.ErrorResponse"}}}, "text/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BlueTape.Company.API.Models.Errors.ErrorResponse"}}}}}, "404": {"description": "Not Found", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/BlueTape.Company.API.Models.Errors.ErrorResponse"}}, "application/json": {"schema": {"$ref": "#/components/schemas/BlueTape.Company.API.Models.Errors.ErrorResponse"}}, "text/json": {"schema": {"$ref": "#/components/schemas/BlueTape.Company.API.Models.Errors.ErrorResponse"}}}}, "500": {"description": "Server Error", "content": {"text/plain": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BlueTape.Company.API.Models.Errors.ErrorResponse"}}}, "application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BlueTape.Company.API.Models.Errors.ErrorResponse"}}}, "text/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BlueTape.Company.API.Models.Errors.ErrorResponse"}}}}}, "200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/BlueTape.Company.API.Models.CashFlow.Responses.ManualCashFlowValidationResultResponse"}}, "application/json": {"schema": {"$ref": "#/components/schemas/BlueTape.Company.API.Models.CashFlow.Responses.ManualCashFlowValidationResultResponse"}}, "text/json": {"schema": {"$ref": "#/components/schemas/BlueTape.Company.API.Models.CashFlow.Responses.ManualCashFlowValidationResultResponse"}}}}}}}, "/companies/{companyId}/cashFlow/{accountId}/file": {"post": {"tags": ["Company"], "parameters": [{"name": "companyId", "in": "path", "required": true, "schema": {"type": "string"}}, {"name": "accountId", "in": "path", "required": true, "schema": {"type": "string"}}, {"name": "userId", "in": "header", "required": true, "schema": {"type": "string"}}], "requestBody": {"content": {"multipart/form-data": {"schema": {"type": "object", "properties": {"ContentType": {"type": "string"}, "ContentDisposition": {"type": "string"}, "Headers": {"type": "object", "additionalProperties": {"type": "array", "items": {"type": "string"}}}, "Length": {"type": "integer", "format": "int64"}, "Name": {"type": "string"}, "FileName": {"type": "string"}}}, "encoding": {"ContentType": {"style": "form"}, "ContentDisposition": {"style": "form"}, "Headers": {"style": "form"}, "Length": {"style": "form"}, "Name": {"style": "form"}, "FileName": {"style": "form"}}}}}, "responses": {"400": {"description": "Bad Request", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/BlueTape.Company.API.Models.Errors.ErrorResponse"}}, "application/json": {"schema": {"$ref": "#/components/schemas/BlueTape.Company.API.Models.Errors.ErrorResponse"}}, "text/json": {"schema": {"$ref": "#/components/schemas/BlueTape.Company.API.Models.Errors.ErrorResponse"}}}}, "401": {"description": "Unauthorized", "content": {"text/plain": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BlueTape.Company.API.Models.Errors.ErrorResponse"}}}, "application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BlueTape.Company.API.Models.Errors.ErrorResponse"}}}, "text/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BlueTape.Company.API.Models.Errors.ErrorResponse"}}}}}, "404": {"description": "Not Found", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/BlueTape.Company.API.Models.Errors.ErrorResponse"}}, "application/json": {"schema": {"$ref": "#/components/schemas/BlueTape.Company.API.Models.Errors.ErrorResponse"}}, "text/json": {"schema": {"$ref": "#/components/schemas/BlueTape.Company.API.Models.Errors.ErrorResponse"}}}}, "500": {"description": "Server Error", "content": {"text/plain": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BlueTape.Company.API.Models.Errors.ErrorResponse"}}}, "application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BlueTape.Company.API.Models.Errors.ErrorResponse"}}}, "text/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BlueTape.Company.API.Models.Errors.ErrorResponse"}}}}}, "200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/BlueTape.CompanyService.CashFlow.AddManualCashFlow"}}, "application/json": {"schema": {"$ref": "#/components/schemas/BlueTape.CompanyService.CashFlow.AddManualCashFlow"}}, "text/json": {"schema": {"$ref": "#/components/schemas/BlueTape.CompanyService.CashFlow.AddManualCashFlow"}}}}}}}, "/companies/{companyId}/active-users": {"get": {"tags": ["Company"], "parameters": [{"name": "companyId", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"400": {"description": "Bad Request", "content": {"text/plain": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BlueTape.Company.API.Models.Errors.ErrorResponse"}}}, "application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BlueTape.Company.API.Models.Errors.ErrorResponse"}}}, "text/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BlueTape.Company.API.Models.Errors.ErrorResponse"}}}}}, "401": {"description": "Unauthorized", "content": {"text/plain": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BlueTape.Company.API.Models.Errors.ErrorResponse"}}}, "application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BlueTape.Company.API.Models.Errors.ErrorResponse"}}}, "text/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BlueTape.Company.API.Models.Errors.ErrorResponse"}}}}}, "404": {"description": "Not Found"}, "500": {"description": "Server Error", "content": {"text/plain": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BlueTape.Company.API.Models.Errors.ErrorResponse"}}}, "application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BlueTape.Company.API.Models.Errors.ErrorResponse"}}}, "text/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BlueTape.Company.API.Models.Errors.ErrorResponse"}}}}}, "200": {"description": "Success", "content": {"text/plain": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BlueTape.Company.Domain.DTOs.Companies.UserDto"}}}, "application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BlueTape.Company.Domain.DTOs.Companies.UserDto"}}}, "text/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BlueTape.Company.Domain.DTOs.Companies.UserDto"}}}}}}}}, "/companies/v2/{id}": {"get": {"tags": ["Company"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid"}}], "responses": {"400": {"description": "Bad Request", "content": {"text/plain": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BlueTape.Company.API.Models.Errors.ErrorResponse"}}}, "application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BlueTape.Company.API.Models.Errors.ErrorResponse"}}}, "text/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BlueTape.Company.API.Models.Errors.ErrorResponse"}}}}}, "401": {"description": "Unauthorized", "content": {"text/plain": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BlueTape.Company.API.Models.Errors.ErrorResponse"}}}, "application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BlueTape.Company.API.Models.Errors.ErrorResponse"}}}, "text/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BlueTape.Company.API.Models.Errors.ErrorResponse"}}}}}, "404": {"description": "Not Found"}, "500": {"description": "Server Error", "content": {"text/plain": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BlueTape.Company.API.Models.Errors.ErrorResponse"}}}, "application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BlueTape.Company.API.Models.Errors.ErrorResponse"}}}, "text/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BlueTape.Company.API.Models.Errors.ErrorResponse"}}}}}, "200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/BlueTape.CompanyService.Companies.CompanyModel"}}, "application/json": {"schema": {"$ref": "#/components/schemas/BlueTape.CompanyService.Companies.CompanyModel"}}, "text/json": {"schema": {"$ref": "#/components/schemas/BlueTape.CompanyService.Companies.CompanyModel"}}}}}}}, "/companies/v2/getByIds": {"post": {"tags": ["Company"], "requestBody": {"content": {"application/json": {"schema": {"type": "array", "items": {"type": "string", "format": "uuid"}}}, "text/json": {"schema": {"type": "array", "items": {"type": "string", "format": "uuid"}}}, "application/*+json": {"schema": {"type": "array", "items": {"type": "string", "format": "uuid"}}}}}, "responses": {"400": {"description": "Bad Request", "content": {"text/plain": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BlueTape.Company.API.Models.Errors.ErrorResponse"}}}, "application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BlueTape.Company.API.Models.Errors.ErrorResponse"}}}, "text/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BlueTape.Company.API.Models.Errors.ErrorResponse"}}}}}, "401": {"description": "Unauthorized", "content": {"text/plain": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BlueTape.Company.API.Models.Errors.ErrorResponse"}}}, "application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BlueTape.Company.API.Models.Errors.ErrorResponse"}}}, "text/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BlueTape.Company.API.Models.Errors.ErrorResponse"}}}}}, "404": {"description": "Not Found"}, "500": {"description": "Server Error", "content": {"text/plain": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BlueTape.Company.API.Models.Errors.ErrorResponse"}}}, "application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BlueTape.Company.API.Models.Errors.ErrorResponse"}}}, "text/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BlueTape.Company.API.Models.Errors.ErrorResponse"}}}}}, "200": {"description": "Success", "content": {"text/plain": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BlueTape.CompanyService.Companies.CompanyModel"}}}, "application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BlueTape.CompanyService.Companies.CompanyModel"}}}, "text/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BlueTape.CompanyService.Companies.CompanyModel"}}}}}}}}, "/companies/v2/try-migrate": {"post": {"tags": ["Company"], "requestBody": {"content": {"application/json": {"schema": {"type": "array", "items": {"type": "string"}}}, "text/json": {"schema": {"type": "array", "items": {"type": "string"}}}, "application/*+json": {"schema": {"type": "array", "items": {"type": "string"}}}}}, "responses": {"400": {"description": "Bad Request", "content": {"text/plain": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BlueTape.Company.API.Models.Errors.ErrorResponse"}}}, "application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BlueTape.Company.API.Models.Errors.ErrorResponse"}}}, "text/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BlueTape.Company.API.Models.Errors.ErrorResponse"}}}}}, "401": {"description": "Unauthorized", "content": {"text/plain": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BlueTape.Company.API.Models.Errors.ErrorResponse"}}}, "application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BlueTape.Company.API.Models.Errors.ErrorResponse"}}}, "text/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BlueTape.Company.API.Models.Errors.ErrorResponse"}}}}}, "404": {"description": "Not Found"}, "500": {"description": "Server Error", "content": {"text/plain": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BlueTape.Company.API.Models.Errors.ErrorResponse"}}}, "application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BlueTape.Company.API.Models.Errors.ErrorResponse"}}}, "text/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BlueTape.Company.API.Models.Errors.ErrorResponse"}}}}}, "200": {"description": "Success", "content": {"text/plain": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BlueTape.CompanyService.Companies.MigrateResultModel"}}}, "application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BlueTape.CompanyService.Companies.MigrateResultModel"}}}, "text/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BlueTape.CompanyService.Companies.MigrateResultModel"}}}}}}}}, "/companies/v2/with-pagination": {"get": {"tags": ["Company"], "parameters": [{"name": "pageSize", "in": "query", "schema": {"type": "integer", "format": "int32"}}, {"name": "pageNumber", "in": "query", "schema": {"type": "integer", "format": "int32"}}, {"name": "validAccountsOnly", "in": "query", "schema": {"type": "boolean"}}], "responses": {"400": {"description": "Bad Request", "content": {"text/plain": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BlueTape.Company.API.Models.Errors.ErrorResponse"}}}, "application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BlueTape.Company.API.Models.Errors.ErrorResponse"}}}, "text/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BlueTape.Company.API.Models.Errors.ErrorResponse"}}}}}, "401": {"description": "Unauthorized", "content": {"text/plain": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BlueTape.Company.API.Models.Errors.ErrorResponse"}}}, "application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BlueTape.Company.API.Models.Errors.ErrorResponse"}}}, "text/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BlueTape.Company.API.Models.Errors.ErrorResponse"}}}}}, "404": {"description": "Not Found"}, "500": {"description": "Server Error", "content": {"text/plain": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BlueTape.Company.API.Models.Errors.ErrorResponse"}}}, "application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BlueTape.Company.API.Models.Errors.ErrorResponse"}}}, "text/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BlueTape.Company.API.Models.Errors.ErrorResponse"}}}}}}}}, "/companies/v2": {"get": {"tags": ["Company"], "parameters": [{"name": "validAccountsOnly", "in": "query", "schema": {"type": "boolean"}}], "responses": {"400": {"description": "Bad Request", "content": {"text/plain": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BlueTape.Company.API.Models.Errors.ErrorResponse"}}}, "application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BlueTape.Company.API.Models.Errors.ErrorResponse"}}}, "text/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BlueTape.Company.API.Models.Errors.ErrorResponse"}}}}}, "401": {"description": "Unauthorized", "content": {"text/plain": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BlueTape.Company.API.Models.Errors.ErrorResponse"}}}, "application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BlueTape.Company.API.Models.Errors.ErrorResponse"}}}, "text/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BlueTape.Company.API.Models.Errors.ErrorResponse"}}}}}, "404": {"description": "Not Found"}, "500": {"description": "Server Error", "content": {"text/plain": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BlueTape.Company.API.Models.Errors.ErrorResponse"}}}, "application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BlueTape.Company.API.Models.Errors.ErrorResponse"}}}, "text/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BlueTape.Company.API.Models.Errors.ErrorResponse"}}}}}}}, "post": {"tags": ["Company"], "parameters": [{"name": "isIdOnly", "in": "query", "schema": {"type": "boolean"}}], "requestBody": {"content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BlueTape.CompanyService.Common.Enums.AccountStatusEnum"}}}, "text/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BlueTape.CompanyService.Common.Enums.AccountStatusEnum"}}}, "application/*+json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BlueTape.CompanyService.Common.Enums.AccountStatusEnum"}}}}}, "responses": {"400": {"description": "Bad Request", "content": {"text/plain": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BlueTape.Company.API.Models.Errors.ErrorResponse"}}}, "application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BlueTape.Company.API.Models.Errors.ErrorResponse"}}}, "text/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BlueTape.Company.API.Models.Errors.ErrorResponse"}}}}}, "401": {"description": "Unauthorized", "content": {"text/plain": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BlueTape.Company.API.Models.Errors.ErrorResponse"}}}, "application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BlueTape.Company.API.Models.Errors.ErrorResponse"}}}, "text/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BlueTape.Company.API.Models.Errors.ErrorResponse"}}}}}, "404": {"description": "Not Found"}, "500": {"description": "Server Error", "content": {"text/plain": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BlueTape.Company.API.Models.Errors.ErrorResponse"}}}, "application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BlueTape.Company.API.Models.Errors.ErrorResponse"}}}, "text/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BlueTape.Company.API.Models.Errors.ErrorResponse"}}}}}}}}, "/customers/{id}": {"get": {"tags": ["Customer"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"400": {"description": "Bad Request", "content": {"text/plain": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BlueTape.Company.API.Models.Errors.ErrorResponse"}}}, "application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BlueTape.Company.API.Models.Errors.ErrorResponse"}}}, "text/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BlueTape.Company.API.Models.Errors.ErrorResponse"}}}}}, "401": {"description": "Unauthorized", "content": {"text/plain": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BlueTape.Company.API.Models.Errors.ErrorResponse"}}}, "application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BlueTape.Company.API.Models.Errors.ErrorResponse"}}}, "text/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BlueTape.Company.API.Models.Errors.ErrorResponse"}}}}}, "404": {"description": "Not Found"}, "500": {"description": "Server Error", "content": {"text/plain": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BlueTape.Company.API.Models.Errors.ErrorResponse"}}}, "application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BlueTape.Company.API.Models.Errors.ErrorResponse"}}}, "text/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BlueTape.Company.API.Models.Errors.ErrorResponse"}}}}}, "200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/BlueTape.CompanyService.Customers.CustomerModel"}}, "application/json": {"schema": {"$ref": "#/components/schemas/BlueTape.CompanyService.Customers.CustomerModel"}}, "text/json": {"schema": {"$ref": "#/components/schemas/BlueTape.CompanyService.Customers.CustomerModel"}}}}}}}, "/customers/integration/{integrationId}/contact/{contactId}": {"get": {"tags": ["Customer"], "parameters": [{"name": "contactId", "in": "path", "required": true, "schema": {"type": "string"}}, {"name": "integrationId", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"400": {"description": "Bad Request", "content": {"text/plain": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BlueTape.Company.API.Models.Errors.ErrorResponse"}}}, "application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BlueTape.Company.API.Models.Errors.ErrorResponse"}}}, "text/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BlueTape.Company.API.Models.Errors.ErrorResponse"}}}}}, "401": {"description": "Unauthorized", "content": {"text/plain": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BlueTape.Company.API.Models.Errors.ErrorResponse"}}}, "application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BlueTape.Company.API.Models.Errors.ErrorResponse"}}}, "text/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BlueTape.Company.API.Models.Errors.ErrorResponse"}}}}}, "404": {"description": "Not Found"}, "500": {"description": "Server Error", "content": {"text/plain": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BlueTape.Company.API.Models.Errors.ErrorResponse"}}}, "application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BlueTape.Company.API.Models.Errors.ErrorResponse"}}}, "text/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BlueTape.Company.API.Models.Errors.ErrorResponse"}}}}}, "200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/BlueTape.CompanyService.Customers.CustomerModel"}}, "application/json": {"schema": {"$ref": "#/components/schemas/BlueTape.CompanyService.Customers.CustomerModel"}}, "text/json": {"schema": {"$ref": "#/components/schemas/BlueTape.CompanyService.Customers.CustomerModel"}}}}}}}, "/customers/getByIds": {"post": {"tags": ["Customer"], "requestBody": {"content": {"application/json": {"schema": {"type": "array", "items": {"type": "string"}}}, "text/json": {"schema": {"type": "array", "items": {"type": "string"}}}, "application/*+json": {"schema": {"type": "array", "items": {"type": "string"}}}}}, "responses": {"400": {"description": "Bad Request", "content": {"text/plain": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BlueTape.Company.API.Models.Errors.ErrorResponse"}}}, "application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BlueTape.Company.API.Models.Errors.ErrorResponse"}}}, "text/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BlueTape.Company.API.Models.Errors.ErrorResponse"}}}}}, "401": {"description": "Unauthorized", "content": {"text/plain": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BlueTape.Company.API.Models.Errors.ErrorResponse"}}}, "application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BlueTape.Company.API.Models.Errors.ErrorResponse"}}}, "text/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BlueTape.Company.API.Models.Errors.ErrorResponse"}}}}}, "404": {"description": "Not Found"}, "500": {"description": "Server Error", "content": {"text/plain": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BlueTape.Company.API.Models.Errors.ErrorResponse"}}}, "application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BlueTape.Company.API.Models.Errors.ErrorResponse"}}}, "text/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BlueTape.Company.API.Models.Errors.ErrorResponse"}}}}}, "200": {"description": "Success", "content": {"text/plain": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BlueTape.CompanyService.Customers.CustomerModel"}}}, "application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BlueTape.CompanyService.Customers.CustomerModel"}}}, "text/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BlueTape.CompanyService.Customers.CustomerModel"}}}}}}}}, "/customers/billing-contacts/{id}": {"get": {"tags": ["Customer"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"400": {"description": "Bad Request", "content": {"text/plain": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BlueTape.Company.API.Models.Errors.ErrorResponse"}}}, "application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BlueTape.Company.API.Models.Errors.ErrorResponse"}}}, "text/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BlueTape.Company.API.Models.Errors.ErrorResponse"}}}}}, "401": {"description": "Unauthorized", "content": {"text/plain": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BlueTape.Company.API.Models.Errors.ErrorResponse"}}}, "application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BlueTape.Company.API.Models.Errors.ErrorResponse"}}}, "text/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BlueTape.Company.API.Models.Errors.ErrorResponse"}}}}}, "404": {"description": "Not Found"}, "500": {"description": "Server Error", "content": {"text/plain": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BlueTape.Company.API.Models.Errors.ErrorResponse"}}}, "application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BlueTape.Company.API.Models.Errors.ErrorResponse"}}}, "text/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BlueTape.Company.API.Models.Errors.ErrorResponse"}}}}}, "200": {"description": "Success", "content": {"text/plain": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BlueTape.CompanyService.Customers.CustomerModel"}}}, "application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BlueTape.CompanyService.Customers.CustomerModel"}}}, "text/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BlueTape.CompanyService.Customers.CustomerModel"}}}}}}}}, "/customers/companies/{id}": {"get": {"tags": ["Customer"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"400": {"description": "Bad Request", "content": {"text/plain": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BlueTape.Company.API.Models.Errors.ErrorResponse"}}}, "application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BlueTape.Company.API.Models.Errors.ErrorResponse"}}}, "text/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BlueTape.Company.API.Models.Errors.ErrorResponse"}}}}}, "401": {"description": "Unauthorized", "content": {"text/plain": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BlueTape.Company.API.Models.Errors.ErrorResponse"}}}, "application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BlueTape.Company.API.Models.Errors.ErrorResponse"}}}, "text/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BlueTape.Company.API.Models.Errors.ErrorResponse"}}}}}, "404": {"description": "Not Found"}, "500": {"description": "Server Error", "content": {"text/plain": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BlueTape.Company.API.Models.Errors.ErrorResponse"}}}, "application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BlueTape.Company.API.Models.Errors.ErrorResponse"}}}, "text/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BlueTape.Company.API.Models.Errors.ErrorResponse"}}}}}, "200": {"description": "Success", "content": {"text/plain": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BlueTape.CompanyService.Customers.CustomerModel"}}}, "application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BlueTape.CompanyService.Customers.CustomerModel"}}}, "text/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BlueTape.CompanyService.Customers.CustomerModel"}}}}}}}}, "/customers/{customerId}": {"patch": {"tags": ["Customer"], "parameters": [{"name": "customerId", "in": "path", "required": true, "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/BlueTape.CompanyService.Customers.UpdateCustomerModel"}}, "text/json": {"schema": {"$ref": "#/components/schemas/BlueTape.CompanyService.Customers.UpdateCustomerModel"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/BlueTape.CompanyService.Customers.UpdateCustomerModel"}}}}, "responses": {"400": {"description": "Bad Request", "content": {"text/plain": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BlueTape.Company.API.Models.Errors.ErrorResponse"}}}, "application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BlueTape.Company.API.Models.Errors.ErrorResponse"}}}, "text/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BlueTape.Company.API.Models.Errors.ErrorResponse"}}}}}, "401": {"description": "Unauthorized", "content": {"text/plain": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BlueTape.Company.API.Models.Errors.ErrorResponse"}}}, "application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BlueTape.Company.API.Models.Errors.ErrorResponse"}}}, "text/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BlueTape.Company.API.Models.Errors.ErrorResponse"}}}}}, "404": {"description": "Not Found"}, "500": {"description": "Server Error", "content": {"text/plain": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BlueTape.Company.API.Models.Errors.ErrorResponse"}}}, "application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BlueTape.Company.API.Models.Errors.ErrorResponse"}}}, "text/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BlueTape.Company.API.Models.Errors.ErrorResponse"}}}}}, "200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/BlueTape.CompanyService.Customers.CustomerModel"}}, "application/json": {"schema": {"$ref": "#/components/schemas/BlueTape.CompanyService.Customers.CustomerModel"}}, "text/json": {"schema": {"$ref": "#/components/schemas/BlueTape.CompanyService.Customers.CustomerModel"}}}}}}}, "/customers/companies/getByIds": {"post": {"tags": ["Customer"], "requestBody": {"content": {"application/json": {"schema": {"type": "array", "items": {"type": "string"}}}, "text/json": {"schema": {"type": "array", "items": {"type": "string"}}}, "application/*+json": {"schema": {"type": "array", "items": {"type": "string"}}}}}, "responses": {"400": {"description": "Bad Request", "content": {"text/plain": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BlueTape.Company.API.Models.Errors.ErrorResponse"}}}, "application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BlueTape.Company.API.Models.Errors.ErrorResponse"}}}, "text/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BlueTape.Company.API.Models.Errors.ErrorResponse"}}}}}, "401": {"description": "Unauthorized", "content": {"text/plain": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BlueTape.Company.API.Models.Errors.ErrorResponse"}}}, "application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BlueTape.Company.API.Models.Errors.ErrorResponse"}}}, "text/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BlueTape.Company.API.Models.Errors.ErrorResponse"}}}}}, "404": {"description": "Not Found"}, "500": {"description": "Server Error", "content": {"text/plain": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BlueTape.Company.API.Models.Errors.ErrorResponse"}}}, "application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BlueTape.Company.API.Models.Errors.ErrorResponse"}}}, "text/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BlueTape.Company.API.Models.Errors.ErrorResponse"}}}}}, "200": {"description": "Success", "content": {"text/plain": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BlueTape.CompanyService.Customers.CustomerModel"}}}, "application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BlueTape.CompanyService.Customers.CustomerModel"}}}, "text/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BlueTape.CompanyService.Customers.CustomerModel"}}}}}}}}, "/customers/companies/{merchantId}/customerCompanyId/{customerCompanyId}": {"get": {"tags": ["Customer"], "parameters": [{"name": "merchantId", "in": "path", "required": true, "schema": {"type": "string"}}, {"name": "customerCompanyId", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"400": {"description": "Bad Request", "content": {"text/plain": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BlueTape.Company.API.Models.Errors.ErrorResponse"}}}, "application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BlueTape.Company.API.Models.Errors.ErrorResponse"}}}, "text/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BlueTape.Company.API.Models.Errors.ErrorResponse"}}}}}, "401": {"description": "Unauthorized", "content": {"text/plain": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BlueTape.Company.API.Models.Errors.ErrorResponse"}}}, "application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BlueTape.Company.API.Models.Errors.ErrorResponse"}}}, "text/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BlueTape.Company.API.Models.Errors.ErrorResponse"}}}}}, "404": {"description": "Not Found"}, "500": {"description": "Server Error", "content": {"text/plain": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BlueTape.Company.API.Models.Errors.ErrorResponse"}}}, "application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BlueTape.Company.API.Models.Errors.ErrorResponse"}}}, "text/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BlueTape.Company.API.Models.Errors.ErrorResponse"}}}}}, "200": {"description": "Success", "content": {"text/plain": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BlueTape.CompanyService.Customers.CustomerModel"}}}, "application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BlueTape.CompanyService.Customers.CustomerModel"}}}, "text/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BlueTape.CompanyService.Customers.CustomerModel"}}}}}}}}, "/documents/{id}/path": {"get": {"tags": ["Document"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid"}}], "responses": {"400": {"description": "Bad Request", "content": {"text/plain": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BlueTape.Company.API.Models.Errors.ErrorResponse"}}}, "application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BlueTape.Company.API.Models.Errors.ErrorResponse"}}}, "text/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BlueTape.Company.API.Models.Errors.ErrorResponse"}}}}}, "401": {"description": "Unauthorized", "content": {"text/plain": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BlueTape.Company.API.Models.Errors.ErrorResponse"}}}, "application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BlueTape.Company.API.Models.Errors.ErrorResponse"}}}, "text/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BlueTape.Company.API.Models.Errors.ErrorResponse"}}}}}, "404": {"description": "Not Found"}, "500": {"description": "Server Error", "content": {"text/plain": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BlueTape.Company.API.Models.Errors.ErrorResponse"}}}, "application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BlueTape.Company.API.Models.Errors.ErrorResponse"}}}, "text/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BlueTape.Company.API.Models.Errors.ErrorResponse"}}}}}, "200": {"description": "Success", "content": {"text/plain": {"schema": {"type": "string"}}, "application/json": {"schema": {"type": "string"}}, "text/json": {"schema": {"type": "string"}}}}}}}, "/documents/loan-application/{id}": {"get": {"tags": ["Document"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string"}}, {"name": "code", "in": "query", "schema": {"$ref": "#/components/schemas/BlueTape.CompanyService.Common.Enums.TemplateType"}}], "responses": {"400": {"description": "Bad Request", "content": {"text/plain": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BlueTape.Company.API.Models.Errors.ErrorResponse"}}}, "application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BlueTape.Company.API.Models.Errors.ErrorResponse"}}}, "text/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BlueTape.Company.API.Models.Errors.ErrorResponse"}}}}}, "401": {"description": "Unauthorized", "content": {"text/plain": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BlueTape.Company.API.Models.Errors.ErrorResponse"}}}, "application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BlueTape.Company.API.Models.Errors.ErrorResponse"}}}, "text/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BlueTape.Company.API.Models.Errors.ErrorResponse"}}}}}, "404": {"description": "Not Found"}, "500": {"description": "Server Error", "content": {"text/plain": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BlueTape.Company.API.Models.Errors.ErrorResponse"}}}, "application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BlueTape.Company.API.Models.Errors.ErrorResponse"}}}, "text/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BlueTape.Company.API.Models.Errors.ErrorResponse"}}}}}, "200": {"description": "Success", "content": {"text/plain": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BlueTape.CompanyService.Documents.Responses.DocumentLoanApplicationResponse"}}}, "application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BlueTape.CompanyService.Documents.Responses.DocumentLoanApplicationResponse"}}}, "text/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BlueTape.CompanyService.Documents.Responses.DocumentLoanApplicationResponse"}}}}}}, "deprecated": true}, "post": {"tags": ["Document"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/BlueTape.Company.API.Models.Documents.Requests.DocumentLoanApplicationRequest"}}, "text/json": {"schema": {"$ref": "#/components/schemas/BlueTape.Company.API.Models.Documents.Requests.DocumentLoanApplicationRequest"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/BlueTape.Company.API.Models.Documents.Requests.DocumentLoanApplicationRequest"}}}}, "responses": {"400": {"description": "Bad Request", "content": {"text/plain": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BlueTape.Company.API.Models.Errors.ErrorResponse"}}}, "application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BlueTape.Company.API.Models.Errors.ErrorResponse"}}}, "text/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BlueTape.Company.API.Models.Errors.ErrorResponse"}}}}}, "401": {"description": "Unauthorized", "content": {"text/plain": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BlueTape.Company.API.Models.Errors.ErrorResponse"}}}, "application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BlueTape.Company.API.Models.Errors.ErrorResponse"}}}, "text/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BlueTape.Company.API.Models.Errors.ErrorResponse"}}}}}, "404": {"description": "Not Found"}, "500": {"description": "Server Error", "content": {"text/plain": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BlueTape.Company.API.Models.Errors.ErrorResponse"}}}, "application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BlueTape.Company.API.Models.Errors.ErrorResponse"}}}, "text/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BlueTape.Company.API.Models.Errors.ErrorResponse"}}}}}, "201": {"description": "Created", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/BlueTape.CompanyService.Documents.Responses.DocumentLoanApplicationResponse"}}, "application/json": {"schema": {"$ref": "#/components/schemas/BlueTape.CompanyService.Documents.Responses.DocumentLoanApplicationResponse"}}, "text/json": {"schema": {"$ref": "#/components/schemas/BlueTape.CompanyService.Documents.Responses.DocumentLoanApplicationResponse"}}}}}}, "delete": {"tags": ["Document"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"400": {"description": "Bad Request", "content": {"text/plain": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BlueTape.Company.API.Models.Errors.ErrorResponse"}}}, "application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BlueTape.Company.API.Models.Errors.ErrorResponse"}}}, "text/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BlueTape.Company.API.Models.Errors.ErrorResponse"}}}}}, "401": {"description": "Unauthorized", "content": {"text/plain": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BlueTape.Company.API.Models.Errors.ErrorResponse"}}}, "application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BlueTape.Company.API.Models.Errors.ErrorResponse"}}}, "text/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BlueTape.Company.API.Models.Errors.ErrorResponse"}}}}}, "404": {"description": "Not Found"}, "500": {"description": "Server Error", "content": {"text/plain": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BlueTape.Company.API.Models.Errors.ErrorResponse"}}}, "application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BlueTape.Company.API.Models.Errors.ErrorResponse"}}}, "text/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BlueTape.Company.API.Models.Errors.ErrorResponse"}}}}}, "200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/BlueTape.CompanyService.Documents.Responses.NewDocumentNameResponse"}}, "application/json": {"schema": {"$ref": "#/components/schemas/BlueTape.CompanyService.Documents.Responses.NewDocumentNameResponse"}}, "text/json": {"schema": {"$ref": "#/components/schemas/BlueTape.CompanyService.Documents.Responses.NewDocumentNameResponse"}}}}}}}, "/documents/company/{id}/type/{code}/metadata": {"get": {"tags": ["Document"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string"}}, {"name": "code", "in": "path", "required": true, "schema": {"$ref": "#/components/schemas/BlueTape.CompanyService.Common.Enums.TemplateType"}}], "responses": {"400": {"description": "Bad Request", "content": {"text/plain": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BlueTape.Company.API.Models.Errors.ErrorResponse"}}}, "application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BlueTape.Company.API.Models.Errors.ErrorResponse"}}}, "text/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BlueTape.Company.API.Models.Errors.ErrorResponse"}}}}}, "401": {"description": "Unauthorized", "content": {"text/plain": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BlueTape.Company.API.Models.Errors.ErrorResponse"}}}, "application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BlueTape.Company.API.Models.Errors.ErrorResponse"}}}, "text/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BlueTape.Company.API.Models.Errors.ErrorResponse"}}}}}, "404": {"description": "Not Found"}, "500": {"description": "Server Error", "content": {"text/plain": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BlueTape.Company.API.Models.Errors.ErrorResponse"}}}, "application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BlueTape.Company.API.Models.Errors.ErrorResponse"}}}, "text/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BlueTape.Company.API.Models.Errors.ErrorResponse"}}}}}, "200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/BlueTape.CompanyService.Documents.Responses.DocumentMetadataResponse"}}, "application/json": {"schema": {"$ref": "#/components/schemas/BlueTape.CompanyService.Documents.Responses.DocumentMetadataResponse"}}, "text/json": {"schema": {"$ref": "#/components/schemas/BlueTape.CompanyService.Documents.Responses.DocumentMetadataResponse"}}}}}}}, "/documents/company/{id}/type/{code}": {"get": {"tags": ["Document"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string"}}, {"name": "code", "in": "path", "required": true, "schema": {"$ref": "#/components/schemas/BlueTape.CompanyService.Documents.Enums.TemplateTypeFilter"}}], "responses": {"400": {"description": "Bad Request", "content": {"text/plain": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BlueTape.Company.API.Models.Errors.ErrorResponse"}}}, "application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BlueTape.Company.API.Models.Errors.ErrorResponse"}}}, "text/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BlueTape.Company.API.Models.Errors.ErrorResponse"}}}}}, "401": {"description": "Unauthorized", "content": {"text/plain": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BlueTape.Company.API.Models.Errors.ErrorResponse"}}}, "application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BlueTape.Company.API.Models.Errors.ErrorResponse"}}}, "text/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BlueTape.Company.API.Models.Errors.ErrorResponse"}}}}}, "404": {"description": "Not Found"}, "500": {"description": "Server Error", "content": {"text/plain": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BlueTape.Company.API.Models.Errors.ErrorResponse"}}}, "application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BlueTape.Company.API.Models.Errors.ErrorResponse"}}}, "text/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BlueTape.Company.API.Models.Errors.ErrorResponse"}}}}}, "200": {"description": "Success", "content": {"text/plain": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BlueTape.CompanyService.Documents.Responses.DocumentLoanApplicationResponse"}}}, "application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BlueTape.CompanyService.Documents.Responses.DocumentLoanApplicationResponse"}}}, "text/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BlueTape.CompanyService.Documents.Responses.DocumentLoanApplicationResponse"}}}}}}, "deprecated": true}}, "/documents/company/{id}/all": {"get": {"tags": ["Document"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string"}}, {"name": "types", "in": "query", "schema": {"type": "array", "items": {"$ref": "#/components/schemas/BlueTape.CompanyService.Documents.Enums.TemplateTypeFilter"}}}], "responses": {"400": {"description": "Bad Request", "content": {"text/plain": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BlueTape.Company.API.Models.Errors.ErrorResponse"}}}, "application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BlueTape.Company.API.Models.Errors.ErrorResponse"}}}, "text/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BlueTape.Company.API.Models.Errors.ErrorResponse"}}}}}, "401": {"description": "Unauthorized", "content": {"text/plain": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BlueTape.Company.API.Models.Errors.ErrorResponse"}}}, "application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BlueTape.Company.API.Models.Errors.ErrorResponse"}}}, "text/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BlueTape.Company.API.Models.Errors.ErrorResponse"}}}}}, "404": {"description": "Not Found"}, "500": {"description": "Server Error", "content": {"text/plain": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BlueTape.Company.API.Models.Errors.ErrorResponse"}}}, "application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BlueTape.Company.API.Models.Errors.ErrorResponse"}}}, "text/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BlueTape.Company.API.Models.Errors.ErrorResponse"}}}}}, "200": {"description": "Success", "content": {"text/plain": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BlueTape.CompanyService.Documents.Responses.DocumentLoanApplicationResponse"}}}, "application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BlueTape.CompanyService.Documents.Responses.DocumentLoanApplicationResponse"}}}, "text/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BlueTape.CompanyService.Documents.Responses.DocumentLoanApplicationResponse"}}}}}}}}, "/documents/reference/{referenceId}/type/{type}/latest": {"get": {"tags": ["Document"], "parameters": [{"name": "referenceId", "in": "path", "required": true, "schema": {"type": "string"}}, {"name": "type", "in": "path", "required": true, "schema": {"$ref": "#/components/schemas/BlueTape.CompanyService.Common.Enums.TemplateType"}}], "responses": {"400": {"description": "Bad Request", "content": {"text/plain": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BlueTape.Company.API.Models.Errors.ErrorResponse"}}}, "application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BlueTape.Company.API.Models.Errors.ErrorResponse"}}}, "text/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BlueTape.Company.API.Models.Errors.ErrorResponse"}}}}}, "401": {"description": "Unauthorized", "content": {"text/plain": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BlueTape.Company.API.Models.Errors.ErrorResponse"}}}, "application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BlueTape.Company.API.Models.Errors.ErrorResponse"}}}, "text/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BlueTape.Company.API.Models.Errors.ErrorResponse"}}}}}, "404": {"description": "Not Found"}, "500": {"description": "Server Error", "content": {"text/plain": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BlueTape.Company.API.Models.Errors.ErrorResponse"}}}, "application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BlueTape.Company.API.Models.Errors.ErrorResponse"}}}, "text/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BlueTape.Company.API.Models.Errors.ErrorResponse"}}}}}, "200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/BlueTape.Company.API.Models.Documents.Responses.DocumentResponse"}}, "application/json": {"schema": {"$ref": "#/components/schemas/BlueTape.Company.API.Models.Documents.Responses.DocumentResponse"}}, "text/json": {"schema": {"$ref": "#/components/schemas/BlueTape.Company.API.Models.Documents.Responses.DocumentResponse"}}}}}}}, "/documents/company/{companyId}/reference/{referenceId}": {"post": {"tags": ["Document"], "parameters": [{"name": "companyId", "in": "path", "required": true, "schema": {"type": "string"}}, {"name": "referenceId", "in": "path", "required": true, "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/BlueTape.Company.API.Models.Documents.Requests.DocumentLoanApplicationRequest"}}, "text/json": {"schema": {"$ref": "#/components/schemas/BlueTape.Company.API.Models.Documents.Requests.DocumentLoanApplicationRequest"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/BlueTape.Company.API.Models.Documents.Requests.DocumentLoanApplicationRequest"}}}}, "responses": {"400": {"description": "Bad Request", "content": {"text/plain": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BlueTape.Company.API.Models.Errors.ErrorResponse"}}}, "application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BlueTape.Company.API.Models.Errors.ErrorResponse"}}}, "text/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BlueTape.Company.API.Models.Errors.ErrorResponse"}}}}}, "401": {"description": "Unauthorized", "content": {"text/plain": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BlueTape.Company.API.Models.Errors.ErrorResponse"}}}, "application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BlueTape.Company.API.Models.Errors.ErrorResponse"}}}, "text/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BlueTape.Company.API.Models.Errors.ErrorResponse"}}}}}, "404": {"description": "Not Found"}, "500": {"description": "Server Error", "content": {"text/plain": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BlueTape.Company.API.Models.Errors.ErrorResponse"}}}, "application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BlueTape.Company.API.Models.Errors.ErrorResponse"}}}, "text/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BlueTape.Company.API.Models.Errors.ErrorResponse"}}}}}, "201": {"description": "Created", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/BlueTape.CompanyService.Documents.Responses.DocumentLoanApplicationResponse"}}, "application/json": {"schema": {"$ref": "#/components/schemas/BlueTape.CompanyService.Documents.Responses.DocumentLoanApplicationResponse"}}, "text/json": {"schema": {"$ref": "#/components/schemas/BlueTape.CompanyService.Documents.Responses.DocumentLoanApplicationResponse"}}}}}}}, "/documents/new/{id}/by-template-type": {"get": {"tags": ["Document"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string"}}, {"name": "code", "in": "query", "schema": {"$ref": "#/components/schemas/BlueTape.CompanyService.Common.Enums.TemplateType"}}], "responses": {"400": {"description": "Bad Request", "content": {"text/plain": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BlueTape.Company.API.Models.Errors.ErrorResponse"}}}, "application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BlueTape.Company.API.Models.Errors.ErrorResponse"}}}, "text/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BlueTape.Company.API.Models.Errors.ErrorResponse"}}}}}, "401": {"description": "Unauthorized", "content": {"text/plain": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BlueTape.Company.API.Models.Errors.ErrorResponse"}}}, "application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BlueTape.Company.API.Models.Errors.ErrorResponse"}}}, "text/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BlueTape.Company.API.Models.Errors.ErrorResponse"}}}}}, "404": {"description": "Not Found"}, "500": {"description": "Server Error", "content": {"text/plain": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BlueTape.Company.API.Models.Errors.ErrorResponse"}}}, "application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BlueTape.Company.API.Models.Errors.ErrorResponse"}}}, "text/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BlueTape.Company.API.Models.Errors.ErrorResponse"}}}}}, "200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/BlueTape.CompanyService.Documents.Responses.NewDocumentNameResponse"}}, "application/json": {"schema": {"$ref": "#/components/schemas/BlueTape.CompanyService.Documents.Responses.NewDocumentNameResponse"}}, "text/json": {"schema": {"$ref": "#/components/schemas/BlueTape.CompanyService.Documents.Responses.NewDocumentNameResponse"}}}}}}}, "/documents/new/{id}/by-template-id": {"get": {"tags": ["Document"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string"}}, {"name": "templateId", "in": "query", "schema": {"type": "string", "format": "uuid"}}], "responses": {"400": {"description": "Bad Request", "content": {"text/plain": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BlueTape.Company.API.Models.Errors.ErrorResponse"}}}, "application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BlueTape.Company.API.Models.Errors.ErrorResponse"}}}, "text/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BlueTape.Company.API.Models.Errors.ErrorResponse"}}}}}, "401": {"description": "Unauthorized", "content": {"text/plain": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BlueTape.Company.API.Models.Errors.ErrorResponse"}}}, "application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BlueTape.Company.API.Models.Errors.ErrorResponse"}}}, "text/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BlueTape.Company.API.Models.Errors.ErrorResponse"}}}}}, "404": {"description": "Not Found"}, "500": {"description": "Server Error", "content": {"text/plain": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BlueTape.Company.API.Models.Errors.ErrorResponse"}}}, "application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BlueTape.Company.API.Models.Errors.ErrorResponse"}}}, "text/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BlueTape.Company.API.Models.Errors.ErrorResponse"}}}}}, "200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/BlueTape.CompanyService.Documents.Responses.NewDocumentNameResponse"}}, "application/json": {"schema": {"$ref": "#/components/schemas/BlueTape.CompanyService.Documents.Responses.NewDocumentNameResponse"}}, "text/json": {"schema": {"$ref": "#/components/schemas/BlueTape.CompanyService.Documents.Responses.NewDocumentNameResponse"}}}}}}}, "/documents/new/{templateType}/company/{companyId}/reference/{referenceId}": {"get": {"tags": ["Document"], "parameters": [{"name": "templateType", "in": "path", "required": true, "schema": {"$ref": "#/components/schemas/BlueTape.CompanyService.Common.Enums.TemplateType"}}, {"name": "companyId", "in": "path", "required": true, "schema": {"type": "string"}}, {"name": "referenceId", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"400": {"description": "Bad Request", "content": {"text/plain": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BlueTape.Company.API.Models.Errors.ErrorResponse"}}}, "application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BlueTape.Company.API.Models.Errors.ErrorResponse"}}}, "text/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BlueTape.Company.API.Models.Errors.ErrorResponse"}}}}}, "401": {"description": "Unauthorized", "content": {"text/plain": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BlueTape.Company.API.Models.Errors.ErrorResponse"}}}, "application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BlueTape.Company.API.Models.Errors.ErrorResponse"}}}, "text/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BlueTape.Company.API.Models.Errors.ErrorResponse"}}}}}, "404": {"description": "Not Found"}, "500": {"description": "Server Error", "content": {"text/plain": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BlueTape.Company.API.Models.Errors.ErrorResponse"}}}, "application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BlueTape.Company.API.Models.Errors.ErrorResponse"}}}, "text/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BlueTape.Company.API.Models.Errors.ErrorResponse"}}}}}, "200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/BlueTape.CompanyService.Documents.Responses.NewDocumentNameResponse"}}, "application/json": {"schema": {"$ref": "#/components/schemas/BlueTape.CompanyService.Documents.Responses.NewDocumentNameResponse"}}, "text/json": {"schema": {"$ref": "#/components/schemas/BlueTape.CompanyService.Documents.Responses.NewDocumentNameResponse"}}}}}}}, "/documents/{id}/approvals": {"get": {"tags": ["Document"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid"}}], "responses": {"400": {"description": "Bad Request", "content": {"text/plain": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BlueTape.Company.API.Models.Errors.ErrorResponse"}}}, "application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BlueTape.Company.API.Models.Errors.ErrorResponse"}}}, "text/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BlueTape.Company.API.Models.Errors.ErrorResponse"}}}}}, "401": {"description": "Unauthorized", "content": {"text/plain": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BlueTape.Company.API.Models.Errors.ErrorResponse"}}}, "application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BlueTape.Company.API.Models.Errors.ErrorResponse"}}}, "text/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BlueTape.Company.API.Models.Errors.ErrorResponse"}}}}}, "404": {"description": "Not Found"}, "500": {"description": "Server Error", "content": {"text/plain": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BlueTape.Company.API.Models.Errors.ErrorResponse"}}}, "application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BlueTape.Company.API.Models.Errors.ErrorResponse"}}}, "text/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BlueTape.Company.API.Models.Errors.ErrorResponse"}}}}}, "200": {"description": "Success", "content": {"text/plain": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BlueTape.CompanyService.Documents.Responses.DocumentApprovalResponse"}}}, "application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BlueTape.CompanyService.Documents.Responses.DocumentApprovalResponse"}}}, "text/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BlueTape.CompanyService.Documents.Responses.DocumentApprovalResponse"}}}}}}}, "post": {"tags": ["Document"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid"}}, {"name": "X-User-Id", "in": "header", "required": true, "schema": {"type": "string"}}, {"name": "alreadyApproved", "in": "query", "schema": {"type": "boolean", "default": false}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/BlueTape.Company.API.Models.Documents.Requests.CreateDocumentApprovalRequest"}}, "text/json": {"schema": {"$ref": "#/components/schemas/BlueTape.Company.API.Models.Documents.Requests.CreateDocumentApprovalRequest"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/BlueTape.Company.API.Models.Documents.Requests.CreateDocumentApprovalRequest"}}}}, "responses": {"400": {"description": "Bad Request", "content": {"text/plain": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BlueTape.Company.API.Models.Errors.ErrorResponse"}}}, "application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BlueTape.Company.API.Models.Errors.ErrorResponse"}}}, "text/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BlueTape.Company.API.Models.Errors.ErrorResponse"}}}}}, "401": {"description": "Unauthorized", "content": {"text/plain": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BlueTape.Company.API.Models.Errors.ErrorResponse"}}}, "application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BlueTape.Company.API.Models.Errors.ErrorResponse"}}}, "text/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BlueTape.Company.API.Models.Errors.ErrorResponse"}}}}}, "404": {"description": "Not Found"}, "500": {"description": "Server Error", "content": {"text/plain": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BlueTape.Company.API.Models.Errors.ErrorResponse"}}}, "application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BlueTape.Company.API.Models.Errors.ErrorResponse"}}}, "text/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BlueTape.Company.API.Models.Errors.ErrorResponse"}}}}}, "200": {"description": "Success", "content": {"text/plain": {"schema": {"type": "string"}}, "application/json": {"schema": {"type": "string"}}, "text/json": {"schema": {"type": "string"}}}}}}}, "/documents/approvals/authorize": {"post": {"tags": ["Document"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/BlueTape.Company.API.Models.Documents.Requests.AuthorizeApprovalRequest"}}, "text/json": {"schema": {"$ref": "#/components/schemas/BlueTape.Company.API.Models.Documents.Requests.AuthorizeApprovalRequest"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/BlueTape.Company.API.Models.Documents.Requests.AuthorizeApprovalRequest"}}}}, "responses": {"400": {"description": "Bad Request", "content": {"text/plain": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BlueTape.Company.API.Models.Errors.ErrorResponse"}}}, "application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BlueTape.Company.API.Models.Errors.ErrorResponse"}}}, "text/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BlueTape.Company.API.Models.Errors.ErrorResponse"}}}}}, "401": {"description": "Unauthorized", "content": {"text/plain": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BlueTape.Company.API.Models.Errors.ErrorResponse"}}}, "application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BlueTape.Company.API.Models.Errors.ErrorResponse"}}}, "text/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BlueTape.Company.API.Models.Errors.ErrorResponse"}}}}}, "404": {"description": "Not Found"}, "500": {"description": "Server Error", "content": {"text/plain": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BlueTape.Company.API.Models.Errors.ErrorResponse"}}}, "application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BlueTape.Company.API.Models.Errors.ErrorResponse"}}}, "text/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BlueTape.Company.API.Models.Errors.ErrorResponse"}}}}}, "200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/BlueTape.CompanyService.Documents.Responses.DocumentApprovalResponse"}}, "application/json": {"schema": {"$ref": "#/components/schemas/BlueTape.CompanyService.Documents.Responses.DocumentApprovalResponse"}}, "text/json": {"schema": {"$ref": "#/components/schemas/BlueTape.CompanyService.Documents.Responses.DocumentApprovalResponse"}}}}}}}, "/documents/validate-jwt": {"get": {"tags": ["Document"], "parameters": [{"name": "jwt", "in": "query", "schema": {"type": "string"}}], "responses": {"400": {"description": "Bad Request", "content": {"text/plain": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BlueTape.Company.API.Models.Errors.ErrorResponse"}}}, "application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BlueTape.Company.API.Models.Errors.ErrorResponse"}}}, "text/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BlueTape.Company.API.Models.Errors.ErrorResponse"}}}}}, "401": {"description": "Unauthorized", "content": {"text/plain": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BlueTape.Company.API.Models.Errors.ErrorResponse"}}}, "application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BlueTape.Company.API.Models.Errors.ErrorResponse"}}}, "text/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BlueTape.Company.API.Models.Errors.ErrorResponse"}}}}}, "404": {"description": "Not Found"}, "500": {"description": "Server Error", "content": {"text/plain": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BlueTape.Company.API.Models.Errors.ErrorResponse"}}}, "application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BlueTape.Company.API.Models.Errors.ErrorResponse"}}}, "text/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BlueTape.Company.API.Models.Errors.ErrorResponse"}}}}}, "200": {"description": "Success"}, "412": {"description": "<PERSON><PERSON>", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/Microsoft.AspNetCore.Mvc.ProblemDetails"}}, "application/json": {"schema": {"$ref": "#/components/schemas/Microsoft.AspNetCore.Mvc.ProblemDetails"}}, "text/json": {"schema": {"$ref": "#/components/schemas/Microsoft.AspNetCore.Mvc.ProblemDetails"}}}}}}}, "/documents/loan-application/legacy": {"post": {"tags": ["DocumentLegacy"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/BlueTape.Company.API.Models.Documents.Requests.DocumentLoanApplicationLegacyRequest"}}, "text/json": {"schema": {"$ref": "#/components/schemas/BlueTape.Company.API.Models.Documents.Requests.DocumentLoanApplicationLegacyRequest"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/BlueTape.Company.API.Models.Documents.Requests.DocumentLoanApplicationLegacyRequest"}}}}, "responses": {"400": {"description": "Bad Request", "content": {"text/plain": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BlueTape.Company.API.Models.Errors.ErrorResponse"}}}, "application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BlueTape.Company.API.Models.Errors.ErrorResponse"}}}, "text/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BlueTape.Company.API.Models.Errors.ErrorResponse"}}}}}, "401": {"description": "Unauthorized", "content": {"text/plain": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BlueTape.Company.API.Models.Errors.ErrorResponse"}}}, "application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BlueTape.Company.API.Models.Errors.ErrorResponse"}}}, "text/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BlueTape.Company.API.Models.Errors.ErrorResponse"}}}}}, "404": {"description": "Not Found"}, "500": {"description": "Server Error", "content": {"text/plain": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BlueTape.Company.API.Models.Errors.ErrorResponse"}}}, "application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BlueTape.Company.API.Models.Errors.ErrorResponse"}}}, "text/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BlueTape.Company.API.Models.Errors.ErrorResponse"}}}}}, "201": {"description": "Created", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/BlueTape.CompanyService.Documents.Responses.DocumentLoanApplicationResponse"}}, "application/json": {"schema": {"$ref": "#/components/schemas/BlueTape.CompanyService.Documents.Responses.DocumentLoanApplicationResponse"}}, "text/json": {"schema": {"$ref": "#/components/schemas/BlueTape.CompanyService.Documents.Responses.DocumentLoanApplicationResponse"}}}}}}}, "/documents/loan-application/agreement": {"get": {"tags": ["DocumentLegacy"], "responses": {"400": {"description": "Bad Request", "content": {"text/plain": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BlueTape.Company.API.Models.Errors.ErrorResponse"}}}, "application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BlueTape.Company.API.Models.Errors.ErrorResponse"}}}, "text/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BlueTape.Company.API.Models.Errors.ErrorResponse"}}}}}, "401": {"description": "Unauthorized", "content": {"text/plain": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BlueTape.Company.API.Models.Errors.ErrorResponse"}}}, "application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BlueTape.Company.API.Models.Errors.ErrorResponse"}}}, "text/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BlueTape.Company.API.Models.Errors.ErrorResponse"}}}}}, "404": {"description": "Not Found"}, "500": {"description": "Server Error", "content": {"text/plain": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BlueTape.Company.API.Models.Errors.ErrorResponse"}}}, "application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BlueTape.Company.API.Models.Errors.ErrorResponse"}}}, "text/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BlueTape.Company.API.Models.Errors.ErrorResponse"}}}}}, "200": {"description": "Success", "content": {"text/plain": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BlueTape.Document.Domain.DTOs.LoanApplicationMigrationInfo"}}}, "application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BlueTape.Document.Domain.DTOs.LoanApplicationMigrationInfo"}}}, "text/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BlueTape.Document.Domain.DTOs.LoanApplicationMigrationInfo"}}}}}}}}, "/documents/all": {"get": {"tags": ["DocumentLegacy"], "responses": {"400": {"description": "Bad Request", "content": {"text/plain": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BlueTape.Company.API.Models.Errors.ErrorResponse"}}}, "application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BlueTape.Company.API.Models.Errors.ErrorResponse"}}}, "text/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BlueTape.Company.API.Models.Errors.ErrorResponse"}}}}}, "401": {"description": "Unauthorized", "content": {"text/plain": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BlueTape.Company.API.Models.Errors.ErrorResponse"}}}, "application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BlueTape.Company.API.Models.Errors.ErrorResponse"}}}, "text/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BlueTape.Company.API.Models.Errors.ErrorResponse"}}}}}, "404": {"description": "Not Found"}, "500": {"description": "Server Error", "content": {"text/plain": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BlueTape.Company.API.Models.Errors.ErrorResponse"}}}, "application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BlueTape.Company.API.Models.Errors.ErrorResponse"}}}, "text/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BlueTape.Company.API.Models.Errors.ErrorResponse"}}}}}, "200": {"description": "Success", "content": {"text/plain": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BlueTape.CompanyService.Documents.Responses.DocumentReportResponse"}}}, "application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BlueTape.CompanyService.Documents.Responses.DocumentReportResponse"}}}, "text/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BlueTape.CompanyService.Documents.Responses.DocumentReportResponse"}}}}}}}}, "/guestSuppliers": {"get": {"tags": ["GuestSuppliers"], "parameters": [{"name": "Search", "in": "query", "schema": {"type": "string"}}, {"name": "SortBy", "in": "query", "schema": {"type": "string"}}, {"name": "SortOrder", "in": "query", "schema": {"type": "string"}}, {"name": "PageNumber", "in": "query", "schema": {"type": "integer", "format": "int32"}}, {"name": "PageSize", "in": "query", "schema": {"type": "integer", "format": "int32"}}], "responses": {"400": {"description": "Bad Request", "content": {"text/plain": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BlueTape.Company.API.Models.Errors.ErrorResponse"}}}, "application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BlueTape.Company.API.Models.Errors.ErrorResponse"}}}, "text/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BlueTape.Company.API.Models.Errors.ErrorResponse"}}}}}, "401": {"description": "Unauthorized", "content": {"text/plain": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BlueTape.Company.API.Models.Errors.ErrorResponse"}}}, "application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BlueTape.Company.API.Models.Errors.ErrorResponse"}}}, "text/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BlueTape.Company.API.Models.Errors.ErrorResponse"}}}}}, "404": {"description": "Not Found"}, "500": {"description": "Server Error", "content": {"text/plain": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BlueTape.Company.API.Models.Errors.ErrorResponse"}}}, "application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BlueTape.Company.API.Models.Errors.ErrorResponse"}}}, "text/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BlueTape.Company.API.Models.Errors.ErrorResponse"}}}}}, "200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/BlueTape.Company.API.Models.ResultWithPaginationModel`1[BlueTape.CompanyService.GuestSuppliers.GuestSupplierModel]"}}, "application/json": {"schema": {"$ref": "#/components/schemas/BlueTape.Company.API.Models.ResultWithPaginationModel`1[BlueTape.CompanyService.GuestSuppliers.GuestSupplierModel]"}}, "text/json": {"schema": {"$ref": "#/components/schemas/BlueTape.Company.API.Models.ResultWithPaginationModel`1[BlueTape.CompanyService.GuestSuppliers.GuestSupplierModel]"}}}}}}}, "/guestSuppliers/{id}/customers": {"get": {"tags": ["GuestSuppliers"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string"}}, {"name": "Account<PERSON><PERSON><PERSON>", "in": "query", "schema": {"$ref": "#/components/schemas/BlueTape.CompanyService.Common.Enums.AccountStatusEnum"}}, {"name": "Search", "in": "query", "schema": {"type": "string"}}, {"name": "SortBy", "in": "query", "schema": {"type": "string"}}, {"name": "SortOrder", "in": "query", "schema": {"type": "string"}}, {"name": "PageNumber", "in": "query", "schema": {"type": "integer", "format": "int32"}}, {"name": "PageSize", "in": "query", "schema": {"type": "integer", "format": "int32"}}], "responses": {"400": {"description": "Bad Request", "content": {"text/plain": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BlueTape.Company.API.Models.Errors.ErrorResponse"}}}, "application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BlueTape.Company.API.Models.Errors.ErrorResponse"}}}, "text/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BlueTape.Company.API.Models.Errors.ErrorResponse"}}}}}, "401": {"description": "Unauthorized", "content": {"text/plain": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BlueTape.Company.API.Models.Errors.ErrorResponse"}}}, "application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BlueTape.Company.API.Models.Errors.ErrorResponse"}}}, "text/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BlueTape.Company.API.Models.Errors.ErrorResponse"}}}}}, "404": {"description": "Not Found"}, "500": {"description": "Server Error", "content": {"text/plain": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BlueTape.Company.API.Models.Errors.ErrorResponse"}}}, "application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BlueTape.Company.API.Models.Errors.ErrorResponse"}}}, "text/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BlueTape.Company.API.Models.Errors.ErrorResponse"}}}}}, "200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/BlueTape.Company.API.Models.ResultWithPaginationModel`1[BlueTape.CompanyService.GuestSuppliers.GuestSupplierCustomerModel]"}}, "application/json": {"schema": {"$ref": "#/components/schemas/BlueTape.Company.API.Models.ResultWithPaginationModel`1[BlueTape.CompanyService.GuestSuppliers.GuestSupplierCustomerModel]"}}, "text/json": {"schema": {"$ref": "#/components/schemas/BlueTape.Company.API.Models.ResultWithPaginationModel`1[BlueTape.CompanyService.GuestSuppliers.GuestSupplierCustomerModel]"}}}}}}}, "/guestSuppliers/{id}/invoices": {"get": {"tags": ["GuestSuppliers"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string"}}, {"name": "DueDateFrom", "in": "query", "schema": {"type": "string", "format": "date-time"}}, {"name": "DueDateTo", "in": "query", "schema": {"type": "string", "format": "date-time"}}, {"name": "Search", "in": "query", "schema": {"type": "string"}}, {"name": "SortBy", "in": "query", "schema": {"type": "string"}}, {"name": "SortOrder", "in": "query", "schema": {"type": "string"}}, {"name": "PageNumber", "in": "query", "schema": {"type": "integer", "format": "int32"}}, {"name": "PageSize", "in": "query", "schema": {"type": "integer", "format": "int32"}}], "responses": {"400": {"description": "Bad Request", "content": {"text/plain": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BlueTape.Company.API.Models.Errors.ErrorResponse"}}}, "application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BlueTape.Company.API.Models.Errors.ErrorResponse"}}}, "text/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BlueTape.Company.API.Models.Errors.ErrorResponse"}}}}}, "401": {"description": "Unauthorized", "content": {"text/plain": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BlueTape.Company.API.Models.Errors.ErrorResponse"}}}, "application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BlueTape.Company.API.Models.Errors.ErrorResponse"}}}, "text/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BlueTape.Company.API.Models.Errors.ErrorResponse"}}}}}, "404": {"description": "Not Found"}, "500": {"description": "Server Error", "content": {"text/plain": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BlueTape.Company.API.Models.Errors.ErrorResponse"}}}, "application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BlueTape.Company.API.Models.Errors.ErrorResponse"}}}, "text/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BlueTape.Company.API.Models.Errors.ErrorResponse"}}}}}, "200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/BlueTape.Company.API.Models.ResultWithPaginationModel`1[BlueTape.CompanyService.GuestSuppliers.GuestSupplierInvoiceModel]"}}, "application/json": {"schema": {"$ref": "#/components/schemas/BlueTape.Company.API.Models.ResultWithPaginationModel`1[BlueTape.CompanyService.GuestSuppliers.GuestSupplierInvoiceModel]"}}, "text/json": {"schema": {"$ref": "#/components/schemas/BlueTape.Company.API.Models.ResultWithPaginationModel`1[BlueTape.CompanyService.GuestSuppliers.GuestSupplierInvoiceModel]"}}}}}}}, "/guestSuppliers/{id}/notes": {"get": {"tags": ["GuestSuppliers"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"400": {"description": "Bad Request", "content": {"text/plain": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BlueTape.Company.API.Models.Errors.ErrorResponse"}}}, "application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BlueTape.Company.API.Models.Errors.ErrorResponse"}}}, "text/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BlueTape.Company.API.Models.Errors.ErrorResponse"}}}}}, "401": {"description": "Unauthorized", "content": {"text/plain": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BlueTape.Company.API.Models.Errors.ErrorResponse"}}}, "application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BlueTape.Company.API.Models.Errors.ErrorResponse"}}}, "text/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BlueTape.Company.API.Models.Errors.ErrorResponse"}}}}}, "404": {"description": "Not Found"}, "500": {"description": "Server Error", "content": {"text/plain": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BlueTape.Company.API.Models.Errors.ErrorResponse"}}}, "application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BlueTape.Company.API.Models.Errors.ErrorResponse"}}}, "text/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BlueTape.Company.API.Models.Errors.ErrorResponse"}}}}}, "200": {"description": "Success", "content": {"text/plain": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BlueTape.CompanyService.Companies.CompanyNoteModel"}}}, "application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BlueTape.CompanyService.Companies.CompanyNoteModel"}}}, "text/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BlueTape.CompanyService.Companies.CompanyNoteModel"}}}}}}}}, "/helper/loan-application/{id}/all-approved-by-supplier-id": {"get": {"tags": ["Helper"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"400": {"description": "Bad Request", "content": {"text/plain": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BlueTape.Company.API.Models.Errors.ErrorResponse"}}}, "application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BlueTape.Company.API.Models.Errors.ErrorResponse"}}}, "text/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BlueTape.Company.API.Models.Errors.ErrorResponse"}}}}}, "401": {"description": "Unauthorized", "content": {"text/plain": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BlueTape.Company.API.Models.Errors.ErrorResponse"}}}, "application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BlueTape.Company.API.Models.Errors.ErrorResponse"}}}, "text/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BlueTape.Company.API.Models.Errors.ErrorResponse"}}}}}, "404": {"description": "Not Found"}, "500": {"description": "Server Error", "content": {"text/plain": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BlueTape.Company.API.Models.Errors.ErrorResponse"}}}, "application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BlueTape.Company.API.Models.Errors.ErrorResponse"}}}, "text/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BlueTape.Company.API.Models.Errors.ErrorResponse"}}}}}, "200": {"description": "Success", "content": {"text/plain": {"schema": {"type": "array", "items": {"type": "string"}}}, "application/json": {"schema": {"type": "array", "items": {"type": "string"}}}, "text/json": {"schema": {"type": "array", "items": {"type": "string"}}}}}}}}, "/helper/loc/report": {"get": {"tags": ["Helper"], "parameters": [{"name": "batchSize", "in": "query", "schema": {"type": "integer", "format": "int32"}}, {"name": "loanApproved", "in": "query", "schema": {"type": "boolean"}}, {"name": "loanExist", "in": "query", "schema": {"type": "boolean"}}, {"name": "loCNumberExist", "in": "query", "schema": {"type": "boolean"}}, {"name": "allCompanyDocuments", "in": "query", "schema": {"type": "boolean"}}], "responses": {"400": {"description": "Bad Request", "content": {"text/plain": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BlueTape.Company.API.Models.Errors.ErrorResponse"}}}, "application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BlueTape.Company.API.Models.Errors.ErrorResponse"}}}, "text/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BlueTape.Company.API.Models.Errors.ErrorResponse"}}}}}, "401": {"description": "Unauthorized", "content": {"text/plain": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BlueTape.Company.API.Models.Errors.ErrorResponse"}}}, "application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BlueTape.Company.API.Models.Errors.ErrorResponse"}}}, "text/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BlueTape.Company.API.Models.Errors.ErrorResponse"}}}}}, "404": {"description": "Not Found"}, "500": {"description": "Server Error", "content": {"text/plain": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BlueTape.Company.API.Models.Errors.ErrorResponse"}}}, "application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BlueTape.Company.API.Models.Errors.ErrorResponse"}}}, "text/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BlueTape.Company.API.Models.Errors.ErrorResponse"}}}}}, "200": {"description": "Success", "content": {"text/plain": {"schema": {"type": "object", "additionalProperties": {"type": "array", "items": {"$ref": "#/components/schemas/BlueTape.Company.API.Models.Documents.Responses.DocumentLocReportResponse"}}}}, "application/json": {"schema": {"type": "object", "additionalProperties": {"type": "array", "items": {"$ref": "#/components/schemas/BlueTape.Company.API.Models.Documents.Responses.DocumentLocReportResponse"}}}}, "text/json": {"schema": {"type": "object", "additionalProperties": {"type": "array", "items": {"$ref": "#/components/schemas/BlueTape.Company.API.Models.Documents.Responses.DocumentLocReportResponse"}}}}}}}}}, "/migration/company/{id}": {"post": {"tags": ["Migration"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"401": {"description": "Unauthorized", "content": {"text/plain": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BlueTape.Company.API.Models.Errors.ErrorResponse"}}}, "application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BlueTape.Company.API.Models.Errors.ErrorResponse"}}}, "text/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BlueTape.Company.API.Models.Errors.ErrorResponse"}}}}}, "500": {"description": "Server Error", "content": {"text/plain": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BlueTape.Company.API.Models.Errors.ErrorResponse"}}}, "application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BlueTape.Company.API.Models.Errors.ErrorResponse"}}}, "text/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BlueTape.Company.API.Models.Errors.ErrorResponse"}}}}}, "200": {"description": "Success"}}}}, "/documents/templates": {"get": {"tags": ["Template"], "parameters": [{"name": "code", "in": "query", "schema": {"$ref": "#/components/schemas/BlueTape.CompanyService.Common.Enums.TemplateType"}}], "responses": {"400": {"description": "Bad Request", "content": {"text/plain": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BlueTape.Company.API.Models.Errors.ErrorResponse"}}}, "application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BlueTape.Company.API.Models.Errors.ErrorResponse"}}}, "text/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BlueTape.Company.API.Models.Errors.ErrorResponse"}}}}}, "401": {"description": "Unauthorized", "content": {"text/plain": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BlueTape.Company.API.Models.Errors.ErrorResponse"}}}, "application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BlueTape.Company.API.Models.Errors.ErrorResponse"}}}, "text/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BlueTape.Company.API.Models.Errors.ErrorResponse"}}}}}, "404": {"description": "Not Found"}, "500": {"description": "Server Error", "content": {"text/plain": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BlueTape.Company.API.Models.Errors.ErrorResponse"}}}, "application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BlueTape.Company.API.Models.Errors.ErrorResponse"}}}, "text/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BlueTape.Company.API.Models.Errors.ErrorResponse"}}}}}, "200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/BlueTape.CompanyService.Documents.Responses.TemplateResponse"}}, "application/json": {"schema": {"$ref": "#/components/schemas/BlueTape.CompanyService.Documents.Responses.TemplateResponse"}}, "text/json": {"schema": {"$ref": "#/components/schemas/BlueTape.CompanyService.Documents.Responses.TemplateResponse"}}}}}}, "post": {"tags": ["Template"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/BlueTape.Company.API.Models.Documents.Requests.CreateTemplateRequest"}}, "text/json": {"schema": {"$ref": "#/components/schemas/BlueTape.Company.API.Models.Documents.Requests.CreateTemplateRequest"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/BlueTape.Company.API.Models.Documents.Requests.CreateTemplateRequest"}}}}, "responses": {"400": {"description": "Bad Request", "content": {"text/plain": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BlueTape.Company.API.Models.Errors.ErrorResponse"}}}, "application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BlueTape.Company.API.Models.Errors.ErrorResponse"}}}, "text/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BlueTape.Company.API.Models.Errors.ErrorResponse"}}}}}, "401": {"description": "Unauthorized", "content": {"text/plain": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BlueTape.Company.API.Models.Errors.ErrorResponse"}}}, "application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BlueTape.Company.API.Models.Errors.ErrorResponse"}}}, "text/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BlueTape.Company.API.Models.Errors.ErrorResponse"}}}}}, "404": {"description": "Not Found"}, "500": {"description": "Server Error", "content": {"text/plain": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BlueTape.Company.API.Models.Errors.ErrorResponse"}}}, "application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BlueTape.Company.API.Models.Errors.ErrorResponse"}}}, "text/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BlueTape.Company.API.Models.Errors.ErrorResponse"}}}}}, "201": {"description": "Created", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/BlueTape.CompanyService.Documents.Responses.TemplateResponse"}}, "application/json": {"schema": {"$ref": "#/components/schemas/BlueTape.CompanyService.Documents.Responses.TemplateResponse"}}, "text/json": {"schema": {"$ref": "#/components/schemas/BlueTape.CompanyService.Documents.Responses.TemplateResponse"}}}}}}}, "/documents/templates/{templateId}/future-file-name": {"put": {"tags": ["Template"], "parameters": [{"name": "templateId", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/BlueTape.Company.API.Models.Documents.Requests.UpdateFutureFileNameTemplateRequest"}}, "text/json": {"schema": {"$ref": "#/components/schemas/BlueTape.Company.API.Models.Documents.Requests.UpdateFutureFileNameTemplateRequest"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/BlueTape.Company.API.Models.Documents.Requests.UpdateFutureFileNameTemplateRequest"}}}}, "responses": {"400": {"description": "Bad Request", "content": {"text/plain": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BlueTape.Company.API.Models.Errors.ErrorResponse"}}}, "application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BlueTape.Company.API.Models.Errors.ErrorResponse"}}}, "text/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BlueTape.Company.API.Models.Errors.ErrorResponse"}}}}}, "401": {"description": "Unauthorized", "content": {"text/plain": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BlueTape.Company.API.Models.Errors.ErrorResponse"}}}, "application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BlueTape.Company.API.Models.Errors.ErrorResponse"}}}, "text/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BlueTape.Company.API.Models.Errors.ErrorResponse"}}}}}, "404": {"description": "Not Found"}, "500": {"description": "Server Error", "content": {"text/plain": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BlueTape.Company.API.Models.Errors.ErrorResponse"}}}, "application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BlueTape.Company.API.Models.Errors.ErrorResponse"}}}, "text/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BlueTape.Company.API.Models.Errors.ErrorResponse"}}}}}, "201": {"description": "Created", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/BlueTape.CompanyService.Documents.Responses.TemplateResponse"}}, "application/json": {"schema": {"$ref": "#/components/schemas/BlueTape.CompanyService.Documents.Responses.TemplateResponse"}}, "text/json": {"schema": {"$ref": "#/components/schemas/BlueTape.CompanyService.Documents.Responses.TemplateResponse"}}}}}}}, "/documents/templates/{templateId}/update-file-path": {"put": {"tags": ["Template"], "parameters": [{"name": "templateId", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/BlueTape.Company.API.Models.Documents.Requests.UpdateFutureFileNameTemplateRequest"}}, "text/json": {"schema": {"$ref": "#/components/schemas/BlueTape.Company.API.Models.Documents.Requests.UpdateFutureFileNameTemplateRequest"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/BlueTape.Company.API.Models.Documents.Requests.UpdateFutureFileNameTemplateRequest"}}}}, "responses": {"400": {"description": "Bad Request", "content": {"text/plain": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BlueTape.Company.API.Models.Errors.ErrorResponse"}}}, "application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BlueTape.Company.API.Models.Errors.ErrorResponse"}}}, "text/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BlueTape.Company.API.Models.Errors.ErrorResponse"}}}}}, "401": {"description": "Unauthorized", "content": {"text/plain": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BlueTape.Company.API.Models.Errors.ErrorResponse"}}}, "application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BlueTape.Company.API.Models.Errors.ErrorResponse"}}}, "text/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BlueTape.Company.API.Models.Errors.ErrorResponse"}}}}}, "404": {"description": "Not Found"}, "500": {"description": "Server Error", "content": {"text/plain": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BlueTape.Company.API.Models.Errors.ErrorResponse"}}}, "application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BlueTape.Company.API.Models.Errors.ErrorResponse"}}}, "text/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BlueTape.Company.API.Models.Errors.ErrorResponse"}}}}}, "201": {"description": "Created", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/BlueTape.CompanyService.Documents.Responses.TemplateResponse"}}, "application/json": {"schema": {"$ref": "#/components/schemas/BlueTape.CompanyService.Documents.Responses.TemplateResponse"}}, "text/json": {"schema": {"$ref": "#/components/schemas/BlueTape.CompanyService.Documents.Responses.TemplateResponse"}}}}}}}, "/documents/templates/allversions": {"get": {"tags": ["Template"], "parameters": [{"name": "code", "in": "query", "schema": {"$ref": "#/components/schemas/BlueTape.CompanyService.Common.Enums.TemplateType"}}], "responses": {"400": {"description": "Bad Request", "content": {"text/plain": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BlueTape.Company.API.Models.Errors.ErrorResponse"}}}, "application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BlueTape.Company.API.Models.Errors.ErrorResponse"}}}, "text/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BlueTape.Company.API.Models.Errors.ErrorResponse"}}}}}, "401": {"description": "Unauthorized", "content": {"text/plain": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BlueTape.Company.API.Models.Errors.ErrorResponse"}}}, "application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BlueTape.Company.API.Models.Errors.ErrorResponse"}}}, "text/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BlueTape.Company.API.Models.Errors.ErrorResponse"}}}}}, "404": {"description": "Not Found"}, "500": {"description": "Server Error", "content": {"text/plain": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BlueTape.Company.API.Models.Errors.ErrorResponse"}}}, "application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BlueTape.Company.API.Models.Errors.ErrorResponse"}}}, "text/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BlueTape.Company.API.Models.Errors.ErrorResponse"}}}}}, "200": {"description": "Success", "content": {"text/plain": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BlueTape.CompanyService.Documents.Responses.TemplateResponse"}}}, "application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BlueTape.CompanyService.Documents.Responses.TemplateResponse"}}}, "text/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BlueTape.CompanyService.Documents.Responses.TemplateResponse"}}}}}}}}, "/documents/templates/{id}": {"get": {"tags": ["Template"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid"}}], "responses": {"400": {"description": "Bad Request", "content": {"text/plain": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BlueTape.Company.API.Models.Errors.ErrorResponse"}}}, "application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BlueTape.Company.API.Models.Errors.ErrorResponse"}}}, "text/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BlueTape.Company.API.Models.Errors.ErrorResponse"}}}}}, "401": {"description": "Unauthorized", "content": {"text/plain": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BlueTape.Company.API.Models.Errors.ErrorResponse"}}}, "application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BlueTape.Company.API.Models.Errors.ErrorResponse"}}}, "text/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BlueTape.Company.API.Models.Errors.ErrorResponse"}}}}}, "404": {"description": "Not Found"}, "500": {"description": "Server Error", "content": {"text/plain": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BlueTape.Company.API.Models.Errors.ErrorResponse"}}}, "application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BlueTape.Company.API.Models.Errors.ErrorResponse"}}}, "text/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BlueTape.Company.API.Models.Errors.ErrorResponse"}}}}}, "200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/BlueTape.CompanyService.Documents.Responses.TemplateResponse"}}, "application/json": {"schema": {"$ref": "#/components/schemas/BlueTape.CompanyService.Documents.Responses.TemplateResponse"}}, "text/json": {"schema": {"$ref": "#/components/schemas/BlueTape.CompanyService.Documents.Responses.TemplateResponse"}}}}}}}}, "components": {"schemas": {"BlueTape.Common.Enums.CashFlowGrouping": {"enum": ["None", "Daily", "Monthly"], "type": "string"}, "BlueTape.Common.Enums.PlaidBankAccountStatus": {"enum": ["<PERSON><PERSON><PERSON>", "Active", "Expired", "Expiring", "Disconnected"], "type": "string"}, "BlueTape.Company.API.Models.CashFlow.AddCashFlowTransactions": {"type": "object", "properties": {"transactionId": {"type": "string", "nullable": true}, "transactionDate": {"type": "string", "format": "date-time"}, "amount": {"type": "number", "format": "double"}, "balance": {"type": "number", "format": "double"}}, "additionalProperties": false}, "BlueTape.Company.API.Models.CashFlow.AddManualCashFlow": {"type": "object", "properties": {"accountId": {"type": "string", "nullable": true}, "transactions": {"type": "array", "items": {"$ref": "#/components/schemas/BlueTape.Company.API.Models.CashFlow.AddCashFlowTransactions"}, "nullable": true}}, "additionalProperties": false}, "BlueTape.Company.API.Models.CashFlow.AddPlaidCashFlow": {"type": "object", "properties": {"accountId": {"type": "string", "nullable": true}, "plaidAssetReport": {"$ref": "#/components/schemas/BlueTape.Company.API.Models.CashFlow.AssetReportViewModels.PlaidAssetReportViewModel"}}, "additionalProperties": false}, "BlueTape.Company.API.Models.CashFlow.AssetReportViewModels.AccountViewModel": {"type": "object", "properties": {"account_id": {"type": "string", "nullable": true}, "balances": {"$ref": "#/components/schemas/BlueTape.Company.API.Models.CashFlow.AssetReportViewModels.BalanceViewModel"}, "days_available": {"type": "integer", "format": "int32"}, "historical_balances": {"type": "array", "items": {"$ref": "#/components/schemas/BlueTape.Company.API.Models.CashFlow.AssetReportViewModels.HistoricalBalancesViewModel"}, "nullable": true}, "mask": {"type": "string", "nullable": true}, "name": {"type": "string", "nullable": true}, "official_name": {"type": "string", "nullable": true}, "owners": {"type": "array", "items": {"$ref": "#/components/schemas/BlueTape.Company.API.Models.CashFlow.AssetReportViewModels.OwnersViewModel"}, "nullable": true}, "ownership_type": {"type": "string", "nullable": true}, "subtype": {"type": "string", "nullable": true}, "transactions": {"type": "array", "items": {"$ref": "#/components/schemas/BlueTape.Company.API.Models.CashFlow.AssetReportViewModels.TransactionViewModel"}, "nullable": true}, "type": {"type": "string", "nullable": true}}, "additionalProperties": false}, "BlueTape.Company.API.Models.CashFlow.AssetReportViewModels.AddressesViewModel": {"type": "object", "properties": {"data": {"$ref": "#/components/schemas/BlueTape.Company.API.Models.CashFlow.AssetReportViewModels.DataViewModel"}, "primary": {"type": "boolean"}}, "additionalProperties": false}, "BlueTape.Company.API.Models.CashFlow.AssetReportViewModels.AssetReportViewModel": {"type": "object", "properties": {"asset_report_id": {"type": "string", "nullable": true}, "client_report_id": {"type": "string", "nullable": true}, "date_generated": {"type": "string", "format": "date-time"}, "days_requested": {"type": "integer", "format": "int32"}, "items": {"type": "array", "items": {"$ref": "#/components/schemas/BlueTape.Company.API.Models.CashFlow.AssetReportViewModels.ItemsViewModel"}, "nullable": true}, "user": {"$ref": "#/components/schemas/BlueTape.Company.API.Models.CashFlow.AssetReportViewModels.UserViewModel"}}, "additionalProperties": false}, "BlueTape.Company.API.Models.CashFlow.AssetReportViewModels.BalanceViewModel": {"type": "object", "properties": {"available": {"type": "number", "format": "double", "nullable": true}, "current": {"type": "number", "format": "double", "nullable": true}, "iso_currency_code": {"type": "string", "nullable": true}, "limit": {"type": "number", "format": "double", "nullable": true}, "unofficial_currency_code": {"type": "string", "nullable": true}}, "additionalProperties": false}, "BlueTape.Company.API.Models.CashFlow.AssetReportViewModels.DataViewModel": {"type": "object", "properties": {"city": {"type": "string", "nullable": true}, "country": {"type": "string", "nullable": true}, "region": {"type": "string", "nullable": true}, "street": {"type": "string", "nullable": true}, "postal_code": {"type": "string", "nullable": true}}, "additionalProperties": false}, "BlueTape.Company.API.Models.CashFlow.AssetReportViewModels.EmailsViewModel": {"type": "object", "properties": {"data": {"type": "string", "nullable": true}, "primary": {"type": "boolean"}, "type": {"type": "string", "nullable": true}}, "additionalProperties": false}, "BlueTape.Company.API.Models.CashFlow.AssetReportViewModels.HistoricalBalancesViewModel": {"type": "object", "properties": {"current": {"type": "number", "format": "double"}, "date": {"type": "string", "format": "date-time"}, "iso_currency_code": {"type": "string", "nullable": true}, "unofficial_currency_code": {"type": "string", "nullable": true}}, "additionalProperties": false}, "BlueTape.Company.API.Models.CashFlow.AssetReportViewModels.ItemsViewModel": {"type": "object", "properties": {"accounts": {"type": "array", "items": {"$ref": "#/components/schemas/BlueTape.Company.API.Models.CashFlow.AssetReportViewModels.AccountViewModel"}, "nullable": true}, "date_last_updated": {"type": "string", "format": "date-time"}, "institution_id": {"type": "string", "nullable": true}, "institution_name": {"type": "string", "nullable": true}, "item_id": {"type": "string", "nullable": true}}, "additionalProperties": false}, "BlueTape.Company.API.Models.CashFlow.AssetReportViewModels.OwnersViewModel": {"type": "object", "properties": {"addresses": {"type": "array", "items": {"$ref": "#/components/schemas/BlueTape.Company.API.Models.CashFlow.AssetReportViewModels.AddressesViewModel"}, "nullable": true}, "emails": {"type": "array", "items": {"$ref": "#/components/schemas/BlueTape.Company.API.Models.CashFlow.AssetReportViewModels.EmailsViewModel"}, "nullable": true}, "names": {"type": "array", "items": {"type": "string"}, "nullable": true}, "phone_numbers": {"type": "array", "items": {"$ref": "#/components/schemas/BlueTape.Company.API.Models.CashFlow.AssetReportViewModels.PhoneNumbersViewModel"}, "nullable": true}}, "additionalProperties": false}, "BlueTape.Company.API.Models.CashFlow.AssetReportViewModels.PhoneNumbersViewModel": {"type": "object", "properties": {"data": {"type": "string", "nullable": true}, "primary": {"type": "boolean"}, "type": {"type": "string", "nullable": true}}, "additionalProperties": false}, "BlueTape.Company.API.Models.CashFlow.AssetReportViewModels.PlaidAssetReportViewModel": {"type": "object", "properties": {"report": {"$ref": "#/components/schemas/BlueTape.Company.API.Models.CashFlow.AssetReportViewModels.AssetReportViewModel"}, "request_id": {"type": "string", "nullable": true}, "warnings": {"type": "array", "items": {"type": "string"}, "nullable": true}}, "additionalProperties": false}, "BlueTape.Company.API.Models.CashFlow.AssetReportViewModels.TransactionViewModel": {"type": "object", "properties": {"account_id": {"type": "string", "nullable": true}, "amount": {"type": "number", "format": "double"}, "date": {"type": "string", "format": "date-time"}, "iso_currency_code": {"type": "string", "nullable": true}, "original_description": {"type": "string", "nullable": true}, "pending": {"type": "boolean"}, "transaction_id": {"type": "string", "nullable": true}, "unofficial_currency_code": {"type": "string", "nullable": true}}, "additionalProperties": false}, "BlueTape.Company.API.Models.CashFlow.AssetReportViewModels.UserViewModel": {"type": "object", "properties": {"client_user_id": {"type": "string", "nullable": true}, "email": {"type": "string", "nullable": true}, "first_name": {"type": "string", "nullable": true}, "last_name": {"type": "string", "nullable": true}, "middle_name": {"type": "string", "nullable": true}, "phone_number": {"type": "string", "nullable": true}, "ssn": {"type": "string", "nullable": true}}, "additionalProperties": false}, "BlueTape.Company.API.Models.CashFlow.Responses.AddPlaidCashFlowResponse": {"type": "object", "properties": {"result": {"type": "boolean"}, "daysAvailableFrom": {"type": "string", "nullable": true}, "daysAvailableTo": {"type": "string", "nullable": true}}, "additionalProperties": false}, "BlueTape.Company.API.Models.CashFlow.Responses.CashFlowAggregatedResponse": {"type": "object", "properties": {"sixMonthAverageBankBalance": {"type": "number", "format": "double"}, "averageMonthlyCashFlow": {"type": "number", "format": "double"}}, "additionalProperties": false}, "BlueTape.Company.API.Models.CashFlow.Responses.CashFlowItemResponse": {"type": "object", "properties": {"accountId": {"type": "string", "nullable": true}, "assetReportId": {"type": "string", "nullable": true}, "date": {"type": "string", "format": "date"}, "debit": {"type": "number", "format": "double"}, "credit": {"type": "number", "format": "double"}, "balance": {"type": "number", "format": "double"}, "cashFlowResult": {"type": "number", "format": "double"}}, "additionalProperties": false}, "BlueTape.Company.API.Models.CashFlow.Responses.CashFlowResponse": {"type": "object", "properties": {"updatedAt": {"type": "string", "format": "date-time", "nullable": true}, "cashFlowItems": {"type": "array", "items": {"$ref": "#/components/schemas/BlueTape.Company.API.Models.CashFlow.Responses.CashFlowItemResponse"}, "nullable": true}}, "additionalProperties": false}, "BlueTape.Company.API.Models.CashFlow.Responses.CashFlowRowValidationResponse": {"type": "object", "properties": {"row": {"type": "string", "nullable": true}, "rowNumber": {"type": "integer", "format": "int32"}}, "additionalProperties": false}, "BlueTape.Company.API.Models.CashFlow.Responses.ManualCashFlowValidationResultResponse": {"type": "object", "properties": {"isValid": {"type": "boolean"}, "invalidRows": {"type": "array", "items": {"$ref": "#/components/schemas/BlueTape.Company.API.Models.CashFlow.Responses.CashFlowRowValidationResponse"}, "nullable": true}}, "additionalProperties": false}, "BlueTape.Company.API.Models.Documents.Requests.AuthorizeApprovalRequest": {"type": "object", "properties": {"token": {"type": "string", "nullable": true}, "ipAddress": {"type": "string", "nullable": true}}, "additionalProperties": false}, "BlueTape.Company.API.Models.Documents.Requests.CreateDocumentApprovalRequest": {"type": "object", "properties": {"ownerIdentifier": {"type": "string", "nullable": true}, "emailAddress": {"type": "string", "nullable": true}, "userId": {"type": "string", "nullable": true}, "firstName": {"type": "string", "nullable": true}, "lastName": {"type": "string", "nullable": true}, "ipAddress": {"type": "string", "nullable": true}}, "additionalProperties": false}, "BlueTape.Company.API.Models.Documents.Requests.CreateTemplateRequest": {"type": "object", "properties": {"code": {"$ref": "#/components/schemas/BlueTape.CompanyService.Common.Enums.TemplateType"}, "filePath": {"type": "string", "nullable": true}, "semanticVersion": {"type": "string", "nullable": true}, "validFrom": {"type": "string", "format": "date-time"}, "futureFileName": {"type": "string", "nullable": true}}, "additionalProperties": false}, "BlueTape.Company.API.Models.Documents.Requests.DocumentLoanApplicationLegacyRequest": {"type": "object", "properties": {"s3Url": {"type": "string", "nullable": true}}, "additionalProperties": false}, "BlueTape.Company.API.Models.Documents.Requests.DocumentLoanApplicationRequest": {"type": "object", "properties": {"documentTemplateId": {"type": "string", "format": "uuid"}, "s3Url": {"type": "string", "nullable": true}}, "additionalProperties": false}, "BlueTape.Company.API.Models.Documents.Requests.UpdateFutureFileNameTemplateRequest": {"type": "object", "properties": {"futureFileName": {"type": "string", "nullable": true}}, "additionalProperties": false}, "BlueTape.Company.API.Models.Documents.Responses.DocumentLocReportResponse": {"type": "object", "properties": {"documentId": {"type": "string", "format": "uuid"}, "companyId": {"type": "string", "nullable": true}, "s3Url": {"type": "string", "nullable": true}, "loanExist": {"type": "boolean"}, "loanApproved": {"type": "boolean"}, "loCNumberExist": {"type": "boolean"}, "templateValidFrom": {"type": "string", "format": "date-time"}}, "additionalProperties": false}, "BlueTape.Company.API.Models.Documents.Responses.DocumentResponse": {"type": "object", "properties": {"id": {"type": "string", "format": "uuid"}, "createdAt": {"type": "string", "format": "date-time"}, "s3Url": {"type": "string", "nullable": true}, "template": {"$ref": "#/components/schemas/BlueTape.CompanyService.Documents.Responses.TemplateResponse"}, "documentApprovals": {"type": "array", "items": {"$ref": "#/components/schemas/BlueTape.CompanyService.Documents.Responses.DocumentApprovalResponse"}, "nullable": true}}, "additionalProperties": false}, "BlueTape.Company.API.Models.Errors.ErrorResponse": {"type": "object", "properties": {"errorType": {"$ref": "#/components/schemas/BlueTape.Company.API.Models.Errors.ErrorType"}, "code": {"type": "string", "nullable": true}, "reason": {"type": "string", "nullable": true}}, "additionalProperties": false}, "BlueTape.Company.API.Models.Errors.ErrorType": {"enum": ["<PERSON><PERSON><PERSON>", "ValidationError", "BusinessLogicError", "InternalError"], "type": "string"}, "BlueTape.Company.API.Models.NoteModel": {"type": "object", "properties": {"note": {"type": "string", "nullable": true}}, "additionalProperties": false}, "BlueTape.Company.API.Models.PaginatedList`1[BlueTape.Company.API.Models.CashFlow.Responses.CashFlowItemResponse]": {"type": "object", "properties": {"result": {"type": "array", "items": {"$ref": "#/components/schemas/BlueTape.Company.API.Models.CashFlow.Responses.CashFlowItemResponse"}, "nullable": true}, "totalCount": {"type": "integer", "format": "int64"}, "pageNumber": {"type": "integer", "format": "int64"}, "pagesCount": {"type": "integer", "format": "int64"}}, "additionalProperties": false}, "BlueTape.Company.API.Models.ResultWithPaginationModel`1[BlueTape.CompanyService.GuestSuppliers.GuestSupplierCustomerModel]": {"type": "object", "properties": {"pageNumber": {"type": "integer", "format": "int64"}, "pagesCount": {"type": "integer", "format": "int64"}, "totalCount": {"type": "integer", "format": "int64"}, "result": {"type": "array", "items": {"$ref": "#/components/schemas/BlueTape.CompanyService.GuestSuppliers.GuestSupplierCustomerModel"}, "nullable": true}}, "additionalProperties": false}, "BlueTape.Company.API.Models.ResultWithPaginationModel`1[BlueTape.CompanyService.GuestSuppliers.GuestSupplierInvoiceModel]": {"type": "object", "properties": {"pageNumber": {"type": "integer", "format": "int64"}, "pagesCount": {"type": "integer", "format": "int64"}, "totalCount": {"type": "integer", "format": "int64"}, "result": {"type": "array", "items": {"$ref": "#/components/schemas/BlueTape.CompanyService.GuestSuppliers.GuestSupplierInvoiceModel"}, "nullable": true}}, "additionalProperties": false}, "BlueTape.Company.API.Models.ResultWithPaginationModel`1[BlueTape.CompanyService.GuestSuppliers.GuestSupplierModel]": {"type": "object", "properties": {"pageNumber": {"type": "integer", "format": "int64"}, "pagesCount": {"type": "integer", "format": "int64"}, "totalCount": {"type": "integer", "format": "int64"}, "result": {"type": "array", "items": {"$ref": "#/components/schemas/BlueTape.CompanyService.GuestSuppliers.GuestSupplierModel"}, "nullable": true}}, "additionalProperties": false}, "BlueTape.Company.API.Models.UpdateManualAccountStatusModel": {"type": "object", "properties": {"newAccountStatus": {"$ref": "#/components/schemas/BlueTape.CompanyService.Common.Enums.AccountStatusEnum"}, "note": {"type": "string", "nullable": true}}, "additionalProperties": false}, "BlueTape.Company.Application.Models.AccountStatus.ChangeAccountStatusManuallyModel": {"type": "object", "properties": {"accountStatus": {"$ref": "#/components/schemas/BlueTape.CompanyService.Common.Enums.AccountStatusEnum"}, "statusEvent": {"type": "string", "nullable": true}, "note": {"type": "string", "nullable": true}}, "additionalProperties": false}, "BlueTape.Company.Domain.DTOs.Companies.UserDto": {"type": "object", "properties": {"id": {"type": "string", "nullable": true}, "createdAt": {"type": "string", "format": "date-time", "nullable": true}, "updatedAt": {"type": "string", "format": "date-time", "nullable": true}, "sub": {"type": "string", "nullable": true}, "firstName": {"type": "string", "nullable": true}, "lastName": {"type": "string", "nullable": true}, "contactName": {"type": "string", "nullable": true}, "phone": {"type": "string", "nullable": true}, "email": {"type": "string", "nullable": true}, "login": {"type": "string", "nullable": true}}, "additionalProperties": false}, "BlueTape.CompanyService.BankAccounts.AccessTokenPlaidModel": {"type": "object", "properties": {"display": {"type": "string", "nullable": true}, "cipher": {"type": "string", "nullable": true}, "hash": {"type": "string", "nullable": true}}, "additionalProperties": false}, "BlueTape.CompanyService.BankAccounts.AccountNumberModel": {"type": "object", "properties": {"display": {"type": "string", "nullable": true}, "cipher": {"type": "string", "nullable": true}, "hash": {"type": "string", "nullable": true}}, "additionalProperties": false}, "BlueTape.CompanyService.BankAccounts.BankAccountAionSettingsModel": {"type": "object", "properties": {"blueTapeAccountId": {"type": "string", "nullable": true}, "transferMethodId": {"type": "string", "nullable": true}}, "additionalProperties": false}, "BlueTape.CompanyService.BankAccounts.BankAccountIdentityModel": {"type": "object", "properties": {"postalCode": {"type": "string", "nullable": true}, "city": {"type": "string", "nullable": true}, "countryCode": {"type": "string", "nullable": true}, "countrySubDivisionCode": {"type": "string", "nullable": true}, "addressLine1": {"type": "string", "nullable": true}, "addressLine2": {"type": "string", "nullable": true}}, "additionalProperties": false}, "BlueTape.CompanyService.BankAccounts.BankAccountModel": {"type": "object", "properties": {"id": {"type": "string", "nullable": true}, "accountHolderName": {"type": "string", "nullable": true}, "accountName": {"type": "string", "nullable": true}, "accountType": {"type": "string", "nullable": true}, "finicityHistorySyncDone": {"type": "boolean", "nullable": true}, "isDeactivated": {"type": "boolean", "nullable": true}, "isManualEntry": {"type": "boolean", "nullable": true}, "isPrimary": {"type": "boolean", "nullable": true}, "isPrimaryForCredit": {"type": "boolean", "nullable": true}, "isPrimaryForIHCAutoPay": {"type": "boolean", "nullable": true}, "name": {"type": "string", "nullable": true}, "paymentMethodType": {"type": "string", "nullable": true}, "routingNumber": {"type": "string", "nullable": true}, "status": {"type": "string", "nullable": true}, "thirdPartyId": {"type": "string", "nullable": true}, "voidedCheck": {"type": "string", "nullable": true}, "includeInCashFlow": {"type": "boolean"}, "accountNumber": {"$ref": "#/components/schemas/BlueTape.CompanyService.BankAccounts.AccountNumberModel"}, "billingAddress": {"$ref": "#/components/schemas/BlueTape.CompanyService.BankAccounts.BillingAddressModel"}, "plaid": {"$ref": "#/components/schemas/BlueTape.CompanyService.BankAccounts.BankAccountPlaidModel"}, "giact": {"$ref": "#/components/schemas/BlueTape.CompanyService.BankAccounts.Giact.BankAccountGiactModel"}, "aionSettings": {"$ref": "#/components/schemas/BlueTape.CompanyService.BankAccounts.BankAccountAionSettingsModel"}, "createdBy": {"type": "string", "nullable": true}, "network": {"type": "string", "nullable": true}, "type": {"type": "string", "nullable": true}, "response": {"nullable": true}, "isRegulated": {"type": "boolean", "nullable": true}, "identity": {"$ref": "#/components/schemas/BlueTape.CompanyService.BankAccounts.BankAccountIdentityModel"}}, "additionalProperties": false}, "BlueTape.CompanyService.BankAccounts.BankAccountPlaidModel": {"type": "object", "properties": {"itemId": {"type": "string", "nullable": true}, "accountId": {"type": "string", "nullable": true}, "accessToken": {"$ref": "#/components/schemas/BlueTape.CompanyService.BankAccounts.AccessTokenPlaidModel"}, "institutionId": {"type": "string", "nullable": true}, "includeInCashFlow": {"type": "boolean"}, "status": {"type": "string", "nullable": true}}, "additionalProperties": false}, "BlueTape.CompanyService.BankAccounts.BillingAddressModel": {"type": "object", "properties": {"addressLine1": {"type": "string", "nullable": true}, "addressLine2": {"type": "string", "nullable": true}, "city": {"type": "string", "nullable": true}, "stateCode": {"type": "string", "nullable": true}, "zipCode": {"type": "string", "nullable": true}}, "additionalProperties": false}, "BlueTape.CompanyService.BankAccounts.CreateBankAccountModel": {"type": "object", "properties": {"accountHolderName": {"type": "string", "nullable": true}, "accountName": {"type": "string", "nullable": true}, "accountType": {"type": "string", "nullable": true}, "finicityHistorySyncDone": {"type": "boolean", "nullable": true}, "isDeactivated": {"type": "boolean", "nullable": true}, "isManualEntry": {"type": "boolean", "nullable": true}, "isPrimary": {"type": "boolean", "nullable": true}, "isPrimaryForCredit": {"type": "boolean", "nullable": true}, "isPrimaryForIHCAutoPay": {"type": "boolean", "nullable": true}, "name": {"type": "string", "nullable": true}, "paymentMethodType": {"type": "string", "nullable": true}, "routingNumber": {"type": "string", "nullable": true}, "status": {"type": "string", "nullable": true}, "thirdPartyId": {"type": "string", "nullable": true}, "voidedCheck": {"type": "string", "nullable": true}, "accountNumberCipher": {"type": "string", "nullable": true}, "billingAddress": {"$ref": "#/components/schemas/BlueTape.CompanyService.BankAccounts.BillingAddressModel"}, "plaid": {"$ref": "#/components/schemas/BlueTape.CompanyService.BankAccounts.BankAccountPlaidModel"}, "includeInCashFlow": {"type": "boolean", "nullable": true}, "giact": {"$ref": "#/components/schemas/BlueTape.CompanyService.BankAccounts.Giact.BankAccountGiactModel"}, "isRegulated": {"type": "boolean", "nullable": true}, "identity": {"$ref": "#/components/schemas/BlueTape.CompanyService.BankAccounts.BankAccountIdentityModel"}}, "additionalProperties": false}, "BlueTape.CompanyService.BankAccounts.Giact.BankAccountGiactModel": {"type": "object", "properties": {"status": {"type": "string", "nullable": true}, "lastUpdated": {"type": "string", "format": "date-time"}, "accounts": {"type": "array", "items": {"$ref": "#/components/schemas/BlueTape.CompanyService.BankAccounts.Giact.GiactAccountModel"}, "nullable": true}}, "additionalProperties": false}, "BlueTape.CompanyService.BankAccounts.Giact.GiactAccountModel": {"type": "object", "properties": {"id": {"type": "string", "nullable": true}, "accountNumberDisplay": {"type": "string", "nullable": true}, "byBusinessName": {"type": "boolean"}, "response": {"$ref": "#/components/schemas/BlueTape.CompanyService.BankAccounts.Giact.GiactResponseModel"}, "error": {"type": "array", "items": {"type": "string"}, "nullable": true}}, "additionalProperties": false}, "BlueTape.CompanyService.BankAccounts.Giact.GiactAuthenticationModel": {"type": "object", "properties": {"code": {"type": "string", "nullable": true}, "description": {"type": "string", "nullable": true}}, "additionalProperties": false}, "BlueTape.CompanyService.BankAccounts.Giact.GiactResponseModel": {"type": "object", "properties": {"responseId": {"type": "string", "nullable": true}, "verificationResult": {"type": "string", "nullable": true}, "accountVerification": {"$ref": "#/components/schemas/BlueTape.CompanyService.BankAccounts.Giact.GiactVerificationModel"}, "accountAuthentication": {"$ref": "#/components/schemas/BlueTape.CompanyService.BankAccounts.Giact.GiactAuthenticationModel"}}, "additionalProperties": false}, "BlueTape.CompanyService.BankAccounts.Giact.GiactVerificationModel": {"type": "object", "properties": {"code": {"type": "string", "nullable": true}, "description": {"type": "string", "nullable": true}}, "additionalProperties": false}, "BlueTape.CompanyService.BankAccounts.NoSupplierBankDetailsModel": {"type": "object", "properties": {"routingNumber": {"type": "string", "nullable": true}, "accountNumber": {"$ref": "#/components/schemas/BlueTape.CompanyService.BankAccounts.AccountNumberModel"}, "accountType": {"type": "string", "nullable": true}}, "additionalProperties": false}, "BlueTape.CompanyService.BankAccounts.PatchBankAccountCashFlow": {"type": "object", "properties": {"includeInCashFlow": {"type": "boolean"}}, "additionalProperties": false}, "BlueTape.CompanyService.BankAccounts.PatchBankAccountSettingsModel": {"type": "object", "properties": {"isPrimary": {"type": "boolean", "nullable": true}, "isPrimaryForCredit": {"type": "boolean", "nullable": true}, "isPrimaryForIHCAutoPay": {"type": "boolean", "nullable": true}}, "additionalProperties": false}, "BlueTape.CompanyService.BankAccounts.UpdateBankAccountNumberModel": {"type": "object", "properties": {"bankDetails": {"$ref": "#/components/schemas/BlueTape.CompanyService.BankAccounts.NoSupplierBankDetailsModel"}, "giactVerificationResult": {"$ref": "#/components/schemas/BlueTape.CompanyService.BankAccounts.Giact.GiactResponseModel"}}, "additionalProperties": false}, "BlueTape.CompanyService.CashFlow.AddManualCashFlow": {"type": "object", "properties": {"result": {"type": "boolean"}, "daysAvailableFrom": {"type": "string", "nullable": true}, "daysAvailableTo": {"type": "string", "nullable": true}}, "additionalProperties": false}, "BlueTape.CompanyService.Common.Enums.AccountStatusEnum": {"enum": ["New", "Active", "InCollection", "Inactive", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Closed"], "type": "string"}, "BlueTape.CompanyService.Common.Enums.AutoApproveType": {"enum": ["AllCustomers", "SelectedCustomers"], "type": "string"}, "BlueTape.CompanyService.Common.Enums.CompanyStatusEnum": {"enum": ["New", "Applied", "Approved", "Rejected", "Validated"], "type": "string"}, "BlueTape.CompanyService.Common.Enums.CompanyTypeEnum": {"enum": ["Supplier", "Contractor"], "type": "string"}, "BlueTape.CompanyService.Common.Enums.ContactSource": {"enum": ["UnDefined", "NetSuite", "QuickBooks", "Generic", "ZohoBooks"], "type": "string"}, "BlueTape.CompanyService.Common.Enums.DebtInvestorType": {"enum": ["Arcadia", "Raistone", "Aion"], "type": "string"}, "BlueTape.CompanyService.Common.Enums.LoanPaymentCollection": {"enum": ["Supplier", "<PERSON><PERSON><PERSON>"], "type": "string"}, "BlueTape.CompanyService.Common.Enums.RepaysType": {"enum": ["Supplier", "<PERSON><PERSON><PERSON>"], "type": "string"}, "BlueTape.CompanyService.Common.Enums.TemplateType": {"enum": ["BNPL_AGREEMENT", "MASTER_AGREEMENT", "CUSTOMER_AGREEMENT_FOR_SELLER", "PERSONAL_GUARANTOR_AGREEMENT", "PERSONAL_GUARANTOR_AGREEMENT_ENTITY", "INVOICE_PURCHASER_AGREEMENT", "GUARANTY_SECURED_AGREEMENT"], "type": "string"}, "BlueTape.CompanyService.Common.Functions.AccountStatus.ChangeAccountStatusDetailsModel": {"type": "object", "properties": {"id": {"type": "string", "nullable": true}, "status": {"type": "string", "nullable": true}, "companyId": {"type": "string", "nullable": true}}, "additionalProperties": false}, "BlueTape.CompanyService.Common.Functions.AccountStatus.ChangeAccountStatusModel": {"type": "object", "properties": {"createdBy": {"type": "string", "nullable": true}, "createdAt": {"type": "string", "format": "date-time"}, "eventType": {"type": "string", "nullable": true}, "details": {"$ref": "#/components/schemas/BlueTape.CompanyService.Common.Functions.AccountStatus.ChangeAccountStatusDetailsModel"}}, "additionalProperties": false}, "BlueTape.CompanyService.Companies.AddressModel": {"type": "object", "properties": {"address": {"type": "string", "nullable": true}, "unitNumber": {"type": "string", "nullable": true}, "city": {"type": "string", "nullable": true}, "state": {"type": "string", "nullable": true}, "zip": {"type": "string", "nullable": true}, "phone": {"type": "string", "nullable": true}}, "additionalProperties": false}, "BlueTape.CompanyService.Companies.ArAdvanceModel": {"type": "object", "properties": {"isEnabled": {"type": "boolean", "nullable": true}, "merchantLimit": {"type": "number", "format": "double", "nullable": true}, "defaultCustomerLimit": {"type": "number", "format": "double", "nullable": true}, "defaultFactoringTerm": {"type": "string", "nullable": true}, "defaultSupplierPackage": {"type": "string", "nullable": true}, "defaultDebtInvestor": {"$ref": "#/components/schemas/BlueTape.CompanyService.Common.Enums.DebtInvestorType"}}, "additionalProperties": false}, "BlueTape.CompanyService.Companies.AutomatedDrawApprovalModel": {"type": "object", "properties": {"isEnabled": {"type": "boolean", "nullable": true}, "drawLimit": {"type": "number", "format": "double", "nullable": true}, "creditLimitPercentage": {"type": "number", "format": "double", "nullable": true}, "dailyAmountLimit": {"type": "number", "format": "double", "nullable": true}, "weeklyAmountLimit": {"type": "number", "format": "double", "nullable": true}, "lastUpdatedBy": {"type": "string", "nullable": true}, "lastUpdatedAt": {"type": "string", "format": "date-time", "nullable": true}}, "additionalProperties": false}, "BlueTape.CompanyService.Companies.CompanyAionSettingsModel": {"type": "object", "properties": {"сounterPartyId": {"type": "string", "nullable": true}, "сounterPartyObjectId": {"type": "string", "nullable": true}}, "additionalProperties": false}, "BlueTape.CompanyService.Companies.CompanyCreditModel": {"type": "object", "properties": {"loCnumber": {"type": "string", "nullable": true}, "limit": {"type": "number", "format": "double", "nullable": true}, "purchaseType": {"type": "string", "nullable": true}}, "additionalProperties": false}, "BlueTape.CompanyService.Companies.CompanyInHouseCreditModel": {"type": "object", "properties": {"isAutoPayRequired": {"type": "boolean", "nullable": true}, "isAutoPayEnabledByCompanyUser": {"type": "boolean", "nullable": true}}, "additionalProperties": false}, "BlueTape.CompanyService.Companies.CompanyModel": {"type": "object", "properties": {"type": {"$ref": "#/components/schemas/BlueTape.CompanyService.Common.Enums.CompanyTypeEnum"}, "status": {"$ref": "#/components/schemas/BlueTape.CompanyService.Common.Enums.CompanyStatusEnum"}, "bankAccounts": {"type": "array", "items": {"type": "string"}, "nullable": true}, "id": {"type": "string", "nullable": true}, "createdAt": {"type": "string", "format": "date-time"}, "updatedAt": {"type": "string", "format": "date-time"}, "name": {"type": "string", "nullable": true}, "entity": {"type": "string", "nullable": true}, "legalName": {"type": "string", "nullable": true}, "contactName": {"type": "string", "nullable": true}, "website": {"type": "string", "nullable": true}, "address": {"$ref": "#/components/schemas/BlueTape.CompanyService.Companies.AddressModel"}, "phone": {"type": "string", "nullable": true}, "email": {"type": "string", "nullable": true}, "isGuest": {"type": "boolean", "nullable": true}, "isBusiness": {"type": "boolean", "nullable": true}, "settings": {"$ref": "#/components/schemas/BlueTape.CompanyService.Companies.CompanySettingsModel"}, "credit": {"$ref": "#/components/schemas/BlueTape.CompanyService.Companies.CompanyCreditModel"}, "merchantAutomaticPullAllowed": {"type": "boolean"}, "aionSettings": {"$ref": "#/components/schemas/BlueTape.CompanyService.Companies.CompanyAionSettingsModel"}, "owner": {"$ref": "#/components/schemas/BlueTape.CompanyService.Companies.UserModel"}, "accountStatus": {"$ref": "#/components/schemas/BlueTape.CompanyService.Common.Enums.AccountStatusEnum"}, "accountStatusUpdatedAt": {"type": "string", "format": "date-time", "nullable": true}, "isAccountStatusManuallySet": {"type": "boolean", "nullable": true}, "manualStatusBy": {"type": "string", "nullable": true}, "manualStatusNote": {"type": "string", "nullable": true}, "statusEvent": {"type": "string", "nullable": true}, "publicIdentifier": {"type": "string", "nullable": true}}, "additionalProperties": false}, "BlueTape.CompanyService.Companies.CompanyNoteModel": {"type": "object", "properties": {"id": {"type": "string", "format": "uuid", "nullable": true}, "createdAt": {"type": "string", "format": "date-time", "nullable": true}, "createdBy": {"type": "string", "nullable": true}, "updatedAt": {"type": "string", "format": "date-time", "nullable": true}, "updatedBy": {"type": "string", "nullable": true}, "deletedAt": {"type": "string", "format": "date-time", "nullable": true}, "deletedBy": {"type": "string", "nullable": true}, "note": {"type": "string", "nullable": true}}, "additionalProperties": false}, "BlueTape.CompanyService.Companies.CompanySettingsModel": {"type": "object", "properties": {"approveRead": {"type": "boolean", "nullable": true}, "onboarded": {"type": "boolean", "nullable": true}, "onboardingType": {"type": "array", "items": {"type": "string"}, "nullable": true}, "invitedBy": {"type": "string", "nullable": true}, "loanPlans": {"type": "array", "items": {"type": "string"}, "nullable": true}, "invoiceLoanPlans": {"type": "array", "items": {"$ref": "#/components/schemas/BlueTape.CompanyService.Companies.InvoiceLoanPlanModel"}, "nullable": true}, "businessType": {"type": "string", "nullable": true}, "cardPricingPackageId": {"type": "string", "nullable": true}, "loanPricingPackageId": {"type": "string", "nullable": true}, "acceptAchPayment": {"type": "boolean", "nullable": true}, "achDelayDisabled": {"type": "boolean", "nullable": true}, "achDiscount": {"$ref": "#/components/schemas/BlueTape.CompanyService.Companies.ArAdvanceModel"}, "repayment": {"$ref": "#/components/schemas/BlueTape.CompanyService.Companies.RepaymentModel"}, "ocrModelId": {"type": "string", "nullable": true}, "arAdvance": {"$ref": "#/components/schemas/BlueTape.CompanyService.Companies.ArAdvanceModel"}, "canEditAuthorization": {"type": "boolean", "nullable": true}, "canPostTransactions": {"type": "boolean", "nullable": true}, "canUploadInvoice": {"type": "boolean", "nullable": true}, "dueDay": {"type": "integer", "format": "int32", "nullable": true}, "email": {"$ref": "#/components/schemas/BlueTape.CompanyService.Companies.EmailConfigurationModel"}, "sendFinalPaymentWhenLoanIsPaid": {"type": "boolean", "nullable": true}, "supplierCanPay": {"type": "boolean", "nullable": true}, "tutorialViewed": {"type": "boolean", "nullable": true}, "welcomeViewed": {"type": "boolean", "nullable": true}, "automatedDrawApproval": {"$ref": "#/components/schemas/BlueTape.CompanyService.Companies.AutomatedDrawApprovalModel"}, "defaultDebtInvestorTradeCredit": {"$ref": "#/components/schemas/BlueTape.CompanyService.Common.Enums.DebtInvestorType"}, "depositDetails": {"$ref": "#/components/schemas/BlueTape.CompanyService.Companies.DepositDetailsModel"}, "downPaymentDetails": {"$ref": "#/components/schemas/BlueTape.CompanyService.Companies.DownPaymentDetailsModel"}, "directTerms": {"$ref": "#/components/schemas/BlueTape.CompanyService.Companies.DirectTermsModel"}, "inHouseCredit": {"$ref": "#/components/schemas/BlueTape.CompanyService.Companies.CompanyInHouseCreditModel"}}, "additionalProperties": false}, "BlueTape.CompanyService.Companies.CreateCompanyModel": {"type": "object", "properties": {"type": {"$ref": "#/components/schemas/BlueTape.CompanyService.Common.Enums.CompanyTypeEnum"}, "isGuest": {"type": "boolean"}, "status": {"$ref": "#/components/schemas/BlueTape.CompanyService.Common.Enums.CompanyStatusEnum"}, "bankAccounts": {"type": "array", "items": {"type": "string"}, "nullable": true}, "createdAt": {"type": "string", "format": "date-time", "nullable": true}, "updatedAt": {"type": "string", "format": "date-time", "nullable": true}, "name": {"type": "string", "nullable": true}, "entity": {"type": "string", "nullable": true}, "legalName": {"type": "string", "nullable": true}, "contactName": {"type": "string", "nullable": true}, "website": {"type": "string", "nullable": true}, "address": {"$ref": "#/components/schemas/BlueTape.CompanyService.Companies.AddressModel"}, "phone": {"type": "string", "nullable": true}, "email": {"type": "string", "nullable": true}, "isBusiness": {"type": "boolean"}, "settings": {"$ref": "#/components/schemas/BlueTape.CompanyService.Companies.CompanySettingsModel"}}, "additionalProperties": false}, "BlueTape.CompanyService.Companies.CreateCompanyNoteModel": {"type": "object", "properties": {"note": {"type": "string", "nullable": true}}, "additionalProperties": false}, "BlueTape.CompanyService.Companies.DepositDetailsModel": {"type": "object", "properties": {"isSecured": {"type": "boolean"}, "depositAmount": {"type": "number", "format": "double", "nullable": true}, "isDepositPaid": {"type": "boolean", "nullable": true}}, "additionalProperties": false}, "BlueTape.CompanyService.Companies.DirectTermsModel": {"type": "object", "properties": {"loanPlans": {"type": "array", "items": {"type": "string"}, "nullable": true}}, "additionalProperties": false}, "BlueTape.CompanyService.Companies.DownPaymentDetailsModel": {"type": "object", "properties": {"isRequired": {"type": "boolean"}, "downPaymentPercentage": {"type": "number", "format": "double", "nullable": true}, "expireDays": {"type": "integer", "format": "int32", "nullable": true}}, "additionalProperties": false}, "BlueTape.CompanyService.Companies.EmailConfigurationModel": {"type": "object", "properties": {"senderEmail": {"type": "string", "nullable": true}, "sendInvitationTemplate": {"type": "string", "nullable": true}, "sendInvoiceTemplate": {"type": "string", "nullable": true}}, "additionalProperties": false}, "BlueTape.CompanyService.Companies.InvoiceLoanPlanModel": {"type": "object", "properties": {"minAmount": {"type": "number", "format": "double", "nullable": true}, "maxAmount": {"type": "number", "format": "double", "nullable": true}, "plans": {"type": "array", "items": {"type": "string"}, "nullable": true}}, "additionalProperties": false}, "BlueTape.CompanyService.Companies.MigrateResultModel": {"type": "object", "properties": {"newId": {"type": "string", "format": "uuid"}, "legacyId": {"type": "string", "nullable": true}}, "additionalProperties": false}, "BlueTape.CompanyService.Companies.PaginatedCompanyResponse": {"type": "object", "properties": {"limit": {"type": "integer", "format": "int32", "nullable": true}, "offset": {"type": "integer", "format": "int32", "nullable": true}, "count": {"type": "integer", "format": "int32", "nullable": true}, "total": {"type": "integer", "format": "int32", "nullable": true}, "result": {"type": "array", "items": {"$ref": "#/components/schemas/BlueTape.CompanyService.Companies.CompanyModel"}, "nullable": true}}, "additionalProperties": false}, "BlueTape.CompanyService.Companies.RepaymentModel": {"type": "object", "properties": {"repays": {"$ref": "#/components/schemas/BlueTape.CompanyService.Common.Enums.RepaysType"}, "nameOnAccount": {"type": "string", "nullable": true}, "routingNumber": {"type": "string", "nullable": true}, "accountNumber": {"type": "string", "nullable": true}, "autoApprove": {"type": "boolean", "nullable": true}, "paymentPlan": {"type": "string", "nullable": true}, "maxInvoiceAmount": {"type": "number", "format": "double", "nullable": true}, "autoApproveType": {"$ref": "#/components/schemas/BlueTape.CompanyService.Common.Enums.AutoApproveType"}}, "additionalProperties": false}, "BlueTape.CompanyService.Companies.UpdateCompanyModel": {"type": "object", "properties": {"type": {"$ref": "#/components/schemas/BlueTape.CompanyService.Common.Enums.CompanyTypeEnum"}, "isGuest": {"type": "boolean", "nullable": true}, "status": {"$ref": "#/components/schemas/BlueTape.CompanyService.Common.Enums.CompanyStatusEnum"}, "bankAccounts": {"type": "array", "items": {"type": "string"}, "nullable": true}, "createdAt": {"type": "string", "format": "date-time", "nullable": true}, "updatedAt": {"type": "string", "format": "date-time", "nullable": true}, "name": {"type": "string", "nullable": true}, "entity": {"type": "string", "nullable": true}, "legalName": {"type": "string", "nullable": true}, "contactName": {"type": "string", "nullable": true}, "website": {"type": "string", "nullable": true}, "address": {"$ref": "#/components/schemas/BlueTape.CompanyService.Companies.AddressModel"}, "phone": {"type": "string", "nullable": true}, "email": {"type": "string", "nullable": true}, "isBusiness": {"type": "boolean", "nullable": true}, "settings": {"$ref": "#/components/schemas/BlueTape.CompanyService.Companies.CompanySettingsModel"}, "credit": {"$ref": "#/components/schemas/BlueTape.CompanyService.Companies.CompanyCreditModel"}}, "additionalProperties": false}, "BlueTape.CompanyService.Companies.UserModel": {"type": "object", "properties": {"id": {"type": "string", "nullable": true}, "createdAt": {"type": "string", "format": "date-time", "nullable": true}, "updatedAt": {"type": "string", "format": "date-time", "nullable": true}, "sub": {"type": "string", "nullable": true}, "firstName": {"type": "string", "nullable": true}, "lastName": {"type": "string", "nullable": true}, "contactName": {"type": "string", "nullable": true}, "phone": {"type": "string", "nullable": true}, "email": {"type": "string", "nullable": true}, "login": {"type": "string", "nullable": true}}, "additionalProperties": false}, "BlueTape.CompanyService.Customers.CustomerConnectorModel": {"type": "object", "properties": {"connectorCustomerId": {"type": "string", "nullable": true}, "integrationId": {"type": "string", "nullable": true}, "connectorContactId": {"type": "string", "nullable": true}, "businessName": {"type": "string", "nullable": true}, "sourceModifiedDate": {"type": "string", "format": "date-time"}, "isPrimaryContact": {"type": "boolean"}, "blueTapeCustomerCompanyId": {"type": "string", "nullable": true}, "isCompanyType": {"type": "boolean"}}, "additionalProperties": false}, "BlueTape.CompanyService.Customers.CustomerInHouseCreditModel": {"type": "object", "properties": {"isEnabled": {"type": "boolean"}, "factoringTerm": {"type": "string", "nullable": true}, "limit": {"type": "number", "format": "double", "nullable": true}, "recoursePercentage": {"type": "number", "format": "double", "nullable": true}, "supplierPackage": {"type": "string", "nullable": true}, "debtInvestor": {"$ref": "#/components/schemas/BlueTape.CompanyService.Common.Enums.DebtInvestorType"}, "calculatedDebtInvestor": {"$ref": "#/components/schemas/BlueTape.CompanyService.Common.Enums.DebtInvestorType"}}, "additionalProperties": false}, "BlueTape.CompanyService.Customers.CustomerModel": {"type": "object", "properties": {"id": {"type": "string", "nullable": true}, "companyId": {"type": "string", "nullable": true}, "displayName": {"type": "string", "nullable": true}, "firstName": {"type": "string", "nullable": true}, "lastName": {"type": "string", "nullable": true}, "type": {"type": "string", "nullable": true}, "name": {"type": "string", "nullable": true}, "emailAddress": {"type": "string", "nullable": true}, "cellPhoneNumber": {"type": "string", "nullable": true}, "businessPhoneNumber": {"type": "string", "nullable": true}, "businessAddress": {"type": "string", "nullable": true}, "integration": {"$ref": "#/components/schemas/BlueTape.CompanyService.Customers.CustomerConnectorModel"}, "isDraft": {"type": "boolean"}, "isDeleted": {"type": "boolean"}, "contactSource": {"$ref": "#/components/schemas/BlueTape.CompanyService.Common.Enums.ContactSource"}, "syncToken": {"type": "string", "nullable": true}, "salesRep": {"type": "string", "nullable": true}, "lastPurchaseDate": {"type": "string", "format": "date-time", "nullable": true}, "settings": {"$ref": "#/components/schemas/BlueTape.CompanyService.Customers.CustomerSettingsModel"}}, "additionalProperties": false}, "BlueTape.CompanyService.Customers.CustomerSettingsModel": {"type": "object", "properties": {"acceptAchPayment": {"type": "boolean", "nullable": true}, "acceptCheckPayment": {"type": "boolean", "nullable": true}, "acceptCreditPayment": {"type": "boolean", "nullable": true}, "autoDebit": {"type": "boolean", "nullable": true}, "autoTradeCreditDisabled": {"type": "boolean", "nullable": true}, "autoTradeCreditEnabled": {"type": "boolean", "nullable": true}, "autoTradeCreditMaxInvoiceAmount": {"type": "number", "format": "double", "nullable": true}, "autoTradeCreditPaymentPlan": {"type": "string", "nullable": true}, "inHouseCredit": {"$ref": "#/components/schemas/BlueTape.CompanyService.Customers.CustomerInHouseCreditModel"}, "loanPaymentCollection": {"$ref": "#/components/schemas/BlueTape.CompanyService.Common.Enums.LoanPaymentCollection"}, "loanPlans": {"type": "array", "items": {"type": "string"}, "nullable": true}, "sendFinalPaymentWhenLoanIsPaid": {"type": "boolean", "nullable": true}, "tradeCreditPaymentFrequency": {"type": "string", "nullable": true}, "automatedDrawApproval": {"$ref": "#/components/schemas/BlueTape.CompanyService.Companies.AutomatedDrawApprovalModel"}, "debtInvestorTradeCredit": {"$ref": "#/components/schemas/BlueTape.CompanyService.Common.Enums.DebtInvestorType"}, "calculatedDebtInvestorTradeCredit": {"$ref": "#/components/schemas/BlueTape.CompanyService.Common.Enums.DebtInvestorType"}}, "additionalProperties": false}, "BlueTape.CompanyService.Customers.UpdateCustomerModel": {"type": "object", "properties": {"lastPurchaseDate": {"type": "string", "format": "date-time", "nullable": true}, "resourcePercentage": {"type": "number", "format": "double", "nullable": true}, "factoringTerm": {"type": "string", "nullable": true}, "supplierPackage": {"type": "string", "nullable": true}, "limit": {"type": "number", "format": "double", "nullable": true}}, "additionalProperties": false}, "BlueTape.CompanyService.Documents.Enums.TemplateTypeFilter": {"enum": ["BNPL_AGREEMENT", "MASTER_AGREEMENT", "CUSTOMER_AGREEMENT_FOR_SELLER", "PERSONAL_GUARANTOR_AGREEMENT", "PERSONAL_GUARANTOR_AGREEMENT_ENTITY", "INVOICE_PURCHASER_AGREEMENT", "GUARANTY_SECURED_AGREEMENT", "ALL"], "type": "string"}, "BlueTape.CompanyService.Documents.Responses.BankStatementResponse": {"type": "object", "properties": {"id": {"type": "string", "format": "uuid"}, "uploadedAt": {"type": "string", "format": "date-time"}, "uploadedBy": {"type": "string", "nullable": true}, "accountId": {"type": "string", "nullable": true}, "fileName": {"type": "string", "nullable": true}, "fileUrl": {"type": "string", "nullable": true}, "daysAvailableFrom": {"type": "string", "format": "date"}, "daysAvailableTo": {"type": "string", "format": "date"}}, "additionalProperties": false}, "BlueTape.CompanyService.Documents.Responses.DocumentApprovalResponse": {"type": "object", "properties": {"id": {"type": "string", "format": "uuid"}, "createdAt": {"type": "string", "format": "date-time"}, "createdBy": {"type": "string", "nullable": true}, "updatedAt": {"type": "string", "format": "date-time"}, "documentId": {"type": "string", "nullable": true}, "expiredAt": {"type": "string", "format": "date-time"}, "isExpired": {"type": "boolean"}, "ownerIdentifier": {"type": "string", "nullable": true}, "emailAddress": {"type": "string", "nullable": true}, "userId": {"type": "string", "nullable": true}, "firstName": {"type": "string", "nullable": true}, "lastName": {"type": "string", "nullable": true}, "ipAddress": {"type": "string", "nullable": true}, "approvedAt": {"type": "string", "format": "date-time", "nullable": true}, "isApproved": {"type": "boolean"}}, "additionalProperties": false}, "BlueTape.CompanyService.Documents.Responses.DocumentLoanApplicationResponse": {"type": "object", "properties": {"id": {"type": "string", "format": "uuid"}, "createdAt": {"type": "string", "format": "date-time"}, "loanApplicationId": {"type": "string", "nullable": true}, "referenceId": {"type": "string", "nullable": true}, "s3Url": {"type": "string", "nullable": true}, "template": {"$ref": "#/components/schemas/BlueTape.CompanyService.Documents.Responses.TemplateResponse"}, "documentApprovals": {"type": "array", "items": {"$ref": "#/components/schemas/BlueTape.CompanyService.Documents.Responses.DocumentApprovalResponse"}, "nullable": true}}, "additionalProperties": false}, "BlueTape.CompanyService.Documents.Responses.DocumentMetadataResponse": {"type": "object", "properties": {"currentValidFrom": {"type": "string", "format": "date-time", "nullable": true}, "latestValidFrom": {"type": "string", "format": "date-time", "nullable": true}, "currentVersion": {"type": "string", "nullable": true}, "latestVersion": {"type": "string", "nullable": true}}, "additionalProperties": false}, "BlueTape.CompanyService.Documents.Responses.DocumentReportResponse": {"type": "object", "properties": {"loanApplicationId": {"type": "string", "nullable": true}, "companyId": {"type": "string", "nullable": true}, "url": {"type": "string", "nullable": true}}, "additionalProperties": false}, "BlueTape.CompanyService.Documents.Responses.NewDocumentNameResponse": {"type": "object", "properties": {"documentTemplateId": {"type": "string", "format": "uuid"}, "bucket": {"type": "string", "nullable": true}, "path": {"type": "string", "nullable": true}, "fileName": {"type": "string", "nullable": true}, "templateS3Url": {"type": "string", "nullable": true}}, "additionalProperties": false}, "BlueTape.CompanyService.Documents.Responses.TemplateResponse": {"type": "object", "properties": {"id": {"type": "string", "format": "uuid"}, "version": {"type": "integer", "format": "int32"}, "code": {"$ref": "#/components/schemas/BlueTape.CompanyService.Common.Enums.TemplateType"}, "s3Url": {"type": "string", "nullable": true}, "semanticVersion": {"type": "string", "nullable": true}, "validFrom": {"type": "string", "format": "date-time"}, "futureFileName": {"type": "string", "nullable": true}}, "additionalProperties": false}, "BlueTape.CompanyService.Enums.AccountSortingParameter": {"enum": ["businessName", "dba", "category", "accountStatus", "contactName", "createdAt"], "type": "string"}, "BlueTape.CompanyService.GuestSuppliers.GuestSupplierCustomerModel": {"type": "object", "properties": {"businessName": {"type": "string", "nullable": true}, "contactName": {"type": "string", "nullable": true}, "accountStatus": {"$ref": "#/components/schemas/BlueTape.CompanyService.Common.Enums.AccountStatusEnum"}, "numberOfInvoices": {"type": "integer", "format": "int64", "nullable": true}, "amountOfInvoices": {"type": "number", "format": "double", "nullable": true}}, "additionalProperties": false}, "BlueTape.CompanyService.GuestSuppliers.GuestSupplierInvoiceModel": {"type": "object", "properties": {"businessName": {"type": "string", "nullable": true}, "invoiceNumber": {"type": "string", "nullable": true}, "statu": {"type": "string", "nullable": true}, "dueDate": {"type": "string", "format": "date-time", "nullable": true}, "amount": {"type": "number", "format": "double", "nullable": true}}, "additionalProperties": false}, "BlueTape.CompanyService.GuestSuppliers.GuestSupplierModel": {"type": "object", "properties": {"id": {"type": "string", "nullable": true}, "businessName": {"type": "string", "nullable": true}, "contactName": {"type": "string", "nullable": true}, "phone": {"type": "string", "nullable": true}, "email": {"type": "string", "nullable": true}, "numberOfInvoices": {"type": "integer", "format": "int64", "nullable": true}, "amountOfInvoices": {"type": "number", "format": "double", "nullable": true}}, "additionalProperties": false}, "BlueTape.Document.Domain.DTOs.LoanApplicationMigrationInfo": {"type": "object", "properties": {"loanApplicationId": {"type": "string", "nullable": true}, "templateType": {"$ref": "#/components/schemas/BlueTape.CompanyService.Common.Enums.TemplateType"}}, "additionalProperties": false}, "BlueTape.Utilities.Enums.SortOrderType": {"enum": ["asc", "desc"], "type": "string"}, "Microsoft.AspNetCore.Hosting.IWebHostEnvironment": {"type": "object", "properties": {"webRootPath": {"type": "string", "nullable": true}, "webRootFileProvider": {"$ref": "#/components/schemas/Microsoft.Extensions.FileProviders.IFileProvider"}, "environmentName": {"type": "string", "nullable": true}, "applicationName": {"type": "string", "nullable": true}, "contentRootPath": {"type": "string", "nullable": true}, "contentRootFileProvider": {"$ref": "#/components/schemas/Microsoft.Extensions.FileProviders.IFileProvider"}}, "additionalProperties": false}, "Microsoft.AspNetCore.Mvc.ProblemDetails": {"type": "object", "properties": {"type": {"type": "string", "nullable": true}, "title": {"type": "string", "nullable": true}, "status": {"type": "integer", "format": "int32", "nullable": true}, "detail": {"type": "string", "nullable": true}, "instance": {"type": "string", "nullable": true}}, "additionalProperties": {}}, "Microsoft.Extensions.FileProviders.IFileProvider": {"type": "object", "additionalProperties": false}}, "securitySchemes": {"ApiKey": {"type": "<PERSON><PERSON><PERSON><PERSON>", "description": "API Key needed to access the endpoints. Add the API key to the 'Authorization' header in the format 'Bearer {apiKey}'", "name": "Authorization", "in": "header"}}}, "security": [{"ApiKey": []}]}