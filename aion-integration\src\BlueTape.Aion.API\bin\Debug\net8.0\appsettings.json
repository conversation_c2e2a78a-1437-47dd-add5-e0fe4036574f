{"Serilog": {"MinimumLevel": {"Default": "Information", "Override": {"AWSSDK": "Warning", "BlueTape.Services.Utilities.AspNetCore.Tracing": "Error", "BlueTape.Services.Utilities.AWS": "Warning", "Microsoft.AspNetCore": "Warning", "Microsoft.AspNetCore.DataProtection": "Error", "Microsoft.EntityFrameworkCore": "Warning", "System.Net.Http.HttpClient": "Warning", "Microsoft.AspNetCore.HttpLogging.HttpLoggingMiddleware": "Warning"}}}}