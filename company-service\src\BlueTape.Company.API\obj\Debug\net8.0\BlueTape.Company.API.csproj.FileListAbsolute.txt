C:\Users\<USER>\source\repos\BlueTape\company-service\src\BlueTape.Company.API\bin\Debug\net8.0\appsettings.beta.json
C:\Users\<USER>\source\repos\BlueTape\company-service\src\BlueTape.Company.API\bin\Debug\net8.0\appsettings.dev.json
C:\Users\<USER>\source\repos\BlueTape\company-service\src\BlueTape.Company.API\bin\Debug\net8.0\appsettings.json
C:\Users\<USER>\source\repos\BlueTape\company-service\src\BlueTape.Company.API\bin\Debug\net8.0\appsettings.prod.json
C:\Users\<USER>\source\repos\BlueTape\company-service\src\BlueTape.Company.API\bin\Debug\net8.0\appsettings.qa.json
C:\Users\<USER>\source\repos\BlueTape\company-service\src\BlueTape.Company.API\bin\Debug\net8.0\BlueTape.Company.API.staticwebassets.endpoints.json
C:\Users\<USER>\source\repos\BlueTape\company-service\src\BlueTape.Company.API\bin\Debug\net8.0\BlueTape.Company.API.exe
C:\Users\<USER>\source\repos\BlueTape\company-service\src\BlueTape.Company.API\bin\Debug\net8.0\BlueTape.Company.API.deps.json
C:\Users\<USER>\source\repos\BlueTape\company-service\src\BlueTape.Company.API\bin\Debug\net8.0\BlueTape.Company.API.runtimeconfig.json
C:\Users\<USER>\source\repos\BlueTape\company-service\src\BlueTape.Company.API\bin\Debug\net8.0\BlueTape.Company.API.dll
C:\Users\<USER>\source\repos\BlueTape\company-service\src\BlueTape.Company.API\bin\Debug\net8.0\BlueTape.Company.API.pdb
C:\Users\<USER>\source\repos\BlueTape\company-service\src\BlueTape.Company.API\bin\Debug\net8.0\Amazon.Lambda.APIGatewayEvents.dll
C:\Users\<USER>\source\repos\BlueTape\company-service\src\BlueTape.Company.API\bin\Debug\net8.0\Amazon.Lambda.ApplicationLoadBalancerEvents.dll
C:\Users\<USER>\source\repos\BlueTape\company-service\src\BlueTape.Company.API\bin\Debug\net8.0\Amazon.Lambda.AspNetCoreServer.dll
C:\Users\<USER>\source\repos\BlueTape\company-service\src\BlueTape.Company.API\bin\Debug\net8.0\Amazon.Lambda.AspNetCoreServer.Hosting.dll
C:\Users\<USER>\source\repos\BlueTape\company-service\src\BlueTape.Company.API\bin\Debug\net8.0\Amazon.Lambda.Core.dll
C:\Users\<USER>\source\repos\BlueTape\company-service\src\BlueTape.Company.API\bin\Debug\net8.0\Amazon.Lambda.Logging.AspNetCore.dll
C:\Users\<USER>\source\repos\BlueTape\company-service\src\BlueTape.Company.API\bin\Debug\net8.0\Amazon.Lambda.RuntimeSupport.dll
C:\Users\<USER>\source\repos\BlueTape\company-service\src\BlueTape.Company.API\bin\Debug\net8.0\Amazon.Lambda.Serialization.SystemTextJson.dll
C:\Users\<USER>\source\repos\BlueTape\company-service\src\BlueTape.Company.API\bin\Debug\net8.0\AutoMapper.dll
C:\Users\<USER>\source\repos\BlueTape\company-service\src\BlueTape.Company.API\bin\Debug\net8.0\AutoMapper.Extensions.Microsoft.DependencyInjection.dll
C:\Users\<USER>\source\repos\BlueTape\company-service\src\BlueTape.Company.API\bin\Debug\net8.0\AWSSDK.Core.dll
C:\Users\<USER>\source\repos\BlueTape\company-service\src\BlueTape.Company.API\bin\Debug\net8.0\AWSSDK.Extensions.NETCore.Setup.dll
C:\Users\<USER>\source\repos\BlueTape\company-service\src\BlueTape.Company.API\bin\Debug\net8.0\AWSSDK.KeyManagementService.dll
C:\Users\<USER>\source\repos\BlueTape\company-service\src\BlueTape.Company.API\bin\Debug\net8.0\AWSSDK.S3.dll
C:\Users\<USER>\source\repos\BlueTape\company-service\src\BlueTape.Company.API\bin\Debug\net8.0\AWSSDK.SecretsManager.dll
C:\Users\<USER>\source\repos\BlueTape\company-service\src\BlueTape.Company.API\bin\Debug\net8.0\Amazon.SecretsManager.Extensions.Caching.dll
C:\Users\<USER>\source\repos\BlueTape\company-service\src\BlueTape.Company.API\bin\Debug\net8.0\AWSSDK.SecurityToken.dll
C:\Users\<USER>\source\repos\BlueTape\company-service\src\BlueTape.Company.API\bin\Debug\net8.0\AWSSDK.SimpleNotificationService.dll
C:\Users\<USER>\source\repos\BlueTape\company-service\src\BlueTape.Company.API\bin\Debug\net8.0\Azure.Core.dll
C:\Users\<USER>\source\repos\BlueTape\company-service\src\BlueTape.Company.API\bin\Debug\net8.0\Azure.Core.Amqp.dll
C:\Users\<USER>\source\repos\BlueTape\company-service\src\BlueTape.Company.API\bin\Debug\net8.0\Azure.Extensions.AspNetCore.Configuration.Secrets.dll
C:\Users\<USER>\source\repos\BlueTape\company-service\src\BlueTape.Company.API\bin\Debug\net8.0\Azure.Identity.dll
C:\Users\<USER>\source\repos\BlueTape\company-service\src\BlueTape.Company.API\bin\Debug\net8.0\Azure.Messaging.ServiceBus.dll
C:\Users\<USER>\source\repos\BlueTape\company-service\src\BlueTape.Company.API\bin\Debug\net8.0\Azure.Security.KeyVault.Keys.dll
C:\Users\<USER>\source\repos\BlueTape\company-service\src\BlueTape.Company.API\bin\Debug\net8.0\Azure.Security.KeyVault.Secrets.dll
C:\Users\<USER>\source\repos\BlueTape\company-service\src\BlueTape.Company.API\bin\Debug\net8.0\BlueTape.AWSS3.dll
C:\Users\<USER>\source\repos\BlueTape\company-service\src\BlueTape.Company.API\bin\Debug\net8.0\BlueTape.AzureKeyVault.dll
C:\Users\<USER>\source\repos\BlueTape\company-service\src\BlueTape.Company.API\bin\Debug\net8.0\BlueTape.Common.ExceptionHandling.dll
C:\Users\<USER>\source\repos\BlueTape\company-service\src\BlueTape.Company.API\bin\Debug\net8.0\BlueTape.Common.Extensions.dll
C:\Users\<USER>\source\repos\BlueTape\company-service\src\BlueTape.Company.API\bin\Debug\net8.0\BlueTape.Common.FileService.dll
C:\Users\<USER>\source\repos\BlueTape\company-service\src\BlueTape.Company.API\bin\Debug\net8.0\BlueTape.CompanyService.dll
C:\Users\<USER>\source\repos\BlueTape\company-service\src\BlueTape.Company.API\bin\Debug\net8.0\BlueTape.CompanyService.Common.dll
C:\Users\<USER>\source\repos\BlueTape\company-service\src\BlueTape.Company.API\bin\Debug\net8.0\BlueTape.EmailSender.dll
C:\Users\<USER>\source\repos\BlueTape\company-service\src\BlueTape.Company.API\bin\Debug\net8.0\BlueTape.LMS.dll
C:\Users\<USER>\source\repos\BlueTape\company-service\src\BlueTape.Company.API\bin\Debug\net8.0\BlueTape.Logging.dll
C:\Users\<USER>\source\repos\BlueTape\company-service\src\BlueTape.Company.API\bin\Debug\net8.0\BlueTape.LS.dll
C:\Users\<USER>\source\repos\BlueTape\company-service\src\BlueTape.Company.API\bin\Debug\net8.0\BlueTape.LS.Domain.dll
C:\Users\<USER>\source\repos\BlueTape\company-service\src\BlueTape.Company.API\bin\Debug\net8.0\BlueTape.MongoDB.dll
C:\Users\<USER>\source\repos\BlueTape\company-service\src\BlueTape.Company.API\bin\Debug\net8.0\BlueTape.OBS.dll
C:\Users\<USER>\source\repos\BlueTape\company-service\src\BlueTape.Company.API\bin\Debug\net8.0\BlueTape.ServiceBusMessaging.dll
C:\Users\<USER>\source\repos\BlueTape\company-service\src\BlueTape.Company.API\bin\Debug\net8.0\BlueTape.SNS.dll
C:\Users\<USER>\source\repos\BlueTape\company-service\src\BlueTape.Company.API\bin\Debug\net8.0\BlueTape.Utilities.dll
C:\Users\<USER>\source\repos\BlueTape\company-service\src\BlueTape.Company.API\bin\Debug\net8.0\ClosedXML.dll
C:\Users\<USER>\source\repos\BlueTape\company-service\src\BlueTape.Company.API\bin\Debug\net8.0\CsvHelper.dll
C:\Users\<USER>\source\repos\BlueTape\company-service\src\BlueTape.Company.API\bin\Debug\net8.0\DnsClient.dll
C:\Users\<USER>\source\repos\BlueTape\company-service\src\BlueTape.Company.API\bin\Debug\net8.0\DocumentFormat.OpenXml.dll
C:\Users\<USER>\source\repos\BlueTape\company-service\src\BlueTape.Company.API\bin\Debug\net8.0\Elastic.CommonSchema.dll
C:\Users\<USER>\source\repos\BlueTape\company-service\src\BlueTape.Company.API\bin\Debug\net8.0\Elastic.CommonSchema.Serilog.dll
C:\Users\<USER>\source\repos\BlueTape\company-service\src\BlueTape.Company.API\bin\Debug\net8.0\ExcelNumberFormat.dll
C:\Users\<USER>\source\repos\BlueTape\company-service\src\BlueTape.Company.API\bin\Debug\net8.0\FluentValidation.dll
C:\Users\<USER>\source\repos\BlueTape\company-service\src\BlueTape.Company.API\bin\Debug\net8.0\FluentValidation.AspNetCore.dll
C:\Users\<USER>\source\repos\BlueTape\company-service\src\BlueTape.Company.API\bin\Debug\net8.0\FluentValidation.DependencyInjectionExtensions.dll
C:\Users\<USER>\source\repos\BlueTape\company-service\src\BlueTape.Company.API\bin\Debug\net8.0\Humanizer.dll
C:\Users\<USER>\source\repos\BlueTape\company-service\src\BlueTape.Company.API\bin\Debug\net8.0\Irony.dll
C:\Users\<USER>\source\repos\BlueTape\company-service\src\BlueTape.Company.API\bin\Debug\net8.0\Microsoft.ApplicationInsights.dll
C:\Users\<USER>\source\repos\BlueTape\company-service\src\BlueTape.Company.API\bin\Debug\net8.0\Microsoft.ApplicationInsights.AspNetCore.dll
C:\Users\<USER>\source\repos\BlueTape\company-service\src\BlueTape.Company.API\bin\Debug\net8.0\Microsoft.AI.DependencyCollector.dll
C:\Users\<USER>\source\repos\BlueTape\company-service\src\BlueTape.Company.API\bin\Debug\net8.0\Microsoft.AI.EventCounterCollector.dll
C:\Users\<USER>\source\repos\BlueTape\company-service\src\BlueTape.Company.API\bin\Debug\net8.0\Microsoft.AI.PerfCounterCollector.dll
C:\Users\<USER>\source\repos\BlueTape\company-service\src\BlueTape.Company.API\bin\Debug\net8.0\Microsoft.AI.WindowsServer.dll
C:\Users\<USER>\source\repos\BlueTape\company-service\src\BlueTape.Company.API\bin\Debug\net8.0\Microsoft.AI.ServerTelemetryChannel.dll
C:\Users\<USER>\source\repos\BlueTape\company-service\src\BlueTape.Company.API\bin\Debug\net8.0\Microsoft.Azure.Amqp.dll
C:\Users\<USER>\source\repos\BlueTape\company-service\src\BlueTape.Company.API\bin\Debug\net8.0\Microsoft.Bcl.AsyncInterfaces.dll
C:\Users\<USER>\source\repos\BlueTape\company-service\src\BlueTape.Company.API\bin\Debug\net8.0\Microsoft.Data.SqlClient.dll
C:\Users\<USER>\source\repos\BlueTape\company-service\src\BlueTape.Company.API\bin\Debug\net8.0\Microsoft.EntityFrameworkCore.dll
C:\Users\<USER>\source\repos\BlueTape\company-service\src\BlueTape.Company.API\bin\Debug\net8.0\Microsoft.EntityFrameworkCore.Abstractions.dll
C:\Users\<USER>\source\repos\BlueTape\company-service\src\BlueTape.Company.API\bin\Debug\net8.0\Microsoft.EntityFrameworkCore.Design.dll
C:\Users\<USER>\source\repos\BlueTape\company-service\src\BlueTape.Company.API\bin\Debug\net8.0\Microsoft.EntityFrameworkCore.InMemory.dll
C:\Users\<USER>\source\repos\BlueTape\company-service\src\BlueTape.Company.API\bin\Debug\net8.0\Microsoft.EntityFrameworkCore.Relational.dll
C:\Users\<USER>\source\repos\BlueTape\company-service\src\BlueTape.Company.API\bin\Debug\net8.0\Microsoft.Extensions.DependencyModel.dll
C:\Users\<USER>\source\repos\BlueTape\company-service\src\BlueTape.Company.API\bin\Debug\net8.0\Microsoft.Extensions.Http.Polly.dll
C:\Users\<USER>\source\repos\BlueTape\company-service\src\BlueTape.Company.API\bin\Debug\net8.0\Microsoft.Extensions.Logging.ApplicationInsights.dll
C:\Users\<USER>\source\repos\BlueTape\company-service\src\BlueTape.Company.API\bin\Debug\net8.0\Microsoft.Extensions.Options.dll
C:\Users\<USER>\source\repos\BlueTape\company-service\src\BlueTape.Company.API\bin\Debug\net8.0\Microsoft.Identity.Client.dll
C:\Users\<USER>\source\repos\BlueTape\company-service\src\BlueTape.Company.API\bin\Debug\net8.0\Microsoft.Identity.Client.Extensions.Msal.dll
C:\Users\<USER>\source\repos\BlueTape\company-service\src\BlueTape.Company.API\bin\Debug\net8.0\Microsoft.IdentityModel.Abstractions.dll
C:\Users\<USER>\source\repos\BlueTape\company-service\src\BlueTape.Company.API\bin\Debug\net8.0\Microsoft.IdentityModel.JsonWebTokens.dll
C:\Users\<USER>\source\repos\BlueTape\company-service\src\BlueTape.Company.API\bin\Debug\net8.0\Microsoft.IdentityModel.Logging.dll
C:\Users\<USER>\source\repos\BlueTape\company-service\src\BlueTape.Company.API\bin\Debug\net8.0\Microsoft.IdentityModel.Protocols.dll
C:\Users\<USER>\source\repos\BlueTape\company-service\src\BlueTape.Company.API\bin\Debug\net8.0\Microsoft.IdentityModel.Protocols.OpenIdConnect.dll
C:\Users\<USER>\source\repos\BlueTape\company-service\src\BlueTape.Company.API\bin\Debug\net8.0\Microsoft.IdentityModel.Tokens.dll
C:\Users\<USER>\source\repos\BlueTape\company-service\src\BlueTape.Company.API\bin\Debug\net8.0\Microsoft.OpenApi.dll
C:\Users\<USER>\source\repos\BlueTape\company-service\src\BlueTape.Company.API\bin\Debug\net8.0\Microsoft.SqlServer.Server.dll
C:\Users\<USER>\source\repos\BlueTape\company-service\src\BlueTape.Company.API\bin\Debug\net8.0\Microsoft.Win32.SystemEvents.dll
C:\Users\<USER>\source\repos\BlueTape\company-service\src\BlueTape.Company.API\bin\Debug\net8.0\MongoDB.Bson.dll
C:\Users\<USER>\source\repos\BlueTape\company-service\src\BlueTape.Company.API\bin\Debug\net8.0\MongoDB.Driver.dll
C:\Users\<USER>\source\repos\BlueTape\company-service\src\BlueTape.Company.API\bin\Debug\net8.0\MongoDB.Driver.Core.dll
C:\Users\<USER>\source\repos\BlueTape\company-service\src\BlueTape.Company.API\bin\Debug\net8.0\MongoDB.Libmongocrypt.dll
C:\Users\<USER>\source\repos\BlueTape\company-service\src\BlueTape.Company.API\bin\Debug\net8.0\Mono.TextTemplating.dll
C:\Users\<USER>\source\repos\BlueTape\company-service\src\BlueTape.Company.API\bin\Debug\net8.0\Namotion.Reflection.dll
C:\Users\<USER>\source\repos\BlueTape\company-service\src\BlueTape.Company.API\bin\Debug\net8.0\Newtonsoft.Json.dll
C:\Users\<USER>\source\repos\BlueTape\company-service\src\BlueTape.Company.API\bin\Debug\net8.0\NJsonSchema.dll
C:\Users\<USER>\source\repos\BlueTape\company-service\src\BlueTape.Company.API\bin\Debug\net8.0\NJsonSchema.Annotations.dll
C:\Users\<USER>\source\repos\BlueTape\company-service\src\BlueTape.Company.API\bin\Debug\net8.0\NJsonSchema.NewtonsoftJson.dll
C:\Users\<USER>\source\repos\BlueTape\company-service\src\BlueTape.Company.API\bin\Debug\net8.0\NJsonSchema.Yaml.dll
C:\Users\<USER>\source\repos\BlueTape\company-service\src\BlueTape.Company.API\bin\Debug\net8.0\Npgsql.dll
C:\Users\<USER>\source\repos\BlueTape\company-service\src\BlueTape.Company.API\bin\Debug\net8.0\Npgsql.EntityFrameworkCore.PostgreSQL.dll
C:\Users\<USER>\source\repos\BlueTape\company-service\src\BlueTape.Company.API\bin\Debug\net8.0\NSwag.Annotations.dll
C:\Users\<USER>\source\repos\BlueTape\company-service\src\BlueTape.Company.API\bin\Debug\net8.0\NSwag.AspNetCore.dll
C:\Users\<USER>\source\repos\BlueTape\company-service\src\BlueTape.Company.API\bin\Debug\net8.0\NSwag.Core.dll
C:\Users\<USER>\source\repos\BlueTape\company-service\src\BlueTape.Company.API\bin\Debug\net8.0\NSwag.Core.Yaml.dll
C:\Users\<USER>\source\repos\BlueTape\company-service\src\BlueTape.Company.API\bin\Debug\net8.0\NSwag.Generation.dll
C:\Users\<USER>\source\repos\BlueTape\company-service\src\BlueTape.Company.API\bin\Debug\net8.0\NSwag.Generation.AspNetCore.dll
C:\Users\<USER>\source\repos\BlueTape\company-service\src\BlueTape.Company.API\bin\Debug\net8.0\Polly.dll
C:\Users\<USER>\source\repos\BlueTape\company-service\src\BlueTape.Company.API\bin\Debug\net8.0\Polly.Extensions.Http.dll
C:\Users\<USER>\source\repos\BlueTape\company-service\src\BlueTape.Company.API\bin\Debug\net8.0\SendGrid.dll
C:\Users\<USER>\source\repos\BlueTape\company-service\src\BlueTape.Company.API\bin\Debug\net8.0\SendGrid.Extensions.DependencyInjection.dll
C:\Users\<USER>\source\repos\BlueTape\company-service\src\BlueTape.Company.API\bin\Debug\net8.0\Serilog.dll
C:\Users\<USER>\source\repos\BlueTape\company-service\src\BlueTape.Company.API\bin\Debug\net8.0\Serilog.AspNetCore.dll
C:\Users\<USER>\source\repos\BlueTape\company-service\src\BlueTape.Company.API\bin\Debug\net8.0\Serilog.Enrichers.GlobalLogContext.dll
C:\Users\<USER>\source\repos\BlueTape\company-service\src\BlueTape.Company.API\bin\Debug\net8.0\Serilog.Extensions.Hosting.dll
C:\Users\<USER>\source\repos\BlueTape\company-service\src\BlueTape.Company.API\bin\Debug\net8.0\Serilog.Extensions.Logging.dll
C:\Users\<USER>\source\repos\BlueTape\company-service\src\BlueTape.Company.API\bin\Debug\net8.0\Serilog.Formatting.Compact.dll
C:\Users\<USER>\source\repos\BlueTape\company-service\src\BlueTape.Company.API\bin\Debug\net8.0\Serilog.Settings.Configuration.dll
C:\Users\<USER>\source\repos\BlueTape\company-service\src\BlueTape.Company.API\bin\Debug\net8.0\Serilog.Sinks.ApplicationInsights.dll
C:\Users\<USER>\source\repos\BlueTape\company-service\src\BlueTape.Company.API\bin\Debug\net8.0\Serilog.Sinks.Console.dll
C:\Users\<USER>\source\repos\BlueTape\company-service\src\BlueTape.Company.API\bin\Debug\net8.0\Serilog.Sinks.Debug.dll
C:\Users\<USER>\source\repos\BlueTape\company-service\src\BlueTape.Company.API\bin\Debug\net8.0\Serilog.Sinks.File.dll
C:\Users\<USER>\source\repos\BlueTape\company-service\src\BlueTape.Company.API\bin\Debug\net8.0\Serilog.Sinks.Http.dll
C:\Users\<USER>\source\repos\BlueTape\company-service\src\BlueTape.Company.API\bin\Debug\net8.0\Serilog.Sinks.Logz.Io.dll
C:\Users\<USER>\source\repos\BlueTape\company-service\src\BlueTape.Company.API\bin\Debug\net8.0\Serilog.Sinks.PeriodicBatching.dll
C:\Users\<USER>\source\repos\BlueTape\company-service\src\BlueTape.Company.API\bin\Debug\net8.0\SharpCompress.dll
C:\Users\<USER>\source\repos\BlueTape\company-service\src\BlueTape.Company.API\bin\Debug\net8.0\SixLabors.Fonts.dll
C:\Users\<USER>\source\repos\BlueTape\company-service\src\BlueTape.Company.API\bin\Debug\net8.0\Snappier.dll
C:\Users\<USER>\source\repos\BlueTape\company-service\src\BlueTape.Company.API\bin\Debug\net8.0\StarkbankEcdsa.dll
C:\Users\<USER>\source\repos\BlueTape\company-service\src\BlueTape.Company.API\bin\Debug\net8.0\Swashbuckle.AspNetCore.Swagger.dll
C:\Users\<USER>\source\repos\BlueTape\company-service\src\BlueTape.Company.API\bin\Debug\net8.0\Swashbuckle.AspNetCore.SwaggerGen.dll
C:\Users\<USER>\source\repos\BlueTape\company-service\src\BlueTape.Company.API\bin\Debug\net8.0\Swashbuckle.AspNetCore.SwaggerUI.dll
C:\Users\<USER>\source\repos\BlueTape\company-service\src\BlueTape.Company.API\bin\Debug\net8.0\System.ClientModel.dll
C:\Users\<USER>\source\repos\BlueTape\company-service\src\BlueTape.Company.API\bin\Debug\net8.0\System.CodeDom.dll
C:\Users\<USER>\source\repos\BlueTape\company-service\src\BlueTape.Company.API\bin\Debug\net8.0\System.Configuration.ConfigurationManager.dll
C:\Users\<USER>\source\repos\BlueTape\company-service\src\BlueTape.Company.API\bin\Debug\net8.0\System.Diagnostics.PerformanceCounter.dll
C:\Users\<USER>\source\repos\BlueTape\company-service\src\BlueTape.Company.API\bin\Debug\net8.0\System.Drawing.Common.dll
C:\Users\<USER>\source\repos\BlueTape\company-service\src\BlueTape.Company.API\bin\Debug\net8.0\System.IdentityModel.Tokens.Jwt.dll
C:\Users\<USER>\source\repos\BlueTape\company-service\src\BlueTape.Company.API\bin\Debug\net8.0\System.IO.Packaging.dll
C:\Users\<USER>\source\repos\BlueTape\company-service\src\BlueTape.Company.API\bin\Debug\net8.0\System.Memory.Data.dll
C:\Users\<USER>\source\repos\BlueTape\company-service\src\BlueTape.Company.API\bin\Debug\net8.0\System.Runtime.Caching.dll
C:\Users\<USER>\source\repos\BlueTape\company-service\src\BlueTape.Company.API\bin\Debug\net8.0\System.Security.Cryptography.ProtectedData.dll
C:\Users\<USER>\source\repos\BlueTape\company-service\src\BlueTape.Company.API\bin\Debug\net8.0\System.Security.Permissions.dll
C:\Users\<USER>\source\repos\BlueTape\company-service\src\BlueTape.Company.API\bin\Debug\net8.0\System.Windows.Extensions.dll
C:\Users\<USER>\source\repos\BlueTape\company-service\src\BlueTape.Company.API\bin\Debug\net8.0\TinyHelpers.dll
C:\Users\<USER>\source\repos\BlueTape\company-service\src\BlueTape.Company.API\bin\Debug\net8.0\TinyHelpers.EntityFrameworkCore.dll
C:\Users\<USER>\source\repos\BlueTape\company-service\src\BlueTape.Company.API\bin\Debug\net8.0\XLParser.dll
C:\Users\<USER>\source\repos\BlueTape\company-service\src\BlueTape.Company.API\bin\Debug\net8.0\YamlDotNet.dll
C:\Users\<USER>\source\repos\BlueTape\company-service\src\BlueTape.Company.API\bin\Debug\net8.0\Z.EntityFramework.Extensions.EFCore.dll
C:\Users\<USER>\source\repos\BlueTape\company-service\src\BlueTape.Company.API\bin\Debug\net8.0\Z.EntityFramework.Plus.EFCore.dll
C:\Users\<USER>\source\repos\BlueTape\company-service\src\BlueTape.Company.API\bin\Debug\net8.0\Z.Expressions.Eval.dll
C:\Users\<USER>\source\repos\BlueTape\company-service\src\BlueTape.Company.API\bin\Debug\net8.0\ZstdSharp.dll
C:\Users\<USER>\source\repos\BlueTape\company-service\src\BlueTape.Company.API\bin\Debug\net8.0\runtimes\unix\lib\net6.0\Microsoft.Data.SqlClient.dll
C:\Users\<USER>\source\repos\BlueTape\company-service\src\BlueTape.Company.API\bin\Debug\net8.0\runtimes\win\lib\net6.0\Microsoft.Data.SqlClient.dll
C:\Users\<USER>\source\repos\BlueTape\company-service\src\BlueTape.Company.API\bin\Debug\net8.0\runtimes\win-arm\native\Microsoft.Data.SqlClient.SNI.dll
C:\Users\<USER>\source\repos\BlueTape\company-service\src\BlueTape.Company.API\bin\Debug\net8.0\runtimes\win-arm64\native\Microsoft.Data.SqlClient.SNI.dll
C:\Users\<USER>\source\repos\BlueTape\company-service\src\BlueTape.Company.API\bin\Debug\net8.0\runtimes\win-x64\native\Microsoft.Data.SqlClient.SNI.dll
C:\Users\<USER>\source\repos\BlueTape\company-service\src\BlueTape.Company.API\bin\Debug\net8.0\runtimes\win-x86\native\Microsoft.Data.SqlClient.SNI.dll
C:\Users\<USER>\source\repos\BlueTape\company-service\src\BlueTape.Company.API\bin\Debug\net8.0\runtimes\win\lib\net7.0\Microsoft.Win32.SystemEvents.dll
C:\Users\<USER>\source\repos\BlueTape\company-service\src\BlueTape.Company.API\bin\Debug\net8.0\runtimes\linux\native\libmongocrypt.so
C:\Users\<USER>\source\repos\BlueTape\company-service\src\BlueTape.Company.API\bin\Debug\net8.0\runtimes\osx\native\libmongocrypt.dylib
C:\Users\<USER>\source\repos\BlueTape\company-service\src\BlueTape.Company.API\bin\Debug\net8.0\runtimes\win\native\mongocrypt.dll
C:\Users\<USER>\source\repos\BlueTape\company-service\src\BlueTape.Company.API\bin\Debug\net8.0\runtimes\win\lib\net6.0\System.Diagnostics.PerformanceCounter.dll
C:\Users\<USER>\source\repos\BlueTape\company-service\src\BlueTape.Company.API\bin\Debug\net8.0\runtimes\win\lib\net7.0\System.Drawing.Common.dll
C:\Users\<USER>\source\repos\BlueTape\company-service\src\BlueTape.Company.API\bin\Debug\net8.0\runtimes\win\lib\net6.0\System.Runtime.Caching.dll
C:\Users\<USER>\source\repos\BlueTape\company-service\src\BlueTape.Company.API\bin\Debug\net8.0\runtimes\win\lib\net7.0\System.Security.Cryptography.ProtectedData.dll
C:\Users\<USER>\source\repos\BlueTape\company-service\src\BlueTape.Company.API\bin\Debug\net8.0\runtimes\win\lib\net7.0\System.Windows.Extensions.dll
C:\Users\<USER>\source\repos\BlueTape\company-service\src\BlueTape.Company.API\bin\Debug\net8.0\BlueTape.CashFlow.Application.dll
C:\Users\<USER>\source\repos\BlueTape\company-service\src\BlueTape.Company.API\bin\Debug\net8.0\BlueTape.CashFlow.Domain.dll
C:\Users\<USER>\source\repos\BlueTape\company-service\src\BlueTape.Company.API\bin\Debug\net8.0\BlueTape.Common.dll
C:\Users\<USER>\source\repos\BlueTape\company-service\src\BlueTape.Company.API\bin\Debug\net8.0\BlueTape.Company.Application.dll
C:\Users\<USER>\source\repos\BlueTape\company-service\src\BlueTape.Company.API\bin\Debug\net8.0\BlueTape.Company.Domain.dll
C:\Users\<USER>\source\repos\BlueTape\company-service\src\BlueTape.Company.API\bin\Debug\net8.0\BlueTape.DataAccess.EF.dll
C:\Users\<USER>\source\repos\BlueTape\company-service\src\BlueTape.Company.API\bin\Debug\net8.0\BlueTape.DataAccess.MongoDB.dll
C:\Users\<USER>\source\repos\BlueTape\company-service\src\BlueTape.Company.API\bin\Debug\net8.0\BlueTape.Document.Application.dll
C:\Users\<USER>\source\repos\BlueTape\company-service\src\BlueTape.Company.API\bin\Debug\net8.0\BlueTape.Document.Domain.dll
C:\Users\<USER>\source\repos\BlueTape\company-service\src\BlueTape.Company.API\bin\Debug\net8.0\BlueTape.Domain.dll
C:\Users\<USER>\source\repos\BlueTape\company-service\src\BlueTape.Company.API\bin\Debug\net8.0\BlueTape.LoanService.Client.dll
C:\Users\<USER>\source\repos\BlueTape\company-service\src\BlueTape.Company.API\bin\Debug\net8.0\BlueTape.OBS.Client.dll
C:\Users\<USER>\source\repos\BlueTape\company-service\src\BlueTape.Company.API\bin\Debug\net8.0\BlueTape.CashFlow.Application.pdb
C:\Users\<USER>\source\repos\BlueTape\company-service\src\BlueTape.Company.API\bin\Debug\net8.0\BlueTape.Company.Application.pdb
C:\Users\<USER>\source\repos\BlueTape\company-service\src\BlueTape.Company.API\bin\Debug\net8.0\BlueTape.Document.Application.pdb
C:\Users\<USER>\source\repos\BlueTape\company-service\src\BlueTape.Company.API\bin\Debug\net8.0\BlueTape.CashFlow.Domain.pdb
C:\Users\<USER>\source\repos\BlueTape\company-service\src\BlueTape.Company.API\bin\Debug\net8.0\BlueTape.Common.pdb
C:\Users\<USER>\source\repos\BlueTape\company-service\src\BlueTape.Company.API\bin\Debug\net8.0\BlueTape.Company.Domain.pdb
C:\Users\<USER>\source\repos\BlueTape\company-service\src\BlueTape.Company.API\bin\Debug\net8.0\BlueTape.DataAccess.EF.pdb
C:\Users\<USER>\source\repos\BlueTape\company-service\src\BlueTape.Company.API\bin\Debug\net8.0\BlueTape.DataAccess.MongoDB.pdb
C:\Users\<USER>\source\repos\BlueTape\company-service\src\BlueTape.Company.API\bin\Debug\net8.0\BlueTape.Document.Domain.pdb
C:\Users\<USER>\source\repos\BlueTape\company-service\src\BlueTape.Company.API\bin\Debug\net8.0\BlueTape.Domain.pdb
C:\Users\<USER>\source\repos\BlueTape\company-service\src\BlueTape.Company.API\bin\Debug\net8.0\BlueTape.LoanService.Client.pdb
C:\Users\<USER>\source\repos\BlueTape\company-service\src\BlueTape.Company.API\bin\Debug\net8.0\BlueTape.OBS.Client.pdb
C:\Users\<USER>\source\repos\BlueTape\company-service\src\BlueTape.Company.API\obj\Debug\net8.0\BlueTape.Company.API.csproj.AssemblyReference.cache
C:\Users\<USER>\source\repos\BlueTape\company-service\src\BlueTape.Company.API\obj\Debug\net8.0\BlueTape.Company.API.GeneratedMSBuildEditorConfig.editorconfig
C:\Users\<USER>\source\repos\BlueTape\company-service\src\BlueTape.Company.API\obj\Debug\net8.0\BlueTape.Company.API.AssemblyInfoInputs.cache
C:\Users\<USER>\source\repos\BlueTape\company-service\src\BlueTape.Company.API\obj\Debug\net8.0\BlueTape.Company.API.AssemblyInfo.cs
C:\Users\<USER>\source\repos\BlueTape\company-service\src\BlueTape.Company.API\obj\Debug\net8.0\BlueTape.Company.API.csproj.CoreCompileInputs.cache
C:\Users\<USER>\source\repos\BlueTape\company-service\src\BlueTape.Company.API\obj\Debug\net8.0\BlueTape.Company.API.MvcApplicationPartsAssemblyInfo.cs
C:\Users\<USER>\source\repos\BlueTape\company-service\src\BlueTape.Company.API\obj\Debug\net8.0\BlueTape.Company.API.MvcApplicationPartsAssemblyInfo.cache
C:\Users\<USER>\source\repos\BlueTape\company-service\src\BlueTape.Company.API\obj\Debug\net8.0\BlueTape.Company.API.sourcelink.json
C:\Users\<USER>\source\repos\BlueTape\company-service\src\BlueTape.Company.API\obj\Debug\net8.0\scopedcss\bundle\BlueTape.Company.API.styles.css
C:\Users\<USER>\source\repos\BlueTape\company-service\src\BlueTape.Company.API\obj\Debug\net8.0\staticwebassets.build.json
C:\Users\<USER>\source\repos\BlueTape\company-service\src\BlueTape.Company.API\obj\Debug\net8.0\staticwebassets.development.json
C:\Users\<USER>\source\repos\BlueTape\company-service\src\BlueTape.Company.API\obj\Debug\net8.0\staticwebassets.build.endpoints.json
C:\Users\<USER>\source\repos\BlueTape\company-service\src\BlueTape.Company.API\obj\Debug\net8.0\staticwebassets.upToDateCheck.txt
C:\Users\<USER>\source\repos\BlueTape\company-service\src\BlueTape.Company.API\obj\Debug\net8.0\BlueTape.C381DFC3.Up2Date
C:\Users\<USER>\source\repos\BlueTape\company-service\src\BlueTape.Company.API\obj\Debug\net8.0\BlueTape.Company.API.dll
C:\Users\<USER>\source\repos\BlueTape\company-service\src\BlueTape.Company.API\obj\Debug\net8.0\refint\BlueTape.Company.API.dll
C:\Users\<USER>\source\repos\BlueTape\company-service\src\BlueTape.Company.API\obj\Debug\net8.0\BlueTape.Company.API.pdb
C:\Users\<USER>\source\repos\BlueTape\company-service\src\BlueTape.Company.API\obj\Debug\net8.0\BlueTape.Company.API.genruntimeconfig.cache
C:\Users\<USER>\source\repos\BlueTape\company-service\src\BlueTape.Company.API\obj\Debug\net8.0\ref\BlueTape.Company.API.dll
C:\Users\<USER>\source\repos\BlueTape\company-service\src\BlueTape.Company.API\bin\Debug\net8.0\BlueTape.Plaid.Client.dll
C:\Users\<USER>\source\repos\BlueTape\company-service\src\BlueTape.Company.API\bin\Debug\net8.0\BlueTape.Plaid.Client.pdb
C:\Users\<USER>\source\repos\BlueTape\company-service\src\BlueTape.Company.API\bin\Debug\net8.0\BlueTape.Integrations.Plaid.dll
C:\Users\<USER>\source\repos\BlueTape\company-service\src\BlueTape.Company.API\bin\Debug\net8.0\BlueTape.Integrations.Plaid.Infrastructure.dll
C:\Users\<USER>\source\repos\BlueTape\company-service\src\BlueTape.Company.API\obj\Debug\net8.0\staticwebassets.build.json.cache
C:\Users\<USER>\source\repos\BlueTape\company-service\src\BlueTape.Company.API\obj\Debug\net8.0\rpswa.dswa.cache.json
C:\Users\<USER>\source\repos\BlueTape\company-service\src\BlueTape.Company.API\obj\Debug\net8.0\rjimswa.dswa.cache.json
C:\Users\<USER>\source\repos\BlueTape\company-service\src\BlueTape.Company.API\obj\Debug\net8.0\rjsmrazor.dswa.cache.json
C:\Users\<USER>\source\repos\BlueTape\company-service\src\BlueTape.Company.API\obj\Debug\net8.0\rjsmcshtml.dswa.cache.json
