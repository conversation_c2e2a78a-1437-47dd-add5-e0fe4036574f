# BlueTape Developer Documentation - Generic Connector API

Current documentation solution is based on [Slate](https://github.com/slatedocs/slate).

## Prequisities

Fork and clone [Slate repository](https://github.com/slatedocs/slate) and copy all documentation files to `source` folder.

> Notice to Windows users

Please note that cloning to Windows machines could mess up newlines and may notice errors when running `slate.sh`, you can ignore via:

```
git config --global core.autocrlf false
```

## Authoring

Document root is `source\index.html.md`, which considered as a Table-Of-Contents document. Add new topics and articles under `source\includes` folder in Markdown format and reference it in `index.html.md`.

## Build

Slate is Ruby-based. The best and easiest option to build and render static pages is using Docker (see included Dockerfile to make changes).

**Build image**

```
docker build . -t slatedocs/slate
```

**Render static pages**

```
docker run --rm --name slate --mount type=bind,src=$(pwd)/build,dst=/srv/slate/build -v $(pwd)/source:/srv/slate/source slatedocs/slate build
```

**Host Slate via Docker**

```
docker run --rm --name slate -p 4567:4567 -v $(pwd)/source:/srv/slate/source slatedocs/slate serve
```

Inspect `slate.sh` to get available commands.

## Output

The rendered static page files can be found in `build` folder after a successful execution.

> Slate could host documentation via Docker itself, of course.
