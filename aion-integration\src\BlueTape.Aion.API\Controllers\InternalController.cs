﻿using AutoMapper;
using BlueTape.Aion.API.Extensions;
using BlueTape.Aion.API.Models.Errors;
using BlueTape.Aion.Application.Abstractions;
using BlueTape.Aion.Application.Models.InternalTransfer;
using BlueTape.Aion.DataAccess.External.Constants;
using BlueTape.Aion.DataAccess.External.Models.InternalTransfer;
using BlueTape.Aion.Domain.Constants;
using BlueTape.Aion.Domain.Extensions;
using BlueTape.Integrations.Aion.Internal;
using FluentValidation;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using MongoDB.Bson;
using System.ComponentModel.DataAnnotations;
using System.Diagnostics.CodeAnalysis;

namespace BlueTape.Aion.API.Controllers;

[ApiController]
[Route("/api/internal")]
[ExcludeFromCodeCoverage]
[Authorize]
[ProducesResponseType(typeof(List<ErrorModel>), StatusCodes.Status400BadRequest)]
[ProducesResponseType(typeof(void), StatusCodes.Status401Unauthorized)]
[ProducesResponseType(typeof(List<ErrorModel>), StatusCodes.Status500InternalServerError)]
public class InternalController : ControllerBase
{
    private readonly IMapper _mapper;
    private readonly IInternalTransferService _internalTransferService;
    private readonly IValidator<CreateInternalModel> _createInternalValidator;

    public InternalController(
        IMapper mapper,
        IInternalTransferService internalTransferService,
        IValidator<CreateInternalModel> createInternalValidator)
    {
        _mapper = mapper;
        _internalTransferService = internalTransferService;
        _createInternalValidator = createInternalValidator;
    }

    [HttpPost]
    [ProducesResponseType(typeof(object), StatusCodes.Status200OK)]
    public async Task<IActionResult> CreateInternalTransfer(
        [FromHeader(Name = "PaymentRequestId")][Required] string paymentRequestId,
        [FromHeader(Name = ClientConstants.PaymentSubscriptionTypeHeader)][Required] string paymentSubscriptionType,
        [FromBody] CreateInternalModel createInternalModel,
        CancellationToken ctx)
    {
        if (Environment.GetEnvironmentVariable(EnvironmentConstants.Environment).IsEnvironmentDevOrBeta())
        {
            return Ok(GetBookTransferObj(createInternalModel));
        }

        await _createInternalValidator.ValidateAndThrowsManualAsync(createInternalModel, ctx);
        var model = _mapper.Map<CreateInternal>(createInternalModel);
        var result = await _internalTransferService.CreateInternalTransferAsync(model, paymentSubscriptionType, ctx);
        return Ok(result);
    }

    private static BookTransferObj GetBookTransferObj(CreateInternalModel createModel) =>
        new()
        {
            ObjectId = ObjectId.GenerateNewId().ToString(),
            Id = ObjectId.GenerateNewId().ToString(),
            Amount = createModel.Amount,
            Description = createModel.Description,
            TraceNumber = "T2404506540616PP1",
            Currency = "usd"
        };
}