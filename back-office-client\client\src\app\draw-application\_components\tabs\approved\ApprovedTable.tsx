import type { ColumnsType } from 'antd/es/table'
import { useTranslation } from 'react-i18next'
import { useCallback, useMemo } from 'react'
import { produce } from 'immer'

import DrawApplicationTableFilter, {
  type IFilterState,
} from '@/app/draw-application/_components/tabs/components/DrawApplicationTableFilter'
import { drawApplicationTableColumns } from '@/app/draw-application/_components/tabs/utils'
import { DrawApplicationStatus } from '@/globals/types'
import CentredSpinner from '@/components/common/CentredSpinner'
import Spacer from '@/components/common/Spacer'
import { StyledTable } from '@/components/common/Table'
import { columnWithSorting, mapTableSorting } from '@/globals/utils'
import { useDrawApplicationListQuery } from '@/lib/redux/api/draw-application'
import type {
  IDrawApplicationListItem,
  IDrawApplicationListSortBy,
} from '@/lib/redux/api/draw-application/types'
import {
  useDrawApplicationFilterState,
  useDrawApplicationTableRowClick,
} from '@/app/draw-application/_components/tabs/hooks'
import {
  useSearchParamsTableState,
  useTableSortingPagination,
} from '@/globals/hooks'

interface IProps {
  isActive: boolean
  isTabsMounted: boolean
}

const ApprovedTable = ({ isActive, isTabsMounted }: IProps): JSX.Element => {
  const { t } = useTranslation()

  const [filters, setListFilters] = useDrawApplicationFilterState(
    isActive,
    !isTabsMounted,
  )

  const [tableState, setTableState] =
    useSearchParamsTableState<IDrawApplicationListSortBy>(
      isActive,
      !isTabsMounted,
    )

  const { pagination, sorting, onTableChange, useTablePagination } =
    useTableSortingPagination(tableState, setTableState)

  const {
    isLoading,
    data: tableData,
    isFetching,
  } = useDrawApplicationListQuery({
    status: [DrawApplicationStatus.APPROVED],
    ...filters,
    ...pagination,
    ...sorting,
    sortOrder: mapTableSorting(sorting.sortOrder),
    refetchKey: 'approved',
  })

  const tablePagination = useTablePagination(tableData?.totalCount ?? 0)

  const columns = useMemo<ColumnsType<IDrawApplicationListItem>>(
    () => [
      drawApplicationTableColumns.company(t),
      columnWithSorting(
        drawApplicationTableColumns.automatedDecision(t),
        sorting,
      ),
      drawApplicationTableColumns.type(t),
      drawApplicationTableColumns.approvalType(t),
      drawApplicationTableColumns.submissionDate(t),
      drawApplicationTableColumns.decisionDate(t),
      columnWithSorting(drawApplicationTableColumns.drawAmount(t), sorting),
      drawApplicationTableColumns.viewLink(t),
    ],
    [t, sorting],
  )

  const onFilterChange = useCallback(
    (updater: (prevState: IFilterState) => IFilterState) => {
      setListFilters(updater)
      setTableState(
        produce((state) => {
          state.pageNumber = 1
        }),
      )
    },
    [setListFilters, setTableState],
  )

  const { onRowClick, rowClassName } = useDrawApplicationTableRowClick()

  if (isLoading) {
    return <CentredSpinner />
  }

  return (
    <>
      <Spacer height={28} />
      <DrawApplicationTableFilter
        onFilterChange={onFilterChange}
        defaultValue={filters}
      />
      <Spacer height={24} />
      <StyledTable<IDrawApplicationListItem>
        columns={columns}
        dataSource={tableData?.result}
        loading={isFetching}
        pagination={tablePagination}
        onChange={onTableChange}
        rowKey={(record) => record.id}
        scroll={{ x: 'max-content' }}
        rowClassName={rowClassName}
        onRow={(record) => ({
          onClick: onRowClick(record),
        })}
        data-testid="drawApplicationListApprovedTable"
      />
    </>
  )
}

export default ApprovedTable
