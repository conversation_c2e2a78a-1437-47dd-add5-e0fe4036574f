import React, { useState } from 'react'
import { Col, Flex } from 'antd'
import { useTranslation } from 'react-i18next'
import { useParams } from 'next/navigation'

import PercentOwnedDescription from '@/app/ar-advance-application/[application_id]/detailed/_components/tabs/kyc/components/PercentOwnedDescription'
import InstantIdSsnDescription from '@/app/ar-advance-application/[application_id]/detailed/_components/tabs/kyc/components/InstantIdSsnDescription'
import RiskDescription from '@/app/ar-advance-application/[application_id]/detailed/_components/tabs/kyc/components/RiskDescription'
import type { IArAdvanceApplicationOwnersData } from '@/lib/redux/api/ar-advance-application/types'
import NexisLexisViewer from '@/app/line-of-credit/[application_id]/detailed/_components/external/NexisLexisViewer'
import Spacer from '@/components/common/Spacer'
import { NexisLexisDataType } from '@/app/line-of-credit/[application_id]/detailed/_types'
import StyledTitle from '@/components/common/typography/StyledTitle'
import OtherBluetapeProductsDescription from '@/app/ar-advance-application/[application_id]/detailed/_components/tabs/kyc/components/other-bluetape-products/OtherBluetapeProductsDescription'
import { StyledButton } from '@/components/common/Button'
import OtherBluetapeProductsModal from '@/app/ar-advance-application/[application_id]/detailed/_components/tabs/kyc/components/other-bluetape-products/OtherBluetapeProductsModal'
import { AutomatedDecision } from '@/globals/types'

interface IProps {
  data: IArAdvanceApplicationOwnersData
  companyId: string
}

const IndividualOwnerData = ({ data, companyId }: IProps): JSX.Element => {
  const { t } = useTranslation<string | undefined>()

  const [isOtherBtProductsModalOpened, setIsOtherBtProductsModalOpened] =
    useState(false)

  const router = useParams()
  const applicationId = router.application_id as string

  return (
    <>
      <Flex gap={30} justify="space-between">
        <Col flex="1">
          <StyledTitle level={5} $text={data.ownerName ?? t('na')} />
          <Spacer height={24} />
          <PercentOwnedDescription
            percentOwned={data.percentOwned}
            principalOwner={data.principalOwner}
            ownership={t(`lineOfCredit.detailPage.tabs.ownerType.individual`)}
          />

          <Spacer height={16} />
          <NexisLexisViewer
            type={NexisLexisDataType.FRAUD_POINT}
            applicationId={applicationId}
            ownerIdentifier={data.ownerIdentifier}
          />
        </Col>
        <Col flex="1">
          <StyledTitle
            level={5}
            $text={t(
              'arAdvanceApplication.page.detailed.tabs.kyc.otherBtProducts',
            )}
          />
          <Spacer height={24} />
          <OtherBluetapeProductsDescription
            otherBtProductsOrApplications={data.otherBtProductsOrApplications}
          />
          <Spacer height={24} />
          {data.otherBtProductsOrApplications.result ===
            AutomatedDecision.SOFT_FAIL && (
            <>
              <StyledButton
                ghost
                type="primary"
                onClick={() => setIsOtherBtProductsModalOpened(true)}
              >
                {t('arAdvanceApplication.page.detailed.tabs.kyc.showDetails')}
              </StyledButton>
              <OtherBluetapeProductsModal
                isOpened={isOtherBtProductsModalOpened}
                close={() => setIsOtherBtProductsModalOpened(false)}
                companyId={companyId}
                ssnHash={data.ssnHash}
              />
            </>
          )}
        </Col>
      </Flex>
      <Spacer height={32} />
      <Col flex="1">
        <InstantIdSsnDescription data={data} />
        <NexisLexisViewer
          type={NexisLexisDataType.EMAIL_AGE}
          applicationId={applicationId}
          ownerIdentifier={data.ownerIdentifier}
        />
        <Spacer height={20} />
      </Col>
      <Col flex="1">
        <RiskDescription data={data} />
      </Col>
    </>
  )
}

export default IndividualOwnerData
