﻿using BlueTape.Common.ExceptionHandling.Exceptions.Base;
using System.Net;
using System.Runtime.Serialization;

namespace BueTape.Aion.Infrastructure.Exceptions;

[Serializable]
public class RequestNullException : DomainException
{
    public RequestNullException(string message, HttpStatusCode statusCode = HttpStatusCode.BadRequest) : base(message, statusCode)
    {
    }

    protected RequestNullException(SerializationInfo info, StreamingContext context) : base(info, context)
    {
    }

    public override string Code => ErrorCodes.RequestNull;
}