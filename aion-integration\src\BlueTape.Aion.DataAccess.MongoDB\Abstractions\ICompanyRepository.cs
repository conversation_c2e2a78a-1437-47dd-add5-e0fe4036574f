﻿using BlueTape.Aion.Domain.DTOs.Company;

namespace BlueTape.Aion.DataAccess.MongoDB.Abstractions;

public interface ICompanyRepository
{
    Task<CompanyDto> GetByCompanyId(string companyId, CancellationToken cancellationToken);
    Task UpdateAionSettingsAsync(CompanyDto company, CancellationToken cancellationToken);
    Task UpdateCompanyNameAsync(CompanyDto company, CancellationToken cancellationToken);
}