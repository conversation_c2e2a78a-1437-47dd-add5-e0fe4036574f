using System.Diagnostics.CodeAnalysis;
using BlueTape.Aion.Application.Models.Ach.Pull.Response;
using BlueTape.Aion.Application.Models.Instant;
using BlueTape.Aion.DataAccess.External.Models.InternalTransfer;
using BlueTape.Integrations.Aion.Ach.CreateAchTransfer;
using BlueTape.Integrations.Aion.Ach.CreateWireTransfer.Response;
using BlueTape.Integrations.Aion.Internal;
using MongoDB.Bson;

namespace BlueTape.Aion.Application.SandboxDataHelper;

[ExcludeFromCodeCoverage]
public static class SandboxTransferData
{
    public static CreateInstantTransferResponseModel GetInstantResponseModel(CreateAchModel createModel)
    {
        var model = new CreateInstantTransferResponseModel
        {
            ResponseMessage = string.Empty,
            Result = true,
            InstantTransfer = new InstantTransferObjectItemResponseModel
            {
                Amount = createModel.Amount,
                FeeAmount = 1.22m,
                CreatedAt = DateTime.UtcNow,
                UpdatedAt = DateTime.UtcNow,
                Status = "Pending",
                StatusMessage = "Transaction is pending",
                InitiatedByUserName = "John Doe",
                ContextIdentifier = "Context123",
                TransactionType = "instant",
                FromAccountNumber = createModel.Receiver.BankAccountId,
                AionSource = "AionSource123",
                PaymentId = ObjectId.GenerateNewId().ToString(),
                SenderDescription = "Sender Description",
                ReceiverDescription = "Receiver Description",
                UserNote = "User Note",
                Purpose = "Payment for services",
                LastModifiedAt = DateTime.UtcNow,
                ProviderStatus = "Pending",
                RtpPaymentInfo = new RtpPaymentInfoModel()
                {
                    AccountNumber = ObjectId.GenerateNewId().ToString(),
                    Amount = createModel.Amount,
                    Direction = "outbound",
                    Status = "Pending",
                    ReferenceId = ObjectId.GenerateNewId().ToString(),
                    Creditor = new CreditorModel()
                    {
                        AccountNumber = ObjectId.GenerateNewId().ToString(),
                        Name = "John Doe",
                        RoutingNumber = ObjectId.GenerateNewId().ToString(),
                    },
                    Debtor = new DebtorModel()
                    {
                        AccountNumber = ObjectId.GenerateNewId().ToString(),
                        Name = "Jane Smith",
                        RoutingNumber = ObjectId.GenerateNewId().ToString(),
                    },
                    Network = new NetworkModel()
                    {
                        CreatedAt = DateTime.UtcNow,
                        Currency = "USD",
                        MessageId = ObjectId.GenerateNewId().ToString(),
                        InterbankSettlementDate = DateTime.UtcNow,
                    },
                    Source = "Aion",
                    ClientIdentifier = "Client123",
                    PaymentType = "InstantTransfer",
                    OriginalPaymentId = ObjectId.GenerateNewId().ToString(),
                    RtpPaymentId = ObjectId.GenerateNewId().ToString(),
                }
            }
        };
        model.InstantTransfer.ProviderStatus = "Pending";
        return model;
    }
    
    public static CreateWireTransferResponseModel GetWireResponseModel(CreateAchModel createModel)
    {
        var model = new CreateWireTransferResponseModel()
        {
            ResponseMessage = string.Empty,
            Result = true,
            WireTransferObj = new WireTransferObjItemModel()
            {
                Id = ObjectId.GenerateNewId().ToString(),
                AccountId = ObjectId.GenerateNewId().ToString(),
                Amount = createModel.Amount.ToString(),
                CounterpartyId = ObjectId.GenerateNewId().ToString(),
                Direction = "Ach",
                Originator = new FinancialInstitutionModel()
                {
                    Address1 = "123 Main St",
                    Address2 = "Suite 100",
                    Address3 = "Suite 200",
                    Name = "John Doe",
                    IdCode = "*********",
                    Identifier = "*********"
                },
                ReferenceId = Guid.NewGuid().ToString(),
                EffectiveDate = DateTime.UtcNow.ToString("yyMMdd"),
                FeeAmount = 12.34m,
                Status = "Pending",
                DebitStatus = false,
                StatusMessage = "Transaction is pending",
                SendEmail = false,
                InitiatedByUserName = "John Doe",
                InitiatedBy = "System",
                ContextIdentifier = "Context123",
                PaymentMethodId = ObjectId.GenerateNewId().ToString(),
                CreatedAt = DateTime.UtcNow,
                UpdatedAt = DateTime.UtcNow,
                AccountNumber = createModel.Receiver.BankAccountId,
            }
        };
        model.WireTransferObj.ProviderStatus = "Pending";
        return model;
    }

    public static CreateAchResponseModel GetAchResponseModel(CreateAchModel createModel) =>
        new()
        {
            ResponseMessage = string.Empty,
            Result = true,
            AchObj = new AchObjResponseModel
            {
                Id = ObjectId.GenerateNewId().ToString(),
                AccountId = ObjectId.GenerateNewId().ToString(),
                Amount = createModel.Amount.ToString(),
                CounterpartyId = ObjectId.GenerateNewId().ToString(),
                Description = createModel.Description,
                Direction = "Ach",
                ReasonData = "Everything is fine",
                TraceNumber = "T2404506540616PP1",
                Originator = new OriginatorResponseModel
                {
                    AccountNumber = createModel.OriginatorAccountCode.ToString(),
                    AccountType = createModel.OriginatorAccountCode.ToString(),
                },
                Receiver = new ReceiverResponseModel
                {
                    AccountNumber = createModel.Receiver.BankAccountId
                },
                ReferenceId = Guid.NewGuid().ToString(),
                EffectiveDate = DateTime.UtcNow.ToString("yyMMdd")
            }
        };
    
    public static BookTransferObj GetInternalTransferObj(CreateInternalModel createModel) =>
        new()
        {
            ObjectId = ObjectId.GenerateNewId().ToString(),
            Id = ObjectId.GenerateNewId().ToString(),
            Amount = createModel.Amount,
            Description = createModel.Description,
            TraceNumber = "T2404506540616PP1",
            Currency = "usd"
        };
}