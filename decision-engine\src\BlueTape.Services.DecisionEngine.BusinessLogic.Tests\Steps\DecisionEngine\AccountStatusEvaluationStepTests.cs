﻿using BlueTape.Services.DecisionEngine.BusinessLogic.Abstractions.Services;
using BlueTape.Services.DecisionEngine.BusinessLogic.Constants;
using BlueTape.Services.DecisionEngine.BusinessLogic.Models.Credit;
using BlueTape.Services.DecisionEngine.BusinessLogic.Models.CreditApplication;
using BlueTape.Services.DecisionEngine.BusinessLogic.Models.DecisionEngineStep;
using BlueTape.Services.DecisionEngine.BusinessLogic.Models.StepInputs.DecisionEngine;
using BlueTape.Services.DecisionEngine.BusinessLogic.Steps.DecisionEngine;
using BlueTape.Services.DecisionEngine.DataAccess.External.Abstractions.Providers;
using BlueTape.Services.DecisionEngine.Infrastructure.Exceptions;
using BlueTape.Services.DecisionEngine.PolicyRulesFramework.Domain.Enums;
using BlueTape.Services.DecisionEngine.PolicyRulesFramework.Domain.Models.Options;
using BlueTape.Services.DecisionEngine.PolicyRulesFramework.PolicyRulesEngine.Abstractions.Providers;
using Microsoft.Extensions.Logging;

namespace BlueTape.Services.DecisionEngine.BusinessLogic.Tests.Steps.DecisionEngine;

public class AccountStatusEvaluationStepTests
{
    private readonly IOnBoardingService _onBoardingServiceMock;
    private readonly IPolicyRulesProvider _policyRulesProviderMock;
    private readonly IStepHandlingService _stepHandlingServiceMock;
    private readonly ILoanService _loanServiceMock;
    private readonly AccountStatusEvaluationStep _accountStatusEvaluationStep;

    public AccountStatusEvaluationStepTests()
    {
        _onBoardingServiceMock = Substitute.For<IOnBoardingService>();
        _policyRulesProviderMock = Substitute.For<IPolicyRulesProvider>();
        _stepHandlingServiceMock = Substitute.For<IStepHandlingService>();
        _loanServiceMock = Substitute.For<ILoanService>();
        ILogger<AccountStatusEvaluationStep> loggerMock = Substitute.For<ILogger<AccountStatusEvaluationStep>>();
        var traceIdProviderMock = Substitute.For<ITraceIdProvider>();

        _accountStatusEvaluationStep = new AccountStatusEvaluationStep(
            traceIdProviderMock,
            _stepHandlingServiceMock,
            loggerMock,
            _policyRulesProviderMock,
            _loanServiceMock,
            _onBoardingServiceMock);
    }

    [Fact]
    public async Task Process_ReturnsDecisionEngineStepModel()
    {
        var input = new StepInputBase { ExecutionId = "test-execution-id", CreditApplicationId = "test-credit-application-id" };
        var expectedResult = new DecisionEngineStepModel() { ExecutionId = input.ExecutionId, CreditApplicationId = input.CreditApplicationId };
        var credit = new CreditModel()
        {
            Id = Guid.NewGuid(),
            CreditApplicationId = input.CreditApplicationId,
            CompanyId = "company_id",
        };

        _policyRulesProviderMock.GetPolicyConfiguration(Arg.Any<string>(), Arg.Any<string>()).Returns(new PolicyConfigurationOptions()
        {
            Enabled = true,
        });
        _onBoardingServiceMock.GetCreditApplicationById(Arg.Any<string>(), Arg.Any<CancellationToken>())
            .Returns(new CreditApplicationModel { Id = input.CreditApplicationId, CompanyId = "company_id" });
        _onBoardingServiceMock.CreateDecisionEngineStep(Arg.Any<CreateDecisionEngineStepModel>(), default).Returns(new DecisionEngineStepModel { Id = "test-step-id" });
        _onBoardingServiceMock.GetLatestDecisionEngineExecutionsByCompanyId("company_id", default).Returns(new List<DecisionEngineStepModel>()
        {
            new()
            {
                Step = StepsImplementationConstants.KybStep,
                Status = ComparisonResult.Pass.ToString(),
                Results = new List<DecisionEngineStepResultModel>()
                {
                    new()
                    {
                        Result = ComparisonResult.Pass.ToString()
                    }
                }
            },
            new()
            {
                Step = StepsImplementationConstants.KycStep,
                Status = ComparisonResult.HardFail.ToString(),
                Results = new List<DecisionEngineStepResultModel>()
                {
                    new()
                    {
                        Result = ComparisonResult.HardFail.ToString(),
                        ManualResult = ComparisonResult.Pass.ToString(),
                    },
                    new()
                    {
                        Result = ComparisonResult.Pass.ToString()
                    }
                }
            },
            new()
            {
                Step = StepsImplementationConstants.PreliminaryStep,
                Status = ComparisonResult.SoftFail.ToString(),
                Results = new List<DecisionEngineStepResultModel>()
                {
                    new()
                    {
                        Result = ComparisonResult.SoftFail.ToString(),
                    },
                    new()
                    {
                        Result = ComparisonResult.Pass.ToString(),
                    }
                }
            },
            new()
            {
                Step = StepsImplementationConstants.InitializationStep,
            }
        });
        _loanServiceMock.GetCreditsByCompanyId("company_id", false, default).Returns(new List<CreditModel>() { credit });
        _onBoardingServiceMock.PatchDecisionEngineStep("test-step-id", Arg.Is<UpdateDecisionEngineStepModel>(x => x.NewStatus == ComparisonResult.SoftFail.ToString()), default)
            .Returns(expectedResult);

        var result = await _accountStatusEvaluationStep.Process(input, default);

        result.CreditApplicationId.ShouldBe(input.CreditApplicationId);
        result.ExecutionId.ShouldBe(input.ExecutionId);

        await _onBoardingServiceMock.Received(1).CreateDecisionEngineStep(Arg.Any<CreateDecisionEngineStepModel>(), default);
        await _onBoardingServiceMock.Received(1).GetLatestDecisionEngineExecutionsByCompanyId("company_id", default);
        await _loanServiceMock.Received(1).GetCreditsByCompanyId("company_id", false, default);
        await _stepHandlingServiceMock.Received(3).HandleRefreshStatus(Arg.Any<string>(), credit, "company_id", Arg.Any<string>(), Arg.Any<string>(), default);
    }

    [Fact]
    public Task ProcessStep_CreditApplicationIsNull_ThrowsDtoNotFoundException()
    {
        var input = new StepInputBase { ExecutionId = "test-execution-id", CreditApplicationId = "test-credit-application-id" };

        _onBoardingServiceMock.GetCreditApplicationById(Arg.Any<string>(), Arg.Any<CancellationToken>())
            .Returns((CreditApplicationModel?)null);

        return Should.ThrowAsync<DtoNotFoundException>(() => _accountStatusEvaluationStep.Process(input, default));
    }

    [Fact]
    public async Task ProcessStep_PolicyIsNull_ReturnsDecisionEngineStepModel()
    {
        var input = new StepInputBase { ExecutionId = "test-execution-id", CreditApplicationId = "test-credit-application-id" };
        var creditApplication = new CreditApplicationModel { Id = "test-credit-application-id" };

        _onBoardingServiceMock.GetCreditApplicationById(Arg.Any<string>(), Arg.Any<CancellationToken>())
            .Returns(creditApplication);

        _policyRulesProviderMock.GetPolicyConfiguration(Arg.Any<string>(), Arg.Any<string>())
            .Returns((PolicyConfigurationOptions?)null);

        var result = await _accountStatusEvaluationStep.Process(input, default);

        result.CreditApplicationId.ShouldBe(creditApplication.Id);
        result.ExecutionId.ShouldBe(input.ExecutionId);
        result.Status.ShouldBe(ComparisonResult.Pass.ToString());
    }
}