@startuml Virtual Card Draw Issue Process (.NET) /1

title Virtual Card Draw Issue Process (.NET) /1

participant "OnBoarding Service" as obs #SkyBlue
queue "Draw Events Queue" as dq #PaleVioletRed
participant "LFS" as lfs #SkyBlue
participant "VCard Service" as vs #SkyBlue
database "PostgreSQL" as pgsql #Cyan
participant "CBW" as cbw #LightGray

autonumber

== Draw approved, issue virtual card ==

obs --> obs : Draw approved
obs -> dq : Place event\n""Draw.Approved""\nwith type\n""virtualCard""
dq -> lfs : Consume draw events
lfs -> vs : Issue virtual card
vs -> cbw
cbw --> vs
vs -> pgsql : Save VC to Db
vs --> lfs
lfs -> obs : Save virtual card id to draw approval
