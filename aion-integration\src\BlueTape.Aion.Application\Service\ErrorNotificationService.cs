﻿using BlueTape.Aion.Application.Abstractions;
using BlueTape.SNS.SlackNotification;
using BlueTape.SNS.SlackNotification.Models;
using BueTape.Aion.Infrastructure.Options;
using Microsoft.Extensions.Options;
using System.Globalization;
using System.Text;
using AchResponseObj = BlueTape.Aion.DataAccess.External.Models.CreateAchTransfer.Response.AchResponseObj;

namespace BlueTape.Aion.Application.Service;

public class ErrorNotificationService(
    ISnsEndpoint snsEndpoint,
    IOptions<AionReportOptions> options)
    : IErrorNotificationService
{
    private readonly AionReportOptions _aionReportOptions = options.Value;

    public async Task Notify(string message, string eventName, string eventSource, CancellationToken ctx)
    {
        var notification = new EventMessageBody
        {
            Message =
                message,
            EventLevel = EventLevel.Error,
            EventName = eventName,
            EventSource = eventSource,
            ServiceName = "AionService",
            TimeStamp = DateTime.UtcNow.ToString(CultureInfo.InvariantCulture),
        };

        await Notify(notification, ctx);
    }

    public Task Notify(EventMessageBody message, CancellationToken ctx)
    {
        var awsAccountId = Environment.GetEnvironmentVariable("LP_AWS_ACCOUNT") ??
                           Environment.GetEnvironmentVariable("LP_AWS_ACCOUNT");
        var region = Environment.GetEnvironmentVariable("AWS_REGION");

        var topic = $"arn:aws:sns:{region}:{awsAccountId}:{_aionReportOptions.ErrorSnsTopicName}";

        return snsEndpoint.PublishSlackNotificationAsync(
            message,
            topic,
            "AionService notification triggered",
            ctx);
    }

    public Task NotifyUnmatchedReturn(AchResponseObj achResponseObj, CancellationToken ctx)
    {
        return NotifyTransaction(achResponseObj, "ACH return transactions identified without corresponding original transactions.", ctx);
    }

    public Task NotifyReturnedTransaction(AchResponseObj achResponseObj, CancellationToken ctx)
    {
        return NotifyTransaction(achResponseObj, "ACH return transaction has been found.", ctx);
    }

    public async Task NotifyTransaction(AchResponseObj achResponseObj, string eventName, CancellationToken ctx)
    {
        var notification = new EventMessageBody
        {
            Message = FormatTransactions([achResponseObj]),
            EventLevel = EventLevel.Error,
            EventName = eventName,
            EventSource = "Reports Lambda",
            ServiceName = "AionService",
            TimeStamp = DateTime.UtcNow.ToString(CultureInfo.InvariantCulture),
            AwsAccountId = Environment.GetEnvironmentVariable("LP_AWS_ACCOUNT") ??
                           "Not provided in service"
        };

        await Notify(notification, ctx);
    }

    private static string FormatTransactions(List<AchResponseObj> transactions)
    {
        var formattedString = new StringBuilder();

        foreach (var transaction in transactions)
        {
            formattedString.AppendFormat("*ReasonCode*: {0}, *Error*: {1}, *TransactionNumber*: {2}, *CreatedAt*: {3}, *Status*: {4}, *Amount*: {5},  *OriginatorName*: {6}, *ReceiverName*: {7}\n",
                transaction.ReasonCode, transaction.Error, transaction.ReferenceId, transaction.CreatedAt,
                transaction.Status, transaction.Amount, transaction.OriginatorResponse?.Name, transaction.Receiver?.Name);
        }

        return formattedString.ToString();
    }
}