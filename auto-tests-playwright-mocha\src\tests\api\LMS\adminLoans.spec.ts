// import { expect } from '@playwright/test';
// import { test } from '../../test-utils';
// import sendLMSRequest from '../../../api/common/lms-send-request';
// import axios from "axios";

// const constants = JSON.parse(JSON.stringify(require('../../../constants/LMStestData.json')))

// test.describe(`LMS. Loans. API Test.`, async () => {
//     test(`Get Loans to sync with MongoDb. @lms`, async () => {
//         const response = await sendLMSRequest('get', 'Admin/Loans');
//         expect(response.status).toEqual(200);
//         expect(response.data).toEqual(expect.any(Array));
//     });

//     test(`Change loan status to defaulted or recovered. @lms`, async () => {
//         const requestBody = {
//             "status": "Defaulted",
//             "note": "Test"
//         };
//         const response = await axios.patch(`${process.env.LMS_BASE_URL}/Admin/Loans/${constants.loans[1].id}`, requestBody, {
//             headers: {
//                 accept: '*/*', 
//                 userId: `${constants.loans[1].ownerId}`, 
//                 'Content-Type': 'application/json-patch+json' 
//             },
//         });

//         expect(response.status).toEqual(200);
//     });

//     test(`Get Loans by array of Ids. @lms`, async () => {
//         const requestBody = {
//             "companyId": `${constants.loans[1].ownerId}`,
//             "amount": 100,
//             "loanTemplateId": `${constants.loanTemplates[0].loanTemplate}`
//         };
//         const response = await sendLMSRequest('post', 'Admin/Loans', requestBody);
//         expect(response.status).toEqual(200);
//         expect(response.data).toEqual(expect.any(Array));
//     });
//     test(`Array LoanReceivables by loan id. @lms`, async () => {
//         const response = await sendLMSRequest('get', `Admin/Loans/${constants.loans[1].id }/LoanReceivables`);
//         expect(response.status).toEqual(200);
//         expect(response.data).toEqual(expect.any(Array));
//         expect(response.data[0]).toHaveProperty('expectedAmount');
//         expect(response.data[0]).toHaveProperty('scheduleStatus');
//     });

//     test(`Change LatePaymentFee's expected amount if status is Pending. @lms`, async () => {
//         const requestBody = {
//             "status": "Canceled",
//             "note": "string",
//             "expectedAmount": 10
//         };
//         const response = await axios.patch(`${process.env.LMS_BASE_URL}/Admin/LatePaymentFee/${constants.latePaymentFees[0].id}`, requestBody, {
//             headers: {
//                 accept: 'text/plain',
//                 userId: `${constants.loans[1].ownerId}`,
//                 'Content-Type': 'application/json-patch+json'
//             },
//         });
//         expect(response.status).toEqual(200);
//         expect(response.statusText).toEqual('OK');
//     });
// });
