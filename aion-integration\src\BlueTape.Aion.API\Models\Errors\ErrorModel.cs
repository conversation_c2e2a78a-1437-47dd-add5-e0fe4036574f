#nullable enable
using System.Text.Json.Serialization;

namespace BlueTape.Aion.API.Models.Errors;

public class ErrorModel
{
    [JsonPropertyName("errorType")]
    public ErrorType ErrorType { get; set; }

    [JsonPropertyName("code")]
    public string Code { get; set; } = null!;

    [JsonPropertyName("reason")]
    public string Reason { get; set; } = null!;

    [JsonPropertyName("errorData")]
    public object? ErrorData { get; set; }
}