{"version": 3, "targets": {"net6.0": {"BlueTape.CashFlow.Domain/1.0.2": {"type": "package", "compile": {"lib/net6.0/BlueTape.CashFlow.Domain.dll": {}}, "runtime": {"lib/net6.0/BlueTape.CashFlow.Domain.dll": {}}}, "BlueTape.OBS/1.6.69": {"type": "package", "compile": {"lib/net6.0/BlueTape.OBS.dll": {}}, "runtime": {"lib/net6.0/BlueTape.OBS.dll": {}}}}}, "libraries": {"BlueTape.CashFlow.Domain/1.0.2": {"sha512": "Q+ZZvORniOmIs/p90EyQVbBzzfCeOSnEKkwfpc8nTL3YPXMZbKd1qKnLUgJBu9BqIiBqI6z4lrz96yKcn9UcGA==", "type": "package", "path": "bluetape.cashflow.domain/1.0.2", "files": [".nupkg.metadata", "bluetape.cashflow.domain.1.0.2.nupkg.sha512", "bluetape.cashflow.domain.nuspec", "lib/net6.0/BlueTape.CashFlow.Domain.dll"]}, "BlueTape.OBS/1.6.69": {"sha512": "x3TJInMARVQQf1jQ7bj3LCTpJQwNjplwg8FDFJXNeNcbG72RXxKxti78wDN9z0OdOm6DzAQMRHBqrkwESRgKXQ==", "type": "package", "path": "bluetape.obs/1.6.69", "files": [".nupkg.metadata", "bluetape.obs.1.6.69.nupkg.sha512", "bluetape.obs.nuspec", "lib/net6.0/BlueTape.OBS.dll"]}}, "projectFileDependencyGroups": {"net6.0": ["BlueTape.CashFlow.Domain >= 1.0.2", "BlueTape.OBS >= 1.6.69"]}, "packageFolders": {"C:\\Users\\<USER>\\.nuget\\packages\\": {}, "C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages": {}}, "project": {"version": "1.0.0", "restore": {"projectUniqueName": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\decision-engine\\src\\BlueTape.Services.DecisionEngine.Domain\\BlueTape.Services.DecisionEngine.Domain.csproj", "projectName": "BlueTape.Services.DecisionEngine.Domain", "projectPath": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\decision-engine\\src\\BlueTape.Services.DecisionEngine.Domain\\BlueTape.Services.DecisionEngine.Domain.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\decision-engine\\src\\BlueTape.Services.DecisionEngine.Domain\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\source\\repos\\BlueTape\\decision-engine\\NuGet.Config", "C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net6.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}, "https://nuget.pkg.github.com/bluetape-org/index.json": {}}, "frameworks": {"net6.0": {"targetAlias": "net6.0", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"net6.0": {"targetAlias": "net6.0", "dependencies": {"BlueTape.CashFlow.Domain": {"target": "Package", "version": "[1.0.2, )"}, "BlueTape.OBS": {"target": "Package", "version": "[1.6.69, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "downloadDependencies": [{"name": "Microsoft.AspNetCore.App.Ref", "version": "[6.0.36, 6.0.36]"}, {"name": "Microsoft.NETCore.App.Ref", "version": "[6.0.36, 6.0.36]"}, {"name": "Microsoft.WindowsDesktop.App.Ref", "version": "[6.0.36, 6.0.36]"}], "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.301\\RuntimeIdentifierGraph.json"}}}}