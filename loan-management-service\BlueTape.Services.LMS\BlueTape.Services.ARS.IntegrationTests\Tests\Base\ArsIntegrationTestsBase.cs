﻿using BlueTape.CompanyService.Common.Senders;
using BlueTape.DataAccess.Mongo.Contexts;
using BlueTape.LS.Domain.Enums;
using BlueTape.LS.DTOs.Enums;
using BlueTape.LS.DTOs.Loan;
using BlueTape.LS.DTOs.LoanReceivable;
using BlueTape.LS.DTOs.Payment;
using BlueTape.Notification.Sender.Abstractions;
using BlueTape.PaymentService.Senders;
using BlueTape.Services.ARS.IntegrationTests.Constants;
using BlueTape.Services.ARS.IntegrationTests.Entities.LoanTemplates;
using BlueTape.Services.ARS.IntegrationTests.Entities.SofraiResults;
using BlueTape.Services.ARS.Models.Models.AgingLoanReports;
using BlueTape.Services.ARS.Models.Models.AgingReports;
using BlueTape.Services.LMS.Application.Abstractions.Senders;
using BlueTape.Services.LMS.DataAccess.Contexts;
using BlueTape.Services.LMS.DataAccess.SOFR.Abstractions.ExternalServices;
using BlueTape.Services.LMS.DataAccess.SOFR.Entities.SecuredRates.Request;
using BlueTape.Utilities.Providers;
using Microsoft.AspNetCore.Mvc.Testing;
using Microsoft.AspNetCore.TestHost;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.DependencyInjection.Extensions;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Logging.Abstractions;
using Moq;
using Newtonsoft.Json;
using System.Text;

namespace BlueTape.Services.ARS.IntegrationTests.Tests.Base
{
    public class ArsIntegrationTestsBase : IDisposable
    {
        private readonly Mock<IDateProvider> _dateProviderMock = new();
        private readonly HttpClient _httpClient;
        private readonly WebApplicationFactory<Program> _webHost;
        protected readonly DatabaseContext _dbContext;

        public ArsIntegrationTestsBase()
        {
            var dateTime = DateTime.UtcNow;
            var date = DateOnly.FromDateTime(dateTime);
            _webHost = new WebApplicationFactory<Program>().WithWebHostBuilder(builder =>
            {
                builder.ConfigureTestServices(services =>
                {
                    var dbContextDescriptor = services.First(d =>
                        d.ServiceType == typeof(DbContextOptions<DatabaseContext>));

                    services.Remove(dbContextDescriptor);

                    services.AddDbContext<DatabaseContext>(options => { options.UseInMemoryDatabase("loan_db"); });

                    var securedRatesExternalServiceDescriptor = services.First(d =>
                        d.ServiceType == typeof(ISecuredRatesExternalService));

                    services.Remove(securedRatesExternalServiceDescriptor);

                    var securedRatesExternalServiceMock = new Mock<ISecuredRatesExternalService>();
                    var sofraiResult = SofraiResultEntities.PenaltyInterestSofraiResultEntity;

                    securedRatesExternalServiceMock.Setup(m =>
                        m.GetSecuredRates(It.IsAny<SecuredRatesRequestEntity>(), It.IsAny<CancellationToken>()))
                        .ReturnsAsync(sofraiResult);

                    services.AddSingleton(securedRatesExternalServiceMock.Object);

                    var dateProvider = services.SingleOrDefault(d => d.ServiceType == typeof(IDateProvider));

                    services.Remove(dateProvider);
                    services.AddSingleton<ILoggerFactory, NullLoggerFactory>();

                    _dateProviderMock.Setup(x => x.CurrentDate).Returns(date);

                    services.AddTransient(_ => _dateProviderMock.Object);

                    var accountStatusChangeQueue = services.SingleOrDefault(d => d.ServiceType == typeof(IAccountStatusChangeQueueSender));
                    services.Remove(accountStatusChangeQueue);
                    var accountStatusChangeQueueMock = new Mock<IAccountStatusChangeQueueSender>();
                    services.AddTransient(_ => accountStatusChangeQueueMock.Object);

                    var mongoDb = services.SingleOrDefault(d => d.ServiceType == typeof(ILmsMongoDBContext));
                    services.Remove(mongoDb);
                    var mongoDbMock = new Mock<ILmsMongoDBContext>();
                    services.AddTransient(_ => mongoDbMock.Object);

                    services.RemoveAll<IInvoiceSyncMessageSender>();
                    services.AddScoped(_ => new Mock<IInvoiceSyncMessageSender>().Object);

                    services.RemoveAll<IIhcRepaymentMessageSender>();
                    services.AddScoped(_ => new Mock<IIhcRepaymentMessageSender>().Object);

                    services.RemoveAll<IAzureNotificationSenderService>();
                    services.AddScoped(_ => new Mock<IAzureNotificationSenderService>().Object);
                });
            });

            _httpClient = _webHost.CreateClient();

            _dbContext = _webHost.Services.CreateScope().ServiceProvider.GetService<DatabaseContext>();

            _dbContext!.LoanTemplates!.RemoveRange(_dbContext.LoanTemplates);
            _dbContext.LoanTemplates.AddRange(LoanTemplateEntities.LoanTemplateEntitiesList);

            _dbContext.SaveChanges();
        }

        protected async Task<PaymentDto> Pay(Guid id, decimal amount)
        {
            var payment = new CreatePaymentDto
            {
                LoanId = id,
                Amount = amount
            };

            var json = JsonConvert.SerializeObject(payment);
            var data = new StringContent(json, Encoding.UTF8, "application/json");
            var response = await _httpClient.PostAsync($"{PathConstants.PayPath}", data);

            return JsonConvert.DeserializeObject<PaymentDto>(await response.Content.ReadAsStringAsync());
        }

        protected async Task<LoanDto> CreateAndStartLoan(CreateLoanDto createLoan)
        {
            var json = JsonConvert.SerializeObject(createLoan);
            var data = new StringContent(json, Encoding.UTF8, "application/json");
            var response = await _httpClient.PostAsync($"{PathConstants.LoanPath}", data);
            var loan = JsonConvert.DeserializeObject<LoanDto>(await response.Content.ReadAsStringAsync());

            await StartLoan(loan!.Id);

            return await GetLoanById(loan.Id);
        }

        protected Task StartLoan(Guid id)
        {
            var status = new { Status = InputLoanStatus.Started };
            var startLoanJson = JsonConvert.SerializeObject(status);
            var startLoanData = new StringContent(startLoanJson, Encoding.UTF8, "application/json");
            return _httpClient.PatchAsync($"{PathConstants.LoanPath}/{id}", startLoanData);
        }

        protected async Task<LoanDto> GetLoanById(Guid id)
        {
            var response = await _httpClient.GetAsync($"{PathConstants.LoanPath}/{id}");
            return JsonConvert.DeserializeObject<LoanDto>(await response.Content.ReadAsStringAsync());
        }

        protected async Task<IEnumerable<LoanDto>> GetLoans()
        {
            var response = await _httpClient.GetAsync($"{PathConstants.LoanPath}");
            return JsonConvert.DeserializeObject<IEnumerable<LoanDto>>(await response.Content.ReadAsStringAsync());
        }

        protected async Task ChangePaymentStatus(Guid id, InputPaymentStatus status)
        {
            var payment = new UpdatePaymentDto
            {
                Status = status
            };

            var json = JsonConvert.SerializeObject(payment);
            var data = new StringContent(json, Encoding.UTF8, "application/json");
            var response = await _httpClient.PatchAsync($"{PathConstants.PayPath}/{id}", data);

            await response.Content.ReadAsStringAsync();
        }

        protected async Task ChangePaymentStatus(Guid id, PaymentStatus status)
        {
            var payment = new UpdateAdminPaymentDto
            {
                Status = status,
                Note = string.Empty
            };

            _httpClient.DefaultRequestHeaders.Add("userId", new Guid().ToString());
            var json = JsonConvert.SerializeObject(payment);
            var data = new StringContent(json, Encoding.UTF8, "application/json");
            var response = await _httpClient.PatchAsync($"{PathConstants.Admin}/{PathConstants.PayPath}/{id}", data);

            await response.Content.ReadAsStringAsync();
        }

        protected async Task<AgingReport> GetAgingReportByCompanyId(string companyId)
        {
            var response = await _httpClient.GetAsync(
                $"{PathConstants.Admin}/{PathConstants.LoanPath}/{PathConstants.AgingListPath}?{PathConstants.CompanyId}={companyId}");
            return JsonConvert.DeserializeObject<AgingReport>(await response.Content.ReadAsStringAsync());
        }

        protected async Task<AgingReport> GetAgingReport()
        {
            var response = await _httpClient.GetAsync(
                $"{PathConstants.Admin}/{PathConstants.LoanPath}/{PathConstants.AgingListPath}");
            return JsonConvert.DeserializeObject<AgingReport>(await response.Content.ReadAsStringAsync());
        }

        public async Task<List<AgingLoanReport>> GetAgingLoanReportByCompanyId(string companyId)
        {
            var response = await _httpClient.GetAsync(
                $"{PathConstants.Admin}/{PathConstants.LoanPath}/{PathConstants.AgingListPath}/{PathConstants.CompanyPath}/{companyId}");
            return JsonConvert.DeserializeObject<List<AgingLoanReport>>(await response.Content.ReadAsStringAsync());
        }

        protected async Task<LoanReceivableDto> ChangeExpectedDate(DateOnly date, Guid id)
        {
            var payment = new UpdateLoanReceivableDateDto
            {
                Date = date,
                Note = string.Empty
            };

            _httpClient.DefaultRequestHeaders.Add("userId", new Guid().ToString());
            var json = JsonConvert.SerializeObject(payment);
            var data = new StringContent(json, Encoding.UTF8, "application/json");
            var response =
                await _httpClient.PatchAsync($"{PathConstants.Admin}/{PathConstants.LoanReceivablesPath}/{id}", data);

            return JsonConvert.DeserializeObject<LoanReceivableDto>(await response.Content.ReadAsStringAsync());
        }

        protected void ChangeCurrentDay(int days, DateOnly date)
        {
            var localDate = date.AddDays(days);
            _dateProviderMock.Setup(x => x.CurrentDate).Returns(localDate);
        }

        public void Dispose()
        {
            _dbContext.Database.EnsureDeleted();
            _dbContext.Dispose();
            _httpClient.Dispose();

            GC.SuppressFinalize(this);
        }
    }
}
