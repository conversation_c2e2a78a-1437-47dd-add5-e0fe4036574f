# Integration models

BlueTape provides API for the following operations:

- create / update a customer

- create / update a quote

- create an invoice

- return credit information for a customer

- create / update projects

BlueTape expects the following API to be implemented on integrator side:

- add payment to an invoice

- authorization result ( required only when quotes are used for approval)

- get all customers (optional)


There are two models supported by BlueTape:

## A. Invoice only
1. Create an invoice in BlueTape

2. BlueTape notifies the customer

3. The customer selects the payment method and pays

4. For packages with 100% advance rate BlueTape marks the invoice as paid. For packages with 80% or 90% advance rate BlueTape marks that invoice as “Partially Paid” for the amount of advance payment. During the final payment BlueTape marks the invoice as paid.

## B. Using quote for approval
1. Create a quote in BlueTape

2. BlueTape notifies the customer

3. Customer approves or reject the quote

4. BlueTape informs the integrator about the approval result

5. Create the invoice in BlueTape

6. For packages with 100% advance rate BlueTape marks the invoice as paid. For packages with 80% or 90% advance rate BlueTape marks that invoice as “Partially Paid” for the amount of advance payment. During the final payment BlueTape marks the invoice as paid.