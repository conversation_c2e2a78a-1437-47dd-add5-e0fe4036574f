﻿using BlueTape.Aion.DataAccess.External.Models.CreateAchTransfer.Response;
using System.Runtime.Serialization;
using System.Text.Json.Serialization;

namespace BlueTape.Aion.DataAccess.External.Models.AchTransfer.Response;

[DataContract]
public class GetACHTransferResponse : BaseAionPageAbleResponseModel
{
    [JsonPropertyName("achList")]
    public List<AchResponseObj> AchList { get; set; } = new();
}