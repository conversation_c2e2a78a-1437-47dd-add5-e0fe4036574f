import {BasePage} from '../../../../base.page';

export class AddCustomerModal extends BasePage {
    constructor(page){
        super(page);
    };

    containers = {
        selectCustomerContainer: this.page.locator('[class="css-1dbjc4n r-105ug2t"]'),
    };

    inputFields = {
        searchCustomer: this.page.locator('[data-testid="Search by contact name, business name, phone number, and Email..._input"]'),
        cellPhoneNumber: this.page.locator('//*[@data-testid="Cell phone number"]//input'),
        firstName: this.page.locator('//*[@data-testid="First name"]//input'),
        lastName: this.page.locator('//*[@data-testid="Last name"]//input'),
        displayName: this.page.locator('//*[@data-testid="Display name"]//input'),
        businessName: this.page.locator('//*[@data-testid="Business name"]//input'),
        emailAddress: this.page.locator('//*[@data-testid="Email Address"]//input'),
        businessAddress: this.page.locator('//*[@data-testid="Business address"]//input'),
        businessPhoneNumber: this.page.locator('//*[@data-testid="Business phone number"]//input'),
        note: this.page.locator('//*[@data-testid="Note"]//input'),
    };

    buttons = {
        uploadCustomer: this.page.locator('[data-testid="Upload_Customer_Btn"]'),
        saveInvite: this.page.locator('[data-testid="account_sidebar_footer_save_send_button"]'),
        save: this.page.locator('[data-testid="account_sidebar_footer_save_button"]'),
        cancel: this.page.locator('[data-testid="account_sidebar_footer_cancel_button"]'),
        addCustomer: this.page.locator('[data-testid="account_sidebar_create_first_name_input"]')
    };

    dropdowns = {
        selectCustomerListElement: this.containers.selectCustomerContainer.locator('.css-1dbjc4n.r-1loqt21'),
    };

    async selectFirstCustomer(){
        await super.clickFistOption(this.dropdowns.selectCustomerListElement);
    };

    async fillUpCustomerFields(cellPhoneNumber, firstName, emailAddress) {
        await this.inputFields.cellPhoneNumber.fill(cellPhoneNumber);
        await this.inputFields.firstName.fill(firstName);
        await this.inputFields.emailAddress.fill(emailAddress);
    };
}