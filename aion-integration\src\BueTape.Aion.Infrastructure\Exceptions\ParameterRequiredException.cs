﻿using BlueTape.Common.ExceptionHandling.Exceptions.Base;
using System.Net;
using System.Runtime.Serialization;

namespace BueTape.Aion.Infrastructure.Exceptions;

[Serializable]
public class ParameterRequiredException : DomainException
{
    public ParameterRequiredException(string parameterName, HttpStatusCode statusCode = HttpStatusCode.BadRequest) : base(BuildErrorMessage(parameterName), statusCode)
    {
    }

    protected ParameterRequiredException(SerializationInfo info, StreamingContext context) : base(info, context)
    {
    }

    private static string BuildErrorMessage(string parameterName)
    {
        return $"Parameter: {parameterName} not provided";
    }

    public override string Code => ErrorCodes.ParameterRequired;
}