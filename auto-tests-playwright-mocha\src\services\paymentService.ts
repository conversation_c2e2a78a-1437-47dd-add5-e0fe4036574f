﻿import {Browser, expect} from "@playwright/test";
import {Action} from "../database/autoTests/entities/AutoTestReport";
import {
    getPaymentRequestExist,
    getRequiredPaymentRequestById,
    getPaymentRequests,
    confirmPaymentRequest,
    CreatePaymentRequestDto,
    createManualPaymentRequestMessage,
    generateTabapayReport
} from '../api/paynow/send-pay-now-request';

import {
    findLoans
} from '../api/common/lms-send-request';
import {
    triggerPaymentScheduledJobWithDelay,
    transactionStatusUpdateJob,
    triggerCommandEventProcessorJob} from '../api/paynow/queue-ivents';
import {executePaymentRequest, createPaymentRequest} from '../api/paynow/tests-utils';
import {InvoiceRepository} from "../database/invoices/invoiceRepository";
import {OperationRepository} from "../database/operation/operationRepository";
import {LoanApplicationRepository} from "../database/loanApplication/loanApplicationRepository";
import {BackOfficeService} from "./backOfficeService";
import {CustomerService} from "./customerService";
import {AwsService} from "./AwsService";
import {ApproveDrawApplicationRequest} from "../api/back-office-decision-engine/requests/approveDrawApplicationRequest";
import {DrawApprovalStatusEnum} from "../api/back-office-decision-engine/drawApprovalStatusEnum";
import {BaseTest} from "../tests/test-utils";
import {Decimal128} from "mongodb";
import {RequestType} from "../enums/paymentRequestType";

const DEFAULT_ENV = 'beta';
const ENVIRONMENT = process.env.TEST_ENV || DEFAULT_ENV;

export interface TestParams {
    supplierEmail: string;
    supplierPassword: string;
    customerEmail: string;
    customerPassword: string;
    customerCompanyName: string;
    invoiceNumber: string;
    invoiceAmount: string;
    invoiceType: string;
    environment: string;
}

export class paymentService 
{
    private _invoiceRepository: InvoiceRepository
    private _operationRepository: OperationRepository
    private _loanApplicationRepository: LoanApplicationRepository
    private _backOfficeService: BackOfficeService
    private _customerService: CustomerService
    private _awsService: AwsService;
    
    constructor(invoiceRepository: InvoiceRepository, backOfficeService: BackOfficeService, customerService: CustomerService) {
        this._invoiceRepository = invoiceRepository
        this._operationRepository = new OperationRepository();
        this._loanApplicationRepository = new LoanApplicationRepository();
        this._backOfficeService = backOfficeService;
        this._customerService = customerService;
        this._awsService = new AwsService();
    }

    async executeManualPaymentRequestFlow(params: TestParams, browser: Browser, actionsPerTest: Action[], isCard: boolean = false )
    {
        actionsPerTest.push({description: `Send request to get payment request by invoiceId/Number: ${params.invoiceNumber}`})

        // Invoice not found by ID, so try to find by number
        let invoice = await this._invoiceRepository.getInvoiceById(params.invoiceNumber);

        if (!invoice) {
            // Invoice not found by ID, so try to find by number
            invoice = await this._invoiceRepository.getInvoiceByNumber(params.invoiceNumber);
        }

        if (!invoice) {
            throw new Error(`No invoices found for invoice ID/Number: ${params.invoiceNumber}`);
        }

        var invoiceId = invoice!._id.toString();

        let paymentRequests = await this.getAllPaymentRequestsByInvoice(invoiceId);
        // get min by date
        const advancePayment = [...paymentRequests].sort((a, b) => 
            new Date(a.createdAt).getTime() - new Date(b.createdAt).getTime())[0];

        const baseUrl = `https://${params.environment}.bluetape.com/`;
        const loan = (
            await findLoans({
                payableId: invoice._id.toString(),
                detailed: true,
            })
        )?.[0]

        var manualPayment = await this.getPaymentRequestByLmsIdV2(loan.id, 'CREATE.DRAW.REPAYMENT.MANUAL');
        if (!manualPayment) {
            await this.createPaymentManualPaymentRequest(invoiceId, invoice.material_subtotal/2);
            await BaseTest.delayOperation(5000)
            manualPayment = await this.getPaymentRequestByLmsIdV2(loan.id, 'CREATE.DRAW.REPAYMENT.MANUAL');
        }

        // Manual payment execution
        if (manualPayment.status != "Settled") {
            await executePaymentRequest(manualPayment.id)
        }

        var customerPayment = paymentRequests.filter(item => item.requestType === "InvoicePaymentV2")[0];
        if (!customerPayment) {
            if(isCard) {
                await this._customerService.payInvoiceViaCard(
                    baseUrl,
                    browser,
                    invoice.invoice_number,
                    params.customerEmail,
                    params.customerPassword,
                    actionsPerTest
                );
            } else {

            await this._customerService.payInvoiceViaAch(
                baseUrl,
                browser,
                invoice.invoice_number,
                params.customerEmail,
                params.customerPassword,
                actionsPerTest
            );
        }
            await BaseTest.delayOperation(10000)
        }

                        // Advance payment execution
                        if (advancePayment.status != "Settled") {
                            await executePaymentRequest(advancePayment.id)
                        }

                        if(isCard) {
                            // TODO need to check is it already generated
                            actionsPerTest.push({description: `Trigger tabapay report generation`})
                            await generateTabapayReport(invoice.invoice_number);
                            await BaseTest.delayOperation(5000)
                            // Set pull result to day back to allow processing
                            let operation = await this._operationRepository.getByOwnerId(invoiceId, 'invoice_payment');
                            const operationId = operation[0]._id.toString();
                            const currentPullResult = operation[0].metadata?.pullResult;

                            if (currentPullResult) {
                                // Parse the current date and subtract one day
                                const pullDate = new Date(currentPullResult);
                                pullDate.setDate(pullDate.getDate() - 1);
                                
                                const updateData = {
                                    "metadata.pullResult": pullDate.toISOString()
                                };
                                
                                await this._operationRepository.update(operationId, updateData);
                            }

                            var operationCheckLambda = `linqpal-microservices-${params.environment}-btOperationCheck`;
                            await this._awsService.invokeLambda(operationCheckLambda, 'RequestResponse');
                            await BaseTest.delayOperation(5000)
                        } else {
                            paymentRequests = await this.getAllPaymentRequestsByInvoice(invoiceId);
                            customerPayment = paymentRequests.filter(item => item.requestType === "InvoicePaymentV2")[0];
                            if (customerPayment.status != "Settled") {
                                await executePaymentRequest(customerPayment.id)
                            }
                        }
                
                        actionsPerTest.push({description: `Trigger operation check AWS lambda`})
                        var operationCheckLambda = `linqpal-microservices-${params.environment}-btOperationCheck`;
                        await this._awsService.invokeLambda(operationCheckLambda, 'RequestResponse');
                        await BaseTest.delayOperation(20000)
                
                        paymentRequests = await this.getAllPaymentRequestsByInvoice(invoiceId);
                        var finalPayment = paymentRequests.filter(item => item.requestType === "FactoringFinalPayment")[0];
                        if(!finalPayment) { 
                            throw new Error(`No final payment found for invoice ID: ${invoiceId}`);
                        }

                        if (finalPayment.status != "Settled") {
                            await executePaymentRequest(finalPayment.id)
        }
    }

    async executeFullFactoringManualPaymentRequestFlow(params: TestParams, browser: Browser, actionsPerTest: Action[] )
    {
        actionsPerTest.push({description: `Send request to get payment request by invoiceId/Number: ${params.invoiceNumber}`})

        // Invoice not found by ID, so try to find by number
        let invoice = await this._invoiceRepository.getInvoiceById(params.invoiceNumber);

        if (!invoice) {
            // Invoice not found by ID, so try to find by number
            invoice = await this._invoiceRepository.getInvoiceByNumber(params.invoiceNumber);
        }

        if (!invoice) {
            throw new Error(`No invoices found for invoice ID/Number: ${params.invoiceNumber}`);
        }

        var invoiceId = invoice!._id.toString();

        let paymentRequests = await this.getAllPaymentRequestsByInvoice(invoiceId);
        // get min by date
        const advancePayment = [...paymentRequests].sort((a, b) => 
            new Date(a.createdAt).getTime() - new Date(b.createdAt).getTime())[0];

        const baseUrl = `https://${params.environment}.bluetape.com/`;
        const loan = (
            await findLoans({
                payableId: invoice._id.toString(),
                detailed: true,
            })
        )?.[0]

        var manualPayment = await this.getPaymentRequestByLmsIdV2(loan.id, 'CREATE.DRAW.REPAYMENT.MANUAL');
        if (!manualPayment) {
            await this.createPaymentManualPaymentRequest(invoiceId, invoice.material_subtotal);
            await BaseTest.delayOperation(5000)
            manualPayment = await this.getPaymentRequestByLmsIdV2(loan.id, 'CREATE.DRAW.REPAYMENT.MANUAL');
        }

        // Manual payment execution
        if (manualPayment.status != "Settled") {
            await executePaymentRequest(manualPayment.id)
        }


                        // Advance payment execution
                        if (advancePayment.status != "Settled") {
                            await executePaymentRequest(advancePayment.id)
                        }
                
                        actionsPerTest.push({description: `Trigger operation check AWS lambda`})
                        var operationCheckLambda = `linqpal-microservices-${params.environment}-btOperationCheck`;
                        await this._awsService.invokeLambda(operationCheckLambda, 'RequestResponse');
                        await BaseTest.delayOperation(20000)
                
                        paymentRequests = await this.getAllPaymentRequestsByInvoice(invoiceId);
                        var finalPayment = paymentRequests.filter(item => item.requestType === "FactoringFinalPayment")[0];
                        if(!finalPayment) { 
                            throw new Error(`No final payment found for invoice ID: ${invoiceId}`);
                        }

                        if (finalPayment.status != "Settled") {
                            await executePaymentRequest(finalPayment.id)
        }
    }

    async executePaymentRequestFlow(params: TestParams, browser: Browser, actionsPerTest: Action[] )
    {
        actionsPerTest.push({description: `Send request to get payment request by invoiceId/Number: ${params.invoiceNumber}`})

        // Invoice not found by ID, so try to find by number
        let invoice = await this._invoiceRepository.getInvoiceById(params.invoiceNumber);

        if (!invoice) {
            // Invoice not found by ID, so try to find by number
            invoice = await this._invoiceRepository.getInvoiceByNumber(params.invoiceNumber);
        }

        if (!invoice) {
            throw new Error(`No invoices found for invoice ID/Number: ${params.invoiceNumber}`);
        }

        var invoiceId = invoice!._id.toString();

        let paymentRequests = await this.getAllPaymentRequestsByInvoice(invoiceId);
        // get min by date
        const firstPayment = [...paymentRequests].sort((a, b) => 
            new Date(a.createdAt).getTime() - new Date(b.createdAt).getTime())[0];

        var requestType = firstPayment.requestType;
        const baseUrl = `https://${params.environment}.bluetape.com/`;

                // Select payment method
                switch (requestType) {
                    case 'InvoicePayment':
                        await executePaymentRequest(firstPayment.id);
                        break;
                    case 'DrawDisbursement':
                        let loan = await this._loanApplicationRepository.getByInvoiceId(invoiceId);
                        if (!loan) {
                            throw new Error(`No loan application found for invoice ID: ${invoiceId}`);
                        }
                        let lmsId = loan.lms_id;
                        let repayment = await this.getPaymentRequestByLmsIdV2(lmsId);
                        if (!repayment) {
                            await this._customerService.payDrawViaAch(
                                baseUrl,
                                browser,
                                actionsPerTest,
                                invoice.invoice_number,
                                params.customerEmail,
                                params.customerPassword,
                            );
                            await BaseTest.delayOperation(10000)
                        }

                        // Advance payment execution
                        if (firstPayment.status != "Settled") {
                            await executePaymentRequest(firstPayment.id)
                        }

                        repayment = await this.getPaymentRequestByLmsIdV2(lmsId);
                        if (repayment.status != "Settled") {
                            await executePaymentRequest(repayment.id)
                        }
                
                        //actionsPerTest.push({description: `Trigger operation check AWS lambda`})
                        //var operationCheckLambda = `linqpal-microservices-${params.environment}-btOperationCheck`;
                        //await this._awsService.invokeLambda(operationCheckLambda, 'RequestResponse');
                        
                        actionsPerTest.push({description: `Trigger final payment AWS lambda`})
                        var finalPaymentLambda = `linqpal-microservices-${params.environment}-btFinalPayment`;
                        await this._awsService.invokeLambda(finalPaymentLambda, 'RequestResponse');
                
                        await BaseTest.delayOperation(20000)
                
                        paymentRequests = await this.getAllPaymentRequestsByInvoice(invoiceId);
                        var finalPayment = paymentRequests.filter(item => item.requestType === "FinalPaymentV2")[0];
                        if(!finalPayment) { 
                            throw new Error(`No final payment found for invoice ID: ${invoiceId}`);
                        }

                        if (finalPayment.status != "Settled"){
                            await executePaymentRequest(finalPayment.id)
                        }
                        break;
                    case 'FactoringDisbursement':

                        var customerPayment = paymentRequests.filter(item => item.requestType === "InvoicePaymentV2")[0];
                        if (!customerPayment) {
                            await this._customerService.payInvoiceViaAch(
                                baseUrl,
                                browser,
                                invoice.invoice_number,
                                params.customerEmail,
                                params.customerPassword,
                                actionsPerTest
                            );
                            await BaseTest.delayOperation(10000)
                        }

                        // Advance payment execution
                        if (firstPayment.status != "Settled") {
                            await executePaymentRequest(firstPayment.id)
                        }

                        paymentRequests = await this.getAllPaymentRequestsByInvoice(invoiceId);
                        customerPayment = paymentRequests.filter(item => item.requestType === "InvoicePaymentV2")[0];
                        if (customerPayment.status != "Settled") {
                            await executePaymentRequest(customerPayment.id)
                        }
                
                        actionsPerTest.push({description: `Trigger operation check AWS lambda`})
                        var operationCheckLambda = `linqpal-microservices-${params.environment}-btOperationCheck`;
                        await this._awsService.invokeLambda(operationCheckLambda, 'RequestResponse');
                        await BaseTest.delayOperation(20000)
                
                        paymentRequests = await this.getAllPaymentRequestsByInvoice(invoiceId);
                        var finalPayment = paymentRequests.filter(item => item.requestType === "FactoringFinalPayment")[0];
                        if(!finalPayment) { 
                            throw new Error(`No final payment found for invoice ID: ${invoiceId}`);
                        }

                        if (finalPayment.status != "Settled") {
                            await executePaymentRequest(finalPayment.id)
                        break;
                        }
                    default:
                        throw new Error(`Unsupported payment method: ${requestType}`);
                }
    }
    
    async makeInoviceSuccesfulyPaidViaAch(invoiceId: string, invoiceNumber: string, browser: Browser, login: string, password: string, actionsPerTest: Action[] )
    {
        var params: TestParams = {
            customerEmail: login,
            customerPassword: password,
            invoiceNumber: invoiceNumber,
            environment: ENVIRONMENT
        };

        await this.executePaymentRequestFlow(params, browser, actionsPerTest)
    }
    
    async makeInoviceSuccesfulyPaidViaBTC(invoiceId: string, invoiceNumber: string, browser: Browser, login: string, password: string, actionsPerTest: Action[], adminIdToken: any)
    {
        let invoice = await this._invoiceRepository.getInvoiceById(invoiceId);

        if (!invoice) {
            // Invoice not found by ID, so try to find by number
            invoice = await this._invoiceRepository.getInvoiceByNumber(invoiceNumber);
            invoiceId = invoice!._id.toString();
        }

        invoiceNumber = invoice!.invoice_number;

        await this._backOfficeService.approveDrawApprovalViaBackOffice(invoiceId, browser, actionsPerTest, adminIdToken);
        await BaseTest.delayOperation(60000)

        var params: TestParams = {
            customerEmail: login,
            customerPassword: password,
            invoiceNumber: invoiceNumber,
            environment: ENVIRONMENT
        };

        await this.executePaymentRequestFlow(params, browser, actionsPerTest);
    }

    async makeInoviceSuccesfulyPaidViaFactoring(invoiceId: string, invoiceNumber: string, browser: Browser, login: string, password: string, actionsPerTest: Action[], adminIdToken: any)
    {
        var params: TestParams = {
            customerEmail: login,
            customerPassword: password,
            invoiceNumber: invoiceNumber,
            environment: ENVIRONMENT
        };

        await this.executePaymentRequestFlow(params, browser, actionsPerTest);
    }

    async makeInoviceSuccesfulyPaidViaFactoringWithManualPayment(invoiceId: string, invoiceNumber: string, browser: Browser, login: string, password: string, actionsPerTest: Action[], adminIdToken: any)
    {
        var params: TestParams = {
            customerEmail: login,
            customerPassword: password,
            invoiceNumber: invoiceNumber,
            environment: ENVIRONMENT
        };

        await this.executeManualPaymentRequestFlow(params, browser, actionsPerTest);
    }

    async makeInoviceSuccesfulyPaidViaFactoringWith4Payments(invoiceId: string, invoiceNumber: string, browser: Browser, login: string, password: string, actionsPerTest: Action[], adminIdToken: any)
    {
        var params: TestParams = {
            customerEmail: login,
            customerPassword: password,
            invoiceNumber: invoiceNumber,
            environment: ENVIRONMENT
        };

        actionsPerTest.push({description: `Send request to get payment request by invoiceId/Number: ${params.invoiceNumber}`})

        // Invoice not found by ID, so try to find by number
        let invoice = await this._invoiceRepository.getInvoiceById(params.invoiceNumber);

        if (!invoice) {
            // Invoice not found by ID, so try to find by number
            invoice = await this._invoiceRepository.getInvoiceByNumber(params.invoiceNumber);
        }

        if (!invoice) {
            throw new Error(`No invoices found for invoice ID/Number: ${params.invoiceNumber}`);
        }

        var invoiceId = invoice!._id.toString();

        let paymentRequests = await this.getAllPaymentRequestsByInvoice(invoiceId);
        // get min by date
        const advancePayment = [...paymentRequests].sort((a, b) => 
            new Date(a.createdAt).getTime() - new Date(b.createdAt).getTime())[0];

        const baseUrl = `https://${params.environment}.bluetape.com/`;
        const loan = (
            await findLoans({
                payableId: invoice._id.toString(),
                detailed: true,
            })
        )?.[0]

        //1
        var manualPayment = await this.getPaymentRequestByLmsIdV2(loan.id, 'CREATE.DRAW.REPAYMENT.MANUAL');
        if (!manualPayment) {
            await this.createPaymentManualPaymentRequest(invoiceId, invoice.material_subtotal/4);
            await BaseTest.delayOperation(5000)
            manualPayment = await this.getPaymentRequestByLmsIdV2(loan.id, 'CREATE.DRAW.REPAYMENT.MANUAL');
        }

        // Manual payment execution
        if (manualPayment.status != "Settled") {
            await executePaymentRequest(manualPayment.id)
        }

        manualPayment = await this.getPaymentRequestByLmsIdV2(loan.id, 'CREATE.DRAW.REPAYMENT.MANUAL');
        if (manualPayment.status != "Settled") {
            throw new Error(`Manual payment not settled for invoice ID: ${invoiceId}`);
        }

        //2
        var manualPayment = await this.getPaymentRequestByLmsIdV2(loan.id, 'CREATE.DRAW.REPAYMENT.MANUAL');
        if (!manualPayment || manualPayment.status == "Settled") {
            await this.createPaymentManualPaymentRequest(invoiceId, invoice.material_subtotal/4);
            await BaseTest.delayOperation(5000)
            manualPayment = await this.getPaymentRequestByLmsIdV2(loan.id, 'CREATE.DRAW.REPAYMENT.MANUAL');
        }

        // Manual payment execution
        if (manualPayment.status != "Settled") {
            await executePaymentRequest(manualPayment.id)
        }

        manualPayment = await this.getPaymentRequestByLmsIdV2(loan.id, 'CREATE.DRAW.REPAYMENT.MANUAL');
        if (manualPayment.status != "Settled") {
            throw new Error(`Manual payment not settled for invoice ID: ${invoiceId}`);
        }

        // Advance payment execution
        if (advancePayment.status != "Settled") {
            await executePaymentRequest(advancePayment.id)
        }

        //3
        var customerPayment = paymentRequests.filter(item => item.requestType === "InvoicePaymentV2")[0];
        if (!customerPayment) {
            const payload = {
                amount: invoice.material_subtotal / 4, // Using 1/4 of the invoice amount as in previous manual payments
                drawId: loan.id
            };
            await createPaymentRequest( payload );
            await BaseTest.delayOperation(5000)
        }

        paymentRequests = await this.getAllPaymentRequestsByInvoice(invoiceId);
        customerPayment = paymentRequests.filter(item => item.requestType === "InvoicePaymentV2")[0];
        if (customerPayment.status != "Settled") {
                await executePaymentRequest(customerPayment.id)
        }

                //4
                paymentRequests = await this.getAllPaymentRequestsByInvoice(invoiceId);
                var customerPayment = paymentRequests
                .filter(item => item.requestType === "InvoicePaymentV2")
                .sort((a, b) => new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime())[0];
                if (!customerPayment || customerPayment.status == "Settled") {
                    const payload = {
                        amount: invoice.material_subtotal / 4, // Using 1/4 of the invoice amount as in previous manual payments
                        drawId: loan.id
                    };
                    await createPaymentRequest( payload );
                    await BaseTest.delayOperation(5000)
                }
        
                paymentRequests = await this.getAllPaymentRequestsByInvoice(invoiceId);
                customerPayment = paymentRequests
                .filter(item => item.requestType === "InvoicePaymentV2")
                .sort((a, b) => new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime())[0];
                
                if (customerPayment.status != "Settled") {
                        await executePaymentRequest(customerPayment.id)
                }
                
                        actionsPerTest.push({description: `Trigger operation check AWS lambda`})
                        var operationCheckLambda = `linqpal-microservices-${params.environment}-btOperationCheck`;
                        await this._awsService.invokeLambda(operationCheckLambda, 'RequestResponse');
                        await BaseTest.delayOperation(20000)
                
                        paymentRequests = await this.getAllPaymentRequestsByInvoice(invoiceId);
                        var finalPayment = paymentRequests.filter(item => item.requestType === "FactoringFinalPayment")[0];
                        if(!finalPayment) { 
                            throw new Error(`No final payment found for invoice ID: ${invoiceId}`);
                        }

                        if (finalPayment.status != "Settled") {
                            await executePaymentRequest(finalPayment.id)
        }
    }

    async makeInoviceSuccesfulyPaidViaFactoringWithManualPaymentAndCard(invoiceId: string, invoiceNumber: string, browser: Browser, login: string, password: string, actionsPerTest: Action[], adminIdToken: any)
    {
        var params: TestParams = {
            customerEmail: login,
            customerPassword: password,
            invoiceNumber: invoiceNumber,
            environment: ENVIRONMENT
        };

        await this.executeManualPaymentRequestFlow(params, browser, actionsPerTest, true);
    }

    async makeInoviceSuccesfulyPaidViaFactoringWithManualPaymentFully(invoiceId: string, invoiceNumber: string, browser: Browser, login: string, password: string, actionsPerTest: Action[], adminIdToken: any)
    {
        var params: TestParams = {
            customerEmail: login,
            customerPassword: password,
            invoiceNumber: invoiceNumber,
            environment: ENVIRONMENT
        };

        await this.executeFullFactoringManualPaymentRequestFlow(params, browser, actionsPerTest);
    }

    async getPaymentRequestByLmsId(lmsId: string, maxRetries = 4, delayMs = 15000) {
        let retries = 0;
        while (retries < maxRetries) {
        try {
            let currentDate = new Date();
            currentDate.setDate(currentDate.getDate() - 1);
            let year = currentDate.getFullYear();
            let month = String(currentDate.getMonth() + 1).padStart(2, '0'); // Months are zero-indexed
            let day = String(currentDate.getDate()).padStart(2, '0');
            let formattedDate = `${year}-${month}-${day}`;

            const response = await getPaymentRequests(formattedDate, 'CREATE.DRAW.REPAYMENT');
            
            const filteredpaymentRequests = response.filter(item =>
                item.paymentRequestDetails &&
                item.paymentRequestDetails.drawId &&
                item.paymentRequestDetails.drawId === lmsId
            );
            const paymentRequest = filteredpaymentRequests[0];

            if (!paymentRequest) {
                throw new Error('No matching payment request found');
            }

            const transactions = paymentRequest.transactions.sort((a, b) => 
                Number(a.sequenceNumber) - Number(b.sequenceNumber));
        return {paymentRequest, transactions};
    } catch (error) {
        console.log(`Attempt ${retries + 1} failed: ${error.message}`);
        retries++;

        if (retries < maxRetries) {
            console.log(`Retrying in ${delayMs}ms...`);
            await new Promise(resolve => setTimeout(resolve, delayMs));
        } else {
            console.log('All attempts failed');
            return { paymentRequest: null, transactions: null };
        }
    }
    }
}

    async getPaymentRequestByInvoice(invoiceId: string, requestType: string | null = null, maxRetries = 4, delayMs = 15000) {
        let retries = 0;
        while (retries < maxRetries) {
        try {
                const response = await getPaymentRequestExist(invoiceId);
                const status = response.response ? response.response.status : response.status;
                const data = response.response ? response.response.data : response.data;
            
                expect(status, `Unable to find payment request by invoiceId: ${invoiceId} responseStatus: ${status} body: ${JSON.stringify(data)}`).toEqual(200);
            
                const filteredpaymentRequests = data.filter(item => item.requestType === requestType);

                if (filteredpaymentRequests.length === 0) {
                    throw new Error('No matching payment request found');
                }

                const paymentRequest = filteredpaymentRequests[0];
                const transactions = paymentRequest.transactions.sort((a, b) => 
                    Number(a.sequenceNumber) - Number(b.sequenceNumber));

                return { paymentRequest, transactions };
        } catch (error) {
            console.log(`Attempt ${retries + 1} failed: ${error.message}`);
            retries++;

            if (retries < maxRetries) {
                console.log(`Retrying in ${delayMs}ms...`);
                await new Promise(resolve => setTimeout(resolve, delayMs));
            } else {
                console.log('All attempts failed');
                return { paymentRequest: null, transactions: null };
            }
        }
    }
    }

    async getAllPaymentRequestsByInvoice(invoiceId: string, maxRetries = 4, delayMs = 15000) {
        let retries = 0;
        while (retries < maxRetries) {
            try {
                const response = await getPaymentRequestExist(invoiceId);
                const status = response.response ? response.response.status : response.status;
                const data = response.response ? response.response.data : response.data;
            
                expect(status, `Unable to find payment request by invoiceId: ${invoiceId} responseStatus: ${status} body: ${JSON.stringify(data)}`).toEqual(200);
            
                if (!data || data.length === 0) {
                    throw new Error('No payment requests found');
                }
       
                return data;
            } catch (error) {
                console.log(`Attempt ${retries + 1} failed: ${error.message}`);
                retries++;
    
                if (retries < maxRetries) {
                    console.log(`Retrying in ${delayMs}ms...`);
                    await new Promise(resolve => setTimeout(resolve, delayMs));
                } else {
                    console.log('All attempts failed');
                    return null;
                }
            }
        }
    }

    async getPaymentRequestByLmsIdV2(lmsId: string, flowTempale = 'CREATE.DRAW.REPAYMENT', maxRetries = 2, delayMs = 15000) {
        let retries = 0;
        while (retries < maxRetries) {
        try {
            let currentDate = new Date();
            currentDate.setDate(currentDate.getDate() - 1);
            let year = currentDate.getFullYear();
            let month = String(currentDate.getMonth() + 1).padStart(2, '0'); // Months are zero-indexed
            let day = String(currentDate.getDate()).padStart(2, '0');
            let formattedDate = `${year}-${month}-${day}`;

            const response = await getPaymentRequests(formattedDate, flowTempale);
            
            const filteredpaymentRequests = response.filter(item =>
                item.paymentRequestDetails &&
                item.paymentRequestDetails.drawId &&
                item.paymentRequestDetails.drawId === lmsId
            );

            if (!filteredpaymentRequests.length) {
                throw new Error('No matching payment request found');
            }

            const sortedPaymentRequests = filteredpaymentRequests.sort((a, b) => 
                new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime()
            );

            const paymentRequest = sortedPaymentRequests[0];

            if (!paymentRequest) {
                throw new Error('No matching payment request found');
            }

        return paymentRequest;
    } catch (error) {
        console.log(`Attempt ${retries + 1} failed: ${error.message}`);
        retries++;

        if (retries < maxRetries) {
            console.log(`Retrying in ${delayMs}ms...`);
            await new Promise(resolve => setTimeout(resolve, delayMs));
        } else {
            console.log('All attempts failed');
            return null;
        }
    }
    }
}
    
    async getPaymentRequestById(id: string ) {
        try {
            const response = await getRequiredPaymentRequestById(id);
            const status = response.response ? response.response.status : response.status;
            const paymentRequest = response.response ? response.response.data : response.data;
            const transactions = paymentRequest.transactions.sort((a, b) => 
                Number(a.sequenceNumber) - Number(b.sequenceNumber));

            expect(status, 'Expected status code 201').toEqual(200);
        return {paymentRequest, transactions};
        } catch (error) {
            console.log('Error', error);
            return null;
        }
    }

    async createPaymentManualPaymentRequest(invoiceId: string, amount?: number) {
        try {
            const invoice = await this._invoiceRepository.getInvoiceById(invoiceId);
            if (!invoice) {
                throw new Error(`Invoice not found with ID: ${invoiceId}`);
            }

            const loan = (
                await findLoans({
                    payableId: invoice._id.toString(),
                    detailed: true,
                })
            )?.[0]

            if (!loan) {
                throw new Error(`Loan not found with payable ID: ${invoiceId}`);
            }

            const finalAmount = amount !== undefined ? amount.toString() : 
                (invoice.amount instanceof Decimal128 ? invoice.amount.toString() : invoice.amount.toString());

            const payload: CreatePaymentRequestDto = {
                accountCode: 'LOCKBOXCOLLECTION',
                externalReferenceNumber: 'AUTOTEST-123456', 
                drawId: loan.id,
                amount: finalAmount,
                currency: 'USD',
                manualPaymentMethod: 'Wire',
                paymentDate: new Date().toISOString(),
            };

            await createManualPaymentRequestMessage(payload);
        } catch (error) {
            console.error('Error creating payment request:', error);
            throw error;
        }
    }
}
