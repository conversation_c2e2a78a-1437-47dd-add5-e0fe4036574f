// TODO: Save with origin name
// add check for file name
// add tests for PDF FIELDS checking

const fs = require('fs');
import {expect} from '@playwright/test';
import {test, BaseTest} from '../../../test-utils';
import {PageManager} from '../../../../objects/pages/page-manager';

test.use({storageState: {cookies: [], origins: []}});

test.describe('Loan tests', async () => {
    const filePath = 'test-data/files/file.pdf';

    test.beforeAll(async () => {
    });

    test.afterEach(async ({adminPageManager}) => {
        await adminPageManager.page.close();
        fs.unlinkSync(filePath);
        expect(!fs.existsSync(filePath)).toBeTruthy();
    });

    test('Admin can download Credit Agreement PDF via Back Office @loan', async ({adminPageManager}) => {
        await adminPageManager.backOfficeSideMenu.sideMenuTabs.loanApplications.click();
        await adminPageManager.loanApplications.clickOnLoanTab(adminPageManager.loanApplications.tabs.approved);
        await adminPageManager.loanApplications.downloadCreditAgreement();
        expect(fs.existsSync(filePath)).toEqual(true);
        //await pageManager.loanApplications.parsePDF(filePath);
    });
});
