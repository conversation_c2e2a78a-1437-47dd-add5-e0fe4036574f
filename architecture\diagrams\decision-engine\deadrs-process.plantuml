@startuml DEADRS

title DEADRS Process Diagram

participant "DEADRS\nDetector" as det #LightGrey
queue "DEADRS events" as queue #LightSalmon
participant "DEADRS" as dead #LightGrey
participant "DE Logic App\n/Function" as la #AliceBlue
participant "Company\n(Account)\nService" as cs #SkyBlue
participant "OnBoarding\nService" as obs #SkyBlue
queue "AccountStatus\nUpdates" as asqueue

autonumber

== Initialization ==

det -> cs : Get active accounts
cs --> det
det -> obs : Get account authorization details by EINs
obs --> det

== Generate events ==

loop Each scheduled step
    det -> obs : Get last execution of the step
    obs --> det
    det -> queue : Generate messages
end

== Processing ==

loop Each message
    queue -> dead : Get message
    dead -> la : Execute\nappropriate\nlogic app / function
    la --> la : Executes steps,\nevaluates rules
    la -> obs :  Refresh\nAccountAuthorizationDetails
    la -> asqueue : Write account status update event, if fails
end

@enduml