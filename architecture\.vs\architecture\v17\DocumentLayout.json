{"Version": 1, "WorkspaceRootPath": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\architecture\\", "Documents": [{"AbsoluteMoniker": "D:0:0:{A2FE74E1-B743-11D0-AE1A-00A0C90FFFC3}|<MiscFiles>|C:\\Users\\<USER>\\source\\repos\\BlueTape\\architecture\\postman-collections\\paymentService\\PaymentService.postman_collection.json||{90A6B3A7-C1A3-4009-A288-E2FF89E96FA0}", "RelativeMoniker": "D:0:0:{A2FE74E1-B743-11D0-AE1A-00A0C90FFFC3}|<MiscFiles>|solutionrelative:postman-collections\\paymentService\\PaymentService.postman_collection.json||{90A6B3A7-C1A3-4009-A288-E2FF89E96FA0}"}], "DocumentGroupContainers": [{"Orientation": 0, "VerticalTabListWidth": 256, "DocumentGroups": [{"DockedWidth": 200, "SelectedChildIndex": 0, "Children": [{"$type": "Document", "DocumentIndex": 0, "Title": "PaymentService.postman_collection.json", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\architecture\\postman-collections\\paymentService\\PaymentService.postman_collection.json", "RelativeDocumentMoniker": "postman-collections\\paymentService\\PaymentService.postman_collection.json", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\architecture\\postman-collections\\paymentService\\PaymentService.postman_collection.json", "RelativeToolTip": "postman-collections\\paymentService\\PaymentService.postman_collection.json", "ViewState": "AQIAAEwDAAAAAAAAAADwv1YDAAAAAAAA", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.001642|", "WhenOpened": "2024-05-22T10:11:39.638Z", "EditorCaption": ""}]}]}]}