﻿using BlueTape.Aion.Application.Models.InternalTransfer;
using BlueTape.Aion.DataAccess.External.Models.InternalTransfer;
using BlueTape.Integrations.Aion;

namespace BlueTape.Aion.Application.Abstractions;

public interface IInternalTransferService
{
    Task<BookTransferObj> CreateInternalTransferAsync(CreateInternal createInternal, string paymentSubscriptionType, CancellationToken ctx);
    Task<BlueTapeTransactionResponseModel> CreateInternalTransferAsyncV2(CreateInternal createInternal, string paymentSubscriptionType, CancellationToken ctx);
}