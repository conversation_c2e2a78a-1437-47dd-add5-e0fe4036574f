using System.Runtime.Serialization;
using System.Text.Json.Serialization;

namespace BlueTape.Aion.Application.Models.Instant;

[DataContract]
public class CreateInstantTransferResponseModel
{
    [JsonPropertyName("responseMessage")]
    public string ResponseMessage { get; set; } = null!;

    [JsonPropertyName("result")]
    public bool Result { get; set; }
    
    [JsonPropertyName("instantTransferObj")]
    public InstantTransferObjectItemResponseModel? InstantTransfer { get; set; }
}