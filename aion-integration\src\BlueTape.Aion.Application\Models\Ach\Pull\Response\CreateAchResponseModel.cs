﻿using System.Runtime.Serialization;
using System.Text.Json.Serialization;

namespace BlueTape.Aion.Application.Models.Ach.Pull.Response;

[DataContract]
public class CreateAchResponseModel
{
    [JsonPropertyName("responseMessage")]
    public string ResponseMessage { get; set; } = null!;

    [JsonPropertyName("result")]
    public bool Result { get; set; }

    [JsonPropertyName("achObj")]
    public AchObjResponseModel AchObj { get; set; } = null!;
}