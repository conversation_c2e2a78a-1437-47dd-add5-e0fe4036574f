{"format": 1, "restore": {"C:\\Users\\<USER>\\source\\repos\\BlueTape\\decision-engine\\src\\lambdas\\DecisionEngine\\BlueTape.Services.DecisionEngine.KycStep\\BlueTape.Services.DecisionEngine.KycStep.csproj": {}}, "projects": {"C:\\Users\\<USER>\\source\\repos\\BlueTape\\decision-engine\\src\\BlueTape.Services.DecisionEngine.BusinessLogic\\BlueTape.Services.DecisionEngine.BusinessLogic.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\decision-engine\\src\\BlueTape.Services.DecisionEngine.BusinessLogic\\BlueTape.Services.DecisionEngine.BusinessLogic.csproj", "projectName": "BlueTape.Services.DecisionEngine.BusinessLogic", "projectPath": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\decision-engine\\src\\BlueTape.Services.DecisionEngine.BusinessLogic\\BlueTape.Services.DecisionEngine.BusinessLogic.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\decision-engine\\src\\BlueTape.Services.DecisionEngine.BusinessLogic\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\source\\repos\\BlueTape\\decision-engine\\NuGet.Config", "C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net6.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}, "https://nuget.pkg.github.com/bluetape-org/index.json": {}}, "frameworks": {"net6.0": {"targetAlias": "net6.0", "projectReferences": {"C:\\Users\\<USER>\\source\\repos\\BlueTape\\decision-engine\\src\\BlueTape.Services.DecisionEngine.DataAccess.External\\BlueTape.Services.DecisionEngine.DataAccess.External.csproj": {"projectPath": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\decision-engine\\src\\BlueTape.Services.DecisionEngine.DataAccess.External\\BlueTape.Services.DecisionEngine.DataAccess.External.csproj"}, "C:\\Users\\<USER>\\source\\repos\\BlueTape\\decision-engine\\src\\BlueTape.Services.DecisionEngine.Domain\\BlueTape.Services.DecisionEngine.Domain.csproj": {"projectPath": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\decision-engine\\src\\BlueTape.Services.DecisionEngine.Domain\\BlueTape.Services.DecisionEngine.Domain.csproj"}, "C:\\Users\\<USER>\\source\\repos\\BlueTape\\decision-engine\\src\\BlueTape.Services.DecisionEngine.Infrastructure\\BlueTape.Services.DecisionEngine.Infrastructure.csproj": {"projectPath": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\decision-engine\\src\\BlueTape.Services.DecisionEngine.Infrastructure\\BlueTape.Services.DecisionEngine.Infrastructure.csproj"}, "C:\\Users\\<USER>\\source\\repos\\BlueTape\\decision-engine\\src\\BlueTape.Services.DecisionEngine.PolicyRulesFramework.PolicyRulesEngine\\BlueTape.Services.DecisionEngine.PolicyRulesFramework.PolicyRulesEngine.csproj": {"projectPath": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\decision-engine\\src\\BlueTape.Services.DecisionEngine.PolicyRulesFramework.PolicyRulesEngine\\BlueTape.Services.DecisionEngine.PolicyRulesFramework.PolicyRulesEngine.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"net6.0": {"targetAlias": "net6.0", "dependencies": {"AWSSDK.Extensions.NETCore.Setup": {"target": "Package", "version": "[3.7.300, )"}, "Amazon.Lambda.Core": {"target": "Package", "version": "[2.2.0, )"}, "AutoMapper": {"target": "Package", "version": "[12.0.1, )"}, "AutoMapper.Extensions.Microsoft.DependencyInjection": {"target": "Package", "version": "[12.0.1, )"}, "BlueTape.AWSMessaging": {"target": "Package", "version": "[2.0.5, )"}, "BlueTape.AWSS3": {"target": "Package", "version": "[1.1.6, )"}, "BlueTape.EmailSender": {"target": "Package", "version": "[3.0.7, )"}, "BlueTape.MongoDB": {"target": "Package", "version": "[1.1.31, )"}, "BlueTape.SNS": {"target": "Package", "version": "[1.0.2, )"}, "DateOnlyTimeOnly.AspNet": {"target": "Package", "version": "[2.1.1, )"}, "F23.StringSimilarity": {"target": "Package", "version": "[5.1.0, )"}, "Microsoft.Extensions.DependencyInjection": {"target": "Package", "version": "[8.0.0, )"}, "TinyHelpers": {"target": "Package", "version": "[3.1.18, )"}, "libphonenumber-csharp": {"target": "Package", "version": "[8.13.24, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "downloadDependencies": [{"name": "Microsoft.AspNetCore.App.Ref", "version": "[6.0.36, 6.0.36]"}, {"name": "Microsoft.NETCore.App.Ref", "version": "[6.0.36, 6.0.36]"}, {"name": "Microsoft.WindowsDesktop.App.Ref", "version": "[6.0.36, 6.0.36]"}], "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.301\\RuntimeIdentifierGraph.json"}}}, "C:\\Users\\<USER>\\source\\repos\\BlueTape\\decision-engine\\src\\BlueTape.Services.DecisionEngine.DataAccess.External\\BlueTape.Services.DecisionEngine.DataAccess.External.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\decision-engine\\src\\BlueTape.Services.DecisionEngine.DataAccess.External\\BlueTape.Services.DecisionEngine.DataAccess.External.csproj", "projectName": "BlueTape.Services.DecisionEngine.DataAccess.External", "projectPath": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\decision-engine\\src\\BlueTape.Services.DecisionEngine.DataAccess.External\\BlueTape.Services.DecisionEngine.DataAccess.External.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\decision-engine\\src\\BlueTape.Services.DecisionEngine.DataAccess.External\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\source\\repos\\BlueTape\\decision-engine\\NuGet.Config", "C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net6.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}, "https://nuget.pkg.github.com/bluetape-org/index.json": {}}, "frameworks": {"net6.0": {"targetAlias": "net6.0", "projectReferences": {"C:\\Users\\<USER>\\source\\repos\\BlueTape\\decision-engine\\src\\BlueTape.Services.DecisionEngine.Domain\\BlueTape.Services.DecisionEngine.Domain.csproj": {"projectPath": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\decision-engine\\src\\BlueTape.Services.DecisionEngine.Domain\\BlueTape.Services.DecisionEngine.Domain.csproj"}, "C:\\Users\\<USER>\\source\\repos\\BlueTape\\decision-engine\\src\\BlueTape.Services.DecisionEngine.Infrastructure\\BlueTape.Services.DecisionEngine.Infrastructure.csproj": {"projectPath": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\decision-engine\\src\\BlueTape.Services.DecisionEngine.Infrastructure\\BlueTape.Services.DecisionEngine.Infrastructure.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"net6.0": {"targetAlias": "net6.0", "dependencies": {"AWSSDK.Extensions.NETCore.Setup": {"target": "Package", "version": "[3.7.300, )"}, "BlueTape.CompanyService": {"target": "Package", "version": "[1.2.42, )"}, "BlueTape.CompanyService.Common": {"target": "Package", "version": "[1.1.21, )"}, "BlueTape.Integrations.Experian": {"target": "Package", "version": "[1.0.2, )"}, "BlueTape.Integrations.Giact": {"target": "Package", "version": "[1.0.3, )"}, "BlueTape.Integrations.LexisNexis": {"target": "Package", "version": "[1.0.6, )"}, "BlueTape.Integrations.Plaid": {"target": "Package", "version": "[1.0.7, )"}, "BlueTape.Integrations.Plaid.Infrastructure": {"target": "Package", "version": "[1.0.0, )"}, "BlueTape.LS": {"target": "Package", "version": "[1.1.69, )"}, "BlueTape.Utilities": {"target": "Package", "version": "[1.4.6, )"}, "DateOnlyTimeOnly.AspNet": {"target": "Package", "version": "[2.1.1, )"}, "Macross.Json.Extensions": {"target": "Package", "version": "[3.0.0, )"}, "Newtonsoft.Json": {"target": "Package", "version": "[13.0.1, )"}, "bluetape.invoiceservice": {"target": "Package", "version": "[1.0.38, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "downloadDependencies": [{"name": "Microsoft.AspNetCore.App.Ref", "version": "[6.0.36, 6.0.36]"}, {"name": "Microsoft.NETCore.App.Ref", "version": "[6.0.36, 6.0.36]"}, {"name": "Microsoft.WindowsDesktop.App.Ref", "version": "[6.0.36, 6.0.36]"}], "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.301\\RuntimeIdentifierGraph.json"}}}, "C:\\Users\\<USER>\\source\\repos\\BlueTape\\decision-engine\\src\\BlueTape.Services.DecisionEngine.Domain\\BlueTape.Services.DecisionEngine.Domain.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\decision-engine\\src\\BlueTape.Services.DecisionEngine.Domain\\BlueTape.Services.DecisionEngine.Domain.csproj", "projectName": "BlueTape.Services.DecisionEngine.Domain", "projectPath": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\decision-engine\\src\\BlueTape.Services.DecisionEngine.Domain\\BlueTape.Services.DecisionEngine.Domain.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\decision-engine\\src\\BlueTape.Services.DecisionEngine.Domain\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\source\\repos\\BlueTape\\decision-engine\\NuGet.Config", "C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net6.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}, "https://nuget.pkg.github.com/bluetape-org/index.json": {}}, "frameworks": {"net6.0": {"targetAlias": "net6.0", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"net6.0": {"targetAlias": "net6.0", "dependencies": {"BlueTape.CashFlow.Domain": {"target": "Package", "version": "[1.0.2, )"}, "BlueTape.OBS": {"target": "Package", "version": "[1.6.69, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "downloadDependencies": [{"name": "Microsoft.AspNetCore.App.Ref", "version": "[6.0.36, 6.0.36]"}, {"name": "Microsoft.NETCore.App.Ref", "version": "[6.0.36, 6.0.36]"}, {"name": "Microsoft.WindowsDesktop.App.Ref", "version": "[6.0.36, 6.0.36]"}], "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.301\\RuntimeIdentifierGraph.json"}}}, "C:\\Users\\<USER>\\source\\repos\\BlueTape\\decision-engine\\src\\BlueTape.Services.DecisionEngine.Infrastructure\\BlueTape.Services.DecisionEngine.Infrastructure.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\decision-engine\\src\\BlueTape.Services.DecisionEngine.Infrastructure\\BlueTape.Services.DecisionEngine.Infrastructure.csproj", "projectName": "BlueTape.Services.DecisionEngine.Infrastructure", "projectPath": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\decision-engine\\src\\BlueTape.Services.DecisionEngine.Infrastructure\\BlueTape.Services.DecisionEngine.Infrastructure.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\decision-engine\\src\\BlueTape.Services.DecisionEngine.Infrastructure\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\source\\repos\\BlueTape\\decision-engine\\NuGet.Config", "C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net6.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}, "https://nuget.pkg.github.com/bluetape-org/index.json": {}}, "frameworks": {"net6.0": {"targetAlias": "net6.0", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"net6.0": {"targetAlias": "net6.0", "dependencies": {"AWSSDK.Extensions.NETCore.Setup": {"target": "Package", "version": "[3.7.300, )"}, "AWSSDK.KeyManagementService": {"target": "Package", "version": "[3.7.300.54, )"}, "AWSSDK.SecretsManager": {"target": "Package", "version": "[3.7.302.29, )"}, "BlueTape.Utilities": {"target": "Package", "version": "[1.4.6, )"}, "Microsoft.Extensions.DependencyInjection.Abstractions": {"target": "Package", "version": "[8.0.0, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "downloadDependencies": [{"name": "Microsoft.AspNetCore.App.Ref", "version": "[6.0.36, 6.0.36]"}, {"name": "Microsoft.NETCore.App.Ref", "version": "[6.0.36, 6.0.36]"}, {"name": "Microsoft.WindowsDesktop.App.Ref", "version": "[6.0.36, 6.0.36]"}], "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.301\\RuntimeIdentifierGraph.json"}}}, "C:\\Users\\<USER>\\source\\repos\\BlueTape\\decision-engine\\src\\BlueTape.Services.DecisionEngine.PolicyRulesFramework.Domain\\BlueTape.Services.DecisionEngine.PolicyRulesFramework.Domain.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\decision-engine\\src\\BlueTape.Services.DecisionEngine.PolicyRulesFramework.Domain\\BlueTape.Services.DecisionEngine.PolicyRulesFramework.Domain.csproj", "projectName": "BlueTape.Services.DecisionEngine.PolicyRulesFramework.Domain", "projectPath": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\decision-engine\\src\\BlueTape.Services.DecisionEngine.PolicyRulesFramework.Domain\\BlueTape.Services.DecisionEngine.PolicyRulesFramework.Domain.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\decision-engine\\src\\BlueTape.Services.DecisionEngine.PolicyRulesFramework.Domain\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\source\\repos\\BlueTape\\decision-engine\\NuGet.Config", "C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net6.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}, "https://nuget.pkg.github.com/bluetape-org/index.json": {}}, "frameworks": {"net6.0": {"targetAlias": "net6.0", "projectReferences": {"C:\\Users\\<USER>\\source\\repos\\BlueTape\\decision-engine\\src\\BlueTape.Services.DecisionEngine.Domain\\BlueTape.Services.DecisionEngine.Domain.csproj": {"projectPath": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\decision-engine\\src\\BlueTape.Services.DecisionEngine.Domain\\BlueTape.Services.DecisionEngine.Domain.csproj"}, "C:\\Users\\<USER>\\source\\repos\\BlueTape\\decision-engine\\src\\BlueTape.Services.DecisionEngine.Infrastructure\\BlueTape.Services.DecisionEngine.Infrastructure.csproj": {"projectPath": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\decision-engine\\src\\BlueTape.Services.DecisionEngine.Infrastructure\\BlueTape.Services.DecisionEngine.Infrastructure.csproj"}, "C:\\Users\\<USER>\\source\\repos\\BlueTape\\decision-engine\\src\\BlueTape.Services.DecisionEngine.PolicyRulesFramework.Infrasrtucture\\BlueTape.Services.DecisionEngine.PolicyRulesFramework.Infrasrtucture.csproj": {"projectPath": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\decision-engine\\src\\BlueTape.Services.DecisionEngine.PolicyRulesFramework.Infrasrtucture\\BlueTape.Services.DecisionEngine.PolicyRulesFramework.Infrasrtucture.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"net6.0": {"targetAlias": "net6.0", "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "downloadDependencies": [{"name": "Microsoft.AspNetCore.App.Ref", "version": "[6.0.36, 6.0.36]"}, {"name": "Microsoft.NETCore.App.Ref", "version": "[6.0.36, 6.0.36]"}, {"name": "Microsoft.WindowsDesktop.App.Ref", "version": "[6.0.36, 6.0.36]"}], "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.301\\RuntimeIdentifierGraph.json"}}}, "C:\\Users\\<USER>\\source\\repos\\BlueTape\\decision-engine\\src\\BlueTape.Services.DecisionEngine.PolicyRulesFramework.Infrasrtucture\\BlueTape.Services.DecisionEngine.PolicyRulesFramework.Infrasrtucture.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\decision-engine\\src\\BlueTape.Services.DecisionEngine.PolicyRulesFramework.Infrasrtucture\\BlueTape.Services.DecisionEngine.PolicyRulesFramework.Infrasrtucture.csproj", "projectName": "BlueTape.Services.DecisionEngine.PolicyRulesFramework.Infrasrtucture", "projectPath": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\decision-engine\\src\\BlueTape.Services.DecisionEngine.PolicyRulesFramework.Infrasrtucture\\BlueTape.Services.DecisionEngine.PolicyRulesFramework.Infrasrtucture.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\decision-engine\\src\\BlueTape.Services.DecisionEngine.PolicyRulesFramework.Infrasrtucture\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\source\\repos\\BlueTape\\decision-engine\\NuGet.Config", "C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net6.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}, "https://nuget.pkg.github.com/bluetape-org/index.json": {}}, "frameworks": {"net6.0": {"targetAlias": "net6.0", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"net6.0": {"targetAlias": "net6.0", "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "downloadDependencies": [{"name": "Microsoft.AspNetCore.App.Ref", "version": "[6.0.36, 6.0.36]"}, {"name": "Microsoft.NETCore.App.Ref", "version": "[6.0.36, 6.0.36]"}, {"name": "Microsoft.WindowsDesktop.App.Ref", "version": "[6.0.36, 6.0.36]"}], "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.301\\RuntimeIdentifierGraph.json"}}}, "C:\\Users\\<USER>\\source\\repos\\BlueTape\\decision-engine\\src\\BlueTape.Services.DecisionEngine.PolicyRulesFramework.PolicyRulesEngine\\BlueTape.Services.DecisionEngine.PolicyRulesFramework.PolicyRulesEngine.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\decision-engine\\src\\BlueTape.Services.DecisionEngine.PolicyRulesFramework.PolicyRulesEngine\\BlueTape.Services.DecisionEngine.PolicyRulesFramework.PolicyRulesEngine.csproj", "projectName": "BlueTape.Services.DecisionEngine.PolicyRulesFramework.PolicyRulesEngine", "projectPath": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\decision-engine\\src\\BlueTape.Services.DecisionEngine.PolicyRulesFramework.PolicyRulesEngine\\BlueTape.Services.DecisionEngine.PolicyRulesFramework.PolicyRulesEngine.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\decision-engine\\src\\BlueTape.Services.DecisionEngine.PolicyRulesFramework.PolicyRulesEngine\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\source\\repos\\BlueTape\\decision-engine\\NuGet.Config", "C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net6.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}, "https://nuget.pkg.github.com/bluetape-org/index.json": {}}, "frameworks": {"net6.0": {"targetAlias": "net6.0", "projectReferences": {"C:\\Users\\<USER>\\source\\repos\\BlueTape\\decision-engine\\src\\BlueTape.Services.DecisionEngine.PolicyRulesFramework.Domain\\BlueTape.Services.DecisionEngine.PolicyRulesFramework.Domain.csproj": {"projectPath": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\decision-engine\\src\\BlueTape.Services.DecisionEngine.PolicyRulesFramework.Domain\\BlueTape.Services.DecisionEngine.PolicyRulesFramework.Domain.csproj"}, "C:\\Users\\<USER>\\source\\repos\\BlueTape\\decision-engine\\src\\BlueTape.Services.DecisionEngine.PolicyRulesFramework.Infrasrtucture\\BlueTape.Services.DecisionEngine.PolicyRulesFramework.Infrasrtucture.csproj": {"projectPath": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\decision-engine\\src\\BlueTape.Services.DecisionEngine.PolicyRulesFramework.Infrasrtucture\\BlueTape.Services.DecisionEngine.PolicyRulesFramework.Infrasrtucture.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"net6.0": {"targetAlias": "net6.0", "dependencies": {"Microsoft.Extensions.Configuration.Abstractions": {"target": "Package", "version": "[8.0.0, )"}, "Microsoft.Extensions.DependencyInjection.Abstractions": {"target": "Package", "version": "[8.0.0, )"}, "Microsoft.Extensions.Options.ConfigurationExtensions": {"target": "Package", "version": "[8.0.0, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.301\\RuntimeIdentifierGraph.json"}}}, "C:\\Users\\<USER>\\source\\repos\\BlueTape\\decision-engine\\src\\lambdas\\DecisionEngine\\BlueTape.Services.DecisionEngine.KycStep\\BlueTape.Services.DecisionEngine.KycStep.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\decision-engine\\src\\lambdas\\DecisionEngine\\BlueTape.Services.DecisionEngine.KycStep\\BlueTape.Services.DecisionEngine.KycStep.csproj", "projectName": "BlueTape.Services.DecisionEngine.KycStep", "projectPath": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\decision-engine\\src\\lambdas\\DecisionEngine\\BlueTape.Services.DecisionEngine.KycStep\\BlueTape.Services.DecisionEngine.KycStep.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\decision-engine\\src\\lambdas\\DecisionEngine\\BlueTape.Services.DecisionEngine.KycStep\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\source\\repos\\BlueTape\\decision-engine\\NuGet.Config", "C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net6.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}, "https://nuget.pkg.github.com/bluetape-org/index.json": {}}, "frameworks": {"net6.0": {"targetAlias": "net6.0", "projectReferences": {"C:\\Users\\<USER>\\source\\repos\\BlueTape\\decision-engine\\src\\BlueTape.Services.DecisionEngine.BusinessLogic\\BlueTape.Services.DecisionEngine.BusinessLogic.csproj": {"projectPath": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\decision-engine\\src\\BlueTape.Services.DecisionEngine.BusinessLogic\\BlueTape.Services.DecisionEngine.BusinessLogic.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"net6.0": {"targetAlias": "net6.0", "dependencies": {"Amazon.Lambda.Core": {"target": "Package", "version": "[2.2.0, )"}, "Amazon.Lambda.Serialization.SystemTextJson": {"target": "Package", "version": "[2.4.0, )"}, "BlueTape.LambdaBase": {"target": "Package", "version": "[1.1.1, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "downloadDependencies": [{"name": "Microsoft.AspNetCore.App.Ref", "version": "[6.0.36, 6.0.36]"}, {"name": "Microsoft.NETCore.App.Crossgen2.win-x64", "version": "[6.0.36, 6.0.36]"}, {"name": "Microsoft.NETCore.App.Ref", "version": "[6.0.36, 6.0.36]"}, {"name": "Microsoft.WindowsDesktop.App.Ref", "version": "[6.0.36, 6.0.36]"}], "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.301\\RuntimeIdentifierGraph.json"}}}}}