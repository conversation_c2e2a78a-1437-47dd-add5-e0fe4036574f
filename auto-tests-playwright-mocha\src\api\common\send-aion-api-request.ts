import axios from "axios";

require('dotenv').config();

const constants = JSON.parse(JSON.stringify(require('../../constants/constants.json')));

// Send AION API requests

export async function sendAionApiRequest(method: string, endpoint: string, body: any = null, secretKey: any) {
    try {
        const url = `${process.env.AION_API_BASE_URL}/${endpoint}`;
        let response;

        switch (method) {
            case 'post':
                response = await axios.post(url, body, {
                    headers: {
                        accept: 'application/json',
                        'Content-Type': 'application/json',
                        'X-API-KEY': `${secretKey}`,
                    }
                });
                break;
            case 'get':
                response = await axios.get(url, {
                    headers: {
                        accept: 'application/json',
                        'Content-Type': 'application/json',
                        'X-API-KEY': `${process.env.X_API_KEY}`,
                    }
                });
                break;
            case 'put':
                response = await axios.put(url, body, {
                    headers: {
                        accept: 'application/json',
                        'Content-Type': 'application/json',
                        'X-API-KEY': `${process.env.X_API_KEY}`,
                    }
                });
                break;
            case 'delete':
                response = await axios.delete(url, {
                    headers: {
                        accept: 'application/json',
                        'Content-Type': 'application/json',
                        'X-API-KEY': `${process.env.X_API_KEY}`,
                    }
                });
                break;
        }
        return response;
    } catch (error) {
        return error;
    }
}

export async function pullAion(amount: any, description: any, addenda: any, companyId: any, bankAccountId: any) {
    const requestBody = {
        "amount": amount,
        "description": description,
        "addenda": addenda,
        "receiver": {
            "companyId": companyId,
            "bankAccountId": bankAccountId
        }
    };
    const response = await sendAionApiRequest('post', `api/ach/pull`, requestBody, process.env.X_API_KEY);
    return response;
}

export async function pushAion(amount: any, description: any, addenda: any, companyId: any, bankAccountId: any) {
    const requestBody = {
        "amount": amount,
        "description": description,
        "addenda": addenda,
        "receiver": {
            "companyId": companyId,
            "bankAccountId": bankAccountId
        }
    };
    const response = await sendAionApiRequest('post', `api/ach/push`, requestBody, process.env.X_API_KEY);
    return response;
}

export async function getAllTransactions() {
    const response = await sendAionApiRequest('get', `transactions`, null, process.env.X_API_KEY);
    return response.data;
}
