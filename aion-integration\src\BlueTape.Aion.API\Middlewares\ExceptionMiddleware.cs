﻿using BlueTape.Aion.API.Constants;
using BlueTape.Aion.API.Helpers;
using BlueTape.Aion.Application.Abstractions;
using BlueTape.Common.ExceptionHandling.Exceptions.Base;
using BlueTape.Common.ExceptionHandling.Extensions;
using BlueTape.SNS.SlackNotification.Models;
using System.Diagnostics.CodeAnalysis;
using System.Globalization;
using System.Net;
using System.Text.Json;
using System.Text.Json.Serialization;

namespace BlueTape.Aion.API.Middlewares;

[ExcludeFromCodeCoverage]
public class ExceptionMiddleware
{
    private static readonly JsonSerializerOptions SerializerOptions = new()
    {
        Converters = { new JsonStringEnumConverter() },
        PropertyNamingPolicy = JsonNamingPolicy.CamelCase,
    };

    private readonly RequestDelegate _next;
    private readonly IExceptionToResponseMapper _exceptionToResponseMapper;
    private readonly IErrorNotificationService _errorNotificationService;
    private readonly ILogger<ExceptionMiddleware> _logger;

    public ExceptionMiddleware(RequestDelegate next, IExceptionToResponseMapper exceptionToResponseMapper,
        IErrorNotificationService errorNotificationService,
        ILogger<ExceptionMiddleware> logger)
    {
        _next = next;
        _exceptionToResponseMapper = exceptionToResponseMapper;
        _errorNotificationService = errorNotificationService;
        _logger = logger;
    }

    public async Task InvokeAsync(HttpContext context, ILogger<ExceptionMiddleware> logger)
    {
        try
        {
            await _next.Invoke(context);
        }
        catch (Exception exception)
        {
            _logger.LogError(exception, "{Message}", exception.Message);
            await HandleErrorAsync(context, exception);
        }
    }

    private async Task HandleErrorAsync(HttpContext context, Exception exception)
    {
        var (exceptionResponse, statusCode) = _exceptionToResponseMapper.Map(exception);
        context.Response.StatusCode = (int)statusCode;

        if (exception is DomainException domainEx)
            await _errorNotificationService.Notify(domainEx.GetSlackEventMessageBody($"{EnvironmentExtensions.GetExecutionEnvironment()} API request path: {context.Request.Path}",
                ConfigConstants.ProjectValue, EventLevel.Error), CancellationToken.None);

        if (exception != null)
        {
            await _errorNotificationService.Notify(exception.GetSlackEventMessageBody($"{EnvironmentExtensions.GetExecutionEnvironment()} API request path: {context.Request.Path}",
                ConfigConstants.ProjectValue, EventLevel.Error, maxMessageLength: 400), CancellationToken.None);
        }
        else if (context.Response.StatusCode == (int)HttpStatusCode.InternalServerError)
        {
            await _errorNotificationService.Notify(new EventMessageBody
            {
                Message = $"Internal Error was happened during API request: ${context.Request.Path}",
                EventLevel = EventLevel.Error,
                EventName = "API Internal Error",
                EventSource = "API",
                ServiceName = ConfigConstants.ProjectValue,
                TimeStamp = DateTime.UtcNow.ToString(CultureInfo.InvariantCulture),
                AwsAccountId = Environment.GetEnvironmentVariable("LP_AWS_ACCOUNT") ??
                               Environment.GetEnvironmentVariable("LP_AWS_ACCOUNT") ??
                               "Not provided in service"
            }, CancellationToken.None);
        }

        if (exceptionResponse.Count == 0)
        {
            await context.Response.WriteAsync(string.Empty);
            return;
        }

        context.Response.ContentType = "application/json";
        await JsonSerializer.SerializeAsync(context.Response.Body, exceptionResponse, SerializerOptions);
    }
}
