﻿using AutoFixture;
using BlueTape.Aion.Application.Constants;
using BlueTape.Aion.Application.Service;
using BlueTape.Aion.DataAccess.External.Abstractions;
using BlueTape.Aion.DataAccess.External.Models.TransferMethod;
using BlueTape.Aion.DataAccess.MongoDB.Abstractions;
using BlueTape.Aion.Domain.DTOs.BankAccount;
using BlueTape.Aion.Domain.DTOs.Company;
using BlueTape.Application.Tests.Helper;
using BlueTape.Integrations.Aion.Infrastructure.Enums;
using BlueTape.Services.Utilities.AWS;
using BueTape.Aion.Infrastructure.Exceptions;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using Moq;
using System.Threading;
using System.Threading.Tasks;
using BlueTape.CompanyClient.Abstractions;

namespace BlueTape.Application.Tests.Services;

public class BankAccountServiceTests
{
    private readonly Mock<IAionHttpClient> _aionHttpClientMock = new();
    private readonly Mock<IBankAccountRepository> _bankAccountRepositoryMock = new();
    private readonly Mock<ILogger<BankAccountService>> _loggerMock = new();
    private readonly Mock<IKmsDecryptorService> _kmsDecryptorServiceMock = new();
    private readonly Mock<IConfiguration> _configurationMock = new();
    private readonly Mock<ICompanyHttpClient> _companyHttpClientMock = new();

    private Fixture _fixture = new();

    private BankAccountService GetService()
    {
        return new BankAccountService(
            _aionHttpClientMock.Object,
            _bankAccountRepositoryMock.Object,
            _loggerMock.Object,
            _kmsDecryptorServiceMock.Object,
            _configurationMock.Object,
            _companyHttpClientMock.Object);
    }

    private void VerifyNoOtherCalls()
    {
        _aionHttpClientMock.VerifyNoOtherCalls();
        _bankAccountRepositoryMock.VerifyNoOtherCalls();
        _loggerMock.VerifyNoOtherCalls();
        _kmsDecryptorServiceMock.VerifyNoOtherCalls();
        _configurationMock.VerifyNoOtherCalls();
    }

    [Fact]
    public async Task PreProcessAchPullAsync_Throws_ParameterRequiredException_CounterPartyObjectId()
    {
        var receiverBankAccountId = _fixture.Create<string>();
        var companyDto = _fixture.Create<CompanyDto>();
        companyDto.AionSettings!.CounterPartyObjectId = string.Empty;

        var service = GetService();

        await service.SynBankAccountWithAionAsync(
                receiverBankAccountId, 
                companyDto,
                PaymentSubscriptionType.SUBSCRIPTION1.ToString(),
                AionPaymentMethodType.ACH,
                CancellationToken.None)
            .ShouldThrowAsync<ParameterRequiredException>();

        _loggerMock.VerifyLogging(LogLevel.Information, Times.Once());

        VerifyNoOtherCalls();
    }

    [Fact]
    public async Task PreProcessAchPullAsync_Throws_BankAccountDoesNotExistException()
    {
        var receiverBankAccountId = _fixture.Create<string>();
        var companyDto = _fixture.Create<CompanyDto>();

        _bankAccountRepositoryMock.Setup(x =>
            x.GetByBankAccountIdAsync(
                It.IsAny<string>(),
                It.IsAny<CancellationToken>()));

        var service = GetService();

        await service.SynBankAccountWithAionAsync(
                receiverBankAccountId, 
                companyDto, 
                PaymentSubscriptionType.SUBSCRIPTION1.ToString(), 
                AionPaymentMethodType.ACH,
                CancellationToken.None)
            .ShouldThrowAsync<BankAccountDoesNotExistException>();

        _bankAccountRepositoryMock.Verify(x =>
            x.GetByBankAccountIdAsync(
                receiverBankAccountId,
                It.IsAny<CancellationToken>()), Times.Once);

        _loggerMock.VerifyLogging(LogLevel.Information, Times.Once());

        VerifyNoOtherCalls();
    }

    [Fact]
    public async Task PreProcessAchPullAsync_Throws_ParameterRequiredException_AccountType()
    {
        var receiverBankAccountId = _fixture.Create<string>();
        var companyDto = _fixture.Create<CompanyDto>();
        var bankAccount = _fixture.Create<BankAccountDto>();
        bankAccount.AccountType = string.Empty;

        _bankAccountRepositoryMock.Setup(x =>
                x.GetByBankAccountIdAsync(
                    It.IsAny<string>(),
                    It.IsAny<CancellationToken>()))
            .ReturnsAsync(bankAccount);

        var service = GetService();

        await service.SynBankAccountWithAionAsync(
                receiverBankAccountId,
                companyDto,
                PaymentSubscriptionType.SUBSCRIPTION1.ToString(), 
                AionPaymentMethodType.ACH,
                CancellationToken.None)
            .ShouldThrowAsync<ParameterRequiredException>();

        _bankAccountRepositoryMock.Verify(x =>
            x.GetByBankAccountIdAsync(
                receiverBankAccountId,
                It.IsAny<CancellationToken>()), Times.Once);

        _loggerMock.VerifyLogging(LogLevel.Information, Times.Once());

        VerifyNoOtherCalls();
    }

    [Fact]
    public async Task PreProcessAchPullAsync_Throws_ParameterRequiredException_RoutingNumber()
    {
        var receiverBankAccountId = _fixture.Create<string>();
        var companyDto = _fixture.Create<CompanyDto>();
        var bankAccount = _fixture.Create<BankAccountDto>();
        bankAccount.RoutingNumber = string.Empty;

        _bankAccountRepositoryMock.Setup(x =>
                x.GetByBankAccountIdAsync(
                    It.IsAny<string>(),
                    It.IsAny<CancellationToken>()))
            .ReturnsAsync(bankAccount);

        var service = GetService();

        await service.SynBankAccountWithAionAsync(
                receiverBankAccountId,
                companyDto,
                PaymentSubscriptionType.SUBSCRIPTION1.ToString(),
                AionPaymentMethodType.ACH,
                CancellationToken.None)
            .ShouldThrowAsync<ParameterRequiredException>();

        _bankAccountRepositoryMock.Verify(x =>
            x.GetByBankAccountIdAsync(
                receiverBankAccountId,
                It.IsAny<CancellationToken>()), Times.Once);

        _loggerMock.VerifyLogging(LogLevel.Information, Times.Once());

        VerifyNoOtherCalls();
    }

    [Fact]
    public async Task PreProcessAchPullAsync_Throws_ParameterRequiredException_AccountNumber()
    {
        var receiverBankAccountId = _fixture.Create<string>();
        var companyDto = _fixture.Create<CompanyDto>();
        var bankAccount = _fixture.Create<BankAccountDto>();
        bankAccount.AccountNumber = null;

        _bankAccountRepositoryMock.Setup(x =>
                x.GetByBankAccountIdAsync(
                    It.IsAny<string>(),
                    It.IsAny<CancellationToken>()))
            .ReturnsAsync(bankAccount);

        var service = GetService();

        await service.SynBankAccountWithAionAsync(
                receiverBankAccountId,
                companyDto, 
                PaymentSubscriptionType.SUBSCRIPTION1.ToString(),
                AionPaymentMethodType.ACH,
                CancellationToken.None)
            .ShouldThrowAsync<ParameterRequiredException>();

        _bankAccountRepositoryMock.Verify(x =>
            x.GetByBankAccountIdAsync(
                receiverBankAccountId,
                It.IsAny<CancellationToken>()), Times.Once);

        _loggerMock.VerifyLogging(LogLevel.Information, Times.Once());

        VerifyNoOtherCalls();
    }

    [Fact]
    public async Task PreProcessAchPullAsync_Throws_ParameterRequiredException_AccountNumberCipher()
    {
        var receiverBankAccountId = _fixture.Create<string>();
        var companyDto = _fixture.Create<CompanyDto>();
        var bankAccount = _fixture.Create<BankAccountDto>();
        bankAccount.AccountNumber!.Cipher = string.Empty;

        _bankAccountRepositoryMock.Setup(x =>
                x.GetByBankAccountIdAsync(
                    It.IsAny<string>(),
                    It.IsAny<CancellationToken>()))
            .ReturnsAsync(bankAccount);

        var service = GetService();

        await service.SynBankAccountWithAionAsync(
                receiverBankAccountId,
                companyDto, 
                PaymentSubscriptionType.SUBSCRIPTION1.ToString(),
                AionPaymentMethodType.ACH,
                CancellationToken.None)
            .ShouldThrowAsync<ParameterRequiredException>();

        _bankAccountRepositoryMock.Verify(x =>
            x.GetByBankAccountIdAsync(
                receiverBankAccountId,
                It.IsAny<CancellationToken>()), Times.Once);

        _loggerMock.VerifyLogging(LogLevel.Information, Times.Once());

        VerifyNoOtherCalls();
    }

    [Fact]
    public async Task PreProcessAchPullAsync_Execute_Success_With_Update()
    {
        var companyDto = _fixture.Create<CompanyDto>();
        var bankAccount = _fixture.Create<BankAccountDto>();
        var receiverBankAccountId = bankAccount.BlueTapeBankAccountId;
        bankAccount.AionSettings = null;
        var aionOriginatorAccountId = _fixture.Create<string>();
        var decryptionResult = _fixture.Create<DecryptionResult>();
        AddTransferMethodRequest? addTransferMethodRequestCallBack = null;
        BankAccountDto bankAccountDtoCallBack = null;
        var transferMethodResponse = _fixture.Create<AddTransferMethodResponse>();

        _bankAccountRepositoryMock.Setup(x =>
                x.GetByBankAccountIdAsync(
                    It.IsAny<string>(),
                    It.IsAny<CancellationToken>()))
            .ReturnsAsync(bankAccount);

        _configurationMock.Setup(x => x[It.IsAny<string>()])
            .Returns(aionOriginatorAccountId);

        _kmsDecryptorServiceMock.Setup(x =>
            x.DecryptSingleAsync(
                It.IsAny<string>(),
                It.IsAny<string>(),
                It.IsAny<CancellationToken>()))
            .ReturnsAsync(decryptionResult);

        _aionHttpClientMock.Setup(x =>
                x.AddTransferMethod(
                    It.IsAny<AddTransferMethodRequest>(),
                    PaymentSubscriptionType.SUBSCRIPTION1,
                    It.IsAny<CancellationToken>()))
            .ReturnsAsync(transferMethodResponse)
            .Callback<AddTransferMethodRequest, PaymentSubscriptionType, CancellationToken>((addTransferCallBack, _, _) =>
                addTransferMethodRequestCallBack = addTransferCallBack);

        _bankAccountRepositoryMock.Setup(x =>
            x.UpdateAionSettingsAsync(
                It.IsAny<BankAccountDto>(),
                It.IsAny<CancellationToken>()))
            .Callback<BankAccountDto, CancellationToken>((x, y) => bankAccountDtoCallBack = x);

        var service = GetService();

        await service.SynBankAccountWithAionAsync(
            receiverBankAccountId,
            companyDto,
            PaymentSubscriptionType.SUBSCRIPTION1.ToString(),
            AionPaymentMethodType.ACH,
            CancellationToken.None);

        addTransferMethodRequestCallBack!.AccountId.ShouldBeEquivalentTo(aionOriginatorAccountId);
        addTransferMethodRequestCallBack!.CounterpartyId.ShouldBeEquivalentTo(companyDto.AionSettings!.CounterPartyObjectId!);
        addTransferMethodRequestCallBack!.BankDetail.ShouldNotBeNull();
        addTransferMethodRequestCallBack!.BankDetail.Type.ShouldBeEquivalentTo(AionConstants.BankDetailTypeAch);
        addTransferMethodRequestCallBack!.BankDetail.AccountType.ShouldBeEquivalentTo(bankAccount.AccountType);
        addTransferMethodRequestCallBack!.BankDetail.AccountNumber.ShouldBeEquivalentTo(decryptionResult.DecryptedText);
        addTransferMethodRequestCallBack!.BankDetail.RoutingNumber.ShouldBeEquivalentTo(bankAccount.RoutingNumber);

        bankAccountDtoCallBack!.BlueTapeBankAccountId.ShouldBeEquivalentTo(receiverBankAccountId);
        bankAccountDtoCallBack!.AionSettings.ShouldNotBeNull();
        bankAccountDtoCallBack!.AionSettings!.TransferMethodId = transferMethodResponse.TransferMethod.Id;

        _bankAccountRepositoryMock.Verify(x =>
            x.GetByBankAccountIdAsync(
                receiverBankAccountId,
                It.IsAny<CancellationToken>()), Times.Once);

        _configurationMock.Verify(x => x[AionConstants.AionCollectionAccountId], Times.Once);

        _kmsDecryptorServiceMock.Verify(x =>
            x.DecryptSingleAsync(
                CryptoConstants.AccountNumberKeyId,
                bankAccount.AccountNumber!.Cipher,
                It.IsAny<CancellationToken>()), Times.Once);

        _aionHttpClientMock.Verify(x =>
            x.AddTransferMethod(
                It.IsAny<AddTransferMethodRequest>(),
                PaymentSubscriptionType.SUBSCRIPTION1,
                It.IsAny<CancellationToken>()), Times.Once);

        _bankAccountRepositoryMock.Verify(x =>
            x.UpdateAionSettingsAsync(
                It.IsAny<BankAccountDto>(),
                It.IsAny<CancellationToken>()), Times.Once());

        _loggerMock.VerifyLogging(LogLevel.Information, Times.Exactly(3));

        VerifyNoOtherCalls();
    }
}