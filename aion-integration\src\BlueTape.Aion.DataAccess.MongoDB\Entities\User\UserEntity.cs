﻿using BlueTape.MongoDB.Attributes;
using MongoDB.Bson;
using MongoDB.Bson.Serialization.Attributes;

namespace BlueTape.Aion.DataAccess.MongoDB.Entities.User;

[MongoCollection("users")]
[BsonIgnoreExtraElements]
public class UserEntity
{
    [BsonId]
    [BsonRepresentation(BsonType.ObjectId)]
    [BsonElement("_id")]
    public string Id { get; set; } = null!;

    [BsonElement("sub")]
    public string ExternalId { get; set; } = null!;

    [BsonElement("firstName")]
    public string FirstName { get; set; } = null!;

    [BsonElement("lastName")]
    public string LastName { get; set; } = null!;

    [BsonElement("email")]
    public string Email { get; set; } = null!;

    [BsonElement("login")]
    public string Login { get; set; } = null!;

    [BsonElement("phone")]
    public string Phone { get; set; } = null!;
}