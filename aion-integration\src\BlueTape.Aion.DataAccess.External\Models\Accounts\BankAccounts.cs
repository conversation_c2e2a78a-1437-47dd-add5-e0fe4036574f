﻿using System.Runtime.Serialization;
using System.Text.Json.Serialization;

namespace BlueTape.Aion.DataAccess.External.Models.Accounts;

[DataContract]
public class BankAccounts
{
    [JsonPropertyName("accountId")]
    public string AccountId { get; set; } = null!;
    
    [JsonPropertyName("mask")]
    public string Mask { get; set; } = null!;
    
    [JsonPropertyName("accountName")]
    public string AccountName { get; set; } = null!;

    [JsonPropertyName("nickName")]
    public string NickName { get; set; } = null!;
    
    [JsonPropertyName("accountSubType")]
    public string AccountSubType { get; set; } = null!;
    
    [JsonPropertyName("accountNumber")]
    public string AccountNumber { get; set; } = null!;
    
    [JsonPropertyName("routingNumber")]
    public string RoutingNumber { get; set; } = null!;
    
    [JsonPropertyName("ledgerType")]
    public string LedgerType { get; set; } = null!;
    
    [JsonPropertyName("classification")]
    public string Classification { get; set; } = null!;
    
    [JsonPropertyName("title")]
    public string Title { get; set; } = null!;
    
    [JsonPropertyName("status")]
    public string Status { get; set; } = null!;
    
    [JsonPropertyName("locked")]
    public bool Locked { get; set; }
    
    [JsonPropertyName("availableBalance")]
    public decimal AvailableBalance { get; set; }
    
    [JsonPropertyName("currentBalance")]
    public decimal CurrentBalance { get; set; }
    
    [JsonPropertyName("availableBalanceStr")]
    public string AvailableBalanceStr { get; set; } = null!;
    
    [JsonPropertyName("currentBalanceStr")]
    public string CurrentBalanceStr { get; set; } = null!;
    
    [JsonPropertyName("currency")]
    public string Currency { get; set; } = null!;
    
    [JsonPropertyName("createdAt")]
    public DateTime CreatedAt { get; set; }
    
    [JsonPropertyName("updatedAt")]
    public DateTime UpdatedAt { get; set; }
    
    [JsonPropertyName("openedAt")]
    public DateTime OpenedAt { get; set; }
    
    [JsonPropertyName("address")]
    public BankAddress Address { get; set; } = null!;
}