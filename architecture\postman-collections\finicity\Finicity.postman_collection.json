{"info": {"_postman_id": "c8b97075-8675-4932-bb4d-a63a5df2bd02", "name": "Finicity", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json", "_exporter_id": "********"}, "item": [{"name": "Create token", "request": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\r\n  \"partnerId\": \"{{PartnerId}}\",\r\n  \"partnerSecret\": \"{{PartnerSecret}}\"\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{url}}/aggregation/v2/partners/authentication", "host": ["{{url}}"], "path": ["aggregation", "v2", "partners", "authentication"]}}, "response": []}, {"name": "Get customer accounts", "request": {"method": "GET", "header": [{"key": "Finicity-App-Token", "value": "{{access_token}}", "type": "text"}], "url": {"raw": "{{url}}/aggregation/v1/customers/:customerId/accounts", "host": ["{{url}}"], "path": ["aggregation", "v1", "customers", ":customerId", "accounts"], "variable": [{"key": "customerId", "value": null}]}}, "response": []}, {"name": "Add customer", "request": {"method": "POST", "header": [{"key": "Finicity-App-Token", "value": "{{access_token}}", "type": "text"}], "url": {"raw": "{{url}}/aggregation/v2/customers/testing", "host": ["{{url}}"], "path": ["aggregation", "v2", "customers", "testing"]}}, "response": []}, {"name": "Get account owner", "request": {"method": "GET", "header": [{"key": "Finicity-App-Token", "value": "{{access_token}}", "type": "text"}], "url": {"raw": "{{url}}/aggregation/v1/customers/:customerId/accounts/:accountId/owner", "host": ["{{url}}"], "path": ["aggregation", "v1", "customers", ":customerId", "accounts", ":accountId", "owner"], "variable": [{"key": "customerId", "value": null}, {"key": "accountId", "value": null}]}}, "response": []}, {"name": "Load historic transactions", "request": {"method": "POST", "header": [{"key": "Finicity-App-Token", "value": "{{access_token}}", "type": "text"}], "url": {"raw": "{{url}}/aggregation/v1/customers/:customerId/accounts/:accountId/transactions/historic", "host": ["{{url}}"], "path": ["aggregation", "v1", "customers", ":customerId", "accounts", ":accountId", "transactions", "historic"], "variable": [{"key": "customerId", "value": null}, {"key": "accountId", "value": null}]}}, "response": []}, {"name": "Get all customer transactions", "request": {"method": "GET", "header": [{"key": "Finicity-App-Token", "value": "{{access_token}}", "type": "text"}], "url": {"raw": "{{url}}/aggregation/v3/customers/:customerId/transactions", "host": ["{{url}}"], "path": ["aggregation", "v3", "customers", ":customerId", "transactions"], "variable": [{"key": "customerId", "value": null}]}}, "response": []}, {"name": "Available balance live", "request": {"method": "GET", "header": [{"key": "Finicity-App-Token", "value": "{{access_token}}", "type": "text"}], "url": {"raw": "{{url}}/aggregation/v1/customers/:customerId/accounts/:accountId/availableBalance/live", "host": ["{{url}}"], "path": ["aggregation", "v1", "customers", ":customerId", "accounts", ":accountId", "availableBalance", "live"], "variable": [{"key": "customerId", "value": null}, {"key": "accountId", "value": null}]}}, "response": []}, {"name": "Get institution by id", "request": {"method": "GET", "header": [{"key": "Finicity-App-Token", "value": "{{access_token}}", "type": "text"}], "url": {"raw": "{{url}}/institution/v2/institutions/:institutionId", "host": ["{{url}}"], "path": ["institution", "v2", "institutions", ":institutionId"], "variable": [{"key": "institutionId", "value": null}]}}, "response": []}, {"name": "Get Certified Institutions", "request": {"method": "GET", "header": [{"key": "Finicity-App-Token", "value": "{{access_token}}", "type": "text"}], "url": {"raw": "{{url}}/institution/v2/certifiedInstitutions", "host": ["{{url}}"], "path": ["institution", "v2", "certifiedInstitutions"]}}, "response": []}, {"name": "Get ACH details", "request": {"method": "GET", "header": [{"key": "Finicity-App-Token", "value": "{{access_token}}", "type": "text"}], "url": {"raw": "{{url}}/aggregation/v1/customers/:customerId/accounts/:accountId/details", "host": ["{{url}}"], "path": ["aggregation", "v1", "customers", ":customerId", "accounts", ":accountId", "details"], "variable": [{"key": "customerId", "value": null}, {"key": "accountId", "value": null}]}}, "response": []}], "auth": {"type": "apikey", "apikey": [{"key": "value", "value": "{{<PERSON><PERSON><PERSON><PERSON>}}", "type": "string"}, {"key": "key", "value": "Finicity-App-Key", "type": "string"}]}, "event": [{"listen": "prerequest", "script": {"type": "text/javascript", "exec": [""]}}, {"listen": "test", "script": {"type": "text/javascript", "exec": [""]}}]}