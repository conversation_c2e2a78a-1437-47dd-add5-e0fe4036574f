﻿using BlueTape.Integrations.Aion.Internal;
using FluentValidation;

namespace BlueTape.Aion.API.Validators.InternalTransfer;

public class BaseInternalAccountDetailsModelValidator : AbstractValidator<BaseInternalAccountDetailsModel>
{
    public BaseInternalAccountDetailsModelValidator()
    {
        RuleSet(ValidationConstant.ManualValidation, () =>
        {
            RuleFor(x => x.AccountCode)
                .NotNull();

            RuleFor(x => x.Description)
                .MaximumLength(30)
                .When(x => !string.IsNullOrEmpty(x.Description));
        });
    }
}