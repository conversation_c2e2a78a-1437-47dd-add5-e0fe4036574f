import {client} from "../client";
import {findTranscationByTransactionNumber} from "./find-transaction-by-transcation-number";
import {changeToNextLetter} from "../../utils/string-operations";
import {updateAchTranscation} from "./update-ach-transaction";

export async function setTransactionNumber(transactionNumber) {
    try {
        await client.connect();
        const transaction = await findTranscationByTransactionNumber(transactionNumber);
        const operationID = await transaction.operation_id;
        const changedTransactionNumber = changeToNextLetter(transactionNumber);
        await updateAchTranscation(operationID, changedTransactionNumber);
        return {
            "transactionNumber": changedTransactionNumber,
            "operationID": operationID,
        };
    } catch (e) {
        console.error(e);
    } finally {
        await client.close();
    }
}
