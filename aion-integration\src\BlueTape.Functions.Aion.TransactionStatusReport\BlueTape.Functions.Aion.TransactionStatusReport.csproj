<Project Sdk="Microsoft.NET.Sdk">
    <PropertyGroup>
        <TargetFramework>net8.0</TargetFramework>
        <AzureFunctionsVersion>v4</AzureFunctionsVersion>
        <OutputType>Exe</OutputType>
        <ImplicitUsings>enable</ImplicitUsings>
        <Nullable>enable</Nullable>
    </PropertyGroup>
    <ItemGroup>
        <PackageReference Include="Microsoft.Azure.Functions.Worker" Version="1.20.1" />
        <PackageReference Include="Microsoft.Azure.Functions.Worker.Extensions.Timer" Version="4.3.0" />
        <PackageReference Include="Microsoft.Azure.Functions.Worker.Sdk" Version="1.16.4" />
        <PackageReference Include="Microsoft.ApplicationInsights.WorkerService" Version="2.22.0" />
        <PackageReference Include="Microsoft.Azure.Functions.Worker.Extensions.ServiceBus" Version="5.15.0" />
        <PackageReference Include="Microsoft.Azure.Functions.Worker.ApplicationInsights" Version="1.1.0" />
        
        <PackageReference Include="Azure.Extensions.AspNetCore.Configuration.Secrets" Version="1.3.0" />
        <PackageReference Include="BlueTape.LambdaBase" Version="1.1.1" />
        <PackageReference Include="Serilog.Sinks.ApplicationInsights" Version="4.0.0" />
    </ItemGroup>
    <ItemGroup>
        <None Update="host.json">
            <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
        </None>
        <None Update="local.settings.json">
            <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
            <CopyToPublishDirectory>Never</CopyToPublishDirectory>
        </None>
        <None Update="appsettings.json">
          <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
        </None>
        <None Update="appsettings.beta.json">
          <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
          <DependentUpon>appsettings.json</DependentUpon>
        </None>
        <None Update="appsettings.dev.json">
          <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
          <DependentUpon>appsettings.json</DependentUpon>
        </None>
        <None Update="appsettings.Development.json">
          <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
          <DependentUpon>appsettings.json</DependentUpon>
        </None>
        <None Update="appsettings.prod.json">
          <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
          <DependentUpon>appsettings.json</DependentUpon>
        </None>
        <None Update="appsettings.qa.json">
          <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
          <DependentUpon>appsettings.json</DependentUpon>
        </None>
    </ItemGroup>
    <ItemGroup>
        <Using Include="System.Threading.ExecutionContext" Alias="ExecutionContext" />
    </ItemGroup>
    <ItemGroup>
      <ProjectReference Include="..\BlueTape.Aion.Application\BlueTape.Aion.Application.csproj" />
    </ItemGroup>
</Project>