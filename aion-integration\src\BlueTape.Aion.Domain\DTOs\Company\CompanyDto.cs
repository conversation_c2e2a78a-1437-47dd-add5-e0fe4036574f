﻿namespace BlueTape.Aion.Domain.DTOs.Company;

public class CompanyDto
{
    public string BlueTapeCompanyId { get; set; } = null!;
    public string[]? BankAccounts { get; set; }
    public string? Name { get; set; }
    public string? LegalName { get; set; }
    public string? Email { get; set; } = null!;
    public bool IsBusiness { get; set; } = true;    //If property null - company is business too
    public CompanyAionSettingsDto? AionSettings { get; set; }
}