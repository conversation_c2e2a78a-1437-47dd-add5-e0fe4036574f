openapi: '3.0.0'
info:
  version: '0.0.1'
  title: 'Document Versioning Service API'
  description: | 
    API definition of private service for handling document template versions and managing loans' agreed documents.
servers:
- url: TBD-Dev
  description: Development server
- url: TBD-QA
  description: QA
- url: TBD-Prod
  description: Production server
paths:
  /documents/templates:
    get:
      tags:
        - templates
      summary: Gets document templates actual versions
      description: Gets document templates actual versions
      operationId: getDocumentsTemplatesActualVersions
      parameters:
        - name: code
          description: Code of document template to filter
          in: query
          required: false
          schema:
            type: string
            enum:
              - BNPL_AGREEMENT
              - MASTER_AGREEMENT
              - MANUAL_UPLOAD
      responses:
        200:
          description: List of templates with latest versions.
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/DocumentTemplate'
        400:
          description: Invalid request
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        401:
          description: Unauthorized
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        404:
          description: Not found
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        500:
          description: Unexpected error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
    post:
      tags:
        - templates
      summary: Adds new document template as new actual version from specific date
      description: Adds new document template as new actual version from specific date
      operationId: newDocumentTemplate
      requestBody:
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/NewDocumentTemplate"
      responses:
        201:
          description: The newly added document template.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/DocumentTemplate'
        400:
          description: Invalid request
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        401:
          description: Unauthorized
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        404:
          description: Not found
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        500:
          description: Unexpected error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
  /documents/templates/allversions:
    get:
      tags:
        - templates
      summary: Gets document templates all versions
      description: Gets document templates all versions
      operationId: getDocumentsTemplatesAllVersions
      parameters:
        - name: code
          description: Code of document template to filter
          in: query
          required: false
          schema:
            type: string
            enum:
              - BNPL_AGREEMENT
              - MASTER_AGREEMENT
              - MANUAL_UPLOAD
      responses:
        200:
          description: List of templates.
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/DocumentTemplate'
        400:
          description: Invalid request
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        401:
          description: Unauthorized
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        404:
          description: Not found
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        500:
          description: Unexpected error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
  /documents/templates/{id}:
    get:
      tags:
        - templates
      summary: Gets document template by id
      description: Gets document template by id
      operationId: getDocumentTemplatesById
      parameters:
        - name: id
          description: Identifier of the document template
          in: path
          required: true
          schema:
            type: string
            nullable: false
      responses:
        200:
          description: The document template.
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/DocumentTemplate"
        400:
          description: Invalid request
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        401:
          description: Unauthorized
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        404:
          description: Not found
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        500:
          description: Unexpected error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
  /documents/loans/{id}:
    get:
      tags:
        - documents
      summary: Gets actual versions of loan documents
      description: Gets actual versions of loan documents
      operationId: getActualVersionDocumentsOfLoan
      parameters:
        - name: id
          description: Identifier of the loan
          in: path
          required: true
          schema:
            type: string
            nullable: false
        - name: code
          description: Code of document template to filter
          in: query
          required: false
          schema:
            type: string
            enum:
              - BNPL_AGREEMENT
              - MASTER_AGREEMENT
      responses:
        200:
          description: The list of actual documents of the loan
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/LoanDocument'
        400:
          description: Invalid request
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        401:
          description: Unauthorized
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        404:
          description: Not found
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        500:
          description: Unexpected error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
    post:
      tags:
        - documents
      summary: Adds a new document for a loan. The file should exists in S3.
      description: Adds a new document for a loan. The file should exists in S3.
      operationId: newDocumentForLoan
      parameters:
        - name: id
          description: Identifier of the loan
          in: path
          required: true
          schema:
            type: string
            nullable: false
      requestBody:
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/NewLoanDocument"
      responses:
        201:
          description: The created loan document.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/LoanDocument'
        400:
          description: Invalid request
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        401:
          description: Unauthorized
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        404:
          description: Not found
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        500:
          description: Unexpected error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
  /documents/loans/{id}/new:
    get:
      tags:
        - documents
      summary: Generates a filename for a new, latest version of template for specified loan. Service does not saves anything, just generates a new filename. (Could be deprecated in the future.)
      description: Generates a filename for a new, latest version of template for specified loan. Service does not saves anything, just generates a new filename. (Could be deprecated in the future.)
      operationId: getNewVersionDocumentOfLoan
      parameters:
        - name: id
          description: Identifier of the loan
          in: path
          required: true
          schema:
            type: string
            nullable: false
        - name: code
          description: Code of document template to generate
          in: query
          required: true
          schema:
            type: string
            enum:
              - BNPL_AGREEMENT
              - MASTER_AGREEMENT
      responses:
        200:
          description: The generated filename for loan for latest template
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/NewLoanDocument'
        400:
          description: Invalid request
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        401:
          description: Unauthorized
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        404:
          description: Not found
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        500:
          description: Unexpected error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
  /documents/creditApplication/{id}:
    post:
      tags:
        - documents-creditApplication
      summary: Uploads a document file with note, body is multipart
      description: Uploads a document file with note, body is multipart
      operationId: uploadManualFile
      parameters:
        - name: id
          description: Identifier of the creditApplication
          in: path
          required: true
          schema:
            type: string
            nullable: false
        - name: userId
          description: Identifier of the uploader user
          in: header
          required: true
          schema:
            type: string
            nullable: false
      requestBody:
        content:
          multipart/form-data:
            schema:
              $ref: "#/components/schemas/NewDocument"
      responses:
        201:
          description: The created document.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Document'
        400:
          description: Invalid request
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        404:
          description: Not found
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
    get:
      tags:
        - documents-creditApplication
      summary: Gets manually uploaded files list for a credit application
      description: Gets manually uploaded files list for a credit application
      operationId: getManualFileListCreditApplication
      parameters:
        - name: id
          description: Identifier of the credit application
          in: path
          required: true
          schema:
            type: string
            nullable: false
      responses:
        200:
          description: The created document.
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/Document'
        404:
          description: Not found
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
  /documents/creditApplication/{id}/document/{documentId}:
    get:
      tags:
        - documents-creditApplication
      summary: Downloads a document
      description: Downloads a document
      operationId: getFileCreditApplication
      parameters:
        - name: id
          description: Identifier of the credit application
          in: path
          required: true
          schema:
            type: string
            nullable: false
        - name: documentId
          description: Identifier of the document
          in: path
          required: true
          schema:
            type: string
            nullable: false
      responses:
        200:
          description: The created document.
          content:
            application/json:
              schema:
                type: string
                format: binary
        404:
          description: Not found
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
  /documents/loans/legacy/{legacyId}:
    get:
      tags:
        - documents-legacy
      summary: Gets actual versions of loan documents
      description: Gets actual versions of loan documents
      operationId: getActualVersionDocumentsOfLoanByLegacyId
      parameters:
        - name: legacyId
          description: Legacy identifier of the loan
          in: path
          required: true
          schema:
            type: string
            nullable: false
        - name: code
          description: Code of document template to filter
          in: query
          required: false
          schema:
            type: string
            enum:
              - BNPL_AGREEMENT
              - MASTER_AGREEMENT
      responses:
        200:
          description: The list of actual documents of the loan
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/LoanDocument'
        400:
          description: Invalid request
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        401:
          description: Unauthorized
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        404:
          description: Not found
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        500:
          description: Unexpected error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
    post:
      tags:
        - documents-legacy
      summary: Adds a new document for a loan. The file should exists in S3.
      description: Adds a new document for a loan. The file should exists in S3.
      operationId: newDocumentForLoanByLegacyId
      parameters:
        - name: legacyId
          description: Legacy identifier of the loan
          in: path
          required: true
          schema:
            type: string
            nullable: false
      requestBody:
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/NewLoanDocument"
      responses:
        201:
          description: The created loan document.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/LoanDocument'
        400:
          description: Invalid request
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        401:
          description: Unauthorized
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        404:
          description: Not found
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        500:
          description: Unexpected error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
  /documents/loans/legacy/{legacyId}/new:
    get:
      tags:
        - documents-legacy
      summary: Generates a filename for a new, latest version of template for specified loan. Service does not saves anything, just generates a new filename. (Could be deprecated in the future.)
      description: Generates a filename for a new, latest version of template for specified loan. Service does not saves anything, just generates a new filename. (Could be deprecated in the future.)
      operationId: getNewVersionDocumentOfLoanByLegacyId
      parameters:
        - name: legacyId
          description: Legacy identifier of the loan
          in: path
          required: true
          schema:
            type: string
            nullable: false
        - name: code
          description: Code of document template to generate
          in: query
          required: true
          schema:
            type: string
            enum:
              - BNPL_AGREEMENT
              - MASTER_AGREEMENT
      responses:
        200:
          description: The generated filename for loan for latest template
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/NewLoanDocument'
        400:
          description: Invalid request
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        401:
          description: Unauthorized
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        404:
          description: Not found
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        500:
          description: Unexpected error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
components:
  schemas:
    DocumentTemplate:
      allOf:
        - type: object
          required:
            - id
            - version
          properties:
            id:
              type: string
              format: guid
              description: Id of template version
              example: d3a5d889-47e4-4321-b87b-f816ce5e7b6e
            version:
              type: number
              format: int
              description: Version number of document template
              example: 8
        - $ref: '#/components/schemas/NewDocumentTemplate'
    NewDocumentTemplate:
      type: object
      required: 
        - code
        - s3Url
        - semanticVersion
        - validFrom
      properties:
        code:
          type: string
          description: Type code of template
          enum:
            - BNPL_AGREEMENT
            - MASTER_AGREEMENT
          example: BNPL_AGREEMENT
        s3Url:
          type: string
          description: The S3 url of template file
          example: s3://prod.uw1.linqpal-user-assets/contracts/bnpl_agreement_template.docx
        semanticVersion:
          type: string
          description: Semantic version string of document template
          example: v1.4
        validFrom:
          type: string
          format: date
          description: The date when template becomes valid. All the templates of this type becomes invalid automatically.
          example: 2014-04-05
    LoanDocument:
      type: object
      properties:
        id:
          type: string
          format: guid
          description: Id of document
          example: 535520a6-f6b0-44b7-8852-64518ec665f6
        createdAt:
          type: string
          format: date-time
          description: Time of creation
          example: 2014-04-05T12:59:59.000Z
        loanId:
          type: string
          format: guid
          description: Id of loan
          example: c898fe80-7903-4fd0-90aa-374682dcffd1
        loanLegacyId:
          type: string
          description: Legacy id of loan
          example: 61765f3e615d67289de7ae55
        s3Url:
          type: string
          description: The S3 url of filled document
          example: s3://prod.uw1.linqpal-user-assets/credit_applications/6165fc80009ca0d4d/bnpl_agreement_c898fe80-7903-4fd0-90aa-374682dcffd1.pdf
        template:
          $ref: '#/components/schemas/DocumentTemplate'
    NewLoanDocument:
      type: object
      required:
        - documentTemplateId
        - s3Url
      properties:
        documentTemplateId:
          type: string
          format: guid
          description: Id of document template
          example: 93e633f2-9f4a-46f6-af5c-68170cf1c5c5
        s3Url:
          type: string
          description: The S3 url of new document
          example: s3://prod.uw1.linqpal-user-assets/credit_applications/6165fc80009ca0d4d/bnpl_agreement_0a073d20-65cd-4dfe-9941-82.pdf
    ErrorResponse:
      type: object
      required:
        - statusCode
        - message
        - errorDescription
      properties:
        statusCode:
          description: HTTP status code corresponding to the error.
          type: integer
          example: 400
        message:
          description: A human readable error message.
          type: string
          example: Invalid model.
        errorDescription:
          description: Detailed information.
          type: string
    NewDocument:
      type: object
      properties:
        note:
          type: string
        fileName:
          type: string
        documentFile:
          type: string
          format: binary   
    Document:
      type: object
      properties:
        id:
          type: string
          format: guid
          description: Id of document
          example: 535520a6-f6b0-44b7-8852-64518ec665f6
        createdAt:
          type: string
          format: date-time
          description: Time of creation (upload time)
          example: 2014-04-05T12:59:59.000Z
        uploadedBy:
          type: string
        companyId:
          type: string
          format: guid
          description: Id of company
          example: c898fe80-7903-4fd0-90aa-374682dcffd1
        creditApplicationId:
          type: string
          format: guid
          description: Id of credit application
          example: c898fe80-7903-4fd0-90aa-374682dcffd1
        fileName:
          type: string
        note:
          type: string
        s3Url:
          type: string
          description: The S3 url of filled document
          example: s3://prod.uw1.linqpal-user-assets/credit_applications/6165fc80009ca0d4d/bnpl_agreement_c898fe80-7903-4fd0-90aa-374682dcffd1.pdf
        template:
          $ref: '#/components/schemas/ManualDocumentTemplate'
    ManualDocumentTemplate:
      allOf:
        - type: object
          required:
            - id
          properties:
            id:
              type: string
              format: guid
              description: Id of template version
              example: d3a5d889-47e4-4321-b87b-f816ce5e7b6e
        - $ref: '#/components/schemas/NewManualDocumentTemplate'
    NewManualDocumentTemplate:
      type: object
      required: 
        - code
      properties:
        code:
          type: string
          description: Type code of template
          enum:
            - MANUAL_UPLOAD
          example: MANUAL_UPLOAD
  securitySchemes:
    ApiKey:
      type: apiKey
      in: header
      name: X-Api-Key
security:
  - ApiKey: []