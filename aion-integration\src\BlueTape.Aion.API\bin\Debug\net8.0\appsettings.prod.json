{"Serilog": {"MinimumLevel": {"Default": "Information", "Override": {"AWSSDK": "Warning", "BlueTape.Services.Utilities.AspNetCore.Tracing": "Error", "BlueTape.Services.Utilities.AWS": "Warning", "Microsoft.AspNetCore": "Information", "Microsoft.AspNetCore.DataProtection": "Error", "Microsoft.EntityFrameworkCore": "Warning", "System.Net.Http.HttpClient": "Information", "Microsoft.AspNetCore.HttpLogging.HttpLoggingMiddleware": "Information"}}}, "Services": {"AionServiceApi": "https://bb.aionfi.com", "AionAuthServiceApi": "https://uam.aionfi.com"}, "AionReport": {"DateRange": 7, "BucketName": "prod.uw1.linqpal-aion-reports", "ErrorSnsTopicName": "aion-error-notification-prod"}, "AzureTableOptions": {"AionTransactionConnectionString": ""}, "AzureDataTable": {"ConnectionString": ""}, "AionInternalTransfer": {"AccountNumberSecretNames": {"GL": "", "FBO": "", "SERVICE": "", "REVENUE": "AION-REVENUE-ACCOUNT-NUMBER", "OP": "", "FUNDING": "AION-DISBURSEMENTS-ACCOUNT-NUMBER", "TABAPAY": "", "COLLECTION": "AION-COLLECTIONS-ACCOUNT-NUMBER", "COLLECTIONREPAYMENT": "AION-SPV-COLLECTIONS-ACCOUNT-NUMBER", "DISBURSEMENT": "AION-SPV-DISBURSEMENTS-ACCOUNT-NUMBER", "CARDCOLLECTION": "AION-SPV-CARDCOLLECTIONS-ACCOUNT-NUMBER", "LOCKBOXCOLLECTION": "AION-SPV-<PERSON>OCKBOXCOLLECTIONS-ACCOUNT-NUMBER", "DACACOLLECTION": "AION-SPV-<PERSON><PERSON><PERSON><PERSON>LECTIONS-ACCOUNT-NUMBER", "SPV_COLLECTION_AION": "AION-SPV-COLLECTIONS-AION-ACCOUNT-NUMBER", "SPV_COLLECTION_RAISTONE": "AION-SPV-COLLECTIONS-RAISTONE-ACCOUNT-NUMBER", "SPV_COLLECTION_ARCADIA": "AION-SPV-COLLECTIONS-ARCADIA-ACCOUNT-NUMBER", "SPV_FUNDING_AION": "AION-SPV-FUNDING-AION-ACCOUNT-NUMBER", "SPV_FUNDING_RAISTONE": "AION-SPV-FUNDING-RAISTONE-ACCOUNT-NUMBER", "SPV_FUNDING_ARCADIA": "AION-SPV-FUNDING-ARCADIA-ACCOUNT-NUMBER"}, "AccountIdSecretNames": {"GL": "", "FBO": "", "SERVICE": "", "REVENUE": "AION-REVENUE-ACCOUNT-ID", "OP": "", "FUNDING": "AION-DISBURSEMENTS-ACCOUNT-ID", "TABAPAY": "", "COLLECTION": "AION-COLLECTIONS-ACCOUNT-ID", "COLLECTIONREPAYMENT": "AION-SPV-COLLECTIONS-ACCOUNT-ID", "DISBURSEMENT": "AION-SPV-DISBURSEMENTS-ACCOUNT-ID", "CARDCOLLECTION": "AION-SPV-CARDCOLLECTIONS-ACCOUNT-ID", "LOCKBOXCOLLECTION": "AION-SPV-LOCKBOXCOLLECTIONS-ACCOUNT-ID", "DACACOLLECTION": "AION-SPV-D<PERSON><PERSON>OLLECTIONS-ACCOUNT-ID", "SPV_COLLECTION_AION": "AION-SPV-COLLECTIONS-AION-ACCOUNT-ID", "SPV_COLLECTION_RAISTONE": "AION-SPV-COLLECTIONS-RAISTONE-ACCOUNT-ID", "SPV_COLLECTION_ARCADIA": "AION-SPV-COLLECTIONS-ARCADIA-ACCOUNT-ID", "SPV_FUNDING_AION": "AION-SPV-FUNDING-AION-ACCOUNT-ID", "SPV_FUNDING_RAISTONE": "AION-SPV-FUNDING-RAISTONE-ACCOUNT-ID", "SPV_FUNDING_ARCADIA": "AION-SPV-FUNDING-ARCADIA-ACCOUNT-ID"}}, "BlueTapeOptions": {"AwsSecretName": "bluetape_keys_prod"}, "AllowOrigins": ["https://api.bluetape.com", "https://bluetape.com"], "DailyAchProcessing": {"ReceiverCounterpartyId": "c0753561-dfba-4177-88ea-50eba583a3c1", "ReceiverTransferMethodId": "675b16b6fb339f5708c6d1a5"}}