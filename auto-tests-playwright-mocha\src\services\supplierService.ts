import {Browser, expect} from "@playwright/test";
import {Action} from "../database/autoTests/entities/AutoTestReport";
import {BaseTest} from "../tests/test-utils";
import {CreateInvoiceSimplifyRequest} from "../api/common/genericAPI/requests/createSimplifyInvoiceRequest";
import {createSimplifyInvoice, getCheckOutUrl} from "../api/common/send-generic-api-request";
import {PageManager} from "../objects/pages/page-manager";


export class SupplierService
{
    async addArAdvanceInvoice(
        url: string,
        browser: Browser,
        login: string,
        password: string,
        customerId: string,
        actionsPerTest: Action[]) {
        
        let customerPage = await browser.newPage();
        await customerPage.goto(`${url}`)

        const invoiceId = BaseTest.getGUID();
        const invoiceNumber = `AUTO-FACTOR-${invoiceId}`
        const totalAmount = BaseTest.getRandomNumber(10000, 50000);

        let pageManager = new PageManager(customerPage);
        await pageManager.loginPage.login(login, password)
        await pageManager.sideMenu.openSalesSubTab(pageManager.sideMenu.sideMenuSubTabs.sales.customers);
        await pageManager.customersList.clickOnArAdvanceTab();
        await pageManager.customersList.clickOnCustomerRow(customerId);
        await pageManager.customerDetailsModal.buttons.addInvoice.click();
        await pageManager.addInvoiceModal.fillUpAddInvoice(invoiceNumber, totalAmount.toString(), '0');
        await customerPage.close();

        return { invoiceNumber: invoiceNumber}
    }    async addInvoice(
        url: string,
        browser: Browser,
        login: string,
        password: string,
        customerId: string,
        actionsPerTest: Action[],
        invoiceAmount?: string) {
        
        let customerPage = await browser.newPage();
        await customerPage.goto(`${url}`)

        const invoiceId = BaseTest.getGUID();
        const invoiceNumber = `AUTO-${invoiceId}`
        const totalAmount = invoiceAmount ? parseInt(invoiceAmount)*100 : BaseTest.getRandomNumber(10000, 50000);
        
        actionsPerTest.push({
            description: `Creating invoice with ${invoiceAmount ? 'specified amount: ' + totalAmount : 'random amount: ' + totalAmount}`
        });

        let pageManager = new PageManager(customerPage);
        await pageManager.loginPage.login(login, password)
        await pageManager.sideMenu.openSalesSubTab(pageManager.sideMenu.sideMenuSubTabs.sales.customers);
        await pageManager.customersList.clickOnCustomerRow(customerId);
        await pageManager.customerDetailsModal.buttons.addInvoice.click();
        await pageManager.addInvoiceModal.fillUpAddInvoice(invoiceNumber, totalAmount.toString(), '0');
        await customerPage.close();

        return { invoiceNumber: invoiceNumber}
    }
}
