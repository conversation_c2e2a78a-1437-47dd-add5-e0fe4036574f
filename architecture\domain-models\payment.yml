openapi: '3.0.0'
info:
  version: '0.0.1'
  title: Payment Domain Model
  description: | 
    Model definition of Payment Domain related business models.
paths:
  /PaymentOperation:
    get:
      tags:
        - payment
      summary: Payment Operation model
      description: Payment Operation model
      operationId: getPaymentOperation
      responses:
        200:
          description: Payment Operation model
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/PaymentOperation'
  /PaymentTransaction:
    get:
      tags:
        - payment
      summary: Payment Transaction model
      description: Payment Transaction model
      operationId: getPaymentTransaction
      responses:
        200:
          description: Payment Transaction model
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/PaymentTransaction'
  /ACHTransactionDetails:
    get:
      tags:
        - payment
      summary: ACH Transaction Details model
      description: ACH Transaction Details model
      operationId: getACHTransactionDetails
      responses:
        200:
          description: ACH Transaction Details model
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/AchTransactionDetails'
  /CardPaymentTransactionDetails:
    get:
      tags:
        - payment
      summary: Card Payment Transaction Details model
      description: Card Payment Transaction Details model
      operationId: getCardPaymentTransaction
      responses:
        200:
          description: Card Payment Transaction Details model
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/CardTransactionDetails'
  /VirtualCard:
    get:
      tags:
        - payment
      summary: Virtual Card model
      description: Virtual Card model
      operationId: getVirtualCard
      responses:
        200:
          description: Virtual Card model
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/VirtualCard'
components:
  schemas:
    PaymentOperation:
      type: object
      required:
        - id
        - subject
        - type
        - amount
        - currency
        - status
        - effectiveDate
        - originator
        - beneficiary
        - createdAt
        - updatedAt
        - priority
      properties:
        id:
          type: string
          description: Identifier of operation (BlueTape)
          example: 637dff014189f7dd1fa510a1
        createdAt:
          type: string
          format: datetime
          description: Time of operation creating. Immutable after creation.
          example: 2023-01-04T09:47:57.477Z
        updatedAt:
          type: string
          format: datetime
          description: Time of last operation update. In the time of creation equals to createdAt.
          example: 2023-01-04T09:47:57.477Z
        type:
          type: string
          description: Operation type, same as business event
          example: loan.Repayment
          enum:
            - loan.Repayment
            - loan.Issue
            - loan.Buyback
            - loan.Cancel
            - loan.Service
            - invoice.Payment
            - invoice.Refund
            - invoice.Void
            - invoice.FinalPayment
        subjectId:
          type: string
        originator:
          $ref: '#/components/schemas/ParticipantDetails'
          description: The requester, the payer of the operation.
        beneficiary:
          $ref: '#/components/schemas/ParticipantDetails'
          description: The requested, the payee of the operation.
        amount:
          type: number
          format: decimal
          description: Amount of payment operation without fees
          pattern: '[0-9]*\.[0-9]{2}'
          multipleOf: 0.01
          example: 0.00
        currency:
          type: string
          description: Currency code of operation (ISO 4217)
          pattern: '^[A-Z]{3}$'
          example: USD
        status:
          type: string
          description: Overall status of operation, based on belonging transactions' statuses
          example: placed
          enum:
            - placed
            - processing
            - success
            - fail
            - declined
        effectiveDate:
          type: string
          description: Effective entry date
          format: datetime
          example: 2023-01-04T00:00:00.000Z
        priority:
          type: string
          description: Representation of settlement priority
          example: normal
          enum:
            - normal
            - high
        addenda:
          type: object
          description: Additional values stored along with operation data
          nullable: true
        transactions:
          type: array
          nullable: true
          description: List in transactions made by this operation
          items:
            $ref: '#/components/schemas/PaymentTransaction'
    ParticipantDetails:
      type: object
      required:
        - id
      properties:
        id:
          type: string
          description: Id of participant (companyId)
          example: b079e25663bb593810002681
        name:
          type: string
          description: Name of participant
          example: John Doe
    PaymentTransaction:
      type: object
      required:
        - id
        - operationId
        - amount
        - currency
        - feeAmount
        - paymentMethod
        - status
        - effectiveDate
        - targetAccount
        - number
        - transactionStatus
        - method
        - reference
        - statusCode
        - createdAt
        - updatedAt
        - identificationNumber
        - direction
        - details
      properties:
        id:
          type: string
          description: Identifier of transaction (BlueTape)
          example: f7dd1fa510a1637dff014189
        operationId:
          type: string
          description: Identifier of operation, which initiated the transaction (BlueTape)
          example: 637dff014189f7dd1fa510a1
        createdAt:
          type: string
          format: datetime
          description: Time of transaction creating. Immutable after creation.
          example: 2023-01-04T09:47:57.477Z
        updatedAt:
          type: string
          format: datetime
          description: Time of last transaction update. In the time of creation equals to createdAt.
          example: 2023-01-04T09:47:57.477Z
        payer:
          $ref: '#/components/schemas/ParticipantDetails'
          description: The requester, the payer of the operation.
        payee:
          $ref: '#/components/schemas/ParticipantDetails'
          description: The requested, the payee of the operation.
        sourceAccount:
          $ref: '#/components/schemas/Account'
          description: Source account details of transaction
        targetAccount:
          $ref: '#/components/schemas/Account'
          description: Target account details of transaction
        amount:
          type: number
          format: decimal
          description: Amount of transaction with fixed 2 decimals
          pattern: '[0-9]*\.[0-9]{2}'
          multipleOf: 0.01
          example: 0.00
        currency:
          type: string
          description: Currency code of transaction (ISO 4217)
          pattern: '^[A-Z]{3}$'
          example: USD
        feeAmount:
          type: number
          format: decimal
          description: Amount of applied fees with fixed 2 decimals
          pattern: '[0-9]*\.[0-9]{2}'
          multipleOf: 0.01
          example: 0.00
        paymentMethod:
          type: string
          description: Payment method
          example: ach
          enum:
            - card
            - ach
            - loan
        direction:
          type: string
          description: Direction of transaction (* should be removed)
          example: debit
          enum:
            - debit
            - credit
        effectiveDate:
          type: string
          description: Effective entry date
          format: datetime
          example: 2023-01-04T00:00:00.000Z
        status:
          type: string
          description: Status of transaction
          example: placed
          enum:
            - pending
            - processing
            - success
            - error
            - canceled
            - scheduled
        statusHistory:
          type: array
          description: Status change history
          nullable: true
          items:
            $ref: '#/components/schemas/StatusHistoryItem'
        addenda:
          type: object
          description: Additional values stored along with transaction data
          nullable: true
        details:
          type: object
          oneOf:
            - $ref: '#/components/schemas/AchTransactionDetails'
            - $ref: '#/components/schemas/CardTransactionDetails'
    Account:
      type: object
      required:
        - number
        - id
        - status
      properties:
        number:
          type: string
          description: Account number
        id:
          type: string
          description: Account id
    VirtualCard:
      type: object
      required:
        - cardId
        - companyId
        - name
        - limit
        - currency
        - status
      properties:
        cardId:
          type: string
          description: The card Id on provider side
        provider:
          type: string
          description: Provider name or identifier
          example: CBW
        companyId:
          type: string
          description: Company id where card belongs to
        invoiceId:
          type: string
        name:
          type: string
          description: Name of the card holder
        limit:
          type: number
          format: decimal
          pattern: '[0-9]*\.[0-9]{2}'
          multipleOf: 0.01
          example: 0.00
          description: The limit of the card
        currency:
          type: string
          description: Currency code of card (ISO 4217)
          pattern: '^[A-Z]{3}$'
          example: USD
        status:
          type: string
          description: Card actual status
          enum:
            - activated
            - expired
            - blockedByClient
    StatusHistoryItem:
      type: object
      required:
        - status
        - statusUpdatedAt
      properties:
        status:
          type: string
          description: Status of transaction
          example: placed
          enum:
            - placed
            - processing
            - success
            - error
            - canceled
            - scheduled
        statusUpdatedAt:
          type: string
          format: datetime
          description: Time of status update.
          example: 2023-01-04T09:47:57.477Z
    AchTransactionDetails:
      type: object
      description: Current model represents CBW model
      properties:
        provider:
          type: string
          description: Provider name or identifier
          example: CBW
        referenceNumber:
          type: string
          description: Transaction reference of provider
        originalReferenceNumber:
          type: string
          description: Backreferece for returns and refunds
        batchReferenceNumber:
          type: string
          description: Batch transaction reference of provider
        identificationNumber:
          type: string
          description: Identification number of transaction by the provider, AKA transaction number
        method:
          type: string
          description: Transaction method on provider side
          example: pull
          enum:
            - pull
            - out
            - internal
        errorCode:
          type: string
          description: ErrorCode of ACH transaction
        addenda:
          type: object
          description: Additional values stored along with ACH transaction data
          nullable: true
    CardTransactionDetails:
      type: object
      description: Current model represents Tabapay model
      properties:
        provider:
          type: string
          description: Provider name or identifier
          example: Tabapay
        referenceNumber:
          type: string
          description: Transaction reference or identifier for the provider by initiator
          example: aBnElgzL24QAEUolE3FOUO
        transactionId:
          type: string
          description: Transaction identifier of the provider
        method:
          type: string
          description: Transaction method on provider side
          example: pull
          enum:
            - pull
            - out
            - internal
        network:
          type: string
          example: Visa
        networkRC:
          type: string
          description: Network response code
          example: 00
        networkId:
          type: string
          nullable: true
        approvalCode:
          type: string
          example: 155410
        avsResponseCode:
          type: string
        securityCodeResponseCode:
          type: string
          nullable: true
          example: M
        fees:
          type: array
          items:
            $ref: '#/components/schemas/CardPaymentFees'
        cardLastFour:
          type: string
          example: 0000
        cardExpirationDate:
          type: string
          description: Card expiration date in YYYYMM format
          example: 202312
        addenda:
          type: object
          description: Additional values stored along with ACH transaction data
          nullable: true
    CardPaymentFees:
      type: object
      required:
        - feeType
        - amount
      properties:
        feeType:
          type: string
          description: Type of card payment fee
          enum:
            - interchange
            - network
            - provider
        amount:
          type: number
          format: decimal
          description: Amount of fee. Currency is same as transaction currency.
          pattern: '[0-9]*\.[0-9]{2}'
          multipleOf: 0.01
          example: 0.00
          nullable: true
