using System.Runtime.Serialization;
using System.Text.Json.Serialization;

namespace BlueTape.Aion.DataAccess.External.Models.WireTransfer.Response;

[DataContract]
public class FinancialInstitution
{
    [JsonPropertyName("idCode")]
    public string IdCode { get; set; }

    [JsonPropertyName("identifier")]
    public string Identifier { get; set; }

    [Json<PERSON>ropertyName("name")]
    public string Name { get; set; }

    [JsonPropertyName("address1")]
    public string Address1 { get; set; }

    [JsonPropertyName("address2")]
    public string Address2 { get; set; }

    [JsonPropertyName("address3")]
    public string Address3 { get; set; }
}