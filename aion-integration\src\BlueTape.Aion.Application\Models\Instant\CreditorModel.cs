using System.Runtime.Serialization;
using System.Text.Json.Serialization;

namespace BlueTape.Aion.Application.Models.Instant;

[DataContract]
public class CreditorModel
{
    [JsonPropertyName("routingNumber")]
    public string? RoutingNumber { get; set; }

    [JsonPropertyName("accountNumber")]
    public string? AccountNumber { get; set; }

    [JsonPropertyName("name")]
    public string? Name { get; set; }

    [JsonPropertyName("addressStreetName")]
    public string? AddressStreetName { get; set; }

    [JsonPropertyName("addressLine")]
    public string? AddressLine { get; set; }

    [JsonPropertyName("addressCity")]
    public string? AddressCity { get; set; }

    [JsonPropertyName("addressState")]
    public string? AddressState { get; set; }

    [Json<PERSON>ropertyName("addressPostalCode")]
    public string? AddressPostalCode { get; set; }

    [Json<PERSON>ropertyName("addressCountry")]
    public string? AddressCountry { get; set; }
}