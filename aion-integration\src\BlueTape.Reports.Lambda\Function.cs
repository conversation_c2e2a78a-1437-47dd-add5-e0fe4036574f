using Amazon;
using Amazon.Extensions.NETCore.Setup;
using Amazon.KeyManagementService;
using Amazon.Lambda.Core;
using Amazon.SecretsManager;
using BlueTape.Common.Extensions.Abstractions;
using BlueTape.LambdaBase;
using BlueTape.Services.Utilities.AWS;
using BlueTape.Services.Utilities.Options;
using BlueTape.SNS.SlackNotification.Models;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Serilog;
using Serilog.Context;
using System.Diagnostics.CodeAnalysis;
using System.Globalization;
using System.Net;
using BlueTape.Aion.Application.Abstractions;
using BlueTape.Aion.Application.DI;
using BueTape.Aion.Infrastructure.Exceptions;

// Assembly attribute to enable the Lambda function's JSON input to be converted into a .NET class.
[assembly: LambdaSerializer(typeof(Amazon.Lambda.Serialization.SystemTextJson.DefaultLambdaJsonSerializer))]

namespace BlueTape.Reports.Lambda;

[ExcludeFromCodeCoverage]
public class Function : FunctionBase
{
    private const string BlueTapeCorrelationId = "BlueTapeCorrelationId";
    private ITraceIdAccessor TraceIdAccessor => ServiceProvider.GetRequiredService<ITraceIdAccessor>();
    private IReportService ReportService => ServiceProvider.GetRequiredService<IReportService>();
    private IErrorNotificationService ErrorNotificationService => ServiceProvider.GetRequiredService<IErrorNotificationService>();

    public async Task Handler(ILambdaContext context)
    {
        try
        {
            Log.Logger.Information("Start Transaction status report");

            using (GlobalLogContext.PushProperty("Method", "Scheduler"))
            using (GlobalLogContext.PushProperty(BlueTapeCorrelationId, TraceIdAccessor.TraceId))
            {
            }
        }
        catch (AionRequestException aionRequestException)
        {
            await ErrorNotificationService.Notify(new EventMessageBody
            {
                Message = aionRequestException.Message,
                EventLevel = aionRequestException.SourceHttpStatusCode is HttpStatusCode.OK
                    ? EventLevel.Warning
                    : EventLevel.Error,
                EventName = $"Error during request to external system. RequestPath: {aionRequestException.RequestPath}",
                EventSource = "Reports Lambda",
                ServiceName = "AionService",
                TimeStamp = DateTime.UtcNow.ToString(CultureInfo.InvariantCulture),
                AwsAccountId = Environment.GetEnvironmentVariable("LP_AWS_ACCOUNT") ??
                               Environment.GetEnvironmentVariable("LP_AWS_ACCOUNT") ??
                               "Not provided in service"
            }, CancellationToken.None);

            Log.Logger.Error(aionRequestException, "BlueTape.Reports.Lambda");
            throw;
        }
        catch (AionLoginRequestException aionLoginRequestException)
        {
            await ErrorNotificationService.Notify(new EventMessageBody
            {
                Message = aionLoginRequestException.Message,
                EventLevel = EventLevel.Error,
                EventName =
                    $"Error during request to external system. RequestPath: {aionLoginRequestException.RequestPath}",
                EventSource = "Reports Lambda",
                ServiceName = "AionService",
                TimeStamp = DateTime.UtcNow.ToString(CultureInfo.InvariantCulture),
                AwsAccountId = Environment.GetEnvironmentVariable("LP_AWS_ACCOUNT") ??
                               Environment.GetEnvironmentVariable("LP_AWS_ACCOUNT") ??
                               "Not provided in service"
            }, CancellationToken.None);

            Log.Logger.Error(aionLoginRequestException, "BlueTape.Reports.Lambda");
            throw;
        }
        catch (Exception ex)
        {
            await ErrorNotificationService.Notify(new EventMessageBody
            {
                Message = $"Internal Error was happened during report lambda. ",
                EventLevel = EventLevel.Error,
                EventName = $"API Internal Error",
                EventSource = "Reports Lambda",
                ServiceName = "AionService",
                TimeStamp = DateTime.UtcNow.ToString(CultureInfo.InvariantCulture),
                AwsAccountId = Environment.GetEnvironmentVariable("LP_AWS_ACCOUNT") ??
                               Environment.GetEnvironmentVariable("LP_AWS_ACCOUNT") ??
                               "Not provided in service"
            }, CancellationToken.None);
            Log.Logger.Error(ex, "BlueTape.Reports.Lambda");
            throw;
        }
        finally
        {
            await Log.CloseAndFlushAsync();
        }
    }

    protected override void ConfigureServices(IServiceCollection services, IConfiguration configuration)
    {
        services.AddOptions();
        services.Configure<BlueTapeOptions>(configuration.GetSection(nameof(BlueTapeOptions)));
        services.AddDefaultAWSOptions(configuration.GetAWSOptions());
        services.AddAWSService<IAmazonSecretsManager>(new AWSOptions { Region = RegionEndpoint.USWest1 });
        services.AddAWSService<IAmazonKeyManagementService>(new AWSOptions { Region = RegionEndpoint.USWest1 });
        services.AddSingleton<ISecretsManagerService, AwsSecretsManagerService>();
        services.AddApplicationDependencies(configuration);
        services.AddScoped<ITraceIdAccessor, LambdaTraceIdAccessor>();
    }
}