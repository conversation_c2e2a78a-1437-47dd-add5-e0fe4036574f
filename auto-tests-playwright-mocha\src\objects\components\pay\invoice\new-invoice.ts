import {BasePage} from '../../../base.page';

export class NewInvoices extends BasePage {
    constructor(page){
        super(page);
    };
    
    inputFields = {
        invoiceNumber: this.page.locator('[data-testid="invoice_upload_invoice_number_input"]'),
        dueDate: this.page.locator('[data-testid="invoice_upload_invoice_due_input"]'),
        totalAmount: this.page.locator('[data-testid="invoice_upload_amount_input"]'),
        businessName: this.page.locator('[data-testid="invoice_upload_business_name_input"]'),
        contactName: this.page.locator('[data-testid="invoice_upload_contact_name_input"]'),
        emailAddress: this.page.locator('[data-testid="invoice_upload_email_input"]'),
        phoneNumber: this.page.locator('[data-testid="invoice_upload_phone_input"]'),
    };

    buttons = {
        next: this.page.locator('[data-testid="invoice_upload_next_btn"]'),
        cancel: this.page.locator('[data-testid="invoice_upload_cancel_btn"]'),
        add: this.page.locator('"Add"'),
    };

    async fillUpInvoiceDetails(invoiceNumber, dueDate, invoiceAmount, businessName){
        await this.inputFields.invoiceNumber.fill(invoiceNumber);
        await this.inputFields.dueDate.fill(dueDate);
        await this.inputFields.totalAmount.fill(invoiceAmount);
        await this.buttons.next.click();
        await this.inputFields.businessName.fill(businessName);
        await this.buttons.add.click();
    };
}