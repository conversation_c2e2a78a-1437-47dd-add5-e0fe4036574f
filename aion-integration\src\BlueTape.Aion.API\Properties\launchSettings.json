{"iisSettings": {"windowsAuthentication": false, "anonymousAuthentication": true, "iisExpress": {"applicationUrl": "http://localhost:51595", "sslPort": 44309}}, "profiles": {"DEV BlueTape.Aion.API": {"commandName": "Project", "dotnetRunMessages": true, "launchBrowser": true, "launchUrl": "swagger", "applicationUrl": "http://localhost:8001", "environmentVariables": {"ASPNETCORE_ENVIRONMENT": "dev", "AWS_REGION": "us-west-1", "LP_AWS_ACCOUNT": "************", "Branch": "Local", "KEYVAULT_URI": "https://keyvault-dev-17b195c92e.vault.azure.net/"}}, "BETA BlueTape.Aion.API": {"commandName": "Project", "dotnetRunMessages": true, "launchBrowser": true, "launchUrl": "swagger", "applicationUrl": "http://localhost:8001", "environmentVariables": {"ASPNETCORE_ENVIRONMENT": "beta", "AWS_REGION": "us-west-1", "LP_AWS_ACCOUNT": "************", "Branch": "Local", "KEYVAULT_URI": "https://keyvault-beta-10ea6a9eb6.vault.azure.net/"}}, "QA BlueTape.Aion.API": {"commandName": "Project", "dotnetRunMessages": true, "launchBrowser": true, "launchUrl": "swagger", "applicationUrl": "http://localhost:8001", "environmentVariables": {"ASPNETCORE_ENVIRONMENT": "qa", "AWS_REGION": "us-west-1", "LP_AWS_ACCOUNT": "************", "Branch": "Local", "KEYVAULT_URI": "https://keyvault-qa-b2c7f314826.vault.azure.net/"}}, "PROD BlueTape.Aion.API": {"commandName": "Project", "dotnetRunMessages": true, "launchBrowser": true, "launchUrl": "swagger", "applicationUrl": "http://localhost:8001", "environmentVariables": {"ASPNETCORE_ENVIRONMENT": "prod", "AWS_REGION": "us-west-1", "LP_AWS_ACCOUNT": "************", "Branch": "Local", "KEYVAULT_URI": "https://keyvault-prod-c3b7f21532.vault.azure.net/"}}, "Mock Lambda Test Tool": {"commandName": "Executable", "commandLineArgs": "--port 5050", "workingDirectory": ".\\bin\\$(Configuration)\\net8.0", "executablePath": "%USERPROFILE%\\.dotnet\\tools\\dotnet-lambda-test-tool-8.0.exe"}}}