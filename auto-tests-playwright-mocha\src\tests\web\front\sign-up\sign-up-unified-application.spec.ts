import {Page, expect} from '@playwright/test';
import {test, BaseTest} from '../../../test-utils';
import {PageManager} from '../../../../objects/pages/page-manager';
import {BaseAPI} from '../../../../api/base-api';
import {deleteUser, getUserSub} from "../../../../api/admin";

test.use({storageState: {cookies: [], origins: []}});

test.describe('Sign up Unified application.', async () => {
    let page: Page;
    let email: string;
    let firstName: string;
    let lastName: string;
    let businessName: string;
    let accountName: string;
    let cellPhoneNumber: string;

    test.beforeEach(async ({browser}) => {
        email = `automation_user+${BaseTest.dateTimePrefix()}@bluetape.com`;
        firstName = BaseTest.dateTimePrefix() + 'firstName';
        lastName = BaseTest.dateTimePrefix() + 'testlastName';
        businessName = BaseTest.dateTimePrefix() + 'testbusinessName';
        accountName = BaseTest.dateTimePrefix() + 'testaccountName';
        cellPhoneNumber = '************';

        const googleIDToken = await BaseAPI.googleSignUp(email, BaseTest.constants.password);
        if (googleIDToken === null) {
            test.fail(true, 'idToken is empty.');
        }
        await BaseAPI.userSignUp(businessName, email, firstName, googleIDToken, lastName, cellPhoneNumber);
        page = await browser.newPage();
        await BaseTest.verificationLinkSignIn(page, email);
    });

    test.afterEach(async ({adminIdToken}) => {
        const userSub = await getUserSub(adminIdToken, email);
        await deleteUser(adminIdToken, userSub);
        await page.close();
    });

    test(`Unified application sign up feature. @smoke5`, async () => {
        test.slow();
        const pageManager = new PageManager(page);
        await pageManager.loginPage.login(email, BaseTest.constants.password);
        await pageManager.onBoardingPage.chooseRole('Dealer / Retailer / Supplier');
        await pageManager.onBoardingPage.buttons.skip.click();
        await expect(pageManager.home.labels.emailVerified, 'Email is verified on the main page should be visible.').toBeVisible();

        await pageManager.unifiedApplication.buttons.start.click();
        await expect(pageManager.unifiedApplication.businessDetails.businessName, `Business name field should be pre-filled with ${businessName}`).toHaveValue(businessName);
        await pageManager.unifiedApplication.buttons.next.click();
        await expect(pageManager.unifiedApplication.businessDetails.businessPhoneNumber, `Business phone number field should be pre-filled with ${cellPhoneNumber}`).toHaveValue(cellPhoneNumber);
        await pageManager.unifiedApplication.buttons.next.click();
        await pageManager.unifiedApplication.fillUpBusinessAddress(BaseTest.constants.address.street);

        await expect(pageManager.unifiedApplication.businessDetails.businessZipCode).toHaveValue(BaseTest.constants.address.zipCode);
        await expect(pageManager.unifiedApplication.businessDetails.businessCity).toHaveValue(BaseTest.constants.address.city);
        await expect(pageManager.unifiedApplication.businessDetails.businessState).toHaveValue((BaseTest.constants.address.state));
        await pageManager.unifiedApplication.fillUpBusinessDetailsForBusinessOwner('12/2000', '*********', '500000', '100');
        await pageManager.unifiedApplication.fillUpUsersDetails(BaseTest.constants.address.street, '********', BaseTest.constants.user.socialSecurityNumber);
        await pageManager.unifiedApplication.fillUpBankInformation(BaseTest.constants.bankAccount.bankName, accountName, BaseTest.constants.bankAccount.accountNumber, BaseTest.constants.bankAccount.routingNumber);
        await expect(pageManager.unifiedApplication.applicationDetails.filledSections, 'All sections should be filled').toBeVisible();

        await page.waitForTimeout(2000); // wait for bank account request to proceed
        await pageManager.unifiedApplication.buttons.submit.click();
        await expect(pageManager.unifiedApplication.applicationDetails.applicationSubmitted, 'Application Submitted message should be visible').toBeVisible();
        await pageManager.unifiedApplication.applicationDetails.okayBtn.click();

        await pageManager.sideMenu.openPaySubTab(pageManager.sideMenu.sideMenuSubTabs.pay.credit);
        await pageManager.page.waitForLoadState('networkidle');
        await pageManager.creditList.buttons.getPrequalified.click({delay: 500});
        await pageManager.creditRequest.getPrequalifiedCredit('1000', '10000'); // TODO: solve the problem with getPrequalifiedCredit button doesn't go off.
        await expect(pageManager.unifiedApplication.applicationDetails.filledSections, 'All sections should be filled').toBeVisible();
        await pageManager.creditRequest.buttons.submit.click();

        await expect(pageManager.creditRequest.applicationDetails.applicationSubmitted, 'Application Submitted message should be visible').toBeVisible();
        await pageManager.creditRequest.applicationDetails.okayBtn.click();

        await page.goto(`${process.env.CI_ENVIRONMENT_BACKEND_URL}`);
        await pageManager.backOfficeLoginPage.login(`${process.env.ADMIN_EMAIL}`, `${process.env.ADMIN_PASSWORD}`);
        await pageManager.backOfficeSideMenu.sideMenuTabs.supplierApplication.click();
        await pageManager.supplierApplication.clickOnSupplierApplication(email);
        await pageManager.supplierApplication.selectCardLoanPackages(email);
        await pageManager.supplierApplication.buttons.edit.click();
        await pageManager.supplierApplicationDetailsModal.buttons.approve.click();
        await pageManager.supplierApplicationDetailsModal.buttons.ok.click();
        await pageManager.supplierApplication.waitForApplicationStatusToUpdate(email);

        await page.goto(`${process.env.CI_ENVIRONMENT_URL}`);
        await expect(pageManager.home.labels.emailVerified, 'Application approved on the main page should be visible.').toBeVisible();
    });
});
