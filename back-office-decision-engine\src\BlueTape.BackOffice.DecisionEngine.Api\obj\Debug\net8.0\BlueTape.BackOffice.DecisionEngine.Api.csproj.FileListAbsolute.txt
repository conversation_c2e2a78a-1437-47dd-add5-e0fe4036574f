C:\Users\<USER>\source\repos\BlueTape\back-office-decision-engine\src\BlueTape.BackOffice.DecisionEngine.Api\bin\Debug\net8.0\appsettings.beta.json
C:\Users\<USER>\source\repos\BlueTape\back-office-decision-engine\src\BlueTape.BackOffice.DecisionEngine.Api\bin\Debug\net8.0\appsettings.dev.json
C:\Users\<USER>\source\repos\BlueTape\back-office-decision-engine\src\BlueTape.BackOffice.DecisionEngine.Api\bin\Debug\net8.0\appsettings.json
C:\Users\<USER>\source\repos\BlueTape\back-office-decision-engine\src\BlueTape.BackOffice.DecisionEngine.Api\bin\Debug\net8.0\appsettings.prod.json
C:\Users\<USER>\source\repos\BlueTape\back-office-decision-engine\src\BlueTape.BackOffice.DecisionEngine.Api\bin\Debug\net8.0\appsettings.qa.json
C:\Users\<USER>\source\repos\BlueTape\back-office-decision-engine\src\BlueTape.BackOffice.DecisionEngine.Api\bin\Debug\net8.0\BlueTape.BackOffice.DecisionEngine.Api.staticwebassets.endpoints.json
C:\Users\<USER>\source\repos\BlueTape\back-office-decision-engine\src\BlueTape.BackOffice.DecisionEngine.Api\bin\Debug\net8.0\BlueTape.BackOffice.DecisionEngine.Api.exe
C:\Users\<USER>\source\repos\BlueTape\back-office-decision-engine\src\BlueTape.BackOffice.DecisionEngine.Api\bin\Debug\net8.0\BlueTape.BackOffice.DecisionEngine.Api.deps.json
C:\Users\<USER>\source\repos\BlueTape\back-office-decision-engine\src\BlueTape.BackOffice.DecisionEngine.Api\bin\Debug\net8.0\BlueTape.BackOffice.DecisionEngine.Api.runtimeconfig.json
C:\Users\<USER>\source\repos\BlueTape\back-office-decision-engine\src\BlueTape.BackOffice.DecisionEngine.Api\bin\Debug\net8.0\BlueTape.BackOffice.DecisionEngine.Api.dll
C:\Users\<USER>\source\repos\BlueTape\back-office-decision-engine\src\BlueTape.BackOffice.DecisionEngine.Api\bin\Debug\net8.0\BlueTape.BackOffice.DecisionEngine.Api.pdb
C:\Users\<USER>\source\repos\BlueTape\back-office-decision-engine\src\BlueTape.BackOffice.DecisionEngine.Api\bin\Debug\net8.0\AutoMapper.dll
C:\Users\<USER>\source\repos\BlueTape\back-office-decision-engine\src\BlueTape.BackOffice.DecisionEngine.Api\bin\Debug\net8.0\AutoMapper.Extensions.ExpressionMapping.dll
C:\Users\<USER>\source\repos\BlueTape\back-office-decision-engine\src\BlueTape.BackOffice.DecisionEngine.Api\bin\Debug\net8.0\AutoMapper.Extensions.Microsoft.DependencyInjection.dll
C:\Users\<USER>\source\repos\BlueTape\back-office-decision-engine\src\BlueTape.BackOffice.DecisionEngine.Api\bin\Debug\net8.0\AWSSDK.Core.dll
C:\Users\<USER>\source\repos\BlueTape\back-office-decision-engine\src\BlueTape.BackOffice.DecisionEngine.Api\bin\Debug\net8.0\AWSSDK.Extensions.NETCore.Setup.dll
C:\Users\<USER>\source\repos\BlueTape\back-office-decision-engine\src\BlueTape.BackOffice.DecisionEngine.Api\bin\Debug\net8.0\AWSSDK.KeyManagementService.dll
C:\Users\<USER>\source\repos\BlueTape\back-office-decision-engine\src\BlueTape.BackOffice.DecisionEngine.Api\bin\Debug\net8.0\AWSSDK.S3.dll
C:\Users\<USER>\source\repos\BlueTape\back-office-decision-engine\src\BlueTape.BackOffice.DecisionEngine.Api\bin\Debug\net8.0\AWSSDK.SecretsManager.dll
C:\Users\<USER>\source\repos\BlueTape\back-office-decision-engine\src\BlueTape.BackOffice.DecisionEngine.Api\bin\Debug\net8.0\Amazon.SecretsManager.Extensions.Caching.dll
C:\Users\<USER>\source\repos\BlueTape\back-office-decision-engine\src\BlueTape.BackOffice.DecisionEngine.Api\bin\Debug\net8.0\AWSSDK.SecurityToken.dll
C:\Users\<USER>\source\repos\BlueTape\back-office-decision-engine\src\BlueTape.BackOffice.DecisionEngine.Api\bin\Debug\net8.0\AWSSDK.SimpleNotificationService.dll
C:\Users\<USER>\source\repos\BlueTape\back-office-decision-engine\src\BlueTape.BackOffice.DecisionEngine.Api\bin\Debug\net8.0\AWSSDK.StepFunctions.dll
C:\Users\<USER>\source\repos\BlueTape\back-office-decision-engine\src\BlueTape.BackOffice.DecisionEngine.Api\bin\Debug\net8.0\Azure.Core.dll
C:\Users\<USER>\source\repos\BlueTape\back-office-decision-engine\src\BlueTape.BackOffice.DecisionEngine.Api\bin\Debug\net8.0\Azure.Core.Amqp.dll
C:\Users\<USER>\source\repos\BlueTape\back-office-decision-engine\src\BlueTape.BackOffice.DecisionEngine.Api\bin\Debug\net8.0\Azure.Extensions.AspNetCore.Configuration.Secrets.dll
C:\Users\<USER>\source\repos\BlueTape\back-office-decision-engine\src\BlueTape.BackOffice.DecisionEngine.Api\bin\Debug\net8.0\Azure.Identity.dll
C:\Users\<USER>\source\repos\BlueTape\back-office-decision-engine\src\BlueTape.BackOffice.DecisionEngine.Api\bin\Debug\net8.0\Azure.Messaging.ServiceBus.dll
C:\Users\<USER>\source\repos\BlueTape\back-office-decision-engine\src\BlueTape.BackOffice.DecisionEngine.Api\bin\Debug\net8.0\Azure.Security.KeyVault.Keys.dll
C:\Users\<USER>\source\repos\BlueTape\back-office-decision-engine\src\BlueTape.BackOffice.DecisionEngine.Api\bin\Debug\net8.0\Azure.Security.KeyVault.Secrets.dll
C:\Users\<USER>\source\repos\BlueTape\back-office-decision-engine\src\BlueTape.BackOffice.DecisionEngine.Api\bin\Debug\net8.0\BlueTape.AWSS3.dll
C:\Users\<USER>\source\repos\BlueTape\back-office-decision-engine\src\BlueTape.BackOffice.DecisionEngine.Api\bin\Debug\net8.0\BlueTape.AWSStepFunction.dll
C:\Users\<USER>\source\repos\BlueTape\back-office-decision-engine\src\BlueTape.BackOffice.DecisionEngine.Api\bin\Debug\net8.0\BlueTape.AzureKeyVault.dll
C:\Users\<USER>\source\repos\BlueTape\back-office-decision-engine\src\BlueTape.BackOffice.DecisionEngine.Api\bin\Debug\net8.0\BlueTape.Common.ExceptionHandling.dll
C:\Users\<USER>\source\repos\BlueTape\back-office-decision-engine\src\BlueTape.BackOffice.DecisionEngine.Api\bin\Debug\net8.0\BlueTape.Common.Extensions.dll
C:\Users\<USER>\source\repos\BlueTape\back-office-decision-engine\src\BlueTape.BackOffice.DecisionEngine.Api\bin\Debug\net8.0\BlueTape.Common.FileService.dll
C:\Users\<USER>\source\repos\BlueTape\back-office-decision-engine\src\BlueTape.BackOffice.DecisionEngine.Api\bin\Debug\net8.0\BlueTape.Common.Validation.dll
C:\Users\<USER>\source\repos\BlueTape\back-office-decision-engine\src\BlueTape.BackOffice.DecisionEngine.Api\bin\Debug\net8.0\BlueTape.CompanyService.dll
C:\Users\<USER>\source\repos\BlueTape\back-office-decision-engine\src\BlueTape.BackOffice.DecisionEngine.Api\bin\Debug\net8.0\BlueTape.CompanyService.Common.dll
C:\Users\<USER>\source\repos\BlueTape\back-office-decision-engine\src\BlueTape.BackOffice.DecisionEngine.Api\bin\Debug\net8.0\BlueTape.Firebase.dll
C:\Users\<USER>\source\repos\BlueTape\back-office-decision-engine\src\BlueTape.BackOffice.DecisionEngine.Api\bin\Debug\net8.0\BlueTape.Integrations.Aion.dll
C:\Users\<USER>\source\repos\BlueTape\back-office-decision-engine\src\BlueTape.BackOffice.DecisionEngine.Api\bin\Debug\net8.0\BlueTape.Integrations.Aion.Infrastructure.dll
C:\Users\<USER>\source\repos\BlueTape\back-office-decision-engine\src\BlueTape.BackOffice.DecisionEngine.Api\bin\Debug\net8.0\BlueTape.Integrations.Giact.dll
C:\Users\<USER>\source\repos\BlueTape\back-office-decision-engine\src\BlueTape.BackOffice.DecisionEngine.Api\bin\Debug\net8.0\BlueTape.Integrations.Plaid.dll
C:\Users\<USER>\source\repos\BlueTape\back-office-decision-engine\src\BlueTape.BackOffice.DecisionEngine.Api\bin\Debug\net8.0\BlueTape.Integrations.Plaid.Infrastructure.dll
C:\Users\<USER>\source\repos\BlueTape\back-office-decision-engine\src\BlueTape.BackOffice.DecisionEngine.Api\bin\Debug\net8.0\BlueTape.InvoiceClient.dll
C:\Users\<USER>\source\repos\BlueTape\back-office-decision-engine\src\BlueTape.BackOffice.DecisionEngine.Api\bin\Debug\net8.0\BlueTape.InvoiceService.dll
C:\Users\<USER>\source\repos\BlueTape\back-office-decision-engine\src\BlueTape.BackOffice.DecisionEngine.Api\bin\Debug\net8.0\BlueTape.InvoiceService.Common.dll
C:\Users\<USER>\source\repos\BlueTape\back-office-decision-engine\src\BlueTape.BackOffice.DecisionEngine.Api\bin\Debug\net8.0\BlueTape.Logging.dll
C:\Users\<USER>\source\repos\BlueTape\back-office-decision-engine\src\BlueTape.BackOffice.DecisionEngine.Api\bin\Debug\net8.0\BlueTape.LS.dll
C:\Users\<USER>\source\repos\BlueTape\back-office-decision-engine\src\BlueTape.BackOffice.DecisionEngine.Api\bin\Debug\net8.0\BlueTape.LS.Domain.dll
C:\Users\<USER>\source\repos\BlueTape\back-office-decision-engine\src\BlueTape.BackOffice.DecisionEngine.Api\bin\Debug\net8.0\BlueTape.OBS.dll
C:\Users\<USER>\source\repos\BlueTape\back-office-decision-engine\src\BlueTape.BackOffice.DecisionEngine.Api\bin\Debug\net8.0\BlueTape.PaymentService.dll
C:\Users\<USER>\source\repos\BlueTape\back-office-decision-engine\src\BlueTape.BackOffice.DecisionEngine.Api\bin\Debug\net8.0\BlueTape.ServiceBusMessaging.dll
C:\Users\<USER>\source\repos\BlueTape\back-office-decision-engine\src\BlueTape.BackOffice.DecisionEngine.Api\bin\Debug\net8.0\BlueTape.SNS.dll
C:\Users\<USER>\source\repos\BlueTape\back-office-decision-engine\src\BlueTape.BackOffice.DecisionEngine.Api\bin\Debug\net8.0\BlueTape.Utilities.dll
C:\Users\<USER>\source\repos\BlueTape\back-office-decision-engine\src\BlueTape.BackOffice.DecisionEngine.Api\bin\Debug\net8.0\ClosedXML.dll
C:\Users\<USER>\source\repos\BlueTape\back-office-decision-engine\src\BlueTape.BackOffice.DecisionEngine.Api\bin\Debug\net8.0\CsvHelper.dll
C:\Users\<USER>\source\repos\BlueTape\back-office-decision-engine\src\BlueTape.BackOffice.DecisionEngine.Api\bin\Debug\net8.0\DateOnlyTimeOnly.AspNet.dll
C:\Users\<USER>\source\repos\BlueTape\back-office-decision-engine\src\BlueTape.BackOffice.DecisionEngine.Api\bin\Debug\net8.0\DateOnlyTimeOnly.AspNet.Swashbuckle.dll
C:\Users\<USER>\source\repos\BlueTape\back-office-decision-engine\src\BlueTape.BackOffice.DecisionEngine.Api\bin\Debug\net8.0\DocumentFormat.OpenXml.dll
C:\Users\<USER>\source\repos\BlueTape\back-office-decision-engine\src\BlueTape.BackOffice.DecisionEngine.Api\bin\Debug\net8.0\Elastic.CommonSchema.dll
C:\Users\<USER>\source\repos\BlueTape\back-office-decision-engine\src\BlueTape.BackOffice.DecisionEngine.Api\bin\Debug\net8.0\Elastic.CommonSchema.Serilog.dll
C:\Users\<USER>\source\repos\BlueTape\back-office-decision-engine\src\BlueTape.BackOffice.DecisionEngine.Api\bin\Debug\net8.0\ExcelNumberFormat.dll
C:\Users\<USER>\source\repos\BlueTape\back-office-decision-engine\src\BlueTape.BackOffice.DecisionEngine.Api\bin\Debug\net8.0\FirebaseAdmin.dll
C:\Users\<USER>\source\repos\BlueTape\back-office-decision-engine\src\BlueTape.BackOffice.DecisionEngine.Api\bin\Debug\net8.0\FluentValidation.dll
C:\Users\<USER>\source\repos\BlueTape\back-office-decision-engine\src\BlueTape.BackOffice.DecisionEngine.Api\bin\Debug\net8.0\FluentValidation.AspNetCore.dll
C:\Users\<USER>\source\repos\BlueTape\back-office-decision-engine\src\BlueTape.BackOffice.DecisionEngine.Api\bin\Debug\net8.0\FluentValidation.DependencyInjectionExtensions.dll
C:\Users\<USER>\source\repos\BlueTape\back-office-decision-engine\src\BlueTape.BackOffice.DecisionEngine.Api\bin\Debug\net8.0\Google.Api.Gax.dll
C:\Users\<USER>\source\repos\BlueTape\back-office-decision-engine\src\BlueTape.BackOffice.DecisionEngine.Api\bin\Debug\net8.0\Google.Api.Gax.Rest.dll
C:\Users\<USER>\source\repos\BlueTape\back-office-decision-engine\src\BlueTape.BackOffice.DecisionEngine.Api\bin\Debug\net8.0\Google.Apis.dll
C:\Users\<USER>\source\repos\BlueTape\back-office-decision-engine\src\BlueTape.BackOffice.DecisionEngine.Api\bin\Debug\net8.0\Google.Apis.Auth.PlatformServices.dll
C:\Users\<USER>\source\repos\BlueTape\back-office-decision-engine\src\BlueTape.BackOffice.DecisionEngine.Api\bin\Debug\net8.0\Google.Apis.Auth.dll
C:\Users\<USER>\source\repos\BlueTape\back-office-decision-engine\src\BlueTape.BackOffice.DecisionEngine.Api\bin\Debug\net8.0\Google.Apis.Core.dll
C:\Users\<USER>\source\repos\BlueTape\back-office-decision-engine\src\BlueTape.BackOffice.DecisionEngine.Api\bin\Debug\net8.0\Irony.dll
C:\Users\<USER>\source\repos\BlueTape\back-office-decision-engine\src\BlueTape.BackOffice.DecisionEngine.Api\bin\Debug\net8.0\PhoneNumbers.dll
C:\Users\<USER>\source\repos\BlueTape\back-office-decision-engine\src\BlueTape.BackOffice.DecisionEngine.Api\bin\Debug\net8.0\Microsoft.ApplicationInsights.dll
C:\Users\<USER>\source\repos\BlueTape\back-office-decision-engine\src\BlueTape.BackOffice.DecisionEngine.Api\bin\Debug\net8.0\Microsoft.ApplicationInsights.AspNetCore.dll
C:\Users\<USER>\source\repos\BlueTape\back-office-decision-engine\src\BlueTape.BackOffice.DecisionEngine.Api\bin\Debug\net8.0\Microsoft.AI.DependencyCollector.dll
C:\Users\<USER>\source\repos\BlueTape\back-office-decision-engine\src\BlueTape.BackOffice.DecisionEngine.Api\bin\Debug\net8.0\Microsoft.AI.EventCounterCollector.dll
C:\Users\<USER>\source\repos\BlueTape\back-office-decision-engine\src\BlueTape.BackOffice.DecisionEngine.Api\bin\Debug\net8.0\Microsoft.AI.PerfCounterCollector.dll
C:\Users\<USER>\source\repos\BlueTape\back-office-decision-engine\src\BlueTape.BackOffice.DecisionEngine.Api\bin\Debug\net8.0\Microsoft.AI.WindowsServer.dll
C:\Users\<USER>\source\repos\BlueTape\back-office-decision-engine\src\BlueTape.BackOffice.DecisionEngine.Api\bin\Debug\net8.0\Microsoft.AI.ServerTelemetryChannel.dll
C:\Users\<USER>\source\repos\BlueTape\back-office-decision-engine\src\BlueTape.BackOffice.DecisionEngine.Api\bin\Debug\net8.0\Microsoft.AspNetCore.OpenApi.dll
C:\Users\<USER>\source\repos\BlueTape\back-office-decision-engine\src\BlueTape.BackOffice.DecisionEngine.Api\bin\Debug\net8.0\Microsoft.Azure.Amqp.dll
C:\Users\<USER>\source\repos\BlueTape\back-office-decision-engine\src\BlueTape.BackOffice.DecisionEngine.Api\bin\Debug\net8.0\Microsoft.Bcl.AsyncInterfaces.dll
C:\Users\<USER>\source\repos\BlueTape\back-office-decision-engine\src\BlueTape.BackOffice.DecisionEngine.Api\bin\Debug\net8.0\Microsoft.Extensions.DependencyModel.dll
C:\Users\<USER>\source\repos\BlueTape\back-office-decision-engine\src\BlueTape.BackOffice.DecisionEngine.Api\bin\Debug\net8.0\Microsoft.Extensions.Http.Polly.dll
C:\Users\<USER>\source\repos\BlueTape\back-office-decision-engine\src\BlueTape.BackOffice.DecisionEngine.Api\bin\Debug\net8.0\Microsoft.Extensions.Logging.ApplicationInsights.dll
C:\Users\<USER>\source\repos\BlueTape\back-office-decision-engine\src\BlueTape.BackOffice.DecisionEngine.Api\bin\Debug\net8.0\Microsoft.Extensions.Options.dll
C:\Users\<USER>\source\repos\BlueTape\back-office-decision-engine\src\BlueTape.BackOffice.DecisionEngine.Api\bin\Debug\net8.0\Microsoft.Identity.Client.dll
C:\Users\<USER>\source\repos\BlueTape\back-office-decision-engine\src\BlueTape.BackOffice.DecisionEngine.Api\bin\Debug\net8.0\Microsoft.Identity.Client.Extensions.Msal.dll
C:\Users\<USER>\source\repos\BlueTape\back-office-decision-engine\src\BlueTape.BackOffice.DecisionEngine.Api\bin\Debug\net8.0\Microsoft.IdentityModel.Abstractions.dll
C:\Users\<USER>\source\repos\BlueTape\back-office-decision-engine\src\BlueTape.BackOffice.DecisionEngine.Api\bin\Debug\net8.0\Microsoft.OpenApi.dll
C:\Users\<USER>\source\repos\BlueTape\back-office-decision-engine\src\BlueTape.BackOffice.DecisionEngine.Api\bin\Debug\net8.0\Microsoft.Win32.SystemEvents.dll
C:\Users\<USER>\source\repos\BlueTape\back-office-decision-engine\src\BlueTape.BackOffice.DecisionEngine.Api\bin\Debug\net8.0\MongoDB.Bson.dll
C:\Users\<USER>\source\repos\BlueTape\back-office-decision-engine\src\BlueTape.BackOffice.DecisionEngine.Api\bin\Debug\net8.0\Newtonsoft.Json.dll
C:\Users\<USER>\source\repos\BlueTape\back-office-decision-engine\src\BlueTape.BackOffice.DecisionEngine.Api\bin\Debug\net8.0\Polly.dll
C:\Users\<USER>\source\repos\BlueTape\back-office-decision-engine\src\BlueTape.BackOffice.DecisionEngine.Api\bin\Debug\net8.0\Polly.Extensions.Http.dll
C:\Users\<USER>\source\repos\BlueTape\back-office-decision-engine\src\BlueTape.BackOffice.DecisionEngine.Api\bin\Debug\net8.0\Serilog.dll
C:\Users\<USER>\source\repos\BlueTape\back-office-decision-engine\src\BlueTape.BackOffice.DecisionEngine.Api\bin\Debug\net8.0\Serilog.AspNetCore.dll
C:\Users\<USER>\source\repos\BlueTape\back-office-decision-engine\src\BlueTape.BackOffice.DecisionEngine.Api\bin\Debug\net8.0\Serilog.Enrichers.GlobalLogContext.dll
C:\Users\<USER>\source\repos\BlueTape\back-office-decision-engine\src\BlueTape.BackOffice.DecisionEngine.Api\bin\Debug\net8.0\Serilog.Extensions.Hosting.dll
C:\Users\<USER>\source\repos\BlueTape\back-office-decision-engine\src\BlueTape.BackOffice.DecisionEngine.Api\bin\Debug\net8.0\Serilog.Extensions.Logging.dll
C:\Users\<USER>\source\repos\BlueTape\back-office-decision-engine\src\BlueTape.BackOffice.DecisionEngine.Api\bin\Debug\net8.0\Serilog.Formatting.Compact.dll
C:\Users\<USER>\source\repos\BlueTape\back-office-decision-engine\src\BlueTape.BackOffice.DecisionEngine.Api\bin\Debug\net8.0\Serilog.Settings.Configuration.dll
C:\Users\<USER>\source\repos\BlueTape\back-office-decision-engine\src\BlueTape.BackOffice.DecisionEngine.Api\bin\Debug\net8.0\Serilog.Sinks.ApplicationInsights.dll
C:\Users\<USER>\source\repos\BlueTape\back-office-decision-engine\src\BlueTape.BackOffice.DecisionEngine.Api\bin\Debug\net8.0\Serilog.Sinks.Console.dll
C:\Users\<USER>\source\repos\BlueTape\back-office-decision-engine\src\BlueTape.BackOffice.DecisionEngine.Api\bin\Debug\net8.0\Serilog.Sinks.Debug.dll
C:\Users\<USER>\source\repos\BlueTape\back-office-decision-engine\src\BlueTape.BackOffice.DecisionEngine.Api\bin\Debug\net8.0\Serilog.Sinks.File.dll
C:\Users\<USER>\source\repos\BlueTape\back-office-decision-engine\src\BlueTape.BackOffice.DecisionEngine.Api\bin\Debug\net8.0\Serilog.Sinks.Http.dll
C:\Users\<USER>\source\repos\BlueTape\back-office-decision-engine\src\BlueTape.BackOffice.DecisionEngine.Api\bin\Debug\net8.0\Serilog.Sinks.Logz.Io.dll
C:\Users\<USER>\source\repos\BlueTape\back-office-decision-engine\src\BlueTape.BackOffice.DecisionEngine.Api\bin\Debug\net8.0\Serilog.Sinks.PeriodicBatching.dll
C:\Users\<USER>\source\repos\BlueTape\back-office-decision-engine\src\BlueTape.BackOffice.DecisionEngine.Api\bin\Debug\net8.0\SixLabors.Fonts.dll
C:\Users\<USER>\source\repos\BlueTape\back-office-decision-engine\src\BlueTape.BackOffice.DecisionEngine.Api\bin\Debug\net8.0\Swashbuckle.AspNetCore.ReDoc.dll
C:\Users\<USER>\source\repos\BlueTape\back-office-decision-engine\src\BlueTape.BackOffice.DecisionEngine.Api\bin\Debug\net8.0\Swashbuckle.AspNetCore.Swagger.dll
C:\Users\<USER>\source\repos\BlueTape\back-office-decision-engine\src\BlueTape.BackOffice.DecisionEngine.Api\bin\Debug\net8.0\Swashbuckle.AspNetCore.SwaggerGen.dll
C:\Users\<USER>\source\repos\BlueTape\back-office-decision-engine\src\BlueTape.BackOffice.DecisionEngine.Api\bin\Debug\net8.0\Swashbuckle.AspNetCore.SwaggerUI.dll
C:\Users\<USER>\source\repos\BlueTape\back-office-decision-engine\src\BlueTape.BackOffice.DecisionEngine.Api\bin\Debug\net8.0\System.Configuration.ConfigurationManager.dll
C:\Users\<USER>\source\repos\BlueTape\back-office-decision-engine\src\BlueTape.BackOffice.DecisionEngine.Api\bin\Debug\net8.0\System.Diagnostics.PerformanceCounter.dll
C:\Users\<USER>\source\repos\BlueTape\back-office-decision-engine\src\BlueTape.BackOffice.DecisionEngine.Api\bin\Debug\net8.0\System.Drawing.Common.dll
C:\Users\<USER>\source\repos\BlueTape\back-office-decision-engine\src\BlueTape.BackOffice.DecisionEngine.Api\bin\Debug\net8.0\System.IO.Packaging.dll
C:\Users\<USER>\source\repos\BlueTape\back-office-decision-engine\src\BlueTape.BackOffice.DecisionEngine.Api\bin\Debug\net8.0\System.Memory.Data.dll
C:\Users\<USER>\source\repos\BlueTape\back-office-decision-engine\src\BlueTape.BackOffice.DecisionEngine.Api\bin\Debug\net8.0\System.Security.Cryptography.ProtectedData.dll
C:\Users\<USER>\source\repos\BlueTape\back-office-decision-engine\src\BlueTape.BackOffice.DecisionEngine.Api\bin\Debug\net8.0\System.Security.Permissions.dll
C:\Users\<USER>\source\repos\BlueTape\back-office-decision-engine\src\BlueTape.BackOffice.DecisionEngine.Api\bin\Debug\net8.0\System.Windows.Extensions.dll
C:\Users\<USER>\source\repos\BlueTape\back-office-decision-engine\src\BlueTape.BackOffice.DecisionEngine.Api\bin\Debug\net8.0\XLParser.dll
C:\Users\<USER>\source\repos\BlueTape\back-office-decision-engine\src\BlueTape.BackOffice.DecisionEngine.Api\bin\Debug\net8.0\runtimes\win\lib\net6.0\Microsoft.Win32.SystemEvents.dll
C:\Users\<USER>\source\repos\BlueTape\back-office-decision-engine\src\BlueTape.BackOffice.DecisionEngine.Api\bin\Debug\net8.0\runtimes\win\lib\net6.0\System.Diagnostics.PerformanceCounter.dll
C:\Users\<USER>\source\repos\BlueTape\back-office-decision-engine\src\BlueTape.BackOffice.DecisionEngine.Api\bin\Debug\net8.0\runtimes\unix\lib\net6.0\System.Drawing.Common.dll
C:\Users\<USER>\source\repos\BlueTape\back-office-decision-engine\src\BlueTape.BackOffice.DecisionEngine.Api\bin\Debug\net8.0\runtimes\win\lib\net6.0\System.Drawing.Common.dll
C:\Users\<USER>\source\repos\BlueTape\back-office-decision-engine\src\BlueTape.BackOffice.DecisionEngine.Api\bin\Debug\net8.0\runtimes\win\lib\net6.0\System.Security.Cryptography.ProtectedData.dll
C:\Users\<USER>\source\repos\BlueTape\back-office-decision-engine\src\BlueTape.BackOffice.DecisionEngine.Api\bin\Debug\net8.0\runtimes\win\lib\net6.0\System.Windows.Extensions.dll
C:\Users\<USER>\source\repos\BlueTape\back-office-decision-engine\src\BlueTape.BackOffice.DecisionEngine.Api\bin\Debug\net8.0\BlueTape.BackOffice.DecisionEngine.Application.dll
C:\Users\<USER>\source\repos\BlueTape\back-office-decision-engine\src\BlueTape.BackOffice.DecisionEngine.Api\bin\Debug\net8.0\BlueTape.BackOffice.DecisionEngine.DataAccess.External.dll
C:\Users\<USER>\source\repos\BlueTape\back-office-decision-engine\src\BlueTape.BackOffice.DecisionEngine.Api\bin\Debug\net8.0\BlueTape.BackOffice.DecisionEngine.Domain.dll
C:\Users\<USER>\source\repos\BlueTape\back-office-decision-engine\src\BlueTape.BackOffice.DecisionEngine.Api\bin\Debug\net8.0\BlueTape.BackOffice.DecisionEngine.Application.pdb
C:\Users\<USER>\source\repos\BlueTape\back-office-decision-engine\src\BlueTape.BackOffice.DecisionEngine.Api\bin\Debug\net8.0\BlueTape.BackOffice.DecisionEngine.Domain.pdb
C:\Users\<USER>\source\repos\BlueTape\back-office-decision-engine\src\BlueTape.BackOffice.DecisionEngine.Api\bin\Debug\net8.0\BlueTape.BackOffice.DecisionEngine.DataAccess.External.pdb
C:\Users\<USER>\source\repos\BlueTape\back-office-decision-engine\src\BlueTape.BackOffice.DecisionEngine.Api\obj\Debug\net8.0\BlueTape.BackOffice.DecisionEngine.Api.csproj.AssemblyReference.cache
C:\Users\<USER>\source\repos\BlueTape\back-office-decision-engine\src\BlueTape.BackOffice.DecisionEngine.Api\obj\Debug\net8.0\BlueTape.BackOffice.DecisionEngine.Api.GeneratedMSBuildEditorConfig.editorconfig
C:\Users\<USER>\source\repos\BlueTape\back-office-decision-engine\src\BlueTape.BackOffice.DecisionEngine.Api\obj\Debug\net8.0\BlueTape.BackOffice.DecisionEngine.Api.AssemblyInfoInputs.cache
C:\Users\<USER>\source\repos\BlueTape\back-office-decision-engine\src\BlueTape.BackOffice.DecisionEngine.Api\obj\Debug\net8.0\BlueTape.BackOffice.DecisionEngine.Api.AssemblyInfo.cs
C:\Users\<USER>\source\repos\BlueTape\back-office-decision-engine\src\BlueTape.BackOffice.DecisionEngine.Api\obj\Debug\net8.0\BlueTape.BackOffice.DecisionEngine.Api.csproj.CoreCompileInputs.cache
C:\Users\<USER>\source\repos\BlueTape\back-office-decision-engine\src\BlueTape.BackOffice.DecisionEngine.Api\obj\Debug\net8.0\BlueTape.BackOffice.DecisionEngine.Api.MvcApplicationPartsAssemblyInfo.cs
C:\Users\<USER>\source\repos\BlueTape\back-office-decision-engine\src\BlueTape.BackOffice.DecisionEngine.Api\obj\Debug\net8.0\BlueTape.BackOffice.DecisionEngine.Api.MvcApplicationPartsAssemblyInfo.cache
C:\Users\<USER>\source\repos\BlueTape\back-office-decision-engine\src\BlueTape.BackOffice.DecisionEngine.Api\obj\Debug\net8.0\BlueTape.BackOffice.DecisionEngine.Api.sourcelink.json
C:\Users\<USER>\source\repos\BlueTape\back-office-decision-engine\src\BlueTape.BackOffice.DecisionEngine.Api\obj\Debug\net8.0\scopedcss\bundle\BlueTape.BackOffice.DecisionEngine.Api.styles.css
C:\Users\<USER>\source\repos\BlueTape\back-office-decision-engine\src\BlueTape.BackOffice.DecisionEngine.Api\obj\Debug\net8.0\staticwebassets.build.json
C:\Users\<USER>\source\repos\BlueTape\back-office-decision-engine\src\BlueTape.BackOffice.DecisionEngine.Api\obj\Debug\net8.0\staticwebassets.development.json
C:\Users\<USER>\source\repos\BlueTape\back-office-decision-engine\src\BlueTape.BackOffice.DecisionEngine.Api\obj\Debug\net8.0\staticwebassets.build.endpoints.json
C:\Users\<USER>\source\repos\BlueTape\back-office-decision-engine\src\BlueTape.BackOffice.DecisionEngine.Api\obj\Debug\net8.0\staticwebassets.upToDateCheck.txt
C:\Users\<USER>\source\repos\BlueTape\back-office-decision-engine\src\BlueTape.BackOffice.DecisionEngine.Api\obj\Debug\net8.0\BlueTape.028E5511.Up2Date
C:\Users\<USER>\source\repos\BlueTape\back-office-decision-engine\src\BlueTape.BackOffice.DecisionEngine.Api\obj\Debug\net8.0\BlueTape.BackOffice.DecisionEngine.Api.dll
C:\Users\<USER>\source\repos\BlueTape\back-office-decision-engine\src\BlueTape.BackOffice.DecisionEngine.Api\obj\Debug\net8.0\refint\BlueTape.BackOffice.DecisionEngine.Api.dll
C:\Users\<USER>\source\repos\BlueTape\back-office-decision-engine\src\BlueTape.BackOffice.DecisionEngine.Api\obj\Debug\net8.0\BlueTape.BackOffice.DecisionEngine.Api.pdb
C:\Users\<USER>\source\repos\BlueTape\back-office-decision-engine\src\BlueTape.BackOffice.DecisionEngine.Api\obj\Debug\net8.0\BlueTape.BackOffice.DecisionEngine.Api.genruntimeconfig.cache
C:\Users\<USER>\source\repos\BlueTape\back-office-decision-engine\src\BlueTape.BackOffice.DecisionEngine.Api\obj\Debug\net8.0\ref\BlueTape.BackOffice.DecisionEngine.Api.dll
C:\Users\<USER>\source\repos\BlueTape\back-office-decision-engine\src\BlueTape.BackOffice.DecisionEngine.Api\bin\Debug\net8.0\BlueTape.Notification.Sender.dll
C:\Users\<USER>\source\repos\BlueTape\back-office-decision-engine\src\BlueTape.BackOffice.DecisionEngine.Api\obj\Debug\net8.0\staticwebassets.build.json.cache
C:\Users\<USER>\source\repos\BlueTape\back-office-decision-engine\src\BlueTape.BackOffice.DecisionEngine.Api\obj\Debug\net8.0\rpswa.dswa.cache.json
C:\Users\<USER>\source\repos\BlueTape\back-office-decision-engine\src\BlueTape.BackOffice.DecisionEngine.Api\obj\Debug\net8.0\rjimswa.dswa.cache.json
C:\Users\<USER>\source\repos\BlueTape\back-office-decision-engine\src\BlueTape.BackOffice.DecisionEngine.Api\obj\Debug\net8.0\rjsmrazor.dswa.cache.json
C:\Users\<USER>\source\repos\BlueTape\back-office-decision-engine\src\BlueTape.BackOffice.DecisionEngine.Api\obj\Debug\net8.0\rjsmcshtml.dswa.cache.json
