import {Page, expect} from '@playwright/test';
import {test, BaseTest} from '../../../test-utils';
import {PageManager} from '../../../../objects/pages/page-manager';
import {getInvitationLink, getVerificationLink} from "../../../../api/gmail/findLink";
import {waitForResult} from "../../../../utils/waiters";
import {deleteUser, getUserSub} from "../../../../api/admin";

test.use({storageState: {cookies: [], origins: []}});

test.describe('Sign up tests.', async () => {
    let page: Page;
    let email: string;
    let firstName: string;
    let lastName: string;
    let businessName: string;

    test.beforeEach(async ({browser}) => {
        email = `automation_user+${BaseTest.dateTimePrefix()}@bluetape.com`;
        firstName = BaseTest.dateTimePrefix() + 'firstName';
        lastName = BaseTest.dateTimePrefix() + 'lastName';
        businessName = BaseTest.dateTimePrefix() + 'businessName';
        page = await browser.newPage();
        await page.goto('/');
    });

    test.afterEach(async ({adminIdToken}) => {
        const userSub = await getUserSub(adminIdToken, email);
        await deleteUser(adminIdToken, userSub);
        await page.close();
    });

    test.skip('Success sign up via settings invitation. @smoke', async () => {
        const pageManager = new PageManager(page);
        await pageManager.loginPage.login(`${process.env.USER_EMAIL}`, `${process.env.USER_PASSWORD}`);
        await pageManager.sideMenu.openSalesSubTab(pageManager.sideMenu.sideMenuSubTabs.sales.setting);
        await pageManager.settingsTopMenu.settingsMenu.userManagement.click();
        await pageManager.userManagement.buttons.addUser.click();
        await pageManager.addUserModal.fillUpUserInformation(firstName, lastName, email);
        await pageManager.addUserModal.buttons.send.click();
        await page.goto(await waitForResult({
            func: getInvitationLink,
            args: [email, `${process.env.USER_FIRSTNAME} ${process.env.USER_LASTNAME} has invited you to join BlueTape`]
        }));
        await fillUpSignUpFields({pageManager});
        await pageManager.signUpPage.inputFields.businessName.fill(businessName);
        //await pageManager.signUpPage.inputFields.cellphone.fill(BaseTest.constants.user.cellPhoneNumber);
        await pageManager.signUpPage.buttons.continue.click();
        await page.goto(await waitForResult({
            func: getVerificationLink,
            args: [email, 'BlueTape Verification'],
        }));
        await expect(pageManager.sideMenu.sideMenuTabs.home, 'Home bar on the main page should be visible.').toBeVisible();
    });

    test.skip('Success sign up via customer invitation. @smoke', async () => {
        const pageManager = new PageManager(page);
        await pageManager.loginPage.login(`${process.env.USER_EMAIL}`, `${process.env.USER_PASSWORD}`);
        await openCustomersAndCreateNewOne({pageManager});// cellphone problem
        await pageManager.addCustomerModal.buttons.saveInvite.click();
        await page.goto(await waitForResult({
            func: getInvitationLink,
            args: [email, `${process.env.USER_FIRSTNAME} ${process.env.USER_LASTNAME} has invited you to join BlueTape`]
        }));
        await pageManager.signUpPage.buttons.signUpEmail.click();
        await fillUpSignUpFields({pageManager});
        await pageManager.signUpPage.inputFields.cellPhoneNumber.fill(BaseTest.constants.user.cellPhoneNumber);
        await pageManager.signUpPage.buttons.continue.click();
        await page.goto(await waitForResult({
            func: getVerificationLink,
            args: [email, 'BlueTape Verification'],
        }));
        await expect(pageManager.sideMenu.sideMenuTabs.home, 'Home bar on the main page should be visible.').toBeVisible();
    });

    test.skip('Success sign up via invoice. @smoke', async () => {
        const pageManager = new PageManager(page);
        await pageManager.loginPage.login(`${process.env.USER_EMAIL}`, `${process.env.USER_PASSWORD}`);

        await openCustomersAndCreateNewOne({pageManager});
        await pageManager.addCustomerModal.buttons.save.click();

        await pageManager.customersList.clickOnCustomerRow(businessName);
        await pageManager.customerDetailsModal.buttons.addInvoice.click();
        await pageManager.addInvoiceModal.fillUpAddInvoice("5235235", "43242", '4');
        await page.goto(await waitForResult({
            func: getInvitationLink,
            args: [email, `${process.env.USER_FIRSTNAME} ${process.env.USER_LASTNAME} has sent you a payment request`]
        }));

        await pageManager.invoicesDetails.addNewPaymentMethod.click();
        await fillUpSignUpFields({pageManager});
        await pageManager.signUpPage.inputFields.businessName.fill(businessName);
        await pageManager.signUpPage.buttons.continue.click();
        await page.goto(await waitForResult({
            func: getVerificationLink,
            args: [email, 'BlueTape Verification'],
        }));
        await expect(pageManager.sideMenu.sideMenuTabs.home, 'Home bar on the main page should be visible.').toBeVisible();
    });

    async function openCustomersAndCreateNewOne({pageManager}) {
        await pageManager.sideMenu.openSalesSubTab(pageManager.sideMenu.sideMenuSubTabs.sales.customers);
        await pageManager.customersList.buttons.addCustomer.click();
        await pageManager.addCustomerModal.fillUpCustomerFields(BaseTest.constants.user.cellPhoneNumber, firstName, email);
    }

    async function fillUpSignUpFields({pageManager}) {
        await pageManager.signUpPage.buttons.agreeContinue.click();
        await pageManager.signUpPage.inputFields.password.fill(BaseTest.constants.password);
        await pageManager.signUpPage.buttons.signUp.click();
    }
});
