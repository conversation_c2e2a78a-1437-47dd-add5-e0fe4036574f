import {expect} from '@playwright/test';
import {test, BaseTest} from '../../../test-utils';
import {BaseAPI} from '../../../../api/base-api';
import {createCustomerInvoice} from '../../../../api/user/create-invoice';
import {payWithBluetapeCredit, callDecision} from '../../../../api/user';
import {verifyLoan, approveLoan} from '../../../../api/admin';
import {delay} from '../../../../utils/waiters';

test.use({storageState: {cookies: [], origins: []}});

test.describe('Loans edit payment schedule', async () => {
    let supplierAuth: { session: any; challenge: any; };
    let customerAuth: { session: any; challenge: any; };
    let invoice: { id: string; };
    let loanRequest: { id: string; };
    let note: string;
    const amountFee = '10';
    let totalAmount: string;
    let totalAmountOnSite: string;

    test.beforeAll(async ({userIdToken, customerIdToken, adminIdToken}) => {
        test.slow();
        totalAmount = Math.floor(Math.random() * 1000).toString();
        totalAmountOnSite = `$${totalAmount}.00`;
        supplierAuth = await BaseAPI.getAuth(userIdToken);
        invoice = await createCustomerInvoice(supplierAuth.session, supplierAuth.challenge, process.env.USER_CUSTOMERID, totalAmount);
        customerAuth = await BaseAPI.getAuth(customerIdToken);
        loanRequest = await payWithBluetapeCredit(customerAuth.session, customerAuth.challenge, invoice.id);
        await callDecision(customerAuth.session, customerAuth.challenge, loanRequest.id);
        // await delay(270000);
        await verifyLoan(adminIdToken, loanRequest.id);
        // await delay(2000);
        await approveLoan(adminIdToken, loanRequest.id);
        // await delay(45000);
    });

    test.beforeEach(async () => {
        note = `Note${BaseTest.dateTimePrefix()}`;
    });

    test.afterEach(async ({adminPage}) => {
        await adminPage.close();
    });

    async function reloadPageUntilElementIsVisible({adminPageManager}, element) {
        for (let i = 0; i < 20; i += 1) {
            if (await element.isVisible()) {
                break;
            } else {
                await adminPageManager.page.reload();
                await adminPageManager.backOfficeSideMenu.sideMenuTabs.loans.click();
            }
        }
    }

    test.skip('Change one date of Installments. Date should be changed  @smoke', async ({adminPageManager}) => {
        test.slow();
        const dueDateInMonth = BaseTest.getDueDateInFuture(32);
        await reloadPageUntilElementIsVisible({adminPageManager}, await adminPageManager.backOfficeSideMenu.sideMenuTabs.loans);
        await adminPageManager.backOfficeSideMenu.sideMenuTabs.loans.click();
        await adminPageManager.page.waitForLoadState('networkidle');
        await adminPageManager.loans.elements.companyName(totalAmountOnSite).click();
        const thirdDateInScheduleText = await adminPageManager.loansStatusDetails.elements.dateOfInstallments.nth(2).textContent();
        await adminPageManager.loansStatusDetails.clickThirthDate();

        await expect(adminPageManager.editPaymentScheduleModal.fields.originalDate,
            'Edit payment schedule modal should be visible').toBeVisible();

        await expect(adminPageManager.editPaymentScheduleModal.fields.originalDate,
            'Date in the schedule should be equal original date in the modal window').toContainText(thirdDateInScheduleText);

        await adminPageManager.page.waitForTimeout(200);
        await adminPageManager.editPaymentScheduleModal.inputFields.newDate.clear();
        await adminPageManager.editPaymentScheduleModal.inputFields.newDate.type(dueDateInMonth.replace(/\//gi, ''), {delay: 300});
        await adminPageManager.editPaymentScheduleModal.inputFields.note.click();
        await adminPageManager.editPaymentScheduleModal.inputFields.note.fill(note);
        await adminPageManager.editPaymentScheduleModal.buttons.applyNewDate.click();
        await adminPageManager.page.waitForLoadState('networkidle');

        await expect(adminPageManager.loansStatusDetails.elements.dateOfInstallmentsByText(dueDateInMonth),
            'New date should be visible').toBeVisible();

        await expect(adminPageManager.loansStatusDetails.elements.dateOfInstallmentsByText(thirdDateInScheduleText),
            'Original date should not be visible').toBeVisible({visible: false});
    });

    test.skip('Change one date of Installments. Note should be visible in the notes schedule @smoke', async ({adminPageManager}) => {
        const dueDateInTwoMonth = BaseTest.getDueDateInFuture(60);
        await reloadPageUntilElementIsVisible({adminPageManager}, await adminPageManager.backOfficeSideMenu.sideMenuTabs.loans);
        await adminPageManager.backOfficeSideMenu.sideMenuTabs.loans.click();
        await adminPageManager.page.waitForLoadState('networkidle');
        await adminPageManager.loans.elements.companyName(totalAmountOnSite).click();
        await adminPageManager.loansStatusDetails.clickThirthDate();
        await adminPageManager.page.waitForTimeout(200);
        await adminPageManager.editPaymentScheduleModal.inputFields.newDate.clear();
        await adminPageManager.editPaymentScheduleModal.inputFields.newDate.type(dueDateInTwoMonth.replace(/\//gi, ''), {delay: 200});
        await adminPageManager.editPaymentScheduleModal.inputFields.note.type(note);
        await adminPageManager.editPaymentScheduleModal.buttons.applyNewDate.click();
        await adminPageManager.page.waitForLoadState('networkidle');

        await expect(adminPageManager.loansStatusDetails.elements.noteByText(note),
            'Note should be visible in the notes schedule').toBeVisible();

        await expect(adminPageManager.loansStatusDetails.elements.noteByText(note),
            'Note should consist note message').toContainText(note);
    });

    test('Change one date of Installments with Extension fee.  @smoke', async ({adminPageManager}) => {
        const dueDateInThreeMonth = BaseTest.getDueDateInFuture(90);
        const currentDate = BaseTest.getDueDateInFuture(0);
        await reloadPageUntilElementIsVisible({adminPageManager}, await adminPageManager.backOfficeSideMenu.sideMenuTabs.loans);
        await adminPageManager.backOfficeSideMenu.sideMenuTabs.loans.click();
        await adminPageManager.page.waitForLoadState('networkidle');
        await adminPageManager.loans.elements.companyName(totalAmountOnSite).click();
        const thirdDateInScheduleText = await adminPageManager.loansStatusDetails.elements.dateOfInstallments.nth(2).textContent();
        await adminPageManager.loansStatusDetails.clickThirthDate();
        await adminPageManager.page.waitForTimeout(200);
        await adminPageManager.editPaymentScheduleModal.inputFields.newDate.clear();
        await adminPageManager.editPaymentScheduleModal.inputFields.newDate.type(dueDateInThreeMonth, {delay: 200});
        await adminPageManager.editPaymentScheduleModal.inputFields.note.type(note);
        await adminPageManager.editPaymentScheduleModal.checkBoxes.addExtensionFee.click();
        await adminPageManager.editPaymentScheduleModal.inputFields.extensionFeeDate.type(currentDate, {delay: 200});
        await adminPageManager.editPaymentScheduleModal.inputFields.amountOfFee.type(amountFee, {delay: 100});
        await adminPageManager.editPaymentScheduleModal.buttons.applyNewDate.click();
        await adminPageManager.page.waitForLoadState('networkidle');

        await expect(adminPageManager.loansStatusDetails.elements.dateOfInstallmentsByText(dueDateInThreeMonth),
            'New date should be visible').toBeVisible();

        await expect(adminPageManager.loansStatusDetails.elements.dateOfInstallmentsByText(thirdDateInScheduleText),
            'Original date should not be visible').toBeVisible({visible: false});

        await expect(adminPageManager.loansStatusDetails.elements.dateOfInstallmentsByText(currentDate),
            'Date of exstention fee should be visible').toBeVisible();

        await expect(adminPageManager.loansStatusDetails.elements.extensionFeeByDate(currentDate),
            'Extension fee should be added in to installments schedule').toBeVisible();

        await expect(adminPageManager.loansStatusDetails.elements.extensionFeeByDate(currentDate),
            `Extension fee should contain ${amountFee} fee`).toContainText(amountFee);

        await expect(adminPageManager.loansStatusDetails.elements.noteByText(note),
            `Reschedule fee and extension fee should be visible in the notes`).toHaveCount(2);
    });

    test.skip('Add fee with type of Late Fee.  @smoke', async ({adminPageManager}) => {
        const dueDateInTwentyDays = BaseTest.getDueDateInFuture(20);
        await reloadPageUntilElementIsVisible({adminPageManager}, adminPageManager.backOfficeSideMenu.sideMenuTabs.loans);
        await adminPageManager.backOfficeSideMenu.sideMenuTabs.loans.click();
        await adminPageManager.page.waitForLoadState('networkidle');
        await adminPageManager.loans.elements.companyName(totalAmountOnSite).click();
        await adminPageManager.page.waitForLoadState('networkidle');
        await adminPageManager.loansStatusDetails.buttons.addFee.click();
        await adminPageManager.addFeeModal.addLateFee('LateFee', amountFee, dueDateInTwentyDays, note);

        await expect(adminPageManager.loansStatusDetails.elements.manualLateFee,
            'Extension fee should be added in to installments schedule').toBeVisible();

        await expect(adminPageManager.loansStatusDetails.elements.manualLateFee,
            `Extension fee should contain ${amountFee} fee`).toContainText(amountFee);

        await expect(adminPageManager.loansStatusDetails.elements.noteByText(note),
            `Late fee should be visible in the notes`).toBeVisible();
    });

    test.skip('Add fee with type of Extension Fee.  @smoke', async ({adminPageManager}) => {
        const dueDateInTwentyDays = BaseTest.getDueDateInFuture(20);
        await reloadPageUntilElementIsVisible({adminPageManager}, await adminPageManager.backOfficeSideMenu.sideMenuTabs.loans);
        await adminPageManager.backOfficeSideMenu.sideMenuTabs.loans.click();
        await adminPageManager.page.waitForLoadState('networkidle');
        await adminPageManager.loans.elements.companyName(totalAmountOnSite).click();
        await adminPageManager.page.waitForLoadState('networkidle');
        await adminPageManager.loansStatusDetails.buttons.addFee.click();
        await adminPageManager.addFeeModal.addLateFee('ExtensionFee', amountFee, dueDateInTwentyDays, note);

        await expect(adminPageManager.loansStatusDetails.elements.extensionFeeByDate(dueDateInTwentyDays),
            'Extension fee should be added in to installments schedule').toBeVisible();

        await expect(adminPageManager.loansStatusDetails.elements.extensionFeeByDate(dueDateInTwentyDays),
            `Extension fee should contain ${amountFee} fee`).toContainText(amountFee);

        await expect(adminPageManager.loansStatusDetails.elements.noteByText(note),
            `Extension fee should be visible in the notes`).toBeVisible();
    });
});
