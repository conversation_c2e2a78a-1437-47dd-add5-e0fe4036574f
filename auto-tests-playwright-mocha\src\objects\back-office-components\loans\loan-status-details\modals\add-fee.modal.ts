import {BasePage} from '../../../../base.page';

export class AddFeeModal extends BasePage {
    constructor(page){
        super(page);
    };

    containers = {
        modal: this.page.locator('.modal-content'),   
    };

    buttons = {
        create: this.containers.modal.locator('button:has-text("Create")'),
        cancel: this.containers.modal.locator('button:has-text("Cancel")'),
    };
    
    checkBoxes = {
        lateFee: this.containers.modal.locator('#add_fee_modal_late_fee'),
        extentionFee: this.containers.modal.locator('#add_fee_modal_extension_fee'),
    };

    inputFields = {
        expectedAmount: this.containers.modal.locator('[value="$"]'),
        expectedDate: this.containers.modal.locator('[class="col"] [type="date"]'),
        reasonToCreate: this.containers.modal.locator('[placeholder="Note *"]'),
    };

    async addLateFee (type:string, amount: string, date:string, note:string) {  
        await this.page.waitForTimeout(500);
        await this.inputFields.expectedAmount.clear();
        await this.inputFields.expectedAmount.type(amount, {delay: 100});
        await this.inputFields.expectedDate.clear();
        await this.inputFields.expectedDate.type(date, {delay: 100});
        type === 'LateFee' ? await this.checkBoxes.lateFee.click() : await this.checkBoxes.extentionFee.click();
        await this.inputFields.reasonToCreate.type(note);
        await this.buttons.create.click();
    };
}