﻿
Microsoft Visual Studio Solution File, Format Version 12.00
# Visual Studio Version 17
VisualStudioVersion = 17.0.31903.59
MinimumVisualStudioVersion = 10.0.40219.1
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "library", "library", "{3B6432A9-F3BD-4582-A105-F662032C9455}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "BlueTape.Aion.API", "BlueTape.Aion.API\BlueTape.Aion.API.csproj", "{F0AFA73D-76F3-4C43-AF99-027BC85488E6}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "BlueTape.Aion.Application", "BlueTape.Aion.Application\BlueTape.Aion.Application.csproj", "{9966E9B8-4583-4F6D-9C19-F053E58B719F}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "BlueTape.Aion.DataAccess.External", "BlueTape.Aion.DataAccess.External\BlueTape.Aion.DataAccess.External.csproj", "{30DB309D-FC02-4E8C-8882-05FC39BE874F}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "BlueTape.Aion.DataAccess.MongoDB", "BlueTape.Aion.DataAccess.MongoDB\BlueTape.Aion.DataAccess.MongoDB.csproj", "{517D535F-7C3A-49DD-A01A-E607CEB7EC77}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "BlueTape.Aion.Domain", "BlueTape.Aion.Domain\BlueTape.Aion.Domain.csproj", "{A25CE271-9BB9-4104-9B19-CD4285804EA7}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "BueTape.Aion.Infrastructure", "BueTape.Aion.Infrastructure\BueTape.Aion.Infrastructure.csproj", "{FAB1E883-CCE8-4EE1-B518-E54CE28C79D2}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "lambdas", "lambdas", "{7EA6AA2C-7382-4F6C-A42C-EE960AC194AB}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "BlueTape.Reports.Lambda", "BlueTape.Reports.Lambda\BlueTape.Reports.Lambda.csproj", "{C7128E8B-CAA7-467E-BAE6-2CAA10E9B452}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "tests", "tests", "{994B5C5A-F781-4D6D-88FE-29E75E258A51}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "BlueTape.Application.Tests", "BlueTape.Application.Tests\BlueTape.Application.Tests.csproj", "{97FE58C0-1FBC-4217-A60A-E2CC1AC427E1}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "nuget", "nuget", "{BB623CC6-A4D8-45ED-92CF-76C9DE2F23B5}"
	ProjectSection(SolutionItems) = preProject
		nuget.config = nuget.config
	EndProjectSection
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "functions", "functions", "{D3B2DD44-8841-4459-B60A-A7473DF1A452}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "BlueTape.Functions.Aion.TransactionStatusReport", "BlueTape.Functions.Aion.TransactionStatusReport\BlueTape.Functions.Aion.TransactionStatusReport.csproj", "{7727D902-4A57-4FA2-8B15-AF65172F214D}"
EndProject
Global
	GlobalSection(SolutionConfigurationPlatforms) = preSolution
		Debug|Any CPU = Debug|Any CPU
		Release|Any CPU = Release|Any CPU
	EndGlobalSection
	GlobalSection(ProjectConfigurationPlatforms) = postSolution
		{F0AFA73D-76F3-4C43-AF99-027BC85488E6}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{F0AFA73D-76F3-4C43-AF99-027BC85488E6}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{F0AFA73D-76F3-4C43-AF99-027BC85488E6}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{F0AFA73D-76F3-4C43-AF99-027BC85488E6}.Release|Any CPU.Build.0 = Release|Any CPU
		{9966E9B8-4583-4F6D-9C19-F053E58B719F}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{9966E9B8-4583-4F6D-9C19-F053E58B719F}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{9966E9B8-4583-4F6D-9C19-F053E58B719F}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{9966E9B8-4583-4F6D-9C19-F053E58B719F}.Release|Any CPU.Build.0 = Release|Any CPU
		{30DB309D-FC02-4E8C-8882-05FC39BE874F}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{30DB309D-FC02-4E8C-8882-05FC39BE874F}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{30DB309D-FC02-4E8C-8882-05FC39BE874F}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{30DB309D-FC02-4E8C-8882-05FC39BE874F}.Release|Any CPU.Build.0 = Release|Any CPU
		{517D535F-7C3A-49DD-A01A-E607CEB7EC77}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{517D535F-7C3A-49DD-A01A-E607CEB7EC77}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{517D535F-7C3A-49DD-A01A-E607CEB7EC77}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{517D535F-7C3A-49DD-A01A-E607CEB7EC77}.Release|Any CPU.Build.0 = Release|Any CPU
		{A25CE271-9BB9-4104-9B19-CD4285804EA7}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{A25CE271-9BB9-4104-9B19-CD4285804EA7}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{A25CE271-9BB9-4104-9B19-CD4285804EA7}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{A25CE271-9BB9-4104-9B19-CD4285804EA7}.Release|Any CPU.Build.0 = Release|Any CPU
		{FAB1E883-CCE8-4EE1-B518-E54CE28C79D2}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{FAB1E883-CCE8-4EE1-B518-E54CE28C79D2}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{FAB1E883-CCE8-4EE1-B518-E54CE28C79D2}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{FAB1E883-CCE8-4EE1-B518-E54CE28C79D2}.Release|Any CPU.Build.0 = Release|Any CPU
		{C7128E8B-CAA7-467E-BAE6-2CAA10E9B452}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{C7128E8B-CAA7-467E-BAE6-2CAA10E9B452}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{C7128E8B-CAA7-467E-BAE6-2CAA10E9B452}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{C7128E8B-CAA7-467E-BAE6-2CAA10E9B452}.Release|Any CPU.Build.0 = Release|Any CPU
		{97FE58C0-1FBC-4217-A60A-E2CC1AC427E1}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{97FE58C0-1FBC-4217-A60A-E2CC1AC427E1}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{97FE58C0-1FBC-4217-A60A-E2CC1AC427E1}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{97FE58C0-1FBC-4217-A60A-E2CC1AC427E1}.Release|Any CPU.Build.0 = Release|Any CPU
		{7727D902-4A57-4FA2-8B15-AF65172F214D}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{7727D902-4A57-4FA2-8B15-AF65172F214D}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{7727D902-4A57-4FA2-8B15-AF65172F214D}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{7727D902-4A57-4FA2-8B15-AF65172F214D}.Release|Any CPU.Build.0 = Release|Any CPU
	EndGlobalSection
	GlobalSection(SolutionProperties) = preSolution
		HideSolutionNode = FALSE
	EndGlobalSection
	GlobalSection(NestedProjects) = preSolution
		{9966E9B8-4583-4F6D-9C19-F053E58B719F} = {3B6432A9-F3BD-4582-A105-F662032C9455}
		{30DB309D-FC02-4E8C-8882-05FC39BE874F} = {3B6432A9-F3BD-4582-A105-F662032C9455}
		{517D535F-7C3A-49DD-A01A-E607CEB7EC77} = {3B6432A9-F3BD-4582-A105-F662032C9455}
		{A25CE271-9BB9-4104-9B19-CD4285804EA7} = {3B6432A9-F3BD-4582-A105-F662032C9455}
		{FAB1E883-CCE8-4EE1-B518-E54CE28C79D2} = {3B6432A9-F3BD-4582-A105-F662032C9455}
		{C7128E8B-CAA7-467E-BAE6-2CAA10E9B452} = {7EA6AA2C-7382-4F6C-A42C-EE960AC194AB}
		{97FE58C0-1FBC-4217-A60A-E2CC1AC427E1} = {994B5C5A-F781-4D6D-88FE-29E75E258A51}
		{7727D902-4A57-4FA2-8B15-AF65172F214D} = {D3B2DD44-8841-4459-B60A-A7473DF1A452}
	EndGlobalSection
	GlobalSection(ExtensibilityGlobals) = postSolution
		SolutionGuid = {C66936A8-CDF1-4732-A769-A1243073EA25}
	EndGlobalSection
EndGlobal
