﻿using BlueTape.Aion.Application.Models.Ach.Pull;
using BlueTape.Aion.Application.Models.Ach.Pull.Response;
using BlueTape.Aion.Application.Models.Instant;
using BlueTape.Aion.DataAccess.External.Models.CreateAchTransfer.Response;
using BlueTape.Aion.DataAccess.External.Models.InstantTransfer.Response;
using BlueTape.Aion.DataAccess.External.Models.InternalTransfer;
using BlueTape.Integrations.Aion;
using BlueTape.Integrations.Aion.Accounts;
using BlueTape.Integrations.Aion.Ach.CreateWireTransfer.Response;
using BlueTape.Integrations.Aion.Infrastructure.Enums;
using BlueTape.Integrations.Aion.Transactions;
using BlueTape.Utilities.Models;

namespace BlueTape.Aion.Application.Abstractions;

public interface IAchService
{
    Task<decimal> GetAvailableBalance(AccountCodeType accountCodeType, string paymentSubscriptionType, CancellationToken ctx);
    
    Task<BlueTapeTransactionResponseModel> StartAionTransactionAsync(
        CreateAch createAch,
        TransactionType transactionType,
        string paymentSubscriptionType,
        AionPaymentMethodType aionPaymentMethodType,
        CancellationToken ctx);
    
    Task<CreateAchResponseModel> CreateAchAsync(
        CreateAch createAch, TransactionType transactionType, string paymentSubscriptionType, CancellationToken ctx);
    Task<CreateWireTransferResponseModel> CreateWireAsync(
        CreateAch createAch,
        string paymentSubscriptionType,
        CancellationToken ctx);
    Task<CreateInstantTransferResponseModel> CreateInstantAsync(
        CreateAch createAch,
        string paymentSubscriptionType,
        CancellationToken ctx);
    
    Task<List<AchResponseObj>> GetAllAchTransactionsAsync(int forLastDaysNumber, string paymentSubscriptionType, CancellationToken ctx);
    Task<List<AchResponseObj>> GetAllAchReturnTransactionsAsync(int forLastDaysNumber, string paymentSubscriptionType, CancellationToken ctx);
    Task<List<BookTransferObj>> GetAllInternalTransactionsAsync(int forLastDaysNumber, string paymentSubscriptionType, CancellationToken ctx);
    Task<List<DataAccess.External.Models.Transactions.TransactionListObj>> GetAllTransactionsAsync(string accountId, DateTime? fromDate, DateTime? toDate, string paymentSubscriptionType, CancellationToken ctx);
    Task<PaginatedResponse<DataAccess.External.Models.Transactions.TransactionListObj>> GetAllTransactionsAsync(TransactionsQuery query, CancellationToken ctx);

    Task<(List<AchResponseObj>, int?)> GetAchTransactionsByPageAsync(int page, int forLastDaysNumber, string paymentSubscriptionType, CancellationToken ctx);
    Task<(List<AchResponseObj>, int?)> GetAchReturnTransactionsByPageAsync(int page, int forLastDaysNumber, string paymentSubscriptionType, CancellationToken ctx);
    Task<(List<BookTransferObj>, int?)> GetAchInternalTransactionsByPageAsync(int page, int forLastDaysNumber, string paymentSubscriptionType, CancellationToken ctx);
    Task<(List<InstantTransferObjectItemResponse>, int?)> GetInstantTransactionsByPageAsync(int instantPage, int forLastDaysNumber, string paymentSubscriptionType, CancellationToken ctx);
    Task<(List<WireTransferObjItem>, int?)> GetWireTransactionsByPageAsync(int wirePage, int forLastDaysNumber, string paymentSubscriptionType, CancellationToken ctx);
    Task<List<AccountResponseObj>> GetAllAccounts(CancellationToken ctx);
}