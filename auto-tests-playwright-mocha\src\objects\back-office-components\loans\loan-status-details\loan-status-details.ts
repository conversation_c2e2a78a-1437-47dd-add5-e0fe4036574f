import {BasePage} from '../../../base.page';

export class LoansStatusDetails extends BasePage {
    constructor(page){
        super(page);
    };

    containers = {
        loanInfoContainer: this.page.locator('.container-fluid'),
        notesContainer: this.page.locator('_react=[fields.0="update"] >> [class*="table"]'),
        installmentsContainer: this.page.locator('_react=Zr[fields.9="type"]'),
    };

    buttons = {
        newPaymentPlan: this.page.locator('button:has-text("New Payment Plan")'),
        addFee: this.page.locator('button:has-text("Add Fee")'),
    };

    elements = {
        dateOfInstallments: this.containers.installmentsContainer.locator('_react=ie[key="0"]'),
        dateOfInstallmentsByText: (text:string) => this.containers.installmentsContainer.locator(`_react=ie[key="0"] [value="${text}"]`),
        noteByText: (text:string) => this.containers.notesContainer.locator(`tr:has-text("${text}")`),
        extensionFeeByDate: (date = '') => this.containers.installmentsContainer.locator(`tr:has-text("Extension Fee"):has-text("${date}")`),
        manualLateFee: this.containers.installmentsContainer.locator('tr:has-text("Manual Late Fee")'),
        manualExtensionFee: this.containers.installmentsContainer.locator('tr:has-text("Manual Extension Fee")'),
        penaltyInterest: this.containers.installmentsContainer.locator('tr:has-text("Penalty Interest")'),
        loanOutstandingAmount: this.containers.loanInfoContainer.locator('div[class*="form-group"]:has-text("Loan Outstanding Amount") input'),
        nextPaymentAmount: this.containers.loanInfoContainer.locator('div[class*="form-group"]:has-text("Next Payment Amount") input')
    };

    async clickThirthDate() {
        await this.elements.dateOfInstallments.nth(2).click();
    };

    async getTextOfElement(element) {
        await element.textContent();
    };

    async getAmountOfLateInstallmentsAndPenalty(loanReceivables) {
        let nextPaymentAmount = 0;
        for(const payment of loanReceivables) {
            nextPaymentAmount = nextPaymentAmount + payment.expectedAmount;
            if(payment.type === 'PenaltyInterestFee') {
                break;
            }
        };
        return nextPaymentAmount;
    }
}