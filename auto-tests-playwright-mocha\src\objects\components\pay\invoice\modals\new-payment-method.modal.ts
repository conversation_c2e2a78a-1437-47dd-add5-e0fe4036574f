import {Page} from '@playwright/test';
import {BasePage} from '../../../../base.page';

export class NewPaymentMethodModal extends BasePage {
    constructor(page: Page) {
        super(page);
    };

    buttons = {
        connectYourBank: this.page.locator('[data-testid="connectBank"]'),
        linkYourCard: this.page.locator('[data-testid="linkCard"]'),
        submitConnectYourBank: this.page.locator('"Connect your bank" >> nth=2'), //fix
        linkBankManually: this.page.locator('"Link your bank manually" >> nth=1'), // fix

        //dev
        linkBankManuallyDev: this.page.getByText('Link your bank manually').nth(1),
        linkYourCardDev: this.page.getByText('Link your card'),
    };

    async clickOnLinkBankAccountButton() {
        await this.buttons.linkBankManuallyDev.click();
    }

    async clickOnLinkYourCard() {
        await this.buttons.linkYourCardDev.click();
    }
}