{"version": 3, "targets": {"net6.0": {"Amazon.Lambda.Core/2.2.0": {"type": "package", "compile": {"lib/net6.0/Amazon.Lambda.Core.dll": {"related": ".xml"}}, "runtime": {"lib/net6.0/Amazon.Lambda.Core.dll": {"related": ".xml"}}}, "Amazon.Lambda.Serialization.SystemTextJson/2.4.0": {"type": "package", "dependencies": {"Amazon.Lambda.Core": "2.2.0"}, "compile": {"lib/net6.0/Amazon.Lambda.Serialization.SystemTextJson.dll": {"related": ".xml"}}, "runtime": {"lib/net6.0/Amazon.Lambda.Serialization.SystemTextJson.dll": {"related": ".xml"}}}, "Amazon.Lambda.SQSEvents/2.1.0": {"type": "package", "compile": {"lib/netcoreapp3.1/Amazon.Lambda.SQSEvents.dll": {"related": ".xml"}}, "runtime": {"lib/netcoreapp3.1/Amazon.Lambda.SQSEvents.dll": {"related": ".xml"}}}, "Autofac/6.4.0": {"type": "package", "dependencies": {"System.Diagnostics.DiagnosticSource": "4.7.1"}, "compile": {"lib/net6.0/Autofac.dll": {"related": ".xml"}}, "runtime": {"lib/net6.0/Autofac.dll": {"related": ".xml"}}}, "Autofac.Configuration/6.0.0": {"type": "package", "dependencies": {"Autofac": "6.0.0", "Microsoft.Extensions.Configuration": "2.2.0", "Microsoft.Extensions.Configuration.Binder": "2.2.0"}, "compile": {"lib/netstandard2.1/Autofac.Configuration.dll": {"related": ".pdb;.xml"}}, "runtime": {"lib/netstandard2.1/Autofac.Configuration.dll": {"related": ".pdb;.xml"}}}, "Autofac.Extensions.DependencyInjection/8.0.0": {"type": "package", "dependencies": {"Autofac": "6.4.0", "Microsoft.Extensions.DependencyInjection.Abstractions": "6.0.0"}, "compile": {"lib/net6.0/Autofac.Extensions.DependencyInjection.dll": {"related": ".xml"}}, "runtime": {"lib/net6.0/Autofac.Extensions.DependencyInjection.dll": {"related": ".xml"}}}, "AutoMapper/12.0.1": {"type": "package", "dependencies": {"Microsoft.CSharp": "4.7.0"}, "compile": {"lib/netstandard2.1/AutoMapper.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.1/AutoMapper.dll": {"related": ".xml"}}}, "AutoMapper.Extensions.Microsoft.DependencyInjection/12.0.1": {"type": "package", "dependencies": {"AutoMapper": "[12.0.1]", "Microsoft.Extensions.Options": "6.0.0"}, "compile": {"lib/netstandard2.1/AutoMapper.Extensions.Microsoft.DependencyInjection.dll": {}}, "runtime": {"lib/netstandard2.1/AutoMapper.Extensions.Microsoft.DependencyInjection.dll": {}}}, "AWSSDK.Core/3.7.302.15": {"type": "package", "compile": {"lib/netcoreapp3.1/AWSSDK.Core.dll": {"related": ".pdb;.xml"}}, "runtime": {"lib/netcoreapp3.1/AWSSDK.Core.dll": {"related": ".pdb;.xml"}}}, "AWSSDK.Extensions.NETCore.Setup/3.7.300": {"type": "package", "dependencies": {"AWSSDK.Core": "3.7.300", "Microsoft.Extensions.Configuration.Abstractions": "2.0.0", "Microsoft.Extensions.DependencyInjection.Abstractions": "2.0.0", "Microsoft.Extensions.Logging.Abstractions": "2.0.0"}, "compile": {"lib/netcoreapp3.1/AWSSDK.Extensions.NETCore.Setup.dll": {"related": ".pdb"}}, "runtime": {"lib/netcoreapp3.1/AWSSDK.Extensions.NETCore.Setup.dll": {"related": ".pdb"}}}, "AWSSDK.KeyManagementService/3.7.300.54": {"type": "package", "dependencies": {"AWSSDK.Core": "[3.7.302.15, 4.0.0)"}, "compile": {"lib/netcoreapp3.1/AWSSDK.KeyManagementService.dll": {"related": ".pdb;.xml"}}, "runtime": {"lib/netcoreapp3.1/AWSSDK.KeyManagementService.dll": {"related": ".pdb;.xml"}}}, "AWSSDK.S3/3.7.10": {"type": "package", "dependencies": {"AWSSDK.Core": "[3.7.13.22, 4.0.0)"}, "compile": {"lib/netcoreapp3.1/AWSSDK.S3.dll": {"related": ".pdb;.xml"}}, "runtime": {"lib/netcoreapp3.1/AWSSDK.S3.dll": {"related": ".pdb;.xml"}}}, "AWSSDK.SecretsManager/3.7.302.29": {"type": "package", "dependencies": {"AWSSDK.Core": "[3.7.302.15, 4.0.0)"}, "compile": {"lib/netcoreapp3.1/AWSSDK.SecretsManager.dll": {"related": ".pdb;.xml"}}, "runtime": {"lib/netcoreapp3.1/AWSSDK.SecretsManager.dll": {"related": ".pdb;.xml"}}}, "AWSSDK.SecretsManager.Caching/1.0.6": {"type": "package", "dependencies": {"AWSSDK.SecretsManager": "3.7.102.54", "Microsoft.Extensions.Caching.Memory": "7.0.0"}, "compile": {"lib/netstandard2.0/Amazon.SecretsManager.Extensions.Caching.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.0/Amazon.SecretsManager.Extensions.Caching.dll": {"related": ".xml"}}}, "AWSSDK.SecurityToken/3.7.300.47": {"type": "package", "dependencies": {"AWSSDK.Core": "[3.7.302.6, 4.0.0)"}, "compile": {"lib/netcoreapp3.1/AWSSDK.SecurityToken.dll": {"related": ".pdb;.xml"}}, "runtime": {"lib/netcoreapp3.1/AWSSDK.SecurityToken.dll": {"related": ".pdb;.xml"}}}, "AWSSDK.SimpleNotificationService/3.7.200.52": {"type": "package", "dependencies": {"AWSSDK.Core": "[3.7.202.21, 4.0.0)"}, "compile": {"lib/netcoreapp3.1/AWSSDK.SimpleNotificationService.dll": {"related": ".pdb;.xml"}}, "runtime": {"lib/netcoreapp3.1/AWSSDK.SimpleNotificationService.dll": {"related": ".pdb;.xml"}}}, "AWSSDK.SQS/3.7.2.121": {"type": "package", "dependencies": {"AWSSDK.Core": "[3.7.13.22, 4.0.0)"}, "compile": {"lib/netcoreapp3.1/AWSSDK.SQS.dll": {"related": ".pdb;.xml"}}, "runtime": {"lib/netcoreapp3.1/AWSSDK.SQS.dll": {"related": ".pdb;.xml"}}}, "Azure.Core/1.36.0": {"type": "package", "dependencies": {"Microsoft.Bcl.AsyncInterfaces": "1.1.1", "System.Diagnostics.DiagnosticSource": "6.0.1", "System.Memory.Data": "1.0.2", "System.Numerics.Vectors": "4.5.0", "System.Text.Encodings.Web": "4.7.2", "System.Text.Json": "4.7.2", "System.Threading.Tasks.Extensions": "4.5.4"}, "compile": {"lib/net6.0/Azure.Core.dll": {"related": ".xml"}}, "runtime": {"lib/net6.0/Azure.Core.dll": {"related": ".xml"}}}, "Azure.Core.Amqp/1.3.0": {"type": "package", "dependencies": {"Microsoft.Azure.Amqp": "2.6.1", "System.Memory": "4.5.4", "System.Memory.Data": "1.0.2"}, "compile": {"lib/netstandard2.0/Azure.Core.Amqp.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.0/Azure.Core.Amqp.dll": {"related": ".xml"}}}, "Azure.Identity/1.10.4": {"type": "package", "dependencies": {"Azure.Core": "1.36.0", "Microsoft.Identity.Client": "4.56.0", "Microsoft.Identity.Client.Extensions.Msal": "4.56.0", "System.Memory": "4.5.4", "System.Security.Cryptography.ProtectedData": "4.7.0", "System.Text.Json": "4.7.2", "System.Threading.Tasks.Extensions": "4.5.4"}, "compile": {"lib/netstandard2.0/Azure.Identity.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.0/Azure.Identity.dll": {"related": ".xml"}}}, "Azure.Messaging.ServiceBus/7.17.1": {"type": "package", "dependencies": {"Azure.Core": "1.36.0", "Azure.Core.Amqp": "1.3.0", "Microsoft.Azure.Amqp": "2.6.4", "Microsoft.Bcl.AsyncInterfaces": "1.1.1", "System.Memory.Data": "1.0.2"}, "compile": {"lib/netstandard2.0/Azure.Messaging.ServiceBus.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.0/Azure.Messaging.ServiceBus.dll": {"related": ".xml"}}}, "Azure.Security.KeyVault.Keys/4.5.0": {"type": "package", "dependencies": {"Azure.Core": "1.30.0", "System.Memory": "4.5.4", "System.Text.Json": "4.7.2", "System.Threading.Tasks.Extensions": "4.5.4"}, "compile": {"lib/netstandard2.0/Azure.Security.KeyVault.Keys.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.0/Azure.Security.KeyVault.Keys.dll": {"related": ".xml"}}}, "Azure.Security.KeyVault.Secrets/4.5.0": {"type": "package", "dependencies": {"Azure.Core": "1.30.0", "System.Memory": "4.5.4", "System.Text.Json": "4.7.2", "System.Threading.Tasks.Extensions": "4.5.4"}, "compile": {"lib/netstandard2.0/Azure.Security.KeyVault.Secrets.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.0/Azure.Security.KeyVault.Secrets.dll": {"related": ".xml"}}}, "BlueTape.AWSMessaging/2.0.5": {"type": "package", "dependencies": {"AWSSDK.Extensions.NETCore.Setup": "3.7.2", "AWSSDK.SQS": "3.7.2.121", "AWSSDK.SimpleNotificationService": "3.7.4.24", "Amazon.Lambda.SQSEvents": "2.1.0", "BlueTape.Common.Extensions": "1.1.0", "Microsoft.Extensions.DependencyInjection.Abstractions": "8.0.0", "Microsoft.Extensions.Logging.Abstractions": "8.0.0", "Microsoft.Extensions.Options": "8.0.1"}, "compile": {"lib/net6.0/BlueTape.AWSMessaging.dll": {}}, "runtime": {"lib/net6.0/BlueTape.AWSMessaging.dll": {}}}, "BlueTape.AWSS3/1.1.6": {"type": "package", "dependencies": {"AWSSDK.Extensions.NETCore.Setup": "3.7.2", "AWSSDK.S3": "3.7.10", "Microsoft.Extensions.Logging.Abstractions": "8.0.0"}, "compile": {"lib/net6.0/BlueTape.AWSS3.dll": {}}, "runtime": {"lib/net6.0/BlueTape.AWSS3.dll": {}}}, "BlueTape.AzureKeyVault/1.0.3": {"type": "package", "dependencies": {"Azure.Identity": "1.10.4", "Azure.Security.KeyVault.Keys": "4.5.0", "Azure.Security.KeyVault.Secrets": "4.5.0", "BlueTape.Utilities": "1.2.7", "Microsoft.Extensions.Logging.Abstractions": "8.0.0"}, "compile": {"lib/net6.0/BlueTape.AzureKeyVault.dll": {}}, "runtime": {"lib/net6.0/BlueTape.AzureKeyVault.dll": {}}}, "BlueTape.CashFlow.Domain/1.0.2": {"type": "package", "compile": {"lib/net6.0/BlueTape.CashFlow.Domain.dll": {}}, "runtime": {"lib/net6.0/BlueTape.CashFlow.Domain.dll": {}}}, "BlueTape.Common.Extensions/1.1.0": {"type": "package", "compile": {"lib/net6.0/BlueTape.Common.Extensions.dll": {}}, "runtime": {"lib/net6.0/BlueTape.Common.Extensions.dll": {}}}, "BlueTape.Common.Validation/1.0.4": {"type": "package", "dependencies": {"BlueTape.AWSS3": "1.1.3", "Microsoft.Extensions.DependencyInjection.Abstractions": "8.0.0", "Microsoft.Extensions.Logging.Abstractions": "8.0.0", "Microsoft.Extensions.Options": "8.0.1", "libphonenumber-csharp": "8.12.45"}, "compile": {"lib/net6.0/BlueTape.Common.Validation.dll": {}}, "runtime": {"lib/net6.0/BlueTape.Common.Validation.dll": {}}}, "BlueTape.CompanyService/1.2.42": {"type": "package", "dependencies": {"BlueTape.CompanyService.Common": "1.1.21", "Newtonsoft.Json": "13.0.1"}, "compile": {"lib/net6.0/BlueTape.CompanyService.dll": {}}, "runtime": {"lib/net6.0/BlueTape.CompanyService.dll": {}}}, "BlueTape.CompanyService.Common/1.1.21": {"type": "package", "dependencies": {"BlueTape.LS": "1.1.68", "BlueTape.ServiceBusMessaging": "1.0.8"}, "compile": {"lib/net6.0/BlueTape.CompanyService.Common.dll": {}}, "runtime": {"lib/net6.0/BlueTape.CompanyService.Common.dll": {}}}, "BlueTape.EmailSender/3.0.7": {"type": "package", "dependencies": {"BlueTape.Utilities": "1.4.3", "Microsoft.Extensions.DependencyInjection.Abstractions": "8.0.0", "Microsoft.Extensions.Logging.Abstractions": "8.0.0", "Microsoft.Extensions.Options": "8.0.1", "SendGrid": "9.28.1", "SendGrid.Extensions.DependencyInjection": "1.0.1"}, "compile": {"lib/net6.0/BlueTape.EmailSender.dll": {}}, "runtime": {"lib/net6.0/BlueTape.EmailSender.dll": {}}}, "BlueTape.Integrations.Experian/1.0.2": {"type": "package", "compile": {"lib/net6.0/BlueTape.Integrations.Experian.dll": {}}, "runtime": {"lib/net6.0/BlueTape.Integrations.Experian.dll": {}}}, "BlueTape.Integrations.Giact/1.0.3": {"type": "package", "compile": {"lib/net6.0/BlueTape.Integrations.Giact.dll": {}}, "runtime": {"lib/net6.0/BlueTape.Integrations.Giact.dll": {}}}, "BlueTape.Integrations.LexisNexis/1.0.6": {"type": "package", "compile": {"lib/net6.0/BlueTape.Integrations.LexisNexis.dll": {}}, "runtime": {"lib/net6.0/BlueTape.Integrations.LexisNexis.dll": {}}}, "BlueTape.Integrations.Plaid/1.0.7": {"type": "package", "dependencies": {"BlueTape.Integrations.Plaid.Infrastructure": "1.0.0"}, "compile": {"lib/net6.0/BlueTape.Integrations.Plaid.dll": {}}, "runtime": {"lib/net6.0/BlueTape.Integrations.Plaid.dll": {}}}, "BlueTape.Integrations.Plaid.Infrastructure/1.0.0": {"type": "package", "compile": {"lib/net6.0/BlueTape.Integrations.Plaid.Infrastructure.dll": {}}, "runtime": {"lib/net6.0/BlueTape.Integrations.Plaid.Infrastructure.dll": {}}}, "BlueTape.InvoiceService/1.0.38": {"type": "package", "dependencies": {"BlueTape.Common.Validation": "1.0.4", "BlueTape.InvoiceService.Common": "1.1.2", "BlueTape.OBS": "1.6.47", "BlueTape.PaymentService": "1.0.3", "FluentValidation": "11.0.2"}, "compile": {"lib/net6.0/BlueTape.InvoiceService.dll": {}}, "runtime": {"lib/net6.0/BlueTape.InvoiceService.dll": {}}}, "BlueTape.InvoiceService.Common/1.1.2": {"type": "package", "compile": {"lib/net6.0/BlueTape.InvoiceService.Common.dll": {}}, "runtime": {"lib/net6.0/BlueTape.InvoiceService.Common.dll": {}}}, "BlueTape.LambdaBase/1.1.1": {"type": "package", "dependencies": {"Amazon.Lambda.Core": "2.1.0", "Amazon.Lambda.Serialization.SystemTextJson": "2.3.0", "Autofac": "6.4.0", "Autofac.Configuration": "6.0.0", "Autofac.Extensions.DependencyInjection": "8.0.0", "BlueTape.Utilities": "1.2.7", "Microsoft.Extensions.Configuration": "6.0.1", "Microsoft.Extensions.Configuration.EnvironmentVariables": "6.0.1", "Microsoft.Extensions.Configuration.FileExtensions": "6.0.0", "Microsoft.Extensions.Configuration.Json": "6.0.0", "Microsoft.Extensions.Configuration.UserSecrets": "6.0.1", "Microsoft.Extensions.DependencyInjection": "6.0.0", "Microsoft.Extensions.Logging": "6.0.0", "Microsoft.Extensions.Logging.Configuration": "6.0.0", "Microsoft.Extensions.Options": "8.0.1", "Serilog": "2.12.0", "Serilog.AspNetCore": "6.0.1", "Serilog.Enrichers.GlobalLogContext": "2.1.0", "Serilog.Sinks.Console": "4.1.0", "Serilog.Sinks.Logz.Io": "7.1.0"}, "compile": {"lib/net6.0/BlueTape.LambdaBase.dll": {}}, "runtime": {"lib/net6.0/BlueTape.LambdaBase.dll": {}}}, "BlueTape.LS/1.1.69": {"type": "package", "dependencies": {"BlueTape.LS.Domain": "1.1.33"}, "compile": {"lib/net6.0/BlueTape.LS.dll": {}}, "runtime": {"lib/net6.0/BlueTape.LS.dll": {}}}, "BlueTape.LS.Domain/1.1.33": {"type": "package", "dependencies": {"BlueTape.Utilities": "1.4.5"}, "compile": {"lib/net6.0/BlueTape.LS.Domain.dll": {}}, "runtime": {"lib/net6.0/BlueTape.LS.Domain.dll": {}}}, "BlueTape.MongoDB/1.1.31": {"type": "package", "dependencies": {"BlueTape.AzureKeyVault": "1.0.3", "BlueTape.Utilities": "1.4.5", "MongoDB.Driver": "2.25.0"}, "compile": {"lib/net6.0/BlueTape.MongoDB.dll": {}}, "runtime": {"lib/net6.0/BlueTape.MongoDB.dll": {}}}, "BlueTape.OBS/1.6.69": {"type": "package", "compile": {"lib/net6.0/BlueTape.OBS.dll": {}}, "runtime": {"lib/net6.0/BlueTape.OBS.dll": {}}}, "BlueTape.PaymentService/1.0.3": {"type": "package", "compile": {"lib/net6.0/BlueTape.PaymentService.dll": {}}, "runtime": {"lib/net6.0/BlueTape.PaymentService.dll": {}}}, "BlueTape.ServiceBusMessaging/1.0.8": {"type": "package", "dependencies": {"Azure.Messaging.ServiceBus": "7.17.1", "BlueTape.AzureKeyVault": "1.0.3"}, "compile": {"lib/net6.0/BlueTape.ServiceBusMessaging.dll": {}}, "runtime": {"lib/net6.0/BlueTape.ServiceBusMessaging.dll": {}}}, "BlueTape.SNS/1.0.2": {"type": "package", "dependencies": {"AWSSDK.SimpleNotificationService": "3.7.200.52", "Microsoft.Extensions.DependencyInjection": "7.0.0", "Serilog.AspNetCore": "6.0.1"}, "compile": {"lib/net6.0/BlueTape.SNS.dll": {}}, "runtime": {"lib/net6.0/BlueTape.SNS.dll": {}}}, "BlueTape.Utilities/1.4.6": {"type": "package", "dependencies": {"AWSSDK.KeyManagementService": "3.7.300.46", "AWSSDK.SecretsManager": "3.7.302.21", "AWSSDK.SecretsManager.Caching": "1.0.6", "AWSSDK.SecurityToken": "3.7.300.47", "BlueTape.Common.Extensions": "1.1.0", "BlueTape.OBS": "1.6.68", "Microsoft.AspNetCore.Http.Abstractions": "2.2.0", "Microsoft.Extensions.DependencyInjection.Abstractions": "8.0.0", "Microsoft.Extensions.Hosting.Abstractions": "6.0.0", "Microsoft.Extensions.Http.Polly": "6.0.9", "Microsoft.Extensions.Options": "8.0.1", "MongoDB.Bson": "2.25.0", "Polly": "7.2.3", "Serilog": "2.12.0", "Serilog.AspNetCore": "6.0.1", "Serilog.Enrichers.GlobalLogContext": "2.1.0", "Serilog.Sinks.Console": "4.1.0", "Serilog.Sinks.Logz.Io": "7.1.0"}, "compile": {"lib/net6.0/BlueTape.Utilities.dll": {}}, "runtime": {"lib/net6.0/BlueTape.Utilities.dll": {}}, "frameworkReferences": ["Microsoft.AspNetCore.App"]}, "DateOnlyTimeOnly.AspNet/2.1.1": {"type": "package", "compile": {"lib/net6.0/DateOnlyTimeOnly.AspNet.dll": {}}, "runtime": {"lib/net6.0/DateOnlyTimeOnly.AspNet.dll": {}}, "frameworkReferences": ["Microsoft.AspNetCore.App"]}, "DnsClient/1.6.1": {"type": "package", "dependencies": {"Microsoft.Win32.Registry": "5.0.0"}, "compile": {"lib/net5.0/DnsClient.dll": {"related": ".xml"}}, "runtime": {"lib/net5.0/DnsClient.dll": {"related": ".xml"}}}, "Elastic.CommonSchema/1.5.3": {"type": "package", "dependencies": {"System.Text.Json": "4.7.0"}, "compile": {"lib/netstandard2.1/Elastic.CommonSchema.dll": {"related": ".pdb"}}, "runtime": {"lib/netstandard2.1/Elastic.CommonSchema.dll": {"related": ".pdb"}}}, "Elastic.CommonSchema.Serilog/1.5.3": {"type": "package", "dependencies": {"Elastic.CommonSchema": "1.5.3", "Microsoft.AspNetCore.Http.Abstractions": "2.1.0", "Serilog": "2.9.0"}, "compile": {"lib/netstandard2.1/Elastic.CommonSchema.Serilog.dll": {"related": ".pdb"}}, "runtime": {"lib/netstandard2.1/Elastic.CommonSchema.Serilog.dll": {"related": ".pdb"}}}, "F23.StringSimilarity/5.1.0": {"type": "package", "dependencies": {"System.Memory": "4.5.4"}, "compile": {"lib/netstandard2.0/F23.StringSimilarity.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.0/F23.StringSimilarity.dll": {"related": ".xml"}}}, "FluentValidation/11.0.2": {"type": "package", "compile": {"lib/net6.0/FluentValidation.dll": {"related": ".xml"}}, "runtime": {"lib/net6.0/FluentValidation.dll": {"related": ".xml"}}}, "libphonenumber-csharp/8.13.24": {"type": "package", "compile": {"lib/net6.0/PhoneNumbers.dll": {"related": ".xml"}}, "runtime": {"lib/net6.0/PhoneNumbers.dll": {"related": ".xml"}}}, "Macross.Json.Extensions/3.0.0": {"type": "package", "compile": {"lib/net6.0/Macross.Json.Extensions.dll": {"related": ".xml"}}, "runtime": {"lib/net6.0/Macross.Json.Extensions.dll": {"related": ".xml"}}}, "Microsoft.AspNetCore.Http.Abstractions/2.2.0": {"type": "package", "dependencies": {"Microsoft.AspNetCore.Http.Features": "2.2.0", "System.Text.Encodings.Web": "4.5.0"}, "compile": {"lib/netstandard2.0/Microsoft.AspNetCore.Http.Abstractions.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.0/Microsoft.AspNetCore.Http.Abstractions.dll": {"related": ".xml"}}}, "Microsoft.AspNetCore.Http.Features/2.2.0": {"type": "package", "dependencies": {"Microsoft.Extensions.Primitives": "2.2.0"}, "compile": {"lib/netstandard2.0/Microsoft.AspNetCore.Http.Features.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.0/Microsoft.AspNetCore.Http.Features.dll": {"related": ".xml"}}}, "Microsoft.Azure.Amqp/2.6.4": {"type": "package", "compile": {"lib/netstandard2.0/Microsoft.Azure.Amqp.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.0/Microsoft.Azure.Amqp.dll": {"related": ".xml"}}}, "Microsoft.Bcl.AsyncInterfaces/1.1.1": {"type": "package", "compile": {"ref/netstandard2.1/Microsoft.Bcl.AsyncInterfaces.dll": {}}, "runtime": {"lib/netstandard2.1/Microsoft.Bcl.AsyncInterfaces.dll": {"related": ".xml"}}}, "Microsoft.CSharp/4.7.0": {"type": "package", "compile": {"ref/netcoreapp2.0/_._": {}}, "runtime": {"lib/netcoreapp2.0/_._": {}}}, "Microsoft.Extensions.Caching.Abstractions/7.0.0": {"type": "package", "dependencies": {"Microsoft.Extensions.Primitives": "7.0.0"}, "compile": {"lib/net6.0/Microsoft.Extensions.Caching.Abstractions.dll": {"related": ".xml"}}, "runtime": {"lib/net6.0/Microsoft.Extensions.Caching.Abstractions.dll": {"related": ".xml"}}, "build": {"buildTransitive/net6.0/_._": {}}}, "Microsoft.Extensions.Caching.Memory/7.0.0": {"type": "package", "dependencies": {"Microsoft.Extensions.Caching.Abstractions": "7.0.0", "Microsoft.Extensions.DependencyInjection.Abstractions": "7.0.0", "Microsoft.Extensions.Logging.Abstractions": "7.0.0", "Microsoft.Extensions.Options": "7.0.0", "Microsoft.Extensions.Primitives": "7.0.0"}, "compile": {"lib/net6.0/Microsoft.Extensions.Caching.Memory.dll": {"related": ".xml"}}, "runtime": {"lib/net6.0/Microsoft.Extensions.Caching.Memory.dll": {"related": ".xml"}}, "build": {"buildTransitive/net6.0/_._": {}}}, "Microsoft.Extensions.Configuration/6.0.1": {"type": "package", "dependencies": {"Microsoft.Extensions.Configuration.Abstractions": "6.0.0", "Microsoft.Extensions.Primitives": "6.0.0"}, "compile": {"lib/netstandard2.0/Microsoft.Extensions.Configuration.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.0/Microsoft.Extensions.Configuration.dll": {"related": ".xml"}}}, "Microsoft.Extensions.Configuration.Abstractions/8.0.0": {"type": "package", "dependencies": {"Microsoft.Extensions.Primitives": "8.0.0"}, "compile": {"lib/net6.0/Microsoft.Extensions.Configuration.Abstractions.dll": {"related": ".xml"}}, "runtime": {"lib/net6.0/Microsoft.Extensions.Configuration.Abstractions.dll": {"related": ".xml"}}, "build": {"buildTransitive/net6.0/_._": {}}}, "Microsoft.Extensions.Configuration.Binder/8.0.0": {"type": "package", "dependencies": {"Microsoft.Extensions.Configuration.Abstractions": "8.0.0"}, "compile": {"lib/net6.0/Microsoft.Extensions.Configuration.Binder.dll": {"related": ".xml"}}, "runtime": {"lib/net6.0/Microsoft.Extensions.Configuration.Binder.dll": {"related": ".xml"}}, "build": {"buildTransitive/netstandard2.0/Microsoft.Extensions.Configuration.Binder.targets": {}}}, "Microsoft.Extensions.Configuration.EnvironmentVariables/6.0.1": {"type": "package", "dependencies": {"Microsoft.Extensions.Configuration": "6.0.0", "Microsoft.Extensions.Configuration.Abstractions": "6.0.0"}, "compile": {"lib/netstandard2.0/Microsoft.Extensions.Configuration.EnvironmentVariables.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.0/Microsoft.Extensions.Configuration.EnvironmentVariables.dll": {"related": ".xml"}}}, "Microsoft.Extensions.Configuration.FileExtensions/6.0.0": {"type": "package", "dependencies": {"Microsoft.Extensions.Configuration": "6.0.0", "Microsoft.Extensions.Configuration.Abstractions": "6.0.0", "Microsoft.Extensions.FileProviders.Abstractions": "6.0.0", "Microsoft.Extensions.FileProviders.Physical": "6.0.0", "Microsoft.Extensions.Primitives": "6.0.0"}, "compile": {"lib/netstandard2.0/Microsoft.Extensions.Configuration.FileExtensions.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.0/Microsoft.Extensions.Configuration.FileExtensions.dll": {"related": ".xml"}}}, "Microsoft.Extensions.Configuration.Json/6.0.0": {"type": "package", "dependencies": {"Microsoft.Extensions.Configuration": "6.0.0", "Microsoft.Extensions.Configuration.Abstractions": "6.0.0", "Microsoft.Extensions.Configuration.FileExtensions": "6.0.0", "Microsoft.Extensions.FileProviders.Abstractions": "6.0.0", "System.Text.Json": "6.0.0"}, "compile": {"lib/netstandard2.1/Microsoft.Extensions.Configuration.Json.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.1/Microsoft.Extensions.Configuration.Json.dll": {"related": ".xml"}}}, "Microsoft.Extensions.Configuration.UserSecrets/6.0.1": {"type": "package", "dependencies": {"Microsoft.Extensions.Configuration.Abstractions": "6.0.0", "Microsoft.Extensions.Configuration.Json": "6.0.0", "Microsoft.Extensions.FileProviders.Abstractions": "6.0.0", "Microsoft.Extensions.FileProviders.Physical": "6.0.0"}, "compile": {"lib/netstandard2.0/Microsoft.Extensions.Configuration.UserSecrets.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.0/Microsoft.Extensions.Configuration.UserSecrets.dll": {"related": ".xml"}}, "build": {"buildTransitive/netstandard2.0/Microsoft.Extensions.Configuration.UserSecrets.props": {}, "buildTransitive/netstandard2.0/Microsoft.Extensions.Configuration.UserSecrets.targets": {}}}, "Microsoft.Extensions.DependencyInjection/8.0.0": {"type": "package", "dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "8.0.0"}, "compile": {"lib/net6.0/Microsoft.Extensions.DependencyInjection.dll": {"related": ".xml"}}, "runtime": {"lib/net6.0/Microsoft.Extensions.DependencyInjection.dll": {"related": ".xml"}}, "build": {"buildTransitive/net6.0/_._": {}}}, "Microsoft.Extensions.DependencyInjection.Abstractions/8.0.0": {"type": "package", "compile": {"lib/net6.0/Microsoft.Extensions.DependencyInjection.Abstractions.dll": {"related": ".xml"}}, "runtime": {"lib/net6.0/Microsoft.Extensions.DependencyInjection.Abstractions.dll": {"related": ".xml"}}, "build": {"buildTransitive/net6.0/_._": {}}}, "Microsoft.Extensions.DependencyModel/3.0.0": {"type": "package", "dependencies": {"System.Text.Json": "4.6.0"}, "compile": {"lib/netstandard2.0/Microsoft.Extensions.DependencyModel.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.0/Microsoft.Extensions.DependencyModel.dll": {"related": ".xml"}}}, "Microsoft.Extensions.FileProviders.Abstractions/6.0.0": {"type": "package", "dependencies": {"Microsoft.Extensions.Primitives": "6.0.0"}, "compile": {"lib/net6.0/Microsoft.Extensions.FileProviders.Abstractions.dll": {"related": ".xml"}}, "runtime": {"lib/net6.0/Microsoft.Extensions.FileProviders.Abstractions.dll": {"related": ".xml"}}, "build": {"buildTransitive/netcoreapp3.1/_._": {}}}, "Microsoft.Extensions.FileProviders.Physical/6.0.0": {"type": "package", "dependencies": {"Microsoft.Extensions.FileProviders.Abstractions": "6.0.0", "Microsoft.Extensions.FileSystemGlobbing": "6.0.0", "Microsoft.Extensions.Primitives": "6.0.0"}, "compile": {"lib/net6.0/Microsoft.Extensions.FileProviders.Physical.dll": {"related": ".xml"}}, "runtime": {"lib/net6.0/Microsoft.Extensions.FileProviders.Physical.dll": {"related": ".xml"}}, "build": {"buildTransitive/netcoreapp3.1/_._": {}}}, "Microsoft.Extensions.FileSystemGlobbing/6.0.0": {"type": "package", "compile": {"lib/net6.0/Microsoft.Extensions.FileSystemGlobbing.dll": {"related": ".xml"}}, "runtime": {"lib/net6.0/Microsoft.Extensions.FileSystemGlobbing.dll": {"related": ".xml"}}, "build": {"buildTransitive/netcoreapp3.1/_._": {}}}, "Microsoft.Extensions.Hosting.Abstractions/6.0.0": {"type": "package", "dependencies": {"Microsoft.Extensions.Configuration.Abstractions": "6.0.0", "Microsoft.Extensions.DependencyInjection.Abstractions": "6.0.0", "Microsoft.Extensions.FileProviders.Abstractions": "6.0.0"}, "compile": {"lib/netstandard2.1/Microsoft.Extensions.Hosting.Abstractions.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.1/Microsoft.Extensions.Hosting.Abstractions.dll": {"related": ".xml"}}}, "Microsoft.Extensions.Http/6.0.0": {"type": "package", "dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "6.0.0", "Microsoft.Extensions.Logging": "6.0.0", "Microsoft.Extensions.Logging.Abstractions": "6.0.0", "Microsoft.Extensions.Options": "6.0.0"}, "compile": {"lib/netstandard2.0/Microsoft.Extensions.Http.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.0/Microsoft.Extensions.Http.dll": {"related": ".xml"}}}, "Microsoft.Extensions.Http.Polly/6.0.9": {"type": "package", "dependencies": {"Microsoft.Extensions.Http": "6.0.0", "Polly": "7.2.2", "Polly.Extensions.Http": "3.0.0"}, "compile": {"lib/netstandard2.0/Microsoft.Extensions.Http.Polly.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.0/Microsoft.Extensions.Http.Polly.dll": {"related": ".xml"}}}, "Microsoft.Extensions.Logging/6.0.0": {"type": "package", "dependencies": {"Microsoft.Extensions.DependencyInjection": "6.0.0", "Microsoft.Extensions.DependencyInjection.Abstractions": "6.0.0", "Microsoft.Extensions.Logging.Abstractions": "6.0.0", "Microsoft.Extensions.Options": "6.0.0", "System.Diagnostics.DiagnosticSource": "6.0.0"}, "compile": {"lib/netstandard2.1/Microsoft.Extensions.Logging.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.1/Microsoft.Extensions.Logging.dll": {"related": ".xml"}}}, "Microsoft.Extensions.Logging.Abstractions/8.0.0": {"type": "package", "dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "8.0.0"}, "compile": {"lib/net6.0/Microsoft.Extensions.Logging.Abstractions.dll": {"related": ".xml"}}, "runtime": {"lib/net6.0/Microsoft.Extensions.Logging.Abstractions.dll": {"related": ".xml"}}, "build": {"buildTransitive/net6.0/Microsoft.Extensions.Logging.Abstractions.targets": {}}}, "Microsoft.Extensions.Logging.Configuration/6.0.0": {"type": "package", "dependencies": {"Microsoft.Extensions.Configuration": "6.0.0", "Microsoft.Extensions.Configuration.Abstractions": "6.0.0", "Microsoft.Extensions.Configuration.Binder": "6.0.0", "Microsoft.Extensions.DependencyInjection.Abstractions": "6.0.0", "Microsoft.Extensions.Logging": "6.0.0", "Microsoft.Extensions.Logging.Abstractions": "6.0.0", "Microsoft.Extensions.Options": "6.0.0", "Microsoft.Extensions.Options.ConfigurationExtensions": "6.0.0"}, "compile": {"lib/netstandard2.0/Microsoft.Extensions.Logging.Configuration.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.0/Microsoft.Extensions.Logging.Configuration.dll": {"related": ".xml"}}}, "Microsoft.Extensions.Options/8.0.1": {"type": "package", "dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "8.0.0", "Microsoft.Extensions.Primitives": "8.0.0"}, "compile": {"lib/net6.0/Microsoft.Extensions.Options.dll": {"related": ".xml"}}, "runtime": {"lib/net6.0/Microsoft.Extensions.Options.dll": {"related": ".xml"}}, "build": {"buildTransitive/net6.0/Microsoft.Extensions.Options.targets": {}}}, "Microsoft.Extensions.Options.ConfigurationExtensions/8.0.0": {"type": "package", "dependencies": {"Microsoft.Extensions.Configuration.Abstractions": "8.0.0", "Microsoft.Extensions.Configuration.Binder": "8.0.0", "Microsoft.Extensions.DependencyInjection.Abstractions": "8.0.0", "Microsoft.Extensions.Options": "8.0.0", "Microsoft.Extensions.Primitives": "8.0.0"}, "compile": {"lib/net6.0/Microsoft.Extensions.Options.ConfigurationExtensions.dll": {"related": ".xml"}}, "runtime": {"lib/net6.0/Microsoft.Extensions.Options.ConfigurationExtensions.dll": {"related": ".xml"}}, "build": {"buildTransitive/net6.0/_._": {}}}, "Microsoft.Extensions.Primitives/8.0.0": {"type": "package", "dependencies": {"System.Runtime.CompilerServices.Unsafe": "6.0.0"}, "compile": {"lib/net6.0/Microsoft.Extensions.Primitives.dll": {"related": ".xml"}}, "runtime": {"lib/net6.0/Microsoft.Extensions.Primitives.dll": {"related": ".xml"}}, "build": {"buildTransitive/net6.0/_._": {}}}, "Microsoft.Identity.Client/4.56.0": {"type": "package", "dependencies": {"Microsoft.IdentityModel.Abstractions": "6.22.0"}, "compile": {"lib/net6.0/Microsoft.Identity.Client.dll": {"related": ".xml"}}, "runtime": {"lib/net6.0/Microsoft.Identity.Client.dll": {"related": ".xml"}}}, "Microsoft.Identity.Client.Extensions.Msal/4.56.0": {"type": "package", "dependencies": {"Microsoft.Identity.Client": "4.56.0", "System.IO.FileSystem.AccessControl": "5.0.0", "System.Security.Cryptography.ProtectedData": "4.5.0"}, "compile": {"lib/netstandard2.0/Microsoft.Identity.Client.Extensions.Msal.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.0/Microsoft.Identity.Client.Extensions.Msal.dll": {"related": ".xml"}}}, "Microsoft.IdentityModel.Abstractions/6.22.0": {"type": "package", "compile": {"lib/net6.0/Microsoft.IdentityModel.Abstractions.dll": {"related": ".xml"}}, "runtime": {"lib/net6.0/Microsoft.IdentityModel.Abstractions.dll": {"related": ".xml"}}}, "Microsoft.NETCore.Platforms/5.0.0": {"type": "package", "compile": {"lib/netstandard1.0/_._": {}}, "runtime": {"lib/netstandard1.0/_._": {}}}, "Microsoft.Win32.Registry/5.0.0": {"type": "package", "dependencies": {"System.Security.AccessControl": "5.0.0", "System.Security.Principal.Windows": "5.0.0"}, "compile": {"ref/netstandard2.0/Microsoft.Win32.Registry.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.0/Microsoft.Win32.Registry.dll": {"related": ".xml"}}, "runtimeTargets": {"runtimes/win/lib/netstandard2.0/Microsoft.Win32.Registry.dll": {"assetType": "runtime", "rid": "win"}}}, "MongoDB.Bson/2.25.0": {"type": "package", "dependencies": {"System.Memory": "4.5.5", "System.Runtime.CompilerServices.Unsafe": "5.0.0"}, "compile": {"lib/netstandard2.1/MongoDB.Bson.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.1/MongoDB.Bson.dll": {"related": ".xml"}}}, "MongoDB.Driver/2.25.0": {"type": "package", "dependencies": {"Microsoft.Extensions.Logging.Abstractions": "2.0.0", "MongoDB.Bson": "2.25.0", "MongoDB.Driver.Core": "2.25.0", "MongoDB.Libmongocrypt": "1.8.2"}, "compile": {"lib/netstandard2.1/MongoDB.Driver.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.1/MongoDB.Driver.dll": {"related": ".xml"}}}, "MongoDB.Driver.Core/2.25.0": {"type": "package", "dependencies": {"AWSSDK.SecurityToken": "3.7.100.14", "DnsClient": "1.6.1", "Microsoft.Extensions.Logging.Abstractions": "2.0.0", "MongoDB.Bson": "2.25.0", "MongoDB.Libmongocrypt": "1.8.2", "SharpCompress": "0.30.1", "Snappier": "1.0.0", "System.Buffers": "4.5.1", "ZstdSharp.Port": "0.7.3"}, "compile": {"lib/netstandard2.1/MongoDB.Driver.Core.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.1/MongoDB.Driver.Core.dll": {"related": ".xml"}}}, "MongoDB.Libmongocrypt/1.8.2": {"type": "package", "compile": {"lib/netstandard2.1/MongoDB.Libmongocrypt.dll": {}}, "runtime": {"lib/netstandard2.1/MongoDB.Libmongocrypt.dll": {}}, "contentFiles": {"contentFiles/any/any/_._": {"buildAction": "None", "codeLanguage": "any", "copyToOutput": false}}, "build": {"build/_._": {}}, "runtimeTargets": {"runtimes/linux/native/libmongocrypt.so": {"assetType": "native", "rid": "linux"}, "runtimes/osx/native/libmongocrypt.dylib": {"assetType": "native", "rid": "osx"}, "runtimes/win/native/mongocrypt.dll": {"assetType": "native", "rid": "win"}}}, "Newtonsoft.Json/13.0.1": {"type": "package", "compile": {"lib/netstandard2.0/Newtonsoft.Json.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.0/Newtonsoft.Json.dll": {"related": ".xml"}}}, "Polly/7.2.3": {"type": "package", "compile": {"lib/netstandard2.0/Polly.dll": {"related": ".pdb;.xml"}}, "runtime": {"lib/netstandard2.0/Polly.dll": {"related": ".pdb;.xml"}}}, "Polly.Extensions.Http/3.0.0": {"type": "package", "dependencies": {"Polly": "7.1.0"}, "compile": {"lib/netstandard2.0/Polly.Extensions.Http.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.0/Polly.Extensions.Http.dll": {"related": ".xml"}}}, "SendGrid/9.28.1": {"type": "package", "dependencies": {"Newtonsoft.Json": "13.0.1", "starkbank-ecdsa": "[1.3.3, 2.0.0)"}, "compile": {"lib/netstandard2.0/SendGrid.dll": {"related": ".pdb;.xml"}}, "runtime": {"lib/netstandard2.0/SendGrid.dll": {"related": ".pdb;.xml"}}}, "SendGrid.Extensions.DependencyInjection/1.0.1": {"type": "package", "dependencies": {"Microsoft.Extensions.Http": "2.1.0", "SendGrid": "9.24.3"}, "compile": {"lib/netstandard2.0/SendGrid.Extensions.DependencyInjection.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.0/SendGrid.Extensions.DependencyInjection.dll": {"related": ".xml"}}}, "Serilog/2.12.0": {"type": "package", "compile": {"lib/net6.0/Serilog.dll": {"related": ".xml"}}, "runtime": {"lib/net6.0/Serilog.dll": {"related": ".xml"}}}, "Serilog.AspNetCore/6.0.1": {"type": "package", "dependencies": {"Microsoft.Extensions.DependencyInjection": "5.0.0", "Microsoft.Extensions.Logging": "5.0.0", "Serilog": "2.10.0", "Serilog.Extensions.Hosting": "5.0.1", "Serilog.Formatting.Compact": "1.1.0", "Serilog.Settings.Configuration": "3.3.0", "Serilog.Sinks.Console": "4.0.1", "Serilog.Sinks.Debug": "2.0.0", "Serilog.Sinks.File": "5.0.0"}, "compile": {"lib/net5.0/Serilog.AspNetCore.dll": {"related": ".xml"}}, "runtime": {"lib/net5.0/Serilog.AspNetCore.dll": {"related": ".xml"}}, "frameworkReferences": ["Microsoft.AspNetCore.App"]}, "Serilog.Enrichers.GlobalLogContext/2.1.0": {"type": "package", "dependencies": {"Serilog": "2.11.0"}, "compile": {"lib/net5.0/Serilog.Enrichers.GlobalLogContext.dll": {"related": ".pdb;.xml"}}, "runtime": {"lib/net5.0/Serilog.Enrichers.GlobalLogContext.dll": {"related": ".pdb;.xml"}}}, "Serilog.Extensions.Hosting/5.0.1": {"type": "package", "dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "3.1.8", "Microsoft.Extensions.Hosting.Abstractions": "3.1.8", "Microsoft.Extensions.Logging.Abstractions": "3.1.8", "Serilog": "2.10.0", "Serilog.Extensions.Logging": "3.1.0"}, "compile": {"lib/netstandard2.1/Serilog.Extensions.Hosting.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.1/Serilog.Extensions.Hosting.dll": {"related": ".xml"}}}, "Serilog.Extensions.Logging/3.1.0": {"type": "package", "dependencies": {"Microsoft.Extensions.Logging": "2.0.0", "Serilog": "2.9.0"}, "compile": {"lib/netstandard2.0/Serilog.Extensions.Logging.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.0/Serilog.Extensions.Logging.dll": {"related": ".xml"}}}, "Serilog.Formatting.Compact/1.1.0": {"type": "package", "dependencies": {"Serilog": "2.8.0"}, "compile": {"lib/netstandard2.0/Serilog.Formatting.Compact.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.0/Serilog.Formatting.Compact.dll": {"related": ".xml"}}}, "Serilog.Settings.Configuration/3.3.0": {"type": "package", "dependencies": {"Microsoft.Extensions.DependencyModel": "3.0.0", "Microsoft.Extensions.Options.ConfigurationExtensions": "2.0.0", "Serilog": "2.10.0"}, "compile": {"lib/netstandard2.0/Serilog.Settings.Configuration.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.0/Serilog.Settings.Configuration.dll": {"related": ".xml"}}}, "Serilog.Sinks.Console/4.1.0": {"type": "package", "dependencies": {"Serilog": "2.10.0"}, "compile": {"lib/net5.0/Serilog.Sinks.Console.dll": {"related": ".xml"}}, "runtime": {"lib/net5.0/Serilog.Sinks.Console.dll": {"related": ".xml"}}}, "Serilog.Sinks.Debug/2.0.0": {"type": "package", "dependencies": {"Serilog": "2.10.0"}, "compile": {"lib/netstandard2.1/Serilog.Sinks.Debug.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.1/Serilog.Sinks.Debug.dll": {"related": ".xml"}}}, "Serilog.Sinks.File/5.0.0": {"type": "package", "dependencies": {"Serilog": "2.10.0"}, "compile": {"lib/net5.0/Serilog.Sinks.File.dll": {"related": ".pdb;.xml"}}, "runtime": {"lib/net5.0/Serilog.Sinks.File.dll": {"related": ".pdb;.xml"}}}, "Serilog.Sinks.Http/8.0.0": {"type": "package", "dependencies": {"Microsoft.Extensions.Configuration.Abstractions": "2.0.0", "Serilog.Sinks.File": "5.0.0"}, "compile": {"lib/netstandard2.1/Serilog.Sinks.Http.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.1/Serilog.Sinks.Http.dll": {"related": ".xml"}}}, "Serilog.Sinks.Logz.Io/7.1.0": {"type": "package", "dependencies": {"Elastic.CommonSchema.Serilog": "1.5.3", "Newtonsoft.Json": "13.0.1", "Serilog.Sinks.Http": "8.0.0", "Serilog.Sinks.PeriodicBatching": "3.1.0"}, "compile": {"lib/net6.0/Serilog.Sinks.Logz.Io.dll": {}}, "runtime": {"lib/net6.0/Serilog.Sinks.Logz.Io.dll": {}}}, "Serilog.Sinks.PeriodicBatching/3.1.0": {"type": "package", "dependencies": {"Serilog": "2.0.0"}, "compile": {"lib/netstandard2.1/Serilog.Sinks.PeriodicBatching.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.1/Serilog.Sinks.PeriodicBatching.dll": {"related": ".xml"}}}, "SharpCompress/0.30.1": {"type": "package", "compile": {"lib/net5.0/SharpCompress.dll": {}}, "runtime": {"lib/net5.0/SharpCompress.dll": {}}}, "Snappier/1.0.0": {"type": "package", "compile": {"lib/net5.0/Snappier.dll": {"related": ".xml"}}, "runtime": {"lib/net5.0/Snappier.dll": {"related": ".xml"}}}, "starkbank-ecdsa/1.3.3": {"type": "package", "compile": {"lib/netstandard2.1/StarkbankEcdsa.dll": {}}, "runtime": {"lib/netstandard2.1/StarkbankEcdsa.dll": {}}}, "System.Buffers/4.5.1": {"type": "package", "compile": {"ref/netcoreapp2.0/_._": {}}, "runtime": {"lib/netcoreapp2.0/_._": {}}}, "System.Diagnostics.DiagnosticSource/6.0.1": {"type": "package", "dependencies": {"System.Runtime.CompilerServices.Unsafe": "6.0.0"}, "compile": {"lib/net6.0/System.Diagnostics.DiagnosticSource.dll": {"related": ".xml"}}, "runtime": {"lib/net6.0/System.Diagnostics.DiagnosticSource.dll": {"related": ".xml"}}, "build": {"buildTransitive/netcoreapp3.1/_._": {}}}, "System.IO.FileSystem.AccessControl/5.0.0": {"type": "package", "dependencies": {"System.Security.AccessControl": "5.0.0", "System.Security.Principal.Windows": "5.0.0"}, "compile": {"ref/netstandard2.0/System.IO.FileSystem.AccessControl.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.0/System.IO.FileSystem.AccessControl.dll": {"related": ".xml"}}, "runtimeTargets": {"runtimes/win/lib/netstandard2.0/System.IO.FileSystem.AccessControl.dll": {"assetType": "runtime", "rid": "win"}}}, "System.Memory/4.5.5": {"type": "package", "compile": {"ref/netcoreapp2.1/_._": {}}, "runtime": {"lib/netcoreapp2.1/_._": {}}}, "System.Memory.Data/1.0.2": {"type": "package", "dependencies": {"System.Text.Encodings.Web": "4.7.2", "System.Text.Json": "4.6.0"}, "compile": {"lib/netstandard2.0/System.Memory.Data.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.0/System.Memory.Data.dll": {"related": ".xml"}}}, "System.Numerics.Vectors/4.5.0": {"type": "package", "compile": {"ref/netcoreapp2.0/_._": {}}, "runtime": {"lib/netcoreapp2.0/_._": {}}}, "System.Runtime.CompilerServices.Unsafe/6.0.0": {"type": "package", "compile": {"lib/net6.0/System.Runtime.CompilerServices.Unsafe.dll": {"related": ".xml"}}, "runtime": {"lib/net6.0/System.Runtime.CompilerServices.Unsafe.dll": {"related": ".xml"}}, "build": {"buildTransitive/netcoreapp3.1/_._": {}}}, "System.Security.AccessControl/5.0.0": {"type": "package", "dependencies": {"Microsoft.NETCore.Platforms": "5.0.0", "System.Security.Principal.Windows": "5.0.0"}, "compile": {"ref/netstandard2.0/System.Security.AccessControl.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.0/System.Security.AccessControl.dll": {"related": ".xml"}}, "runtimeTargets": {"runtimes/win/lib/netcoreapp2.0/System.Security.AccessControl.dll": {"assetType": "runtime", "rid": "win"}}}, "System.Security.Cryptography.ProtectedData/4.7.0": {"type": "package", "compile": {"ref/netstandard2.0/System.Security.Cryptography.ProtectedData.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.0/System.Security.Cryptography.ProtectedData.dll": {"related": ".xml"}}, "runtimeTargets": {"runtimes/win/lib/netstandard2.0/System.Security.Cryptography.ProtectedData.dll": {"assetType": "runtime", "rid": "win"}}}, "System.Security.Principal.Windows/5.0.0": {"type": "package", "compile": {"ref/netcoreapp3.0/System.Security.Principal.Windows.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.0/System.Security.Principal.Windows.dll": {"related": ".xml"}}, "runtimeTargets": {"runtimes/unix/lib/netcoreapp2.1/System.Security.Principal.Windows.dll": {"assetType": "runtime", "rid": "unix"}, "runtimes/win/lib/netcoreapp2.1/System.Security.Principal.Windows.dll": {"assetType": "runtime", "rid": "win"}}}, "System.Text.Encodings.Web/8.0.0": {"type": "package", "dependencies": {"System.Runtime.CompilerServices.Unsafe": "6.0.0"}, "compile": {"lib/net6.0/System.Text.Encodings.Web.dll": {"related": ".xml"}}, "runtime": {"lib/net6.0/System.Text.Encodings.Web.dll": {"related": ".xml"}}, "build": {"buildTransitive/net6.0/_._": {}}, "runtimeTargets": {"runtimes/browser/lib/net6.0/System.Text.Encodings.Web.dll": {"assetType": "runtime", "rid": "browser"}}}, "System.Text.Json/8.0.5": {"type": "package", "dependencies": {"System.Runtime.CompilerServices.Unsafe": "6.0.0", "System.Text.Encodings.Web": "8.0.0"}, "compile": {"lib/net6.0/System.Text.Json.dll": {"related": ".xml"}}, "runtime": {"lib/net6.0/System.Text.Json.dll": {"related": ".xml"}}, "build": {"buildTransitive/net6.0/System.Text.Json.targets": {}}}, "System.Threading.Tasks.Extensions/4.5.4": {"type": "package", "compile": {"ref/netcoreapp2.1/_._": {}}, "runtime": {"lib/netcoreapp2.1/_._": {}}}, "TinyHelpers/3.1.18": {"type": "package", "dependencies": {"System.Text.Json": "8.0.5"}, "compile": {"lib/net6.0/TinyHelpers.dll": {"related": ".xml"}}, "runtime": {"lib/net6.0/TinyHelpers.dll": {"related": ".xml"}}}, "ZstdSharp.Port/0.7.3": {"type": "package", "compile": {"lib/net6.0/ZstdSharp.dll": {}}, "runtime": {"lib/net6.0/ZstdSharp.dll": {}}}, "BlueTape.Services.DecisionEngine.BusinessLogic/1.0.0": {"type": "project", "framework": ".NETCoreApp,Version=v6.0", "dependencies": {"AWSSDK.Extensions.NETCore.Setup": "3.7.300", "Amazon.Lambda.Core": "2.2.0", "AutoMapper": "12.0.1", "AutoMapper.Extensions.Microsoft.DependencyInjection": "12.0.1", "BlueTape.AWSMessaging": "2.0.5", "BlueTape.AWSS3": "1.1.6", "BlueTape.EmailSender": "3.0.7", "BlueTape.MongoDB": "1.1.31", "BlueTape.SNS": "1.0.2", "BlueTape.Services.DecisionEngine.DataAccess.External": "1.0.0", "BlueTape.Services.DecisionEngine.Domain": "1.0.0", "BlueTape.Services.DecisionEngine.Infrastructure": "1.0.0", "BlueTape.Services.DecisionEngine.PolicyRulesFramework.PolicyRulesEngine": "1.0.0", "DateOnlyTimeOnly.AspNet": "2.1.1", "F23.StringSimilarity": "5.1.0", "Microsoft.Extensions.DependencyInjection": "8.0.0", "TinyHelpers": "3.1.18", "libphonenumber-csharp": "8.13.24"}, "compile": {"bin/placeholder/BlueTape.Services.DecisionEngine.BusinessLogic.dll": {}}, "runtime": {"bin/placeholder/BlueTape.Services.DecisionEngine.BusinessLogic.dll": {}}}, "BlueTape.Services.DecisionEngine.DataAccess.External/1.0.0": {"type": "project", "framework": ".NETCoreApp,Version=v6.0", "dependencies": {"AWSSDK.Extensions.NETCore.Setup": "3.7.300", "BlueTape.CompanyService": "1.2.42", "BlueTape.CompanyService.Common": "1.1.21", "BlueTape.Integrations.Experian": "1.0.2", "BlueTape.Integrations.Giact": "1.0.3", "BlueTape.Integrations.LexisNexis": "1.0.6", "BlueTape.Integrations.Plaid": "1.0.7", "BlueTape.Integrations.Plaid.Infrastructure": "1.0.0", "BlueTape.LS": "1.1.69", "BlueTape.Services.DecisionEngine.Domain": "1.0.0", "BlueTape.Services.DecisionEngine.Infrastructure": "1.0.0", "BlueTape.Utilities": "1.4.6", "DateOnlyTimeOnly.AspNet": "2.1.1", "Macross.Json.Extensions": "3.0.0", "Newtonsoft.Json": "13.0.1", "bluetape.invoiceservice": "1.0.38"}, "compile": {"bin/placeholder/BlueTape.Services.DecisionEngine.DataAccess.External.dll": {}}, "runtime": {"bin/placeholder/BlueTape.Services.DecisionEngine.DataAccess.External.dll": {}}}, "BlueTape.Services.DecisionEngine.Domain/1.0.0": {"type": "project", "framework": ".NETCoreApp,Version=v6.0", "dependencies": {"BlueTape.CashFlow.Domain": "1.0.2", "BlueTape.OBS": "1.6.69"}, "compile": {"bin/placeholder/BlueTape.Services.DecisionEngine.Domain.dll": {}}, "runtime": {"bin/placeholder/BlueTape.Services.DecisionEngine.Domain.dll": {}}}, "BlueTape.Services.DecisionEngine.Infrastructure/1.0.0": {"type": "project", "framework": ".NETCoreApp,Version=v6.0", "dependencies": {"AWSSDK.Extensions.NETCore.Setup": "3.7.300", "AWSSDK.KeyManagementService": "3.7.300.54", "AWSSDK.SecretsManager": "3.7.302.29", "BlueTape.Utilities": "1.4.6", "Microsoft.Extensions.DependencyInjection.Abstractions": "8.0.0"}, "compile": {"bin/placeholder/BlueTape.Services.DecisionEngine.Infrastructure.dll": {}}, "runtime": {"bin/placeholder/BlueTape.Services.DecisionEngine.Infrastructure.dll": {}}}, "BlueTape.Services.DecisionEngine.PolicyRulesFramework.Domain/1.0.0": {"type": "project", "framework": ".NETCoreApp,Version=v6.0", "dependencies": {"BlueTape.Services.DecisionEngine.Domain": "1.0.0", "BlueTape.Services.DecisionEngine.Infrastructure": "1.0.0", "BlueTape.Services.DecisionEngine.PolicyRulesFramework.Infrasrtucture": "1.0.0"}, "compile": {"bin/placeholder/BlueTape.Services.DecisionEngine.PolicyRulesFramework.Domain.dll": {}}, "runtime": {"bin/placeholder/BlueTape.Services.DecisionEngine.PolicyRulesFramework.Domain.dll": {}}}, "BlueTape.Services.DecisionEngine.PolicyRulesFramework.Infrasrtucture/1.0.0": {"type": "project", "framework": ".NETCoreApp,Version=v6.0", "compile": {"bin/placeholder/BlueTape.Services.DecisionEngine.PolicyRulesFramework.Infrasrtucture.dll": {}}, "runtime": {"bin/placeholder/BlueTape.Services.DecisionEngine.PolicyRulesFramework.Infrasrtucture.dll": {}}}, "BlueTape.Services.DecisionEngine.PolicyRulesFramework.PolicyRulesEngine/1.0.0": {"type": "project", "framework": ".NETCoreApp,Version=v6.0", "dependencies": {"BlueTape.Services.DecisionEngine.PolicyRulesFramework.Domain": "1.0.0", "BlueTape.Services.DecisionEngine.PolicyRulesFramework.Infrasrtucture": "1.0.0", "Microsoft.Extensions.Configuration.Abstractions": "8.0.0", "Microsoft.Extensions.DependencyInjection.Abstractions": "8.0.0", "Microsoft.Extensions.Options.ConfigurationExtensions": "8.0.0"}, "compile": {"bin/placeholder/BlueTape.Services.DecisionEngine.PolicyRulesFramework.PolicyRulesEngine.dll": {}}, "runtime": {"bin/placeholder/BlueTape.Services.DecisionEngine.PolicyRulesFramework.PolicyRulesEngine.dll": {}}}}}, "libraries": {"Amazon.Lambda.Core/2.2.0": {"sha512": "DHqKeD1CYocP0t1dJC/NaXfu+5k6AoqnQ1Hlu/J2mXpLpCyeJfY+tIqT5fpruDarUlU0NtdIH8zSkCjeinyb1A==", "type": "package", "path": "amazon.lambda.core/2.2.0", "files": [".nupkg.metadata", ".signature.p7s", "README.md", "amazon.lambda.core.2.2.0.nupkg.sha512", "amazon.lambda.core.nuspec", "lib/net6.0/Amazon.Lambda.Core.dll", "lib/net6.0/Amazon.Lambda.Core.xml", "lib/net8.0/Amazon.Lambda.Core.dll", "lib/net8.0/Amazon.Lambda.Core.xml", "lib/netstandard2.0/Amazon.Lambda.Core.dll", "lib/netstandard2.0/Amazon.Lambda.Core.xml"]}, "Amazon.Lambda.Serialization.SystemTextJson/2.4.0": {"sha512": "VwHKBvAD3ivAoAYjRNy/LsZaoFayKAWhVYOTYur5UQZzxYCC6itTwBH5skt3l8FCIjOAGnZljHNnayu17Y53jw==", "type": "package", "path": "amazon.lambda.serialization.systemtextjson/2.4.0", "files": [".nupkg.metadata", ".signature.p7s", "README.md", "amazon.lambda.serialization.systemtextjson.2.4.0.nupkg.sha512", "amazon.lambda.serialization.systemtextjson.nuspec", "lib/net6.0/Amazon.Lambda.Serialization.SystemTextJson.dll", "lib/net6.0/Amazon.Lambda.Serialization.SystemTextJson.xml", "lib/net8.0/Amazon.Lambda.Serialization.SystemTextJson.dll", "lib/net8.0/Amazon.Lambda.Serialization.SystemTextJson.xml", "lib/netcoreapp3.1/Amazon.Lambda.Serialization.SystemTextJson.dll", "lib/netcoreapp3.1/Amazon.Lambda.Serialization.SystemTextJson.xml"]}, "Amazon.Lambda.SQSEvents/2.1.0": {"sha512": "a1xaCIuXDleJ8iRE3vHWWhtPqUiURmoQUS68INMEQmtfHr4wTz837TQZONo4cyubv5sZj78DDrYessZEzgFRGQ==", "type": "package", "path": "amazon.lambda.sqsevents/2.1.0", "files": [".nupkg.metadata", ".signature.p7s", "amazon.lambda.sqsevents.2.1.0.nupkg.sha512", "amazon.lambda.sqsevents.nuspec", "lib/netcoreapp3.1/Amazon.Lambda.SQSEvents.dll", "lib/netcoreapp3.1/Amazon.Lambda.SQSEvents.xml", "lib/netstandard2.0/Amazon.Lambda.SQSEvents.dll", "lib/netstandard2.0/Amazon.Lambda.SQSEvents.xml"]}, "Autofac/6.4.0": {"sha512": "tkFxl6wAPuwVhrlN8wuNADnd+k2tv4ReP7ZZSL0vjfcN0RcfC9v25ogxK6b03HC7D4NwWjSLf1G/zTG8Bw43wQ==", "type": "package", "path": "autofac/6.4.0", "files": [".nupkg.metadata", ".signature.p7s", "README.md", "autofac.6.4.0.nupkg.sha512", "autofac.nuspec", "icon.png", "lib/net5.0/Autofac.dll", "lib/net5.0/Autofac.xml", "lib/net6.0/Autofac.dll", "lib/net6.0/Autofac.xml", "lib/netstandard2.0/Autofac.dll", "lib/netstandard2.0/Autofac.xml", "lib/netstandard2.1/Autofac.dll", "lib/netstandard2.1/Autofac.xml"]}, "Autofac.Configuration/6.0.0": {"sha512": "ziOpbr0S0ufYv+6LqM8GX9oR7rlP6KICVxZmCAML6PcncvZ9Y/m8tcpuPXLD6vDyfNvfCq1drYxNVzwBT0+gkA==", "type": "package", "path": "autofac.configuration/6.0.0", "files": [".nupkg.metadata", ".signature.p7s", "autofac.configuration.6.0.0.nupkg.sha512", "autofac.configuration.nuspec", "icon.png", "lib/netstandard2.0/Autofac.Configuration.dll", "lib/netstandard2.0/Autofac.Configuration.pdb", "lib/netstandard2.0/Autofac.Configuration.xml", "lib/netstandard2.1/Autofac.Configuration.dll", "lib/netstandard2.1/Autofac.Configuration.pdb", "lib/netstandard2.1/Autofac.Configuration.xml"]}, "Autofac.Extensions.DependencyInjection/8.0.0": {"sha512": "nGrXNpQX2FiZpIBydK9cxZnnoqP/cUd3k/53uRERYEqLtWzKtE15R6L+j5q5ax5Rv/+3wAIkOaPePkahfqrwjg==", "type": "package", "path": "autofac.extensions.dependencyinjection/8.0.0", "files": [".nupkg.metadata", ".signature.p7s", "autofac.extensions.dependencyinjection.8.0.0.nupkg.sha512", "autofac.extensions.dependencyinjection.nuspec", "icon.png", "lib/net5.0/Autofac.Extensions.DependencyInjection.dll", "lib/net5.0/Autofac.Extensions.DependencyInjection.xml", "lib/net6.0/Autofac.Extensions.DependencyInjection.dll", "lib/net6.0/Autofac.Extensions.DependencyInjection.xml", "lib/netstandard2.0/Autofac.Extensions.DependencyInjection.dll", "lib/netstandard2.0/Autofac.Extensions.DependencyInjection.xml", "lib/netstandard2.1/Autofac.Extensions.DependencyInjection.dll", "lib/netstandard2.1/Autofac.Extensions.DependencyInjection.xml"]}, "AutoMapper/12.0.1": {"sha512": "hvV62vl6Hp/WfQ24yzo3Co9+OPl8wH8hApwVtgWpiAynVJkUcs7xvehnSftawL8Pe8FrPffBRM3hwzLQqWDNjA==", "type": "package", "path": "automapper/12.0.1", "files": [".nupkg.metadata", ".signature.p7s", "README.md", "automapper.12.0.1.nupkg.sha512", "automapper.nuspec", "icon.png", "lib/netstandard2.1/AutoMapper.dll", "lib/netstandard2.1/AutoMapper.xml"]}, "AutoMapper.Extensions.Microsoft.DependencyInjection/12.0.1": {"sha512": "+g/K+Vpe3gGMKGzjslMOdqNlkikScDjWfVvmWTayrDHaG/n2pPmFBMa+jKX1r/h6BDGFdkyRjAuhFE3ykW+r1g==", "type": "package", "path": "automapper.extensions.microsoft.dependencyinjection/12.0.1", "files": [".nupkg.metadata", ".signature.p7s", "README.md", "automapper.extensions.microsoft.dependencyinjection.12.0.1.nupkg.sha512", "automapper.extensions.microsoft.dependencyinjection.nuspec", "icon.png", "lib/netstandard2.1/AutoMapper.Extensions.Microsoft.DependencyInjection.dll"]}, "AWSSDK.Core/3.7.302.15": {"sha512": "DgSlbR4JIthe+iYl4qVfz3Lkh59th/gNPYloxf9iU0pIx3hHYqJ0UJRg9mwBcmpeyBccRSwdNoo9yF9X3tb/rg==", "type": "package", "path": "awssdk.core/3.7.302.15", "hasTools": true, "files": [".nupkg.metadata", ".signature.p7s", "awssdk.core.3.7.302.15.nupkg.sha512", "awssdk.core.nuspec", "images/AWSLogo.png", "lib/net35/AWSSDK.Core.dll", "lib/net35/AWSSDK.Core.pdb", "lib/net35/AWSSDK.Core.xml", "lib/net45/AWSSDK.Core.dll", "lib/net45/AWSSDK.Core.pdb", "lib/net45/AWSSDK.Core.xml", "lib/net8.0/AWSSDK.Core.dll", "lib/net8.0/AWSSDK.Core.pdb", "lib/net8.0/AWSSDK.Core.xml", "lib/netcoreapp3.1/AWSSDK.Core.dll", "lib/netcoreapp3.1/AWSSDK.Core.pdb", "lib/netcoreapp3.1/AWSSDK.Core.xml", "lib/netstandard2.0/AWSSDK.Core.dll", "lib/netstandard2.0/AWSSDK.Core.pdb", "lib/netstandard2.0/AWSSDK.Core.xml", "tools/account-management.ps1"]}, "AWSSDK.Extensions.NETCore.Setup/3.7.300": {"sha512": "zMxAHFYSAWHsVV9Cn96nE+V40agRCjT0etF10f0d/nFMMb1z7lecVwNadq9JYyqlDj+jsVRH9ydk4Al4v/1+jg==", "type": "package", "path": "awssdk.extensions.netcore.setup/3.7.300", "files": [".nupkg.metadata", ".signature.p7s", "awssdk.extensions.netcore.setup.3.7.300.nupkg.sha512", "awssdk.extensions.netcore.setup.nuspec", "images/AWSLogo.png", "lib/net8.0/AWSSDK.Extensions.NETCore.Setup.dll", "lib/net8.0/AWSSDK.Extensions.NETCore.Setup.pdb", "lib/netcoreapp3.1/AWSSDK.Extensions.NETCore.Setup.dll", "lib/netcoreapp3.1/AWSSDK.Extensions.NETCore.Setup.pdb", "lib/netstandard2.0/AWSSDK.Extensions.NETCore.Setup.dll", "lib/netstandard2.0/AWSSDK.Extensions.NETCore.Setup.pdb"]}, "AWSSDK.KeyManagementService/3.7.300.54": {"sha512": "na8/EsXk5Anu+BL6SbyXFwZBEk9FN70L0LleiDAz7N1lttNEMGzp6Sbu6SPmkzKvMfggLZI82w8eFWKxGkMIsw==", "type": "package", "path": "awssdk.keymanagementservice/3.7.300.54", "hasTools": true, "files": [".nupkg.metadata", ".signature.p7s", "analyzers/dotnet/cs/AWSSDK.KeyManagementService.CodeAnalysis.dll", "analyzers/dotnet/cs/SharedAnalysisCode.dll", "awssdk.keymanagementservice.3.7.300.54.nupkg.sha512", "awssdk.keymanagementservice.nuspec", "images/AWSLogo.png", "lib/net35/AWSSDK.KeyManagementService.dll", "lib/net35/AWSSDK.KeyManagementService.pdb", "lib/net35/AWSSDK.KeyManagementService.xml", "lib/net45/AWSSDK.KeyManagementService.dll", "lib/net45/AWSSDK.KeyManagementService.pdb", "lib/net45/AWSSDK.KeyManagementService.xml", "lib/net8.0/AWSSDK.KeyManagementService.dll", "lib/net8.0/AWSSDK.KeyManagementService.pdb", "lib/net8.0/AWSSDK.KeyManagementService.xml", "lib/netcoreapp3.1/AWSSDK.KeyManagementService.dll", "lib/netcoreapp3.1/AWSSDK.KeyManagementService.pdb", "lib/netcoreapp3.1/AWSSDK.KeyManagementService.xml", "lib/netstandard2.0/AWSSDK.KeyManagementService.dll", "lib/netstandard2.0/AWSSDK.KeyManagementService.pdb", "lib/netstandard2.0/AWSSDK.KeyManagementService.xml", "tools/install.ps1", "tools/uninstall.ps1"]}, "AWSSDK.S3/3.7.10": {"sha512": "vsNA29rhuwfILkHhPvpm2DHQaqn2TndpwStmhQMnqTRGB+mL1IG+xAdicEIrWrdOsAP4SD9JONYe9CJMcXcnUg==", "type": "package", "path": "awssdk.s3/3.7.10", "hasTools": true, "files": [".nupkg.metadata", ".signature.p7s", "analyzers/dotnet/cs/AWSSDK.S3.CodeAnalysis.dll", "awssdk.s3.3.7.10.nupkg.sha512", "awssdk.s3.nuspec", "lib/net35/AWSSDK.S3.dll", "lib/net35/AWSSDK.S3.pdb", "lib/net35/AWSSDK.S3.xml", "lib/net45/AWSSDK.S3.dll", "lib/net45/AWSSDK.S3.pdb", "lib/net45/AWSSDK.S3.xml", "lib/netcoreapp3.1/AWSSDK.S3.dll", "lib/netcoreapp3.1/AWSSDK.S3.pdb", "lib/netcoreapp3.1/AWSSDK.S3.xml", "lib/netstandard2.0/AWSSDK.S3.dll", "lib/netstandard2.0/AWSSDK.S3.pdb", "lib/netstandard2.0/AWSSDK.S3.xml", "tools/install.ps1", "tools/uninstall.ps1"]}, "AWSSDK.SecretsManager/3.7.302.29": {"sha512": "MC0Y9Pyy2+WO1e7sxaRYiLVr313SIKW6fi30ZcKOunlyccvoz6o6syQ5OF+obOFXg0R9+8OyJGcK8Fjzx3Pmkw==", "type": "package", "path": "awssdk.secretsmanager/3.7.302.29", "hasTools": true, "files": [".nupkg.metadata", ".signature.p7s", "analyzers/dotnet/cs/AWSSDK.SecretsManager.CodeAnalysis.dll", "analyzers/dotnet/cs/SharedAnalysisCode.dll", "awssdk.secretsmanager.3.7.302.29.nupkg.sha512", "awssdk.secretsmanager.nuspec", "images/AWSLogo.png", "lib/net35/AWSSDK.SecretsManager.dll", "lib/net35/AWSSDK.SecretsManager.pdb", "lib/net35/AWSSDK.SecretsManager.xml", "lib/net45/AWSSDK.SecretsManager.dll", "lib/net45/AWSSDK.SecretsManager.pdb", "lib/net45/AWSSDK.SecretsManager.xml", "lib/net8.0/AWSSDK.SecretsManager.dll", "lib/net8.0/AWSSDK.SecretsManager.pdb", "lib/net8.0/AWSSDK.SecretsManager.xml", "lib/netcoreapp3.1/AWSSDK.SecretsManager.dll", "lib/netcoreapp3.1/AWSSDK.SecretsManager.pdb", "lib/netcoreapp3.1/AWSSDK.SecretsManager.xml", "lib/netstandard2.0/AWSSDK.SecretsManager.dll", "lib/netstandard2.0/AWSSDK.SecretsManager.pdb", "lib/netstandard2.0/AWSSDK.SecretsManager.xml", "tools/install.ps1", "tools/uninstall.ps1"]}, "AWSSDK.SecretsManager.Caching/1.0.6": {"sha512": "N982+rohMJ/w8ywyN6hgnSgw2cpqj4MJDizz+b93gudQSEzR3lCTzHGN3AQl+ngMH4yTG+DfKgmL7QajvvYyKQ==", "type": "package", "path": "awssdk.secretsmanager.caching/1.0.6", "files": [".nupkg.metadata", ".signature.p7s", "awssdk.secretsmanager.caching.1.0.6.nupkg.sha512", "awssdk.secretsmanager.caching.nuspec", "lib/netstandard2.0/Amazon.SecretsManager.Extensions.Caching.dll", "lib/netstandard2.0/Amazon.SecretsManager.Extensions.Caching.xml"]}, "AWSSDK.SecurityToken/3.7.300.47": {"sha512": "wBcBC0axwf+qQTVMPUblDxIpMtbp04z9ElYeW6BSjeAO7spvJXhEz7yhikdTn8YaeWW+K6U9h6AIDQpJlhH2vQ==", "type": "package", "path": "awssdk.securitytoken/3.7.300.47", "hasTools": true, "files": [".nupkg.metadata", ".signature.p7s", "analyzers/dotnet/cs/AWSSDK.SecurityToken.CodeAnalysis.dll", "analyzers/dotnet/cs/SharedAnalysisCode.dll", "awssdk.securitytoken.3.7.300.47.nupkg.sha512", "awssdk.securitytoken.nuspec", "images/AWSLogo.png", "lib/net35/AWSSDK.SecurityToken.dll", "lib/net35/AWSSDK.SecurityToken.pdb", "lib/net35/AWSSDK.SecurityToken.xml", "lib/net45/AWSSDK.SecurityToken.dll", "lib/net45/AWSSDK.SecurityToken.pdb", "lib/net45/AWSSDK.SecurityToken.xml", "lib/net8.0/AWSSDK.SecurityToken.dll", "lib/net8.0/AWSSDK.SecurityToken.pdb", "lib/net8.0/AWSSDK.SecurityToken.xml", "lib/netcoreapp3.1/AWSSDK.SecurityToken.dll", "lib/netcoreapp3.1/AWSSDK.SecurityToken.pdb", "lib/netcoreapp3.1/AWSSDK.SecurityToken.xml", "lib/netstandard2.0/AWSSDK.SecurityToken.dll", "lib/netstandard2.0/AWSSDK.SecurityToken.pdb", "lib/netstandard2.0/AWSSDK.SecurityToken.xml", "tools/install.ps1", "tools/uninstall.ps1"]}, "AWSSDK.SimpleNotificationService/3.7.200.52": {"sha512": "RqBqzwh9dGCxmfaUGvdHyYqBzd4RyCGUdd8w8zo4nxCH/3acKDbQriiFRgYvSOkOunIKXNMR45aBbQXeReuVMw==", "type": "package", "path": "awssdk.simplenotificationservice/3.7.200.52", "hasTools": true, "files": [".nupkg.metadata", ".signature.p7s", "analyzers/dotnet/cs/AWSSDK.SimpleNotificationService.CodeAnalysis.dll", "analyzers/dotnet/cs/SharedAnalysisCode.dll", "awssdk.simplenotificationservice.3.7.200.52.nupkg.sha512", "awssdk.simplenotificationservice.nuspec", "images/AWSLogo.png", "lib/net35/AWSSDK.SimpleNotificationService.dll", "lib/net35/AWSSDK.SimpleNotificationService.pdb", "lib/net35/AWSSDK.SimpleNotificationService.xml", "lib/net45/AWSSDK.SimpleNotificationService.dll", "lib/net45/AWSSDK.SimpleNotificationService.pdb", "lib/net45/AWSSDK.SimpleNotificationService.xml", "lib/netcoreapp3.1/AWSSDK.SimpleNotificationService.dll", "lib/netcoreapp3.1/AWSSDK.SimpleNotificationService.pdb", "lib/netcoreapp3.1/AWSSDK.SimpleNotificationService.xml", "lib/netstandard2.0/AWSSDK.SimpleNotificationService.dll", "lib/netstandard2.0/AWSSDK.SimpleNotificationService.pdb", "lib/netstandard2.0/AWSSDK.SimpleNotificationService.xml", "tools/install.ps1", "tools/uninstall.ps1"]}, "AWSSDK.SQS/3.7.2.121": {"sha512": "l8cp5Ya6UbiMM58gKY0ObavH87YtsCCmUfOpaVbShfoObWRPg2QJgmysQV4PgUGdzEXqozqYUADZqQGRW9NQTA==", "type": "package", "path": "awssdk.sqs/3.7.2.121", "hasTools": true, "files": [".nupkg.metadata", ".signature.p7s", "analyzers/dotnet/cs/AWSSDK.SQS.CodeAnalysis.dll", "awssdk.sqs.3.7.2.121.nupkg.sha512", "awssdk.sqs.nuspec", "lib/net35/AWSSDK.SQS.dll", "lib/net35/AWSSDK.SQS.pdb", "lib/net35/AWSSDK.SQS.xml", "lib/net45/AWSSDK.SQS.dll", "lib/net45/AWSSDK.SQS.pdb", "lib/net45/AWSSDK.SQS.xml", "lib/netcoreapp3.1/AWSSDK.SQS.dll", "lib/netcoreapp3.1/AWSSDK.SQS.pdb", "lib/netcoreapp3.1/AWSSDK.SQS.xml", "lib/netstandard2.0/AWSSDK.SQS.dll", "lib/netstandard2.0/AWSSDK.SQS.pdb", "lib/netstandard2.0/AWSSDK.SQS.xml", "tools/install.ps1", "tools/uninstall.ps1"]}, "Azure.Core/1.36.0": {"sha512": "vwqFZdHS4dzPlI7FFRkPx9ctA+aGGeRev3gnzG8lntWvKMmBhAmulABi1O9CEvS3/jzYt7yA+0pqVdxkpAd7dQ==", "type": "package", "path": "azure.core/1.36.0", "files": [".nupkg.metadata", ".signature.p7s", "CHANGELOG.md", "README.md", "azure.core.1.36.0.nupkg.sha512", "azure.core.nuspec", "azureicon.png", "lib/net461/Azure.Core.dll", "lib/net461/Azure.Core.xml", "lib/net472/Azure.Core.dll", "lib/net472/Azure.Core.xml", "lib/net5.0/Azure.Core.dll", "lib/net5.0/Azure.Core.xml", "lib/net6.0/Azure.Core.dll", "lib/net6.0/Azure.Core.xml", "lib/netcoreapp2.1/Azure.Core.dll", "lib/netcoreapp2.1/Azure.Core.xml", "lib/netstandard2.0/Azure.Core.dll", "lib/netstandard2.0/Azure.Core.xml"]}, "Azure.Core.Amqp/1.3.0": {"sha512": "6GG4gyFkAuHtpBVkvj0wE5+lCM+ttsZlIWAipBkI+jlCUlTgrTiNUROBFnb8xuKoymVDw9Tf1W8RoKqgbd71lg==", "type": "package", "path": "azure.core.amqp/1.3.0", "files": [".nupkg.metadata", ".signature.p7s", "CHANGELOG.md", "README.md", "azure.core.amqp.1.3.0.nupkg.sha512", "azure.core.amqp.nuspec", "azureicon.png", "lib/netstandard2.0/Azure.Core.Amqp.dll", "lib/netstandard2.0/Azure.Core.Amqp.xml"]}, "Azure.Identity/1.10.4": {"sha512": "hSvisZy9sld0Gik1X94od3+rRXCx+AKgi+iLH6fFdlnRZRePn7RtrqUGSsORiH2h8H2sc4NLTrnuUte1WL+QuQ==", "type": "package", "path": "azure.identity/1.10.4", "files": [".nupkg.metadata", ".signature.p7s", "CHANGELOG.md", "README.md", "azure.identity.1.10.4.nupkg.sha512", "azure.identity.nuspec", "azureicon.png", "lib/netstandard2.0/Azure.Identity.dll", "lib/netstandard2.0/Azure.Identity.xml"]}, "Azure.Messaging.ServiceBus/7.17.1": {"sha512": "RvpLKmp2ur7hfm7NqiKPY2wIU7O4+yajYm3w7etnDsNj6sMlLCyNyCNMgVGeudQ4nOrk0YtoHJ1SbX2nJpCsUw==", "type": "package", "path": "azure.messaging.servicebus/7.17.1", "files": [".nupkg.metadata", ".signature.p7s", "CHANGELOG.md", "README.md", "azure.messaging.servicebus.7.17.1.nupkg.sha512", "azure.messaging.servicebus.nuspec", "azureicon.png", "lib/netstandard2.0/Azure.Messaging.ServiceBus.dll", "lib/netstandard2.0/Azure.Messaging.ServiceBus.xml"]}, "Azure.Security.KeyVault.Keys/4.5.0": {"sha512": "HnW9kjhRzQkfJE4ISl63cWVa6qLe3FM1MxoxNvNFtDUeT5iMBEg0YgGbcx2YgEiYaazIvSgZyjBr4L3Ur3+m7g==", "type": "package", "path": "azure.security.keyvault.keys/4.5.0", "files": [".nupkg.metadata", ".signature.p7s", "CHANGELOG.md", "README.md", "azure.security.keyvault.keys.4.5.0.nupkg.sha512", "azure.security.keyvault.keys.nuspec", "azureicon.png", "lib/netstandard2.0/Azure.Security.KeyVault.Keys.dll", "lib/netstandard2.0/Azure.Security.KeyVault.Keys.xml"]}, "Azure.Security.KeyVault.Secrets/4.5.0": {"sha512": "ztr26Ai4K7AZGuw68/ffLDn+2G3WL0myjC7nY1RYkxPMnsplTPEH+Ke4RGxvSkg4kC7bJ9NwdlqpEwfDX0qhdw==", "type": "package", "path": "azure.security.keyvault.secrets/4.5.0", "files": [".nupkg.metadata", ".signature.p7s", "CHANGELOG.md", "README.md", "azure.security.keyvault.secrets.4.5.0.nupkg.sha512", "azure.security.keyvault.secrets.nuspec", "azureicon.png", "lib/netstandard2.0/Azure.Security.KeyVault.Secrets.dll", "lib/netstandard2.0/Azure.Security.KeyVault.Secrets.xml"]}, "BlueTape.AWSMessaging/2.0.5": {"sha512": "go55ZNtn4RT65t+mXGfYe/wCswuMUGCPdsQS6e4pu6HW2FMShBZ6YkurvzqlJt+B63u8mGohHf/IizSjxyca0A==", "type": "package", "path": "bluetape.awsmessaging/2.0.5", "files": [".nupkg.metadata", "bluetape.awsmessaging.2.0.5.nupkg.sha512", "bluetape.awsmessaging.nuspec", "lib/net6.0/BlueTape.AWSMessaging.dll"]}, "BlueTape.AWSS3/1.1.6": {"sha512": "kzpVau1mZ7zVn7X6dsxDQ9KIK256xK4O8yeTgDgPFjNJGpuZpW8ntAllk2lXSxhW9tXtoG7fiRO32TkWIeNXaw==", "type": "package", "path": "bluetape.awss3/1.1.6", "files": [".nupkg.metadata", "bluetape.awss3.1.1.6.nupkg.sha512", "bluetape.awss3.nuspec", "lib/net6.0/BlueTape.AWSS3.dll"]}, "BlueTape.AzureKeyVault/1.0.3": {"sha512": "On4RZI41X71GSmrYnv3p1eN6ullTWF9L8SsZ2NC/tQsQ/Upe0X1kcJE7rgvrT6G1RToKyY2n+OAGwfYZH8uOlQ==", "type": "package", "path": "bluetape.azurekeyvault/1.0.3", "files": [".nupkg.metadata", "bluetape.azurekeyvault.1.0.3.nupkg.sha512", "bluetape.azurekeyvault.nuspec", "lib/net6.0/BlueTape.AzureKeyVault.dll"]}, "BlueTape.CashFlow.Domain/1.0.2": {"sha512": "Q+ZZvORniOmIs/p90EyQVbBzzfCeOSnEKkwfpc8nTL3YPXMZbKd1qKnLUgJBu9BqIiBqI6z4lrz96yKcn9UcGA==", "type": "package", "path": "bluetape.cashflow.domain/1.0.2", "files": [".nupkg.metadata", "bluetape.cashflow.domain.1.0.2.nupkg.sha512", "bluetape.cashflow.domain.nuspec", "lib/net6.0/BlueTape.CashFlow.Domain.dll"]}, "BlueTape.Common.Extensions/1.1.0": {"sha512": "P4mQRSipiN2qp+5ETfnBdZz564U1AWder867Zj/4SV+mZTS+SxqML0H+sW2W7PADp9iUgdngoNzqYsj+UxGheA==", "type": "package", "path": "bluetape.common.extensions/1.1.0", "files": [".nupkg.metadata", "bluetape.common.extensions.1.1.0.nupkg.sha512", "bluetape.common.extensions.nuspec", "lib/net6.0/BlueTape.Common.Extensions.dll"]}, "BlueTape.Common.Validation/1.0.4": {"sha512": "IA6/J881MMavf8qQ28vuZTvcaCU0gOAhZNXZxvtvs9BgXUHlWUx0LWfD9pwKTPNTiqqUub4XFeC34pXgO4bA0w==", "type": "package", "path": "bluetape.common.validation/1.0.4", "files": [".nupkg.metadata", "bluetape.common.validation.1.0.4.nupkg.sha512", "bluetape.common.validation.nuspec", "lib/net6.0/BlueTape.Common.Validation.dll"]}, "BlueTape.CompanyService/1.2.42": {"sha512": "91UlfOAcsmJI94TwntTVxvd2WeTSxfdNa88IH49agclEy10nca8PI6gyQ7XGCcDj2QvgPh15KJnZnYub1uWjqw==", "type": "package", "path": "bluetape.companyservice/1.2.42", "files": [".nupkg.metadata", "bluetape.companyservice.1.2.42.nupkg.sha512", "bluetape.companyservice.nuspec", "lib/net6.0/BlueTape.CompanyService.dll"]}, "BlueTape.CompanyService.Common/1.1.21": {"sha512": "1znqFDa5Gyglpcp3Eq3vh6r88ahgWhhMg6Duja1guXhjdWiRDDbIN8wPYoN5+i/IeW/aqLlyTr7pxapJ28qxwg==", "type": "package", "path": "bluetape.companyservice.common/1.1.21", "files": [".nupkg.metadata", "bluetape.companyservice.common.1.1.21.nupkg.sha512", "bluetape.companyservice.common.nuspec", "lib/net6.0/BlueTape.CompanyService.Common.dll"]}, "BlueTape.EmailSender/3.0.7": {"sha512": "/qaKlPPGOu/EvdKdGGKvq9xKpRq/37hrqzX/TgtQhUkWmEydufTdilB93R7lo7owIpkNkadbsbgPjUHlA1HzXA==", "type": "package", "path": "bluetape.emailsender/3.0.7", "files": [".nupkg.metadata", "bluetape.emailsender.3.0.7.nupkg.sha512", "bluetape.emailsender.nuspec", "lib/net6.0/BlueTape.EmailSender.dll"]}, "BlueTape.Integrations.Experian/1.0.2": {"sha512": "2PME7FF8VGK03HmMf4SMF83zvvQtkAR0u+dPOL9MjWcPah/IzASmPv2S9ATWx9xWxnLrnPRzlpl5I5xQMZ747w==", "type": "package", "path": "bluetape.integrations.experian/1.0.2", "files": [".nupkg.metadata", "bluetape.integrations.experian.1.0.2.nupkg.sha512", "bluetape.integrations.experian.nuspec", "lib/net6.0/BlueTape.Integrations.Experian.dll"]}, "BlueTape.Integrations.Giact/1.0.3": {"sha512": "kZLAmSyJL9yJice31nhT204VMxXtblkuWJGebNgX3mQBKh6FMPeQmQHOFRAiM7VqzYuOoXyKJ/qZ9HTBjKen4w==", "type": "package", "path": "bluetape.integrations.giact/1.0.3", "files": [".nupkg.metadata", "bluetape.integrations.giact.1.0.3.nupkg.sha512", "bluetape.integrations.giact.nuspec", "lib/net6.0/BlueTape.Integrations.Giact.dll"]}, "BlueTape.Integrations.LexisNexis/1.0.6": {"sha512": "lGljvwBB6ynJs6rkikmpVcMYK4lSd8ylE5/Me1ZCHLjL3HwRGSJsbdECY7xLx8GGgELnzhDGwHZwy77DW34qzA==", "type": "package", "path": "bluetape.integrations.lexisnexis/1.0.6", "files": [".nupkg.metadata", "bluetape.integrations.lexisnexis.1.0.6.nupkg.sha512", "bluetape.integrations.lexisnexis.nuspec", "lib/net6.0/BlueTape.Integrations.LexisNexis.dll"]}, "BlueTape.Integrations.Plaid/1.0.7": {"sha512": "qgMFZVPlw6FKMa8lpT5WBkT6ESyEwiAPlw0Zd8FKyn4O6zl63iTGdefA/OnWppjeWHGVgcYAufjQRkOnRa2rqg==", "type": "package", "path": "bluetape.integrations.plaid/1.0.7", "files": [".nupkg.metadata", "bluetape.integrations.plaid.1.0.7.nupkg.sha512", "bluetape.integrations.plaid.nuspec", "lib/net6.0/BlueTape.Integrations.Plaid.dll"]}, "BlueTape.Integrations.Plaid.Infrastructure/1.0.0": {"sha512": "crHwCg+1WNG5kQAfNeoVYzzkcwkuxZgU/Bc4VLZs/WNRMyCLqYQamTQ4QThJUUlciLliiQCTdPsEIoDPSi+b7Q==", "type": "package", "path": "bluetape.integrations.plaid.infrastructure/1.0.0", "files": [".nupkg.metadata", "bluetape.integrations.plaid.infrastructure.1.0.0.nupkg.sha512", "bluetape.integrations.plaid.infrastructure.nuspec", "lib/net6.0/BlueTape.Integrations.Plaid.Infrastructure.dll"]}, "BlueTape.InvoiceService/1.0.38": {"sha512": "LMz6T/ExtvttPTC8r12doGH8NuZUbwQBnNUvfkXL/WKS6NBLBMLAmxoTg1Zikc2uZYCP8LpNhv5G7O+9GCrH1w==", "type": "package", "path": "bluetape.invoiceservice/1.0.38", "files": [".nupkg.metadata", "bluetape.invoiceservice.1.0.38.nupkg.sha512", "bluetape.invoiceservice.nuspec", "lib/net6.0/BlueTape.InvoiceService.dll"]}, "BlueTape.InvoiceService.Common/1.1.2": {"sha512": "m240Z+M2IP6fQsD6qqC2Ee1vqhwqSVLLAC2l9nrnn/t9GQNj2DpfRAL8/+5NZ6kWNb7K0BcKFd3w5o7UCKUm2Q==", "type": "package", "path": "bluetape.invoiceservice.common/1.1.2", "files": [".nupkg.metadata", "bluetape.invoiceservice.common.1.1.2.nupkg.sha512", "bluetape.invoiceservice.common.nuspec", "lib/net6.0/BlueTape.InvoiceService.Common.dll"]}, "BlueTape.LambdaBase/1.1.1": {"sha512": "HdiheZo1vOpPku0z3kymQ9LWsuwC5WdSJf4Jx1JaUx8+u7eRPYLNk6G8AR2MysknilOs8P7YfguJSruhF4zyFQ==", "type": "package", "path": "bluetape.lambdabase/1.1.1", "files": [".nupkg.metadata", "bluetape.lambdabase.1.1.1.nupkg.sha512", "bluetape.lambdabase.nuspec", "lib/net6.0/BlueTape.LambdaBase.dll"]}, "BlueTape.LS/1.1.69": {"sha512": "GQ2nM00xOvuR2/7mjRFH1KtqK9IEdI4tOvvqFN91plJu5jkciIKO5DUtdASwmKmqcDPDIyqezt+mc9oi22Jcvg==", "type": "package", "path": "bluetape.ls/1.1.69", "files": [".nupkg.metadata", "bluetape.ls.1.1.69.nupkg.sha512", "bluetape.ls.nuspec", "lib/net6.0/BlueTape.LS.dll"]}, "BlueTape.LS.Domain/1.1.33": {"sha512": "rHfT70TOeUuhVeOlPlrY3/iWIu2lpmGd5jiOpaSz7UTDF/3f0NG3aVR64YeIRbzZ0VEHybVObK5JLUPnw5nezg==", "type": "package", "path": "bluetape.ls.domain/1.1.33", "files": [".nupkg.metadata", "bluetape.ls.domain.1.1.33.nupkg.sha512", "bluetape.ls.domain.nuspec", "lib/net6.0/BlueTape.LS.Domain.dll"]}, "BlueTape.MongoDB/1.1.31": {"sha512": "Ujgbcc+EjJTAJNB0/jtHDReKWuE0NIoNRX0JQMLxcxJCDYimwuXR+jhTt7dLt3Wh0EoE6xp5jHjL2uiI92jXUg==", "type": "package", "path": "bluetape.mongodb/1.1.31", "files": [".nupkg.metadata", "bluetape.mongodb.1.1.31.nupkg.sha512", "bluetape.mongodb.nuspec", "lib/net6.0/BlueTape.MongoDB.dll"]}, "BlueTape.OBS/1.6.69": {"sha512": "x3TJInMARVQQf1jQ7bj3LCTpJQwNjplwg8FDFJXNeNcbG72RXxKxti78wDN9z0OdOm6DzAQMRHBqrkwESRgKXQ==", "type": "package", "path": "bluetape.obs/1.6.69", "files": [".nupkg.metadata", "bluetape.obs.1.6.69.nupkg.sha512", "bluetape.obs.nuspec", "lib/net6.0/BlueTape.OBS.dll"]}, "BlueTape.PaymentService/1.0.3": {"sha512": "f5J18uyvcbMQr8nB0KfOsrZryDp7cZ5Z4hXAgw8sbSWZDx+9BQXHhUwhzPFRLfpJBaMWq5h/SR9FGyBD2CObgA==", "type": "package", "path": "bluetape.paymentservice/1.0.3", "files": [".nupkg.metadata", "bluetape.paymentservice.1.0.3.nupkg.sha512", "bluetape.paymentservice.nuspec", "lib/net6.0/BlueTape.PaymentService.dll"]}, "BlueTape.ServiceBusMessaging/1.0.8": {"sha512": "lFXJaFhsHnzBJT5uciOpXT0OXXk4az+t178Je317Kito6hXqgiQbUf7hZQ/TuCc5DQ4zTiMafbgeZwQWi/2M5Q==", "type": "package", "path": "bluetape.servicebusmessaging/1.0.8", "files": [".nupkg.metadata", "bluetape.servicebusmessaging.1.0.8.nupkg.sha512", "bluetape.servicebusmessaging.nuspec", "lib/net6.0/BlueTape.ServiceBusMessaging.dll"]}, "BlueTape.SNS/1.0.2": {"sha512": "yyQoT3RXeiKRFiv/RK6gPVXj4rAR7OXrAUDlIU/f8EztzUgiLsNzazfmzCJRSgLxfmKSJoBkdfqcyBla3Rxkwg==", "type": "package", "path": "bluetape.sns/1.0.2", "files": [".nupkg.metadata", "bluetape.sns.1.0.2.nupkg.sha512", "bluetape.sns.nuspec", "lib/net6.0/BlueTape.SNS.dll"]}, "BlueTape.Utilities/1.4.6": {"sha512": "vD/c4SBIubmwdz8okn2XBUfUMc5kjMOaQJDckOgFSFyhhPjz0JD9sGkHNXq7Py0x8DXujuWSLtVVh77FXS3DMA==", "type": "package", "path": "bluetape.utilities/1.4.6", "files": [".nupkg.metadata", "bluetape.utilities.1.4.6.nupkg.sha512", "bluetape.utilities.nuspec", "lib/net6.0/BlueTape.Utilities.dll"]}, "DateOnlyTimeOnly.AspNet/2.1.1": {"sha512": "7HlGV6sm0efRMquxnZaoqV3KybUMOs1IaJ4TaX85v7IZF01h1YV6Zscos9g4qQfrGYfvqLjbpe3Q54cmMBeISQ==", "type": "package", "path": "dateonlytimeonly.aspnet/2.1.1", "files": [".nupkg.metadata", ".signature.p7s", "README.md", "dateonlytimeonly.aspnet.2.1.1.nupkg.sha512", "dateonlytimeonly.aspnet.nuspec", "lib/net6.0/DateOnlyTimeOnly.AspNet.dll", "lib/net7.0/DateOnlyTimeOnly.AspNet.dll"]}, "DnsClient/1.6.1": {"sha512": "4H/f2uYJOZ+YObZjpY9ABrKZI+JNw3uizp6oMzTXwDw6F+2qIPhpRl/1t68O/6e98+vqNiYGu+lswmwdYUy3gg==", "type": "package", "path": "dnsclient/1.6.1", "files": [".nupkg.metadata", ".signature.p7s", "dnsclient.1.6.1.nupkg.sha512", "dnsclient.nuspec", "icon.png", "lib/net45/DnsClient.dll", "lib/net45/DnsClient.xml", "lib/net471/DnsClient.dll", "lib/net471/DnsClient.xml", "lib/net5.0/DnsClient.dll", "lib/net5.0/DnsClient.xml", "lib/netstandard1.3/DnsClient.dll", "lib/netstandard1.3/DnsClient.xml", "lib/netstandard2.0/DnsClient.dll", "lib/netstandard2.0/DnsClient.xml", "lib/netstandard2.1/DnsClient.dll", "lib/netstandard2.1/DnsClient.xml"]}, "Elastic.CommonSchema/1.5.3": {"sha512": "JgwhfThYY/s17asUiBCUVqnZtDdGTWO/2hTPG01QDfw2+T6kfwskrj5eh6XpBZsOh6r9SpBL95vSsU+q44i7Zg==", "type": "package", "path": "elastic.commonschema/1.5.3", "files": [".nupkg.metadata", ".signature.p7s", "elastic.commonschema.1.5.3.nupkg.sha512", "elastic.commonschema.nuspec", "lib/net461/Elastic.CommonSchema.dll", "lib/net461/Elastic.CommonSchema.pdb", "lib/netstandard2.0/Elastic.CommonSchema.dll", "lib/netstandard2.0/Elastic.CommonSchema.pdb", "lib/netstandard2.1/Elastic.CommonSchema.dll", "lib/netstandard2.1/Elastic.CommonSchema.pdb", "license.txt", "nuget-icon.png"]}, "Elastic.CommonSchema.Serilog/1.5.3": {"sha512": "bp2qHOWmN15fTKUecFMt7oCra68I1cm3yAEmwPcLuz4v2pQ5YxC8nVtyCTSSibquUS/ZPH5JInjlmuywV3UoyQ==", "type": "package", "path": "elastic.commonschema.serilog/1.5.3", "files": [".nupkg.metadata", ".signature.p7s", "elastic.commonschema.serilog.1.5.3.nupkg.sha512", "elastic.commonschema.serilog.nuspec", "lib/net461/Elastic.CommonSchema.Serilog.dll", "lib/net461/Elastic.CommonSchema.Serilog.pdb", "lib/netstandard2.0/Elastic.CommonSchema.Serilog.dll", "lib/netstandard2.0/Elastic.CommonSchema.Serilog.pdb", "lib/netstandard2.1/Elastic.CommonSchema.Serilog.dll", "lib/netstandard2.1/Elastic.CommonSchema.Serilog.pdb", "license.txt", "nuget-icon.png"]}, "F23.StringSimilarity/5.1.0": {"sha512": "RcFtnxG3Pw/wBxQFZv65Wb9VZRLyjorw4rLPW0yqPXasINT3zBKz8cBzKRlESvXve4qrRyo/XSfYfYgmKs/lkg==", "type": "package", "path": "f23.stringsimilarity/5.1.0", "files": [".nupkg.metadata", ".signature.p7s", "f23.stringsimilarity.5.1.0.nupkg.sha512", "f23.stringsimilarity.nuspec", "lib/netstandard2.0/F23.StringSimilarity.dll", "lib/netstandard2.0/F23.StringSimilarity.xml", "logo.png"]}, "FluentValidation/11.0.2": {"sha512": "ik/xogKqBmZWIURMXNEbugJlKka6iV9a4qckmKDp7sHSwIfgkZ/ewzCuAeUTkFdVZujfDW6mCLMjrrR6GG4xLQ==", "type": "package", "path": "fluentvalidation/11.0.2", "files": [".nupkg.metadata", ".signature.p7s", "README.md", "fluent-validation-icon.png", "fluentvalidation.11.0.2.nupkg.sha512", "fluentvalidation.nuspec", "lib/net5.0/FluentValidation.dll", "lib/net5.0/FluentValidation.xml", "lib/net6.0/FluentValidation.dll", "lib/net6.0/FluentValidation.xml", "lib/netstandard2.0/FluentValidation.dll", "lib/netstandard2.0/FluentValidation.xml", "lib/netstandard2.1/FluentValidation.dll", "lib/netstandard2.1/FluentValidation.xml"]}, "libphonenumber-csharp/8.13.24": {"sha512": "Pq/TqzVAVq6N8LeE5Qhg3UDzPV9Hr2UShRUp5GbaANPBU11MA1LTUOmVnMwYhxcZAkGsCkVBkcek3WhOj+hnIg==", "type": "package", "path": "libphonenumber-csharp/8.13.24", "files": [".nupkg.metadata", ".signature.p7s", "README.md", "icon.png", "lib/net6.0/PhoneNumbers.dll", "lib/net6.0/PhoneNumbers.xml", "lib/net7.0/PhoneNumbers.dll", "lib/net7.0/PhoneNumbers.xml", "lib/netstandard2.0/PhoneNumbers.dll", "lib/netstandard2.0/PhoneNumbers.xml", "libphonenumber-csharp.8.13.24.nupkg.sha512", "libphonenumber-csharp.nuspec"]}, "Macross.Json.Extensions/3.0.0": {"sha512": "AkNshs6dopj8FXsmkkJxvLivN2SyDJQDbjcds5lo9+Y6L4zpcoXdmzXQ3VVN+AIWQr0CTD5A7vkuHGAr2aypZg==", "type": "package", "path": "macross.json.extensions/3.0.0", "files": [".nupkg.metadata", ".signature.p7s", "lib/net6.0/Macross.Json.Extensions.dll", "lib/net6.0/Macross.Json.Extensions.xml", "lib/netstandard2.0/Macross.Json.Extensions.dll", "lib/netstandard2.0/Macross.Json.Extensions.xml", "lib/netstandard2.1/Macross.Json.Extensions.dll", "lib/netstandard2.1/Macross.Json.Extensions.xml", "macross.json.extensions.3.0.0.nupkg.sha512", "macross.json.extensions.nuspec"]}, "Microsoft.AspNetCore.Http.Abstractions/2.2.0": {"sha512": "Nxs7Z1q3f1STfLYKJSVXCs1iBl+Ya6E8o4Oy1bCxJ/rNI44E/0f6tbsrVqAWfB7jlnJfyaAtIalBVxPKUPQb4Q==", "type": "package", "path": "microsoft.aspnetcore.http.abstractions/2.2.0", "files": [".nupkg.metadata", ".signature.p7s", "lib/netstandard2.0/Microsoft.AspNetCore.Http.Abstractions.dll", "lib/netstandard2.0/Microsoft.AspNetCore.Http.Abstractions.xml", "microsoft.aspnetcore.http.abstractions.2.2.0.nupkg.sha512", "microsoft.aspnetcore.http.abstractions.nuspec"]}, "Microsoft.AspNetCore.Http.Features/2.2.0": {"sha512": "ziFz5zH8f33En4dX81LW84I6XrYXKf9jg6aM39cM+LffN9KJahViKZ61dGMSO2gd3e+qe5yBRwsesvyqlZaSMg==", "type": "package", "path": "microsoft.aspnetcore.http.features/2.2.0", "files": [".nupkg.metadata", ".signature.p7s", "lib/netstandard2.0/Microsoft.AspNetCore.Http.Features.dll", "lib/netstandard2.0/Microsoft.AspNetCore.Http.Features.xml", "microsoft.aspnetcore.http.features.2.2.0.nupkg.sha512", "microsoft.aspnetcore.http.features.nuspec"]}, "Microsoft.Azure.Amqp/2.6.4": {"sha512": "Xf2mbwTSuUtqRlULKXCEuXPxlBhZzZXWmMxnxF64WJAelo3PA7kIDR4Bv+eOBYxHyr3FJtwG3/7rrhyXIx1Qzg==", "type": "package", "path": "microsoft.azure.amqp/2.6.4", "files": [".nupkg.metadata", ".signature.p7s", "images/icon.png", "lib/monoandroid/Microsoft.Azure.Amqp.dll", "lib/net45/Microsoft.Azure.Amqp.dll", "lib/net45/Microsoft.Azure.Amqp.xml", "lib/netstandard1.3/Microsoft.Azure.Amqp.dll", "lib/netstandard1.3/Microsoft.Azure.Amqp.xml", "lib/netstandard2.0/Microsoft.Azure.Amqp.dll", "lib/netstandard2.0/Microsoft.Azure.Amqp.xml", "lib/portable-net45+wp8+wpa81+win8+MonoAndroid10+MonoTouch10+Xamarin.iOS10+UAP10/Microsoft.Azure.Amqp.dll", "lib/uap10.0/Microsoft.Azure.Amqp.dll", "lib/uap10.0/Microsoft.Azure.Amqp.pri", "microsoft.azure.amqp.2.6.4.nupkg.sha512", "microsoft.azure.amqp.nuspec"]}, "Microsoft.Bcl.AsyncInterfaces/1.1.1": {"sha512": "yuvf07qFWFqtK3P/MRkEKLhn5r2UbSpVueRziSqj0yJQIKFwG1pq9mOayK3zE5qZCTs0CbrwL9M6R8VwqyGy2w==", "type": "package", "path": "microsoft.bcl.asyncinterfaces/1.1.1", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "lib/net461/Microsoft.Bcl.AsyncInterfaces.dll", "lib/net461/Microsoft.Bcl.AsyncInterfaces.xml", "lib/netstandard2.0/Microsoft.Bcl.AsyncInterfaces.dll", "lib/netstandard2.0/Microsoft.Bcl.AsyncInterfaces.xml", "lib/netstandard2.1/Microsoft.Bcl.AsyncInterfaces.dll", "lib/netstandard2.1/Microsoft.Bcl.AsyncInterfaces.xml", "microsoft.bcl.asyncinterfaces.1.1.1.nupkg.sha512", "microsoft.bcl.asyncinterfaces.nuspec", "ref/net461/Microsoft.Bcl.AsyncInterfaces.dll", "ref/netstandard2.0/Microsoft.Bcl.AsyncInterfaces.dll", "ref/netstandard2.1/Microsoft.Bcl.AsyncInterfaces.dll", "useSharedDesignerContext.txt", "version.txt"]}, "Microsoft.CSharp/4.7.0": {"sha512": "pTj+D3uJWyN3My70i2Hqo+OXixq3Os2D1nJ2x92FFo6sk8fYS1m1WLNTs0Dc1uPaViH0YvEEwvzddQ7y4rhXmA==", "type": "package", "path": "microsoft.csharp/4.7.0", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net45/_._", "lib/netcore50/Microsoft.CSharp.dll", "lib/netcoreapp2.0/_._", "lib/netstandard1.3/Microsoft.CSharp.dll", "lib/netstandard2.0/Microsoft.CSharp.dll", "lib/netstandard2.0/Microsoft.CSharp.xml", "lib/portable-net45+win8+wp8+wpa81/_._", "lib/uap10.0.16299/_._", "lib/win8/_._", "lib/wp80/_._", "lib/wpa81/_._", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "microsoft.csharp.4.7.0.nupkg.sha512", "microsoft.csharp.nuspec", "ref/MonoAndroid10/_._", "ref/MonoTouch10/_._", "ref/net45/_._", "ref/netcore50/Microsoft.CSharp.dll", "ref/netcore50/Microsoft.CSharp.xml", "ref/netcore50/de/Microsoft.CSharp.xml", "ref/netcore50/es/Microsoft.CSharp.xml", "ref/netcore50/fr/Microsoft.CSharp.xml", "ref/netcore50/it/Microsoft.CSharp.xml", "ref/netcore50/ja/Microsoft.CSharp.xml", "ref/netcore50/ko/Microsoft.CSharp.xml", "ref/netcore50/ru/Microsoft.CSharp.xml", "ref/netcore50/zh-hans/Microsoft.CSharp.xml", "ref/netcore50/zh-hant/Microsoft.CSharp.xml", "ref/netcoreapp2.0/_._", "ref/netstandard1.0/Microsoft.CSharp.dll", "ref/netstandard1.0/Microsoft.CSharp.xml", "ref/netstandard1.0/de/Microsoft.CSharp.xml", "ref/netstandard1.0/es/Microsoft.CSharp.xml", "ref/netstandard1.0/fr/Microsoft.CSharp.xml", "ref/netstandard1.0/it/Microsoft.CSharp.xml", "ref/netstandard1.0/ja/Microsoft.CSharp.xml", "ref/netstandard1.0/ko/Microsoft.CSharp.xml", "ref/netstandard1.0/ru/Microsoft.CSharp.xml", "ref/netstandard1.0/zh-hans/Microsoft.CSharp.xml", "ref/netstandard1.0/zh-hant/Microsoft.CSharp.xml", "ref/netstandard2.0/Microsoft.CSharp.dll", "ref/netstandard2.0/Microsoft.CSharp.xml", "ref/portable-net45+win8+wp8+wpa81/_._", "ref/uap10.0.16299/_._", "ref/win8/_._", "ref/wp80/_._", "ref/wpa81/_._", "ref/xamarinios10/_._", "ref/xamarinmac20/_._", "ref/xamarintvos10/_._", "ref/xamarinwatchos10/_._", "useSharedDesignerContext.txt", "version.txt"]}, "Microsoft.Extensions.Caching.Abstractions/7.0.0": {"sha512": "IeimUd0TNbhB4ded3AbgBLQv2SnsiVugDyGV1MvspQFVlA07nDC7Zul7kcwH5jWN3JiTcp/ySE83AIJo8yfKjg==", "type": "package", "path": "microsoft.extensions.caching.abstractions/7.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/Microsoft.Extensions.Caching.Abstractions.targets", "buildTransitive/net462/_._", "buildTransitive/net6.0/_._", "buildTransitive/netcoreapp2.0/Microsoft.Extensions.Caching.Abstractions.targets", "lib/net462/Microsoft.Extensions.Caching.Abstractions.dll", "lib/net462/Microsoft.Extensions.Caching.Abstractions.xml", "lib/net6.0/Microsoft.Extensions.Caching.Abstractions.dll", "lib/net6.0/Microsoft.Extensions.Caching.Abstractions.xml", "lib/net7.0/Microsoft.Extensions.Caching.Abstractions.dll", "lib/net7.0/Microsoft.Extensions.Caching.Abstractions.xml", "lib/netstandard2.0/Microsoft.Extensions.Caching.Abstractions.dll", "lib/netstandard2.0/Microsoft.Extensions.Caching.Abstractions.xml", "microsoft.extensions.caching.abstractions.7.0.0.nupkg.sha512", "microsoft.extensions.caching.abstractions.nuspec", "useSharedDesignerContext.txt"]}, "Microsoft.Extensions.Caching.Memory/7.0.0": {"sha512": "xpidBs2KCE2gw1JrD0quHE72kvCaI3xFql5/Peb2GRtUuZX+dYPoK/NTdVMiM67Svym0M0Df9A3xyU0FbMQhHw==", "type": "package", "path": "microsoft.extensions.caching.memory/7.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/Microsoft.Extensions.Caching.Memory.targets", "buildTransitive/net462/_._", "buildTransitive/net6.0/_._", "buildTransitive/netcoreapp2.0/Microsoft.Extensions.Caching.Memory.targets", "lib/net462/Microsoft.Extensions.Caching.Memory.dll", "lib/net462/Microsoft.Extensions.Caching.Memory.xml", "lib/net6.0/Microsoft.Extensions.Caching.Memory.dll", "lib/net6.0/Microsoft.Extensions.Caching.Memory.xml", "lib/net7.0/Microsoft.Extensions.Caching.Memory.dll", "lib/net7.0/Microsoft.Extensions.Caching.Memory.xml", "lib/netstandard2.0/Microsoft.Extensions.Caching.Memory.dll", "lib/netstandard2.0/Microsoft.Extensions.Caching.Memory.xml", "microsoft.extensions.caching.memory.7.0.0.nupkg.sha512", "microsoft.extensions.caching.memory.nuspec", "useSharedDesignerContext.txt"]}, "Microsoft.Extensions.Configuration/6.0.1": {"sha512": "BUyFU9t+HzlSE7ri4B+AQN2BgTgHv/uM82s5ZkgU1BApyzWzIl48nDsG5wR1t0pniNuuyTBzG3qCW8152/NtSw==", "type": "package", "path": "microsoft.extensions.configuration/6.0.1", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "lib/net461/Microsoft.Extensions.Configuration.dll", "lib/net461/Microsoft.Extensions.Configuration.xml", "lib/netstandard2.0/Microsoft.Extensions.Configuration.dll", "lib/netstandard2.0/Microsoft.Extensions.Configuration.xml", "microsoft.extensions.configuration.6.0.1.nupkg.sha512", "microsoft.extensions.configuration.nuspec", "useSharedDesignerContext.txt"]}, "Microsoft.Extensions.Configuration.Abstractions/8.0.0": {"sha512": "3lE/iLSutpgX1CC0NOW70FJoGARRHbyKmG7dc0klnUZ9Dd9hS6N/POPWhKhMLCEuNN5nXEY5agmlFtH562vqhQ==", "type": "package", "path": "microsoft.extensions.configuration.abstractions/8.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "PACKAGE.md", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/Microsoft.Extensions.Configuration.Abstractions.targets", "buildTransitive/net462/_._", "buildTransitive/net6.0/_._", "buildTransitive/netcoreapp2.0/Microsoft.Extensions.Configuration.Abstractions.targets", "lib/net462/Microsoft.Extensions.Configuration.Abstractions.dll", "lib/net462/Microsoft.Extensions.Configuration.Abstractions.xml", "lib/net6.0/Microsoft.Extensions.Configuration.Abstractions.dll", "lib/net6.0/Microsoft.Extensions.Configuration.Abstractions.xml", "lib/net7.0/Microsoft.Extensions.Configuration.Abstractions.dll", "lib/net7.0/Microsoft.Extensions.Configuration.Abstractions.xml", "lib/net8.0/Microsoft.Extensions.Configuration.Abstractions.dll", "lib/net8.0/Microsoft.Extensions.Configuration.Abstractions.xml", "lib/netstandard2.0/Microsoft.Extensions.Configuration.Abstractions.dll", "lib/netstandard2.0/Microsoft.Extensions.Configuration.Abstractions.xml", "microsoft.extensions.configuration.abstractions.8.0.0.nupkg.sha512", "microsoft.extensions.configuration.abstractions.nuspec", "useSharedDesignerContext.txt"]}, "Microsoft.Extensions.Configuration.Binder/8.0.0": {"sha512": "mBMoXLsr5s1y2zOHWmKsE9veDcx8h1x/c3rz4baEdQKTeDcmQAPNbB54Pi/lhFO3K431eEq6PFbMgLaa6PHFfA==", "type": "package", "path": "microsoft.extensions.configuration.binder/8.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "PACKAGE.md", "THIRD-PARTY-NOTICES.TXT", "analyzers/dotnet/cs/Microsoft.Extensions.Configuration.Binder.SourceGeneration.dll", "analyzers/dotnet/cs/cs/Microsoft.Extensions.Configuration.Binder.SourceGeneration.resources.dll", "analyzers/dotnet/cs/de/Microsoft.Extensions.Configuration.Binder.SourceGeneration.resources.dll", "analyzers/dotnet/cs/es/Microsoft.Extensions.Configuration.Binder.SourceGeneration.resources.dll", "analyzers/dotnet/cs/fr/Microsoft.Extensions.Configuration.Binder.SourceGeneration.resources.dll", "analyzers/dotnet/cs/it/Microsoft.Extensions.Configuration.Binder.SourceGeneration.resources.dll", "analyzers/dotnet/cs/ja/Microsoft.Extensions.Configuration.Binder.SourceGeneration.resources.dll", "analyzers/dotnet/cs/ko/Microsoft.Extensions.Configuration.Binder.SourceGeneration.resources.dll", "analyzers/dotnet/cs/pl/Microsoft.Extensions.Configuration.Binder.SourceGeneration.resources.dll", "analyzers/dotnet/cs/pt-BR/Microsoft.Extensions.Configuration.Binder.SourceGeneration.resources.dll", "analyzers/dotnet/cs/ru/Microsoft.Extensions.Configuration.Binder.SourceGeneration.resources.dll", "analyzers/dotnet/cs/tr/Microsoft.Extensions.Configuration.Binder.SourceGeneration.resources.dll", "analyzers/dotnet/cs/zh-<PERSON>/Microsoft.Extensions.Configuration.Binder.SourceGeneration.resources.dll", "analyzers/dotnet/cs/zh-Hant/Microsoft.Extensions.Configuration.Binder.SourceGeneration.resources.dll", "buildTransitive/netstandard2.0/Microsoft.Extensions.Configuration.Binder.targets", "lib/net462/Microsoft.Extensions.Configuration.Binder.dll", "lib/net462/Microsoft.Extensions.Configuration.Binder.xml", "lib/net6.0/Microsoft.Extensions.Configuration.Binder.dll", "lib/net6.0/Microsoft.Extensions.Configuration.Binder.xml", "lib/net7.0/Microsoft.Extensions.Configuration.Binder.dll", "lib/net7.0/Microsoft.Extensions.Configuration.Binder.xml", "lib/net8.0/Microsoft.Extensions.Configuration.Binder.dll", "lib/net8.0/Microsoft.Extensions.Configuration.Binder.xml", "lib/netstandard2.0/Microsoft.Extensions.Configuration.Binder.dll", "lib/netstandard2.0/Microsoft.Extensions.Configuration.Binder.xml", "microsoft.extensions.configuration.binder.8.0.0.nupkg.sha512", "microsoft.extensions.configuration.binder.nuspec", "useSharedDesignerContext.txt"]}, "Microsoft.Extensions.Configuration.EnvironmentVariables/6.0.1": {"sha512": "pnyXV1LFOsYjGveuC07xp0YHIyGq7jRq5Ncb5zrrIieMLWVwgMyYxcOH0jTnBedDT4Gh1QinSqsjqzcieHk1og==", "type": "package", "path": "microsoft.extensions.configuration.environmentvariables/6.0.1", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "lib/net461/Microsoft.Extensions.Configuration.EnvironmentVariables.dll", "lib/net461/Microsoft.Extensions.Configuration.EnvironmentVariables.xml", "lib/netstandard2.0/Microsoft.Extensions.Configuration.EnvironmentVariables.dll", "lib/netstandard2.0/Microsoft.Extensions.Configuration.EnvironmentVariables.xml", "microsoft.extensions.configuration.environmentvariables.6.0.1.nupkg.sha512", "microsoft.extensions.configuration.environmentvariables.nuspec", "useSharedDesignerContext.txt"]}, "Microsoft.Extensions.Configuration.FileExtensions/6.0.0": {"sha512": "V4Dth2cYMZpw3HhGw9XUDIijpI6gN+22LDt0AhufIgOppCUfpWX4483OmN+dFXRJkJLc8Tv0Q8QK+1ingT2+KQ==", "type": "package", "path": "microsoft.extensions.configuration.fileextensions/6.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "lib/net461/Microsoft.Extensions.Configuration.FileExtensions.dll", "lib/net461/Microsoft.Extensions.Configuration.FileExtensions.xml", "lib/netstandard2.0/Microsoft.Extensions.Configuration.FileExtensions.dll", "lib/netstandard2.0/Microsoft.Extensions.Configuration.FileExtensions.xml", "microsoft.extensions.configuration.fileextensions.6.0.0.nupkg.sha512", "microsoft.extensions.configuration.fileextensions.nuspec", "useSharedDesignerContext.txt"]}, "Microsoft.Extensions.Configuration.Json/6.0.0": {"sha512": "GJGery6QytCzS/BxJ96klgG9in3uH26KcUBbiVG/coNDXCRq6LGVVlUT4vXq34KPuM+R2av+LeYdX9h4IZOCUg==", "type": "package", "path": "microsoft.extensions.configuration.json/6.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "lib/net461/Microsoft.Extensions.Configuration.Json.dll", "lib/net461/Microsoft.Extensions.Configuration.Json.xml", "lib/netstandard2.0/Microsoft.Extensions.Configuration.Json.dll", "lib/netstandard2.0/Microsoft.Extensions.Configuration.Json.xml", "lib/netstandard2.1/Microsoft.Extensions.Configuration.Json.dll", "lib/netstandard2.1/Microsoft.Extensions.Configuration.Json.xml", "microsoft.extensions.configuration.json.6.0.0.nupkg.sha512", "microsoft.extensions.configuration.json.nuspec", "useSharedDesignerContext.txt"]}, "Microsoft.Extensions.Configuration.UserSecrets/6.0.1": {"sha512": "Fy8yr4V6obi7ZxvKYI1i85jqtwMq8tqyxQVZpRSkgeA8enqy/KvBIMdcuNdznlxQMZa72mvbHqb7vbg4Pyx95w==", "type": "package", "path": "microsoft.extensions.configuration.usersecrets/6.0.1", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/netstandard2.0/Microsoft.Extensions.Configuration.UserSecrets.props", "buildTransitive/netstandard2.0/Microsoft.Extensions.Configuration.UserSecrets.targets", "lib/net461/Microsoft.Extensions.Configuration.UserSecrets.dll", "lib/net461/Microsoft.Extensions.Configuration.UserSecrets.xml", "lib/netstandard2.0/Microsoft.Extensions.Configuration.UserSecrets.dll", "lib/netstandard2.0/Microsoft.Extensions.Configuration.UserSecrets.xml", "microsoft.extensions.configuration.usersecrets.6.0.1.nupkg.sha512", "microsoft.extensions.configuration.usersecrets.nuspec", "useSharedDesignerContext.txt"]}, "Microsoft.Extensions.DependencyInjection/8.0.0": {"sha512": "V8S3bsm50ig6JSyrbcJJ8bW2b9QLGouz+G1miK3UTaOWmMtFwNNNzUf4AleyDWUmTrWMLNnFSLEQtxmxgNQnNQ==", "type": "package", "path": "microsoft.extensions.dependencyinjection/8.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "PACKAGE.md", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/Microsoft.Extensions.DependencyInjection.targets", "buildTransitive/net462/_._", "buildTransitive/net6.0/_._", "buildTransitive/netcoreapp2.0/Microsoft.Extensions.DependencyInjection.targets", "lib/net462/Microsoft.Extensions.DependencyInjection.dll", "lib/net462/Microsoft.Extensions.DependencyInjection.xml", "lib/net6.0/Microsoft.Extensions.DependencyInjection.dll", "lib/net6.0/Microsoft.Extensions.DependencyInjection.xml", "lib/net7.0/Microsoft.Extensions.DependencyInjection.dll", "lib/net7.0/Microsoft.Extensions.DependencyInjection.xml", "lib/net8.0/Microsoft.Extensions.DependencyInjection.dll", "lib/net8.0/Microsoft.Extensions.DependencyInjection.xml", "lib/netstandard2.0/Microsoft.Extensions.DependencyInjection.dll", "lib/netstandard2.0/Microsoft.Extensions.DependencyInjection.xml", "lib/netstandard2.1/Microsoft.Extensions.DependencyInjection.dll", "lib/netstandard2.1/Microsoft.Extensions.DependencyInjection.xml", "microsoft.extensions.dependencyinjection.8.0.0.nupkg.sha512", "microsoft.extensions.dependencyinjection.nuspec", "useSharedDesignerContext.txt"]}, "Microsoft.Extensions.DependencyInjection.Abstractions/8.0.0": {"sha512": "cjWrLkJXK0rs4zofsK4bSdg+jhDLTaxrkXu4gS6Y7MAlCvRyNNgwY/lJi5RDlQOnSZweHqoyvgvbdvQsRIW+hg==", "type": "package", "path": "microsoft.extensions.dependencyinjection.abstractions/8.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "PACKAGE.md", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/Microsoft.Extensions.DependencyInjection.Abstractions.targets", "buildTransitive/net462/_._", "buildTransitive/net6.0/_._", "buildTransitive/netcoreapp2.0/Microsoft.Extensions.DependencyInjection.Abstractions.targets", "lib/net462/Microsoft.Extensions.DependencyInjection.Abstractions.dll", "lib/net462/Microsoft.Extensions.DependencyInjection.Abstractions.xml", "lib/net6.0/Microsoft.Extensions.DependencyInjection.Abstractions.dll", "lib/net6.0/Microsoft.Extensions.DependencyInjection.Abstractions.xml", "lib/net7.0/Microsoft.Extensions.DependencyInjection.Abstractions.dll", "lib/net7.0/Microsoft.Extensions.DependencyInjection.Abstractions.xml", "lib/net8.0/Microsoft.Extensions.DependencyInjection.Abstractions.dll", "lib/net8.0/Microsoft.Extensions.DependencyInjection.Abstractions.xml", "lib/netstandard2.0/Microsoft.Extensions.DependencyInjection.Abstractions.dll", "lib/netstandard2.0/Microsoft.Extensions.DependencyInjection.Abstractions.xml", "lib/netstandard2.1/Microsoft.Extensions.DependencyInjection.Abstractions.dll", "lib/netstandard2.1/Microsoft.Extensions.DependencyInjection.Abstractions.xml", "microsoft.extensions.dependencyinjection.abstractions.8.0.0.nupkg.sha512", "microsoft.extensions.dependencyinjection.abstractions.nuspec", "useSharedDesignerContext.txt"]}, "Microsoft.Extensions.DependencyModel/3.0.0": {"sha512": "Iaectmzg9Dc4ZbKX/FurrRjgO/I8rTumL5UU+Uube6vZuGetcnXoIgTA94RthFWePhdMVm8MMhVFJZdbzMsdyQ==", "type": "package", "path": "microsoft.extensions.dependencymodel/3.0.0", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "lib/net451/Microsoft.Extensions.DependencyModel.dll", "lib/net451/Microsoft.Extensions.DependencyModel.xml", "lib/netstandard1.3/Microsoft.Extensions.DependencyModel.dll", "lib/netstandard1.3/Microsoft.Extensions.DependencyModel.xml", "lib/netstandard1.6/Microsoft.Extensions.DependencyModel.dll", "lib/netstandard1.6/Microsoft.Extensions.DependencyModel.xml", "lib/netstandard2.0/Microsoft.Extensions.DependencyModel.dll", "lib/netstandard2.0/Microsoft.Extensions.DependencyModel.xml", "microsoft.extensions.dependencymodel.3.0.0.nupkg.sha512", "microsoft.extensions.dependencymodel.nuspec"]}, "Microsoft.Extensions.FileProviders.Abstractions/6.0.0": {"sha512": "0pd4/fho0gC12rQswaGQxbU34jOS1TPS8lZPpkFCH68ppQjHNHYle9iRuHeev1LhrJ94YPvzcRd8UmIuFk23Qw==", "type": "package", "path": "microsoft.extensions.fileproviders.abstractions/6.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/netcoreapp2.0/Microsoft.Extensions.FileProviders.Abstractions.targets", "buildTransitive/netcoreapp3.1/_._", "lib/net461/Microsoft.Extensions.FileProviders.Abstractions.dll", "lib/net461/Microsoft.Extensions.FileProviders.Abstractions.xml", "lib/net6.0/Microsoft.Extensions.FileProviders.Abstractions.dll", "lib/net6.0/Microsoft.Extensions.FileProviders.Abstractions.xml", "lib/netstandard2.0/Microsoft.Extensions.FileProviders.Abstractions.dll", "lib/netstandard2.0/Microsoft.Extensions.FileProviders.Abstractions.xml", "microsoft.extensions.fileproviders.abstractions.6.0.0.nupkg.sha512", "microsoft.extensions.fileproviders.abstractions.nuspec", "useSharedDesignerContext.txt"]}, "Microsoft.Extensions.FileProviders.Physical/6.0.0": {"sha512": "QvkL7l0nM8udt3gfyu0Vw8bbCXblxaKOl7c2oBfgGy4LCURRaL9XWZX1FWJrQc43oMokVneVxH38iz+bY1sbhg==", "type": "package", "path": "microsoft.extensions.fileproviders.physical/6.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/netcoreapp2.0/Microsoft.Extensions.FileProviders.Physical.targets", "buildTransitive/netcoreapp3.1/_._", "lib/net461/Microsoft.Extensions.FileProviders.Physical.dll", "lib/net461/Microsoft.Extensions.FileProviders.Physical.xml", "lib/net6.0/Microsoft.Extensions.FileProviders.Physical.dll", "lib/net6.0/Microsoft.Extensions.FileProviders.Physical.xml", "lib/netstandard2.0/Microsoft.Extensions.FileProviders.Physical.dll", "lib/netstandard2.0/Microsoft.Extensions.FileProviders.Physical.xml", "microsoft.extensions.fileproviders.physical.6.0.0.nupkg.sha512", "microsoft.extensions.fileproviders.physical.nuspec", "useSharedDesignerContext.txt"]}, "Microsoft.Extensions.FileSystemGlobbing/6.0.0": {"sha512": "ip8jnL1aPiaPeKINCqaTEbvBFDmVx9dXQEBZ2HOBRXPD1eabGNqP/bKlsIcp7U2lGxiXd5xIhoFcmY8nM4Hdiw==", "type": "package", "path": "microsoft.extensions.filesystemglobbing/6.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/netcoreapp2.0/Microsoft.Extensions.FileSystemGlobbing.targets", "buildTransitive/netcoreapp3.1/_._", "lib/net461/Microsoft.Extensions.FileSystemGlobbing.dll", "lib/net461/Microsoft.Extensions.FileSystemGlobbing.xml", "lib/net6.0/Microsoft.Extensions.FileSystemGlobbing.dll", "lib/net6.0/Microsoft.Extensions.FileSystemGlobbing.xml", "lib/netstandard2.0/Microsoft.Extensions.FileSystemGlobbing.dll", "lib/netstandard2.0/Microsoft.Extensions.FileSystemGlobbing.xml", "microsoft.extensions.filesystemglobbing.6.0.0.nupkg.sha512", "microsoft.extensions.filesystemglobbing.nuspec", "useSharedDesignerContext.txt"]}, "Microsoft.Extensions.Hosting.Abstractions/6.0.0": {"sha512": "GcT5l2CYXL6Sa27KCSh0TixsRfADUgth+ojQSD5EkzisZxmGFh7CwzkcYuGwvmXLjr27uWRNrJ2vuuEjMhU05Q==", "type": "package", "path": "microsoft.extensions.hosting.abstractions/6.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "lib/net461/Microsoft.Extensions.Hosting.Abstractions.dll", "lib/net461/Microsoft.Extensions.Hosting.Abstractions.xml", "lib/netstandard2.0/Microsoft.Extensions.Hosting.Abstractions.dll", "lib/netstandard2.0/Microsoft.Extensions.Hosting.Abstractions.xml", "lib/netstandard2.1/Microsoft.Extensions.Hosting.Abstractions.dll", "lib/netstandard2.1/Microsoft.Extensions.Hosting.Abstractions.xml", "microsoft.extensions.hosting.abstractions.6.0.0.nupkg.sha512", "microsoft.extensions.hosting.abstractions.nuspec", "useSharedDesignerContext.txt"]}, "Microsoft.Extensions.Http/6.0.0": {"sha512": "15+pa2G0bAMHbHewaQIdr/y6ag2H3yh4rd9hTXavtWDzQBkvpe2RMqFg8BxDpcQWssmjmBApGPcw93QRz6YcMg==", "type": "package", "path": "microsoft.extensions.http/6.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "lib/net461/Microsoft.Extensions.Http.dll", "lib/net461/Microsoft.Extensions.Http.xml", "lib/netstandard2.0/Microsoft.Extensions.Http.dll", "lib/netstandard2.0/Microsoft.Extensions.Http.xml", "microsoft.extensions.http.6.0.0.nupkg.sha512", "microsoft.extensions.http.nuspec", "useSharedDesignerContext.txt"]}, "Microsoft.Extensions.Http.Polly/6.0.9": {"sha512": "+tQeERLaSPA+G//SlIZ5pyv/jAmkn1xnMMOvFu3Bag3EJxwV4D9iEkHD2TaNiJOoFZ/VROUB76/H7n/75e9Dow==", "type": "package", "path": "microsoft.extensions.http.polly/6.0.9", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "THIRD-PARTY-NOTICES.TXT", "lib/netstandard2.0/Microsoft.Extensions.Http.Polly.dll", "lib/netstandard2.0/Microsoft.Extensions.Http.Polly.xml", "microsoft.extensions.http.polly.6.0.9.nupkg.sha512", "microsoft.extensions.http.polly.nuspec"]}, "Microsoft.Extensions.Logging/6.0.0": {"sha512": "eIbyj40QDg1NDz0HBW0S5f3wrLVnKWnDJ/JtZ+yJDFnDj90VoPuoPmFkeaXrtu+0cKm5GRAwoDf+dBWXK0TUdg==", "type": "package", "path": "microsoft.extensions.logging/6.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "lib/net461/Microsoft.Extensions.Logging.dll", "lib/net461/Microsoft.Extensions.Logging.xml", "lib/netstandard2.0/Microsoft.Extensions.Logging.dll", "lib/netstandard2.0/Microsoft.Extensions.Logging.xml", "lib/netstandard2.1/Microsoft.Extensions.Logging.dll", "lib/netstandard2.1/Microsoft.Extensions.Logging.xml", "microsoft.extensions.logging.6.0.0.nupkg.sha512", "microsoft.extensions.logging.nuspec", "useSharedDesignerContext.txt"]}, "Microsoft.Extensions.Logging.Abstractions/8.0.0": {"sha512": "arDBqTgFCyS0EvRV7O3MZturChstm50OJ0y9bDJvAcmEPJm0FFpFyjU/JLYyStNGGey081DvnQYlncNX5SJJGA==", "type": "package", "path": "microsoft.extensions.logging.abstractions/8.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "PACKAGE.md", "THIRD-PARTY-NOTICES.TXT", "analyzers/dotnet/roslyn3.11/cs/Microsoft.Extensions.Logging.Generators.dll", "analyzers/dotnet/roslyn3.11/cs/cs/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn3.11/cs/de/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn3.11/cs/es/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn3.11/cs/fr/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn3.11/cs/it/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn3.11/cs/ja/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn3.11/cs/ko/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn3.11/cs/pl/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn3.11/cs/pt-BR/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn3.11/cs/ru/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn3.11/cs/tr/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn3.11/cs/zh-<PERSON>/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn3.11/cs/zh-Hant/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.0/cs/Microsoft.Extensions.Logging.Generators.dll", "analyzers/dotnet/roslyn4.0/cs/cs/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.0/cs/de/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.0/cs/es/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.0/cs/fr/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.0/cs/it/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.0/cs/ja/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.0/cs/ko/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.0/cs/pl/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.0/cs/pt-BR/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.0/cs/ru/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.0/cs/tr/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.0/cs/zh-<PERSON>/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.0/cs/zh-Hant/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.4/cs/Microsoft.Extensions.Logging.Generators.dll", "analyzers/dotnet/roslyn4.4/cs/cs/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.4/cs/de/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.4/cs/es/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.4/cs/fr/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.4/cs/it/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.4/cs/ja/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.4/cs/ko/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.4/cs/pl/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.4/cs/pt-BR/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.4/cs/ru/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.4/cs/tr/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.4/cs/zh-<PERSON>/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.4/cs/zh-Hant/Microsoft.Extensions.Logging.Generators.resources.dll", "buildTransitive/net461/Microsoft.Extensions.Logging.Abstractions.targets", "buildTransitive/net462/Microsoft.Extensions.Logging.Abstractions.targets", "buildTransitive/net6.0/Microsoft.Extensions.Logging.Abstractions.targets", "buildTransitive/netcoreapp2.0/Microsoft.Extensions.Logging.Abstractions.targets", "buildTransitive/netstandard2.0/Microsoft.Extensions.Logging.Abstractions.targets", "lib/net462/Microsoft.Extensions.Logging.Abstractions.dll", "lib/net462/Microsoft.Extensions.Logging.Abstractions.xml", "lib/net6.0/Microsoft.Extensions.Logging.Abstractions.dll", "lib/net6.0/Microsoft.Extensions.Logging.Abstractions.xml", "lib/net7.0/Microsoft.Extensions.Logging.Abstractions.dll", "lib/net7.0/Microsoft.Extensions.Logging.Abstractions.xml", "lib/net8.0/Microsoft.Extensions.Logging.Abstractions.dll", "lib/net8.0/Microsoft.Extensions.Logging.Abstractions.xml", "lib/netstandard2.0/Microsoft.Extensions.Logging.Abstractions.dll", "lib/netstandard2.0/Microsoft.Extensions.Logging.Abstractions.xml", "microsoft.extensions.logging.abstractions.8.0.0.nupkg.sha512", "microsoft.extensions.logging.abstractions.nuspec", "useSharedDesignerContext.txt"]}, "Microsoft.Extensions.Logging.Configuration/6.0.0": {"sha512": "ZDskjagmBAbv+K8rYW9VhjPplhbOE63xUD0DiuydZJwt15dRyoqicYklLd86zzeintUc7AptDkHn+YhhYkYo8A==", "type": "package", "path": "microsoft.extensions.logging.configuration/6.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "lib/net461/Microsoft.Extensions.Logging.Configuration.dll", "lib/net461/Microsoft.Extensions.Logging.Configuration.xml", "lib/netstandard2.0/Microsoft.Extensions.Logging.Configuration.dll", "lib/netstandard2.0/Microsoft.Extensions.Logging.Configuration.xml", "microsoft.extensions.logging.configuration.6.0.0.nupkg.sha512", "microsoft.extensions.logging.configuration.nuspec", "useSharedDesignerContext.txt"]}, "Microsoft.Extensions.Options/8.0.1": {"sha512": "wmpp+BSU3oGifaev6Z9rrlwHoITLFfpVOSbgBrOXjkbJSCXnZVCsoRGE5c3fJFI4VlNgnNkNlI9y+5jC4fmOEA==", "type": "package", "path": "microsoft.extensions.options/8.0.1", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "PACKAGE.md", "THIRD-PARTY-NOTICES.TXT", "analyzers/dotnet/roslyn4.4/cs/Microsoft.Extensions.Options.SourceGeneration.dll", "analyzers/dotnet/roslyn4.4/cs/cs/Microsoft.Extensions.Options.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.4/cs/de/Microsoft.Extensions.Options.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.4/cs/es/Microsoft.Extensions.Options.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.4/cs/fr/Microsoft.Extensions.Options.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.4/cs/it/Microsoft.Extensions.Options.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.4/cs/ja/Microsoft.Extensions.Options.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.4/cs/ko/Microsoft.Extensions.Options.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.4/cs/pl/Microsoft.Extensions.Options.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.4/cs/pt-BR/Microsoft.Extensions.Options.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.4/cs/ru/Microsoft.Extensions.Options.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.4/cs/tr/Microsoft.Extensions.Options.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.4/cs/zh-<PERSON>/Microsoft.Extensions.Options.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.4/cs/zh-Hant/Microsoft.Extensions.Options.SourceGeneration.resources.dll", "buildTransitive/net461/Microsoft.Extensions.Options.targets", "buildTransitive/net462/Microsoft.Extensions.Options.targets", "buildTransitive/net6.0/Microsoft.Extensions.Options.targets", "buildTransitive/netcoreapp2.0/Microsoft.Extensions.Options.targets", "buildTransitive/netstandard2.0/Microsoft.Extensions.Options.targets", "lib/net462/Microsoft.Extensions.Options.dll", "lib/net462/Microsoft.Extensions.Options.xml", "lib/net6.0/Microsoft.Extensions.Options.dll", "lib/net6.0/Microsoft.Extensions.Options.xml", "lib/net7.0/Microsoft.Extensions.Options.dll", "lib/net7.0/Microsoft.Extensions.Options.xml", "lib/net8.0/Microsoft.Extensions.Options.dll", "lib/net8.0/Microsoft.Extensions.Options.xml", "lib/netstandard2.0/Microsoft.Extensions.Options.dll", "lib/netstandard2.0/Microsoft.Extensions.Options.xml", "lib/netstandard2.1/Microsoft.Extensions.Options.dll", "lib/netstandard2.1/Microsoft.Extensions.Options.xml", "microsoft.extensions.options.8.0.1.nupkg.sha512", "microsoft.extensions.options.nuspec", "useSharedDesignerContext.txt"]}, "Microsoft.Extensions.Options.ConfigurationExtensions/8.0.0": {"sha512": "0f4DMRqEd50zQh+UyJc+/HiBsZ3vhAQALgdkcQEalSH1L2isdC7Yj54M3cyo5e+BeO5fcBQ7Dxly8XiBBcvRgw==", "type": "package", "path": "microsoft.extensions.options.configurationextensions/8.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "PACKAGE.md", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/Microsoft.Extensions.Options.ConfigurationExtensions.targets", "buildTransitive/net462/_._", "buildTransitive/net6.0/_._", "buildTransitive/netcoreapp2.0/Microsoft.Extensions.Options.ConfigurationExtensions.targets", "lib/net462/Microsoft.Extensions.Options.ConfigurationExtensions.dll", "lib/net462/Microsoft.Extensions.Options.ConfigurationExtensions.xml", "lib/net6.0/Microsoft.Extensions.Options.ConfigurationExtensions.dll", "lib/net6.0/Microsoft.Extensions.Options.ConfigurationExtensions.xml", "lib/net7.0/Microsoft.Extensions.Options.ConfigurationExtensions.dll", "lib/net7.0/Microsoft.Extensions.Options.ConfigurationExtensions.xml", "lib/net8.0/Microsoft.Extensions.Options.ConfigurationExtensions.dll", "lib/net8.0/Microsoft.Extensions.Options.ConfigurationExtensions.xml", "lib/netstandard2.0/Microsoft.Extensions.Options.ConfigurationExtensions.dll", "lib/netstandard2.0/Microsoft.Extensions.Options.ConfigurationExtensions.xml", "microsoft.extensions.options.configurationextensions.8.0.0.nupkg.sha512", "microsoft.extensions.options.configurationextensions.nuspec", "useSharedDesignerContext.txt"]}, "Microsoft.Extensions.Primitives/8.0.0": {"sha512": "bXJEZrW9ny8vjMF1JV253WeLhpEVzFo1lyaZu1vQ4ZxWUlVvknZ/+ftFgVheLubb4eZPSwwxBeqS1JkCOjxd8g==", "type": "package", "path": "microsoft.extensions.primitives/8.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "PACKAGE.md", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/Microsoft.Extensions.Primitives.targets", "buildTransitive/net462/_._", "buildTransitive/net6.0/_._", "buildTransitive/netcoreapp2.0/Microsoft.Extensions.Primitives.targets", "lib/net462/Microsoft.Extensions.Primitives.dll", "lib/net462/Microsoft.Extensions.Primitives.xml", "lib/net6.0/Microsoft.Extensions.Primitives.dll", "lib/net6.0/Microsoft.Extensions.Primitives.xml", "lib/net7.0/Microsoft.Extensions.Primitives.dll", "lib/net7.0/Microsoft.Extensions.Primitives.xml", "lib/net8.0/Microsoft.Extensions.Primitives.dll", "lib/net8.0/Microsoft.Extensions.Primitives.xml", "lib/netstandard2.0/Microsoft.Extensions.Primitives.dll", "lib/netstandard2.0/Microsoft.Extensions.Primitives.xml", "microsoft.extensions.primitives.8.0.0.nupkg.sha512", "microsoft.extensions.primitives.nuspec", "useSharedDesignerContext.txt"]}, "Microsoft.Identity.Client/4.56.0": {"sha512": "rr4zbidvHy9r4NvOAs5hdd964Ao2A0pAeFBJKR95u1CJAVzbd1p6tPTXUZ+5ld0cfThiVSGvz6UHwY6JjraTpA==", "type": "package", "path": "microsoft.identity.client/4.56.0", "files": [".nupkg.metadata", ".signature.p7s", "lib/monoandroid10.0/Microsoft.Identity.Client.dll", "lib/monoandroid10.0/Microsoft.Identity.Client.xml", "lib/monoandroid90/Microsoft.Identity.Client.dll", "lib/monoandroid90/Microsoft.Identity.Client.xml", "lib/net45/Microsoft.Identity.Client.dll", "lib/net45/Microsoft.Identity.Client.xml", "lib/net461/Microsoft.Identity.Client.dll", "lib/net461/Microsoft.Identity.Client.xml", "lib/net6.0-android31.0/Microsoft.Identity.Client.dll", "lib/net6.0-android31.0/Microsoft.Identity.Client.xml", "lib/net6.0-ios15.4/Microsoft.Identity.Client.dll", "lib/net6.0-ios15.4/Microsoft.Identity.Client.xml", "lib/net6.0-windows7.0/Microsoft.Identity.Client.dll", "lib/net6.0-windows7.0/Microsoft.Identity.Client.xml", "lib/net6.0/Microsoft.Identity.Client.dll", "lib/net6.0/Microsoft.Identity.Client.xml", "lib/netcoreapp2.1/Microsoft.Identity.Client.dll", "lib/netcoreapp2.1/Microsoft.Identity.Client.xml", "lib/netstandard2.0/Microsoft.Identity.Client.dll", "lib/netstandard2.0/Microsoft.Identity.Client.xml", "lib/uap10.0.17763/Microsoft.Identity.Client.dll", "lib/uap10.0.17763/Microsoft.Identity.Client.pri", "lib/uap10.0.17763/Microsoft.Identity.Client.xml", "lib/xamarinios10/Microsoft.Identity.Client.dll", "lib/xamarinios10/Microsoft.Identity.Client.xml", "microsoft.identity.client.4.56.0.nupkg.sha512", "microsoft.identity.client.nuspec"]}, "Microsoft.Identity.Client.Extensions.Msal/4.56.0": {"sha512": "H12YAzEGK55vZ+QpxUzozhW8ZZtgPDuWvgA0JbdIR9UhMUplj29JhIgE2imuH8W2Nw9D8JKygR1uxRFtpSNcrg==", "type": "package", "path": "microsoft.identity.client.extensions.msal/4.56.0", "files": [".nupkg.metadata", ".signature.p7s", "lib/netstandard2.0/Microsoft.Identity.Client.Extensions.Msal.dll", "lib/netstandard2.0/Microsoft.Identity.Client.Extensions.Msal.xml", "microsoft.identity.client.extensions.msal.4.56.0.nupkg.sha512", "microsoft.identity.client.extensions.msal.nuspec"]}, "Microsoft.IdentityModel.Abstractions/6.22.0": {"sha512": "iI+9V+2ciCrbheeLjpmjcqCnhy+r6yCoEcid3nkoFWerHgjVuT6CPM4HODUTtUPe1uwks4wcnAujJ8u+IKogHQ==", "type": "package", "path": "microsoft.identitymodel.abstractions/6.22.0", "files": [".nupkg.metadata", ".signature.p7s", "lib/net45/Microsoft.IdentityModel.Abstractions.dll", "lib/net45/Microsoft.IdentityModel.Abstractions.xml", "lib/net461/Microsoft.IdentityModel.Abstractions.dll", "lib/net461/Microsoft.IdentityModel.Abstractions.xml", "lib/net472/Microsoft.IdentityModel.Abstractions.dll", "lib/net472/Microsoft.IdentityModel.Abstractions.xml", "lib/net6.0/Microsoft.IdentityModel.Abstractions.dll", "lib/net6.0/Microsoft.IdentityModel.Abstractions.xml", "lib/netstandard2.0/Microsoft.IdentityModel.Abstractions.dll", "lib/netstandard2.0/Microsoft.IdentityModel.Abstractions.xml", "microsoft.identitymodel.abstractions.6.22.0.nupkg.sha512", "microsoft.identitymodel.abstractions.nuspec"]}, "Microsoft.NETCore.Platforms/5.0.0": {"sha512": "VyPlqzH2wavqquTcYpkIIAQ6WdenuKoFN0BdYBbCWsclXacSOHNQn66Gt4z5NBqEYW0FAPm5rlvki9ZiCij5xQ==", "type": "package", "path": "microsoft.netcore.platforms/5.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "lib/netstandard1.0/_._", "microsoft.netcore.platforms.5.0.0.nupkg.sha512", "microsoft.netcore.platforms.nuspec", "runtime.json", "useSharedDesignerContext.txt", "version.txt"]}, "Microsoft.Win32.Registry/5.0.0": {"sha512": "dDoKi0PnDz31yAyETfRntsLArTlVAVzUzCIvvEDsDsucrl33Dl8pIJG06ePTJTI3tGpeyHS9Cq7Foc/s4EeKcg==", "type": "package", "path": "microsoft.win32.registry/5.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "lib/net46/Microsoft.Win32.Registry.dll", "lib/net461/Microsoft.Win32.Registry.dll", "lib/net461/Microsoft.Win32.Registry.xml", "lib/netstandard1.3/Microsoft.Win32.Registry.dll", "lib/netstandard2.0/Microsoft.Win32.Registry.dll", "lib/netstandard2.0/Microsoft.Win32.Registry.xml", "microsoft.win32.registry.5.0.0.nupkg.sha512", "microsoft.win32.registry.nuspec", "ref/net46/Microsoft.Win32.Registry.dll", "ref/net461/Microsoft.Win32.Registry.dll", "ref/net461/Microsoft.Win32.Registry.xml", "ref/netstandard1.3/Microsoft.Win32.Registry.dll", "ref/netstandard1.3/Microsoft.Win32.Registry.xml", "ref/netstandard1.3/de/Microsoft.Win32.Registry.xml", "ref/netstandard1.3/es/Microsoft.Win32.Registry.xml", "ref/netstandard1.3/fr/Microsoft.Win32.Registry.xml", "ref/netstandard1.3/it/Microsoft.Win32.Registry.xml", "ref/netstandard1.3/ja/Microsoft.Win32.Registry.xml", "ref/netstandard1.3/ko/Microsoft.Win32.Registry.xml", "ref/netstandard1.3/ru/Microsoft.Win32.Registry.xml", "ref/netstandard1.3/zh-hans/Microsoft.Win32.Registry.xml", "ref/netstandard1.3/zh-hant/Microsoft.Win32.Registry.xml", "ref/netstandard2.0/Microsoft.Win32.Registry.dll", "ref/netstandard2.0/Microsoft.Win32.Registry.xml", "runtimes/win/lib/net46/Microsoft.Win32.Registry.dll", "runtimes/win/lib/net461/Microsoft.Win32.Registry.dll", "runtimes/win/lib/net461/Microsoft.Win32.Registry.xml", "runtimes/win/lib/netstandard1.3/Microsoft.Win32.Registry.dll", "runtimes/win/lib/netstandard2.0/Microsoft.Win32.Registry.dll", "runtimes/win/lib/netstandard2.0/Microsoft.Win32.Registry.xml", "useSharedDesignerContext.txt", "version.txt"]}, "MongoDB.Bson/2.25.0": {"sha512": "xQx/qtC2nu9oGiyNqAwfiDpUMweLi0nID677cyKykpwmj5AVMrnd//UwmcmuX95178DeY6rf7cjmA613TQXPiA==", "type": "package", "path": "mongodb.bson/2.25.0", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE.md", "lib/net472/MongoDB.Bson.dll", "lib/net472/MongoDB.Bson.xml", "lib/netstandard2.0/MongoDB.Bson.dll", "lib/netstandard2.0/MongoDB.Bson.xml", "lib/netstandard2.1/MongoDB.Bson.dll", "lib/netstandard2.1/MongoDB.Bson.xml", "mongodb.bson.2.25.0.nupkg.sha512", "mongodb.bson.nuspec", "packageIcon.png"]}, "MongoDB.Driver/2.25.0": {"sha512": "dMqnZTV6MuvoEI4yFtSvKJdAoN6NeyAEvG8aoxnrLIVd7bR84QxLgpsM1nhK17qkOcIx/IrpMIfrvp5iMnYGBg==", "type": "package", "path": "mongodb.driver/2.25.0", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE.md", "lib/net472/MongoDB.Driver.dll", "lib/net472/MongoDB.Driver.xml", "lib/netstandard2.0/MongoDB.Driver.dll", "lib/netstandard2.0/MongoDB.Driver.xml", "lib/netstandard2.1/MongoDB.Driver.dll", "lib/netstandard2.1/MongoDB.Driver.xml", "mongodb.driver.2.25.0.nupkg.sha512", "mongodb.driver.nuspec", "packageIcon.png"]}, "MongoDB.Driver.Core/2.25.0": {"sha512": "oN4nLgO5HQEThTg/zqeoHqaO2+q64DBVb4r7BvhaFb0p0TM9jZKnCKvh1EA8d9E9swIz0CgvMrvL1mPyRCZzag==", "type": "package", "path": "mongodb.driver.core/2.25.0", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE.md", "THIRD-PARTY-NOTICES", "lib/net472/MongoDB.Driver.Core.dll", "lib/net472/MongoDB.Driver.Core.xml", "lib/netstandard2.0/MongoDB.Driver.Core.dll", "lib/netstandard2.0/MongoDB.Driver.Core.xml", "lib/netstandard2.1/MongoDB.Driver.Core.dll", "lib/netstandard2.1/MongoDB.Driver.Core.xml", "mongodb.driver.core.2.25.0.nupkg.sha512", "mongodb.driver.core.nuspec", "packageIcon.png"]}, "MongoDB.Libmongocrypt/1.8.2": {"sha512": "z/8JCULSHM1+mzkau0ivIkU9kIn8JEFFSkmYTSaMaWMMHt96JjUtMKuXxeGNGSnHZ5290ZPKIlQfjoWFk2sKog==", "type": "package", "path": "mongodb.libmongocrypt/1.8.2", "files": [".nupkg.metadata", ".signature.p7s", "License.txt", "build/MongoDB.Libmongocrypt.targets", "content/libmongocrypt.dylib", "content/libmongocrypt.so", "content/mongocrypt.dll", "contentFiles/any/netstandard2.0/libmongocrypt.dylib", "contentFiles/any/netstandard2.0/libmongocrypt.so", "contentFiles/any/netstandard2.0/mongocrypt.dll", "contentFiles/any/netstandard2.1/libmongocrypt.dylib", "contentFiles/any/netstandard2.1/libmongocrypt.so", "contentFiles/any/netstandard2.1/mongocrypt.dll", "lib/netstandard2.0/MongoDB.Libmongocrypt.dll", "lib/netstandard2.1/MongoDB.Libmongocrypt.dll", "mongodb.libmongocrypt.1.8.2.nupkg.sha512", "mongodb.libmongocrypt.nuspec", "runtimes/linux/native/libmongocrypt.so", "runtimes/osx/native/libmongocrypt.dylib", "runtimes/win/native/mongocrypt.dll"]}, "Newtonsoft.Json/13.0.1": {"sha512": "ppPFpBcvxdsfUonNcvITKqLl3bqxWbDCZIzDWHzjpdAHRFfZe0Dw9HmA0+za13IdyrgJwpkDTDA9fHaxOrt20A==", "type": "package", "path": "newtonsoft.json/13.0.1", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE.md", "lib/net20/Newtonsoft.Json.dll", "lib/net20/Newtonsoft.Json.xml", "lib/net35/Newtonsoft.Json.dll", "lib/net35/Newtonsoft.Json.xml", "lib/net40/Newtonsoft.Json.dll", "lib/net40/Newtonsoft.Json.xml", "lib/net45/Newtonsoft.Json.dll", "lib/net45/Newtonsoft.Json.xml", "lib/netstandard1.0/Newtonsoft.Json.dll", "lib/netstandard1.0/Newtonsoft.Json.xml", "lib/netstandard1.3/Newtonsoft.Json.dll", "lib/netstandard1.3/Newtonsoft.Json.xml", "lib/netstandard2.0/Newtonsoft.Json.dll", "lib/netstandard2.0/Newtonsoft.Json.xml", "newtonsoft.json.13.0.1.nupkg.sha512", "newtonsoft.json.nuspec", "packageIcon.png"]}, "Polly/7.2.3": {"sha512": "DeCY0OFbNdNxsjntr1gTXHJ5pKUwYzp04Er2LLeN3g6pWhffsGuKVfMBLe1lw7x76HrPkLxKEFxBlpRxS2nDEQ==", "type": "package", "path": "polly/7.2.3", "files": [".nupkg.metadata", ".signature.p7s", "Polly.png", "lib/net461/Polly.dll", "lib/net461/Polly.pdb", "lib/net461/Polly.xml", "lib/net472/Polly.dll", "lib/net472/Polly.pdb", "lib/net472/Polly.xml", "lib/netstandard1.1/Polly.dll", "lib/netstandard1.1/Polly.pdb", "lib/netstandard1.1/Polly.xml", "lib/netstandard2.0/Polly.dll", "lib/netstandard2.0/Polly.pdb", "lib/netstandard2.0/Polly.xml", "polly.7.2.3.nupkg.sha512", "polly.nuspec"]}, "Polly.Extensions.Http/3.0.0": {"sha512": "drrG+hB3pYFY7w1c3BD+lSGYvH2oIclH8GRSehgfyP5kjnFnHKQuuBhuHLv+PWyFuaTDyk/vfRpnxOzd11+J8g==", "type": "package", "path": "polly.extensions.http/3.0.0", "files": [".nupkg.metadata", ".signature.p7s", "lib/netstandard1.1/Polly.Extensions.Http.dll", "lib/netstandard1.1/Polly.Extensions.Http.xml", "lib/netstandard2.0/Polly.Extensions.Http.dll", "lib/netstandard2.0/Polly.Extensions.Http.xml", "polly.extensions.http.3.0.0.nupkg.sha512", "polly.extensions.http.nuspec"]}, "SendGrid/9.28.1": {"sha512": "LyIkgjd+svXuQxpqe5pvyOccyUdKcDqwnBNDPjyCngkKeVpXAOTAr3U1DBLWqHEbFHvu2UBFki3SJzDwxvJdfA==", "type": "package", "path": "sendgrid/9.28.1", "files": [".nupkg.metadata", ".signature.p7s", "lib/net40/SendGrid.dll", "lib/net40/SendGrid.pdb", "lib/net40/SendGrid.xml", "lib/net452/SendGrid.dll", "lib/net452/SendGrid.pdb", "lib/net452/SendGrid.xml", "lib/netstandard1.3/SendGrid.dll", "lib/netstandard1.3/SendGrid.pdb", "lib/netstandard1.3/SendGrid.xml", "lib/netstandard2.0/SendGrid.dll", "lib/netstandard2.0/SendGrid.pdb", "lib/netstandard2.0/SendGrid.xml", "sendgrid.9.28.1.nupkg.sha512", "sendgrid.nuspec"]}, "SendGrid.Extensions.DependencyInjection/1.0.1": {"sha512": "M3dHAkRIIDWvGNro5S25xjQ+nvUTomZ5er12TL0Re+G2UwIntMvO2OthECb3SV28AvOtDd4yZERjdHTrJ+gD1w==", "type": "package", "path": "sendgrid.extensions.dependencyinjection/1.0.1", "files": [".nupkg.metadata", ".signature.p7s", "lib/netstandard2.0/SendGrid.Extensions.DependencyInjection.dll", "lib/netstandard2.0/SendGrid.Extensions.DependencyInjection.xml", "sendgrid.extensions.dependencyinjection.1.0.1.nupkg.sha512", "sendgrid.extensions.dependencyinjection.nuspec"]}, "Serilog/2.12.0": {"sha512": "xaiJLIdu6rYMKfQMYUZgTy8YK7SMZjB4Yk50C/u//Z4OsvxkUfSPJy4nknfvwAC34yr13q7kcyh4grbwhSxyZg==", "type": "package", "path": "serilog/2.12.0", "files": [".nupkg.metadata", ".signature.p7s", "icon.png", "lib/net45/Serilog.dll", "lib/net45/Serilog.xml", "lib/net46/Serilog.dll", "lib/net46/Serilog.xml", "lib/net47/Serilog.dll", "lib/net47/Serilog.xml", "lib/net5.0/Serilog.dll", "lib/net5.0/Serilog.xml", "lib/net6.0/Serilog.dll", "lib/net6.0/Serilog.xml", "lib/netstandard1.0/Serilog.dll", "lib/netstandard1.0/Serilog.xml", "lib/netstandard1.3/Serilog.dll", "lib/netstandard1.3/Serilog.xml", "lib/netstandard2.0/Serilog.dll", "lib/netstandard2.0/Serilog.xml", "lib/netstandard2.1/Serilog.dll", "lib/netstandard2.1/Serilog.xml", "serilog.2.12.0.nupkg.sha512", "serilog.nuspec"]}, "Serilog.AspNetCore/6.0.1": {"sha512": "5XW90k62V7G9I0D/j9Iz+NyRBB6/SnoFpHUPeLnV40gONV2vs2A/ewWi91QVjQmyHBfzFeqIrkvE/DJMZ0alTg==", "type": "package", "path": "serilog.aspnetcore/6.0.1", "files": [".nupkg.metadata", ".signature.p7s", "icon.png", "lib/net5.0/Serilog.AspNetCore.dll", "lib/net5.0/Serilog.AspNetCore.xml", "lib/netcoreapp3.1/Serilog.AspNetCore.dll", "lib/netcoreapp3.1/Serilog.AspNetCore.xml", "lib/netstandard2.0/Serilog.AspNetCore.dll", "lib/netstandard2.0/Serilog.AspNetCore.xml", "lib/netstandard2.1/Serilog.AspNetCore.dll", "lib/netstandard2.1/Serilog.AspNetCore.xml", "serilog.aspnetcore.6.0.1.nupkg.sha512", "serilog.aspnetcore.nuspec"]}, "Serilog.Enrichers.GlobalLogContext/2.1.0": {"sha512": "BtSFymdnkHWYpxJzLAQ7J03pdnkIlQ3kQLswBRz37j2XllEp5cnmCOOTqdw7Hyh8TrHQ1nJSZZ+HTjV4AqTwhA==", "type": "package", "path": "serilog.enrichers.globallogcontext/2.1.0", "files": [".nupkg.metadata", ".signature.p7s", "images/icon.png", "lib/net45/Serilog.Enrichers.GlobalLogContext.dll", "lib/net45/Serilog.Enrichers.GlobalLogContext.pdb", "lib/net45/Serilog.Enrichers.GlobalLogContext.xml", "lib/net5.0/Serilog.Enrichers.GlobalLogContext.dll", "lib/net5.0/Serilog.Enrichers.GlobalLogContext.pdb", "lib/net5.0/Serilog.Enrichers.GlobalLogContext.xml", "lib/netstandard2.0/Serilog.Enrichers.GlobalLogContext.dll", "lib/netstandard2.0/Serilog.Enrichers.GlobalLogContext.pdb", "lib/netstandard2.0/Serilog.Enrichers.GlobalLogContext.xml", "serilog.enrichers.globallogcontext.2.1.0.nupkg.sha512", "serilog.enrichers.globallogcontext.nuspec"]}, "Serilog.Extensions.Hosting/5.0.1": {"sha512": "o0VUyt3npAqOJaZ6CiWLFeLYs3CYJwfcAqaUqprzsmj7qYIvorcn8cZLVR8AQX6vzX7gee2bD0sQeA17iO2/Aw==", "type": "package", "path": "serilog.extensions.hosting/5.0.1", "files": [".nupkg.metadata", ".signature.p7s", "icon.png", "lib/netstandard2.0/Serilog.Extensions.Hosting.dll", "lib/netstandard2.0/Serilog.Extensions.Hosting.xml", "lib/netstandard2.1/Serilog.Extensions.Hosting.dll", "lib/netstandard2.1/Serilog.Extensions.Hosting.xml", "serilog.extensions.hosting.5.0.1.nupkg.sha512", "serilog.extensions.hosting.nuspec"]}, "Serilog.Extensions.Logging/3.1.0": {"sha512": "IWfem7wfrFbB3iw1OikqPFNPEzfayvDuN4WP7Ue1AVFskalMByeWk3QbtUXQR34SBkv1EbZ3AySHda/ErDgpcg==", "type": "package", "path": "serilog.extensions.logging/3.1.0", "files": [".nupkg.metadata", ".signature.p7s", "lib/netstandard2.0/Serilog.Extensions.Logging.dll", "lib/netstandard2.0/Serilog.Extensions.Logging.xml", "serilog-extension-nuget.png", "serilog.extensions.logging.3.1.0.nupkg.sha512", "serilog.extensions.logging.nuspec"]}, "Serilog.Formatting.Compact/1.1.0": {"sha512": "pNroKVjo+rDqlxNG5PXkRLpfSCuDOBY0ri6jp9PLe505ljqwhwZz8ospy2vWhQlFu5GkIesh3FcDs4n7sWZODA==", "type": "package", "path": "serilog.formatting.compact/1.1.0", "files": [".nupkg.metadata", ".signature.p7s", "lib/net452/Serilog.Formatting.Compact.dll", "lib/net452/Serilog.Formatting.Compact.xml", "lib/netstandard1.1/Serilog.Formatting.Compact.dll", "lib/netstandard1.1/Serilog.Formatting.Compact.xml", "lib/netstandard2.0/Serilog.Formatting.Compact.dll", "lib/netstandard2.0/Serilog.Formatting.Compact.xml", "serilog.formatting.compact.1.1.0.nupkg.sha512", "serilog.formatting.compact.nuspec"]}, "Serilog.Settings.Configuration/3.3.0": {"sha512": "7GNudISZwqaT902hqEL2OFGTZeUFWfnrNLupJkOqeF41AR3GjcxX+Hwb30xb8gG2/CDXsCMVfF8o0+8KY0fJNg==", "type": "package", "path": "serilog.settings.configuration/3.3.0", "files": [".nupkg.metadata", ".signature.p7s", "icon.png", "lib/net451/Serilog.Settings.Configuration.dll", "lib/net451/Serilog.Settings.Configuration.xml", "lib/net461/Serilog.Settings.Configuration.dll", "lib/net461/Serilog.Settings.Configuration.xml", "lib/netstandard2.0/Serilog.Settings.Configuration.dll", "lib/netstandard2.0/Serilog.Settings.Configuration.xml", "serilog.settings.configuration.3.3.0.nupkg.sha512", "serilog.settings.configuration.nuspec"]}, "Serilog.Sinks.Console/4.1.0": {"sha512": "K6N5q+5fetjnJPvCmkWOpJ/V8IEIoMIB1s86OzBrbxwTyHxdx3pmz4H+8+O/Dc/ftUX12DM1aynx/dDowkwzqg==", "type": "package", "path": "serilog.sinks.console/4.1.0", "files": [".nupkg.metadata", ".signature.p7s", "icon.png", "lib/net45/Serilog.Sinks.Console.dll", "lib/net45/Serilog.Sinks.Console.xml", "lib/net5.0/Serilog.Sinks.Console.dll", "lib/net5.0/Serilog.Sinks.Console.xml", "lib/netstandard1.3/Serilog.Sinks.Console.dll", "lib/netstandard1.3/Serilog.Sinks.Console.xml", "lib/netstandard2.0/Serilog.Sinks.Console.dll", "lib/netstandard2.0/Serilog.Sinks.Console.xml", "serilog.sinks.console.4.1.0.nupkg.sha512", "serilog.sinks.console.nuspec"]}, "Serilog.Sinks.Debug/2.0.0": {"sha512": "Y6g3OBJ4JzTyyw16fDqtFcQ41qQAydnEvEqmXjhwhgjsnG/FaJ8GUqF5ldsC/bVkK8KYmqrPhDO+tm4dF6xx4A==", "type": "package", "path": "serilog.sinks.debug/2.0.0", "files": [".nupkg.metadata", ".signature.p7s", "icon.png", "lib/net45/Serilog.Sinks.Debug.dll", "lib/net45/Serilog.Sinks.Debug.xml", "lib/net46/Serilog.Sinks.Debug.dll", "lib/net46/Serilog.Sinks.Debug.xml", "lib/netstandard1.0/Serilog.Sinks.Debug.dll", "lib/netstandard1.0/Serilog.Sinks.Debug.xml", "lib/netstandard2.0/Serilog.Sinks.Debug.dll", "lib/netstandard2.0/Serilog.Sinks.Debug.xml", "lib/netstandard2.1/Serilog.Sinks.Debug.dll", "lib/netstandard2.1/Serilog.Sinks.Debug.xml", "serilog.sinks.debug.2.0.0.nupkg.sha512", "serilog.sinks.debug.nuspec"]}, "Serilog.Sinks.File/5.0.0": {"sha512": "uwV5hdhWPwUH1szhO8PJpFiahqXmzPzJT/sOijH/kFgUx+cyoDTMM8MHD0adw9+Iem6itoibbUXHYslzXsLEAg==", "type": "package", "path": "serilog.sinks.file/5.0.0", "files": [".nupkg.metadata", ".signature.p7s", "images/icon.png", "lib/net45/Serilog.Sinks.File.dll", "lib/net45/Serilog.Sinks.File.pdb", "lib/net45/Serilog.Sinks.File.xml", "lib/net5.0/Serilog.Sinks.File.dll", "lib/net5.0/Serilog.Sinks.File.pdb", "lib/net5.0/Serilog.Sinks.File.xml", "lib/netstandard1.3/Serilog.Sinks.File.dll", "lib/netstandard1.3/Serilog.Sinks.File.pdb", "lib/netstandard1.3/Serilog.Sinks.File.xml", "lib/netstandard2.0/Serilog.Sinks.File.dll", "lib/netstandard2.0/Serilog.Sinks.File.pdb", "lib/netstandard2.0/Serilog.Sinks.File.xml", "lib/netstandard2.1/Serilog.Sinks.File.dll", "lib/netstandard2.1/Serilog.Sinks.File.pdb", "lib/netstandard2.1/Serilog.Sinks.File.xml", "serilog.sinks.file.5.0.0.nupkg.sha512", "serilog.sinks.file.nuspec"]}, "Serilog.Sinks.Http/8.0.0": {"sha512": "eHyl2/93Roymf2eudPl/6Eeu2GQ93Ucy4GM1UPF0jyd7CIW8r+Bk5ohdbjjyjB9TQSpP2ovOuj6ltf9DjoWHtg==", "type": "package", "path": "serilog.sinks.http/8.0.0", "files": [".nupkg.metadata", ".signature.p7s", "README.md", "lib/net45/Serilog.Sinks.Http.dll", "lib/net45/Serilog.Sinks.Http.xml", "lib/net461/Serilog.Sinks.Http.dll", "lib/net461/Serilog.Sinks.Http.xml", "lib/netstandard2.0/Serilog.Sinks.Http.dll", "lib/netstandard2.0/Serilog.Sinks.Http.xml", "lib/netstandard2.1/Serilog.Sinks.Http.dll", "lib/netstandard2.1/Serilog.Sinks.Http.xml", "serilog-sink-nuget.png", "serilog.sinks.http.8.0.0.nupkg.sha512", "serilog.sinks.http.nuspec"]}, "Serilog.Sinks.Logz.Io/7.1.0": {"sha512": "uZN5FWMvpk0m/6u7PiKShODbUvfxQZGzK+D91BaxV1ePVJSE0xaqJwCh7rp8/mhPp2C0UPmXOt1hAF306Fzn1Q==", "type": "package", "path": "serilog.sinks.logz.io/7.1.0", "files": [".nupkg.metadata", ".signature.p7s", "icon.png", "lib/net461/Serilog.Sinks.Logz.Io.dll", "lib/net6.0/Serilog.Sinks.Logz.Io.dll", "lib/netstandard2.0/Serilog.Sinks.Logz.Io.dll", "serilog.sinks.logz.io.7.1.0.nupkg.sha512", "serilog.sinks.logz.io.nuspec"]}, "Serilog.Sinks.PeriodicBatching/3.1.0": {"sha512": "NDWR7m3PalVlGEq3rzoktrXikjFMLmpwF0HI4sowo8YDdU+gqPlTHlDQiOGxHfB0sTfjPA9JjA7ctKG9zqjGkw==", "type": "package", "path": "serilog.sinks.periodicbatching/3.1.0", "files": [".nupkg.metadata", ".signature.p7s", "icon.png", "lib/net45/Serilog.Sinks.PeriodicBatching.dll", "lib/net45/Serilog.Sinks.PeriodicBatching.xml", "lib/netstandard1.1/Serilog.Sinks.PeriodicBatching.dll", "lib/netstandard1.1/Serilog.Sinks.PeriodicBatching.xml", "lib/netstandard1.2/Serilog.Sinks.PeriodicBatching.dll", "lib/netstandard1.2/Serilog.Sinks.PeriodicBatching.xml", "lib/netstandard2.0/Serilog.Sinks.PeriodicBatching.dll", "lib/netstandard2.0/Serilog.Sinks.PeriodicBatching.xml", "lib/netstandard2.1/Serilog.Sinks.PeriodicBatching.dll", "lib/netstandard2.1/Serilog.Sinks.PeriodicBatching.xml", "serilog.sinks.periodicbatching.3.1.0.nupkg.sha512", "serilog.sinks.periodicbatching.nuspec"]}, "SharpCompress/0.30.1": {"sha512": "XqD4TpfyYGa7QTPzaGlMVbcecKnXy4YmYLDWrU+JIj7IuRNl7DH2END+Ll7ekWIY8o3dAMWLFDE1xdhfIWD1nw==", "type": "package", "path": "sharpcompress/0.30.1", "files": [".nupkg.metadata", ".signature.p7s", "lib/net461/SharpCompress.dll", "lib/net5.0/SharpCompress.dll", "lib/netcoreapp3.1/SharpCompress.dll", "lib/netstandard2.0/SharpCompress.dll", "lib/netstandard2.1/SharpCompress.dll", "sharpcompress.0.30.1.nupkg.sha512", "sharpcompress.nuspec"]}, "Snappier/1.0.0": {"sha512": "rFtK2KEI9hIe8gtx3a0YDXdHOpedIf9wYCEYtBEmtlyiWVX3XlCNV03JrmmAi/Cdfn7dxK+k0sjjcLv4fpHnqA==", "type": "package", "path": "snappier/1.0.0", "files": [".nupkg.metadata", ".signature.p7s", "COPYING.txt", "lib/net5.0/Snappier.dll", "lib/net5.0/Snappier.xml", "lib/netcoreapp3.0/Snappier.dll", "lib/netcoreapp3.0/Snappier.xml", "lib/netstandard2.0/Snappier.dll", "lib/netstandard2.0/Snappier.xml", "lib/netstandard2.1/Snappier.dll", "lib/netstandard2.1/Snappier.xml", "snappier.1.0.0.nupkg.sha512", "snappier.nuspec"]}, "starkbank-ecdsa/1.3.3": {"sha512": "OblOaKb1enXn+dSp7tsx9yjwV+/BEKM9jFhshIkZTwCk7LuTFTp+wSon6rFzuPiIiTGtvVWQNUw2slHjGktJog==", "type": "package", "path": "starkbank-ecdsa/1.3.3", "files": [".nupkg.metadata", ".signature.p7s", "lib/net40/StarkbankEcdsa.dll", "lib/net452/StarkbankEcdsa.dll", "lib/netstandard1.3/StarkbankEcdsa.dll", "lib/netstandard2.0/StarkbankEcdsa.dll", "lib/netstandard2.1/StarkbankEcdsa.dll", "starkbank-ecdsa.1.3.3.nupkg.sha512", "starkbank-ecdsa.nuspec"]}, "System.Buffers/4.5.1": {"sha512": "Rw7ijyl1qqRS0YQD/WycNst8hUUMgrMH4FCn1nNm27M4VxchZ1js3fVjQaANHO5f3sN4isvP4a+Met9Y4YomAg==", "type": "package", "path": "system.buffers/4.5.1", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "lib/net461/System.Buffers.dll", "lib/net461/System.Buffers.xml", "lib/netcoreapp2.0/_._", "lib/netstandard1.1/System.Buffers.dll", "lib/netstandard1.1/System.Buffers.xml", "lib/netstandard2.0/System.Buffers.dll", "lib/netstandard2.0/System.Buffers.xml", "lib/uap10.0.16299/_._", "ref/net45/System.Buffers.dll", "ref/net45/System.Buffers.xml", "ref/netcoreapp2.0/_._", "ref/netstandard1.1/System.Buffers.dll", "ref/netstandard1.1/System.Buffers.xml", "ref/netstandard2.0/System.Buffers.dll", "ref/netstandard2.0/System.Buffers.xml", "ref/uap10.0.16299/_._", "system.buffers.4.5.1.nupkg.sha512", "system.buffers.nuspec", "useSharedDesignerContext.txt", "version.txt"]}, "System.Diagnostics.DiagnosticSource/6.0.1": {"sha512": "KiLYDu2k2J82Q9BJpWiuQqCkFjRBWVq4jDzKKWawVi9KWzyD0XG3cmfX0vqTQlL14Wi9EufJrbL0+KCLTbqWiQ==", "type": "package", "path": "system.diagnostics.diagnosticsource/6.0.1", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/netcoreapp2.0/System.Diagnostics.DiagnosticSource.targets", "buildTransitive/netcoreapp3.1/_._", "lib/net461/System.Diagnostics.DiagnosticSource.dll", "lib/net461/System.Diagnostics.DiagnosticSource.xml", "lib/net5.0/System.Diagnostics.DiagnosticSource.dll", "lib/net5.0/System.Diagnostics.DiagnosticSource.xml", "lib/net6.0/System.Diagnostics.DiagnosticSource.dll", "lib/net6.0/System.Diagnostics.DiagnosticSource.xml", "lib/netstandard2.0/System.Diagnostics.DiagnosticSource.dll", "lib/netstandard2.0/System.Diagnostics.DiagnosticSource.xml", "system.diagnostics.diagnosticsource.6.0.1.nupkg.sha512", "system.diagnostics.diagnosticsource.nuspec", "useSharedDesignerContext.txt"]}, "System.IO.FileSystem.AccessControl/5.0.0": {"sha512": "SxHB3nuNrpptVk+vZ/F+7OHEpoHUIKKMl02bUmYHQr1r+glbZQxs7pRtsf4ENO29TVm2TH3AEeep2fJcy92oYw==", "type": "package", "path": "system.io.filesystem.accesscontrol/5.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "lib/net46/System.IO.FileSystem.AccessControl.dll", "lib/net461/System.IO.FileSystem.AccessControl.dll", "lib/net461/System.IO.FileSystem.AccessControl.xml", "lib/netstandard1.3/System.IO.FileSystem.AccessControl.dll", "lib/netstandard2.0/System.IO.FileSystem.AccessControl.dll", "lib/netstandard2.0/System.IO.FileSystem.AccessControl.xml", "ref/net46/System.IO.FileSystem.AccessControl.dll", "ref/net461/System.IO.FileSystem.AccessControl.dll", "ref/net461/System.IO.FileSystem.AccessControl.xml", "ref/netstandard1.3/System.IO.FileSystem.AccessControl.dll", "ref/netstandard1.3/System.IO.FileSystem.AccessControl.xml", "ref/netstandard1.3/de/System.IO.FileSystem.AccessControl.xml", "ref/netstandard1.3/es/System.IO.FileSystem.AccessControl.xml", "ref/netstandard1.3/fr/System.IO.FileSystem.AccessControl.xml", "ref/netstandard1.3/it/System.IO.FileSystem.AccessControl.xml", "ref/netstandard1.3/ja/System.IO.FileSystem.AccessControl.xml", "ref/netstandard1.3/ko/System.IO.FileSystem.AccessControl.xml", "ref/netstandard1.3/ru/System.IO.FileSystem.AccessControl.xml", "ref/netstandard1.3/zh-hans/System.IO.FileSystem.AccessControl.xml", "ref/netstandard1.3/zh-hant/System.IO.FileSystem.AccessControl.xml", "ref/netstandard2.0/System.IO.FileSystem.AccessControl.dll", "ref/netstandard2.0/System.IO.FileSystem.AccessControl.xml", "runtimes/win/lib/net46/System.IO.FileSystem.AccessControl.dll", "runtimes/win/lib/net461/System.IO.FileSystem.AccessControl.dll", "runtimes/win/lib/net461/System.IO.FileSystem.AccessControl.xml", "runtimes/win/lib/netstandard1.3/System.IO.FileSystem.AccessControl.dll", "runtimes/win/lib/netstandard2.0/System.IO.FileSystem.AccessControl.dll", "runtimes/win/lib/netstandard2.0/System.IO.FileSystem.AccessControl.xml", "system.io.filesystem.accesscontrol.5.0.0.nupkg.sha512", "system.io.filesystem.accesscontrol.nuspec", "useSharedDesignerContext.txt", "version.txt"]}, "System.Memory/4.5.5": {"sha512": "XIWiDvKPXaTveaB7HVganDlOCRoj03l+jrwNvcge/t8vhGYKvqV+dMv6G4SAX2NoNmN0wZfVPTAlFwZcZvVOUw==", "type": "package", "path": "system.memory/4.5.5", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "lib/net461/System.Memory.dll", "lib/net461/System.Memory.xml", "lib/netcoreapp2.1/_._", "lib/netstandard1.1/System.Memory.dll", "lib/netstandard1.1/System.Memory.xml", "lib/netstandard2.0/System.Memory.dll", "lib/netstandard2.0/System.Memory.xml", "ref/netcoreapp2.1/_._", "system.memory.4.5.5.nupkg.sha512", "system.memory.nuspec", "useSharedDesignerContext.txt", "version.txt"]}, "System.Memory.Data/1.0.2": {"sha512": "JGkzeqgBsiZwKJZ1IxPNsDFZDhUvuEdX8L8BDC8N3KOj+6zMcNU28CNN59TpZE/VJYy9cP+5M+sbxtWJx3/xtw==", "type": "package", "path": "system.memory.data/1.0.2", "files": [".nupkg.metadata", ".signature.p7s", "CHANGELOG.md", "DotNetPackageIcon.png", "README.md", "lib/net461/System.Memory.Data.dll", "lib/net461/System.Memory.Data.xml", "lib/netstandard2.0/System.Memory.Data.dll", "lib/netstandard2.0/System.Memory.Data.xml", "system.memory.data.1.0.2.nupkg.sha512", "system.memory.data.nuspec"]}, "System.Numerics.Vectors/4.5.0": {"sha512": "QQTlPTl06J/iiDbJCiepZ4H//BVraReU4O4EoRw1U02H5TLUIT7xn3GnDp9AXPSlJUDyFs4uWjWafNX6WrAojQ==", "type": "package", "path": "system.numerics.vectors/4.5.0", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net46/System.Numerics.Vectors.dll", "lib/net46/System.Numerics.Vectors.xml", "lib/netcoreapp2.0/_._", "lib/netstandard1.0/System.Numerics.Vectors.dll", "lib/netstandard1.0/System.Numerics.Vectors.xml", "lib/netstandard2.0/System.Numerics.Vectors.dll", "lib/netstandard2.0/System.Numerics.Vectors.xml", "lib/portable-net45+win8+wp8+wpa81/System.Numerics.Vectors.dll", "lib/portable-net45+win8+wp8+wpa81/System.Numerics.Vectors.xml", "lib/uap10.0.16299/_._", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/MonoAndroid10/_._", "ref/MonoTouch10/_._", "ref/net45/System.Numerics.Vectors.dll", "ref/net45/System.Numerics.Vectors.xml", "ref/net46/System.Numerics.Vectors.dll", "ref/net46/System.Numerics.Vectors.xml", "ref/netcoreapp2.0/_._", "ref/netstandard1.0/System.Numerics.Vectors.dll", "ref/netstandard1.0/System.Numerics.Vectors.xml", "ref/netstandard2.0/System.Numerics.Vectors.dll", "ref/netstandard2.0/System.Numerics.Vectors.xml", "ref/uap10.0.16299/_._", "ref/xamarinios10/_._", "ref/xamarinmac20/_._", "ref/xamarintvos10/_._", "ref/xamarinwatchos10/_._", "system.numerics.vectors.4.5.0.nupkg.sha512", "system.numerics.vectors.nuspec", "useSharedDesignerContext.txt", "version.txt"]}, "System.Runtime.CompilerServices.Unsafe/6.0.0": {"sha512": "/iUeP3tq1S0XdNNoMz5C9twLSrM/TH+qElHkXWaPvuNOt+99G75NrV0OS2EqHx5wMN7popYjpc8oTjC1y16DLg==", "type": "package", "path": "system.runtime.compilerservices.unsafe/6.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/netcoreapp2.0/System.Runtime.CompilerServices.Unsafe.targets", "buildTransitive/netcoreapp3.1/_._", "lib/net461/System.Runtime.CompilerServices.Unsafe.dll", "lib/net461/System.Runtime.CompilerServices.Unsafe.xml", "lib/net6.0/System.Runtime.CompilerServices.Unsafe.dll", "lib/net6.0/System.Runtime.CompilerServices.Unsafe.xml", "lib/netcoreapp3.1/System.Runtime.CompilerServices.Unsafe.dll", "lib/netcoreapp3.1/System.Runtime.CompilerServices.Unsafe.xml", "lib/netstandard2.0/System.Runtime.CompilerServices.Unsafe.dll", "lib/netstandard2.0/System.Runtime.CompilerServices.Unsafe.xml", "system.runtime.compilerservices.unsafe.6.0.0.nupkg.sha512", "system.runtime.compilerservices.unsafe.nuspec", "useSharedDesignerContext.txt"]}, "System.Security.AccessControl/5.0.0": {"sha512": "dagJ1mHZO3Ani8GH0PHpPEe/oYO+rVdbQjvjJkBRNQkX4t0r1iaeGn8+/ybkSLEan3/slM0t59SVdHzuHf2jmw==", "type": "package", "path": "system.security.accesscontrol/5.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "lib/net46/System.Security.AccessControl.dll", "lib/net461/System.Security.AccessControl.dll", "lib/net461/System.Security.AccessControl.xml", "lib/netstandard1.3/System.Security.AccessControl.dll", "lib/netstandard2.0/System.Security.AccessControl.dll", "lib/netstandard2.0/System.Security.AccessControl.xml", "lib/uap10.0.16299/_._", "ref/net46/System.Security.AccessControl.dll", "ref/net461/System.Security.AccessControl.dll", "ref/net461/System.Security.AccessControl.xml", "ref/netstandard1.3/System.Security.AccessControl.dll", "ref/netstandard1.3/System.Security.AccessControl.xml", "ref/netstandard1.3/de/System.Security.AccessControl.xml", "ref/netstandard1.3/es/System.Security.AccessControl.xml", "ref/netstandard1.3/fr/System.Security.AccessControl.xml", "ref/netstandard1.3/it/System.Security.AccessControl.xml", "ref/netstandard1.3/ja/System.Security.AccessControl.xml", "ref/netstandard1.3/ko/System.Security.AccessControl.xml", "ref/netstandard1.3/ru/System.Security.AccessControl.xml", "ref/netstandard1.3/zh-hans/System.Security.AccessControl.xml", "ref/netstandard1.3/zh-hant/System.Security.AccessControl.xml", "ref/netstandard2.0/System.Security.AccessControl.dll", "ref/netstandard2.0/System.Security.AccessControl.xml", "ref/uap10.0.16299/_._", "runtimes/win/lib/net46/System.Security.AccessControl.dll", "runtimes/win/lib/net461/System.Security.AccessControl.dll", "runtimes/win/lib/net461/System.Security.AccessControl.xml", "runtimes/win/lib/netcoreapp2.0/System.Security.AccessControl.dll", "runtimes/win/lib/netcoreapp2.0/System.Security.AccessControl.xml", "runtimes/win/lib/netstandard1.3/System.Security.AccessControl.dll", "runtimes/win/lib/uap10.0.16299/_._", "system.security.accesscontrol.5.0.0.nupkg.sha512", "system.security.accesscontrol.nuspec", "useSharedDesignerContext.txt", "version.txt"]}, "System.Security.Cryptography.ProtectedData/4.7.0": {"sha512": "ehYW0m9ptxpGWvE4zgqongBVWpSDU/JCFD4K7krxkQwSz/sFQjEXCUqpvencjy6DYDbn7Ig09R8GFffu8TtneQ==", "type": "package", "path": "system.security.cryptography.protecteddata/4.7.0", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net46/System.Security.Cryptography.ProtectedData.dll", "lib/net461/System.Security.Cryptography.ProtectedData.dll", "lib/net461/System.Security.Cryptography.ProtectedData.xml", "lib/netstandard1.3/System.Security.Cryptography.ProtectedData.dll", "lib/netstandard2.0/System.Security.Cryptography.ProtectedData.dll", "lib/netstandard2.0/System.Security.Cryptography.ProtectedData.xml", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/MonoAndroid10/_._", "ref/MonoTouch10/_._", "ref/net46/System.Security.Cryptography.ProtectedData.dll", "ref/net461/System.Security.Cryptography.ProtectedData.dll", "ref/net461/System.Security.Cryptography.ProtectedData.xml", "ref/netstandard1.3/System.Security.Cryptography.ProtectedData.dll", "ref/netstandard2.0/System.Security.Cryptography.ProtectedData.dll", "ref/netstandard2.0/System.Security.Cryptography.ProtectedData.xml", "ref/xamarinios10/_._", "ref/xamarinmac20/_._", "ref/xamarintvos10/_._", "ref/xamarinwatchos10/_._", "runtimes/win/lib/net46/System.Security.Cryptography.ProtectedData.dll", "runtimes/win/lib/net461/System.Security.Cryptography.ProtectedData.dll", "runtimes/win/lib/net461/System.Security.Cryptography.ProtectedData.xml", "runtimes/win/lib/netstandard1.3/System.Security.Cryptography.ProtectedData.dll", "runtimes/win/lib/netstandard2.0/System.Security.Cryptography.ProtectedData.dll", "runtimes/win/lib/netstandard2.0/System.Security.Cryptography.ProtectedData.xml", "system.security.cryptography.protecteddata.4.7.0.nupkg.sha512", "system.security.cryptography.protecteddata.nuspec", "useSharedDesignerContext.txt", "version.txt"]}, "System.Security.Principal.Windows/5.0.0": {"sha512": "t0MGLukB5WAVU9bO3MGzvlGnyJPgUlcwerXn1kzBRjwLKixT96XV0Uza41W49gVd8zEMFu9vQEFlv0IOrytICA==", "type": "package", "path": "system.security.principal.windows/5.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "lib/net46/System.Security.Principal.Windows.dll", "lib/net461/System.Security.Principal.Windows.dll", "lib/net461/System.Security.Principal.Windows.xml", "lib/netstandard1.3/System.Security.Principal.Windows.dll", "lib/netstandard2.0/System.Security.Principal.Windows.dll", "lib/netstandard2.0/System.Security.Principal.Windows.xml", "lib/uap10.0.16299/_._", "ref/net46/System.Security.Principal.Windows.dll", "ref/net461/System.Security.Principal.Windows.dll", "ref/net461/System.Security.Principal.Windows.xml", "ref/netcoreapp3.0/System.Security.Principal.Windows.dll", "ref/netcoreapp3.0/System.Security.Principal.Windows.xml", "ref/netstandard1.3/System.Security.Principal.Windows.dll", "ref/netstandard1.3/System.Security.Principal.Windows.xml", "ref/netstandard1.3/de/System.Security.Principal.Windows.xml", "ref/netstandard1.3/es/System.Security.Principal.Windows.xml", "ref/netstandard1.3/fr/System.Security.Principal.Windows.xml", "ref/netstandard1.3/it/System.Security.Principal.Windows.xml", "ref/netstandard1.3/ja/System.Security.Principal.Windows.xml", "ref/netstandard1.3/ko/System.Security.Principal.Windows.xml", "ref/netstandard1.3/ru/System.Security.Principal.Windows.xml", "ref/netstandard1.3/zh-hans/System.Security.Principal.Windows.xml", "ref/netstandard1.3/zh-hant/System.Security.Principal.Windows.xml", "ref/netstandard2.0/System.Security.Principal.Windows.dll", "ref/netstandard2.0/System.Security.Principal.Windows.xml", "ref/uap10.0.16299/_._", "runtimes/unix/lib/netcoreapp2.0/System.Security.Principal.Windows.dll", "runtimes/unix/lib/netcoreapp2.0/System.Security.Principal.Windows.xml", "runtimes/unix/lib/netcoreapp2.1/System.Security.Principal.Windows.dll", "runtimes/unix/lib/netcoreapp2.1/System.Security.Principal.Windows.xml", "runtimes/win/lib/net46/System.Security.Principal.Windows.dll", "runtimes/win/lib/net461/System.Security.Principal.Windows.dll", "runtimes/win/lib/net461/System.Security.Principal.Windows.xml", "runtimes/win/lib/netcoreapp2.0/System.Security.Principal.Windows.dll", "runtimes/win/lib/netcoreapp2.0/System.Security.Principal.Windows.xml", "runtimes/win/lib/netcoreapp2.1/System.Security.Principal.Windows.dll", "runtimes/win/lib/netcoreapp2.1/System.Security.Principal.Windows.xml", "runtimes/win/lib/netstandard1.3/System.Security.Principal.Windows.dll", "runtimes/win/lib/uap10.0.16299/_._", "system.security.principal.windows.5.0.0.nupkg.sha512", "system.security.principal.windows.nuspec", "useSharedDesignerContext.txt", "version.txt"]}, "System.Text.Encodings.Web/8.0.0": {"sha512": "yev/k9GHAEGx2Rg3/tU6MQh4HGBXJs70y7j1LaM1i/ER9po+6nnQ6RRqTJn1E7Xu0fbIFK80Nh5EoODxrbxwBQ==", "type": "package", "path": "system.text.encodings.web/8.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/System.Text.Encodings.Web.targets", "buildTransitive/net462/_._", "buildTransitive/net6.0/_._", "buildTransitive/netcoreapp2.0/System.Text.Encodings.Web.targets", "lib/net462/System.Text.Encodings.Web.dll", "lib/net462/System.Text.Encodings.Web.xml", "lib/net6.0/System.Text.Encodings.Web.dll", "lib/net6.0/System.Text.Encodings.Web.xml", "lib/net7.0/System.Text.Encodings.Web.dll", "lib/net7.0/System.Text.Encodings.Web.xml", "lib/net8.0/System.Text.Encodings.Web.dll", "lib/net8.0/System.Text.Encodings.Web.xml", "lib/netstandard2.0/System.Text.Encodings.Web.dll", "lib/netstandard2.0/System.Text.Encodings.Web.xml", "runtimes/browser/lib/net6.0/System.Text.Encodings.Web.dll", "runtimes/browser/lib/net6.0/System.Text.Encodings.Web.xml", "runtimes/browser/lib/net7.0/System.Text.Encodings.Web.dll", "runtimes/browser/lib/net7.0/System.Text.Encodings.Web.xml", "runtimes/browser/lib/net8.0/System.Text.Encodings.Web.dll", "runtimes/browser/lib/net8.0/System.Text.Encodings.Web.xml", "system.text.encodings.web.8.0.0.nupkg.sha512", "system.text.encodings.web.nuspec", "useSharedDesignerContext.txt"]}, "System.Text.Json/8.0.5": {"sha512": "0f1B50Ss7rqxXiaBJyzUu9bWFOO2/zSlifZ/UNMdiIpDYe4cY4LQQicP4nirK1OS31I43rn062UIJ1Q9bpmHpg==", "type": "package", "path": "system.text.json/8.0.5", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "PACKAGE.md", "THIRD-PARTY-NOTICES.TXT", "analyzers/dotnet/roslyn3.11/cs/System.Text.Json.SourceGeneration.dll", "analyzers/dotnet/roslyn3.11/cs/cs/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn3.11/cs/de/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn3.11/cs/es/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn3.11/cs/fr/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn3.11/cs/it/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn3.11/cs/ja/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn3.11/cs/ko/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn3.11/cs/pl/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn3.11/cs/pt-BR/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn3.11/cs/ru/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn3.11/cs/tr/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn3.11/cs/zh-Hans/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn3.11/cs/zh-Hant/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.0/cs/System.Text.Json.SourceGeneration.dll", "analyzers/dotnet/roslyn4.0/cs/cs/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.0/cs/de/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.0/cs/es/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.0/cs/fr/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.0/cs/it/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.0/cs/ja/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.0/cs/ko/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.0/cs/pl/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.0/cs/pt-BR/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.0/cs/ru/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.0/cs/tr/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.0/cs/zh-Hans/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.0/cs/zh-Hant/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.4/cs/System.Text.Json.SourceGeneration.dll", "analyzers/dotnet/roslyn4.4/cs/cs/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.4/cs/de/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.4/cs/es/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.4/cs/fr/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.4/cs/it/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.4/cs/ja/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.4/cs/ko/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.4/cs/pl/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.4/cs/pt-BR/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.4/cs/ru/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.4/cs/tr/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.4/cs/zh-Hans/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.4/cs/zh-Hant/System.Text.Json.SourceGeneration.resources.dll", "buildTransitive/net461/System.Text.Json.targets", "buildTransitive/net462/System.Text.Json.targets", "buildTransitive/net6.0/System.Text.Json.targets", "buildTransitive/netcoreapp2.0/System.Text.Json.targets", "buildTransitive/netstandard2.0/System.Text.Json.targets", "lib/net462/System.Text.Json.dll", "lib/net462/System.Text.Json.xml", "lib/net6.0/System.Text.Json.dll", "lib/net6.0/System.Text.Json.xml", "lib/net7.0/System.Text.Json.dll", "lib/net7.0/System.Text.Json.xml", "lib/net8.0/System.Text.Json.dll", "lib/net8.0/System.Text.Json.xml", "lib/netstandard2.0/System.Text.Json.dll", "lib/netstandard2.0/System.Text.Json.xml", "system.text.json.8.0.5.nupkg.sha512", "system.text.json.nuspec", "useSharedDesignerContext.txt"]}, "System.Threading.Tasks.Extensions/4.5.4": {"sha512": "zteT+G8xuGu6mS+mzDzYXbzS7rd3K6Fjb9RiZlYlJPam2/hU7JCBZBVEcywNuR+oZ1ncTvc/cq0faRr3P01OVg==", "type": "package", "path": "system.threading.tasks.extensions/4.5.4", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net461/System.Threading.Tasks.Extensions.dll", "lib/net461/System.Threading.Tasks.Extensions.xml", "lib/netcoreapp2.1/_._", "lib/netstandard1.0/System.Threading.Tasks.Extensions.dll", "lib/netstandard1.0/System.Threading.Tasks.Extensions.xml", "lib/netstandard2.0/System.Threading.Tasks.Extensions.dll", "lib/netstandard2.0/System.Threading.Tasks.Extensions.xml", "lib/portable-net45+win8+wp8+wpa81/System.Threading.Tasks.Extensions.dll", "lib/portable-net45+win8+wp8+wpa81/System.Threading.Tasks.Extensions.xml", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/MonoAndroid10/_._", "ref/MonoTouch10/_._", "ref/netcoreapp2.1/_._", "ref/xamarinios10/_._", "ref/xamarinmac20/_._", "ref/xamarintvos10/_._", "ref/xamarinwatchos10/_._", "system.threading.tasks.extensions.4.5.4.nupkg.sha512", "system.threading.tasks.extensions.nuspec", "useSharedDesignerContext.txt", "version.txt"]}, "TinyHelpers/3.1.18": {"sha512": "8rooJXh6newc4y+Pt0yPwXvquGf3Hl1WZpdXNdoVVDkdPLYXNrBVLXLhTeBDDYZtBpHcr/75k6N8QVXVgxiWIg==", "type": "package", "path": "tinyhelpers/3.1.18", "files": [".nupkg.metadata", ".signature.p7s", "README.md", "Toolbox.png", "lib/net6.0/TinyHelpers.dll", "lib/net6.0/TinyHelpers.xml", "lib/net7.0/TinyHelpers.dll", "lib/net7.0/TinyHelpers.xml", "lib/net8.0/TinyHelpers.dll", "lib/net8.0/TinyHelpers.xml", "lib/netstandard2.0/TinyHelpers.dll", "lib/netstandard2.0/TinyHelpers.xml", "tinyhelpers.3.1.18.nupkg.sha512", "tinyhelpers.nuspec"]}, "ZstdSharp.Port/0.7.3": {"sha512": "U9Ix4l4cl58Kzz1rJzj5hoVTjmbx1qGMwzAcbv1j/d3NzrFaESIurQyg+ow4mivCgkE3S413y+U9k4WdnEIkRA==", "type": "package", "path": "zstdsharp.port/0.7.3", "files": [".nupkg.metadata", ".signature.p7s", "lib/net461/ZstdSharp.dll", "lib/net5.0/ZstdSharp.dll", "lib/net6.0/ZstdSharp.dll", "lib/net7.0/ZstdSharp.dll", "lib/netcoreapp3.1/ZstdSharp.dll", "lib/netstandard2.0/ZstdSharp.dll", "lib/netstandard2.1/ZstdSharp.dll", "zstdsharp.port.0.7.3.nupkg.sha512", "zstdsharp.port.nuspec"]}, "BlueTape.Services.DecisionEngine.BusinessLogic/1.0.0": {"type": "project", "path": "../../../BlueTape.Services.DecisionEngine.BusinessLogic/BlueTape.Services.DecisionEngine.BusinessLogic.csproj", "msbuildProject": "../../../BlueTape.Services.DecisionEngine.BusinessLogic/BlueTape.Services.DecisionEngine.BusinessLogic.csproj"}, "BlueTape.Services.DecisionEngine.DataAccess.External/1.0.0": {"type": "project", "path": "../../../BlueTape.Services.DecisionEngine.DataAccess.External/BlueTape.Services.DecisionEngine.DataAccess.External.csproj", "msbuildProject": "../../../BlueTape.Services.DecisionEngine.DataAccess.External/BlueTape.Services.DecisionEngine.DataAccess.External.csproj"}, "BlueTape.Services.DecisionEngine.Domain/1.0.0": {"type": "project", "path": "../../../BlueTape.Services.DecisionEngine.Domain/BlueTape.Services.DecisionEngine.Domain.csproj", "msbuildProject": "../../../BlueTape.Services.DecisionEngine.Domain/BlueTape.Services.DecisionEngine.Domain.csproj"}, "BlueTape.Services.DecisionEngine.Infrastructure/1.0.0": {"type": "project", "path": "../../../BlueTape.Services.DecisionEngine.Infrastructure/BlueTape.Services.DecisionEngine.Infrastructure.csproj", "msbuildProject": "../../../BlueTape.Services.DecisionEngine.Infrastructure/BlueTape.Services.DecisionEngine.Infrastructure.csproj"}, "BlueTape.Services.DecisionEngine.PolicyRulesFramework.Domain/1.0.0": {"type": "project", "path": "../../../BlueTape.Services.DecisionEngine.PolicyRulesFramework.Domain/BlueTape.Services.DecisionEngine.PolicyRulesFramework.Domain.csproj", "msbuildProject": "../../../BlueTape.Services.DecisionEngine.PolicyRulesFramework.Domain/BlueTape.Services.DecisionEngine.PolicyRulesFramework.Domain.csproj"}, "BlueTape.Services.DecisionEngine.PolicyRulesFramework.Infrasrtucture/1.0.0": {"type": "project", "path": "../../../BlueTape.Services.DecisionEngine.PolicyRulesFramework.Infrasrtucture/BlueTape.Services.DecisionEngine.PolicyRulesFramework.Infrasrtucture.csproj", "msbuildProject": "../../../BlueTape.Services.DecisionEngine.PolicyRulesFramework.Infrasrtucture/BlueTape.Services.DecisionEngine.PolicyRulesFramework.Infrasrtucture.csproj"}, "BlueTape.Services.DecisionEngine.PolicyRulesFramework.PolicyRulesEngine/1.0.0": {"type": "project", "path": "../../../BlueTape.Services.DecisionEngine.PolicyRulesFramework.PolicyRulesEngine/BlueTape.Services.DecisionEngine.PolicyRulesFramework.PolicyRulesEngine.csproj", "msbuildProject": "../../../BlueTape.Services.DecisionEngine.PolicyRulesFramework.PolicyRulesEngine/BlueTape.Services.DecisionEngine.PolicyRulesFramework.PolicyRulesEngine.csproj"}}, "projectFileDependencyGroups": {"net6.0": ["Amazon.Lambda.Core >= 2.2.0", "Amazon.Lambda.Serialization.SystemTextJson >= 2.4.0", "BlueTape.LambdaBase >= 1.1.1", "BlueTape.Services.DecisionEngine.BusinessLogic >= 1.0.0"]}, "packageFolders": {"C:\\Users\\<USER>\\.nuget\\packages\\": {}, "C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages": {}}, "project": {"version": "1.0.0", "restore": {"projectUniqueName": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\decision-engine\\src\\lambdas\\DecisionEngine\\BlueTape.Services.DecisionEngine.KybStep\\BlueTape.Services.DecisionEngine.KybStep.csproj", "projectName": "BlueTape.Services.DecisionEngine.KybStep", "projectPath": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\decision-engine\\src\\lambdas\\DecisionEngine\\BlueTape.Services.DecisionEngine.KybStep\\BlueTape.Services.DecisionEngine.KybStep.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\decision-engine\\src\\lambdas\\DecisionEngine\\BlueTape.Services.DecisionEngine.KybStep\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\source\\repos\\BlueTape\\decision-engine\\NuGet.Config", "C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net6.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}, "https://nuget.pkg.github.com/bluetape-org/index.json": {}}, "frameworks": {"net6.0": {"targetAlias": "net6.0", "projectReferences": {"C:\\Users\\<USER>\\source\\repos\\BlueTape\\decision-engine\\src\\BlueTape.Services.DecisionEngine.BusinessLogic\\BlueTape.Services.DecisionEngine.BusinessLogic.csproj": {"projectPath": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\decision-engine\\src\\BlueTape.Services.DecisionEngine.BusinessLogic\\BlueTape.Services.DecisionEngine.BusinessLogic.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"net6.0": {"targetAlias": "net6.0", "dependencies": {"Amazon.Lambda.Core": {"target": "Package", "version": "[2.2.0, )"}, "Amazon.Lambda.Serialization.SystemTextJson": {"target": "Package", "version": "[2.4.0, )"}, "BlueTape.LambdaBase": {"target": "Package", "version": "[1.1.1, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "downloadDependencies": [{"name": "Microsoft.AspNetCore.App.Ref", "version": "[6.0.36, 6.0.36]"}, {"name": "Microsoft.NETCore.App.Crossgen2.win-x64", "version": "[6.0.36, 6.0.36]"}, {"name": "Microsoft.NETCore.App.Ref", "version": "[6.0.36, 6.0.36]"}, {"name": "Microsoft.WindowsDesktop.App.Ref", "version": "[6.0.36, 6.0.36]"}], "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.301\\RuntimeIdentifierGraph.json"}}}}