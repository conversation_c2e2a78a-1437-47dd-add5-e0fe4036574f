openapi: "3.0.0"
info:
  version: "0.0.1"
  title: "Ledger Service API"
  description: |
    API definition of Ledger service
servers:
  - url: TBD-Dev
    description: Development server
  - url: TBD-Prod
    description: Production server
paths:
  /operations/templates:
    post:
      tags:
        - templates
      summary: Adds a new ledger operation template
      description: Adds a new ledger operation template
      operationId: createOperationTemplate
      requestBody:
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/CreateOperationTemplateRequest"
      responses:
        201:
          description: Operation template created.
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/OperationTemplate"
        400:
          description: Invalid request
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/ErrorResponse"
        401:
          description: Unauthorized
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/ErrorResponse"
        500:
          description: Unexpected error
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/ErrorResponse"
    get:
      tags:
        - templates
      summary: Get operation templates, filtered by code or id
      description: Get operation templates, filtered by code or id
      operationId: getOperationTemplates
      parameters:
        - name: id
          description: The template id
          in: query
          required: false
          schema:
            type: string
        - name: code
          description: The template code
          in: query
          required: false
          schema:
            type: string
      responses:
        200:
          description: Operation templates
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: "#/components/schemas/OperationTemplate"
        400:
          description: Invalid request
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/ErrorResponse"
        401:
          description: Unauthorized
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/ErrorResponse"
        500:
          description: Unexpected error
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/ErrorResponse"
  /operations/templates/{id}:
    put:
      tags:
        - templates
      parameters:
        - name: id
          description: The template id
          in: path
          required: true
          schema:
            type: string
      summary: Updates operation template by id
      description: Updates operation template by id
      operationId: updateOperationTemplateById
      requestBody:
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/UpdateOperationTemplateRequest"
      responses:
        200:
          description: Operation template
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: "#/components/schemas/OperationTemplate"
        400:
          description: Invalid request
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/ErrorResponse"
        401:
          description: Unauthorized
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/ErrorResponse"
        500:
          description: Unexpected error
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/ErrorResponse"
  /operations:
    post:
      tags:
        - operations
      summary: Adds a new ledger operation (by SQS)
      description: Adds a new ledger operation (by SQS)
      operationId: createOperation
      requestBody:
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/CreateOperationRequest"
      responses:
        201:
          description: Operation created.
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/Operation"
        400:
          description: Invalid request
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/ErrorResponse"
        401:
          description: Unauthorized
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/ErrorResponse"
        500:
          description: Unexpected error
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/ErrorResponse"
    get:
      tags:
        - operations
      summary: Gets ledger operations, filtered by template code or id
      description: Gets ledger operations, filtered by template code or id
      operationId: getOperations
      parameters:
        - name: id
          description: The operation id
          in: query
          required: false
          schema:
            type: string
        - name: code
          description: The template code
          in: query
          required: false
          schema:
            type: string
      responses:
        200:
          description: Operations
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: "#/components/schemas/Operation"
        400:
          description: Invalid request
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/ErrorResponse"
        401:
          description: Unauthorized
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/ErrorResponse"
        500:
          description: Unexpected error
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/ErrorResponse"
  /operations/simple:
    post:
      tags:
        - operations
      summary: Adds a new ledger operation (by SQS)
      description: Adds a new ledger operation (by SQS)
      operationId: createSimpleOperation
      requestBody:
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/CreateSimpleOperationRequest"
      responses:
        201:
          description: Operation created.
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/Operation"
        400:
          description: Invalid request
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/ErrorResponse"
        401:
          description: Unauthorized
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/ErrorResponse"
        500:
          description: Unexpected error
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/ErrorResponse"  
  /operations/items:
    get:
      tags:
        - operations
      summary: Gets raw operation items
      description: Gets raw operation items
      operationId: getOperationItems
      parameters:
        - name: accountId
          description: The ledger account Id
          in: query
          required: false
          schema:
            type: string
        - name: accountOwnerId
          description: The account owner id
          in: query
          required: false
          schema:
            type: string
      responses:
        200:
          description: Accounting items
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: "#/components/schemas/OperationItem"
        400:
          description: Invalid request
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/ErrorResponse"
        401:
          description: Unauthorized
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/ErrorResponse"
        500:
          description: Unexpected error
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/ErrorResponse"
  /administration/correction:
    post:
      tags:
        - administration
      summary: Adds correction
      description: Adds correction
      operationId: createCorrection
      requestBody:
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/CreateCorrectionItemRequest"
      responses:
        201:
          description: Correction item created.
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/CreateCorrectionItemResponse"
        400:
          description: Invalid request
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/ErrorResponse"
        401:
          description: Unauthorized
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/ErrorResponse"
        500:
          description: Unexpected error
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/ErrorResponse"
  /operations/items/balance:
    get:
      tags:
        - operations
      summary: Gets cumulated balance by specific filters
      description: Gets cumulated balance by specific filters
      operationId: getCumulatedBalance
      parameters:
        - name: ledgerAccountId
          description: The ledger account Id
          in: query
          required: false
          schema:
            type: string
        - name: accountOwnerId
          description: The account owner id
          in: query
          required: false
          schema:
            type: string
        - name: loanId
          description: The loan id
          in: query
          required: false
          schema:
            type: string
        - name: invoiceId
          description: The invoice id
          in: query
          required: false
          schema:
            type: string
        - name: projectId
          description: The project id
          in: query
          required: false
          schema:
            type: string
        - name: toDate
          description: Get balance until this date
          in: query
          required: false
          schema:
            type: string
            format: date
            example: 2023-07-10
      responses:
        200:
          description: Accounting balances.
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: "#/components/schemas/AccountingBalance"
        400:
          description: Invalid request
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/ErrorResponse"
        401:
          description: Unauthorized
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/ErrorResponse"
        500:
          description: Unexpected error
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/ErrorResponse"
  /administration/balance:
    post:
      tags:
        - administration
      summary: Adds balance for a specific account
      description: Adds balance for a specific account
      operationId: createBalance
      requestBody:
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/CreateBalanceItemRequest"
      responses:
        201:
          description: Balance item created.
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/CreateBalanceItemResponse"
        400:
          description: Invalid request
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/ErrorResponse"
        401:
          description: Unauthorized
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/ErrorResponse"
        500:
          description: Unexpected error
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/ErrorResponse"
  /ledgerAccounts:
    get:
      tags:
        - ledgeraccounts
      summary: Gets ledger accounts
      description: Gets ledger accounts
      operationId: getLedgerAccounts
      responses:
        200:
          description: The ledger accounts
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: "#/components/schemas/LedgerAccount"
        400:
          description: Invalid request
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/ErrorResponse"
        401:
          description: Unauthorized
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/ErrorResponse"
        500:
          description: Unexpected error
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/ErrorResponse"
    post:
      tags:
        - ledgeraccounts
      summary: Adds ledger account
      description: Adds ledger account
      operationId: createLedgerAccount
      requestBody:
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/LedgerAccountRequest"
      responses:
        201:
          description: The ledger account
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/LedgerAccount"
        400:
          description: Invalid request
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/ErrorResponse"
        401:
          description: Unauthorized
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/ErrorResponse"
        500:
          description: Unexpected error
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/ErrorResponse"
  /ledgerAccounts/{id}:
    get:
      tags:
        - ledgeraccounts
      summary: Gets ledger account
      description: Gets ledger account
      operationId: getLedgerAccountById
      parameters:
        - name: id
          description: The ledger account id
          in: path
          required: true
          schema:
            type: string
      responses:
        200:
          description: The ledger account
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/LedgerAccount"
        400:
          description: Invalid request
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/ErrorResponse"
        401:
          description: Unauthorized
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/ErrorResponse"
        500:
          description: Unexpected error
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/ErrorResponse"
    put:
      tags:
        - ledgeraccounts
      summary: Update ledger account
      description: Update ledger account
      operationId: updateLedgerAccountById
      parameters:
        - name: id
          description: The ledger account id
          in: path
          required: true
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/LedgerAccountRequest"
      responses:
        200:
          description: The ledger account
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/LedgerAccount"
        400:
          description: Invalid request
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/ErrorResponse"
        401:
          description: Unauthorized
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/ErrorResponse"
        500:
          description: Unexpected error
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/ErrorResponse"
components:
  schemas:
    CreateOperationTemplateRequest:
      type: object
      required:
        - templateCode
        - operations
      properties:
        templateCode:
          type: string
          enum:
            - ACH_PULL_SUCCESS
            - ACH_OUT_SUCCESS
            - CARD_PULL_SUCCESS
            - PAYMENT_ORDER
            - PAYMENT_ORDER_CANCEL
            - LOAN_ISSUE
            - LOAN_REPAYMENT
        operations:
          type: array
          items:
            $ref: "#/components/schemas/OperationTemplateItem"
    UpdateOperationTemplateRequest:
      allOf:
        - $ref: "#/components/schemas/CreateOperationTemplateRequest"
    OperationTemplateItem:
      type: object
      required:
        - order
        - itemType
        - financialOperation
        - parentLedgerAccountId
        - participantType
      properties:
        order:
          type: integer
        itemType:
          type: string
          enum:
            - customerDebt
            - sellerDebt
            - fee
            - loan
            - loanFee
            - paymentOrder
            - project
        financialOperation:
          type: string
          enum:
            - CREDIT
            - DEBIT
        parentLedgerAccountId:
          type: string
        participantType:
          type: string
          enum:
            - customer
            - seller
            - blueTape
    OperationTemplate:
      allOf:
        - type: object
          additionalProperties: false
          properties:
            id:
              type: string
              description: Id of operation template
              example: fb5637b2e5f3
            createdAt:
              type: string
              format: date-time
              description: Date of creation
              example: 2014-04-05T12:59:59.000Z
            updatedAt:
              type: string
              format: date-time
              description: Date of last update
              example: 2014-04-05T12:59:59.000Z
        - $ref: "#/components/schemas/CreateOperationTemplateRequest"
    CreateOperationRequest:
      type: object
      required:
        - templateCode
        - date
        - details
      properties:
        templateCode:
          type: string
          enum:
            - ACH_PULL_SUCCESS
            - ACH_OUT_SUCCESS
            - CARD_PULL_SUCCESS
            - PAYMENT_ORDER
            - PAYMENT_ORDER_CANCEL
            - LOAN_ISSUE
            - LOAN_REPAYMENT
        date:
          type: string
          format: date-time
        currency:
          type: string
          example: USD
        transactionNumber:
          type: string
          description: The ACH transaction number
          nullable: true        
        details:
          type: array
          items:
            $ref: "#/components/schemas/OperationDetailItem"
    CreateSimpleOperationRequest:
      type: object
      required:
        - templateCode
        - date
        - currency
      properties:
        templateCode:
          type: string
          enum:
            - ACH_PULL_SUCCESS
            - ACH_OUT_SUCCESS
            - CARD_PULL_SUCCESS
            - PAYMENT_ORDER
            - PAYMENT_ORDER_CANCEL
            - LOAN_ISSUE
            - LOAN_REPAYMENT
        date:
          type: string
          format: date-time
        currency:
          type: string
          example: USD
        customerId:
          type: string
        customerAmount:
          type: number
        customerDescription:
          type: string
        sellerCompanyId:
          type: string
        sellerAmount:
          type: number
        sellerDescription:
          type: string
        feeCompanyId:
          type: string
        feeAmount:
          type: number
        feeDescription:
          type: string
    Operation:
      allOf:
        - type: object
          additionalProperties: false
          properties:
            id:
              type: string
              description: Id of operation
              example: fb5637b2e5f3
            createdAt:
              type: string
              format: date-time
              description: Date of creation
              example: 2014-04-05T12:59:59.000Z
            createdBy:
              type: string
              description: Creator of item, free text or userId
        - $ref: "#/components/schemas/CreateOperationRequest"
    OperationDetailItem:
      type: object
      required:
        - itemType
        - participantId
        - participantType
        - amount
      properties:
        itemType:
          type: string
          enum:
            - customerDebt
            - sellerDebt
            - fee
            - loan
            - loanFee
            - paymentOrder
            - project
        participantId:
          type: string
        participantType:
          type: string
          enum:
            - customer
            - seller
            - blueTape
        amount:
          type: number
        loanId:
          type: string
          description: The loan id
          example: f7150ed1ec90
          nullable: true
        invoiceId:
          type: string
          description: The invoice id
          example: 7150ed1ec90f
          nullable: true
        projectId:
          type: string
          description: The project id
          example: 150ed1ec90f7
          nullable: true
        description:
          type: string
    OperationItem:
      type: object
      required:
        - id
        - createdAt
        - createdBy
        - type
        - subType
        - date
        - ledgerAccountId
        - amount
      properties:
        id:
          type: string
          description: Id of operation item
          example: fb5637b2e5f3
        createdAt:
          type: string
          format: date-time
          description: Date of creation
          example: 2014-04-05T12:59:59.000Z
        createdBy:
          type: string
          description: Creator of item, free text or userId
        type:
          type: string
          enum:
            - item
            - balance
            - correction
          description: Type of item
          example: item
        subType:
          type: string
          enum:
            - loanIssue
            - paymentOrder
            - advancePayment
            - finalPayment
            - achOut
            - achPull
            - achReturn
            - manualPayment
            - balance
            - correction
          description: Sub type of item, not finalized!
          example: paymentOrder
        date:
          type: string
          format: date-time
          description: Date of accounting item
          example: 2014-04-05T12:59:59.000Z
        ledgerAccountId:
          type: string
          description: The ledger account id
          example: 8738c041ab3b
        debtId:
          type: string
          description: The debt id
          example: 8c041ab3b873
          nullable: true
        loanId:
          type: string
          description: The loan id
          example: f7150ed1ec90
          nullable: true
        invoiceId:
          type: string
          description: The invoice id
          example: 7150ed1ec90f
          nullable: true
        projectId:
          type: string
          description: The project id
          example: 150ed1ec90f7
          nullable: true
        amount:
          type: number
          format: decimal
          description: Amount of item
          example: 900.00
          pattern: '[0-9]*\.[0-9]{2}'
          multipleOf: 0.01
        cumulatedBalance:
          type: number
          format: decimal
          description: The account's cumulated balance after this item.
          example: 900.00
          pattern: '[0-9]*\.[0-9]{2}'
          multipleOf: 0.01
        description:
          type: string
          example: Payment order placed by ABC
        transactionNumber:
          type: string
          description: The ACH transaction number
          nullable: true
    CreateCorrectionItemRequest:
      type: object
      required:
        - userId
        - date
        - amount
        - ledgerAccountId
        - accountOwnerId
      properties:
        userId:
          type: string
          description: The user id
          example: 0803a88d01d4
        date:
          type: string
          format: date-time
          description: Date of correction
          example: 2014-04-05T12:59:59.000Z
        amount:
          type: number
          format: decimal
          description: Amount of correction, can be negative.
          example: 900.00
          pattern: '[0-9]*\.[0-9]{2}'
          multipleOf: 0.01
        ledgerAccountId:
          type: string
          description: The ledger account id
          example: 8738c041ab3b
        accountOwnerId:
          type: string
          description: The account owner id
          example: f7150ed1ec90
        accountOwnerType:
          type: string
          enum:
            - customer
            - seller
            - blueTape
        description:
          type: string
        loanId:
          type: string
          description: The loan id
          example: f7150ed1ec90
          nullable: true
        invoiceId:
          type: string
          description: The invoice id
          example: 7150ed1ec90f
          nullable: true
        projectId:
          type: string
          description: The project id
          example: 150ed1ec90f7
          nullable: true
    CreateBalanceItemRequest:
      type: object
      required:
        - userId
        - date
        - amount
        - ledgerAccountId
        - accountOwnerId
      properties:
        userId:
          type: string
          description: The user id
          example: 0803a88d01d4
        date:
          type: string
          format: date-time
          description: Date of balance
          example: 2014-04-05T12:59:59.000Z
        amount:
          type: number
          format: decimal
          description: Amount of balance, can be negative.
          example: 900.00
          pattern: '[0-9]*\.[0-9]{2}'
          multipleOf: 0.01
        ledgerAccountId:
          type: string
          description: The ledger account id
          example: 8738c041ab3b
        accountOwnerId:
          type: string
          description: The account owner id
          example: f7150ed1ec90
        accountOwnerType:
          type: string
          enum:
            - customer
            - seller
            - blueTape
        description:
          type: string
        loanId:
          type: string
          description: The loan id
          example: f7150ed1ec90
          nullable: true
        invoiceId:
          type: string
          description: The invoice id
          example: 7150ed1ec90f
          nullable: true
        projectId:
          type: string
          description: The project id
          example: 150ed1ec90f7
          nullable: true
    CreateBalanceItemResponse:
      allOf:
        - type: object
          additionalProperties: false
          properties:
            id:
              type: string
              description: Id of balance item
              example: f5db2114ad68
        - $ref: "#/components/schemas/CreateBalanceItemRequest"
    CreateCorrectionItemResponse:
      allOf:
        - type: object
          additionalProperties: false
          properties:
            id:
              type: string
              description: Id of correction item
              example: f5db2114ad68
        - $ref: "#/components/schemas/CreateCorrectionItemRequest"
    AccountingBalance:
      type: object
      required:
        - ledgerAccountId
        - accountOwnerId
        - balanceAmount
        - lastItemDate
      properties:
        ledgerAccountId:
          type: string
          description: The ledger account id
          example: 8738c041ab3b
        accountOwnerId:
          type: string
          description: Optional. The account owner id
          example: 6e587a391020
        loanId:
          type: string
          description: The loan id
          example: f7150ed1ec90
          nullable: true
        invoiceId:
          type: string
          description: The invoice id
          example: 7150ed1ec90f
          nullable: true
        projectId:
          type: string
          description: The project id
          example: 150ed1ec90f7
          nullable: true
        balanceAmount:
          type: number
          format: decimal
          description: Amount of balance, can be negative.
          example: 900.00
          pattern: '[0-9]*\.[0-9]{2}'
          multipleOf: 0.01
        lastItemDate:
          type: string
          format: date-time
          description: Last item date
          example: 2014-04-05T12:59:59.000Z
    LedgerAccount:
      allOf:
        - type: object
          additionalProperties: false
          properties:
            id:
              type: string
              description: Id of ledger account
              example: f5db2114ad68
            createdAt:
              type: string
              format: date-time
              description: Date of creation
              example: 2014-04-05T12:59:59.000Z
            updatedAt:
              type: string
              format: date-time
              description: Date of last update
              example: 2014-04-05T12:59:59.000Z
        - $ref: "#/components/schemas/LedgerAccountRequest"
        - type: object
          additionalProperties: false
          properties:
            childAccounts:
              type: array
              nullable: true
              items:
                $ref: "#/components/schemas/ChildLedgerAccount"
    ChildLedgerAccount:
      allOf:
        - type: object
          additionalProperties: false
          properties:
            id:
              type: string
              description: Id of ledger account
              example: f5db2114ad68
            createdAt:
              type: string
              format: date-time
              description: Date of creation
              example: 2014-04-05T12:59:59.000Z
            updatedAt:
              type: string
              format: date-time
              description: Date of last update
              example: 2014-04-05T12:59:59.000Z
        - $ref: "#/components/schemas/LedgerAccountRequest"
    LedgerAccountRequest:
      type: object
      required:
        - accountName
        - status
        - currency
      properties:
        parentAccountId:
          type: string
          description: The parent id of ledger account
          nullable: true
          example: beb11908a153
        type:
          type: string
          enum:
            - debt
            - detail
          description: Type of account
          example: debt
        ownerId:
          type: string
          nullable: true
          description: Optional. The owner id
          example: 6e587a391020
        ownerType:
          type: string
          enum:
            - customer
            - seller
            - blueTape
        accountName:
          type: string
          description: The name of ledger account
          example: Payments
        currency:
          type: string
          example: USD
        status:
          type: string
          enum:
            - active
            - inactive
          description: The status of ledger account
    ErrorResponse:
      type: object
      required:
        - statusCode
        - message
        - errorDescription
      properties:
        statusCode:
          description: HTTP status code corresponding to the error.
          type: integer
          example: 400
        message:
          description: A human readable error message.
          type: string
          example: Invalid model.
        errorDescription:
          description: Detailed information.
          type: string
  securitySchemes:
    ApiKey:
      type: apiKey
      in: header
      name: X-Api-Key
security:
  - ApiKey: []
