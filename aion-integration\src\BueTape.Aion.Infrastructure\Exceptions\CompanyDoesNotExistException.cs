﻿using BlueTape.Common.ExceptionHandling.Exceptions.Base;
using System.Net;
using System.Runtime.Serialization;

namespace BueTape.Aion.Infrastructure.Exceptions;

[Serializable]
public class CompanyDoesNotExistException : DomainException
{
    public CompanyDoesNotExistException(string companyId, HttpStatusCode statusCode = HttpStatusCode.BadRequest) : base(BuildErrorMessage(companyId), statusCode)
    {
    }

    protected CompanyDoesNotExistException(SerializationInfo info, StreamingContext context) : base(info, context)
    {
    }

    private static string BuildErrorMessage(string companyId)
    {
        return $"Company with id: {companyId} does not exist";
    }

    public override string Code => ErrorCodes.CompanyDoesNotExist;
}