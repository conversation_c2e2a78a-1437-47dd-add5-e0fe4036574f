import { useTranslation } from 'react-i18next'
import { useCallback, useMemo } from 'react'
import { Badge, Flex } from 'antd'
import type { DescriptionsItemType } from 'antd/es/descriptions'

import GiactViewer from '@/app/line-of-credit/[application_id]/detailed/_components/external/GiactViewer'
import Spacer from '@/components/common/Spacer'
import StyledTitle from '@/components/common/typography/StyledTitle'
import type { IArAdvanceApplicationDetailedGiactItem } from '@/lib/redux/api/ar-advance-application/types'
import StyledDescription from '@/components/common/Description'
import GiactAccountDetails from '@/app/ar-advance-application/[application_id]/detailed/_components/tabs/bank-cash-flow/components/GiactAccountDetails'

interface IProps {
  giactItems: IArAdvanceApplicationDetailedGiactItem[]
  applicationId: string
}

const GiactVerificationBlock = ({
  giactItems: giact,
  applicationId,
}: Readonly<IProps>): JSX.Element => {
  const { t } = useTranslation()

  const getIsVerifiedBadge = useCallback(
    (value: boolean | null): JSX.Element => {
      if (value === true)
        return (
          <Badge
            status="success"
            text={t(
              'arAdvanceApplication.page.detailed.tabs.bankAccounts.passed',
            )}
          />
        )

      if (value === false)
        return (
          <Badge
            status="error"
            text={t(
              'arAdvanceApplication.page.detailed.tabs.bankAccounts.failed',
            )}
          />
        )

      return <Badge status="default" text={t('na')} />
    },
    [t],
  )

  const items = useMemo<DescriptionsItemType[]>(
    () =>
      giact.map((item) => ({
        key: item.identifier,
        label: item.name,
        children: (
          <Flex gap="4px 16px" wrap="wrap" align="baseline">
            {getIsVerifiedBadge(item.isVerified)}
            <GiactAccountDetails
              accountHolderName={item.accountHolderName}
              accountNumber={item.accountNumber}
              name={item.name}
              routingNumber={item.routingNumber}
            />
          </Flex>
        ),
      })),
    [giact, getIsVerifiedBadge],
  )

  return (
    <>
      <StyledTitle
        level={4}
        $text={t(
          'arAdvanceApplication.page.detailed.tabs.bankAccounts.GIACTVerification',
        )}
      />
      <Spacer height={20} />
      <GiactViewer applicationId={applicationId} />
      {items?.length > 0 && (
        <>
          <Spacer height={24} />
          <StyledDescription items={items} />
        </>
      )}
    </>
  )
}

export default GiactVerificationBlock
