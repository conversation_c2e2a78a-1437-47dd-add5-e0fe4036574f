﻿using System.Runtime.Serialization;
using System.Text.Json.Serialization;

namespace BlueTape.Aion.DataAccess.External.Models.CreateAchTransfer;

[DataContract]
public class AchObjectRequest
{
    [JsonPropertyName("accountId")]
    public string AccountId { get; set; } = null!;

    [JsonPropertyName("accountNumber")]
    public string AccountNumber { get; set; } = null!;

    [JsonPropertyName("amount")]
    public string Amount { get; set; } = null!;

    [JsonPropertyName("counterpartyId")]
    public string CounterpartyId { get; set; } = null!;

    [JsonPropertyName("counterpartyName")]
    public string CounterpartyName { get; set; } = null!;

    [JsonPropertyName("transferMethodId")]
    public string TransferMethodId { get; set; } = null!;

    [JsonPropertyName("transactionType")]
    public string TransactionType { get; set; } = null!;

    [JsonPropertyName("sendEmail")]
    public bool SendEmail { get; set; }

    [JsonPropertyName("addenda")]
    public string[] Addenda { get; set; } = null!;

    [JsonPropertyName("description")]
    public string Description { get; set; } = null!;

    [JsonPropertyName("serviceType")]
    public string ServiceType { get; set; } = null!;

    [JsonPropertyName("secCode")]
    public string SecCode { get; set; } = null!;

    [JsonPropertyName("contextIdentifier")]
    public string ContextIdentifier { get; set; } = null!;

    [JsonPropertyName("originator")]
    public Originator Originator { get; set; } = new();
}

[DataContract]
public class Originator
{
    [JsonPropertyName("name")]
    public string Name { get; set; } = "BLUETAPE";
}