{"Serilog": {"MinimumLevel": {"Default": "Information", "Override": {"AWSSDK": "Warning", "BlueTape.Services.Utilities.AspNetCore.Tracing": "Error", "BlueTape.Services.Utilities.AWS": "Warning", "Microsoft.AspNetCore": "Warning", "Microsoft.AspNetCore.DataProtection": "Error", "Microsoft.EntityFrameworkCore": "Warning", "System.Net.Http.HttpClient": "Warning"}}}, "Services": {"AionServiceApi": "https://bb-stage.aionfi.com", "AionAuthServiceApi": "https://uam-stage.aionfi.com"}, "AionReport": {"DateRange": 7, "BucketName": "dev.uw1.linqpal-aion-reports", "ErrorSnsTopicName": "aion-error-notification-dev"}, "BlueTapeOptions": {"AwsSecretName": "bluetape_keys_dev"}}