import {client} from "../client";

export async function findCellPhoneNumberByPhoneNumber(cellPhoneNumber) {
    const database = client.db(`${process.env.CI_ENVIRONMENT_URL == 'https://dev.bluetape.com' ? 'dev' : 'beta'}`);
    const users = database.collection('users');

    const query = {"phone": `+1${cellPhoneNumber}`};

    const phoneNumber = await users.findOne(query);
    return phoneNumber;
}
