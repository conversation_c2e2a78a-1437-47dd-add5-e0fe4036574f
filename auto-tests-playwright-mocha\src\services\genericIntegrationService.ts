﻿import {Browser, expect} from "@playwright/test";
import {Action} from "../database/autoTests/entities/AutoTestReport";
import {BaseTest} from "../tests/test-utils";
import {CreateInvoiceSimplifyRequest} from "../api/common/genericAPI/requests/createSimplifyInvoiceRequest";
import {createSimplifyInvoice, getCheckOutUrl} from "../api/common/send-generic-api-request";
import {PageManager} from "../objects/pages/page-manager";

export class GenericIntegrationService
{
     async createGenericSimplifyInvoiceAndGetCheckOutUrl(browser: Browser, actionsPerTest: Action[]) {
        const date = new Date().toISOString();
        const externalCustomerId = "autoSimplifyCustomer"
        const externalInvoiceId = BaseTest.getGUID();
        const externalInvoiceNumber = `AUTO-${externalInvoiceId}`
        const totalAmount = BaseTest.getRandomNumberv2(100, 500);

        const invoiceRequestBody: CreateInvoiceSimplifyRequest= {
            payersInfo: [
                {
                    firstName: "John",
                    lastName: "Doe",
                    emailAddress: "<EMAIL>",
                    cellPhoneNumber: "",
                    businessName: "Test business"
                }
            ],
            customerId: externalCustomerId,
            id:externalInvoiceId,
            invoiceNumber:externalInvoiceNumber,
            subTotal: totalAmount,
            totalAmount: totalAmount,
            taxAmount:0,
            lines: [
                {
                    description: "Brick",
                    unitAmount: 56,
                    quantity:5,
                    subTotal: 56,
                    taxAmount: 56,
                    totalAmount: 135
                },
                {
                    description: "wood",
                    unitAmount: 56,
                    quantity:5,
                    subTotal: 56,
                    taxAmount: 56,
                    totalAmount: 135
                }
            ],
            invoiceDate:date,
            sourceModifiedDate:date,
            dueDate:date,
        };

        actionsPerTest.push({description: "Send create invoice via generic API simplify approach"})
        const simplifyResponse = await createSimplifyInvoice(invoiceRequestBody);
        actionsPerTest.push({description: `Receive response: ${simplifyResponse.status} and its body: ${JSON.stringify(simplifyResponse.data)}`})
        expect(simplifyResponse.status, 'Expected status code 201').toEqual(201);

        actionsPerTest.push({description: `Send request te get payment url for externalInvoiceId: ${externalInvoiceId}`})
        const redirectUrlResponse = await getCheckOutUrl(externalInvoiceId)
        actionsPerTest.push({description: `Receive response: ${redirectUrlResponse.status} and its body: ${JSON.stringify(redirectUrlResponse.data)}`})
        expect(redirectUrlResponse.status, 'Expected status code 201').toEqual(200);

        return { invoiceId: simplifyResponse.data.blueTapeId, redirectUrl: redirectUrlResponse.data, externalInvoiceId: externalInvoiceId}
    }

    async integrationPaymentRedirectPayWithAch(paymentUrl: string, browser: Browser, actionsPerTest: Action[]) {
        let integrationPaymentPage = await browser.newPage();
        await integrationPaymentPage.goto(`${paymentUrl}`)

        let pageManager = new PageManager(integrationPaymentPage);
        await pageManager.integrationPaymentPage.loginAndPayWithAch("<EMAIL>", "Qazwsx123!", actionsPerTest)
        await integrationPaymentPage.close();
    }

    async integrationPaymentRedirectPayWithBTC(paymentUrl: string, browser: Browser, actionsPerTest: Action[]) {
        let integrationPaymentPage = await browser.newPage();
        await integrationPaymentPage.goto(`${paymentUrl}`)

        let pageManager = new PageManager(integrationPaymentPage);
        await pageManager.integrationPaymentPage.loginAndPayWithBTC("<EMAIL>", "Qazwsx123!", actionsPerTest)
        await integrationPaymentPage.close();
    }
}