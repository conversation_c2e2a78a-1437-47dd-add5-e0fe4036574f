﻿using Azure.Data.Tables;
using BlueTape.Aion.Application.Abstractions;
using BlueTape.Aion.Application.Constants;
using BlueTape.Aion.Application.Service;
using BlueTape.Aion.DataAccess.External.Models.CreateAchTransfer.Response;
using BlueTape.Application.Tests.AutoFixture.Attributes;
using BlueTape.Integrations.Aion.AzureTableStorage.Abstractions;
using BlueTape.Integrations.Aion.AzureTableStorage.Entities;
using BlueTape.Integrations.Aion.Infrastructure.Enums;
using BlueTape.Utilities.Providers;
using Microsoft.Extensions.Logging;
using NSubstitute.Extensions;
using System;
using System.Collections.Generic;
using System.Globalization;
using System.Threading;
using System.Threading.Tasks;

namespace BlueTape.Application.Tests.Services;

public class TransactionServiceTests
{
    private readonly TransactionService _transactionService;

    private readonly IAzureStorageTransactionRepository _azureStorageTransactionRepository = Substitute.For<IAzureStorageTransactionRepository>();
    private readonly IDateProvider _dateProvider = Substitute.For<IDateProvider>();
    private readonly ILogger<TransactionService> _logger = Substitute.For<ILogger<TransactionService>>();
    private readonly IErrorNotificationService _notificationService = Substitute.For<IErrorNotificationService>();

    public TransactionServiceTests()
    {
        _transactionService = new TransactionService(
            _azureStorageTransactionRepository,
            _dateProvider,
            _logger,
            _notificationService
        );
    }

    [Fact]
    public async Task GetAllExistingSentOrReceivedTransactions_NoTransactionsExist_ReturnsEmptyList()
    {
        IEnumerable<TransactionEntity> listOfTransactions = new List<TransactionEntity>();
        _azureStorageTransactionRepository.ReturnsForAll(listOfTransactions);

        var result = await _transactionService.GetAllExistingSentOrReceivedTransactions(default);

        result.ShouldBe(listOfTransactions);
    }

    [Theory, AutoFixtureCustom]
    public async Task SaveAionCreatedAchTransactionAsync_TransactionNotExists_InsertsNewTransaction(AchResponseObj achTransaction,
        TransactionEntity transaction)
    {
        achTransaction.TransactionType = AionTransferType.PULL.ToString();
        achTransaction.EffectiveDate = DateTime.UtcNow.ToString("yyMMdd");

        var datetime = DateTime.Now;
        _azureStorageTransactionRepository
            .GetByBlueTapeTransactionNumbersAsync(Arg.Any<string[]>(), default)
            .Returns(new List<TransactionEntity>() { transaction });
        _dateProvider.CurrentDateTime.Returns(datetime);

        await _transactionService.SaveAionCreatedAchTransactionAsync(achTransaction, default).ShouldNotThrowAsync();

        await _azureStorageTransactionRepository.InsertOrReplaceEntityAsync(Arg.Is<TransactionEntity>(x =>
            x.IsProcessed == true &&
            x.CreatedAt == datetime &&
            x.UpdatedAt == datetime &&
            x.AionTransactionNumber == achTransaction.ReferenceId), TableUpdateMode.Merge, default);
    }

    [Theory, AutoFixtureCustom]
    public async Task SaveAionCreatedAchTransactionAsync_TransactionExistsWithSameStatus_UpdatesTableWithProcessedFlag(AchResponseObj achTransaction,
        TransactionEntity transaction)
    {
        var status = "status";
        achTransaction.Status = status;
        achTransaction.TransactionType = AionTransferType.PULL.ToString();
        achTransaction.EffectiveDate = DateTime.UtcNow.ToString("yyMMdd");

        transaction.AionTransactionStatus = status;

        var datetime = DateTime.Now;
        _azureStorageTransactionRepository
            .GetByBlueTapeTransactionNumbersAsync(Arg.Any<string[]>(), default)
            .Returns(new List<TransactionEntity>() { transaction });
        _dateProvider.CurrentDateTime.Returns(datetime);

        await _transactionService.SaveAionCreatedAchTransactionAsync(achTransaction, default).ShouldNotThrowAsync();

        await _azureStorageTransactionRepository.InsertOrReplaceEntityAsync(Arg.Is<TransactionEntity>(x =>
            x.IsProcessed == true &&
            x.CreatedAt == new DateTime() &&
            x.UpdatedAt == datetime &&
            x.AionTransactionNumber == status), TableUpdateMode.Merge, default);
    }

    [Theory, AutoFixtureCustom]
    public async Task SaveAionCreatedInternalTransactionAsync_TransactionNotExists_InsertsNewTransaction(AchResponseObj achTransaction,
        TransactionEntity transaction)
    {
        var datetime = DateTime.Now;
        achTransaction.TransactionType = AionTransferType.PULL.ToString();
        achTransaction.EffectiveDate = DateTime.UtcNow.ToString("yyMMdd");

        _azureStorageTransactionRepository
            .GetByBlueTapeTransactionNumbersAsync(Arg.Any<string[]>(), default)
            .Returns(new List<TransactionEntity>() { transaction });
        _dateProvider.CurrentDateTime.Returns(datetime);

        await _transactionService.SaveAionCreatedAchTransactionAsync(achTransaction, default).ShouldNotThrowAsync();

        await _azureStorageTransactionRepository.InsertOrReplaceEntityAsync(Arg.Is<TransactionEntity>(x =>
            x.IsProcessed == false &&
            x.CreatedAt == datetime &&
            x.UpdatedAt == datetime &&
            x.AionTransactionNumber == achTransaction.TraceNumber), TableUpdateMode.Merge, default);
    }

    [Theory, AutoFixtureCustom]
    public async Task SaveAionCreatedInternalTransactionAsync_TransactionExistsWithSameStatus_UpdatesTableWithProcessedFlag(AchResponseObj achTransaction,
        TransactionEntity transaction)
    {
        var status = "status";
        achTransaction.Status = status;
        achTransaction.TransactionType = AionTransferType.PULL.ToString();
        achTransaction.EffectiveDate = DateTime.UtcNow.ToString("yyMMdd");

        transaction.AionTransactionStatus = status;
        var datetime = DateTime.Now;
        _azureStorageTransactionRepository
            .GetByBlueTapeTransactionNumbersAsync(Arg.Any<string[]>(), default)
            .Returns(new List<TransactionEntity>() { transaction });
        _dateProvider.CurrentDateTime.Returns(datetime);

        await _transactionService.SaveAionCreatedAchTransactionAsync(achTransaction, default).ShouldNotThrowAsync();

        await _azureStorageTransactionRepository.InsertOrReplaceEntityAsync(Arg.Is<TransactionEntity>(x =>
            x.IsProcessed == false &&
            x.CreatedAt == new DateTime() &&
            x.UpdatedAt == datetime &&
            x.AionTransactionNumber == status), TableUpdateMode.Merge, default);
    }

    [Fact]
    public async Task SaveAionAchTransactionReturnsAsync_GroupsByPaymentIdAndCallsSaveReturnsAsync()
    {
        var paymentId1 = "payment1";
        var paymentId2 = "payment2";

        var achList = new List<AchResponseObj>
        {
            new ()
            {
                Original = new() { PaymentId = paymentId1 },
                CreatedAt = new DateTime(2023, 09, 01).ToString(CultureInfo.InvariantCulture),
                PaymentType = AionConstants.AionReturnPaymentType
            },
            new ()
            {
                Original = new () { PaymentId = paymentId2 },
                CreatedAt = new DateTime(2023, 09, 02).ToString(CultureInfo.InvariantCulture),
                PaymentType = AionConstants.AionReturnPaymentType
            },
        };

        _azureStorageTransactionRepository
            .GetByAchIdsAsync(Arg.Any<string[]>(), default)
                .Returns(new List<TransactionEntity>() { new() { AchId = paymentId1, ErrorCode = "default" } });

        await _transactionService.SaveAionAchTransactionReturnsAsync(achList, CancellationToken.None);

        await _azureStorageTransactionRepository.Received(2)
            .InsertOrReplaceEntityAsync(Arg.Any<TransactionEntity>(), TableUpdateMode.Merge, Arg.Any<CancellationToken>());
        // One of returns not matched
        await _notificationService.Received(1)
            .NotifyUnmatchedReturn(Arg.Any<AchResponseObj>(), Arg.Any<CancellationToken>());
    }
}
