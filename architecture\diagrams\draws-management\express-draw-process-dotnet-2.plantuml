@startuml ATC/Express Draw Issue Process (.NET) /2

title ATC/Express Draw Draw Issue Process (.NET) /2

participant "LFSASJ" as lfsasj #SkyBlue
queue "Draw Events Queue" as dq #PaleVioletRed
participant "L<PERSON>" as lfs #SkyBlue
participant "LMS" as lms #SkyBlue
queue "LMS Payments Queue" as lmsq #PaleVioletRed
queue "Payment Queue" as pq #PaleVioletRed
participant "Payment Domain Services" as payment #SkyBlue

autonumber

== Authorization ==

loop Daily schedule
    lfsasj --> lfsasj : Daily schedule
    lfsasj -> lms : Read expired authorizations
    lms --> lfsasj
end

alt #LightGreen Draw IS NOT declined during authorization period
    lfsasj -> dq : Place event\n""Draw.Authorization.Expired""
    dq -> lfs : Consume draw events
    lfs -> lms : Start draw
    lfs -> pq : Place payment request\n""CREATE.DRAW.DISBURSEMENT""
    pq -> payment : Consume payment events
    payment --> payment : Create necessary transactions\n(not detailed here)
    payment --> lmsq : Notify LMS about disbursement success
    lmsq -> lms : Consume LMS events
else #LightCoral Draw IS declined during authorization period
    lms -> lms : Cancel draw (backoffice)
    lms --> lms : Cancel authorization period
end

@enduml