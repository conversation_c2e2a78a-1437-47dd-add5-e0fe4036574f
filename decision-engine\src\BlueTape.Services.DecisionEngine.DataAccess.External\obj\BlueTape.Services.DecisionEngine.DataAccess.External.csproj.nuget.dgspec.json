{"format": 1, "restore": {"C:\\Users\\<USER>\\source\\repos\\BlueTape\\decision-engine\\src\\BlueTape.Services.DecisionEngine.DataAccess.External\\BlueTape.Services.DecisionEngine.DataAccess.External.csproj": {}}, "projects": {"C:\\Users\\<USER>\\source\\repos\\BlueTape\\decision-engine\\src\\BlueTape.Services.DecisionEngine.DataAccess.External\\BlueTape.Services.DecisionEngine.DataAccess.External.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\decision-engine\\src\\BlueTape.Services.DecisionEngine.DataAccess.External\\BlueTape.Services.DecisionEngine.DataAccess.External.csproj", "projectName": "BlueTape.Services.DecisionEngine.DataAccess.External", "projectPath": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\decision-engine\\src\\BlueTape.Services.DecisionEngine.DataAccess.External\\BlueTape.Services.DecisionEngine.DataAccess.External.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\decision-engine\\src\\BlueTape.Services.DecisionEngine.DataAccess.External\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\source\\repos\\BlueTape\\decision-engine\\NuGet.Config", "C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net6.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}, "https://nuget.pkg.github.com/bluetape-org/index.json": {}}, "frameworks": {"net6.0": {"targetAlias": "net6.0", "projectReferences": {"C:\\Users\\<USER>\\source\\repos\\BlueTape\\decision-engine\\src\\BlueTape.Services.DecisionEngine.Domain\\BlueTape.Services.DecisionEngine.Domain.csproj": {"projectPath": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\decision-engine\\src\\BlueTape.Services.DecisionEngine.Domain\\BlueTape.Services.DecisionEngine.Domain.csproj"}, "C:\\Users\\<USER>\\source\\repos\\BlueTape\\decision-engine\\src\\BlueTape.Services.DecisionEngine.Infrastructure\\BlueTape.Services.DecisionEngine.Infrastructure.csproj": {"projectPath": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\decision-engine\\src\\BlueTape.Services.DecisionEngine.Infrastructure\\BlueTape.Services.DecisionEngine.Infrastructure.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"net6.0": {"targetAlias": "net6.0", "dependencies": {"AWSSDK.Extensions.NETCore.Setup": {"target": "Package", "version": "[3.7.300, )"}, "BlueTape.CompanyService": {"target": "Package", "version": "[1.2.42, )"}, "BlueTape.CompanyService.Common": {"target": "Package", "version": "[1.1.21, )"}, "BlueTape.Integrations.Experian": {"target": "Package", "version": "[1.0.2, )"}, "BlueTape.Integrations.Giact": {"target": "Package", "version": "[1.0.3, )"}, "BlueTape.Integrations.LexisNexis": {"target": "Package", "version": "[1.0.6, )"}, "BlueTape.Integrations.Plaid": {"target": "Package", "version": "[1.0.7, )"}, "BlueTape.Integrations.Plaid.Infrastructure": {"target": "Package", "version": "[1.0.0, )"}, "BlueTape.LS": {"target": "Package", "version": "[1.1.69, )"}, "BlueTape.Utilities": {"target": "Package", "version": "[1.4.6, )"}, "DateOnlyTimeOnly.AspNet": {"target": "Package", "version": "[2.1.1, )"}, "Macross.Json.Extensions": {"target": "Package", "version": "[3.0.0, )"}, "Newtonsoft.Json": {"target": "Package", "version": "[13.0.1, )"}, "bluetape.invoiceservice": {"target": "Package", "version": "[1.0.38, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "downloadDependencies": [{"name": "Microsoft.AspNetCore.App.Ref", "version": "[6.0.36, 6.0.36]"}, {"name": "Microsoft.NETCore.App.Ref", "version": "[6.0.36, 6.0.36]"}, {"name": "Microsoft.WindowsDesktop.App.Ref", "version": "[6.0.36, 6.0.36]"}], "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.301\\RuntimeIdentifierGraph.json"}}}, "C:\\Users\\<USER>\\source\\repos\\BlueTape\\decision-engine\\src\\BlueTape.Services.DecisionEngine.Domain\\BlueTape.Services.DecisionEngine.Domain.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\decision-engine\\src\\BlueTape.Services.DecisionEngine.Domain\\BlueTape.Services.DecisionEngine.Domain.csproj", "projectName": "BlueTape.Services.DecisionEngine.Domain", "projectPath": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\decision-engine\\src\\BlueTape.Services.DecisionEngine.Domain\\BlueTape.Services.DecisionEngine.Domain.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\decision-engine\\src\\BlueTape.Services.DecisionEngine.Domain\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\source\\repos\\BlueTape\\decision-engine\\NuGet.Config", "C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net6.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}, "https://nuget.pkg.github.com/bluetape-org/index.json": {}}, "frameworks": {"net6.0": {"targetAlias": "net6.0", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"net6.0": {"targetAlias": "net6.0", "dependencies": {"BlueTape.CashFlow.Domain": {"target": "Package", "version": "[1.0.2, )"}, "BlueTape.OBS": {"target": "Package", "version": "[1.6.69, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "downloadDependencies": [{"name": "Microsoft.AspNetCore.App.Ref", "version": "[6.0.36, 6.0.36]"}, {"name": "Microsoft.NETCore.App.Ref", "version": "[6.0.36, 6.0.36]"}, {"name": "Microsoft.WindowsDesktop.App.Ref", "version": "[6.0.36, 6.0.36]"}], "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.301\\RuntimeIdentifierGraph.json"}}}, "C:\\Users\\<USER>\\source\\repos\\BlueTape\\decision-engine\\src\\BlueTape.Services.DecisionEngine.Infrastructure\\BlueTape.Services.DecisionEngine.Infrastructure.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\decision-engine\\src\\BlueTape.Services.DecisionEngine.Infrastructure\\BlueTape.Services.DecisionEngine.Infrastructure.csproj", "projectName": "BlueTape.Services.DecisionEngine.Infrastructure", "projectPath": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\decision-engine\\src\\BlueTape.Services.DecisionEngine.Infrastructure\\BlueTape.Services.DecisionEngine.Infrastructure.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\decision-engine\\src\\BlueTape.Services.DecisionEngine.Infrastructure\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\source\\repos\\BlueTape\\decision-engine\\NuGet.Config", "C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net6.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}, "https://nuget.pkg.github.com/bluetape-org/index.json": {}}, "frameworks": {"net6.0": {"targetAlias": "net6.0", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"net6.0": {"targetAlias": "net6.0", "dependencies": {"AWSSDK.Extensions.NETCore.Setup": {"target": "Package", "version": "[3.7.300, )"}, "AWSSDK.KeyManagementService": {"target": "Package", "version": "[3.7.300.54, )"}, "AWSSDK.SecretsManager": {"target": "Package", "version": "[3.7.302.29, )"}, "BlueTape.Utilities": {"target": "Package", "version": "[1.4.6, )"}, "Microsoft.Extensions.DependencyInjection.Abstractions": {"target": "Package", "version": "[8.0.0, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "downloadDependencies": [{"name": "Microsoft.AspNetCore.App.Ref", "version": "[6.0.36, 6.0.36]"}, {"name": "Microsoft.NETCore.App.Ref", "version": "[6.0.36, 6.0.36]"}, {"name": "Microsoft.WindowsDesktop.App.Ref", "version": "[6.0.36, 6.0.36]"}], "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.301\\RuntimeIdentifierGraph.json"}}}}}