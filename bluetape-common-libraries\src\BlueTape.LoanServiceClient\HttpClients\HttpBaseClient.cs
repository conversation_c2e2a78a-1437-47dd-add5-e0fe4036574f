﻿using BlueTape.Common.ExceptionHandling.Exceptions;
using BlueTape.Common.Extensions.Abstractions;
using BlueTape.LoanServiceClient.Abstractions.HttpClients;
using BlueTape.LoanServiceClient.Configuration;
using BlueTape.Services.Utilities.AspNetCore.Tracing;
using BlueTape.Utilities.Constants;
using Microsoft.Extensions.Logging;
using System.Net.Http.Headers;
using System.Net.Http.Json;
using System.Net.Mime;
using System.Text.Json;

namespace BlueTape.LoanServiceClient.HttpClients;

public class BaseHttpClient : IBaseHttpClient
{
    public HttpClient Client { get; }

    private readonly ILogger<BaseHttpClient> _logger;
    private readonly ITraceIdAccessor _traceIdAccessor;

    protected BaseHttpClient(ILogger<BaseHttpClient> logger, ITraceIdAccessor traceIdAccessor, HttpClient client)
    {
        _logger = logger;
        _traceIdAccessor = traceIdAccessor;
        Client = client;
    }

    public async Task<TResult?> Get<TResult>(string url, CancellationToken ct) where TResult : class
    {
        _logger.LogInformation("Retrieving data from {Path}", url);
        using var request = new HttpRequestMessage(HttpMethod.Get, url);

        var response = await Client.SendAsync(request, ct);
        var responseJson = await response.Content.ReadAsStringAsync(ct);

        _logger.LogInformation("Received response from GET {Path} with status code {StatusCode}", response, response.StatusCode);

        if (response.IsSuccessStatusCode)
        {
            return GetResult<TResult>(responseJson);
        }
        _logger.LogError("Failed at reading data from GET {Path}", url);

        throw new HttpClientRequestException("External service error", response, url, response.StatusCode);
    }

    public async Task<TResult?> Post<TResult, TPayload>(string url, TPayload payload, CancellationToken ct) where TResult : class
    {
        return await Post<TResult, TPayload>(url, payload, null, ct);
    }

    public async Task<TResult?> Post<TResult, TPayload>(string url, TPayload payload, IReadOnlyDictionary<string, string>? headers, CancellationToken ct) where TResult : class
    {
        _logger.LogInformation("Posting data to {Url}: {@Payload}", url, payload);

        var response = await PostAsync<TResult>(url, payload, headers, ct);

        return response;
    }

    public async Task<TResult?> PostAsync<TResult>(string url, object? payload, CancellationToken ct) where TResult : class
    {
        return await PostAsync<TResult>(url, payload, null, ct);
    }

    public async Task<TResult?> PostAsync<TResult>(string url, object? payload, IReadOnlyDictionary<string, string>? headers, CancellationToken ct) where TResult : class
    {
        using var content = JsonContent.Create(payload);
        using var request = new HttpRequestMessage(HttpMethod.Post, url) { Content = content };

        AddHeaders(request, headers);

        var response = await Client.SendAsync(request, ct).ConfigureAwait(false);

        var responseJson = await response.Content.ReadAsStringAsync(ct);

        _logger.LogInformation("Received POST response from url {Path}. Status code: {StatusCode}", url, response.StatusCode);

        if (response.IsSuccessStatusCode)
        {
            return GetResult<TResult>(responseJson);
        }

        _logger.LogError("Failed at reading data from POST {Path}", url);

        throw new HttpClientRequestException("External service error", response, url, response.StatusCode);
    }

    public async Task<TResult?> PatchAsync<TResult>(string url, object? payload, CancellationToken ct) where TResult : class
    {
        return await PatchAsync<TResult>(url, payload, null, ct);
    }

    public async Task<TResult?> PatchAsync<TResult>(string url, object? payload, IReadOnlyDictionary<string, string>? headers, CancellationToken ct) where TResult : class
    {
        using var content = JsonContent.Create(payload);
        using var request = new HttpRequestMessage(HttpMethod.Patch, url) { Content = content };

        AddHeaders(request, headers);

        var response = await Client.SendAsync(request, ct);
        var responseJson = await response.Content.ReadAsStringAsync(ct);

        _logger.LogInformation("Received PATCH response from url {Path}. Status code: {StatusCode}", url, response.StatusCode);

        if (response.IsSuccessStatusCode)
        {
            return GetResult<TResult>(responseJson);
        }

        _logger.LogError("Failed at reading data from PATCH {Path}", url);

        throw new HttpClientRequestException("External service error", response, url, response.StatusCode);
    }

    protected void ConfigureApiAccess(BlueTapeApiConfig config, string? userId)
    {
        ArgumentNullException.ThrowIfNull(config);
        Client.BaseAddress = config.ApiUrl;

        if (!string.IsNullOrEmpty(userId))
            Client.DefaultRequestHeaders.Add(HttpHeadersConstants.UserIdHeader, userId);
        else
            _logger.LogWarning("User id is not provided for the request");

        var traceIdHeaderName = new TraceIdOptions().TraceIdHeaderName;
        var mediaHeaderName = new MediaTypeWithQualityHeaderValue(MediaTypeNames.Application.Json);
        Client.DefaultRequestHeaders.Add(config.HeaderName, config.HeaderValue);
        Client.DefaultRequestHeaders.Add(traceIdHeaderName, _traceIdAccessor.TraceId);
        Client.DefaultRequestHeaders.Accept.Add(mediaHeaderName);
    }

    private static void AddHeaders(HttpRequestMessage request, IReadOnlyDictionary<string, string>? headers)
    {
        if (headers == null) return;

        foreach (var (key, value) in headers)
        {
            request.Headers.Add(key, value);
        }
    }

    private static T? GetResult<T>(string json) where T : class
    {
        var result = string.IsNullOrWhiteSpace(json)
            ? null : JsonSerializer.Deserialize<T>(json, ConfigurationConstants.JsonDeserializationOptions);

        return result;
    }
}