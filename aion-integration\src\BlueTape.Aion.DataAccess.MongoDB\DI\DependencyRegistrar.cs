﻿using BlueTape.Aion.DataAccess.MongoDB.Abstractions;
using BlueTape.Aion.DataAccess.MongoDB.Contexts;
using BlueTape.Aion.DataAccess.MongoDB.Mappers;
using BlueTape.Aion.DataAccess.MongoDB.Repositories;
using BlueTape.Aion.DataAccess.MongoDB.Services;
using BlueTape.MongoDB.Abstractions;
using BlueTape.Utilities.Security;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.DependencyInjection;

namespace BlueTape.Aion.DataAccess.MongoDB.DI;

public static class DependencyRegistrar
{
    public static void AddDataAccessMongoDbDependencies(this IServiceCollection services)
    {
        services.AddAutoMapper(typeof(MongoDbProfile));

        services.AddTransient<IHttpContextAccessor, HttpContextAccessor>();

        services.AddTransient<IKmsEncryptionService, KmsEncryptionService>();

        services.AddTransient<IMongoDbContext, AionMongoDBContext>();
        services.AddTransient<IAionLoggingRepository, AionLoggingRepository>();
        services.AddTransient<IAionLoggingService, AionLoggingService>();

        services.AddTransient<ICompanyRepository, CompanyRepository>();
        services.AddTransient<IBankAccountRepository, BankAccountRepository>();
        services.AddTransient<IUserRepository, UserRepository>();
        services.AddTransient<IUserRoleRepository, UserRoleRepository>();
    }
}