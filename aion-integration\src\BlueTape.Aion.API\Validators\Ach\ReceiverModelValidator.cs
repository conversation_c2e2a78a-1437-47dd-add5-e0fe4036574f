﻿using BlueTape.Integrations.Aion.Ach.CreateAchTransfer;
using FluentValidation;

namespace BlueTape.Aion.API.Validators.Ach;

public class ReceiverModelValidator : AbstractValidator<ReceiverModel>
{
    public ReceiverModelValidator()
    {
        RuleSet(ValidationConstant.ManualValidation, () =>
        {
            RuleFor(x => x.BankAccountId)
                .NotNull()
                .NotEmpty();
            
            RuleFor(x => x.CompanyId)
                .NotNull()
                .NotEmpty();
        });
    }
}