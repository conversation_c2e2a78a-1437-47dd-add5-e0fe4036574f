import {BasePage} from '../../../base.page';

export class AddNewProjectModal extends BasePage {
    constructor(page){
        super(page);
    };

    containers = {
        mainContainer: this.page.locator('_react=[key=".0"]'),
    };

    inputFields = {
        nameOfTheProject: this.containers.mainContainer.locator('[placeholder="Enter name of project"]'),
        nameOfCustomer: this.containers.mainContainer.locator('[placeholder="Enter the customer name"]'),
        address: this.containers.mainContainer.locator('[placeholder="Enter an address"]'),
        amount: this.containers.mainContainer.locator('[placeholder="Enter amount"]'),
        description: this.containers.mainContainer.locator('[placeholder="Enter a description"]'),
    };
    
    buttons = {
        closeModal: this.containers.mainContainer.locator('[data-testid="projects_view_invoice_close_button"]'),
        createProject: this.containers.mainContainer.locator('"Create project"'), 
    };

    async fillTheAddNewProjectForm(projectName, customerName, address, amount, description){
        await this.inputFields.nameOfTheProject.type(projectName);
        await this.inputFields.nameOfCustomer.type(customerName);
        await this.inputFields.address.type(address);
        await this.inputFields.amount.type(amount);
        await this.inputFields.description.type(description);
        await this.buttons.createProject.click();
    };
}