@startuml Paying Over Text Dynamic Architecture
title Paying Over Text Dynamic Architecture (sequence diagram)

actor "Client" as client #LightGray
participant "Provider" as prov #LightSeaGreen
participant "AGW w/\nLambda auth" as agw #Orange
queue "SQS.fifo" as sqs
participant "UserInteractionService" as uis #SkyBlue
queue "SQS.fifo" as sqs2
participant "Templates" as templ #LightSeaGreen
database "DB" as db #Orange
participant "Notification\nInvoicing" as inv #SkyBlue
participant "Linqpal" as linq #SkyBlue

autonumber "<b>[00]"

== New Invoice ==

linq -> db : Create notifications
inv -> db : Get notifications
inv -> sqs2 : Start conversation(s)
sqs2 -> uis
uis -> templ : Get template, build message
uis -> db : Persist message
uis -> prov : Notify user
prov -> client : Notify user

== Paying over text ==

loop Conversation
    client -> prov : User replies
    activate prov
    prov -> agw : Webhook
    agw -> sqs : Message
    sqs -> uis : Consume
    activate uis
    uis -> db : Persist message
    uis -> templ : Get answer templates
    alt Operation
        uis -> linq #DarkSalmon : Operation
        activate uis
        linq --> uis : Operation result
        deactivate uis
    end
    uis -> templ : Get template, build message
    uis -> db : Persist message
    uis --> prov : Notify user
    deactivate uis
    prov --> client : Notify user
    deactivate prov
end

legend bottom
| Color | Meaning |
|<#LightSeaGreen>| 3rd party component |
|<#SkyBlue>| 1st party backend application |
endlegend

@enduml