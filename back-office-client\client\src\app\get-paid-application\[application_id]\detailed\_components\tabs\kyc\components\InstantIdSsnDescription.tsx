import React from 'react'
import { useTranslation } from 'react-i18next'
import type { DescriptionsProps } from 'antd'
import { styled } from 'styled-components'

import StyledDescription from '@/components/common/Description'
import type { IGetPaidApplicationOwnersData } from '@/lib/redux/api/get-paid-application/types'
import {
  getAutomatedDecisionResultBadge,
  getYesNoTranslation,
} from '@/globals/utils'
import AutomatedDecisionCodeWithDescriptionArray from '@/components/common/AutomatedDecisionCodeWithDescriptionArray'

interface IProps {
  data: IGetPaidApplicationOwnersData
}

const InstantIdSsnDescription = ({ data }: IProps): JSX.Element => {
  const { t } = useTranslation<string | undefined>()

  const instantIdSsnItems: DescriptionsProps['items'] = [
    {
      label: `${t('getPaidApplication.page.detailed.tabs.kyc.instantIdBusinessExecLink')}:`,
      children: (
        <AutomatedDecisionCodeWithDescriptionArray
          automatedDecision={data.business2ExecLinkIndex.result}
          items={
            data.business2ExecLinkIndex.value?.code
              ? [
                  {
                    code: data.business2ExecLinkIndex.value.code,
                    description:
                      data.business2ExecLinkIndex.value?.description ?? null,
                  },
                ]
              : []
          }
        />
      ),
    },
    {
      label: (
        <>
          <p>
            {t(
              'getPaidApplication.page.detailed.tabs.kyc.instantIdConsumerVerification',
            )}
          </p>
          <p>({t('getPaidApplication.page.detailed.tabs.kyc.cvi')}):</p>
        </>
      ),
      children: (
        <AutomatedDecisionCodeWithDescriptionArray
          automatedDecision={data.cvi.result}
          items={
            data.cvi.value?.code
              ? [
                  {
                    code: data.cvi.value.code,
                    description: null,
                  },
                ]
              : []
          }
        />
      ),
    },
    {
      label: (
        <>
          <p>
            {t(
              'getPaidApplication.page.detailed.tabs.kyc.instantIdConsumerHighRisk',
            )}
          </p>
          <p>({t('getPaidApplication.page.detailed.tabs.kyc.cri')}):</p>
        </>
      ),
      children: (
        <AutomatedDecisionCodeWithDescriptionArray
          automatedDecision={data.cri.result}
          items={data.cri.value ?? []}
        />
      ),
    },
    {
      label: t('getPaidApplication.page.detailed.tabs.kyc.ssnRejection3m'),
      children: getAutomatedDecisionResultBadge(
        data.ssnRejection3Months.result,
        getYesNoTranslation(data.ssnRejection3Months.value, t),
      ),
    },
    {
      label: t('getPaidApplication.page.detailed.tabs.kyc.ssnRejection18m'),
      children: getAutomatedDecisionResultBadge(
        data.ssnRejection18Months.result,
        getYesNoTranslation(data.ssnRejection18Months.value, t),
      ),
    },
  ]

  return (
    <StyledDescriptionWrapper>
      <StyledDescription items={instantIdSsnItems} />
    </StyledDescriptionWrapper>
  )
}

const StyledDescriptionWrapper = styled.div`
  margin-bottom: 32px;
`

export default InstantIdSsnDescription
