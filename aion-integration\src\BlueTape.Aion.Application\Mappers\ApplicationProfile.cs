﻿using AutoMapper;
using BlueTape.Aion.Application.Models.Ach.Pull.Response;
using BlueTape.Aion.Application.Models.Instant;
using BlueTape.Aion.DataAccess.External.Models.CreateAchTransfer.Response;
using BlueTape.Aion.DataAccess.External.Models.InstantTransfer.Response;
using BlueTape.Aion.DataAccess.External.Models.WireTransfer.Response;
using BlueTape.Integrations.Aion.Ach.CreateWireTransfer.Response;

namespace BlueTape.Aion.Application.Mappers;

public class ApplicationProfile : Profile
{
    public ApplicationProfile()
    {
        CreateMap<CreateAchResponse, CreateAchResponseModel>();
        CreateMap<AchResponseObj, AchObjResponseModel>();
        CreateMap<OriginalResponse, OriginalResponseModel>();
        CreateMap<OriginatorResponse, OriginatorResponseModel>();
        CreateMap<PreviousResponse, PreviousResponseModel>();
        CreateMap<ReceiverResponse, ReceiverResponseModel>();

        CreateMap<CreateWireTransferResponse, CreateWireTransferResponseModel>();
        CreateMap<WireTransferObjItem, WireTransferObjItemModel>();
        CreateMap<FinancialInstitution, FinancialInstitutionModel>();

        CreateMap<CreateInstantTransferResponse, CreateInstantTransferResponseModel>();
        CreateMap<InstantTransferObjectItemResponse, InstantTransferObjectItemResponseModel>();
        CreateMap<Creditor, CreditorModel>();
        CreateMap<Debtor, DebtorModel>();
        CreateMap<Network, NetworkModel>();
        CreateMap<RtpPaymentInfo, RtpPaymentInfoModel>();

    }
}
