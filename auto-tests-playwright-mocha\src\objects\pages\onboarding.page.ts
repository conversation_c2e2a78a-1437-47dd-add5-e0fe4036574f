import {BasePage} from '../base.page';

const pageLocator = '[data-testid="tell_who_you_are_label"]';

export class OnBoardingPage extends BasePage {
    constructor(page, locator = pageLocator){
        super(page, locator); 
    };

    buttons = {
        continue: this.page.locator('[data-testid="onboarding_continue_button"]'),
        requestCredit: this.page.locator('[data-testid="getPrequalified"]'),
        setupAccount: this.page.locator('"Setup account"'),
        uploadSOInvoice: this.page.locator('[class="css-1dbjc4n r-1awozwy r-18u37iz"] >> nth=2'), // checklater
        skip: this.page.locator('[data-testid="onboarding_skip_button"]'),
    };

    inputFields = {
        typeOfBusiness: this.page.locator('[data-testid="firstName"]'), // wrong data-testid
    };

    radioButtons = {
        yes: this.page.locator('[data-testid="onboarding_yes_radio_btn"]'),
        no: this.page.locator('[data-testid="onboarding_no_radio_btn"]'),
    };

    labels = {
        setupAccount: this.page.locator('"Get paid fast by your customers, accept ACH & Card"'),
        requestCredit: this.page.locator('"Get Prequalified For BlueTape Buy Now, Pay Later"'),
        uploadInvoiceSO: this.page.locator('"Upload sales order or invoice to pay your vendor"'),
        setupAccountSales: this.page.locator('"Get paid fast by your customers, and offer financing"'),
        uploadInvoiceSOSales: this.page.locator('"Upload SO or invoice to pay your vendors"'),
    };

    disabledFields = {
        requestCreditDisabled: this.page.locator('[class="css-1dbjc4n r-14lw9ot r-1pi2tsx r-u8s1d"] >> nth=0'),
        setupAccountDisabled: this.page.locator('[class="css-1dbjc4n r-14lw9ot r-1pi2tsx r-u8s1d"] >> nth=1'),
        uploadInvoiceDisabled: this.page.locator('[class="css-1dbjc4n r-14lw9ot r-1pi2tsx r-u8s1d"] >> nth=2'),
    };

    // BusinessCategories = {             // maybe use later
    //     SubContractor: this.page.locator('[data-testid="subContractorButton"]'),
    //     GeneralContractor: this.page.locator('[data-testid="generalContractorButton"]'),
    //     DealerRetailerSupplier: this.page.locator('[data-testid="dealerRetailerSupplierButton"]'),
    //     ManufacturerDistributor: this.page.locator('[data-testid="manufacturerDistributorButton"]'),
    //     ArchitectInteriorDesigner: this.page.locator('[data-testid="architectInteriorDesignerButton"]'),
    //     EngineerConsultant: this.page.locator('[data-testid="engineerConsultantButton"]'),
    //     DeveloperPropertyOwner: this.page.locator('[data-testid="developerPropertyOwnerButton"]'),
    //     Other: this.page.locator('[data-testid="otherButton"]'),
    // };

    invoices = {
        addInvoice: this.page.getByText('Add invoice', { exact: true }),
        searchCustomerField: this.page.getByTestId('Search by contact name, business name, phone number, and Email..._input'),
        requiredCustomer: this.page.getByText('AutoBusFirstNameNine AutoBusLastNameNine'),
        invoiceNumber: this.page.getByTestId('Invoice number').getByRole('textbox'),
        serviceItem: this.page.getByTestId('service_item'),
        subtotalField: this.page.getByPlaceholder('Subtotal'),
        saveAndSendButton: this.page.getByRole('button', { name: 'Save & Send' }),
        invoiceTittle: this.page.getByTestId('Invoices'),
    }; //todo rework

    async uploadInvoice(){
        await super.uploadFile(this.buttons.uploadSOInvoice);
    };

    async chooseRole(roleSelector){
        await this.page.locator(`"${roleSelector}"`).click();
        await this.buttons.continue.click();
    };

    async createInvoiceViaUI(invoiceNumber, subtotal) {
        await this.invoices.addInvoice.click();
        await this.page.waitForLoadState('networkidle');
        await this.invoices.searchCustomerField.fill('AutoBusNine');
        await this.invoices.requiredCustomer.click();
        await this.invoices.invoiceNumber.fill(invoiceNumber);
        await this.invoices.serviceItem.click();
        await this.invoices.subtotalField.fill(subtotal);
        await this.invoices.saveAndSendButton.click();
        await this.invoices.invoiceTittle.isVisible();
    }
}
