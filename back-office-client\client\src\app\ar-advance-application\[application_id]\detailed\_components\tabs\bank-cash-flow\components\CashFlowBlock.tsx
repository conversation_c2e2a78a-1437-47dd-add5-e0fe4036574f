import { useTranslation } from 'react-i18next'
import { Flex } from 'antd'

import StyledTitle from '@/components/common/typography/StyledTitle'
import Spacer from '@/components/common/Spacer'
import CashFlowTable from '@/components/common/CashFlowTable'
import type { IArAdvanceDetailCashFlow } from '@/lib/redux/api/ar-advance-application/types'
import CashFlowDownloadContainer from '@/components/common/CashFlowDownload/CashFlowDownloadContainer'

interface IProps {
  items: IArAdvanceDetailCashFlow[]
  accountId: string
  companyName: string
}

const CashFlowBlock = ({
  items,
  accountId,
  companyName,
}: IProps): JSX.Element => {
  const { t } = useTranslation()
  return (
    <>
      <Flex align="center" gap={20}>
        <StyledTitle
          level={4}
          $text={t('arAdvanceApplication.page.detailed.tabs.supplier.cashFlow')}
        />
        <CashFlowDownloadContainer
          accountId={accountId}
          companyName={companyName}
        />
      </Flex>
      <Spacer height={20} />
      <CashFlowTable items={items} />
    </>
  )
}
export default CashFlowBlock
