import {client} from "../client";
import { ObjectId } from 'mongodb';

const collectionName = 'operations'

export class OperationRepository {
    async getByOwnerId(ownerId: string, type?: string) {
        try {
            await client.connect();
            
            const database = client.db(`${process.env.test_env}`);
            const collection = database.collection(collectionName);
    
            const query = type 
                ? { owner_id: ownerId, type: type }
                : { owner_id: ownerId };

            const result = await collection
                .find(query)
                .sort({ createdAt: -1 })
                .toArray();

            return result || [];
        } catch (e) {
            console.error("Error fetching operations:", e);
            return [];
        } finally {
            await client.close();
        }
    }

    async update(operationId: string, updateData: any) {
        try {
            await client.connect();
            
            const database = client.db(`${process.env.test_env}`);
            const collection = database.collection(collectionName);
    
            const result = await collection.updateOne(
                { _id: new ObjectId(operationId) },
                { $set: updateData }
            );

            return result.modifiedCount > 0;
        } catch (e) {
            console.error("Error updating operation:", e);
            return false;
        } finally {
            await client.close();
        }
    }
}
