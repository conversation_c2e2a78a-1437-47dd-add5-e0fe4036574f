﻿using BlueTape.Common.ExceptionHandling.Exceptions.Base;
using System.Net;
using System.Runtime.Serialization;

namespace BueTape.Aion.Infrastructure.Exceptions;

[Serializable]
public class AionBankAccountDoesNotExistException : DomainException
{
    public AionBankAccountDoesNotExistException(string bankAccountId, HttpStatusCode statusCode = HttpStatusCode.BadRequest) : base(BuildErrorMessage(bankAccountId), statusCode)
    {
    }

    protected AionBankAccountDoesNotExistException(SerializationInfo info, StreamingContext context) : base(info, context)
    {
    }

    private static string BuildErrorMessage(string bankAccountId)
    {
        return $"Aion bankAccount does not exist. AccountNumber and routing number was taken from originator bankAccountId: {bankAccountId}";
    }

    public override string Code => ErrorCodes.AionBankAccountDoesNotExist;
}