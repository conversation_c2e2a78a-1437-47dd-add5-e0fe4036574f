﻿using System.Diagnostics.CodeAnalysis;
using BlueTape.Aion.API.Models.Errors;
using BlueTape.Aion.API.Validators;
using FluentValidation;

namespace BlueTape.Aion.API.Extensions;

[ExcludeFromCodeCoverage]
public static class FluentValidationExtension
{
    public static async Task ValidateAndThrowsManualAsync<T>(this IValidator<T> validator, T model, CancellationToken cancellationToken)
    {
        var validationResult = await validator.ValidateAsync(model, strategy =>
        {
            strategy.IncludeRuleSets(ValidationConstant.ManualValidation);
        }, cancellationToken);

        if (!validationResult.IsValid)
        {
            var errors = validationResult.Errors.Select(failure => new ErrorModel()
            {
                ErrorType = ErrorType.ValidationError,
                Code = failure.ErrorCode,
                Reason = failure.ErrorMessage
            }).ToList();

            throw new FluentValidationException(errors);
        }
    }
}