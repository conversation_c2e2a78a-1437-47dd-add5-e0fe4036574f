import {expect} from '@playwright/test';
import {BaseTest, test} from '../../test-utils';
import {
    sendLMSRequest,
    deleteLoan,
    postLoanRequest,
    postReplaceLoanReceivables,
    postAdminPayment,
    postAdminLatePaymentFee,
    postAdminExtensionFee
} from '../../../api/common/lms-send-request';
import {HttpStatusCode} from 'axios';
import {getSortAi} from '../../../api/sofrai/getSorfAI';
import {calculateReplanDate} from '../../../api/common/lms-send-request';
import {getDaysBetweenDates, changeDateWithDays} from '../../../utils/change-date';

const constants = JSON.parse(JSON.stringify(require('../../../constants/LMStestData.json')));

test.describe.skip(`Penalty Detector API Tests @LMS @API`, async () => {
    const templateId: string = constants.loanTemplateIDs.totalDurationInDays90;
    let loanId: string;
    let amountForLoan: number;
    let loanReceivables;
    let outstandingPrincipalAmount: number;
    let processingPaymentsAmount = 0;
    const basisPoint: number = constants.penaltyInterest.basisPoint;
    let sofrAI30days: number;
    const daysInYear: number = constants.penaltyInterest.daysInYear;
    let amountForDay: string;
    const currentDate: string = BaseTest.getCurrentDate();

    async function calculateOutstandingPrincipalAmount(arrOfReceivables) {
        for (const el of arrOfReceivables) {
            if (el.status !== "Paid" && (el.type === "Installment" || el.type === "LoanFee")) {
                outstandingPrincipalAmount += el.expectedAmount;
            }
        }
    }

    async function calculateProcessingAmount(arrOfReceivables) {
        for (const el of arrOfReceivables) {
            if (el.status === "Processing") {
                processingPaymentsAmount += el.expectedAmount;
            }
        }
    }

    async function changeExpectedDateOfReceivables(arrOfReceivables, element, overdueDays) {
        const expectedDate = await arrOfReceivables[element].expectedDate;
        const overdueDate = await calculateReplanDate(-overdueDays);
        const dif = await getDaysBetweenDates(expectedDate, overdueDate);
        await arrOfReceivables.forEach(async function (el) {
            const getNewDate = await changeDateWithDays(el.expectedDate, -dif);
            el.expectedDate = getNewDate;
        });
    }

    test.beforeAll(async () => {
        const getSorfAi = await getSortAi();
        sofrAI30days = getSorfAi.data.refRates[0].average30day;
    });

    test.beforeEach(async () => {
        amountForLoan = constants.penaltyInterest.enoughAmountForCreatePenalty;
        amountForDay = null;
        outstandingPrincipalAmount = null;
        const createLoan = await postLoanRequest(constants.loans.ownerId, amountForLoan, constants.loans.einHash, templateId);
        loanReceivables = createLoan.data.loanReceivables;
        loanId = createLoan.data.id;
        const patchStatusRequest = await sendLMSRequest('patch', `Loans/${loanId}`, {"status": `Started`});
    });

    test.afterAll(async () => {
        await deleteLoan(loanId);
    });

    test('Loan fee is late to 7 calendar days. Penalty interest should be calculated @lms', async () => {
        loanReceivables[0].expectedDate = await calculateReplanDate(-8);
        await postReplaceLoanReceivables(loanId, loanReceivables);
        const getPenaltyInfo = await sendLMSRequest('patch', `PenaltyDetector/${loanId}`);
        const loanReceivablesWithPenalty = getPenaltyInfo.data.loanReceivables;
        await calculateOutstandingPrincipalAmount(loanReceivablesWithPenalty);
        amountForDay = ((outstandingPrincipalAmount - processingPaymentsAmount) * ((basisPoint * 12) + sofrAI30days / 100) / daysInYear).toFixed(2);
        const penaltyReceivable = loanReceivablesWithPenalty.find(el => el.type === 'PenaltyInterestFee');
        expect(penaltyReceivable, `Response contains Object`).toEqual(expect.any(Object));
        expect(penaltyReceivable.expectedAmount, `Penalty expected amount should be equal ${amountForDay}`).toEqual(Number(amountForDay));
    });

    test('1th Installment is late to 7 calendar days. Penalty interest should be calculated @lms', async () => {
        loanReceivables[1].expectedDate = await calculateReplanDate(-8);
        await postReplaceLoanReceivables(loanId, loanReceivables);
        const getPenaltyInfo = await sendLMSRequest('patch', `PenaltyDetector/${loanId}`);
        const loanReceivablesWithPenalty = getPenaltyInfo.data.loanReceivables;
        await calculateOutstandingPrincipalAmount(loanReceivablesWithPenalty);
        amountForDay = ((outstandingPrincipalAmount - processingPaymentsAmount) * ((basisPoint * 12) + sofrAI30days / 100) / daysInYear).toFixed(2);
        const penaltyReceivable = loanReceivablesWithPenalty.find(el => el.type === 'PenaltyInterestFee');
        expect(penaltyReceivable, `Response contains Object`).toEqual(expect.any(Object));
        expect(penaltyReceivable.expectedAmount, `Penalty expected amount should be equal ${amountForDay}`).toEqual(Number(amountForDay));
    });

    test('Loan fee and 1th Installment is late to 7 calendar days. Penalty interest should be calculated @lms', async () => {
        await changeExpectedDateOfReceivables(loanReceivables, 0, 8);
        await postReplaceLoanReceivables(loanId, loanReceivables);
        const getPenaltyInfo = await sendLMSRequest('patch', `PenaltyDetector/${loanId}`);
        const loanReceivablesWithPenalty = getPenaltyInfo.data.loanReceivables;
        await calculateOutstandingPrincipalAmount(loanReceivablesWithPenalty);
        amountForDay = ((outstandingPrincipalAmount - processingPaymentsAmount) * ((basisPoint * 12) + sofrAI30days / 100) / daysInYear).toFixed(2);
        const penaltyReceivable = loanReceivablesWithPenalty.find(el => el.type === 'PenaltyInterestFee');
        expect(penaltyReceivable, `Response contains Object`).toEqual(expect.any(Object));
        expect(penaltyReceivable.expectedAmount, `Penalty expected amount should be equal ${amountForDay}`).toEqual(Number(amountForDay));
    });

    test('Loan Fee and 1th Installment is late to 4 calendar days and paid, 2th Installment has been paid on time, 3th Installment is late to 3 days. Penalty interest should be calculated @lms', async () => {
        await changeExpectedDateOfReceivables(loanReceivables, 3, 4);
        await postReplaceLoanReceivables(loanId, loanReceivables);
        const amountOfLoanAndFirstInstallment = loanReceivables[0].expectedAmount + loanReceivables[1].expectedAmount;
        const amountOfSecondInstallment = loanReceivables[2].expectedAmount;
        const dateOfPaymentLoanAndFirstInstallment = await changeDateWithDays(loanReceivables[0].expectedDate, 4);
        const dateOfPaymentSecondInstallment = loanReceivables[2].expectedDate;
        const paymentForLoanAndFirstInstallment = await postAdminPayment(loanId, amountOfLoanAndFirstInstallment, dateOfPaymentLoanAndFirstInstallment, constants.loans.ownerId);
        const paymentForSecondInstallment = await postAdminPayment(loanId, amountOfSecondInstallment, dateOfPaymentSecondInstallment, constants.loans.ownerId);
        const getPenaltyInfo = await sendLMSRequest('patch', `PenaltyDetector/${loanId}`);
        const loanReceivablesWithPenalty = getPenaltyInfo.data.loanReceivables;
        await calculateOutstandingPrincipalAmount(loanReceivablesWithPenalty);
        amountForDay = ((outstandingPrincipalAmount - processingPaymentsAmount) * ((basisPoint * 12) + sofrAI30days / 100) / daysInYear).toFixed(2);
        const penaltyReceivable = loanReceivablesWithPenalty.find(el => el.type === 'PenaltyInterestFee');
        expect(penaltyReceivable, `Response contains Object`).toEqual(expect.any(Object));
        expect(penaltyReceivable.expectedAmount, `Penalty expected amount should be equal ${amountForDay}`).toEqual(Number(amountForDay));
        expect(penaltyReceivable.expectedDate, `Penalty expected date should be equal ${currentDate}`).toEqual(currentDate);
    });

    test('Loan Fee and 1th Installment is late to 4 calendar days and paid, 2th Installments has been paid on time, 3th Installment is late to 3 days. Penalty interest should be calculated @lms', async () => {
        await changeExpectedDateOfReceivables(loanReceivables, 3, 4);
        await postReplaceLoanReceivables(loanId, loanReceivables);
        const amountOfLoanAndFirstInstallment = loanReceivables[0].expectedAmount + loanReceivables[1].expectedAmount;
        const amountOfSecondInstallment = loanReceivables[2].expectedAmount;
        const dateOfPaymentLoanAndFirstInstallment = await changeDateWithDays(loanReceivables[0].expectedDate, 4);
        const dateOfPaymentSecondInstallment = loanReceivables[2].expectedDate;
        const paymentForLoanAndFirstInstallment = await postAdminPayment(loanId, amountOfLoanAndFirstInstallment, dateOfPaymentLoanAndFirstInstallment, constants.loans.ownerId);
        const paymentForSecondInstallment = await postAdminPayment(loanId, amountOfSecondInstallment, dateOfPaymentSecondInstallment, constants.loans.ownerId);
        const getPenaltyInfo = await sendLMSRequest('patch', `PenaltyDetector/${loanId}`);
        const loanReceivablesWithPenalty = getPenaltyInfo.data.loanReceivables;
        await calculateOutstandingPrincipalAmount(loanReceivablesWithPenalty);
        amountForDay = ((outstandingPrincipalAmount - processingPaymentsAmount) * ((basisPoint * 12) + sofrAI30days / 100) / daysInYear).toFixed(2);
        const penaltyReceivable = loanReceivablesWithPenalty.find(el => el.type === 'PenaltyInterestFee');
        expect(penaltyReceivable, `Response contains Object`).toEqual(expect.any(Object));
        expect(penaltyReceivable.expectedAmount, `Penalty expected amount should be equal ${amountForDay}`).toEqual(Number(amountForDay));
        expect(penaltyReceivable.expectedDate, `Penalty expected date should be equal ${currentDate}`).toEqual(currentDate);
    });

    test('Loan Fee and 1th Installment paid on time. 2th Installment is late for 2 calendar days, 3th Installment is late to 3 days. Penalty interest should not be calculated @lms', async () => {
        await changeExpectedDateOfReceivables(loanReceivables, 3, 4);
        await postReplaceLoanReceivables(loanId, loanReceivables);
        const amountOfLoanAndFirstInstallment = loanReceivables[0].expectedAmount + loanReceivables[1].expectedAmount;
        const amountOfSecondInstallment = loanReceivables[2].expectedAmount;
        const dateOfPaymentLoanAndFirstInstallment = loanReceivables[0].expectedDate;
        const dateOfPaymentSecondInstallment = await changeDateWithDays(loanReceivables[2].expectedDate, 2);
        const paymentForLoanAndFirstInstallment = await postAdminPayment(loanId, amountOfLoanAndFirstInstallment, dateOfPaymentLoanAndFirstInstallment, constants.loans.ownerId);
        const paymentForSecondInstallment = await postAdminPayment(loanId, amountOfSecondInstallment, dateOfPaymentSecondInstallment, constants.loans.ownerId);
        const getPenaltyInfo = await sendLMSRequest('patch', `PenaltyDetector/${loanId}`);
        expect(getPenaltyInfo.status, `Status code 204`).toEqual(HttpStatusCode.NoContent);
    });

    test('Loan has paid. 1th Installment has paid partially on time, second part has paid in 3 days. 2th Installment has paid partially on time, second part has paid in 3 days. Penalty interest should be calculated @lms', async () => {
        await changeExpectedDateOfReceivables(loanReceivables, 2, 4);
        await postReplaceLoanReceivables(loanId, loanReceivables);
        const amountOfLoanAndHalfOfFirstInstallment = loanReceivables[0].expectedAmount + loanReceivables[1].expectedAmount - loanReceivables[1].expectedAmount / 2;
        const amountOfSecondHalfOfFirstInstallment = loanReceivables[1].expectedAmount / 2;
        const amountOfHalfOfSecondInstallment = loanReceivables[2].expectedAmount / 2;
        const dateOfPaymentLoanAndHalfOfFirstInstallment = loanReceivables[0].expectedDate;
        const dateOfPaymentSecondHalfOfFirstInstallment = await changeDateWithDays(loanReceivables[0].expectedDate, 4);
        const dateOfPaymentFirstHalfOfSecondInstallment = loanReceivables[2].expectedDate;
        const dateOfPaymentSecondHalfOfSecondInstallment = await changeDateWithDays(loanReceivables[2].expectedDate, 4);
        const paymentForLoanAndHalfOfFirstInstallment = await postAdminPayment(loanId, amountOfLoanAndHalfOfFirstInstallment, dateOfPaymentLoanAndHalfOfFirstInstallment, constants.loans.ownerId);
        const paymentForSecondHalfOfFirstInstallment = await postAdminPayment(loanId, amountOfSecondHalfOfFirstInstallment, dateOfPaymentSecondHalfOfFirstInstallment, constants.loans.ownerId);
        const paymentForThirdInstallment = await postAdminPayment(loanId, amountOfHalfOfSecondInstallment, dateOfPaymentFirstHalfOfSecondInstallment, constants.loans.ownerId);
        const paymentForSecondHalfOfSecondInstallment = await postAdminPayment(loanId, amountOfHalfOfSecondInstallment, dateOfPaymentSecondHalfOfSecondInstallment, constants.loans.ownerId);
        const getPenaltyInfo = await sendLMSRequest('patch', `PenaltyDetector/${loanId}`);
        const loanReceivablesWithPenalty = getPenaltyInfo.data.loanReceivables;
        await calculateOutstandingPrincipalAmount(loanReceivablesWithPenalty);
        amountForDay = ((outstandingPrincipalAmount - processingPaymentsAmount) * ((basisPoint * 12) + sofrAI30days / 100) / daysInYear).toFixed(2);
        const penaltyReceivable = loanReceivablesWithPenalty.find(el => el.type === 'PenaltyInterestFee');
        expect(penaltyReceivable, `Response contains Object`).toEqual(expect.any(Object));
        expect(penaltyReceivable.expectedAmount, `Penalty expected amount should be equal ${amountForDay}`).toEqual(Number(amountForDay));
        expect(penaltyReceivable.expectedDate, `Penalty expected date should be equal ${currentDate}`).toEqual(currentDate);
    });

    test('Loan has paid partially. 1th Installment has paid partially on time, second part has paid in 3 days. 2th Installment has paid partially on time, second part has paid in 3 days. Penalty interest should be calculated @lms', async () => {
        await changeExpectedDateOfReceivables(loanReceivables, 5, 4);
        await postReplaceLoanReceivables(loanId, loanReceivables);
        const amountOfHalfOfLoanFee = loanReceivables[0].expectedAmount / 2;
        const amountOfSecondHalfOfLoanFeeAndFirstInstallment = amountOfHalfOfLoanFee + loanReceivables[1].expectedAmount;
        const amountOfHalfOfSecondInstallment = loanReceivables[2].expectedAmount;
        const amountOfHalfOfThirdInstallment = loanReceivables[3].expectedAmount;
        const amountOfHalfOfFourthInstallment = loanReceivables[4].expectedAmount;
        const amountOfHalfOfFifthInstallment = loanReceivables[5].expectedAmount;
        const dateOfPaymentHalfOfLoan = loanReceivables[0].expectedDate;
        const dateOfPaymentHalfOfLoanAndFirstInstallment = await changeDateWithDays(loanReceivables[1].expectedDate, 4);
        const dateOfPaymentOfSecondInstallment = loanReceivables[2].expectedDate;
        const dateOfPaymentOfThirdInstallment = loanReceivables[3].expectedDate;
        const dateOfPaymentOfFourthInstallment = loanReceivables[4].expectedDate;
        const dateOfPaymentOfFifthInstallment = await changeDateWithDays(loanReceivables[5].expectedDate, 4);
        const paymentForHalfOfLoan = await postAdminPayment(loanId, amountOfHalfOfLoanFee, dateOfPaymentHalfOfLoan, constants.loans.ownerId);
        const paymentForHalfOfLoanAndFirstInstallment = await postAdminPayment(loanId, amountOfSecondHalfOfLoanFeeAndFirstInstallment, dateOfPaymentHalfOfLoanAndFirstInstallment, constants.loans.ownerId);
        const paymentForSecondInstallment = await postAdminPayment(loanId, amountOfHalfOfSecondInstallment, dateOfPaymentOfSecondInstallment, constants.loans.ownerId);
        const paymentForThirdInstallment = await postAdminPayment(loanId, amountOfHalfOfThirdInstallment, dateOfPaymentOfThirdInstallment, constants.loans.ownerId);
        const paymentForFourthInstallment = await postAdminPayment(loanId, amountOfHalfOfFourthInstallment, dateOfPaymentOfFourthInstallment, constants.loans.ownerId);
        const paymentForFifthInstallment = await postAdminPayment(loanId, amountOfHalfOfFifthInstallment, dateOfPaymentOfFifthInstallment, constants.loans.ownerId);
        const getPenaltyInfo = await sendLMSRequest('patch', `PenaltyDetector/${loanId}`);
        const loanReceivablesWithPenalty = getPenaltyInfo.data.loanReceivables;
        await calculateOutstandingPrincipalAmount(loanReceivablesWithPenalty);
        amountForDay = ((outstandingPrincipalAmount - processingPaymentsAmount) * ((basisPoint * 12) + sofrAI30days / 100) / daysInYear).toFixed(2);
        const penaltyReceivable = loanReceivablesWithPenalty.find(el => el.type === 'PenaltyInterestFee');
        expect(penaltyReceivable, `Response contains Object`).toEqual(expect.any(Object));
        expect(penaltyReceivable.expectedAmount, `Penalty expected amount should be equal ${amountForDay}`).toEqual(Number(amountForDay));
        expect(penaltyReceivable.expectedDate, `Penalty expected date should be equal ${currentDate}`).toEqual(currentDate);
    });

    test('Loan Fee is late to 4 calendar days and paid. Penalty interest should not be calculated @lms', async () => {
        await changeExpectedDateOfReceivables(loanReceivables, 0, 4);
        await postReplaceLoanReceivables(loanId, loanReceivables);
        const amountOfLoanFee = loanReceivables[0].expectedAmount;
        const dateOfPaymentLoanFee = await changeDateWithDays(loanReceivables[0].expectedDate, 4);
        const paymentForLoanFee = await postAdminPayment(loanId, amountOfLoanFee, dateOfPaymentLoanFee, constants.loans.ownerId);
        const getPenaltyInfo = await sendLMSRequest('patch', `PenaltyDetector/${loanId}`);
        expect(getPenaltyInfo.status, `Status code 204`).toEqual(HttpStatusCode.NoContent);
    });

    test('Loan Fee and 1th Installment are late to 7 calendar days and paid. Run over due detector. Penalty interest should be calculated. Outstanding Principal Amount should be calculate without Late Fee @lms', async () => {
        await changeExpectedDateOfReceivables(loanReceivables, 0, 8);
        await postReplaceLoanReceivables(loanId, loanReceivables);
        const overDueResponse = await sendLMSRequest('patch', `OverDueDetector`);
        const getPenaltyInfo = await sendLMSRequest('patch', `PenaltyDetector/${loanId}`);
        const loanReceivablesWithPenalty = getPenaltyInfo.data.loanReceivables;
        await calculateOutstandingPrincipalAmount(loanReceivablesWithPenalty);
        amountForDay = ((outstandingPrincipalAmount - processingPaymentsAmount) * ((basisPoint * 12) + sofrAI30days / 100) / daysInYear).toFixed(2);
        const penaltyReceivable = loanReceivablesWithPenalty.find(el => el.type === 'PenaltyInterestFee');
        expect(penaltyReceivable, `Response contains Object`).toEqual(expect.any(Object));
        expect(penaltyReceivable.expectedAmount, `Penalty expected amount should be equal ${amountForDay}`).toEqual(Number(amountForDay));
    });

    test('Loan Fee and 1th Installment are late to 7 calendar days. Penalty interest has calculated. Start date and End date should be equal expected date of 1th Installment  @lms', async () => {
        await changeExpectedDateOfReceivables(loanReceivables, 0, 8);
        await postReplaceLoanReceivables(loanId, loanReceivables);
        const dateOfPaymentOfFirstInstallment = loanReceivables[1].expectedDate;
        const getPenaltyInfo = await sendLMSRequest('patch', `PenaltyDetector/${loanId}`);
        const loanReceivablesWithPenalty = getPenaltyInfo.data.loanReceivables;
        const penaltyReceivable = loanReceivablesWithPenalty.find(el => el.type === 'PenaltyInterestFee');
        expect(penaltyReceivable, `Response contains Object`).toEqual(expect.any(Object));
        expect(penaltyReceivable.penaltyInterestDetails.penaltyStartDate, `Start date should be equal ${dateOfPaymentOfFirstInstallment}`).toEqual(dateOfPaymentOfFirstInstallment);
        expect(penaltyReceivable.penaltyInterestDetails.penaltyEndDate, `End date should be equal ${dateOfPaymentOfFirstInstallment}`).toEqual(dateOfPaymentOfFirstInstallment);
    });

    test('Loan Fee and 1th Installment are late to 7 calendar days and paid. Start over due detector, add late and extension fee manualy. Penalty interest should be calculated correctly @lms', async () => {
        await changeExpectedDateOfReceivables(loanReceivables, 0, 8);
        await postReplaceLoanReceivables(loanId, loanReceivables);
        const overDueResponse = await sendLMSRequest('patch', `OverDueDetector`);
        await postAdminLatePaymentFee(loanId, constants.penaltyInterest.notEnoughAmountForCreatePenalty, currentDate, constants.loans.ownerId);
        await postAdminExtensionFee(loanId, constants.penaltyInterest.notEnoughAmountForCreatePenalty, currentDate, constants.loans.ownerId);
        const getPenaltyInfo = await sendLMSRequest('patch', `PenaltyDetector/${loanId}`);
        const loanReceivablesWithPenalty = getPenaltyInfo.data.loanReceivables;
        await calculateOutstandingPrincipalAmount(loanReceivablesWithPenalty);
        amountForDay = ((outstandingPrincipalAmount - processingPaymentsAmount) * ((basisPoint * 12) + sofrAI30days / 100) / daysInYear).toFixed(2);
        const penaltyReceivable = loanReceivablesWithPenalty.find(el => el.type === 'PenaltyInterestFee');
        expect(penaltyReceivable.expectedAmount, `Penalty expected amount should be equal ${amountForDay}`).toEqual(Number(amountForDay));
        expect(penaltyReceivable, `Response contains Object`).toEqual(expect.any(Object));
    });

    test('First installmet was late to 6 calendar days Penalty interest should not be calculated @lms', async () => {
        await changeExpectedDateOfReceivables(loanReceivables, 1, 7);
        await postReplaceLoanReceivables(loanId, loanReceivables);
        const getPenaltyInfo = await sendLMSRequest('patch', `PenaltyDetector/${loanId}`);
        expect(getPenaltyInfo.status, `Status code 204`).toEqual(HttpStatusCode.NoContent);
    });

    test('Loan Fee and 1th Installment paid on with late 2 days. 2th Installment is late for 3 calendar days. Penalty interest should not be calculated @lms', async () => {
        await changeExpectedDateOfReceivables(loanReceivables, 2, 4);
        await postReplaceLoanReceivables(loanId, loanReceivables);
        const amountOfLoanAndFirstInstallment = loanReceivables[0].expectedAmount + loanReceivables[1].expectedAmount;
        const dateOfPaymentLoanAndFirstInstallment = await changeDateWithDays(loanReceivables[0].expectedDate, 2);
        const paymentForLoanAndFirstInstallment = await postAdminPayment(loanId, amountOfLoanAndFirstInstallment, dateOfPaymentLoanAndFirstInstallment, constants.loans.ownerId);
        const getPenaltyInfo = await sendLMSRequest('patch', `PenaltyDetector/${loanId}`);
        expect(getPenaltyInfo.status, `Status code 204`).toEqual(HttpStatusCode.NoContent);
    });

    test('Loan Fee and 1th Installment were late to 3 days and paid. 2th Installment is late to 2 calendar days. Penalty interest should not be calculated @lms', async () => {
        await changeExpectedDateOfReceivables(loanReceivables, 2, 3);
        await postReplaceLoanReceivables(loanId, loanReceivables);
        const amountOfLoanAndFirstInstallment = loanReceivables[0].expectedAmount + loanReceivables[1].expectedAmount;
        const dateOfPaymentLoanAndFirstInstallment = await changeDateWithDays(loanReceivables[0].expectedDate, 4);
        const paymentForLoanAndFirstInstallment = await postAdminPayment(loanId, amountOfLoanAndFirstInstallment, dateOfPaymentLoanAndFirstInstallment, constants.loans.ownerId);
        const getPenaltyInfo = await sendLMSRequest('patch', `PenaltyDetector/${loanId}`);
        expect(getPenaltyInfo.status, `Status code 204`).toEqual(HttpStatusCode.NoContent);
    });

    test('Loan Fee and 1th Installment were late to 3 days. Penalty interest should not be calculated @lms', async () => {
        await changeExpectedDateOfReceivables(loanReceivables, 1, 4);
        await postReplaceLoanReceivables(loanId, loanReceivables);
        const getPenaltyInfo = await sendLMSRequest('patch', `PenaltyDetector/${loanId}`);
        expect(getPenaltyInfo.status, `Status code 204`).toEqual(HttpStatusCode.NoContent);
    });

    // test('Loan Fee and 1th Installment are processing. 2th Installment is late to 7 days. Outstanding Principal Amount should be calculated without Installment with processing state @lms', async () => {
    //     await changeExpectedDateOfReceivables(loanReceivables, 2, 8);
    //     await postReplaceLoanReceivables(loanId, loanReceivables);
    //     const amountOfLoanFeeAndFirstInstallment = loanReceivables[0].expectedAmount + loanReceivables[1].expectedAmount;
    //     const dateOfPaymentLoanFeeAndFirstInstallment = loanReceivables[0].expectedDate;
    //     const requestBody = {
    //         "amount": amountOfLoanFeeAndFirstInstallment,
    //         "loanId": loanId,
    //         "type": 'AutoDebit',
    //     };
    //     const response = await sendLMSRequest('post', `Payments`, requestBody);
    //     const getPenaltyInfo = await sendLMSRequest('patch', `PenaltyDetector/${loanId}`);
    //     const loanReceivablesWithPenalty = getPenaltyInfo.data.loanReceivables;
    //     await calculateOutstandingPrincipalAmount(loanReceivablesWithPenalty);
    //     await calculateProcessingAmount(loanReceivablesWithPenalty);
    //     amountForDay = ((outstandingPrincipalAmount - processingPaymentsAmount) * ((basisPoint * 12) + sofrAI30days/100)/daysInYear).toFixed(2);
    //     const penaltyReceivable = loanReceivablesWithPenalty.find(el => el.type === 'PenaltyInterestFee');
    //     expect(penaltyReceivable.expectedAmount, `Penalty expected amount should be equal ${amountForDay}`).toEqual(Number(amountForDay));
    //     expect(penaltyReceivable, `Response contains Object`).toEqual(expect.any(Object));
    // });

    test('Loan Fee and 1th Installment is late to 7 calendar days. Penalty interest less than 1$. Penalty interest should not be calculated @lms', async () => {
        amountForLoan = constants.penaltyInterest.notEnoughAmountForCreatePenalty;
        const createLoan = await postLoanRequest(constants.loans.ownerId, amountForLoan, constants.loans.einHash, templateId);
        loanReceivables = createLoan.data.loanReceivables;
        loanId = createLoan.data.id;
        const patchStatusRequest = await sendLMSRequest('patch', `Loans/${loanId}`, {"status": `Started`});
        await changeExpectedDateOfReceivables(loanReceivables, 0, 8);
        await postReplaceLoanReceivables(loanId, loanReceivables);
        const getPenaltyInfo = await sendLMSRequest('patch', `PenaltyDetector/${loanId}`);
        await calculateOutstandingPrincipalAmount(loanReceivables);
        amountForDay = ((outstandingPrincipalAmount - processingPaymentsAmount) * ((basisPoint * 12) + sofrAI30days / 100) / daysInYear).toFixed(2);
        expect(getPenaltyInfo.status, `Status code 204`).toEqual(HttpStatusCode.NoContent);
        expect(Number(amountForDay), `Penalty expected amount should be equal ${Number(amountForDay)}`).toBeLessThan(1);
    });
});
