{"Version": 1, "WorkspaceRootPath": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\loan-management-service\\BlueTape.Services.LMS\\", "Documents": [{"AbsoluteMoniker": "D:0:0:{0533DD8A-67A3-4B67-B55B-706736B3C199}|BlueTape.Services.LMS.Application\\BlueTape.Services.LMS.Application.csproj|c:\\users\\<USER>\\source\\repos\\bluetape\\loan-management-service\\bluetape.services.lms\\bluetape.services.lms.application\\services\\loanservices\\createloanservice.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{0533DD8A-67A3-4B67-B55B-706736B3C199}|BlueTape.Services.LMS.Application\\BlueTape.Services.LMS.Application.csproj|solutionrelative:bluetape.services.lms.application\\services\\loanservices\\createloanservice.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{0533DD8A-67A3-4B67-B55B-706736B3C199}|BlueTape.Services.LMS.Application\\BlueTape.Services.LMS.Application.csproj|c:\\users\\<USER>\\source\\repos\\bluetape\\loan-management-service\\bluetape.services.lms\\bluetape.services.lms.application\\services\\paymenteligibilitychecker.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{0533DD8A-67A3-4B67-B55B-706736B3C199}|BlueTape.Services.LMS.Application\\BlueTape.Services.LMS.Application.csproj|solutionrelative:bluetape.services.lms.application\\services\\paymenteligibilitychecker.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{0533DD8A-67A3-4B67-B55B-706736B3C199}|BlueTape.Services.LMS.Application\\BlueTape.Services.LMS.Application.csproj|c:\\users\\<USER>\\source\\repos\\bluetape\\loan-management-service\\bluetape.services.lms\\bluetape.services.lms.application\\services\\loanservices\\loanservice.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{0533DD8A-67A3-4B67-B55B-706736B3C199}|BlueTape.Services.LMS.Application\\BlueTape.Services.LMS.Application.csproj|solutionrelative:bluetape.services.lms.application\\services\\loanservices\\loanservice.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{47EA21B1-3C61-4458-9AAB-FC66B8EB8AE6}|BlueTape.Services.LMS.API\\BlueTape.Services.LMS.API.csproj|c:\\users\\<USER>\\source\\repos\\bluetape\\loan-management-service\\bluetape.services.lms\\bluetape.services.lms.api\\controllers\\admincontroller.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{47EA21B1-3C61-4458-9AAB-FC66B8EB8AE6}|BlueTape.Services.LMS.API\\BlueTape.Services.LMS.API.csproj|solutionrelative:bluetape.services.lms.api\\controllers\\admincontroller.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{47EA21B1-3C61-4458-9AAB-FC66B8EB8AE6}|BlueTape.Services.LMS.API\\BlueTape.Services.LMS.API.csproj|c:\\users\\<USER>\\source\\repos\\bluetape\\loan-management-service\\bluetape.services.lms\\bluetape.services.lms.api\\controllers\\agingreportscontroller.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{47EA21B1-3C61-4458-9AAB-FC66B8EB8AE6}|BlueTape.Services.LMS.API\\BlueTape.Services.LMS.API.csproj|solutionrelative:bluetape.services.lms.api\\controllers\\agingreportscontroller.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{47EA21B1-3C61-4458-9AAB-FC66B8EB8AE6}|BlueTape.Services.LMS.API\\BlueTape.Services.LMS.API.csproj|c:\\users\\<USER>\\source\\repos\\bluetape\\loan-management-service\\bluetape.services.lms\\bluetape.services.lms.api\\controllers\\loancontroller.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{47EA21B1-3C61-4458-9AAB-FC66B8EB8AE6}|BlueTape.Services.LMS.API\\BlueTape.Services.LMS.API.csproj|solutionrelative:bluetape.services.lms.api\\controllers\\loancontroller.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{0533DD8A-67A3-4B67-B55B-706736B3C199}|BlueTape.Services.LMS.Application\\BlueTape.Services.LMS.Application.csproj|c:\\users\\<USER>\\source\\repos\\bluetape\\loan-management-service\\bluetape.services.lms\\bluetape.services.lms.application\\services\\loanservices\\loandownpaymentsservice.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{0533DD8A-67A3-4B67-B55B-706736B3C199}|BlueTape.Services.LMS.Application\\BlueTape.Services.LMS.Application.csproj|solutionrelative:bluetape.services.lms.application\\services\\loanservices\\loandownpaymentsservice.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{87C3D140-B18E-471A-9801-2BA0EEAD10BC}|BlueTape.Services.LMS.IntegrationTests\\BlueTape.Services.LMS.IntegrationTests.csproj|c:\\users\\<USER>\\source\\repos\\bluetape\\loan-management-service\\bluetape.services.lms\\bluetape.services.lms.integrationtests\\tests\\base\\lmsintegrationtestsbase.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{87C3D140-B18E-471A-9801-2BA0EEAD10BC}|BlueTape.Services.LMS.IntegrationTests\\BlueTape.Services.LMS.IntegrationTests.csproj|solutionrelative:bluetape.services.lms.integrationtests\\tests\\base\\lmsintegrationtestsbase.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{47EA21B1-3C61-4458-9AAB-FC66B8EB8AE6}|BlueTape.Services.LMS.API\\BlueTape.Services.LMS.API.csproj|c:\\users\\<USER>\\source\\repos\\bluetape\\loan-management-service\\bluetape.services.lms\\bluetape.services.lms.api\\program.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{47EA21B1-3C61-4458-9AAB-FC66B8EB8AE6}|BlueTape.Services.LMS.API\\BlueTape.Services.LMS.API.csproj|solutionrelative:bluetape.services.lms.api\\program.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{87C3D140-B18E-471A-9801-2BA0EEAD10BC}|BlueTape.Services.LMS.IntegrationTests\\BlueTape.Services.LMS.IntegrationTests.csproj|c:\\users\\<USER>\\source\\repos\\bluetape\\loan-management-service\\bluetape.services.lms\\bluetape.services.lms.integrationtests\\tests\\changereceivableintegrationtests.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{87C3D140-B18E-471A-9801-2BA0EEAD10BC}|BlueTape.Services.LMS.IntegrationTests\\BlueTape.Services.LMS.IntegrationTests.csproj|solutionrelative:bluetape.services.lms.integrationtests\\tests\\changereceivableintegrationtests.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{47EA21B1-3C61-4458-9AAB-FC66B8EB8AE6}|BlueTape.Services.LMS.API\\BlueTape.Services.LMS.API.csproj|c:\\users\\<USER>\\source\\repos\\bluetape\\loan-management-service\\bluetape.services.lms\\bluetape.services.lms.api\\appsettings.json||{90A6B3A7-C1A3-4009-A288-E2FF89E96FA0}", "RelativeMoniker": "D:0:0:{47EA21B1-3C61-4458-9AAB-FC66B8EB8AE6}|BlueTape.Services.LMS.API\\BlueTape.Services.LMS.API.csproj|solutionrelative:bluetape.services.lms.api\\appsettings.json||{90A6B3A7-C1A3-4009-A288-E2FF89E96FA0}"}, {"AbsoluteMoniker": "D:0:0:{47EA21B1-3C61-4458-9AAB-FC66B8EB8AE6}|BlueTape.Services.LMS.API\\BlueTape.Services.LMS.API.csproj|c:\\users\\<USER>\\source\\repos\\bluetape\\loan-management-service\\bluetape.services.lms\\bluetape.services.lms.api\\appsettings.beta.json||{90A6B3A7-C1A3-4009-A288-E2FF89E96FA0}", "RelativeMoniker": "D:0:0:{47EA21B1-3C61-4458-9AAB-FC66B8EB8AE6}|BlueTape.Services.LMS.API\\BlueTape.Services.LMS.API.csproj|solutionrelative:bluetape.services.lms.api\\appsettings.beta.json||{90A6B3A7-C1A3-4009-A288-E2FF89E96FA0}"}, {"AbsoluteMoniker": "D:0:0:{0533DD8A-67A3-4B67-B55B-706736B3C199}|BlueTape.Services.LMS.Application\\BlueTape.Services.LMS.Application.csproj|c:\\users\\<USER>\\source\\repos\\bluetape\\loan-management-service\\bluetape.services.lms\\bluetape.services.lms.application\\di\\dependencyregistrar.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{0533DD8A-67A3-4B67-B55B-706736B3C199}|BlueTape.Services.LMS.Application\\BlueTape.Services.LMS.Application.csproj|solutionrelative:bluetape.services.lms.application\\di\\dependencyregistrar.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{47EA21B1-3C61-4458-9AAB-FC66B8EB8AE6}|BlueTape.Services.LMS.API\\BlueTape.Services.LMS.API.csproj|c:\\users\\<USER>\\source\\repos\\bluetape\\loan-management-service\\bluetape.services.lms\\bluetape.services.lms.api\\middlewares\\exceptionmiddleware.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{47EA21B1-3C61-4458-9AAB-FC66B8EB8AE6}|BlueTape.Services.LMS.API\\BlueTape.Services.LMS.API.csproj|solutionrelative:bluetape.services.lms.api\\middlewares\\exceptionmiddleware.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{A2FE74E1-B743-11D0-AE1A-00A0C90FFFC3}|<MiscFiles>|C:\\Users\\<USER>\\source\\repos\\BlueTape\\loan-management-service\\infra\\modules\\mod-fapp\\main.tf||{8B382828-6202-11D1-8870-0000F87579D2}"}, {"AbsoluteMoniker": "D:0:0:{47EA21B1-3C61-4458-9AAB-FC66B8EB8AE6}|BlueTape.Services.LMS.API\\BlueTape.Services.LMS.API.csproj|c:\\users\\<USER>\\source\\repos\\bluetape\\loan-management-service\\bluetape.services.lms\\bluetape.services.lms.api\\controllers\\сreditstatusdetectorcontroller.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{47EA21B1-3C61-4458-9AAB-FC66B8EB8AE6}|BlueTape.Services.LMS.API\\BlueTape.Services.LMS.API.csproj|solutionrelative:bluetape.services.lms.api\\controllers\\сreditstatusdetectorcontroller.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{42A2F414-9839-4E50-BEE6-811F288E2932}|Functions\\BlueTape.Functions.LMS.CreditStatusDetector\\BlueTape.Functions.LMS.CreditStatusDetector.csproj|c:\\users\\<USER>\\source\\repos\\bluetape\\loan-management-service\\bluetape.services.lms\\functions\\bluetape.functions.lms.creditstatusdetector\\creditstatusdetector.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{42A2F414-9839-4E50-BEE6-811F288E2932}|Functions\\BlueTape.Functions.LMS.CreditStatusDetector\\BlueTape.Functions.LMS.CreditStatusDetector.csproj|solutionrelative:functions\\bluetape.functions.lms.creditstatusdetector\\creditstatusdetector.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{0533DD8A-67A3-4B67-B55B-706736B3C199}|BlueTape.Services.LMS.Application\\BlueTape.Services.LMS.Application.csproj|c:\\users\\<USER>\\source\\repos\\bluetape\\loan-management-service\\bluetape.services.lms\\bluetape.services.lms.application\\services\\creditservices\\creditservice.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{0533DD8A-67A3-4B67-B55B-706736B3C199}|BlueTape.Services.LMS.Application\\BlueTape.Services.LMS.Application.csproj|solutionrelative:bluetape.services.lms.application\\services\\creditservices\\creditservice.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{42A2F414-9839-4E50-BEE6-811F288E2932}|Functions\\BlueTape.Functions.LMS.CreditStatusDetector\\BlueTape.Functions.LMS.CreditStatusDetector.csproj|c:\\users\\<USER>\\source\\repos\\bluetape\\loan-management-service\\bluetape.services.lms\\functions\\bluetape.functions.lms.creditstatusdetector\\creditstatusconsumer.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{42A2F414-9839-4E50-BEE6-811F288E2932}|Functions\\BlueTape.Functions.LMS.CreditStatusDetector\\BlueTape.Functions.LMS.CreditStatusDetector.csproj|solutionrelative:functions\\bluetape.functions.lms.creditstatusdetector\\creditstatusconsumer.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{0533DD8A-67A3-4B67-B55B-706736B3C199}|BlueTape.Services.LMS.Application\\BlueTape.Services.LMS.Application.csproj|c:\\users\\<USER>\\source\\repos\\bluetape\\loan-management-service\\bluetape.services.lms\\bluetape.services.lms.application\\services\\creditservices\\creditstatusdetectorservice.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{0533DD8A-67A3-4B67-B55B-706736B3C199}|BlueTape.Services.LMS.Application\\BlueTape.Services.LMS.Application.csproj|solutionrelative:bluetape.services.lms.application\\services\\creditservices\\creditstatusdetectorservice.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{42A2F414-9839-4E50-BEE6-811F288E2932}|Functions\\BlueTape.Functions.LMS.CreditStatusDetector\\BlueTape.Functions.LMS.CreditStatusDetector.csproj|c:\\users\\<USER>\\source\\repos\\bluetape\\loan-management-service\\bluetape.services.lms\\functions\\bluetape.functions.lms.creditstatusdetector\\bluetape.functions.lms.creditstatusdetector.csproj||{FA3CD31E-987B-443A-9B81-186104E8DAC1}|", "RelativeMoniker": "D:0:0:{42A2F414-9839-4E50-BEE6-811F288E2932}|Functions\\BlueTape.Functions.LMS.CreditStatusDetector\\BlueTape.Functions.LMS.CreditStatusDetector.csproj|solutionrelative:functions\\bluetape.functions.lms.creditstatusdetector\\bluetape.functions.lms.creditstatusdetector.csproj||{FA3CD31E-987B-443A-9B81-186104E8DAC1}|"}, {"AbsoluteMoniker": "D:0:0:{5DA9CB35-7181-4B23-8DF7-7E351B973F9B}|BlueTape.Services.ARS.IntegrationTests\\BlueTape.Services.ARS.IntegrationTests.csproj|c:\\users\\<USER>\\source\\repos\\bluetape\\loan-management-service\\bluetape.services.lms\\bluetape.services.ars.integrationtests\\tests\\base\\arsintegrationtestsbase.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{5DA9CB35-7181-4B23-8DF7-7E351B973F9B}|BlueTape.Services.ARS.IntegrationTests\\BlueTape.Services.ARS.IntegrationTests.csproj|solutionrelative:bluetape.services.ars.integrationtests\\tests\\base\\arsintegrationtestsbase.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}], "DocumentGroupContainers": [{"Orientation": 0, "VerticalTabListWidth": 256, "DocumentGroups": [{"DockedWidth": 200, "SelectedChildIndex": 2, "Children": [{"$type": "Document", "DocumentIndex": 1, "Title": "PaymentEligibilityChecker.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\loan-management-service\\BlueTape.Services.LMS\\BlueTape.Services.LMS.Application\\Services\\PaymentEligibilityChecker.cs", "RelativeDocumentMoniker": "BlueTape.Services.LMS.Application\\Services\\PaymentEligibilityChecker.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\loan-management-service\\BlueTape.Services.LMS\\BlueTape.Services.LMS.Application\\Services\\PaymentEligibilityChecker.cs", "RelativeToolTip": "BlueTape.Services.LMS.Application\\Services\\PaymentEligibilityChecker.cs", "ViewState": "AgIAAAIAAAAAAAAAAAAawA4AAAAZAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-03T09:11:58.197Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 4, "Title": "AgingReportsController.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\loan-management-service\\BlueTape.Services.LMS\\BlueTape.Services.LMS.API\\Controllers\\AgingReportsController.cs", "RelativeDocumentMoniker": "BlueTape.Services.LMS.API\\Controllers\\AgingReportsController.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\loan-management-service\\BlueTape.Services.LMS\\BlueTape.Services.LMS.API\\Controllers\\AgingReportsController.cs", "RelativeToolTip": "BlueTape.Services.LMS.API\\Controllers\\AgingReportsController.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-02T14:43:20.518Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 0, "Title": "CreateLoanService.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\loan-management-service\\BlueTape.Services.LMS\\BlueTape.Services.LMS.Application\\Services\\LoanServices\\CreateLoanService.cs", "RelativeDocumentMoniker": "BlueTape.Services.LMS.Application\\Services\\LoanServices\\CreateLoanService.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\loan-management-service\\BlueTape.Services.LMS\\BlueTape.Services.LMS.Application\\Services\\LoanServices\\CreateLoanService.cs", "RelativeToolTip": "BlueTape.Services.LMS.Application\\Services\\LoanServices\\CreateLoanService.cs", "ViewState": "AgIAAHgAAAAAAAAAAIA3wHwAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-02T11:28:16.328Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 13, "Title": "ExceptionMiddleware.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\loan-management-service\\BlueTape.Services.LMS\\BlueTape.Services.LMS.API\\Middlewares\\ExceptionMiddleware.cs", "RelativeDocumentMoniker": "BlueTape.Services.LMS.API\\Middlewares\\ExceptionMiddleware.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\loan-management-service\\BlueTape.Services.LMS\\BlueTape.Services.LMS.API\\Middlewares\\ExceptionMiddleware.cs", "RelativeToolTip": "BlueTape.Services.LMS.API\\Middlewares\\ExceptionMiddleware.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAcAAABfAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-02T11:27:24.296Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 6, "Title": "LoanDownPaymentsService.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\loan-management-service\\BlueTape.Services.LMS\\BlueTape.Services.LMS.Application\\Services\\LoanServices\\LoanDownPaymentsService.cs", "RelativeDocumentMoniker": "BlueTape.Services.LMS.Application\\Services\\LoanServices\\LoanDownPaymentsService.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\loan-management-service\\BlueTape.Services.LMS\\BlueTape.Services.LMS.Application\\Services\\LoanServices\\LoanDownPaymentsService.cs", "RelativeToolTip": "BlueTape.Services.LMS.Application\\Services\\LoanServices\\LoanDownPaymentsService.cs", "ViewState": "AgIAAJEAAAAAAAAAAAAnwKEAAAAkAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-02T11:33:21.075Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 8, "Title": "Program.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\loan-management-service\\BlueTape.Services.LMS\\BlueTape.Services.LMS.API\\Program.cs", "RelativeDocumentMoniker": "BlueTape.Services.LMS.API\\Program.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\loan-management-service\\BlueTape.Services.LMS\\BlueTape.Services.LMS.API\\Program.cs", "RelativeToolTip": "BlueTape.Services.LMS.API\\Program.cs", "ViewState": "AgIAAHMAAAAAAAAAAAAwwJoAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-02T11:28:22.227Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 7, "Title": "LmsIntegrationTestsBase.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\loan-management-service\\BlueTape.Services.LMS\\BlueTape.Services.LMS.IntegrationTests\\Tests\\Base\\LmsIntegrationTestsBase.cs", "RelativeDocumentMoniker": "BlueTape.Services.LMS.IntegrationTests\\Tests\\Base\\LmsIntegrationTestsBase.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\loan-management-service\\BlueTape.Services.LMS\\BlueTape.Services.LMS.IntegrationTests\\Tests\\Base\\LmsIntegrationTestsBase.cs", "RelativeToolTip": "BlueTape.Services.LMS.IntegrationTests\\Tests\\Base\\LmsIntegrationTestsBase.cs", "ViewState": "AgIAAEIAAAAAAAAAAAD4v2UAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-01T12:17:53.281Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 3, "Title": "AdminController.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\loan-management-service\\BlueTape.Services.LMS\\BlueTape.Services.LMS.API\\Controllers\\AdminController.cs", "RelativeDocumentMoniker": "BlueTape.Services.LMS.API\\Controllers\\AdminController.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\loan-management-service\\BlueTape.Services.LMS\\BlueTape.Services.LMS.API\\Controllers\\AdminController.cs", "RelativeToolTip": "BlueTape.Services.LMS.API\\Controllers\\AdminController.cs", "ViewState": "AgIAAAkDAAAAAAAAAAA4wBYDAAAvAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-30T11:27:47.591Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 2, "Title": "LoanService.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\loan-management-service\\BlueTape.Services.LMS\\BlueTape.Services.LMS.Application\\Services\\LoanServices\\LoanService.cs", "RelativeDocumentMoniker": "BlueTape.Services.LMS.Application\\Services\\LoanServices\\LoanService.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\loan-management-service\\BlueTape.Services.LMS\\BlueTape.Services.LMS.Application\\Services\\LoanServices\\LoanService.cs", "RelativeToolTip": "BlueTape.Services.LMS.Application\\Services\\LoanServices\\LoanService.cs", "ViewState": "AgIAAEkAAAAAAAAAAAAmwFcAAAAyAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-30T11:23:52.933Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 12, "Title": "DependencyRegistrar.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\loan-management-service\\BlueTape.Services.LMS\\BlueTape.Services.LMS.Application\\DI\\DependencyRegistrar.cs", "RelativeDocumentMoniker": "BlueTape.Services.LMS.Application\\DI\\DependencyRegistrar.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\loan-management-service\\BlueTape.Services.LMS\\BlueTape.Services.LMS.Application\\DI\\DependencyRegistrar.cs", "RelativeToolTip": "BlueTape.Services.LMS.Application\\DI\\DependencyRegistrar.cs", "ViewState": "AgIAAKkAAAAAAAAAAAAuwK0AAABpAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-02T11:30:21.213Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 5, "Title": "LoanController.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\loan-management-service\\BlueTape.Services.LMS\\BlueTape.Services.LMS.API\\Controllers\\LoanController.cs", "RelativeDocumentMoniker": "BlueTape.Services.LMS.API\\Controllers\\LoanController.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\loan-management-service\\BlueTape.Services.LMS\\BlueTape.Services.LMS.API\\Controllers\\LoanController.cs", "RelativeToolTip": "BlueTape.Services.LMS.API\\Controllers\\LoanController.cs", "ViewState": "AgIAAEIBAAAAAAAAAAAQwAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-30T11:22:54.823Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 9, "Title": "ChangeReceivableIntegrationTests.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\loan-management-service\\BlueTape.Services.LMS\\BlueTape.Services.LMS.IntegrationTests\\Tests\\ChangeReceivableIntegrationTests.cs", "RelativeDocumentMoniker": "BlueTape.Services.LMS.IntegrationTests\\Tests\\ChangeReceivableIntegrationTests.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\loan-management-service\\BlueTape.Services.LMS\\BlueTape.Services.LMS.IntegrationTests\\Tests\\ChangeReceivableIntegrationTests.cs", "RelativeToolTip": "BlueTape.Services.LMS.IntegrationTests\\Tests\\ChangeReceivableIntegrationTests.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-02T11:35:27.768Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 11, "Title": "appsettings.beta.json", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\loan-management-service\\BlueTape.Services.LMS\\BlueTape.Services.LMS.API\\appsettings.beta.json", "RelativeDocumentMoniker": "BlueTape.Services.LMS.API\\appsettings.beta.json", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\loan-management-service\\BlueTape.Services.LMS\\BlueTape.Services.LMS.API\\appsettings.beta.json", "RelativeToolTip": "BlueTape.Services.LMS.API\\appsettings.beta.json", "ViewState": "AgIAAAAAAAAAAAAAAAAAAA4AAAADAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.001642|", "WhenOpened": "2025-07-02T11:37:33.142Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 10, "Title": "appsettings.json", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\loan-management-service\\BlueTape.Services.LMS\\BlueTape.Services.LMS.API\\appsettings.json", "RelativeDocumentMoniker": "BlueTape.Services.LMS.API\\appsettings.json", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\loan-management-service\\BlueTape.Services.LMS\\BlueTape.Services.LMS.API\\appsettings.json", "RelativeToolTip": "BlueTape.Services.LMS.API\\appsettings.json", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.001642|", "WhenOpened": "2025-07-02T11:37:00.494Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 15, "Title": "СreditStatusDetectorController.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\loan-management-service\\BlueTape.Services.LMS\\BlueTape.Services.LMS.API\\Controllers\\СreditStatusDetectorController.cs", "RelativeDocumentMoniker": "BlueTape.Services.LMS.API\\Controllers\\СreditStatusDetectorController.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\loan-management-service\\BlueTape.Services.LMS\\BlueTape.Services.LMS.API\\Controllers\\СreditStatusDetectorController.cs", "RelativeToolTip": "BlueTape.Services.LMS.API\\Controllers\\СreditStatusDetectorController.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAoAAAAhAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-01T13:40:52.393Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 14, "Title": "main.tf", "DocumentMoniker": "C:\\Users\\<USER>\\Source\\Repos\\BlueTape\\loan-management-service\\infra\\modules\\mod-fapp\\main.tf", "RelativeDocumentMoniker": "..\\infra\\modules\\mod-fapp\\main.tf", "ToolTip": "C:\\Users\\<USER>\\Source\\Repos\\BlueTape\\loan-management-service\\infra\\modules\\mod-fapp\\main.tf", "RelativeToolTip": "..\\infra\\modules\\mod-fapp\\main.tf", "ViewState": "AgIAAA8AAAAAAAAAAAAAACEAAAAvAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.001001|", "WhenOpened": "2025-07-02T11:16:02.925Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 20, "Title": "BlueTape.Functions.LMS.CreditStatusDetector.csproj", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\loan-management-service\\BlueTape.Services.LMS\\Functions\\BlueTape.Functions.LMS.CreditStatusDetector\\BlueTape.Functions.LMS.CreditStatusDetector.csproj", "RelativeDocumentMoniker": "Functions\\BlueTape.Functions.LMS.CreditStatusDetector\\BlueTape.Functions.LMS.CreditStatusDetector.csproj", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\loan-management-service\\BlueTape.Services.LMS\\Functions\\BlueTape.Functions.LMS.CreditStatusDetector\\BlueTape.Functions.LMS.CreditStatusDetector.csproj", "RelativeToolTip": "Functions\\BlueTape.Functions.LMS.CreditStatusDetector\\BlueTape.Functions.LMS.CreditStatusDetector.csproj", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000758|", "WhenOpened": "2025-07-01T13:40:03.416Z"}, {"$type": "Document", "DocumentIndex": 18, "Title": "CreditStatusConsumer.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\loan-management-service\\BlueTape.Services.LMS\\Functions\\BlueTape.Functions.LMS.CreditStatusDetector\\CreditStatusConsumer.cs", "RelativeDocumentMoniker": "Functions\\BlueTape.Functions.LMS.CreditStatusDetector\\CreditStatusConsumer.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\loan-management-service\\BlueTape.Services.LMS\\Functions\\BlueTape.Functions.LMS.CreditStatusDetector\\CreditStatusConsumer.cs", "RelativeToolTip": "Functions\\BlueTape.Functions.LMS.CreditStatusDetector\\CreditStatusConsumer.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAABUAAAAnAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-01T13:40:06.704Z"}, {"$type": "Document", "DocumentIndex": 16, "Title": "CreditStatusDetector.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\loan-management-service\\BlueTape.Services.LMS\\Functions\\BlueTape.Functions.LMS.CreditStatusDetector\\CreditStatusDetector.cs", "RelativeDocumentMoniker": "Functions\\BlueTape.Functions.LMS.CreditStatusDetector\\CreditStatusDetector.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\loan-management-service\\BlueTape.Services.LMS\\Functions\\BlueTape.Functions.LMS.CreditStatusDetector\\CreditStatusDetector.cs", "RelativeToolTip": "Functions\\BlueTape.Functions.LMS.CreditStatusDetector\\CreditStatusDetector.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAABEAAAA2AAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-01T13:40:14.795Z"}, {"$type": "Document", "DocumentIndex": 19, "Title": "CreditStatusDetectorService.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\loan-management-service\\BlueTape.Services.LMS\\BlueTape.Services.LMS.Application\\Services\\CreditServices\\CreditStatusDetectorService.cs", "RelativeDocumentMoniker": "BlueTape.Services.LMS.Application\\Services\\CreditServices\\CreditStatusDetectorService.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\loan-management-service\\BlueTape.Services.LMS\\BlueTape.Services.LMS.Application\\Services\\CreditServices\\CreditStatusDetectorService.cs", "RelativeToolTip": "BlueTape.Services.LMS.Application\\Services\\CreditServices\\CreditStatusDetectorService.cs", "ViewState": "AgIAABoAAAAAAAAAAAAcwCgAAAAWAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-01T13:40:20.577Z"}, {"$type": "Document", "DocumentIndex": 17, "Title": "CreditService.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\loan-management-service\\BlueTape.Services.LMS\\BlueTape.Services.LMS.Application\\Services\\CreditServices\\CreditService.cs", "RelativeDocumentMoniker": "BlueTape.Services.LMS.Application\\Services\\CreditServices\\CreditService.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\loan-management-service\\BlueTape.Services.LMS\\BlueTape.Services.LMS.Application\\Services\\CreditServices\\CreditService.cs", "RelativeToolTip": "BlueTape.Services.LMS.Application\\Services\\CreditServices\\CreditService.cs", "ViewState": "AgIAALsAAAAAAAAAAAAcwMMAAAAiAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-01T13:40:29.666Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 21, "Title": "ArsIntegrationTestsBase.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\loan-management-service\\BlueTape.Services.LMS\\BlueTape.Services.ARS.IntegrationTests\\Tests\\Base\\ArsIntegrationTestsBase.cs", "RelativeDocumentMoniker": "BlueTape.Services.ARS.IntegrationTests\\Tests\\Base\\ArsIntegrationTestsBase.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\loan-management-service\\BlueTape.Services.LMS\\BlueTape.Services.ARS.IntegrationTests\\Tests\\Base\\ArsIntegrationTestsBase.cs", "RelativeToolTip": "BlueTape.Services.ARS.IntegrationTests\\Tests\\Base\\ArsIntegrationTestsBase.cs", "ViewState": "AgIAAD0AAAAAAAAAAAAgwAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-01T12:17:31.613Z"}]}]}]}