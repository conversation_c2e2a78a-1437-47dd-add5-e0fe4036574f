{"Version": 1, "WorkspaceRootPath": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\loan-management-service\\BlueTape.Services.LMS\\", "Documents": [{"AbsoluteMoniker": "D:0:0:{0533DD8A-67A3-4B67-B55B-706736B3C199}|BlueTape.Services.LMS.Application\\BlueTape.Services.LMS.Application.csproj|c:\\users\\<USER>\\source\\repos\\bluetape\\loan-management-service\\bluetape.services.lms\\bluetape.services.lms.application\\services\\paymentexternalservice.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{0533DD8A-67A3-4B67-B55B-706736B3C199}|BlueTape.Services.LMS.Application\\BlueTape.Services.LMS.Application.csproj|solutionrelative:bluetape.services.lms.application\\services\\paymentexternalservice.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{0533DD8A-67A3-4B67-B55B-706736B3C199}|BlueTape.Services.LMS.Application\\BlueTape.Services.LMS.Application.csproj|c:\\users\\<USER>\\source\\repos\\bluetape\\loan-management-service\\bluetape.services.lms\\bluetape.services.lms.application\\services\\paymenteligibilitychecker.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{0533DD8A-67A3-4B67-B55B-706736B3C199}|BlueTape.Services.LMS.Application\\BlueTape.Services.LMS.Application.csproj|solutionrelative:bluetape.services.lms.application\\services\\paymenteligibilitychecker.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{0533DD8A-67A3-4B67-B55B-706736B3C199}|BlueTape.Services.LMS.Application\\BlueTape.Services.LMS.Application.csproj|c:\\users\\<USER>\\source\\repos\\bluetape\\loan-management-service\\bluetape.services.lms\\bluetape.services.lms.application\\services\\notificationservice.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{0533DD8A-67A3-4B67-B55B-706736B3C199}|BlueTape.Services.LMS.Application\\BlueTape.Services.LMS.Application.csproj|solutionrelative:bluetape.services.lms.application\\services\\notificationservice.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{39A0C662-A29D-4B35-836C-1A37D73BF18E}|BlueTape.Services.LMS.AutoPay\\BlueTape.Services.LMS.AutoPay.csproj|c:\\users\\<USER>\\source\\repos\\bluetape\\loan-management-service\\bluetape.services.lms\\bluetape.services.lms.autopay\\infrastructure\\processduestrategy\\processduelocstrategy.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{39A0C662-A29D-4B35-836C-1A37D73BF18E}|BlueTape.Services.LMS.AutoPay\\BlueTape.Services.LMS.AutoPay.csproj|solutionrelative:bluetape.services.lms.autopay\\infrastructure\\processduestrategy\\processduelocstrategy.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{39A0C662-A29D-4B35-836C-1A37D73BF18E}|BlueTape.Services.LMS.AutoPay\\BlueTape.Services.LMS.AutoPay.csproj|c:\\users\\<USER>\\source\\repos\\bluetape\\loan-management-service\\bluetape.services.lms\\bluetape.services.lms.autopay\\infrastructure\\processduestrategy\\processdueihcstrategy.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{39A0C662-A29D-4B35-836C-1A37D73BF18E}|BlueTape.Services.LMS.AutoPay\\BlueTape.Services.LMS.AutoPay.csproj|solutionrelative:bluetape.services.lms.autopay\\infrastructure\\processduestrategy\\processdueihcstrategy.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{39A0C662-A29D-4B35-836C-1A37D73BF18E}|BlueTape.Services.LMS.AutoPay\\BlueTape.Services.LMS.AutoPay.csproj|c:\\users\\<USER>\\source\\repos\\bluetape\\loan-management-service\\bluetape.services.lms\\bluetape.services.lms.autopay\\services\\dueloanmessagesgenerator.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{39A0C662-A29D-4B35-836C-1A37D73BF18E}|BlueTape.Services.LMS.AutoPay\\BlueTape.Services.LMS.AutoPay.csproj|solutionrelative:bluetape.services.lms.autopay\\services\\dueloanmessagesgenerator.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{39A0C662-A29D-4B35-836C-1A37D73BF18E}|BlueTape.Services.LMS.AutoPay\\BlueTape.Services.LMS.AutoPay.csproj|c:\\users\\<USER>\\source\\repos\\bluetape\\loan-management-service\\bluetape.services.lms\\bluetape.services.lms.autopay\\services\\autopayloanservice.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{39A0C662-A29D-4B35-836C-1A37D73BF18E}|BlueTape.Services.LMS.AutoPay\\BlueTape.Services.LMS.AutoPay.csproj|solutionrelative:bluetape.services.lms.autopay\\services\\autopayloanservice.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{39A0C662-A29D-4B35-836C-1A37D73BF18E}|BlueTape.Services.LMS.AutoPay\\BlueTape.Services.LMS.AutoPay.csproj|c:\\users\\<USER>\\source\\repos\\bluetape\\loan-management-service\\bluetape.services.lms\\bluetape.services.lms.autopay\\infrastructure\\getduestrategy\\getduebasestrategy.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{39A0C662-A29D-4B35-836C-1A37D73BF18E}|BlueTape.Services.LMS.AutoPay\\BlueTape.Services.LMS.AutoPay.csproj|solutionrelative:bluetape.services.lms.autopay\\infrastructure\\getduestrategy\\getduebasestrategy.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{39A0C662-A29D-4B35-836C-1A37D73BF18E}|BlueTape.Services.LMS.AutoPay\\BlueTape.Services.LMS.AutoPay.csproj|c:\\users\\<USER>\\source\\repos\\bluetape\\loan-management-service\\bluetape.services.lms\\bluetape.services.lms.autopay\\infrastructure\\getduestrategy\\getduelocstrategy.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{39A0C662-A29D-4B35-836C-1A37D73BF18E}|BlueTape.Services.LMS.AutoPay\\BlueTape.Services.LMS.AutoPay.csproj|solutionrelative:bluetape.services.lms.autopay\\infrastructure\\getduestrategy\\getduelocstrategy.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{39A0C662-A29D-4B35-836C-1A37D73BF18E}|BlueTape.Services.LMS.AutoPay\\BlueTape.Services.LMS.AutoPay.csproj|c:\\users\\<USER>\\source\\repos\\bluetape\\loan-management-service\\bluetape.services.lms\\bluetape.services.lms.autopay\\infrastructure\\getduestrategy\\getdueihcstrategy.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{39A0C662-A29D-4B35-836C-1A37D73BF18E}|BlueTape.Services.LMS.AutoPay\\BlueTape.Services.LMS.AutoPay.csproj|solutionrelative:bluetape.services.lms.autopay\\infrastructure\\getduestrategy\\getdueihcstrategy.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{47EA21B1-3C61-4458-9AAB-FC66B8EB8AE6}|BlueTape.Services.LMS.API\\BlueTape.Services.LMS.API.csproj|c:\\users\\<USER>\\source\\repos\\bluetape\\loan-management-service\\bluetape.services.lms\\bluetape.services.lms.api\\controllers\\autopayloancontroller.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{47EA21B1-3C61-4458-9AAB-FC66B8EB8AE6}|BlueTape.Services.LMS.API\\BlueTape.Services.LMS.API.csproj|solutionrelative:bluetape.services.lms.api\\controllers\\autopayloancontroller.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{39A0C662-A29D-4B35-836C-1A37D73BF18E}|BlueTape.Services.LMS.AutoPay\\BlueTape.Services.LMS.AutoPay.csproj|c:\\users\\<USER>\\source\\repos\\bluetape\\loan-management-service\\bluetape.services.lms\\bluetape.services.lms.autopay\\infrastructure\\processduestrategy\\processduebasestrategy.cs||{8B382828-6202-11D1-8870-0000F87579D2}", "RelativeMoniker": "D:0:0:{39A0C662-A29D-4B35-836C-1A37D73BF18E}|BlueTape.Services.LMS.AutoPay\\BlueTape.Services.LMS.AutoPay.csproj|solutionrelative:bluetape.services.lms.autopay\\infrastructure\\processduestrategy\\processduebasestrategy.cs||{8B382828-6202-11D1-8870-0000F87579D2}"}, {"AbsoluteMoniker": "D:0:0:{47EA21B1-3C61-4458-9AAB-FC66B8EB8AE6}|BlueTape.Services.LMS.API\\BlueTape.Services.LMS.API.csproj|c:\\users\\<USER>\\source\\repos\\bluetape\\loan-management-service\\bluetape.services.lms\\bluetape.services.lms.api\\mappers\\viewmodelsprofile.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{47EA21B1-3C61-4458-9AAB-FC66B8EB8AE6}|BlueTape.Services.LMS.API\\BlueTape.Services.LMS.API.csproj|solutionrelative:bluetape.services.lms.api\\mappers\\viewmodelsprofile.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{47EA21B1-3C61-4458-9AAB-FC66B8EB8AE6}|BlueTape.Services.LMS.API\\BlueTape.Services.LMS.API.csproj|c:\\users\\<USER>\\source\\repos\\bluetape\\loan-management-service\\bluetape.services.lms\\bluetape.services.lms.api\\controllers\\loancontroller.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{47EA21B1-3C61-4458-9AAB-FC66B8EB8AE6}|BlueTape.Services.LMS.API\\BlueTape.Services.LMS.API.csproj|solutionrelative:bluetape.services.lms.api\\controllers\\loancontroller.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{0533DD8A-67A3-4B67-B55B-706736B3C199}|BlueTape.Services.LMS.Application\\BlueTape.Services.LMS.Application.csproj|c:\\users\\<USER>\\source\\repos\\bluetape\\loan-management-service\\bluetape.services.lms\\bluetape.services.lms.application\\services\\loanservices\\createloanservice.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{0533DD8A-67A3-4B67-B55B-706736B3C199}|BlueTape.Services.LMS.Application\\BlueTape.Services.LMS.Application.csproj|solutionrelative:bluetape.services.lms.application\\services\\loanservices\\createloanservice.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{0533DD8A-67A3-4B67-B55B-706736B3C199}|BlueTape.Services.LMS.Application\\BlueTape.Services.LMS.Application.csproj|c:\\users\\<USER>\\source\\repos\\bluetape\\loan-management-service\\bluetape.services.lms\\bluetape.services.lms.application\\services\\loanservices\\loanservice.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{0533DD8A-67A3-4B67-B55B-706736B3C199}|BlueTape.Services.LMS.Application\\BlueTape.Services.LMS.Application.csproj|solutionrelative:bluetape.services.lms.application\\services\\loanservices\\loanservice.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{0533DD8A-67A3-4B67-B55B-706736B3C199}|BlueTape.Services.LMS.Application\\BlueTape.Services.LMS.Application.csproj|c:\\users\\<USER>\\source\\repos\\bluetape\\loan-management-service\\bluetape.services.lms\\bluetape.services.lms.application\\services\\loanservices\\loandownpaymentsservice.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{0533DD8A-67A3-4B67-B55B-706736B3C199}|BlueTape.Services.LMS.Application\\BlueTape.Services.LMS.Application.csproj|solutionrelative:bluetape.services.lms.application\\services\\loanservices\\loandownpaymentsservice.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{C97825F1-C80F-4483-A156-E9A0EB418F50}|BlueTape.Services.LMS.Application.Tests\\BlueTape.Services.LMS.Application.Tests.csproj|c:\\users\\<USER>\\source\\repos\\bluetape\\loan-management-service\\bluetape.services.lms\\bluetape.services.lms.application.tests\\services\\paymentexternalservicetests.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{C97825F1-C80F-4483-A156-E9A0EB418F50}|BlueTape.Services.LMS.Application.Tests\\BlueTape.Services.LMS.Application.Tests.csproj|solutionrelative:bluetape.services.lms.application.tests\\services\\paymentexternalservicetests.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{47EA21B1-3C61-4458-9AAB-FC66B8EB8AE6}|BlueTape.Services.LMS.API\\BlueTape.Services.LMS.API.csproj|c:\\users\\<USER>\\source\\repos\\bluetape\\loan-management-service\\bluetape.services.lms\\bluetape.services.lms.api\\controllers\\qacontroller.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{47EA21B1-3C61-4458-9AAB-FC66B8EB8AE6}|BlueTape.Services.LMS.API\\BlueTape.Services.LMS.API.csproj|solutionrelative:bluetape.services.lms.api\\controllers\\qacontroller.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{47EA21B1-3C61-4458-9AAB-FC66B8EB8AE6}|BlueTape.Services.LMS.API\\BlueTape.Services.LMS.API.csproj|c:\\users\\<USER>\\source\\repos\\bluetape\\loan-management-service\\bluetape.services.lms\\bluetape.services.lms.api\\program.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{47EA21B1-3C61-4458-9AAB-FC66B8EB8AE6}|BlueTape.Services.LMS.API\\BlueTape.Services.LMS.API.csproj|solutionrelative:bluetape.services.lms.api\\program.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{A2FE74E1-B743-11D0-AE1A-00A0C90FFFC3}|<MiscFiles>|C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\MSBuild\\Current\\Bin\\amd64\\Microsoft.Common.CurrentVersion.targets||{FA3CD31E-987B-443A-9B81-186104E8DAC1}|"}, {"AbsoluteMoniker": "D:0:0:{39A0C662-A29D-4B35-836C-1A37D73BF18E}|BlueTape.Services.LMS.AutoPay\\BlueTape.Services.LMS.AutoPay.csproj|c:\\users\\<USER>\\source\\repos\\bluetape\\loan-management-service\\bluetape.services.lms\\bluetape.services.lms.autopay\\infrastructure\\processduestrategy\\abstractions\\iprocessduestrategy.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{39A0C662-A29D-4B35-836C-1A37D73BF18E}|BlueTape.Services.LMS.AutoPay\\BlueTape.Services.LMS.AutoPay.csproj|solutionrelative:bluetape.services.lms.autopay\\infrastructure\\processduestrategy\\abstractions\\iprocessduestrategy.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{47EA21B1-3C61-4458-9AAB-FC66B8EB8AE6}|BlueTape.Services.LMS.API\\BlueTape.Services.LMS.API.csproj|c:\\users\\<USER>\\source\\repos\\bluetape\\loan-management-service\\bluetape.services.lms\\bluetape.services.lms.api\\controllers\\basispointcontroller.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{47EA21B1-3C61-4458-9AAB-FC66B8EB8AE6}|BlueTape.Services.LMS.API\\BlueTape.Services.LMS.API.csproj|solutionrelative:bluetape.services.lms.api\\controllers\\basispointcontroller.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{550EBAC7-27EC-4AF9-A7A7-4ABC52FA40AA}|Functions\\AutoPay\\BlueTape.Functions.LMS.ProcessDue\\BlueTape.Functions.LMS.ProcessDue.csproj|c:\\users\\<USER>\\source\\repos\\bluetape\\loan-management-service\\bluetape.services.lms\\functions\\autopay\\bluetape.functions.lms.processdue\\processdueconsumer.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{550EBAC7-27EC-4AF9-A7A7-4ABC52FA40AA}|Functions\\AutoPay\\BlueTape.Functions.LMS.ProcessDue\\BlueTape.Functions.LMS.ProcessDue.csproj|solutionrelative:functions\\autopay\\bluetape.functions.lms.processdue\\processdueconsumer.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{09DA94D8-216F-46E1-AEF5-B9416C1B14C1}|Functions\\AutoPay\\BlueTape.Functions.LMS.GetDue\\BlueTape.Functions.LMS.GetDue.csproj|c:\\users\\<USER>\\source\\repos\\bluetape\\loan-management-service\\bluetape.services.lms\\functions\\autopay\\bluetape.functions.lms.getdue\\getdueihcloansdetectorfunction.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{09DA94D8-216F-46E1-AEF5-B9416C1B14C1}|Functions\\AutoPay\\BlueTape.Functions.LMS.GetDue\\BlueTape.Functions.LMS.GetDue.csproj|solutionrelative:functions\\autopay\\bluetape.functions.lms.getdue\\getdueihcloansdetectorfunction.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{09DA94D8-216F-46E1-AEF5-B9416C1B14C1}|Functions\\AutoPay\\BlueTape.Functions.LMS.GetDue\\BlueTape.Functions.LMS.GetDue.csproj|c:\\users\\<USER>\\source\\repos\\bluetape\\loan-management-service\\bluetape.services.lms\\functions\\autopay\\bluetape.functions.lms.getdue\\getdueloansdetectorfunction.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{09DA94D8-216F-46E1-AEF5-B9416C1B14C1}|Functions\\AutoPay\\BlueTape.Functions.LMS.GetDue\\BlueTape.Functions.LMS.GetDue.csproj|solutionrelative:functions\\autopay\\bluetape.functions.lms.getdue\\getdueloansdetectorfunction.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{A2FE74E1-B743-11D0-AE1A-00A0C90FFFC3}|<MiscFiles>|C:\\Users\\<USER>\\source\\repos\\BlueTape\\loan-management-service\\scripts\\deploy-azure-functions-manual.ps1||{3B902123-F8A7-4915-9F01-361F908088D0}"}, {"AbsoluteMoniker": "D:0:0:{A2FE74E1-B743-11D0-AE1A-00A0C90FFFC3}|<MiscFiles>|C:\\Users\\<USER>\\source\\repos\\BlueTape\\loan-management-service\\scripts\\CI\\deploy-azure-functions.yml||{8B382828-6202-11D1-8870-0000F87579D2}"}, {"AbsoluteMoniker": "D:0:0:{39A0C662-A29D-4B35-836C-1A37D73BF18E}|BlueTape.Services.LMS.AutoPay\\BlueTape.Services.LMS.AutoPay.csproj|c:\\users\\<USER>\\source\\repos\\bluetape\\loan-management-service\\bluetape.services.lms\\bluetape.services.lms.autopay\\infrastructure\\getduestrategy\\abstractions\\igetduestrategy.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{39A0C662-A29D-4B35-836C-1A37D73BF18E}|BlueTape.Services.LMS.AutoPay\\BlueTape.Services.LMS.AutoPay.csproj|solutionrelative:bluetape.services.lms.autopay\\infrastructure\\getduestrategy\\abstractions\\igetduestrategy.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{69F67AEB-92E3-48CD-9889-144AADA1524E}|BlueTape.Services.LMS.AutoPay.Tests\\BlueTape.Services.LMS.AutoPay.Tests.csproj|c:\\users\\<USER>\\source\\repos\\bluetape\\loan-management-service\\bluetape.services.lms\\bluetape.services.lms.autopay.tests\\autopayloanservicetests.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{69F67AEB-92E3-48CD-9889-144AADA1524E}|BlueTape.Services.LMS.AutoPay.Tests\\BlueTape.Services.LMS.AutoPay.Tests.csproj|solutionrelative:bluetape.services.lms.autopay.tests\\autopayloanservicetests.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{09DA94D8-216F-46E1-AEF5-B9416C1B14C1}|Functions\\AutoPay\\BlueTape.Functions.LMS.GetDue\\BlueTape.Functions.LMS.GetDue.csproj|c:\\users\\<USER>\\source\\repos\\bluetape\\loan-management-service\\bluetape.services.lms\\functions\\autopay\\bluetape.functions.lms.getdue\\bluetape.functions.lms.getdue.csproj||{FA3CD31E-987B-443A-9B81-186104E8DAC1}|", "RelativeMoniker": "D:0:0:{09DA94D8-216F-46E1-AEF5-B9416C1B14C1}|Functions\\AutoPay\\BlueTape.Functions.LMS.GetDue\\BlueTape.Functions.LMS.GetDue.csproj|solutionrelative:functions\\autopay\\bluetape.functions.lms.getdue\\bluetape.functions.lms.getdue.csproj||{FA3CD31E-987B-443A-9B81-186104E8DAC1}|"}, {"AbsoluteMoniker": "D:0:0:{550EBAC7-27EC-4AF9-A7A7-4ABC52FA40AA}|Functions\\AutoPay\\BlueTape.Functions.LMS.ProcessDue\\BlueTape.Functions.LMS.ProcessDue.csproj|c:\\users\\<USER>\\source\\repos\\bluetape\\loan-management-service\\bluetape.services.lms\\functions\\autopay\\bluetape.functions.lms.processdue\\bluetape.functions.lms.processdue.csproj||{FA3CD31E-987B-443A-9B81-186104E8DAC1}|", "RelativeMoniker": "D:0:0:{550EBAC7-27EC-4AF9-A7A7-4ABC52FA40AA}|Functions\\AutoPay\\BlueTape.Functions.LMS.ProcessDue\\BlueTape.Functions.LMS.ProcessDue.csproj|solutionrelative:functions\\autopay\\bluetape.functions.lms.processdue\\bluetape.functions.lms.processdue.csproj||{FA3CD31E-987B-443A-9B81-186104E8DAC1}|"}, {"AbsoluteMoniker": "D:0:0:{C97825F1-C80F-4483-A156-E9A0EB418F50}|BlueTape.Services.LMS.Application.Tests\\BlueTape.Services.LMS.Application.Tests.csproj|c:\\users\\<USER>\\source\\repos\\bluetape\\loan-management-service\\bluetape.services.lms\\bluetape.services.lms.application.tests\\services\\loandownpaymentsservicetests.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{C97825F1-C80F-4483-A156-E9A0EB418F50}|BlueTape.Services.LMS.Application.Tests\\BlueTape.Services.LMS.Application.Tests.csproj|solutionrelative:bluetape.services.lms.application.tests\\services\\loandownpaymentsservicetests.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{5DA9CB35-7181-4B23-8DF7-7E351B973F9B}|BlueTape.Services.ARS.IntegrationTests\\BlueTape.Services.ARS.IntegrationTests.csproj|c:\\users\\<USER>\\source\\repos\\bluetape\\loan-management-service\\bluetape.services.lms\\bluetape.services.ars.integrationtests\\tests\\base\\arsintegrationtestsbase.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{5DA9CB35-7181-4B23-8DF7-7E351B973F9B}|BlueTape.Services.ARS.IntegrationTests\\BlueTape.Services.ARS.IntegrationTests.csproj|solutionrelative:bluetape.services.ars.integrationtests\\tests\\base\\arsintegrationtestsbase.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{5DA9CB35-7181-4B23-8DF7-7E351B973F9B}|BlueTape.Services.ARS.IntegrationTests\\BlueTape.Services.ARS.IntegrationTests.csproj|c:\\users\\<USER>\\source\\repos\\bluetape\\loan-management-service\\bluetape.services.lms\\bluetape.services.ars.integrationtests\\tests\\agingreportsintegrationtests.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{5DA9CB35-7181-4B23-8DF7-7E351B973F9B}|BlueTape.Services.ARS.IntegrationTests\\BlueTape.Services.ARS.IntegrationTests.csproj|solutionrelative:bluetape.services.ars.integrationtests\\tests\\agingreportsintegrationtests.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{0533DD8A-67A3-4B67-B55B-706736B3C199}|BlueTape.Services.LMS.Application\\BlueTape.Services.LMS.Application.csproj|c:\\users\\<USER>\\source\\repos\\bluetape\\loan-management-service\\bluetape.services.lms\\bluetape.services.lms.application\\abstractions\\services\\inotificationservice.cs||{8B382828-6202-11D1-8870-0000F87579D2}", "RelativeMoniker": "D:0:0:{0533DD8A-67A3-4B67-B55B-706736B3C199}|BlueTape.Services.LMS.Application\\BlueTape.Services.LMS.Application.csproj|solutionrelative:bluetape.services.lms.application\\abstractions\\services\\inotificationservice.cs||{8B382828-6202-11D1-8870-0000F87579D2}"}, {"AbsoluteMoniker": "D:0:0:{0533DD8A-67A3-4B67-B55B-706736B3C199}|BlueTape.Services.LMS.Application\\BlueTape.Services.LMS.Application.csproj|c:\\users\\<USER>\\source\\repos\\bluetape\\loan-management-service\\bluetape.services.lms\\bluetape.services.lms.application\\di\\dependencyregistrar.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{0533DD8A-67A3-4B67-B55B-706736B3C199}|BlueTape.Services.LMS.Application\\BlueTape.Services.LMS.Application.csproj|solutionrelative:bluetape.services.lms.application\\di\\dependencyregistrar.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{0533DD8A-67A3-4B67-B55B-706736B3C199}|BlueTape.Services.LMS.Application\\BlueTape.Services.LMS.Application.csproj|c:\\users\\<USER>\\source\\repos\\bluetape\\loan-management-service\\bluetape.services.lms\\bluetape.services.lms.application\\services\\receivablerecalculatorservice.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{0533DD8A-67A3-4B67-B55B-706736B3C199}|BlueTape.Services.LMS.Application\\BlueTape.Services.LMS.Application.csproj|solutionrelative:bluetape.services.lms.application\\services\\receivablerecalculatorservice.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{A2FE74E1-B743-11D0-AE1A-00A0C90FFFC3}|<MiscFiles>|C:\\Users\\<USER>\\AppData\\Local\\JetBrains\\Shared\\vAny\\DecompilerCache\\decompiler\\2f9970a267af4abe839835f8444f06775600\\ec\\1f80e6b1\\LinqpalHttpClient.cs||{8B382828-6202-11D1-8870-0000F87579D2}"}, {"AbsoluteMoniker": "D:0:0:{47EA21B1-3C61-4458-9AAB-FC66B8EB8AE6}|BlueTape.Services.LMS.API\\BlueTape.Services.LMS.API.csproj|c:\\users\\<USER>\\source\\repos\\bluetape\\loan-management-service\\bluetape.services.lms\\bluetape.services.lms.api\\controllers\\authorizationperiodcontroller.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{47EA21B1-3C61-4458-9AAB-FC66B8EB8AE6}|BlueTape.Services.LMS.API\\BlueTape.Services.LMS.API.csproj|solutionrelative:bluetape.services.lms.api\\controllers\\authorizationperiodcontroller.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{47EA21B1-3C61-4458-9AAB-FC66B8EB8AE6}|BlueTape.Services.LMS.API\\BlueTape.Services.LMS.API.csproj|c:\\users\\<USER>\\source\\repos\\bluetape\\loan-management-service\\bluetape.services.lms\\bluetape.services.lms.api\\controllers\\creditholdscontroller.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{47EA21B1-3C61-4458-9AAB-FC66B8EB8AE6}|BlueTape.Services.LMS.API\\BlueTape.Services.LMS.API.csproj|solutionrelative:bluetape.services.lms.api\\controllers\\creditholdscontroller.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{47EA21B1-3C61-4458-9AAB-FC66B8EB8AE6}|BlueTape.Services.LMS.API\\BlueTape.Services.LMS.API.csproj|c:\\users\\<USER>\\source\\repos\\bluetape\\loan-management-service\\bluetape.services.lms\\bluetape.services.lms.api\\controllers\\agingreportscontroller.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{47EA21B1-3C61-4458-9AAB-FC66B8EB8AE6}|BlueTape.Services.LMS.API\\BlueTape.Services.LMS.API.csproj|solutionrelative:bluetape.services.lms.api\\controllers\\agingreportscontroller.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}], "DocumentGroupContainers": [{"Orientation": 0, "VerticalTabListWidth": 256, "DocumentGroups": [{"DockedWidth": 200, "SelectedChildIndex": 5, "Children": [{"$type": "Document", "DocumentIndex": 8, "Title": "GetDueLOCStrategy.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\loan-management-service\\BlueTape.Services.LMS\\BlueTape.Services.LMS.AutoPay\\Infrastructure\\GetDueStrategy\\GetDueLOCStrategy.cs", "RelativeDocumentMoniker": "BlueTape.Services.LMS.AutoPay\\Infrastructure\\GetDueStrategy\\GetDueLOCStrategy.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\loan-management-service\\BlueTape.Services.LMS\\BlueTape.Services.LMS.AutoPay\\Infrastructure\\GetDueStrategy\\GetDueLOCStrategy.cs", "RelativeToolTip": "BlueTape.Services.LMS.AutoPay\\Infrastructure\\GetDueStrategy\\GetDueLOCStrategy.cs", "ViewState": "AgIAAA8AAAAAAAAAAAAowB0AAAA/AAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-24T15:55:41.199Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 6, "Title": "AutoPayLoanService.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\loan-management-service\\BlueTape.Services.LMS\\BlueTape.Services.LMS.AutoPay\\Services\\AutoPayLoanService.cs", "RelativeDocumentMoniker": "BlueTape.Services.LMS.AutoPay\\Services\\AutoPayLoanService.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\loan-management-service\\BlueTape.Services.LMS\\BlueTape.Services.LMS.AutoPay\\Services\\AutoPayLoanService.cs", "RelativeToolTip": "BlueTape.Services.LMS.AutoPay\\Services\\AutoPayLoanService.cs", "ViewState": "AgIAABwAAAAAAAAAAAAcwC4AAAAMAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-24T14:34:44.81Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 13, "Title": "LoanController.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\loan-management-service\\BlueTape.Services.LMS\\BlueTape.Services.LMS.API\\Controllers\\LoanController.cs", "RelativeDocumentMoniker": "BlueTape.Services.LMS.API\\Controllers\\LoanController.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\loan-management-service\\BlueTape.Services.LMS\\BlueTape.Services.LMS.API\\Controllers\\LoanController.cs", "RelativeToolTip": "BlueTape.Services.LMS.API\\Controllers\\LoanController.cs", "ViewState": "AgIAAMcAAAAAAAAAAAAwwOAAAAAsAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-24T11:42:58.19Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 2, "Title": "NotificationService.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\loan-management-service\\BlueTape.Services.LMS\\BlueTape.Services.LMS.Application\\Services\\NotificationService.cs", "RelativeDocumentMoniker": "BlueTape.Services.LMS.Application\\Services\\NotificationService.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\loan-management-service\\BlueTape.Services.LMS\\BlueTape.Services.LMS.Application\\Services\\NotificationService.cs", "RelativeToolTip": "BlueTape.Services.LMS.Application\\Services\\NotificationService.cs", "ViewState": "AgIAAH0AAAAAAAAAAAAiwGkAAAAJAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-23T09:08:10.456Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 1, "Title": "PaymentEligibilityChecker.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\loan-management-service\\BlueTape.Services.LMS\\BlueTape.Services.LMS.Application\\Services\\PaymentEligibilityChecker.cs", "RelativeDocumentMoniker": "BlueTape.Services.LMS.Application\\Services\\PaymentEligibilityChecker.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\loan-management-service\\BlueTape.Services.LMS\\BlueTape.Services.LMS.Application\\Services\\PaymentEligibilityChecker.cs", "RelativeToolTip": "BlueTape.Services.LMS.Application\\Services\\PaymentEligibilityChecker.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-23T09:08:03.944Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 0, "Title": "PaymentExternalService.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\loan-management-service\\BlueTape.Services.LMS\\BlueTape.Services.LMS.Application\\Services\\PaymentExternalService.cs", "RelativeDocumentMoniker": "BlueTape.Services.LMS.Application\\Services\\PaymentExternalService.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\loan-management-service\\BlueTape.Services.LMS\\BlueTape.Services.LMS.Application\\Services\\PaymentExternalService.cs", "RelativeToolTip": "BlueTape.Services.LMS.Application\\Services\\PaymentExternalService.cs", "ViewState": "AgIAAHEAAAAAAAAAAAAnwH4AAAAxAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-23T09:08:04.84Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 5, "Title": "DueLoanMessagesGenerator.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\loan-management-service\\BlueTape.Services.LMS\\BlueTape.Services.LMS.AutoPay\\Services\\DueLoanMessagesGenerator.cs", "RelativeDocumentMoniker": "BlueTape.Services.LMS.AutoPay\\Services\\DueLoanMessagesGenerator.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\loan-management-service\\BlueTape.Services.LMS\\BlueTape.Services.LMS.AutoPay\\Services\\DueLoanMessagesGenerator.cs", "RelativeToolTip": "BlueTape.Services.LMS.AutoPay\\Services\\DueLoanMessagesGenerator.cs", "ViewState": "AgIAAA8AAAAAAAAAAAAIwCUAAAAmAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-25T11:49:48.831Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 9, "Title": "GetDueIHCStrategy.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\loan-management-service\\BlueTape.Services.LMS\\BlueTape.Services.LMS.AutoPay\\Infrastructure\\GetDueStrategy\\GetDueIHCStrategy.cs", "RelativeDocumentMoniker": "BlueTape.Services.LMS.AutoPay\\Infrastructure\\GetDueStrategy\\GetDueIHCStrategy.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\loan-management-service\\BlueTape.Services.LMS\\BlueTape.Services.LMS.AutoPay\\Infrastructure\\GetDueStrategy\\GetDueIHCStrategy.cs", "RelativeToolTip": "BlueTape.Services.LMS.AutoPay\\Infrastructure\\GetDueStrategy\\GetDueIHCStrategy.cs", "ViewState": "AgIAABMAAAAAAAAAAAAwwB4AAAATAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-24T16:06:20.564Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 4, "Title": "ProcessDueIHCStrategy.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\loan-management-service\\BlueTape.Services.LMS\\BlueTape.Services.LMS.AutoPay\\Infrastructure\\ProcessDueStrategy\\ProcessDueIHCStrategy.cs", "RelativeDocumentMoniker": "BlueTape.Services.LMS.AutoPay\\Infrastructure\\ProcessDueStrategy\\ProcessDueIHCStrategy.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\loan-management-service\\BlueTape.Services.LMS\\BlueTape.Services.LMS.AutoPay\\Infrastructure\\ProcessDueStrategy\\ProcessDueIHCStrategy.cs", "RelativeToolTip": "BlueTape.Services.LMS.AutoPay\\Infrastructure\\ProcessDueStrategy\\ProcessDueIHCStrategy.cs", "ViewState": "AgIAABgAAAAAAAAAAAAMwCgAAAAjAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-23T11:43:18.919Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 3, "Title": "ProcessDueLOCStrategy.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\loan-management-service\\BlueTape.Services.LMS\\BlueTape.Services.LMS.AutoPay\\Infrastructure\\ProcessDueStrategy\\ProcessDueLOCStrategy.cs", "RelativeDocumentMoniker": "BlueTape.Services.LMS.AutoPay\\Infrastructure\\ProcessDueStrategy\\ProcessDueLOCStrategy.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\loan-management-service\\BlueTape.Services.LMS\\BlueTape.Services.LMS.AutoPay\\Infrastructure\\ProcessDueStrategy\\ProcessDueLOCStrategy.cs", "RelativeToolTip": "BlueTape.Services.LMS.AutoPay\\Infrastructure\\ProcessDueStrategy\\ProcessDueLOCStrategy.cs", "ViewState": "AgIAABQAAAAAAAAAAAAewCoAAAABAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-23T11:43:15.899Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 7, "Title": "GetDueBaseStrategy.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\loan-management-service\\BlueTape.Services.LMS\\BlueTape.Services.LMS.AutoPay\\Infrastructure\\GetDueStrategy\\GetDueBaseStrategy.cs", "RelativeDocumentMoniker": "BlueTape.Services.LMS.AutoPay\\Infrastructure\\GetDueStrategy\\GetDueBaseStrategy.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\loan-management-service\\BlueTape.Services.LMS\\BlueTape.Services.LMS.AutoPay\\Infrastructure\\GetDueStrategy\\GetDueBaseStrategy.cs", "RelativeToolTip": "BlueTape.Services.LMS.AutoPay\\Infrastructure\\GetDueStrategy\\GetDueBaseStrategy.cs", "ViewState": "AgIAABgAAAAAAAAAAAAwwCYAAAAxAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-24T14:37:53.448Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 15, "Title": "LoanService.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\loan-management-service\\BlueTape.Services.LMS\\BlueTape.Services.LMS.Application\\Services\\LoanServices\\LoanService.cs", "RelativeDocumentMoniker": "BlueTape.Services.LMS.Application\\Services\\LoanServices\\LoanService.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\loan-management-service\\BlueTape.Services.LMS\\BlueTape.Services.LMS.Application\\Services\\LoanServices\\LoanService.cs", "RelativeToolTip": "BlueTape.Services.LMS.Application\\Services\\LoanServices\\LoanService.cs", "ViewState": "AgIAAHUAAAAAAAAAAAAowJUAAAAvAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-26T12:27:22.127Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 10, "Title": "AutopayLoanController.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\loan-management-service\\BlueTape.Services.LMS\\BlueTape.Services.LMS.API\\Controllers\\AutopayLoanController.cs", "RelativeDocumentMoniker": "BlueTape.Services.LMS.API\\Controllers\\AutopayLoanController.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\loan-management-service\\BlueTape.Services.LMS\\BlueTape.Services.LMS.API\\Controllers\\AutopayLoanController.cs", "RelativeToolTip": "BlueTape.Services.LMS.API\\Controllers\\AutopayLoanController.cs", "ViewState": "AgIAACAAAAAAAAAAAADwvzsAAABZAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-23T10:03:10.088Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 16, "Title": "LoanDownPaymentsService.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\loan-management-service\\BlueTape.Services.LMS\\BlueTape.Services.LMS.Application\\Services\\LoanServices\\LoanDownPaymentsService.cs", "RelativeDocumentMoniker": "BlueTape.Services.LMS.Application\\Services\\LoanServices\\LoanDownPaymentsService.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\loan-management-service\\BlueTape.Services.LMS\\BlueTape.Services.LMS.Application\\Services\\LoanServices\\LoanDownPaymentsService.cs", "RelativeToolTip": "BlueTape.Services.LMS.Application\\Services\\LoanServices\\LoanDownPaymentsService.cs", "ViewState": "AgIAAJQAAAAAAAAAAAAqwMMAAABVAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-24T14:22:47.352Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 12, "Title": "ViewModelsProfile.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\loan-management-service\\BlueTape.Services.LMS\\BlueTape.Services.LMS.API\\Mappers\\ViewModelsProfile.cs", "RelativeDocumentMoniker": "BlueTape.Services.LMS.API\\Mappers\\ViewModelsProfile.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\loan-management-service\\BlueTape.Services.LMS\\BlueTape.Services.LMS.API\\Mappers\\ViewModelsProfile.cs", "RelativeToolTip": "BlueTape.Services.LMS.API\\Mappers\\ViewModelsProfile.cs", "ViewState": "AgIAAJ4AAAAAAAAAAAAgwL0AAABeAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-26T14:12:57.395Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 11, "Title": "ProcessDueBaseStrategy.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\loan-management-service\\BlueTape.Services.LMS\\BlueTape.Services.LMS.AutoPay\\Infrastructure\\ProcessDueStrategy\\ProcessDueBaseStrategy.cs", "RelativeDocumentMoniker": "BlueTape.Services.LMS.AutoPay\\Infrastructure\\ProcessDueStrategy\\ProcessDueBaseStrategy.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\loan-management-service\\BlueTape.Services.LMS\\BlueTape.Services.LMS.AutoPay\\Infrastructure\\ProcessDueStrategy\\ProcessDueBaseStrategy.cs", "RelativeToolTip": "BlueTape.Services.LMS.AutoPay\\Infrastructure\\ProcessDueStrategy\\ProcessDueBaseStrategy.cs", "ViewState": "AgIAAEIAAAAAAAAAAAAMwE8AAAA/AAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-25T11:33:49.251Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 14, "Title": "CreateLoanService.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\loan-management-service\\BlueTape.Services.LMS\\BlueTape.Services.LMS.Application\\Services\\LoanServices\\CreateLoanService.cs", "RelativeDocumentMoniker": "BlueTape.Services.LMS.Application\\Services\\LoanServices\\CreateLoanService.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\loan-management-service\\BlueTape.Services.LMS\\BlueTape.Services.LMS.Application\\Services\\LoanServices\\CreateLoanService.cs", "RelativeToolTip": "BlueTape.Services.LMS.Application\\Services\\LoanServices\\CreateLoanService.cs", "ViewState": "AgIAAFkAAAAAAAAAAAAowHcAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-26T08:44:52.486Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 20, "Title": "Microsoft.Common.CurrentVersion.targets", "DocumentMoniker": "C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\MSBuild\\Current\\Bin\\amd64\\Microsoft.Common.CurrentVersion.targets", "RelativeDocumentMoniker": "..\\..\\..\\..\\..\\..\\..\\Program Files\\Microsoft Visual Studio\\2022\\Community\\MSBuild\\Current\\Bin\\amd64\\Microsoft.Common.CurrentVersion.targets", "ToolTip": "C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\MSBuild\\Current\\Bin\\amd64\\Microsoft.Common.CurrentVersion.targets", "RelativeToolTip": "..\\..\\..\\..\\..\\..\\..\\Program Files\\Microsoft Visual Studio\\2022\\Community\\MSBuild\\Current\\Bin\\amd64\\Microsoft.Common.CurrentVersion.targets", "ViewState": "AgIAAAAVAAAAAAAAAAAowBIVAAAEAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.003801|", "WhenOpened": "2025-06-26T08:30:32.393Z"}, {"$type": "Document", "DocumentIndex": 19, "Title": "Program.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\loan-management-service\\BlueTape.Services.LMS\\BlueTape.Services.LMS.API\\Program.cs", "RelativeDocumentMoniker": "BlueTape.Services.LMS.API\\Program.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\loan-management-service\\BlueTape.Services.LMS\\BlueTape.Services.LMS.API\\Program.cs", "RelativeToolTip": "BlueTape.Services.LMS.API\\Program.cs", "ViewState": "AgIAAFEAAAAAAAAAAAAAAHEAAAAiAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-25T10:27:51.509Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 17, "Title": "PaymentExternalServiceTests.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\loan-management-service\\BlueTape.Services.LMS\\BlueTape.Services.LMS.Application.Tests\\Services\\PaymentExternalServiceTests.cs", "RelativeDocumentMoniker": "BlueTape.Services.LMS.Application.Tests\\Services\\PaymentExternalServiceTests.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\loan-management-service\\BlueTape.Services.LMS\\BlueTape.Services.LMS.Application.Tests\\Services\\PaymentExternalServiceTests.cs", "RelativeToolTip": "BlueTape.Services.LMS.Application.Tests\\Services\\PaymentExternalServiceTests.cs", "ViewState": "AgIAAIQAAAAAAAAAAAAAAJQAAAAXAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-26T09:28:25.167Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 18, "Title": "QaController.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\loan-management-service\\BlueTape.Services.LMS\\BlueTape.Services.LMS.API\\Controllers\\QaController.cs", "RelativeDocumentMoniker": "BlueTape.Services.LMS.API\\Controllers\\QaController.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\loan-management-service\\BlueTape.Services.LMS\\BlueTape.Services.LMS.API\\Controllers\\QaController.cs", "RelativeToolTip": "BlueTape.Services.LMS.API\\Controllers\\QaController.cs", "ViewState": "AgIAAGQAAAAAAAAAAAAUwHIAAAAmAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-23T09:41:05.114Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 27, "Title": "deploy-azure-functions.yml", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\loan-management-service\\scripts\\CI\\deploy-azure-functions.yml", "RelativeDocumentMoniker": "..\\scripts\\CI\\deploy-azure-functions.yml", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\loan-management-service\\scripts\\CI\\deploy-azure-functions.yml", "RelativeToolTip": "..\\scripts\\CI\\deploy-azure-functions.yml", "ViewState": "AgIAAD8AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.003775|", "WhenOpened": "2025-06-25T07:32:18.527Z"}, {"$type": "Document", "DocumentIndex": 21, "Title": "IProcessDueStrategy.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\loan-management-service\\BlueTape.Services.LMS\\BlueTape.Services.LMS.AutoPay\\Infrastructure\\ProcessDueStrategy\\Abstractions\\IProcessDueStrategy.cs", "RelativeDocumentMoniker": "BlueTape.Services.LMS.AutoPay\\Infrastructure\\ProcessDueStrategy\\Abstractions\\IProcessDueStrategy.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\loan-management-service\\BlueTape.Services.LMS\\BlueTape.Services.LMS.AutoPay\\Infrastructure\\ProcessDueStrategy\\Abstractions\\IProcessDueStrategy.cs", "RelativeToolTip": "BlueTape.Services.LMS.AutoPay\\Infrastructure\\ProcessDueStrategy\\Abstractions\\IProcessDueStrategy.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAUAAAAdAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-25T11:33:29.263Z"}, {"$type": "Document", "DocumentIndex": 26, "Title": "deploy-azure-functions-manual.ps1", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\loan-management-service\\scripts\\deploy-azure-functions-manual.ps1", "RelativeDocumentMoniker": "..\\scripts\\deploy-azure-functions-manual.ps1", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\loan-management-service\\scripts\\deploy-azure-functions-manual.ps1", "RelativeToolTip": "..\\scripts\\deploy-azure-functions-manual.ps1", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.001001|", "WhenOpened": "2025-06-25T07:32:26.138Z"}, {"$type": "Document", "DocumentIndex": 22, "Title": "BasisPointController.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\loan-management-service\\BlueTape.Services.LMS\\BlueTape.Services.LMS.API\\Controllers\\BasisPointController.cs", "RelativeDocumentMoniker": "BlueTape.Services.LMS.API\\Controllers\\BasisPointController.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\loan-management-service\\BlueTape.Services.LMS\\BlueTape.Services.LMS.API\\Controllers\\BasisPointController.cs", "RelativeToolTip": "BlueTape.Services.LMS.API\\Controllers\\BasisPointController.cs", "ViewState": "AgIAADIAAAAAAAAAAAAwwA8AAAA2AAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-25T10:51:42.747Z"}, {"$type": "Document", "DocumentIndex": 24, "Title": "GetDueIHCLoansDetectorFunction.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\loan-management-service\\BlueTape.Services.LMS\\Functions\\AutoPay\\BlueTape.Functions.LMS.GetDue\\GetDueIHCLoansDetectorFunction.cs", "RelativeDocumentMoniker": "Functions\\AutoPay\\BlueTape.Functions.LMS.GetDue\\GetDueIHCLoansDetectorFunction.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\loan-management-service\\BlueTape.Services.LMS\\Functions\\AutoPay\\BlueTape.Functions.LMS.GetDue\\GetDueIHCLoansDetectorFunction.cs", "RelativeToolTip": "Functions\\AutoPay\\BlueTape.Functions.LMS.GetDue\\GetDueIHCLoansDetectorFunction.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAABUAAAAaAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-25T07:20:38.392Z"}, {"$type": "Document", "DocumentIndex": 25, "Title": "GetDueLoansDetectorFunction.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\loan-management-service\\BlueTape.Services.LMS\\Functions\\AutoPay\\BlueTape.Functions.LMS.GetDue\\GetDueLoansDetectorFunction.cs", "RelativeDocumentMoniker": "Functions\\AutoPay\\BlueTape.Functions.LMS.GetDue\\GetDueLoansDetectorFunction.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\loan-management-service\\BlueTape.Services.LMS\\Functions\\AutoPay\\BlueTape.Functions.LMS.GetDue\\GetDueLoansDetectorFunction.cs", "RelativeToolTip": "Functions\\AutoPay\\BlueTape.Functions.LMS.GetDue\\GetDueLoansDetectorFunction.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAABYAAAAbAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-25T07:10:48.145Z"}, {"$type": "Document", "DocumentIndex": 23, "Title": "ProcessDueConsumer.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\loan-management-service\\BlueTape.Services.LMS\\Functions\\AutoPay\\BlueTape.Functions.LMS.ProcessDue\\ProcessDueConsumer.cs", "RelativeDocumentMoniker": "Functions\\AutoPay\\BlueTape.Functions.LMS.ProcessDue\\ProcessDueConsumer.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\loan-management-service\\BlueTape.Services.LMS\\Functions\\AutoPay\\BlueTape.Functions.LMS.ProcessDue\\ProcessDueConsumer.cs", "RelativeToolTip": "Functions\\AutoPay\\BlueTape.Functions.LMS.ProcessDue\\ProcessDueConsumer.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAA4AAAAqAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-24T14:39:12.988Z"}, {"$type": "Document", "DocumentIndex": 29, "Title": "AutoPayLoanServiceTests.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\loan-management-service\\BlueTape.Services.LMS\\BlueTape.Services.LMS.AutoPay.Tests\\AutoPayLoanServiceTests.cs", "RelativeDocumentMoniker": "BlueTape.Services.LMS.AutoPay.Tests\\AutoPayLoanServiceTests.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\loan-management-service\\BlueTape.Services.LMS\\BlueTape.Services.LMS.AutoPay.Tests\\AutoPayLoanServiceTests.cs", "RelativeToolTip": "BlueTape.Services.LMS.AutoPay.Tests\\AutoPayLoanServiceTests.cs", "ViewState": "AgIAAKUAAAAAAAAAAIAywLMAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-24T14:26:09.079Z"}, {"$type": "Document", "DocumentIndex": 30, "Title": "BlueTape.Functions.LMS.GetDue.csproj", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\loan-management-service\\BlueTape.Services.LMS\\Functions\\AutoPay\\BlueTape.Functions.LMS.GetDue\\BlueTape.Functions.LMS.GetDue.csproj", "RelativeDocumentMoniker": "Functions\\AutoPay\\BlueTape.Functions.LMS.GetDue\\BlueTape.Functions.LMS.GetDue.csproj", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\loan-management-service\\BlueTape.Services.LMS\\Functions\\AutoPay\\BlueTape.Functions.LMS.GetDue\\BlueTape.Functions.LMS.GetDue.csproj", "RelativeToolTip": "Functions\\AutoPay\\BlueTape.Functions.LMS.GetDue\\BlueTape.Functions.LMS.GetDue.csproj", "ViewState": "AgIAAAAAAAAAAAAAAAAAAD0AAAAKAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000758|", "WhenOpened": "2025-06-24T14:38:16.984Z"}, {"$type": "Document", "DocumentIndex": 28, "Title": "IGetDueStrategy.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\loan-management-service\\BlueTape.Services.LMS\\BlueTape.Services.LMS.AutoPay\\Infrastructure\\GetDueStrategy\\Abstractions\\IGetDueStrategy.cs", "RelativeDocumentMoniker": "BlueTape.Services.LMS.AutoPay\\Infrastructure\\GetDueStrategy\\Abstractions\\IGetDueStrategy.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\loan-management-service\\BlueTape.Services.LMS\\BlueTape.Services.LMS.AutoPay\\Infrastructure\\GetDueStrategy\\Abstractions\\IGetDueStrategy.cs", "RelativeToolTip": "BlueTape.Services.LMS.AutoPay\\Infrastructure\\GetDueStrategy\\Abstractions\\IGetDueStrategy.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAcAAAAvAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-24T15:50:41.415Z"}, {"$type": "Document", "DocumentIndex": 31, "Title": "BlueTape.Functions.LMS.ProcessDue.csproj", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\loan-management-service\\BlueTape.Services.LMS\\Functions\\AutoPay\\BlueTape.Functions.LMS.ProcessDue\\BlueTape.Functions.LMS.ProcessDue.csproj", "RelativeDocumentMoniker": "Functions\\AutoPay\\BlueTape.Functions.LMS.ProcessDue\\BlueTape.Functions.LMS.ProcessDue.csproj", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\loan-management-service\\BlueTape.Services.LMS\\Functions\\AutoPay\\BlueTape.Functions.LMS.ProcessDue\\BlueTape.Functions.LMS.ProcessDue.csproj", "RelativeToolTip": "Functions\\AutoPay\\BlueTape.Functions.LMS.ProcessDue\\BlueTape.Functions.LMS.ProcessDue.csproj", "ViewState": "AgIAAAAAAAAAAAAAAAAAADMAAAAKAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000758|", "WhenOpened": "2025-06-24T14:38:17.469Z"}, {"$type": "Document", "DocumentIndex": 32, "Title": "LoanDownPaymentsServiceTests.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\loan-management-service\\BlueTape.Services.LMS\\BlueTape.Services.LMS.Application.Tests\\Services\\LoanDownPaymentsServiceTests.cs", "RelativeDocumentMoniker": "BlueTape.Services.LMS.Application.Tests\\Services\\LoanDownPaymentsServiceTests.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\loan-management-service\\BlueTape.Services.LMS\\BlueTape.Services.LMS.Application.Tests\\Services\\LoanDownPaymentsServiceTests.cs", "RelativeToolTip": "BlueTape.Services.LMS.Application.Tests\\Services\\LoanDownPaymentsServiceTests.cs", "ViewState": "AgIAAF4BAAAAAAAAAAAewHUBAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-24T14:21:08.906Z"}, {"$type": "Document", "DocumentIndex": 33, "Title": "ArsIntegrationTestsBase.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\loan-management-service\\BlueTape.Services.LMS\\BlueTape.Services.ARS.IntegrationTests\\Tests\\Base\\ArsIntegrationTestsBase.cs", "RelativeDocumentMoniker": "BlueTape.Services.ARS.IntegrationTests\\Tests\\Base\\ArsIntegrationTestsBase.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\loan-management-service\\BlueTape.Services.LMS\\BlueTape.Services.ARS.IntegrationTests\\Tests\\Base\\ArsIntegrationTestsBase.cs", "RelativeToolTip": "BlueTape.Services.ARS.IntegrationTests\\Tests\\Base\\ArsIntegrationTestsBase.cs", "ViewState": "AgIAAEMAAAAAAAAAAAAnwFoAAABPAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-24T14:16:26.846Z"}, {"$type": "Document", "DocumentIndex": 34, "Title": "AgingReportsIntegrationTests.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\loan-management-service\\BlueTape.Services.LMS\\BlueTape.Services.ARS.IntegrationTests\\Tests\\AgingReportsIntegrationTests.cs", "RelativeDocumentMoniker": "BlueTape.Services.ARS.IntegrationTests\\Tests\\AgingReportsIntegrationTests.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\loan-management-service\\BlueTape.Services.LMS\\BlueTape.Services.ARS.IntegrationTests\\Tests\\AgingReportsIntegrationTests.cs", "RelativeToolTip": "BlueTape.Services.ARS.IntegrationTests\\Tests\\AgingReportsIntegrationTests.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAkAAABAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-24T14:16:16.156Z"}, {"$type": "Document", "DocumentIndex": 35, "Title": "INotificationService.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\loan-management-service\\BlueTape.Services.LMS\\BlueTape.Services.LMS.Application\\Abstractions\\Services\\INotificationService.cs", "RelativeDocumentMoniker": "BlueTape.Services.LMS.Application\\Abstractions\\Services\\INotificationService.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\loan-management-service\\BlueTape.Services.LMS\\BlueTape.Services.LMS.Application\\Abstractions\\Services\\INotificationService.cs", "RelativeToolTip": "BlueTape.Services.LMS.Application\\Abstractions\\Services\\INotificationService.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAoAAAAZAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-24T07:55:28.63Z"}, {"$type": "Document", "DocumentIndex": 36, "Title": "DependencyRegistrar.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\loan-management-service\\BlueTape.Services.LMS\\BlueTape.Services.LMS.Application\\DI\\DependencyRegistrar.cs", "RelativeDocumentMoniker": "BlueTape.Services.LMS.Application\\DI\\DependencyRegistrar.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\loan-management-service\\BlueTape.Services.LMS\\BlueTape.Services.LMS.Application\\DI\\DependencyRegistrar.cs", "RelativeToolTip": "BlueTape.Services.LMS.Application\\DI\\DependencyRegistrar.cs", "ViewState": "AgIAAJkAAAAAAAAAAAAAwKIAAAA7AAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-24T07:10:09.233Z"}, {"$type": "Document", "DocumentIndex": 41, "Title": "AgingReportsController.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\loan-management-service\\BlueTape.Services.LMS\\BlueTape.Services.LMS.API\\Controllers\\AgingReportsController.cs", "RelativeDocumentMoniker": "BlueTape.Services.LMS.API\\Controllers\\AgingReportsController.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\loan-management-service\\BlueTape.Services.LMS\\BlueTape.Services.LMS.API\\Controllers\\AgingReportsController.cs", "RelativeToolTip": "BlueTape.Services.LMS.API\\Controllers\\AgingReportsController.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-23T10:02:02.064Z"}, {"$type": "Document", "DocumentIndex": 38, "Title": "LinqpalHttpClient.cs", "DocumentMoniker": "C:\\Users\\<USER>\\AppData\\Local\\JetBrains\\Shared\\vAny\\DecompilerCache\\decompiler\\2f9970a267af4abe839835f8444f06775600\\ec\\1f80e6b1\\LinqpalHttpClient.cs", "RelativeDocumentMoniker": "..\\..\\..\\..\\..\\AppData\\Local\\JetBrains\\Shared\\vAny\\DecompilerCache\\decompiler\\2f9970a267af4abe839835f8444f06775600\\ec\\1f80e6b1\\LinqpalHttpClient.cs", "ToolTip": "C:\\Users\\<USER>\\AppData\\Local\\JetBrains\\Shared\\vAny\\DecompilerCache\\decompiler\\2f9970a267af4abe839835f8444f06775600\\ec\\1f80e6b1\\LinqpalHttpClient.cs", "RelativeToolTip": "..\\..\\..\\..\\..\\AppData\\Local\\JetBrains\\Shared\\vAny\\DecompilerCache\\decompiler\\2f9970a267af4abe839835f8444f06775600\\ec\\1f80e6b1\\LinqpalHttpClient.cs", "ViewState": "AgIAADMAAAAAAAAAAAAAADoAAAAGAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-23T10:24:38.406Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 37, "Title": "ReceivableRecalculatorService.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\loan-management-service\\BlueTape.Services.LMS\\BlueTape.Services.LMS.Application\\Services\\ReceivableRecalculatorService.cs", "RelativeDocumentMoniker": "BlueTape.Services.LMS.Application\\Services\\ReceivableRecalculatorService.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\loan-management-service\\BlueTape.Services.LMS\\BlueTape.Services.LMS.Application\\Services\\ReceivableRecalculatorService.cs", "RelativeToolTip": "BlueTape.Services.LMS.Application\\Services\\ReceivableRecalculatorService.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-23T11:13:10.35Z"}, {"$type": "Document", "DocumentIndex": 40, "Title": "CreditHoldsController.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\loan-management-service\\BlueTape.Services.LMS\\BlueTape.Services.LMS.API\\Controllers\\CreditHoldsController.cs", "RelativeDocumentMoniker": "BlueTape.Services.LMS.API\\Controllers\\CreditHoldsController.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\loan-management-service\\BlueTape.Services.LMS\\BlueTape.Services.LMS.API\\Controllers\\CreditHoldsController.cs", "RelativeToolTip": "BlueTape.Services.LMS.API\\Controllers\\CreditHoldsController.cs", "ViewState": "AgIAABQAAAAAAAAAAAAQwAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-23T10:02:03.975Z"}, {"$type": "Document", "DocumentIndex": 39, "Title": "AuthorizationPeriodController.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\loan-management-service\\BlueTape.Services.LMS\\BlueTape.Services.LMS.API\\Controllers\\AuthorizationPeriodController.cs", "RelativeDocumentMoniker": "BlueTape.Services.LMS.API\\Controllers\\AuthorizationPeriodController.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\loan-management-service\\BlueTape.Services.LMS\\BlueTape.Services.LMS.API\\Controllers\\AuthorizationPeriodController.cs", "RelativeToolTip": "BlueTape.Services.LMS.API\\Controllers\\AuthorizationPeriodController.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-23T10:03:09.28Z"}]}]}]}