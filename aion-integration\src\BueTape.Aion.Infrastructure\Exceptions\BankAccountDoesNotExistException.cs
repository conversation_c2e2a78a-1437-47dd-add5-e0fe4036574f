﻿using BlueTape.Common.ExceptionHandling.Exceptions.Base;
using System.Net;
using System.Runtime.Serialization;

namespace BueTape.Aion.Infrastructure.Exceptions;

[Serializable]
public class BankAccountDoesNotExistException : DomainException
{
    public BankAccountDoesNotExistException(string bankAccountId, HttpStatusCode statusCode = HttpStatusCode.BadRequest) : base(BuildErrorMessage(bankAccountId), statusCode)
    {
    }

    protected BankAccountDoesNotExistException(SerializationInfo info, StreamingContext context) : base(info, context)
    {
    }

    private static string BuildErrorMessage(string bankAccountId)
    {
        return $"BankAccount with id: {bankAccountId} does not exist";
    }

    public override string Code => ErrorCodes.BankAccountDoesNotExist;
}