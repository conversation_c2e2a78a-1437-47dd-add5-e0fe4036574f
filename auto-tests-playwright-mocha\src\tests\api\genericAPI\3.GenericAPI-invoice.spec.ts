import {expect} from '@playwright/test';
import {test} from '../../test-utils';
import {
    createCustomer,
    createInvoice,
    cancelInvoice,
    createProject,
    linkInvoiceAndProject,
    deleteLinkedInvoiceAndProject
} from '../../../api/common/send-generic-api-request';
import {BaseTest} from '../../test-utils';

const error = JSON.parse(JSON.stringify(require('../../../constants/generic-api-errors.json')));

test.describe(`Invoice tests @generic @API`, async () => {
    let customerId: string;
    let invoiceId: string;
    let blueTapeId: string;

    test.beforeAll(async () => {
        customerId = `customerId${BaseTest.dateTimePrefix()}`;
        invoiceId = `invoiceId${BaseTest.dateTimePrefix()}`;
        await createCustomer(null, null, customerId);
    });

    test(`Create Invoice`, async () => {
        const invoiceData = {
            customerId: customerId,
            invoiceId: invoiceId
        };
        const response = await createInvoice(invoiceData);
        blueTapeId = await response.data.blueTapeId;

        expect(response.status, `Status code 201`).toEqual(201);

        expect(response.data,
            `Response contains Object`).toEqual(expect.any(Object));
    });

    test(`Cannot Create Invoice with non-unic Invoice ID`, async () => {
        const invoiceData = {
            customerId: customerId,
            invoiceId: invoiceId
        };
        const response = await createInvoice(invoiceData);

        expect(response.status, `Status code 201`).toEqual(201);

        expect(response.data.blueTapeId,
            `BlueTape id equal to ${blueTapeId}`).toEqual(blueTapeId);
        // ADD REASON and CODE CHECKS
    });

    test(`Cannot create Invoice with Invoice ID = null`, async () => {
        const invoiceData = {
            customerId: customerId,
            invoiceId: null,
        };
        const response = await createInvoice(invoiceData);

        expect(response.status, `Status code 400`).toEqual(400);

        expect(response.data,
            `Response contains Object`).toEqual(expect.any(Object));

        expect(response.data[0].code,
            `Error Code: ${error.invoice.code.invalidDataFormat}`)
            .toEqual(error.invoice.code.invalidDataFormat);

        expect(response.data[0].reason,
            `Error Reason:${error.invoice.reason.nullInIdField}`)
            .toEqual(error.invoice.reason.nullInIdField);
    });

    test.skip(`Cannot create Invoice with invalid QUATE_REFERENCE`, async () => { //todo investigate about quote ref
        const invoiceData = {
            customerId: customerId,
            quoteRefNumber: 'random0987'
        };
        const response = await createInvoice(invoiceData);

        expect(response.status, `Status code 400`).toEqual(400);

        expect(response.data,
            `Response contains Array`).toEqual(expect.any(Array));

        expect(response.data[0].code,
            `Error Code: ${error.invoice.code.invalidQuoteRefNumber}`)
            .toEqual(error.invoice.code.invalidQuoteRefNumber);

        expect(response.data[0].reason,
            `Error Reason: Quote Reference with id: "random0987" does not exist.`)
            .toEqual(`Quote Reference with id: "random0987" does not exist.`);
    });


    const amountArray = [0, -1];
    for (const amount of amountArray) {
        test(`Cannot create Invoice with Total Amount = ${amount}`, async () => {
            const invoiceData = {
                customerId: customerId,
                totalAmount: amount,
            };
            const response = await createInvoice(invoiceData);

            expect(response.status, `Status code 400`).toEqual(400);

            expect(response.data,
                `Response contains Array`).toEqual(expect.any(Array));

            expect(response.data[0].code,
                `Error Code: ${error.common.invalidData}`)
                .toEqual(error.common.invalidData);

            expect(response.data[0].reason,
                `Error Reason:${error.invoice.reason.invalidTotaAmount}`)
                .toEqual(error.invoice.reason.invalidTotaAmount);
        });
    }

    test(`Cannot create Invoice with String in Total Amount`, async () => {
        const invoiceData = {
            customerId: customerId,
            totalAmount: 'str',
        };
        const response = await createInvoice(invoiceData);

        expect(response.status, `Status code 400`).toEqual(400);

        expect(response.data,
            `Response contains Array`).toEqual(expect.any(Array));

        expect(response.data[0].code,
            `Error Code: ${error.invoice.code.invalidDataFormat}`)
            .toEqual(error.invoice.code.invalidDataFormat);

        expect(response.data[0].reason,
            `Error Reason:${error.invoice.reason.invalidDataFormat}`)
            .toEqual(error.invoice.reason.invalidDataFormat);
    });

    const subTotalAmountArray = [0, -1];
    for (const amount of subTotalAmountArray) {
        test(`Cannot create Invoice with SubTotal Amount = ${amount}`, async () => {
            const invoiceData = {
                customerId: customerId,
                subTotalAmount: amount,
            };
            const response = await createInvoice(invoiceData);

            expect(response.status, `Status code 400`).toEqual(400);

            expect(response.data,
                `Response contains Array`).toEqual(expect.any(Array));

            expect(response.data[0].code,
                `Error Code: ${error.common.invalidData}`)
                .toEqual(error.common.invalidData);

            expect(response.data[0].reason,
                `Error Reason:${error.invoice.reason.invalidSubTotal}`)
                .toEqual(error.invoice.reason.invalidSubTotal);
        });
    }

    test(`Cannot create Invoice with String in SubTotal Amount`, async () => {
        const invoiceData = {
            customerId: customerId,
            subTotalAmount: 'str',
        };
        const response = await createInvoice(invoiceData);

        expect(response.status, `Status code 400`).toEqual(400);

        expect(response.data,
            `Response contains Array`).toEqual(expect.any(Array));

        expect(response.data[0].code,
            `Error Code: ${error.invoice.code.invalidDataFormat}`)
            .toEqual(error.invoice.code.invalidDataFormat);

        expect(response.data[0].reason,
            `Error Reason:${error.invoice.reason.invalidDataFormat}`)
            .toEqual(error.invoice.reason.invalidDataFormat);
    });

    // #######################################################################
    // TODO KATE: add tests
    // 0 taxAmount, null taxAmount, -1 taxAmount
    // invoiceDate: less than current, in the future, 
    // invoice number not null
    // customerID is not null
    // dueDate: less than current, in the future, 
    // expirationDate: less than current, in the future,
    // sourceModifiedDate ?
    // #######################################################################

    test(`Cannot create Invoice with Tax Amount = null`, async () => {
        const invoiceData = {
            customerId: customerId,
            taxAmount: null,
        };
        const response = await createInvoice(invoiceData);

        expect(response.status, `Status code 400`).toEqual(400);

        expect(response.data,
            `Response contains Object`).toEqual(expect.any(Object));

        expect(response.data[0].code,
            `Error Code: ${error.invoice.code.invalidDataFormat}`)
            .toEqual(error.invoice.code.invalidDataFormat);

        expect(response.data[0].reason,
            `Error Reason:${error.invoice.reason.invalidDataFormat}`)
            .toEqual(error.invoice.reason.invalidDataFormat);
    });

    test(`Create Invoice with Tax Amount = '0'`, async () => {
        const invoiceData = {
            customerId: customerId,
            taxAmount: '0',
        };
        const response = await createInvoice(invoiceData);

        expect(response.status, `Status code 201`).toEqual(201);

        expect(response.data,
            `Response contains Object`).toEqual(expect.any(Object));
    });

    test(`Cannot create Invoice with Tax Amount = '-1'`, async () => {
        const invoiceData = {
            customerId: customerId,
            taxAmount: '-1',
        };
        const response = await createInvoice(invoiceData);

        expect(response.status, `Status code 400`).toEqual(400);

        expect(response.data,
            `Response contains Object`).toEqual(expect.any(Object));

        expect(response.data[0].code,
            `Error Code: ${error.common.invalidData}`)
            .toEqual(error.common.invalidData);

        expect(response.data[0].reason,
            `Error Reason:${error.invoice.reason.invalidTaxAmount}`)
            .toEqual(error.invoice.reason.invalidTaxAmount);
    });

    test(`Cannot create Invoice with Invoice Data in the past`, async () => {
        const previousDate = BaseTest.changeCurrentDate('minus', 20);
        const invoiceData = {
            customerId: customerId,
            invoiceDate: previousDate,
        };

        const response = await createInvoice(invoiceData);

        expect(response.status, `Status code 400`).toEqual(400);

        expect(response.data,
            `Response contains Object`).toEqual(expect.any(Object));
    });

    test(`Create Invoice with Invoice Data in the future`, async () => {
        const nextDate = BaseTest.changeCurrentDate('plus', 20);
        const invoiceData = {
            customerId: customerId,
            invoiceData: nextDate,
        };

        const response = await createInvoice(invoiceData);

        expect(response.status, `Status code 201`).toEqual(201);

        expect(response.data,
            `Response contains Object`).toEqual(expect.any(Object));
    });

    test(`Cannot create Invoice with Invoice Number = null`, async () => {
        const invoiceData = {
            customerId: customerId,
            invoiceNumber: null,
        };

        const response = await createInvoice(invoiceData);

        expect(response.status, `Status code 400`).toEqual(400);

        expect(response.data,
            `Response contains Object`).toEqual(expect.any(Object));

        expect(response.data[0].code,
            `Error Code: ${error.invoice.code.invalidDataFormat}`)
            .toEqual(error.invoice.code.invalidDataFormat);

        expect(response.data[0].reason,
            `Error Reason:${error.invoice.reason.invalidInvoiceNum}`)
            .toEqual(error.invoice.reason.invalidInvoiceNum);
    });

    test(`Cannot create Invoice with Customer ID = null`, async () => {
        const invoiceData = {
            customerId: null,
        };

        const response = await createInvoice(invoiceData);

        expect(response.status, `Status code 400`).toEqual(400);

        expect(response.data,
            `Response contains Object`).toEqual(expect.any(Object));

        expect(response.data[0].code,
            `Error Code: ${error.invoice.code.invalidDataFormat}`)
            .toEqual(error.invoice.code.invalidDataFormat);

        expect(response.data[0].reason,
            `Error Reason:${error.invoice.reason.invalidCustomerId}`)
            .toEqual(error.invoice.reason.invalidCustomerId);
    });

    test(`Cannot create Invoice with Due Date in the past`, async () => {
        const previousDate = BaseTest.changeCurrentDate('minus', 20);
        const invoiceData = {
            customerId: customerId,
            dueDate: previousDate,
        };

        const response = await createInvoice(invoiceData);

        expect(response.status, `Status code 400`).toEqual(400);

        expect(response.data,
            `Response contains Object`).toEqual(expect.any(Object));

        expect(response.data[0].code,
            `Error Code: ${error.common.invalidData}`)
            .toEqual(error.common.invalidData);
    });

    test(`Cannot create Invoice with Due Date in the future`, async () => {
        const nextDate = BaseTest.changeCurrentDate('plus', 20);
        const invoiceData = {
            customerId: customerId,
            dueDate: nextDate,
        };

        const response = await createInvoice(invoiceData);

        expect(response.status, `Status code 201`).toEqual(201);

        expect(response.data,
            `Response contains Object`).toEqual(expect.any(Object));
    });

    test(`Cannot create Invoice with Expiration Date in the past`, async () => {
        const previousDate = BaseTest.changeCurrentDate('minus', 20);
        const invoiceData = {
            customerId: customerId,
            expirationDate: previousDate,
        };

        const response = await createInvoice(invoiceData);

        expect(response.status, `Status code 400`).toEqual(400);

        expect(response.data,
            `Response contains Object`).toEqual(expect.any(Object));

        expect(response.data[0].code,
            `Error Code: ${error.common.invalidData}`)
            .toEqual(error.common.invalidData);
    });

    test(`Create Invoice with Expiration Date in the future`, async () => {
        const nextDate = BaseTest.changeCurrentDate('plus', 20);
        const invoiceData = {
            customerId: customerId,
            expirationDate: nextDate,
        };

        const response = await createInvoice(invoiceData);

        expect(response.status, `Status code 201`).toEqual(201);

        expect(response.data,
            `Response contains Object`).toEqual(expect.any(Object));
    });

    // test(`Cannot create Invoice with Source Modified Date in the past`, async () => {
    //     const previousDate = BaseTest.changeCurrentDate('minus', 20);  
    //     const invoiceData = {
    //         customerId: customerId,
    //         sourceModifiedDate: previousDate,
    //     };
    //     const response = await createInvoice(invoiceData);
    //     expect(response.status, `Status code 400`).toEqual(400);
    //     expect(response.data, `Response contains Object`).toEqual(expect.any(Object));
    //     // TO CHECK
    // });

    //CANCEL INVOICE

    test(`Cancel Invoice`, async () => {
        const invoiceId = `invoiceId${BaseTest.dateTimePrefix()}`;
        const invoiceData = {
            customerId,
            invoiceId,
        };

        await createInvoice(invoiceData);

        const response = await cancelInvoice(invoiceId);

        expect(response.status, `Status code 202`).toEqual(202);
    });

    test(`Cannot cancel Invoice with non existent invoice`, async () => {
        const invoiceId = `invoiceId${BaseTest.dateTimePrefix()}`;
        const response = await cancelInvoice(invoiceId);

        expect(response.status, `Status code 400`).toEqual(400);

        expect(response.data,
            `Response contains Object`).toEqual(expect.any(Object));

        expect(response.data[0].code,
            `Error Code: ${error.invoice.code.invoiceNonExistent}`)
            .toEqual(error.invoice.code.invoiceNonExistent);

        expect(response.data[0].reason,
            `Error Reason:${error.invoice.reason.invoiceNonExistent}`)
            .toEqual(error.invoice.reason.invoiceNonExistent);
    });

    //LINK INVOICE WITH PROJECT

    test(`Link invoice with project`, async () => {
        const projectId = `projectId${BaseTest.dateTimePrefix()}`;
        const invoiceData = {
            customerId,
            invoiceId,
        };

        const projectData = {
            projectId,
        };

        await createInvoice(invoiceData);
        await createProject(projectData);

        const response = await linkInvoiceAndProject(invoiceId, projectId);

        expect(response.status, `Status code 201`).toEqual(201);
    });

    test(`Cannot link invoice and project with linked invoice`, async () => {
        const projectId = `projectId${BaseTest.dateTimePrefix()}`;
        const invoiceData = {
            customerId,
            invoiceId,
        };

        const projectData = {
            projectId,
        };

        await createInvoice(invoiceData);
        await createProject(projectData);

        const response = await linkInvoiceAndProject(invoiceId, projectId);

        expect(response.status, `Status code 400`).toEqual(400);

        expect(response.data,
            `Response contains Object`).toEqual(expect.any(Object));

        expect(response.data[0].code,
            `Error message: ${error.invoice.code.invoiceLinked}`)
            .toEqual(error.invoice.code.invoiceLinked);

        expect(response.data[0].reason,
            `Error message: ${error.invoice.reason.invoiceLinked}`)
            .toEqual(error.invoice.reason.invoiceLinked);
    });

    test(`Cannot link invoice and project with non existent project`, async () => {
        const projectId = `projectId${BaseTest.dateTimePrefix()}`;
        invoiceId = `invoiceId${BaseTest.dateTimePrefix()}`;
        const invoiceData = {
            customerId,
            invoiceId,
        };

        await createInvoice(invoiceData);

        const response = await linkInvoiceAndProject(invoiceId, projectId);

        expect(response.status, `Status code 400`).toEqual(400);

        expect(response.data,
            `Response contains Object`).toEqual(expect.any(Object));

        expect(response.data[0].code,
            `Error message: ${error.project.code.invalidId}`)
            .toEqual(error.project.code.invalidId);

        expect(response.data[0].reason,
            `Error message: ${error.project.reason.invalidId}`)
            .toEqual(error.project.reason.invalidId);
    });

    test(`Cannot link invoice and project with non existent invoice`, async () => {
        const projectId = `projectId${BaseTest.dateTimePrefix()}`;
        invoiceId = `invoiceId${BaseTest.dateTimePrefix()}`;
        const projectData = {
            projectId,
        };

        await createProject(projectData);

        const response = await linkInvoiceAndProject(invoiceId, projectId);

        expect(response.status, `Status code 400`).toEqual(400);

        expect(response.data,
            `Response contains Object`).toEqual(expect.any(Object));

        expect(response.data[0].code,
            `Error message: ${error.invoice.code.invoiceNonExistent}`)
            .toEqual(error.invoice.code.invoiceNonExistent);

        expect(response.data[0].reason,
            `Error message: ${error.invoice.reason.invoiceNonExistent}`)
            .toEqual(error.invoice.reason.invoiceNonExistent);
    });

    //UNLINK INVOICE AND PROJECT

    test(`Unlink invoice and project`, async () => {
        const projectId = `projectId${BaseTest.dateTimePrefix()}`;
        invoiceId = `invoiceId${BaseTest.dateTimePrefix()}`;
        const invoiceData = {
            customerId,
            invoiceId,
        };

        const projectData = {
            projectId,
        };

        await createInvoice(invoiceData);
        await createProject(projectData);
        await linkInvoiceAndProject(invoiceId, projectId);

        const response = await deleteLinkedInvoiceAndProject(invoiceId, projectId);

        expect(response.status, `Status code 200`).toEqual(200);
    });

    test(`Cannot unlink invoice and project with invoice ID = null`, async () => {
        const projectId = `projectId${BaseTest.dateTimePrefix()}`;
        invoiceId = `invoiceId${BaseTest.dateTimePrefix()}`;
        const invoiceData = {
            customerId,
            invoiceId,
        };

        const projectData = {
            projectId,
        };

        await createInvoice(invoiceData);
        await createProject(projectData);
        await linkInvoiceAndProject(invoiceId, projectId);

        const response = await deleteLinkedInvoiceAndProject(null, projectId);

        expect(response.status, `Status code 400`).toEqual(400);

        expect(response.data,
            `Response contains Object`).toEqual(expect.any(Object));

        expect(response.data[0].code,
            `Error Code: ${error.invoice.code.invoiceNonExistent}`)
            .toEqual(error.invoice.code.invoiceNonExistent);

        expect(response.data[0].reason,
            `Error Reason:${error.invoice.reason.invoiceNonExistent}`)
            .toEqual(error.invoice.reason.invoiceNonExistent);
    });

    test(`Cannot unlink invoice and project with project ID = null`, async () => {
        const projectId = `projectId${BaseTest.dateTimePrefix()}`;
        invoiceId = `invoiceId${BaseTest.dateTimePrefix()}`;
        const invoiceData = {
            customerId,
            invoiceId,
        };

        const projectData = {
            projectId,
        };

        await createInvoice(invoiceData);
        await createProject(projectData);
        await linkInvoiceAndProject(invoiceId, projectId);

        const response = await deleteLinkedInvoiceAndProject(invoiceId, null);

        expect(response.status, `Status code 400`).toEqual(400);

        expect(response.data,
            `Response contains Object`).toEqual(expect.any(Object));

        expect(response.data[0].code,
            `Error message: ${error.project.code.invalidId}`)
            .toEqual(error.project.code.invalidId);

        expect(response.data[0].reason,
            `Error message: ${error.project.reason.invalidId}`)
            .toEqual(error.project.reason.invalidId);
    });
});
