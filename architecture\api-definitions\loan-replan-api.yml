openapi: '3.0.0'
info:
  version: '0.0.1'
  title: 'Loan Service Replan API'
  description: | 
    API definition proposal for replanning receivables
servers:
- url: TBD-Dev
  description: Development server
- url: TBD-QA
  description: QA server
- url: TBD-Prod
  description: Production server
paths:
  /loans/{id}:
    put:
      tags:
        - Loans
      summary: Replans a loan by attaching to a new loan template
      description: Replans a loan by attaching to a new loan template
      operationId: replanLoan
      parameters:
        - name: id
          description: Identifier of the loan
          example: 6df30249-c7f0-4f17-b06f-c29b2fee0a62
          in: path
          required: true
          schema:
            type: string
            nullable: false
      requestBody:
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/LoanReplanRequest"
      responses:
        200:
          description: Updated loan
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/LoanViewModel'
        400:
          description: Invalid request
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        401:
          description: Unauthorized
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        404:
          description: Not found
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        500:
          description: Unexpected error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
components:
  schemas:
    LoanReplanRequest:
      type: object
      required: 
        - newLoanTemplateId
      properties:
        newLoanTemplateId:
          type: string
          nullable: false
          example: 3ee4cbd6-935a-436e-b2d1-041ac9a5dc11
          description: The new loan template id to attach to loan
        replanDate:
          type: string
          format: date
          description: Optional parameter to help testers, default today.
          nullable: true
    LoanReceivablesRescheduleItem:
      type: object
      required: 
        - receivableType
        - newExpectedDate
        - newScheduleStatus
        - newExpectedAmount
      properties: 
        id:
          type: string
          description: The id of loan receivable to modify. If Id is not filled, a new receivable is created.
          example: 8867a199-02d1-46ca-ada0-e9e286fb6841
          nullable: true
        receivableType:
          type: string
          enum:
            - installment
            - loanFee
            - lateFee
            - extensionFee
          description: The type of loan receivable. Could not be changed for existing receivables, only for new ones.
          example: installment
          nullable: false
        newExpectedDate:
          type: string
          format: date
          description: The new expected date for the loan receivable, which can be the same that is was before.
          example: 2023-01-01
          nullable: false
        newScheduleStatus:
          type: string
          enum:
            - current
            - postponed
            - scheduled
          description: The new schedule status for the receivable. For new items (no id provided) should be current.
          example: postponed
          nullable: false
        newExpectedAmount:
          type: number
          format: decimal
          description: The new expected amount for the receivable, which can be the same that is was before. Cannot be negative number.
          example: 1000.00
          pattern: '[0-9]*\.[0-9]{2}'
          multipleOf: 0.01
          nullable: false
    LoanViewModel:
      type: object
      properties:
        id:
          type: string
          format: uuid
        companyId:
          type: string
          nullable: true
        amount:
          type: number
          format: double
        fee:
          type: number
          format: double
        loanTemplateId:
          type: string
          format: uuid
        loanTemplate:
          $ref: '#/components/schemas/LoanTemplateViewModel'
        loanDetails:
          $ref: '#/components/schemas/LoanDetailsViewModel'
        loanReceivables:
          type: array
          items:
            $ref: '#/components/schemas/LoanReceivableViewModel'
          nullable: true
        payments:
          type: array
          items:
            $ref: '#/components/schemas/PaymentViewModel'
          nullable: true
        loanParameters:
          type: array
          items:
            $ref: '#/components/schemas/LoanParametersViewModel'
          nullable: true
        status:
          $ref: '#/components/schemas/LoanStatus'
        isDeleted:
          type: boolean
        isOverdue:
          type: boolean
        startDate:
          type: string
          format: date
        closeDate:
          type: string
          format: date
          nullable: true
        lastPaymentDate:
          type: string
          format: date
          nullable: true
        lastSyncDate:
          type: string
          format: date-time
          nullable: true
      additionalProperties: false    
    LoanTemplateViewModel:
      type: object
      properties:
        id:
          type: string
          format: uuid
        loanFeePercentage:
          type: number
          format: double
        installmentsNumber:
          type: integer
          format: int32
        paymentDelayInDays:
          type: integer
          format: int32
        paymentIntervalInDays:
          type: integer
          format: int32
        minimumLateFeeAmount:
          type: number
          format: double
        lateFeePercentage:
          type: number
          format: double
        gracePeriodInDays:
          type: integer
          format: int32
        earlyPayPeriod:
          type: integer
          format: int32
        totalDurationInDays:
          type: integer
          format: int32
        code:
          type: string
          nullable: true
      additionalProperties: false
    LoanDetailsViewModel:
      type: object
      properties:
        loanId:
          type: string
          format: uuid
        businessDaysLate:
          type: integer
          format: int32
        nextPaymentAmount:
          type: number
          format: double
        nextPaymentDate:
          type: string
          format: date
          nullable: true
        lateAmount:
          type: number
          format: double
        totalProcessingAmount:
          type: number
          format: double
        principalBalance:
          type: number
          format: double
        loanOutstandingAmount:
          type: number
          format: double
        totalPaid:
          type: number
          format: double
        totalLoanAmount:
          type: number
          format: double
        isAnyPaymentMissed:
          type: boolean
        isFullyPaid:
          type: boolean
      additionalProperties: false
    LoanParametersViewModel:
      type: object
      properties:
        id:
          type: string
          format: uuid
        isActive:
          type: boolean
        loanId:
          type: string
          format: uuid
        loanFeePercentage:
          type: number
          format: double
        installmentsNumber:
          type: integer
          format: int32
        paymentDelayInDays:
          type: integer
          format: int32
        paymentIntervalInDays:
          type: integer
          format: int32
        minimumLateFeeAmount:
          type: number
          format: double
        lateFeePercentage:
          type: number
          format: double
        gracePeriodInDays:
          type: integer
          format: int32
        earlyPayPeriod:
          type: integer
          format: int32
        totalDurationInDays:
          type: integer
          format: int32
        code:
          type: string
          nullable: true
      additionalProperties: false
    LoanReceivableViewModel:
      type: object
      properties:
        id:
          type: string
          format: uuid
        expectedDate:
          type: string
          format: date
        actualDate:
          type: string
          format: date
          nullable: true
        expectedAmount:
          type: number
          format: double
        actualAmount:
          type: number
          format: double
        status:
          $ref: '#/components/schemas/LoanReceivableStatus'
        type:
          $ref: '#/components/schemas/ReceivableType'
        loanId:
          type: string
          format: uuid
        loan:
          $ref: '#/components/schemas/LoanViewModel'
        createdBy:
          type: string
          nullable: true
        updatedBy:
          type: string
          nullable: true
      additionalProperties: false
    PaymentViewModel:
      type: object
      properties:
        id:
          type: string
          format: uuid
        date:
          type: string
          format: date
        amount:
          type: number
          format: double
        status:
          $ref: '#/components/schemas/PaymentStatus'
        transactionNumber:
          type: string
          nullable: true
        type:
          $ref: '#/components/schemas/PaymentType'
        loanId:
          type: string
          format: uuid
        loan:
          $ref: '#/components/schemas/LoanViewModel'
      additionalProperties: false
    LoanStatus:
      type: string
      enum:
        - None
        - Created
        - Started
        - Pending
        - Canceled
        - Closed
        - Defaulted
        - Recovered
    LoanReceivableStatus:
      type: string
      enum:
        - None
        - Paid
        - Missed
        - Pending
        - Processing
        - Canceled
    ReceivableType:
      type: string
      enum:
        - Installment
        - LoanFee
        - LatePaymentFee
        - ManualLatePaymentFee
    PaymentStatus:
      type: string
      enum:
        - None
        - Success
        - Rejected
        - Processing
        - Canceled
    PaymentType:
      type: string
      enum:
        - AutoDebit
        - Manual
        - Custom
    ErrorResponse:
      type: object
      required:
        - statusCode
        - message
        - errorDescription
      properties:
        statusCode:
          description: HTTP status code corresponding to the error.
          type: integer
          example: 400
        message:
          description: A human readable error message.
          type: string
          example: Invalid model.
        errorDescription:
          description: Detailed information.
          type: string
  securitySchemes:
    ApiKey:
      type: apiKey
      in: header
      name: X-Api-Key
security:
  - ApiKey: []