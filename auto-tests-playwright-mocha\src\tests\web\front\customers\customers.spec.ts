import {expect} from '@playwright/test';
import {test, BaseTest} from '../../../test-utils';
import {BaseAPI} from '../../../../api/base-api';
import {deleteUser, getUserSub} from "../../../../api/admin";


test.describe('Customers tests', async () => {
    let email;
    let firstName;
    let lastName;
    let businessName;
    let cellPhoneNumber;
    let invoiceNumber;

    test.beforeEach(async () => {
        email = `automation_user+${BaseTest.dateTimePrefix()}@bluetape.com`;
        firstName = BaseTest.dateTimePrefix() + 'firstName';
        lastName = BaseTest.dateTimePrefix() + 'lastName';
        businessName = BaseTest.dateTimePrefix() + 'businessName';
        cellPhoneNumber = BaseTest.getCellPhoneNumber();
        invoiceNumber = BaseTest.dateTimePrefix() + 'invoiceNumber';

        const googleIDToken = await BaseAPI.googleSignUp(email, BaseTest.constants.password);
        await BaseAPI.userSignUp(businessName, email, firstName, googleIDToken, lastName, cellPhoneNumber);
    });

    test.afterEach(async ({page, adminIdToken}) => {
        const userSub = await getUserSub(adminIdToken, email);
        await deleteUser(adminIdToken, userSub);
        await page.close();
    });

    test('Add new customer. Send invoice to customer. @smoke', async ({pageManager, page}) => {
        // https://linqpal.atlassian.net/browse/LP-4905
        await pageManager.sideMenu.openSalesSubTab(pageManager.sideMenu.sideMenuSubTabs.sales.customers);
        await pageManager.customersList.buttons.addCustomer.click();
        await page.waitForLoadState("networkidle");
        await pageManager.addCustomerModal.inputFields.searchCustomer.fill(businessName);
        await pageManager.addCustomerModal.selectFirstCustomer();

        await expect(pageManager.addCustomerModal.inputFields.cellPhoneNumber,
            `Cell phone number should be pre-filled with ${cellPhoneNumber}.`).toHaveValue(cellPhoneNumber);

        await expect(pageManager.addCustomerModal.inputFields.firstName,
            `First name field should be pre-filled with ${firstName}.`).toHaveValue(firstName);

        await expect(pageManager.addCustomerModal.inputFields.lastName,
            `Last name field should be pre-filled with ${lastName}.`).toHaveValue(lastName);

        await expect(pageManager.addCustomerModal.inputFields.businessName,
            `Business name field should be pre-filled with ${businessName}.`).toHaveValue(businessName);

        await expect(pageManager.addCustomerModal.inputFields.emailAddress,
            `Email address field should be pre-filled with ${email}.`).toHaveValue(email);

        await pageManager.addCustomerModal.buttons.addCustomer.click();
        await pageManager.customersList.clickOnCustomerRow(businessName);
        await pageManager.customerDetailsModal.buttons.addInvoice.click();
        await pageManager.addInvoiceModal.fillUpAddInvoice(invoiceNumber, '202', '2');

        await expect(await pageManager.customersList.popups.invoiceSent,
            `Invoice sent popup should be visible.`).toBeVisible();
    });
});
