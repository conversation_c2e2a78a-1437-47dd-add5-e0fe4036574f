import {BasePage} from '../../base.page';

export class Projects extends BasePage {
    constructor(page) {
        super(page);
    };

    buttons = {
        addNewProject: this.page.locator('[data-testid="projects-add-button"]'),
    };

    elements = {
        lastCreatedProject: this.page.locator('#row-0'),
    };

    async clickAddNewProject() {
        await this.buttons.addNewProject.click();
    }
}