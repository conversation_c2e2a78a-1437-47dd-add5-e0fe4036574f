﻿using System.Runtime.Serialization;
using System.Text.Json.Serialization;

namespace BlueTape.Aion.Application.Models.Ach.Pull.Response;

[DataContract]
public class AchObjResponseModel
{
    [JsonPropertyName("id")]
    public string Id { get; set; } = null!;

    [JsonPropertyName("accountId")]
    public string AccountId { get; set; } = null!;

    [JsonPropertyName("amount")]
    public string Amount { get; set; } = null!;
    
    [JsonPropertyName("feeAmount")]
    public decimal FeeAmount { get; set; }

    [JsonPropertyName("counterpartyId")]
    public string CounterpartyId { get; set; } = null!;

    [JsonPropertyName("description")]
    public string Description { get; set; } = null!;

    [JsonPropertyName("direction")]
    public string Direction { get; set; } = null!;

    [JsonPropertyName("error")]
    public string Error { get; set; } = null!;

    [JsonPropertyName("secCode")]
    public string SecCode { get; set; } = null!;

    [JsonPropertyName("status")]
    public string Status { get; set; } = null!;

    [JsonPropertyName("counterpartyName")]
    public string CounterpartyName { get; set; } = null!;

    [JsonPropertyName("counterpartyType")]
    public string CounterpartyType { get; set; } = null!;

    [JsonPropertyName("email")]
    public bool Email { get; set; }

    [JsonPropertyName("userNote")]
    public string UserNote { get; set; } = null!;

    [JsonPropertyName("sendEmail")]
    public bool SendEmail { get; set; }

    [JsonPropertyName("effectiveDate")]
    public string EffectiveDate { get; set; } = null!;

    [JsonPropertyName("transferMethodId")]
    public string TransferMethodId { get; set; } = null!;

    [JsonPropertyName("initiatedBy")]
    public string InitiatedBy { get; set; } = null!;

    [JsonPropertyName("contextIdentifier")]
    public string ContextIdentifier { get; set; } = null!;

    [JsonPropertyName("addenda")]
    public List<string> Addenda { get; set; } = null!;

    [JsonPropertyName("createdAt")]
    public DateTime CreatedAt { get; set; }

    [JsonPropertyName("updatedAt")]
    public DateTime UpdatedAt { get; set; }

    [JsonPropertyName("originator")]
    public OriginatorResponseModel Originator { get; set; } = null!;

    [JsonPropertyName("receiver")]
    public ReceiverResponseModel Receiver { get; set; } = null!;

    [JsonPropertyName("referenceId")]
    public string ReferenceId { get; set; } = null!;

    [JsonPropertyName("paymentType")]
    public string PaymentType { get; set; } = null!;

    [JsonPropertyName("postingCode")]
    public string PostingCode { get; set; } = null!;

    [JsonPropertyName("posting")]
    public string Posting { get; set; } = null!;

    [JsonPropertyName("reasonCode")]
    public string ReasonCode { get; set; } = null!;

    [JsonPropertyName("reasonData")]
    public string ReasonData { get; set; } = null!;

    [JsonPropertyName("traceNumber")]
    public string TraceNumber { get; set; } = null!;

    [JsonPropertyName("transactionType")]
    public string TransactionType { get; set; } = null!;

    [JsonPropertyName("serviceType")]
    public string ServiceType { get; set; } = null!;

    [JsonPropertyName("wasReturned")]
    public bool WasReturned { get; set; }

    [JsonPropertyName("wasCorrected")]
    public bool WasCorrected { get; set; }

    [JsonPropertyName("canceledAt")]
    public DateTime CanceledAt { get; set; }

    [JsonPropertyName("processedAt")]
    public DateTime ProcessedAt { get; set; }

    [JsonPropertyName("completedAt")]
    public DateTime CompletedAt { get; set; }

    [JsonPropertyName("postedAt")]
    public DateTime PostedAt { get; set; }

    [JsonPropertyName("rejectedAt")]
    public DateTime RejectedAt { get; set; }

    [JsonPropertyName("original")]
    public OriginalResponseModel Original { get; set; } = null!;

    [JsonPropertyName("previous")]
    public PreviousResponseModel Previous { get; set; } = null!;

    [JsonPropertyName("accountNumber")]
    public string AccountNumber { get; set; } = null!;
}