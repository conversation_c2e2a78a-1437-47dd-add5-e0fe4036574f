﻿using BlueTape.Aion.Domain.DTOs.BankAccount;
using BlueTape.Aion.Domain.DTOs.Company;
using BlueTape.Integrations.Aion.Infrastructure.Enums;

namespace BlueTape.Aion.Application.Extensions;

public static class AionSettingsExtensions
{
    public static string? GetTransferMethodId(
        this BankAccountAionSettingsDto aionSettings,
        PaymentSubscriptionType paymentSubscription,
        AionPaymentMethodType accountType)
    {
        return (paymentSubscription, accountType) switch
        {
            (PaymentSubscriptionType.SUBSCRIPTION1, AionPaymentMethodType.ACH) => aionSettings.TransferMethodId,
            (PaymentSubscriptionType.SUBSCRIPTION2, AionPaymentMethodType.ACH) => aionSettings.TransferMethodId2,
            (PaymentSubscriptionType.SUBSCRIPTION3, AionPaymentMethodType.ACH) => aionSettings.TransferMethodId3,
            
            (PaymentSubscriptionType.SUBSCRIPTION1, AionPaymentMethodType.WIRE) => aionSettings.WireTransferMethodId,
            (PaymentSubscriptionType.SUBSCRIPTION2, AionPaymentMethodType.WIRE) => aionSettings.WireTransferMethodId2,
            (PaymentSubscriptionType.SUBSCRIPTION3, AionPaymentMethodType.WIRE) => aionSettings.WireTransferMethodId3,
            
            (PaymentSubscriptionType.SUBSCRIPTION1, AionPaymentMethodType.INSTANT) => aionSettings.InstantTransferMethodId,
            (PaymentSubscriptionType.SUBSCRIPTION2, AionPaymentMethodType.INSTANT) => aionSettings.InstantTransferMethodId2,
            (PaymentSubscriptionType.SUBSCRIPTION3, AionPaymentMethodType.INSTANT) => aionSettings.InstantTransferMethodId3,
            _ => string.Empty
        };
    }
            
    public static string GetCounterPartyId(this CompanyAionSettingsDto? aionSettings, PaymentSubscriptionType paymentSubscription)
    {
        return paymentSubscription switch
        {
            PaymentSubscriptionType.SUBSCRIPTION1 => aionSettings?.CounterPartyId ?? string.Empty,
            PaymentSubscriptionType.SUBSCRIPTION2 => aionSettings?.CounterPartyId2 ?? string.Empty,
            PaymentSubscriptionType.SUBSCRIPTION3 => aionSettings?.CounterPartyId3 ?? string.Empty,
            _ => string.Empty
        };
    }

    public static string GetCounterPartyObjectId(this CompanyAionSettingsDto? aionSettings, PaymentSubscriptionType paymentSubscription)
    {
        return paymentSubscription switch
        {
            PaymentSubscriptionType.SUBSCRIPTION1 => aionSettings?.CounterPartyObjectId ?? string.Empty,
            PaymentSubscriptionType.SUBSCRIPTION2 => aionSettings?.CounterPartyObjectId2 ?? string.Empty,
            PaymentSubscriptionType.SUBSCRIPTION3 => aionSettings?.CounterPartyObjectId3 ?? string.Empty,
            _ => string.Empty
        };
    }

    public static PaymentSubscriptionType? GetSubscriptionByAccountCode(AccountCodeType accountCode)
    {
        return accountCode switch
        {
            AccountCodeType.COLLECTION or AccountCodeType.REVENUE or AccountCodeType.FUNDING => PaymentSubscriptionType.SUBSCRIPTION1,
            AccountCodeType.COLLECTIONREPAYMENT or AccountCodeType.SPV_COLLECTION_AION or AccountCodeType.SPV_COLLECTION_ARCADIA
                or AccountCodeType.LOCKBOXCOLLECTION or AccountCodeType.DACACOLLECTION
                => PaymentSubscriptionType.SUBSCRIPTION2,
            AccountCodeType.SPV_COLLECTION_RAISTONE => PaymentSubscriptionType.SUBSCRIPTION3,
            _ => null
        };
    }
}
