const {google} = require('googleapis');
const authorize = require('./authorize');
const findMessageFromBluetap = require('./findMessageFromBluetap');
const {getLinkFromBase64} = require('./getLinkFromBase64');
const util = require("util");
const constants = JSON.parse(JSON.stringify(require('../../constants/constants.json')));

async function getInvitationLink(messageReceiver, messageSubject, messageCount = 10) {
    const auth = await authorize();
    const gmail = google.gmail({version: "v1", auth});
    const getListOfMessagesId = util.promisify(gmail.users.messages.list.bind(gmail));

    const listOfMessagesIdResponse = await getListOfMessagesId({
        userId: "me",
        maxResults: messageCount,
    });

    const listOfMessagesId = listOfMessagesIdResponse.data.messages;
    const getMessages = util.promisify(gmail.users.messages.get.bind(gmail));
    const message = await findMessageFromBluetap(
        getMessages,
        listOfMessagesId,
        messageReceiver,
        messageSubject,
    );

    let link = undefined;
    if(message?.data) {
        link = getLinkFromBase64(constants.linkTypes.invitationLink, message.data.payload.parts[1].body.data);
    };
    return link;
}

// TO call in tests
// check ../utils/waiters.ts on how to call this function properly

async function getVerificationLink(messageReceiver, messageSubject, messageCount = 10) {
    const auth = await authorize();
    const gmail = google.gmail({version: "v1", auth});
    const getListOfMessagesId = util.promisify(gmail.users.messages.list.bind(gmail));

    const listOfMessagesIdResponse = await getListOfMessagesId({
        userId: "me",
        maxResults: messageCount,
    });

    const listOfMessagesId = listOfMessagesIdResponse.data.messages;
    const getMessages = util.promisify(gmail.users.messages.get.bind(gmail));
    const message = await findMessageFromBluetap(
        getMessages,
        listOfMessagesId,
        messageReceiver,
        messageSubject,
    );

    let link = undefined;
    if(message?.data) {
        link = getLinkFromBase64(constants.linkTypes.verificationLink, message.data.payload.body.data);
    };
    return link;
}

module.exports = {getInvitationLink, getVerificationLink};