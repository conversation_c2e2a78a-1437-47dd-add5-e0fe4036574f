name: Execute payment flow

on:
  workflow_dispatch:
    inputs:
      customerEmail:
        description: 'Customer email'
        required: true
        type: string
      customerPassword:
        description: 'Customer password'
        required: true
        type: string
      invoiceNumber:
        description: 'Invoice number or id'
        required: true
        type: string
      environment:
        description: 'Environment'
        required: true
        default: 'beta'
        type: choice
        options:
          - beta
          - qa
          - dev

jobs:
  test:
    runs-on: ubuntu-latest
    container:
      image: mcr.microsoft.com/playwright:v1.44.1-jammy

    env:
      USER_COMPANYNAME: ${{ vars.USER_COMPANYNAME }}
      USER_CUSTOMEREMAIL: ${{ vars.USER_CUSTOMEREMAIL }}
      USER_CUSTOMERPASSWORD: ${{ vars.USER_CUSTOMERPASSWORD }}
      USER_CUSTOMERID: ${{ vars.USER_CUSTOMERID }}
      USER_CUSTOMER_COMPANYNAME: ${{ vars.USER_CUSTOMER_COMPANYNAME }}
      GENERIC_API_BASE_URL: ${{ vars.GENERIC_API_BASE_URL }}
      X_BLUETAPE_KEY: ${{ vars.X_BLUETAPE_KEY }}
      X_INTEGRATION_ACCOUNT_ID: ${{ vars.X_INTEGRATION_ACCOUNT_ID }}
      MONGODB_URI: ${{ vars.MONGODB_URI }}
      ACCESS_KEY_AWS: ${{ vars.ACCESS_KEY_AWS }}
      SECRET_ACCESS_KEY_AWS: ${{ vars.SECRET_ACCESS_KEY_AWS }}
      TestGroupId: ${{ github.run_id }}
      test_env: "${{ inputs.environment }}"
      PAY_NOW_BASE_URL: ${{ vars.PAY_NOW_BASE_URL }}
      TEST_PARAMS: >
        {
          "customerEmail": "${{ inputs.customerEmail }}",
          "customerPassword": "${{ inputs.customerPassword }}",
          "invoiceNumber": "${{ inputs.invoiceNumber }}",
          "environment": "${{ inputs.environment }}"
        }

    steps:
      - uses: actions/checkout@v4
      
      - uses: actions/setup-node@v4
        with:
          node-version: 18
          
      - name: Install dependencies
        run: npm ci --verbose
            
      - name: Run Playwright tests
        run: npx playwright test --grep "@makeCustomerPayment" --reporter=./sqs-reporter.js
        
      - uses: actions/upload-artifact@v4
        if: always()
        with:
          name: playwright-report
          path: playwright-report/
          retention-days: 7