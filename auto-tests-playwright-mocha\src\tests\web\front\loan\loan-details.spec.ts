import {test} from '../../../test-utils';
import {BaseAPI} from '../../../../api/base-api';
import {createCustomerInvoice} from '../../../../api/user/create-invoice';
import {payWithBluetapeCredit, callDecision} from '../../../../api/user';

test.use({storageState: {cookies: [], origins: []}});
const constants = JSON.parse(JSON.stringify(require('../../../../constants/LMStestData.json')));

test.describe('Loan tests', async () => {
    let userAuth: { session: any; challenge: any; };
    let secondUserAuth: { session: any; challenge: any; };
    let invoice: { id: string; };
    let loanRequest: { id: string; };

    test.beforeEach(async ({userIdToken, customerIdToken}) => {
        userAuth = await BaseAPI.getAuth(userIdToken);
        invoice = await createCustomerInvoice(userAuth.session, userAuth.challenge, process.env.USER_CUSTOMERID);
        secondUserAuth = await BaseAPI.getAuth(customerIdToken);
        loanRequest = await payWithBluetapeCredit(secondUserAuth.session, secondUserAuth.challenge, invoice.id);
        await callDecision(secondUserAuth.session, secondUserAuth.challenge, loanRequest.id);
    });

    test.afterEach(async ({customerPageManager}) => {
        await customerPageManager.page.close();
    });

    test.skip('Check Loan Details for user with Active Loans only @smoke', async ({customerPageManager}) => {
        await customerPageManager.sideMenu.openPaySubTab(customerPageManager.sideMenu.sideMenuSubTabs.pay.credit);
        await customerPageManager.page.waitForLoadState('networkidle'); //waiter here for request to load
        await customerPageManager.creditList.openFirstLoanDetails();
        // expect >> check Loan details
    });
});
