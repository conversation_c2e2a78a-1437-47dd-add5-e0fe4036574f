import {BasePage} from '../../../../base.page';

export class ReceivablesInvoiceDetailsModal extends BasePage {
    constructor(page){
        super(page);
    };

    containers = {
        invoiceDetailsContainer: this.page.locator('[class="css-1dbjc4n r-13awgt0 r-1777fci r-12vffkv"]'), // refactore this container
    };

    labels = {
        invoiceStatus: this.containers.invoiceDetailsContainer.locator('[data-testid="invoice_status_badge"]>div'),
        transactionNumber: this.containers.invoiceDetailsContainer.locator('[data-testid="Transaction number_item"] > .r-16dba41'),
    };

    buttons = {
        collectPayment: this.containers.invoiceDetailsContainer.locator('"Collect Payment"'),
        close: this.containers.invoiceDetailsContainer.locator('[data-testid="close_sidebar_button"]'),
    };
}