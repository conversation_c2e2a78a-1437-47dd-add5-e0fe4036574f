﻿using System;
using AutoFixture.Kernel;

namespace BlueTape.Application.Tests.AutoFixture.Builders
{
    internal class DateOnlyBuilder : ISpecimenBuilder
    {
        private const int YearLimitsDeviation = 1000;
        private const int FirstMonthInYearNumber = 1;
        private const int LastMonthInYearNumber = 12;

        public object Create(object request, ISpecimenContext context)
        {
            if (request is Type type && type == typeof(DateOnly))
            {
                var randomDate = GenerateRandomDate();

                return randomDate;
            }

            return new NoSpecimen();
        }

        private static DateOnly GenerateRandomDate()
        {
            var random = new Random();

            var year = random.Next(DateOnly.MinValue.Year + YearLimitsDeviation, DateOnly.MaxValue.Year - YearLimitsDeviation);
            var month = random.Next(FirstMonthInYearNumber, LastMonthInYearNumber);
            var day = random.Next(1, DateTime.DaysInMonth(year, month));

            return new DateOnly(year, month, day);
        }
    }
}