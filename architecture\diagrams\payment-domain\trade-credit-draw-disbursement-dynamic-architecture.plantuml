@startuml

title Trade Credit Components Draw Disbursement\n(not all components are shown)

participant "Draw Approval\n(type: regular)\n(OBS)" as obs #LightGray
queue "Draw Issue Queue" as diqueue #LightSalmon
participant "LMS" as lms #LightGray
queue "Payment Operations" as opsqs #LightSalmon
participant "Payment\nService" as pserv #SkyBlue
participant "CBW" as cbw #Orange

autonumber

== Draw disbursement ==

obs -> diqueue : Place\n""CREATE.DRAW.ISSUE""
diqueue -> lms : Read draw operations
lms -> opsqs : Place\n""CREATE.DRAW.DISBURSEMENT""
opsqs -> pserv : Read operations queue\ndisburse to merchant
pserv -> cbw : ACH

@enduml