{"profiles": {"BlueTape.Functions.Aion.TransactionStatusReport DEV": {"commandName": "Project", "commandLineArgs": "--port 7257", "launchBrowser": false, "environmentVariables": {"ASPNETCORE_ENVIRONMENT": "dev", "KEYVAULT_URI": "https://keyvault-dev-17b195c92e.vault.azure.net/", "AWS_REGION": "us-west-1", "LP_AWS_ACCOUNT": "************"}}, "BlueTape.Functions.Aion.TransactionStatusReport QA": {"commandName": "Project", "commandLineArgs": "--port 7257", "launchBrowser": false, "environmentVariables": {"ASPNETCORE_ENVIRONMENT": "qa", "KEYVAULT_URI": "https://keyvault-qa-b2c7f314826.vault.azure.net/", "AWS_REGION": "us-west-1", "LP_AWS_ACCOUNT": "************"}}}}