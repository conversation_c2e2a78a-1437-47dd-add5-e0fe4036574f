openapi: '3.0.0'
info:
  version: '0.0.1'
  title: 'Document Authorization Service API'
  description: | 
    API definition of private service for handling document versions' approvals.
servers:
- url: TBD-Dev
  description: Development server
- url: TBD-QA
  description: QA
- url: TBD-Prod
  description: Production server
paths:
  /documents/{id}/approvals:
    get:
      tags:
        - approvals
      summary: Gets a specific document's approvals
      description: Gets a specific document's approvals
      operationId: getDocumentApprovals
      parameters:
        - name: id
          description: The id of document
          in: path
          required: true
          schema:
            type: string
      responses:
        200:
          description: List of document approvals
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/DocumentApproval'
        400:
          description: Invalid request
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        404:
          description: Document not found
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
    post:
      tags:
        - approvals
      summary: Adds new member to approve a document.
      description: Adds new member to approve a document.
      operationId: createDocumentApproval
      parameters:
        - name: id
          description: The id of document
          in: path
          required: true
          schema:
            type: string
        - name: alreadyApproved
          description: Is the approval already made (only true is applicable)
          in: query
          required: false
          schema:
            type: boolean
        - name: X-User-Id
          in: header
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/CreateApprovalRequest"
      responses:
        200:
          description: The token
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/CreateApprovalResponse'
        400:
          description: Owner already approved, does not need again.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        404:
          description: Document not found.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
  /documents/approvals/authorize:
    post:
      tags:
        - approvals
      summary: (AllowAnonymous) Authorizes a document by token
      description: (AllowAnonymous) Authorizes a document by token
      operationId: authorizeDocumentAuthorizationByToken
      requestBody:
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/ApproveRequest"
      responses:
        200:
          description: The approved document.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/DocumentApproval'
        400:
          description: Invalid request, invalid token format
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        401:
          description: Unauthorized, token expired
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        404:
          description: Token not found
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
components:
  schemas:
    DocumentApproval:
      type: object
      properties:
        id:
          type: string
        createdAt:
          type: string
          format: date-time
          description: Time of creation
          example: 2014-04-05T12:59:59.000Z
        createdBy:
          type: string
        updatedAt:
          type: string
          format: date-time
          description: Time of last update
          example: 2014-04-05T12:59:59.000Z
        documentId:
          type: string
        expiresAt:
          type: string
          format: date-time
          description: Time of token expiration, redundantly
          example: 2014-04-05T12:59:59.000Z
        isExpired:
          type: boolean
          description: Calculated field, is token / link expired?
        ownerIdentifier:
          type: string
        emailAddress:
          type: string
        userId:
          type: string
          nullable: true
          description: The user's id. Can be null, if not registered.
        firstName:
          type: string
        lastName:
          type: string
        ipAddress:
          type: string
          nullable: true
          description: The approver's ip address. Null, if not approved.
        approvedAt:
          type: string
          format: date-time
          nullable: true
          description: Approve time. Null, if not approved.
          example: 2014-04-05T12:59:59.000Z
        isApproved:
          type: boolean
          description: Calculated field, is document approved?
    CreateApprovalRequest:
      type: object
      properties:
        ownerIdentifier:
          type: string
        emailAddress:
          type: string
        userId:
          type: string
          nullable: true
          description: The user's id. Can be null, if not registered.
        firstName:
          type: string
        lastName:
          type: string
        ipAddress:
          type: string
          nullable: true
          description: Only applicable, when already approved equals true.
    ApproveRequest:
      type: object
      properties:
        token:
          type: string
        ipAddress:
          type: string
          nullable: true
          description: The approver's ip address. Null, if not approved.
    CreateApprovalResponse:
      type: object
      properties:
        token:
          type: string
    ErrorResponse:
      type: object
      required:
        - statusCode
        - message
        - errorDescription
      properties:
        statusCode:
          description: HTTP status code corresponding to the error.
          type: integer
          example: 400
        message:
          description: A human readable error message.
          type: string
          example: Invalid model.
        errorDescription:
          description: Detailed information.
          type: string
  securitySchemes:
    ApiKey:
      type: apiKey
      in: header
      name: X-Api-Key
security:
  - ApiKey: []