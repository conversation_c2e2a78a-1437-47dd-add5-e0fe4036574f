{"runtimeTarget": {"name": ".NETCoreApp,Version=v8.0", "signature": ""}, "compilationOptions": {}, "targets": {".NETCoreApp,Version=v8.0": {"BlueTape.DataAccess.EF/1.0.0": {"dependencies": {"AutoMapper": "12.0.0", "AutoMapper.Extensions.Microsoft.DependencyInjection": "12.0.0", "BlueTape.CompanyService": "1.3.3", "BlueTape.Domain": "1.0.0", "BlueTape.Utilities": "1.4.5", "Microsoft.AspNetCore.Http.Abstractions": "2.2.0", "Microsoft.Data.SqlClient": "5.1.5", "Microsoft.EntityFrameworkCore": "7.0.4", "Microsoft.EntityFrameworkCore.InMemory": "7.0.4", "Microsoft.EntityFrameworkCore.Relational": "7.0.4", "Microsoft.EntityFrameworkCore.Tools": "7.0.4", "Npgsql.EntityFrameworkCore.PostgreSQL": "7.0.3", "TinyHelpers.EntityFrameworkCore": "2.0.14"}, "runtime": {"BlueTape.DataAccess.EF.dll": {}}}, "AutoMapper/12.0.0": {"dependencies": {"Microsoft.CSharp": "4.7.0"}, "runtime": {"lib/netstandard2.1/AutoMapper.dll": {"assemblyVersion": "1*******", "fileVersion": "1*******"}}}, "AutoMapper.Extensions.Microsoft.DependencyInjection/12.0.0": {"dependencies": {"AutoMapper": "12.0.0", "Microsoft.Extensions.Options": "8.0.1"}, "runtime": {"lib/netstandard2.1/AutoMapper.Extensions.Microsoft.DependencyInjection.dll": {"assemblyVersion": "1*******", "fileVersion": "1*******"}}}, "AWSSDK.Core/3.7.302.6": {"runtime": {"lib/net8.0/AWSSDK.Core.dll": {"assemblyVersion": "*******", "fileVersion": "3.7.302.6"}}}, "AWSSDK.KeyManagementService/3.7.300.46": {"dependencies": {"AWSSDK.Core": "3.7.302.6"}, "runtime": {"lib/net8.0/AWSSDK.KeyManagementService.dll": {"assemblyVersion": "*******", "fileVersion": "3.7.300.46"}}}, "AWSSDK.SecretsManager/3.7.302.21": {"dependencies": {"AWSSDK.Core": "3.7.302.6"}, "runtime": {"lib/net8.0/AWSSDK.SecretsManager.dll": {"assemblyVersion": "*******", "fileVersion": "3.7.302.21"}}}, "AWSSDK.SecretsManager.Caching/1.0.6": {"dependencies": {"AWSSDK.SecretsManager": "3.7.302.21", "Microsoft.Extensions.Caching.Memory": "7.0.0"}, "runtime": {"lib/netstandard2.0/Amazon.SecretsManager.Extensions.Caching.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "AWSSDK.SecurityToken/3.7.300.47": {"dependencies": {"AWSSDK.Core": "3.7.302.6"}, "runtime": {"lib/net8.0/AWSSDK.SecurityToken.dll": {"assemblyVersion": "*******", "fileVersion": "3.7.300.47"}}}, "Azure.Core/1.36.0": {"dependencies": {"Microsoft.Bcl.AsyncInterfaces": "1.1.1", "System.Diagnostics.DiagnosticSource": "6.0.1", "System.Memory.Data": "1.0.2", "System.Numerics.Vectors": "4.5.0", "System.Text.Encodings.Web": "7.0.0", "System.Text.Json": "7.0.2", "System.Threading.Tasks.Extensions": "4.5.4"}, "runtime": {"lib/net6.0/Azure.Core.dll": {"assemblyVersion": "1.36.0.0", "fileVersion": "1.3600.23.56006"}}}, "Azure.Core.Amqp/1.3.0": {"dependencies": {"Microsoft.Azure.Amqp": "2.6.4", "System.Memory": "4.5.5", "System.Memory.Data": "1.0.2"}, "runtime": {"lib/netstandard2.0/Azure.Core.Amqp.dll": {"assemblyVersion": "1.3.0.0", "fileVersion": "1.300.23.15207"}}}, "Azure.Identity/1.10.4": {"dependencies": {"Azure.Core": "1.36.0", "Microsoft.Identity.Client": "4.56.0", "Microsoft.Identity.Client.Extensions.Msal": "4.56.0", "System.Memory": "4.5.5", "System.Security.Cryptography.ProtectedData": "7.0.0", "System.Text.Json": "7.0.2", "System.Threading.Tasks.Extensions": "4.5.4"}, "runtime": {"lib/netstandard2.0/Azure.Identity.dll": {"assemblyVersion": "1.10.4.0", "fileVersion": "1.1000.423.56303"}}}, "Azure.Messaging.ServiceBus/7.17.1": {"dependencies": {"Azure.Core": "1.36.0", "Azure.Core.Amqp": "1.3.0", "Microsoft.Azure.Amqp": "2.6.4", "Microsoft.Bcl.AsyncInterfaces": "1.1.1", "System.Memory.Data": "1.0.2"}, "runtime": {"lib/netstandard2.0/Azure.Messaging.ServiceBus.dll": {"assemblyVersion": "7.17.1.0", "fileVersion": "7.1700.123.60403"}}}, "Azure.Security.KeyVault.Keys/4.5.0": {"dependencies": {"Azure.Core": "1.36.0", "System.Memory": "4.5.5", "System.Text.Json": "7.0.2", "System.Threading.Tasks.Extensions": "4.5.4"}, "runtime": {"lib/netstandard2.0/Azure.Security.KeyVault.Keys.dll": {"assemblyVersion": "4.5.0.0", "fileVersion": "4.500.23.16403"}}}, "Azure.Security.KeyVault.Secrets/4.5.0": {"dependencies": {"Azure.Core": "1.36.0", "System.Memory": "4.5.5", "System.Text.Json": "7.0.2", "System.Threading.Tasks.Extensions": "4.5.4"}, "runtime": {"lib/netstandard2.0/Azure.Security.KeyVault.Secrets.dll": {"assemblyVersion": "4.5.0.0", "fileVersion": "4.500.23.16403"}}}, "BlueTape.AzureKeyVault/1.0.3": {"dependencies": {"Azure.Identity": "1.10.4", "Azure.Security.KeyVault.Keys": "4.5.0", "Azure.Security.KeyVault.Secrets": "4.5.0", "BlueTape.Utilities": "1.4.5", "Microsoft.Extensions.Logging.Abstractions": "8.0.0"}, "runtime": {"lib/net6.0/BlueTape.AzureKeyVault.dll": {"assemblyVersion": "1.0.3.0", "fileVersion": "1.0.3.0"}}}, "BlueTape.Common.Extensions/1.1.0": {"runtime": {"lib/net6.0/BlueTape.Common.Extensions.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "BlueTape.CompanyService/1.3.3": {"dependencies": {"BlueTape.CompanyService.Common": "1.1.21", "Newtonsoft.Json": "13.0.1"}, "runtime": {"lib/net6.0/BlueTape.CompanyService.dll": {"assemblyVersion": "1.3.3.0", "fileVersion": "1.3.3.0"}}}, "BlueTape.CompanyService.Common/1.1.21": {"dependencies": {"BlueTape.LS": "1.1.68", "BlueTape.ServiceBusMessaging": "1.0.8"}, "runtime": {"lib/net6.0/BlueTape.CompanyService.Common.dll": {"assemblyVersion": "1.1.21.0", "fileVersion": "1.1.21.0"}}}, "BlueTape.LS/1.1.68": {"dependencies": {"BlueTape.LS.Domain": "1.1.32"}, "runtime": {"lib/net6.0/BlueTape.LS.dll": {"assemblyVersion": "1.1.68.0", "fileVersion": "1.1.68.0"}}}, "BlueTape.LS.Domain/1.1.32": {"dependencies": {"BlueTape.Utilities": "1.4.5"}, "runtime": {"lib/net6.0/BlueTape.LS.Domain.dll": {"assemblyVersion": "1.1.32.0", "fileVersion": "1.1.32.0"}}}, "BlueTape.OBS/1.6.45": {"runtime": {"lib/net6.0/BlueTape.OBS.dll": {"assemblyVersion": "1.6.45.0", "fileVersion": "1.6.45.0"}}}, "BlueTape.ServiceBusMessaging/1.0.8": {"dependencies": {"Azure.Messaging.ServiceBus": "7.17.1", "BlueTape.AzureKeyVault": "1.0.3"}, "runtime": {"lib/net6.0/BlueTape.ServiceBusMessaging.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "BlueTape.Utilities/1.4.5": {"dependencies": {"AWSSDK.KeyManagementService": "3.7.300.46", "AWSSDK.SecretsManager": "3.7.302.21", "AWSSDK.SecretsManager.Caching": "1.0.6", "AWSSDK.SecurityToken": "3.7.300.47", "BlueTape.Common.Extensions": "1.1.0", "BlueTape.OBS": "1.6.45", "Microsoft.AspNetCore.Http.Abstractions": "2.2.0", "Microsoft.Extensions.DependencyInjection.Abstractions": "8.0.0", "Microsoft.Extensions.Hosting.Abstractions": "6.0.0", "Microsoft.Extensions.Http.Polly": "6.0.9", "Microsoft.Extensions.Options": "8.0.1", "MongoDB.Bson": "2.25.0", "Polly": "7.2.3", "Serilog": "2.12.0", "Serilog.AspNetCore": "6.0.1", "Serilog.Enrichers.GlobalLogContext": "2.1.0", "Serilog.Sinks.Console": "4.1.0", "Serilog.Sinks.Logz.Io": "7.1.0"}, "runtime": {"lib/net6.0/BlueTape.Utilities.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Elastic.CommonSchema/1.5.3": {"dependencies": {"System.Text.Json": "7.0.2"}, "runtime": {"lib/netstandard2.1/Elastic.CommonSchema.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Elastic.CommonSchema.Serilog/1.5.3": {"dependencies": {"Elastic.CommonSchema": "1.5.3", "Microsoft.AspNetCore.Http.Abstractions": "2.2.0", "Serilog": "2.12.0"}, "runtime": {"lib/netstandard2.1/Elastic.CommonSchema.Serilog.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Humanizer.Core/2.14.1": {"runtime": {"lib/net6.0/Humanizer.dll": {"assemblyVersion": "2.14.0.0", "fileVersion": "2.14.1.48190"}}}, "Microsoft.AspNetCore.Http.Abstractions/2.2.0": {"dependencies": {"Microsoft.AspNetCore.Http.Features": "2.2.0", "System.Text.Encodings.Web": "7.0.0"}}, "Microsoft.AspNetCore.Http.Features/2.2.0": {"dependencies": {"Microsoft.Extensions.Primitives": "8.0.0"}}, "Microsoft.Azure.Amqp/2.6.4": {"runtime": {"lib/netstandard2.0/Microsoft.Azure.Amqp.dll": {"assemblyVersion": "2.4.0.0", "fileVersion": "2.6.4.0"}}}, "Microsoft.Bcl.AsyncInterfaces/1.1.1": {"runtime": {"lib/netstandard2.1/Microsoft.Bcl.AsyncInterfaces.dll": {"assemblyVersion": "*******", "fileVersion": "4.700.20.21406"}}}, "Microsoft.CSharp/4.7.0": {}, "Microsoft.Data.SqlClient/5.1.5": {"dependencies": {"Azure.Identity": "1.10.4", "Microsoft.Data.SqlClient.SNI.runtime": "5.1.1", "Microsoft.Identity.Client": "4.56.0", "Microsoft.IdentityModel.JsonWebTokens": "6.35.0", "Microsoft.IdentityModel.Protocols.OpenIdConnect": "6.35.0", "Microsoft.SqlServer.Server": "1.0.0", "System.Configuration.ConfigurationManager": "7.0.0", "System.Diagnostics.DiagnosticSource": "6.0.1", "System.Runtime.Caching": "6.0.0", "System.Security.Cryptography.Cng": "5.0.0", "System.Security.Principal.Windows": "5.0.0", "System.Text.Encoding.CodePages": "6.0.0", "System.Text.Encodings.Web": "7.0.0"}, "runtime": {"lib/net6.0/Microsoft.Data.SqlClient.dll": {"assemblyVersion": "*******", "fileVersion": "5.15.24027.2"}}, "runtimeTargets": {"runtimes/unix/lib/net6.0/Microsoft.Data.SqlClient.dll": {"rid": "unix", "assetType": "runtime", "assemblyVersion": "*******", "fileVersion": "5.15.24027.2"}, "runtimes/win/lib/net6.0/Microsoft.Data.SqlClient.dll": {"rid": "win", "assetType": "runtime", "assemblyVersion": "*******", "fileVersion": "5.15.24027.2"}}}, "Microsoft.Data.SqlClient.SNI.runtime/5.1.1": {"runtimeTargets": {"runtimes/win-arm/native/Microsoft.Data.SqlClient.SNI.dll": {"rid": "win-arm", "assetType": "native", "fileVersion": "*******"}, "runtimes/win-arm64/native/Microsoft.Data.SqlClient.SNI.dll": {"rid": "win-arm64", "assetType": "native", "fileVersion": "*******"}, "runtimes/win-x64/native/Microsoft.Data.SqlClient.SNI.dll": {"rid": "win-x64", "assetType": "native", "fileVersion": "*******"}, "runtimes/win-x86/native/Microsoft.Data.SqlClient.SNI.dll": {"rid": "win-x86", "assetType": "native", "fileVersion": "*******"}}}, "Microsoft.EntityFrameworkCore/7.0.4": {"dependencies": {"Microsoft.EntityFrameworkCore.Abstractions": "7.0.4", "Microsoft.EntityFrameworkCore.Analyzers": "7.0.4", "Microsoft.Extensions.Caching.Memory": "7.0.0", "Microsoft.Extensions.DependencyInjection": "7.0.0", "Microsoft.Extensions.Logging": "7.0.0"}, "runtime": {"lib/net6.0/Microsoft.EntityFrameworkCore.dll": {"assemblyVersion": "7.0.4.0", "fileVersion": "7.0.423.11504"}}}, "Microsoft.EntityFrameworkCore.Abstractions/7.0.4": {"runtime": {"lib/net6.0/Microsoft.EntityFrameworkCore.Abstractions.dll": {"assemblyVersion": "7.0.4.0", "fileVersion": "7.0.423.11504"}}}, "Microsoft.EntityFrameworkCore.Analyzers/7.0.4": {}, "Microsoft.EntityFrameworkCore.Design/7.0.4": {"dependencies": {"Humanizer.Core": "2.14.1", "Microsoft.EntityFrameworkCore.Relational": "7.0.4", "Microsoft.Extensions.DependencyModel": "7.0.0", "Mono.TextTemplating": "2.2.1"}, "runtime": {"lib/net6.0/Microsoft.EntityFrameworkCore.Design.dll": {"assemblyVersion": "7.0.4.0", "fileVersion": "7.0.423.11504"}}}, "Microsoft.EntityFrameworkCore.InMemory/7.0.4": {"dependencies": {"Microsoft.EntityFrameworkCore": "7.0.4"}, "runtime": {"lib/net6.0/Microsoft.EntityFrameworkCore.InMemory.dll": {"assemblyVersion": "7.0.4.0", "fileVersion": "7.0.423.11504"}}}, "Microsoft.EntityFrameworkCore.Relational/7.0.4": {"dependencies": {"Microsoft.EntityFrameworkCore": "7.0.4", "Microsoft.Extensions.Configuration.Abstractions": "7.0.0"}, "runtime": {"lib/net6.0/Microsoft.EntityFrameworkCore.Relational.dll": {"assemblyVersion": "7.0.4.0", "fileVersion": "7.0.423.11504"}}}, "Microsoft.EntityFrameworkCore.Tools/7.0.4": {"dependencies": {"Microsoft.EntityFrameworkCore.Design": "7.0.4"}}, "Microsoft.Extensions.Caching.Abstractions/7.0.0": {"dependencies": {"Microsoft.Extensions.Primitives": "8.0.0"}}, "Microsoft.Extensions.Caching.Memory/7.0.0": {"dependencies": {"Microsoft.Extensions.Caching.Abstractions": "7.0.0", "Microsoft.Extensions.DependencyInjection.Abstractions": "8.0.0", "Microsoft.Extensions.Logging.Abstractions": "8.0.0", "Microsoft.Extensions.Options": "8.0.1", "Microsoft.Extensions.Primitives": "8.0.0"}}, "Microsoft.Extensions.Configuration/2.0.0": {"dependencies": {"Microsoft.Extensions.Configuration.Abstractions": "7.0.0"}}, "Microsoft.Extensions.Configuration.Abstractions/7.0.0": {"dependencies": {"Microsoft.Extensions.Primitives": "8.0.0"}}, "Microsoft.Extensions.Configuration.Binder/2.0.0": {"dependencies": {"Microsoft.Extensions.Configuration": "2.0.0"}}, "Microsoft.Extensions.DependencyInjection/7.0.0": {"dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "8.0.0"}}, "Microsoft.Extensions.DependencyInjection.Abstractions/8.0.0": {}, "Microsoft.Extensions.DependencyModel/7.0.0": {"dependencies": {"System.Text.Encodings.Web": "7.0.0", "System.Text.Json": "7.0.2"}, "runtime": {"lib/net7.0/Microsoft.Extensions.DependencyModel.dll": {"assemblyVersion": "*******", "fileVersion": "7.0.22.51805"}}}, "Microsoft.Extensions.FileProviders.Abstractions/6.0.0": {"dependencies": {"Microsoft.Extensions.Primitives": "8.0.0"}}, "Microsoft.Extensions.Hosting.Abstractions/6.0.0": {"dependencies": {"Microsoft.Extensions.Configuration.Abstractions": "7.0.0", "Microsoft.Extensions.DependencyInjection.Abstractions": "8.0.0", "Microsoft.Extensions.FileProviders.Abstractions": "6.0.0"}}, "Microsoft.Extensions.Http/6.0.0": {"dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "8.0.0", "Microsoft.Extensions.Logging": "7.0.0", "Microsoft.Extensions.Logging.Abstractions": "8.0.0", "Microsoft.Extensions.Options": "8.0.1"}}, "Microsoft.Extensions.Http.Polly/6.0.9": {"dependencies": {"Microsoft.Extensions.Http": "6.0.0", "Polly": "7.2.3", "Polly.Extensions.Http": "3.0.0"}, "runtime": {"lib/netstandard2.0/Microsoft.Extensions.Http.Polly.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.922.41926"}}}, "Microsoft.Extensions.Logging/7.0.0": {"dependencies": {"Microsoft.Extensions.DependencyInjection": "7.0.0", "Microsoft.Extensions.DependencyInjection.Abstractions": "8.0.0", "Microsoft.Extensions.Logging.Abstractions": "8.0.0", "Microsoft.Extensions.Options": "8.0.1"}}, "Microsoft.Extensions.Logging.Abstractions/8.0.0": {"dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "8.0.0"}}, "Microsoft.Extensions.Options/8.0.1": {"dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "8.0.0", "Microsoft.Extensions.Primitives": "8.0.0"}, "runtime": {"lib/net8.0/Microsoft.Extensions.Options.dll": {"assemblyVersion": "8.0.0.0", "fileVersion": "8.0.123.58001"}}}, "Microsoft.Extensions.Options.ConfigurationExtensions/2.0.0": {"dependencies": {"Microsoft.Extensions.Configuration.Abstractions": "7.0.0", "Microsoft.Extensions.Configuration.Binder": "2.0.0", "Microsoft.Extensions.DependencyInjection.Abstractions": "8.0.0", "Microsoft.Extensions.Options": "8.0.1"}}, "Microsoft.Extensions.Primitives/8.0.0": {}, "Microsoft.Identity.Client/4.56.0": {"dependencies": {"Microsoft.IdentityModel.Abstractions": "6.35.0"}, "runtime": {"lib/net6.0/Microsoft.Identity.Client.dll": {"assemblyVersion": "4.56.0.0", "fileVersion": "4.56.0.0"}}}, "Microsoft.Identity.Client.Extensions.Msal/4.56.0": {"dependencies": {"Microsoft.Identity.Client": "4.56.0", "System.IO.FileSystem.AccessControl": "5.0.0", "System.Security.Cryptography.ProtectedData": "7.0.0"}, "runtime": {"lib/netstandard2.0/Microsoft.Identity.Client.Extensions.Msal.dll": {"assemblyVersion": "4.56.0.0", "fileVersion": "4.56.0.0"}}}, "Microsoft.IdentityModel.Abstractions/6.35.0": {"runtime": {"lib/net6.0/Microsoft.IdentityModel.Abstractions.dll": {"assemblyVersion": "6.35.0.0", "fileVersion": "6.35.0.41201"}}}, "Microsoft.IdentityModel.JsonWebTokens/6.35.0": {"dependencies": {"Microsoft.IdentityModel.Tokens": "6.35.0", "System.Text.Encoding": "4.3.0", "System.Text.Encodings.Web": "7.0.0", "System.Text.Json": "7.0.2"}, "runtime": {"lib/net6.0/Microsoft.IdentityModel.JsonWebTokens.dll": {"assemblyVersion": "6.35.0.0", "fileVersion": "6.35.0.41201"}}}, "Microsoft.IdentityModel.Logging/6.35.0": {"dependencies": {"Microsoft.IdentityModel.Abstractions": "6.35.0"}, "runtime": {"lib/net6.0/Microsoft.IdentityModel.Logging.dll": {"assemblyVersion": "6.35.0.0", "fileVersion": "6.35.0.41201"}}}, "Microsoft.IdentityModel.Protocols/6.35.0": {"dependencies": {"Microsoft.IdentityModel.Logging": "6.35.0", "Microsoft.IdentityModel.Tokens": "6.35.0"}, "runtime": {"lib/net6.0/Microsoft.IdentityModel.Protocols.dll": {"assemblyVersion": "6.35.0.0", "fileVersion": "6.35.0.41201"}}}, "Microsoft.IdentityModel.Protocols.OpenIdConnect/6.35.0": {"dependencies": {"Microsoft.IdentityModel.Protocols": "6.35.0", "System.IdentityModel.Tokens.Jwt": "6.35.0"}, "runtime": {"lib/net6.0/Microsoft.IdentityModel.Protocols.OpenIdConnect.dll": {"assemblyVersion": "6.35.0.0", "fileVersion": "6.35.0.41201"}}}, "Microsoft.IdentityModel.Tokens/6.35.0": {"dependencies": {"Microsoft.CSharp": "4.7.0", "Microsoft.IdentityModel.Logging": "6.35.0", "System.Security.Cryptography.Cng": "5.0.0"}, "runtime": {"lib/net6.0/Microsoft.IdentityModel.Tokens.dll": {"assemblyVersion": "6.35.0.0", "fileVersion": "6.35.0.41201"}}}, "Microsoft.NETCore.Platforms/5.0.0": {}, "Microsoft.NETCore.Targets/1.1.0": {}, "Microsoft.SqlServer.Server/1.0.0": {"runtime": {"lib/netstandard2.0/Microsoft.SqlServer.Server.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Microsoft.Win32.SystemEvents/7.0.0": {"runtime": {"lib/net7.0/Microsoft.Win32.SystemEvents.dll": {"assemblyVersion": "*******", "fileVersion": "7.0.22.51805"}}, "runtimeTargets": {"runtimes/win/lib/net7.0/Microsoft.Win32.SystemEvents.dll": {"rid": "win", "assetType": "runtime", "assemblyVersion": "*******", "fileVersion": "7.0.22.51805"}}}, "MongoDB.Bson/2.25.0": {"dependencies": {"System.Memory": "4.5.5", "System.Runtime.CompilerServices.Unsafe": "6.0.0"}, "runtime": {"lib/netstandard2.1/MongoDB.Bson.dll": {"assemblyVersion": "2.25.0.0", "fileVersion": "2.25.0.0"}}}, "Mono.TextTemplating/2.2.1": {"dependencies": {"System.CodeDom": "4.4.0"}, "runtime": {"lib/netstandard2.0/Mono.TextTemplating.dll": {"assemblyVersion": "2.2.0.0", "fileVersion": "2.2.1.1"}}}, "Newtonsoft.Json/13.0.1": {"runtime": {"lib/netstandard2.0/Newtonsoft.Json.dll": {"assemblyVersion": "1*******", "fileVersion": "13.0.1.25517"}}}, "Npgsql/7.0.2": {"dependencies": {"Microsoft.Extensions.Logging.Abstractions": "8.0.0"}, "runtime": {"lib/net7.0/Npgsql.dll": {"assemblyVersion": "7.0.2.0", "fileVersion": "7.0.2.0"}}}, "Npgsql.EntityFrameworkCore.PostgreSQL/7.0.3": {"dependencies": {"Microsoft.EntityFrameworkCore": "7.0.4", "Microsoft.EntityFrameworkCore.Abstractions": "7.0.4", "Microsoft.EntityFrameworkCore.Relational": "7.0.4", "Npgsql": "7.0.2"}, "runtime": {"lib/net7.0/Npgsql.EntityFrameworkCore.PostgreSQL.dll": {"assemblyVersion": "7.0.3.0", "fileVersion": "7.0.3.0"}}}, "Polly/7.2.3": {"runtime": {"lib/netstandard2.0/Polly.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Polly.Extensions.Http/3.0.0": {"dependencies": {"Polly": "7.2.3"}, "runtime": {"lib/netstandard2.0/Polly.Extensions.Http.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Serilog/2.12.0": {"runtime": {"lib/net6.0/Serilog.dll": {"assemblyVersion": "*******", "fileVersion": "********"}}}, "Serilog.AspNetCore/6.0.1": {"dependencies": {"Microsoft.Extensions.DependencyInjection": "7.0.0", "Microsoft.Extensions.Logging": "7.0.0", "Serilog": "2.12.0", "Serilog.Extensions.Hosting": "5.0.1", "Serilog.Formatting.Compact": "1.1.0", "Serilog.Settings.Configuration": "3.3.0", "Serilog.Sinks.Console": "4.1.0", "Serilog.Sinks.Debug": "2.0.0", "Serilog.Sinks.File": "5.0.0"}, "runtime": {"lib/net5.0/Serilog.AspNetCore.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Serilog.Enrichers.GlobalLogContext/2.1.0": {"dependencies": {"Serilog": "2.12.0"}, "runtime": {"lib/net5.0/Serilog.Enrichers.GlobalLogContext.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Serilog.Extensions.Hosting/5.0.1": {"dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "8.0.0", "Microsoft.Extensions.Hosting.Abstractions": "6.0.0", "Microsoft.Extensions.Logging.Abstractions": "8.0.0", "Serilog": "2.12.0", "Serilog.Extensions.Logging": "3.1.0"}, "runtime": {"lib/netstandard2.1/Serilog.Extensions.Hosting.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Serilog.Extensions.Logging/3.1.0": {"dependencies": {"Microsoft.Extensions.Logging": "7.0.0", "Serilog": "2.12.0"}, "runtime": {"lib/netstandard2.0/Serilog.Extensions.Logging.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Serilog.Formatting.Compact/1.1.0": {"dependencies": {"Serilog": "2.12.0"}, "runtime": {"lib/netstandard2.0/Serilog.Formatting.Compact.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Serilog.Settings.Configuration/3.3.0": {"dependencies": {"Microsoft.Extensions.DependencyModel": "7.0.0", "Microsoft.Extensions.Options.ConfigurationExtensions": "2.0.0", "Serilog": "2.12.0"}, "runtime": {"lib/netstandard2.0/Serilog.Settings.Configuration.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Serilog.Sinks.Console/4.1.0": {"dependencies": {"Serilog": "2.12.0"}, "runtime": {"lib/net5.0/Serilog.Sinks.Console.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Serilog.Sinks.Debug/2.0.0": {"dependencies": {"Serilog": "2.12.0"}, "runtime": {"lib/netstandard2.1/Serilog.Sinks.Debug.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Serilog.Sinks.File/5.0.0": {"dependencies": {"Serilog": "2.12.0"}, "runtime": {"lib/net5.0/Serilog.Sinks.File.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Serilog.Sinks.Http/8.0.0": {"dependencies": {"Microsoft.Extensions.Configuration.Abstractions": "7.0.0", "Serilog.Sinks.File": "5.0.0"}, "runtime": {"lib/netstandard2.1/Serilog.Sinks.Http.dll": {"assemblyVersion": "8.0.0.0", "fileVersion": "8.0.0.0"}}}, "Serilog.Sinks.Logz.Io/7.1.0": {"dependencies": {"Elastic.CommonSchema.Serilog": "1.5.3", "Newtonsoft.Json": "13.0.1", "Serilog.Sinks.Http": "8.0.0", "Serilog.Sinks.PeriodicBatching": "3.1.0"}, "runtime": {"lib/net6.0/Serilog.Sinks.Logz.Io.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Serilog.Sinks.PeriodicBatching/3.1.0": {"dependencies": {"Serilog": "2.12.0"}, "runtime": {"lib/netstandard2.1/Serilog.Sinks.PeriodicBatching.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "System.CodeDom/4.4.0": {"runtime": {"lib/netstandard2.0/System.CodeDom.dll": {"assemblyVersion": "4.0.0.0", "fileVersion": "4.6.25519.3"}}}, "System.Collections/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "5.0.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Runtime": "4.3.0"}}, "System.Collections.Concurrent/4.3.0": {"dependencies": {"System.Collections": "4.3.0", "System.Diagnostics.Debug": "4.3.0", "System.Diagnostics.Tracing": "4.3.0", "System.Globalization": "4.3.0", "System.Reflection": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0", "System.Runtime.Extensions": "4.3.0", "System.Threading": "4.3.0", "System.Threading.Tasks": "4.3.0"}}, "System.Collections.NonGeneric/4.3.0": {"dependencies": {"System.Diagnostics.Debug": "4.3.0", "System.Globalization": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0", "System.Runtime.Extensions": "4.3.0", "System.Threading": "4.3.0"}}, "System.Configuration.ConfigurationManager/7.0.0": {"dependencies": {"System.Diagnostics.EventLog": "7.0.0", "System.Security.Cryptography.ProtectedData": "7.0.0", "System.Security.Permissions": "7.0.0"}, "runtime": {"lib/net7.0/System.Configuration.ConfigurationManager.dll": {"assemblyVersion": "*******", "fileVersion": "7.0.22.51805"}}}, "System.Data.Common/4.3.0": {"dependencies": {"System.Collections": "4.3.0", "System.Globalization": "4.3.0", "System.IO": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0", "System.Runtime.Extensions": "4.3.0", "System.Text.RegularExpressions": "4.3.0", "System.Threading.Tasks": "4.3.0"}}, "System.Diagnostics.Debug/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "5.0.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Runtime": "4.3.0"}}, "System.Diagnostics.DiagnosticSource/6.0.1": {"dependencies": {"System.Runtime.CompilerServices.Unsafe": "6.0.0"}}, "System.Diagnostics.EventLog/7.0.0": {}, "System.Diagnostics.Tracing/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "5.0.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Runtime": "4.3.0"}}, "System.Drawing.Common/7.0.0": {"dependencies": {"Microsoft.Win32.SystemEvents": "7.0.0"}, "runtime": {"lib/net7.0/System.Drawing.Common.dll": {"assemblyVersion": "*******", "fileVersion": "7.0.22.51805"}}, "runtimeTargets": {"runtimes/win/lib/net7.0/System.Drawing.Common.dll": {"rid": "win", "assetType": "runtime", "assemblyVersion": "*******", "fileVersion": "7.0.22.51805"}}}, "System.Formats.Asn1/5.0.0": {}, "System.Globalization/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "5.0.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Runtime": "4.3.0"}}, "System.IdentityModel.Tokens.Jwt/6.35.0": {"dependencies": {"Microsoft.IdentityModel.JsonWebTokens": "6.35.0", "Microsoft.IdentityModel.Tokens": "6.35.0"}, "runtime": {"lib/net6.0/System.IdentityModel.Tokens.Jwt.dll": {"assemblyVersion": "6.35.0.0", "fileVersion": "6.35.0.41201"}}}, "System.IO/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "5.0.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Runtime": "4.3.0", "System.Text.Encoding": "4.3.0", "System.Threading.Tasks": "4.3.0"}}, "System.IO.FileSystem/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "5.0.0", "Microsoft.NETCore.Targets": "1.1.0", "System.IO": "4.3.0", "System.IO.FileSystem.Primitives": "4.3.0", "System.Runtime": "4.3.0", "System.Runtime.Handles": "4.3.0", "System.Text.Encoding": "4.3.0", "System.Threading.Tasks": "4.3.0"}}, "System.IO.FileSystem.AccessControl/5.0.0": {"dependencies": {"System.Security.AccessControl": "5.0.0", "System.Security.Principal.Windows": "5.0.0"}}, "System.IO.FileSystem.Primitives/4.3.0": {"dependencies": {"System.Runtime": "4.3.0"}}, "System.Linq/4.3.0": {"dependencies": {"System.Collections": "4.3.0", "System.Diagnostics.Debug": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0", "System.Runtime.Extensions": "4.3.0"}}, "System.Linq.Expressions/4.3.0": {"dependencies": {"System.Collections": "4.3.0", "System.Diagnostics.Debug": "4.3.0", "System.Globalization": "4.3.0", "System.IO": "4.3.0", "System.Linq": "4.3.0", "System.ObjectModel": "4.3.0", "System.Reflection": "4.3.0", "System.Reflection.Emit": "4.7.0", "System.Reflection.Emit.ILGeneration": "4.3.0", "System.Reflection.Emit.Lightweight": "4.3.0", "System.Reflection.Extensions": "4.3.0", "System.Reflection.Primitives": "4.3.0", "System.Reflection.TypeExtensions": "4.7.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0", "System.Runtime.Extensions": "4.3.0", "System.Threading": "4.3.0"}}, "System.Linq.Queryable/4.3.0": {"dependencies": {"System.Collections": "4.3.0", "System.Diagnostics.Debug": "4.3.0", "System.Linq": "4.3.0", "System.Linq.Expressions": "4.3.0", "System.Reflection": "4.3.0", "System.Reflection.Extensions": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0"}}, "System.Memory/4.5.5": {}, "System.Memory.Data/1.0.2": {"dependencies": {"System.Text.Encodings.Web": "7.0.0", "System.Text.Json": "7.0.2"}, "runtime": {"lib/netstandard2.0/System.Memory.Data.dll": {"assemblyVersion": "1.0.2.0", "fileVersion": "1.0.221.20802"}}}, "System.Numerics.Vectors/4.5.0": {}, "System.ObjectModel/4.3.0": {"dependencies": {"System.Collections": "4.3.0", "System.Diagnostics.Debug": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0", "System.Threading": "4.3.0"}}, "System.Reflection/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "5.0.0", "Microsoft.NETCore.Targets": "1.1.0", "System.IO": "4.3.0", "System.Reflection.Primitives": "4.3.0", "System.Runtime": "4.3.0"}}, "System.Reflection.Emit/4.7.0": {}, "System.Reflection.Emit.ILGeneration/4.3.0": {"dependencies": {"System.Reflection": "4.3.0", "System.Reflection.Primitives": "4.3.0", "System.Runtime": "4.3.0"}}, "System.Reflection.Emit.Lightweight/4.3.0": {"dependencies": {"System.Reflection": "4.3.0", "System.Reflection.Emit.ILGeneration": "4.3.0", "System.Reflection.Primitives": "4.3.0", "System.Runtime": "4.3.0"}}, "System.Reflection.Extensions/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "5.0.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Reflection": "4.3.0", "System.Runtime": "4.3.0"}}, "System.Reflection.Primitives/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "5.0.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Runtime": "4.3.0"}}, "System.Reflection.TypeExtensions/4.7.0": {}, "System.Resources.ResourceManager/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "5.0.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Globalization": "4.3.0", "System.Reflection": "4.3.0", "System.Runtime": "4.3.0"}}, "System.Runtime/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "5.0.0", "Microsoft.NETCore.Targets": "1.1.0"}}, "System.Runtime.Caching/6.0.0": {"dependencies": {"System.Configuration.ConfigurationManager": "7.0.0"}, "runtime": {"lib/net6.0/System.Runtime.Caching.dll": {"assemblyVersion": "4.0.0.0", "fileVersion": "6.0.21.52210"}}, "runtimeTargets": {"runtimes/win/lib/net6.0/System.Runtime.Caching.dll": {"rid": "win", "assetType": "runtime", "assemblyVersion": "4.0.0.0", "fileVersion": "6.0.21.52210"}}}, "System.Runtime.CompilerServices.Unsafe/6.0.0": {}, "System.Runtime.Extensions/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "5.0.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Runtime": "4.3.0"}}, "System.Runtime.Handles/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "5.0.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Runtime": "4.3.0"}}, "System.Runtime.InteropServices/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "5.0.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Reflection": "4.3.0", "System.Reflection.Primitives": "4.3.0", "System.Runtime": "4.3.0", "System.Runtime.Handles": "4.3.0"}}, "System.Security.AccessControl/5.0.0": {"dependencies": {"Microsoft.NETCore.Platforms": "5.0.0", "System.Security.Principal.Windows": "5.0.0"}}, "System.Security.Cryptography.Cng/5.0.0": {"dependencies": {"System.Formats.Asn1": "5.0.0"}}, "System.Security.Cryptography.ProtectedData/7.0.0": {"runtime": {"lib/net7.0/System.Security.Cryptography.ProtectedData.dll": {"assemblyVersion": "*******", "fileVersion": "7.0.22.51805"}}, "runtimeTargets": {"runtimes/win/lib/net7.0/System.Security.Cryptography.ProtectedData.dll": {"rid": "win", "assetType": "runtime", "assemblyVersion": "*******", "fileVersion": "7.0.22.51805"}}}, "System.Security.Permissions/7.0.0": {"dependencies": {"System.Windows.Extensions": "7.0.0"}, "runtime": {"lib/net7.0/System.Security.Permissions.dll": {"assemblyVersion": "*******", "fileVersion": "7.0.22.51805"}}}, "System.Security.Principal.Windows/5.0.0": {}, "System.Text.Encoding/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "5.0.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Runtime": "4.3.0"}}, "System.Text.Encoding.CodePages/6.0.0": {"dependencies": {"System.Runtime.CompilerServices.Unsafe": "6.0.0"}}, "System.Text.Encoding.Extensions/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "5.0.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Runtime": "4.3.0", "System.Text.Encoding": "4.3.0"}}, "System.Text.Encodings.Web/7.0.0": {}, "System.Text.Json/7.0.2": {"dependencies": {"System.Text.Encodings.Web": "7.0.0"}}, "System.Text.RegularExpressions/4.3.0": {"dependencies": {"System.Runtime": "4.3.0"}}, "System.Threading/4.3.0": {"dependencies": {"System.Runtime": "4.3.0", "System.Threading.Tasks": "4.3.0"}}, "System.Threading.Tasks/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "5.0.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Runtime": "4.3.0"}}, "System.Threading.Tasks.Extensions/4.5.4": {}, "System.Windows.Extensions/7.0.0": {"dependencies": {"System.Drawing.Common": "7.0.0"}, "runtime": {"lib/net7.0/System.Windows.Extensions.dll": {"assemblyVersion": "*******", "fileVersion": "7.0.22.51805"}}, "runtimeTargets": {"runtimes/win/lib/net7.0/System.Windows.Extensions.dll": {"rid": "win", "assetType": "runtime", "assemblyVersion": "*******", "fileVersion": "7.0.22.51805"}}}, "System.Xml.ReaderWriter/4.3.0": {"dependencies": {"System.Collections": "4.3.0", "System.Diagnostics.Debug": "4.3.0", "System.Globalization": "4.3.0", "System.IO": "4.3.0", "System.IO.FileSystem": "4.3.0", "System.IO.FileSystem.Primitives": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0", "System.Runtime.Extensions": "4.3.0", "System.Runtime.InteropServices": "4.3.0", "System.Text.Encoding": "4.3.0", "System.Text.Encoding.Extensions": "4.3.0", "System.Text.RegularExpressions": "4.3.0", "System.Threading.Tasks": "4.3.0", "System.Threading.Tasks.Extensions": "4.5.4"}}, "System.Xml.XPath/4.3.0": {"dependencies": {"System.Collections": "4.3.0", "System.Diagnostics.Debug": "4.3.0", "System.Globalization": "4.3.0", "System.IO": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0", "System.Runtime.Extensions": "4.3.0", "System.Threading": "4.3.0", "System.Xml.ReaderWriter": "4.3.0"}}, "TinyHelpers/2.0.28": {"dependencies": {"System.Text.Json": "7.0.2"}, "runtime": {"lib/net7.0/TinyHelpers.dll": {"assemblyVersion": "*******", "fileVersion": "2.0.28.42222"}}}, "TinyHelpers.EntityFrameworkCore/2.0.14": {"dependencies": {"Microsoft.EntityFrameworkCore.Relational": "7.0.4", "TinyHelpers": "2.0.28"}, "runtime": {"lib/net7.0/TinyHelpers.EntityFrameworkCore.dll": {"assemblyVersion": "*******", "fileVersion": "2.0.14.21006"}}}, "Z.EntityFramework.Extensions.EFCore/7.20.0": {"dependencies": {"Microsoft.CSharp": "4.7.0", "Microsoft.EntityFrameworkCore.Relational": "7.0.4", "Newtonsoft.Json": "13.0.1", "System.Configuration.ConfigurationManager": "7.0.0", "System.Reflection.Emit": "4.7.0", "System.Reflection.TypeExtensions": "4.7.0"}, "runtime": {"lib/net6.0/Z.EntityFramework.Extensions.EFCore.dll": {"assemblyVersion": "7.20.0.0", "fileVersion": "7.20.0.0"}}}, "Z.EntityFramework.Plus.EFCore/7.20.0": {"dependencies": {"Microsoft.CSharp": "4.7.0", "Microsoft.EntityFrameworkCore.Relational": "7.0.4", "System.Reflection.Emit": "4.7.0", "System.Reflection.TypeExtensions": "4.7.0", "Z.EntityFramework.Extensions.EFCore": "7.20.0", "Z.Expressions.Eval": "5.0.2"}, "runtime": {"lib/net6.0/Z.EntityFramework.Plus.EFCore.dll": {"assemblyVersion": "7.20.0.0", "fileVersion": "7.20.0.0"}}}, "Z.Expressions.Eval/5.0.2": {"dependencies": {"Microsoft.CSharp": "4.7.0", "Microsoft.Extensions.Caching.Memory": "7.0.0", "System.Collections.Concurrent": "4.3.0", "System.Collections.NonGeneric": "4.3.0", "System.Configuration.ConfigurationManager": "7.0.0", "System.Data.Common": "4.3.0", "System.Linq.Queryable": "4.3.0", "System.Reflection.Emit": "4.7.0", "System.Reflection.TypeExtensions": "4.7.0", "System.Xml.XPath": "4.3.0"}, "runtime": {"lib/net6.0/Z.Expressions.Eval.dll": {"assemblyVersion": "5.0.2.0", "fileVersion": "5.0.2.0"}}}, "BlueTape.Common/1.0.0": {"dependencies": {"BlueTape.CompanyService.Common": "1.1.21"}, "runtime": {"BlueTape.Common.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "BlueTape.Domain/1.0.0": {"dependencies": {"BlueTape.Common": "1.0.0", "Z.EntityFramework.Plus.EFCore": "7.20.0"}, "runtime": {"BlueTape.Domain.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}}}, "libraries": {"BlueTape.DataAccess.EF/1.0.0": {"type": "project", "serviceable": false, "sha512": ""}, "AutoMapper/12.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-0Rmg0zI5AFu1O/y//o9VGyhxKjhggWpk9mOA1tp0DEVx40c61bs+lnQv+0jUq8XbniF7FKgIVvI1perqiMtLrA==", "path": "automapper/12.0.0", "hashPath": "automapper.12.0.0.nupkg.sha512"}, "AutoMapper.Extensions.Microsoft.DependencyInjection/12.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-XCJ4E3oKrbRl1qY9Mr+7uyC0xZj1+bqQjmQRWTiTKiVuuXTny+7YFWHi20tPjwkMukLbicN6yGlDy5PZ4wyi1w==", "path": "automapper.extensions.microsoft.dependencyinjection/12.0.0", "hashPath": "automapper.extensions.microsoft.dependencyinjection.12.0.0.nupkg.sha512"}, "AWSSDK.Core/3.7.302.6": {"type": "package", "serviceable": true, "sha512": "sha512-eOPMHT3YSLQg5wXm+UL8Qoka17byK5l/4qxfuE/S32IoQ34gkTDQ1yI9pQPk8LEpeV/W0BE2np+XuhdDQ3wE8g==", "path": "awssdk.core/3.7.302.6", "hashPath": "awssdk.core.3.7.302.6.nupkg.sha512"}, "AWSSDK.KeyManagementService/3.7.300.46": {"type": "package", "serviceable": true, "sha512": "sha512-6J3FlWpFvEhYinkmabY8i9w66lgvmjlZG5oRHazqyiyatjMOYsA76Ynj+T6gtHC5iB1BrBtEFOPO1rq36sILfA==", "path": "awssdk.keymanagementservice/3.7.300.46", "hashPath": "awssdk.keymanagementservice.3.7.300.46.nupkg.sha512"}, "AWSSDK.SecretsManager/3.7.302.21": {"type": "package", "serviceable": true, "sha512": "sha512-Ns8mQtXUWAhp32xJTeftmAklzQeu0Tful0BTZjQg5EeyYE+qUDQgCrY31UBUr1PVw2sInmT5BBzoyhic9OjAwA==", "path": "awssdk.secretsmanager/3.7.302.21", "hashPath": "awssdk.secretsmanager.3.7.302.21.nupkg.sha512"}, "AWSSDK.SecretsManager.Caching/1.0.6": {"type": "package", "serviceable": true, "sha512": "sha512-N982+rohMJ/w8ywyN6hgnSgw2cpqj4MJDizz+b93gudQSEzR3lCTzHGN3AQl+ngMH4yTG+DfKgmL7QajvvYyKQ==", "path": "awssdk.secretsmanager.caching/1.0.6", "hashPath": "awssdk.secretsmanager.caching.1.0.6.nupkg.sha512"}, "AWSSDK.SecurityToken/3.7.300.47": {"type": "package", "serviceable": true, "sha512": "sha512-wBcBC0axwf+qQTVMPUblDxIpMtbp04z9ElYeW6BSjeAO7spvJXhEz7yhikdTn8YaeWW+K6U9h6AIDQpJlhH2vQ==", "path": "awssdk.securitytoken/3.7.300.47", "hashPath": "awssdk.securitytoken.3.7.300.47.nupkg.sha512"}, "Azure.Core/1.36.0": {"type": "package", "serviceable": true, "sha512": "sha512-vwqFZdHS4dzPlI7FFRkPx9ctA+aGGeRev3gnzG8lntWvKMmBhAmulABi1O9CEvS3/jzYt7yA+0pqVdxkpAd7dQ==", "path": "azure.core/1.36.0", "hashPath": "azure.core.1.36.0.nupkg.sha512"}, "Azure.Core.Amqp/1.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-6GG4gyFkAuHtpBVkvj0wE5+lCM+ttsZlIWAipBkI+jlCUlTgrTiNUROBFnb8xuKoymVDw9Tf1W8RoKqgbd71lg==", "path": "azure.core.amqp/1.3.0", "hashPath": "azure.core.amqp.1.3.0.nupkg.sha512"}, "Azure.Identity/1.10.4": {"type": "package", "serviceable": true, "sha512": "sha512-hSvisZy9sld0Gik1X94od3+rRXCx+AKgi+iLH6fFdlnRZRePn7RtrqUGSsORiH2h8H2sc4NLTrnuUte1WL+QuQ==", "path": "azure.identity/1.10.4", "hashPath": "azure.identity.1.10.4.nupkg.sha512"}, "Azure.Messaging.ServiceBus/7.17.1": {"type": "package", "serviceable": true, "sha512": "sha512-RvpLKmp2ur7hfm7NqiKPY2wIU7O4+yajYm3w7etnDsNj6sMlLCyNyCNMgVGeudQ4nOrk0YtoHJ1SbX2nJpCsUw==", "path": "azure.messaging.servicebus/7.17.1", "hashPath": "azure.messaging.servicebus.7.17.1.nupkg.sha512"}, "Azure.Security.KeyVault.Keys/4.5.0": {"type": "package", "serviceable": true, "sha512": "sha512-HnW9kjhRzQkfJE4ISl63cWVa6qLe3FM1MxoxNvNFtDUeT5iMBEg0YgGbcx2YgEiYaazIvSgZyjBr4L3Ur3+m7g==", "path": "azure.security.keyvault.keys/4.5.0", "hashPath": "azure.security.keyvault.keys.4.5.0.nupkg.sha512"}, "Azure.Security.KeyVault.Secrets/4.5.0": {"type": "package", "serviceable": true, "sha512": "sha512-ztr26Ai4K7AZGuw68/ffLDn+2G3WL0myjC7nY1RYkxPMnsplTPEH+Ke4RGxvSkg4kC7bJ9NwdlqpEwfDX0qhdw==", "path": "azure.security.keyvault.secrets/4.5.0", "hashPath": "azure.security.keyvault.secrets.4.5.0.nupkg.sha512"}, "BlueTape.AzureKeyVault/1.0.3": {"type": "package", "serviceable": true, "sha512": "sha512-On4RZI41X71GSmrYnv3p1eN6ullTWF9L8SsZ2NC/tQsQ/Upe0X1kcJE7rgvrT6G1RToKyY2n+OAGwfYZH8uOlQ==", "path": "bluetape.azurekeyvault/1.0.3", "hashPath": "bluetape.azurekeyvault.1.0.3.nupkg.sha512"}, "BlueTape.Common.Extensions/1.1.0": {"type": "package", "serviceable": true, "sha512": "sha512-P4mQRSipiN2qp+5ETfnBdZz564U1AWder867Zj/4SV+mZTS+SxqML0H+sW2W7PADp9iUgdngoNzqYsj+UxGheA==", "path": "bluetape.common.extensions/1.1.0", "hashPath": "bluetape.common.extensions.1.1.0.nupkg.sha512"}, "BlueTape.CompanyService/1.3.3": {"type": "package", "serviceable": true, "sha512": "sha512-xQBL9/we+qhEbotrpd4TEf80IKFnkE149ZvZZDMENCr4xJSh6NIOUYyJLC7oqeoFxLpBSIs7FzS8/RbkWtwnmg==", "path": "bluetape.companyservice/1.3.3", "hashPath": "bluetape.companyservice.1.3.3.nupkg.sha512"}, "BlueTape.CompanyService.Common/1.1.21": {"type": "package", "serviceable": true, "sha512": "sha512-1znqFDa5Gyglpcp3Eq3vh6r88ahgWhhMg6Duja1guXhjdWiRDDbIN8wPYoN5+i/IeW/aqLlyTr7pxapJ28qxwg==", "path": "bluetape.companyservice.common/1.1.21", "hashPath": "bluetape.companyservice.common.1.1.21.nupkg.sha512"}, "BlueTape.LS/1.1.68": {"type": "package", "serviceable": true, "sha512": "sha512-LsbIXd/LroCTmKbc/DKr/uBYDKuWjsmgtx9tXljy+dPI/hGWw5cz1p95c0B/DVQOiMo2XHUdUf1mDmDCtw+7Aw==", "path": "bluetape.ls/1.1.68", "hashPath": "bluetape.ls.1.1.68.nupkg.sha512"}, "BlueTape.LS.Domain/1.1.32": {"type": "package", "serviceable": true, "sha512": "sha512-paoyM33tittUEkDDRSXAFqOtbXR8trWV0NQYs+A5ZUbxY1KcN0ktBM72TD8FtPTLg0WLMjaEGSD8mkCqp8edIA==", "path": "bluetape.ls.domain/1.1.32", "hashPath": "bluetape.ls.domain.1.1.32.nupkg.sha512"}, "BlueTape.OBS/1.6.45": {"type": "package", "serviceable": true, "sha512": "sha512-/HrzRXTYhWaxmzS08xmTq+LiTHAbcmLlkA7RHOb2TbEMIqXiLngOBLpWOY5vGGi75recvfWO2KGpaJapFyurOw==", "path": "bluetape.obs/1.6.45", "hashPath": "bluetape.obs.1.6.45.nupkg.sha512"}, "BlueTape.ServiceBusMessaging/1.0.8": {"type": "package", "serviceable": true, "sha512": "sha512-lFXJaFhsHnzBJT5uciOpXT0OXXk4az+t178Je317Kito6hXqgiQbUf7hZQ/TuCc5DQ4zTiMafbgeZwQWi/2M5Q==", "path": "bluetape.servicebusmessaging/1.0.8", "hashPath": "bluetape.servicebusmessaging.1.0.8.nupkg.sha512"}, "BlueTape.Utilities/1.4.5": {"type": "package", "serviceable": true, "sha512": "sha512-8z+3lLvzHmVqhX4YAJiaIii8XFAAQUPRUbX3R5ZoSskQJmj7X5nGTEGjsD66g6XzUKKjweLNyMexRlJQMzI5lQ==", "path": "bluetape.utilities/1.4.5", "hashPath": "bluetape.utilities.1.4.5.nupkg.sha512"}, "Elastic.CommonSchema/1.5.3": {"type": "package", "serviceable": true, "sha512": "sha512-JgwhfThYY/s17asUiBCUVqnZtDdGTWO/2hTPG01QDfw2+T6kfwskrj5eh6XpBZsOh6r9SpBL95vSsU+q44i7Zg==", "path": "elastic.commonschema/1.5.3", "hashPath": "elastic.commonschema.1.5.3.nupkg.sha512"}, "Elastic.CommonSchema.Serilog/1.5.3": {"type": "package", "serviceable": true, "sha512": "sha512-bp2qHOWmN15fTKUecFMt7oCra68I1cm3yAEmwPcLuz4v2pQ5YxC8nVtyCTSSibquUS/ZPH5JInjlmuywV3UoyQ==", "path": "elastic.commonschema.serilog/1.5.3", "hashPath": "elastic.commonschema.serilog.1.5.3.nupkg.sha512"}, "Humanizer.Core/2.14.1": {"type": "package", "serviceable": true, "sha512": "sha512-lQKvtaTDOXnoVJ20ibTuSIOf2i0uO0MPbDhd1jm238I+U/2ZnRENj0cktKZhtchBMtCUSRQ5v4xBCUbKNmyVMw==", "path": "humanizer.core/2.14.1", "hashPath": "humanizer.core.2.14.1.nupkg.sha512"}, "Microsoft.AspNetCore.Http.Abstractions/2.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-Nxs7Z1q3f1STfLYKJSVXCs1iBl+Ya6E8o4Oy1bCxJ/rNI44E/0f6tbsrVqAWfB7jlnJfyaAtIalBVxPKUPQb4Q==", "path": "microsoft.aspnetcore.http.abstractions/2.2.0", "hashPath": "microsoft.aspnetcore.http.abstractions.2.2.0.nupkg.sha512"}, "Microsoft.AspNetCore.Http.Features/2.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-ziFz5zH8f33En4dX81LW84I6XrYXKf9jg6aM39cM+LffN9KJahViKZ61dGMSO2gd3e+qe5yBRwsesvyqlZaSMg==", "path": "microsoft.aspnetcore.http.features/2.2.0", "hashPath": "microsoft.aspnetcore.http.features.2.2.0.nupkg.sha512"}, "Microsoft.Azure.Amqp/2.6.4": {"type": "package", "serviceable": true, "sha512": "sha512-Xf2mbwTSuUtqRlULKXCEuXPxlBhZzZXWmMxnxF64WJAelo3PA7kIDR4Bv+eOBYxHyr3FJtwG3/7rrhyXIx1Qzg==", "path": "microsoft.azure.amqp/2.6.4", "hashPath": "microsoft.azure.amqp.2.6.4.nupkg.sha512"}, "Microsoft.Bcl.AsyncInterfaces/1.1.1": {"type": "package", "serviceable": true, "sha512": "sha512-yuvf07qFWFqtK3P/MRkEKLhn5r2UbSpVueRziSqj0yJQIKFwG1pq9mOayK3zE5qZCTs0CbrwL9M6R8VwqyGy2w==", "path": "microsoft.bcl.asyncinterfaces/1.1.1", "hashPath": "microsoft.bcl.asyncinterfaces.1.1.1.nupkg.sha512"}, "Microsoft.CSharp/4.7.0": {"type": "package", "serviceable": true, "sha512": "sha512-pTj+D3uJWyN3My70i2Hqo+OXixq3Os2D1nJ2x92FFo6sk8fYS1m1WLNTs0Dc1uPaViH0YvEEwvzddQ7y4rhXmA==", "path": "microsoft.csharp/4.7.0", "hashPath": "microsoft.csharp.4.7.0.nupkg.sha512"}, "Microsoft.Data.SqlClient/5.1.5": {"type": "package", "serviceable": true, "sha512": "sha512-6kvhQjY5uBCdBccezFD2smfnpQjQ33cZtUZVrNvxlwoBu6uopM5INH6uSgLI7JRLtlQ3bMPwnhMq4kchsXeZ5w==", "path": "microsoft.data.sqlclient/5.1.5", "hashPath": "microsoft.data.sqlclient.5.1.5.nupkg.sha512"}, "Microsoft.Data.SqlClient.SNI.runtime/5.1.1": {"type": "package", "serviceable": true, "sha512": "sha512-wNGM5ZTQCa2blc9ikXQouybGiyMd6IHPVJvAlBEPtr6JepZEOYeDxGyprYvFVeOxlCXs7avridZQ0nYkHzQWCQ==", "path": "microsoft.data.sqlclient.sni.runtime/5.1.1", "hashPath": "microsoft.data.sqlclient.sni.runtime.5.1.1.nupkg.sha512"}, "Microsoft.EntityFrameworkCore/7.0.4": {"type": "package", "serviceable": true, "sha512": "sha512-eNcsY3rft5ERJJcen80Jyg57EScjWZmvhwmFLYXmEOTdVqHG+wQZiMOXnO1b5RH3u2qTQq+Tpci7KGfLAG5Gtg==", "path": "microsoft.entityframeworkcore/7.0.4", "hashPath": "microsoft.entityframeworkcore.7.0.4.nupkg.sha512"}, "Microsoft.EntityFrameworkCore.Abstractions/7.0.4": {"type": "package", "serviceable": true, "sha512": "sha512-6GbYvs4L5oFpYpMzwF05kdDgvX09UmMX7MpDtDlGI5ymijFwquwv+yvdijbtodOuu0yLUpc4n71x6eBdJ8v1xQ==", "path": "microsoft.entityframeworkcore.abstractions/7.0.4", "hashPath": "microsoft.entityframeworkcore.abstractions.7.0.4.nupkg.sha512"}, "Microsoft.EntityFrameworkCore.Analyzers/7.0.4": {"type": "package", "serviceable": true, "sha512": "sha512-YRD4bViuaEPEsaBIL52DzXGzLCt3jYoE3wztYEW1QZYDl89hQ+ca0nvBO2mnMHmCXpU/2wlErrUyDp4x5B/3mg==", "path": "microsoft.entityframeworkcore.analyzers/7.0.4", "hashPath": "microsoft.entityframeworkcore.analyzers.7.0.4.nupkg.sha512"}, "Microsoft.EntityFrameworkCore.Design/7.0.4": {"type": "package", "serviceable": true, "sha512": "sha512-LI/ML3w17ap5IUmEKOPVnGJYi/XSDJW3Rf42utNF0e1tidmKtSkjwoTqIKLt2hE+jQJrlzeaqu5YiqdoFWVuZw==", "path": "microsoft.entityframeworkcore.design/7.0.4", "hashPath": "microsoft.entityframeworkcore.design.7.0.4.nupkg.sha512"}, "Microsoft.EntityFrameworkCore.InMemory/7.0.4": {"type": "package", "serviceable": true, "sha512": "sha512-enWK5KFD21mq6z1qs5i2PsGQbfud5hLF9erQvPCSC0HCSTQUuOqIxccUEbxu4QRrdqpjwVofZs+yNSM5qoe6bg==", "path": "microsoft.entityframeworkcore.inmemory/7.0.4", "hashPath": "microsoft.entityframeworkcore.inmemory.7.0.4.nupkg.sha512"}, "Microsoft.EntityFrameworkCore.Relational/7.0.4": {"type": "package", "serviceable": true, "sha512": "sha512-L41+VonK6L0IurFHopoe5yY+m3MD26OMocKLPPR/XKxnazzZUcGPz0IGJpVnwpZyKVPfEIAnD5vmm60meYr1NA==", "path": "microsoft.entityframeworkcore.relational/7.0.4", "hashPath": "microsoft.entityframeworkcore.relational.7.0.4.nupkg.sha512"}, "Microsoft.EntityFrameworkCore.Tools/7.0.4": {"type": "package", "serviceable": true, "sha512": "sha512-58hDB+ENGisuSjJBl1RBHL9qzFJTukFSQFl/wCU8/3ApcOH/rPrRG4PWThiJTmfHRmh8H8HExdYbtkv7wa7BLg==", "path": "microsoft.entityframeworkcore.tools/7.0.4", "hashPath": "microsoft.entityframeworkcore.tools.7.0.4.nupkg.sha512"}, "Microsoft.Extensions.Caching.Abstractions/7.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-IeimUd0TNbhB4ded3AbgBLQv2SnsiVugDyGV1MvspQFVlA07nDC7Zul7kcwH5jWN3JiTcp/ySE83AIJo8yfKjg==", "path": "microsoft.extensions.caching.abstractions/7.0.0", "hashPath": "microsoft.extensions.caching.abstractions.7.0.0.nupkg.sha512"}, "Microsoft.Extensions.Caching.Memory/7.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-xpidBs2KCE2gw1JrD0quHE72kvCaI3xFql5/Peb2GRtUuZX+dYPoK/NTdVMiM67Svym0M0Df9A3xyU0FbMQhHw==", "path": "microsoft.extensions.caching.memory/7.0.0", "hashPath": "microsoft.extensions.caching.memory.7.0.0.nupkg.sha512"}, "Microsoft.Extensions.Configuration/2.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-SsI4RqI8EH00+cYO96tbftlh87sNUv1eeyuBU1XZdQkG0RrHAOjWgl7P0FoLeTSMXJpOnfweeOWj2d1/5H3FxA==", "path": "microsoft.extensions.configuration/2.0.0", "hashPath": "microsoft.extensions.configuration.2.0.0.nupkg.sha512"}, "Microsoft.Extensions.Configuration.Abstractions/7.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-f34u2eaqIjNO9YLHBz8rozVZ+TcFiFs0F3r7nUJd7FRkVSxk8u4OpoK226mi49MwexHOR2ibP9MFvRUaLilcQQ==", "path": "microsoft.extensions.configuration.abstractions/7.0.0", "hashPath": "microsoft.extensions.configuration.abstractions.7.0.0.nupkg.sha512"}, "Microsoft.Extensions.Configuration.Binder/2.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-IznHHzGUtrdpuQqIUdmzF6TYPcsYHONhHh3o9dGp39sX/9Zfmt476UnhvU0UhXgJnXXAikt/MpN6AuSLCCMdEQ==", "path": "microsoft.extensions.configuration.binder/2.0.0", "hashPath": "microsoft.extensions.configuration.binder.2.0.0.nupkg.sha512"}, "Microsoft.Extensions.DependencyInjection/7.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-elNeOmkeX3eDVG6pYVeV82p29hr+UKDaBhrZyWvWLw/EVZSYEkZlQdkp0V39k/Xehs2Qa0mvoCvkVj3eQxNQ1Q==", "path": "microsoft.extensions.dependencyinjection/7.0.0", "hashPath": "microsoft.extensions.dependencyinjection.7.0.0.nupkg.sha512"}, "Microsoft.Extensions.DependencyInjection.Abstractions/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-cjWrLkJXK0rs4zofsK4bSdg+jhDLTaxrkXu4gS6Y7MAlCvRyNNgwY/lJi5RDlQOnSZweHqoyvgvbdvQsRIW+hg==", "path": "microsoft.extensions.dependencyinjection.abstractions/8.0.0", "hashPath": "microsoft.extensions.dependencyinjection.abstractions.8.0.0.nupkg.sha512"}, "Microsoft.Extensions.DependencyModel/7.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-oONNYd71J3LzkWc4fUHl3SvMfiQMYUCo/mDHDEu76hYYxdhdrPYv6fvGv9nnKVyhE9P0h20AU8RZB5OOWQcAXg==", "path": "microsoft.extensions.dependencymodel/7.0.0", "hashPath": "microsoft.extensions.dependencymodel.7.0.0.nupkg.sha512"}, "Microsoft.Extensions.FileProviders.Abstractions/6.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-0pd4/fho0gC12rQswaGQxbU34jOS1TPS8lZPpkFCH68ppQjHNHYle9iRuHeev1LhrJ94YPvzcRd8UmIuFk23Qw==", "path": "microsoft.extensions.fileproviders.abstractions/6.0.0", "hashPath": "microsoft.extensions.fileproviders.abstractions.6.0.0.nupkg.sha512"}, "Microsoft.Extensions.Hosting.Abstractions/6.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-GcT5l2CYXL6Sa27KCSh0TixsRfADUgth+ojQSD5EkzisZxmGFh7CwzkcYuGwvmXLjr27uWRNrJ2vuuEjMhU05Q==", "path": "microsoft.extensions.hosting.abstractions/6.0.0", "hashPath": "microsoft.extensions.hosting.abstractions.6.0.0.nupkg.sha512"}, "Microsoft.Extensions.Http/6.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-15+pa2G0bAMHbHewaQIdr/y6ag2H3yh4rd9hTXavtWDzQBkvpe2RMqFg8BxDpcQWssmjmBApGPcw93QRz6YcMg==", "path": "microsoft.extensions.http/6.0.0", "hashPath": "microsoft.extensions.http.6.0.0.nupkg.sha512"}, "Microsoft.Extensions.Http.Polly/6.0.9": {"type": "package", "serviceable": true, "sha512": "sha512-+tQeERLaSPA+G//SlIZ5pyv/jAmkn1xnMMOvFu3Bag3EJxwV4D9iEkHD2TaNiJOoFZ/VROUB76/H7n/75e9Dow==", "path": "microsoft.extensions.http.polly/6.0.9", "hashPath": "microsoft.extensions.http.polly.6.0.9.nupkg.sha512"}, "Microsoft.Extensions.Logging/7.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-Nw2muoNrOG5U5qa2ZekXwudUn2BJcD41e65zwmDHb1fQegTX66UokLWZkJRpqSSHXDOWZ5V0iqhbxOEky91atA==", "path": "microsoft.extensions.logging/7.0.0", "hashPath": "microsoft.extensions.logging.7.0.0.nupkg.sha512"}, "Microsoft.Extensions.Logging.Abstractions/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-arDBqTgFCyS0EvRV7O3MZturChstm50OJ0y9bDJvAcmEPJm0FFpFyjU/JLYyStNGGey081DvnQYlncNX5SJJGA==", "path": "microsoft.extensions.logging.abstractions/8.0.0", "hashPath": "microsoft.extensions.logging.abstractions.8.0.0.nupkg.sha512"}, "Microsoft.Extensions.Options/8.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-wmpp+BSU3oGifaev6Z9rrlwHoITLFfpVOSbgBrOXjkbJSCXnZVCsoRGE5c3fJFI4VlNgnNkNlI9y+5jC4fmOEA==", "path": "microsoft.extensions.options/8.0.1", "hashPath": "microsoft.extensions.options.8.0.1.nupkg.sha512"}, "Microsoft.Extensions.Options.ConfigurationExtensions/2.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-Y/lGICwO27fCkQRK3tZseVzFjZaxfGmui990E67sB4MuiPzdJHnJDS/BeYWrHShSSBgCl4KyKRx4ux686fftPg==", "path": "microsoft.extensions.options.configurationextensions/2.0.0", "hashPath": "microsoft.extensions.options.configurationextensions.2.0.0.nupkg.sha512"}, "Microsoft.Extensions.Primitives/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-bXJEZrW9ny8vjMF1JV253WeLhpEVzFo1lyaZu1vQ4ZxWUlVvknZ/+ftFgVheLubb4eZPSwwxBeqS1JkCOjxd8g==", "path": "microsoft.extensions.primitives/8.0.0", "hashPath": "microsoft.extensions.primitives.8.0.0.nupkg.sha512"}, "Microsoft.Identity.Client/4.56.0": {"type": "package", "serviceable": true, "sha512": "sha512-rr4zbidvHy9r4NvOAs5hdd964Ao2A0pAeFBJKR95u1CJAVzbd1p6tPTXUZ+5ld0cfThiVSGvz6UHwY6JjraTpA==", "path": "microsoft.identity.client/4.56.0", "hashPath": "microsoft.identity.client.4.56.0.nupkg.sha512"}, "Microsoft.Identity.Client.Extensions.Msal/4.56.0": {"type": "package", "serviceable": true, "sha512": "sha512-H12YAzEGK55vZ+QpxUzozhW8ZZtgPDuWvgA0JbdIR9UhMUplj29JhIgE2imuH8W2Nw9D8JKygR1uxRFtpSNcrg==", "path": "microsoft.identity.client.extensions.msal/4.56.0", "hashPath": "microsoft.identity.client.extensions.msal.4.56.0.nupkg.sha512"}, "Microsoft.IdentityModel.Abstractions/6.35.0": {"type": "package", "serviceable": true, "sha512": "sha512-xuR8E4Rd96M41CnUSCiOJ2DBh+z+zQSmyrYHdYhD6K4fXBcQGVnRCFQ0efROUYpP+p0zC1BLKr0JRpVuujTZSg==", "path": "microsoft.identitymodel.abstractions/6.35.0", "hashPath": "microsoft.identitymodel.abstractions.6.35.0.nupkg.sha512"}, "Microsoft.IdentityModel.JsonWebTokens/6.35.0": {"type": "package", "serviceable": true, "sha512": "sha512-9wxai3hKgZUb4/NjdRKfQd0QJvtXKDlvmGMYACbEC8DFaicMFCFhQFZq9ZET1kJLwZahf2lfY5Gtcpsx8zYzbg==", "path": "microsoft.identitymodel.jsonwebtokens/6.35.0", "hashPath": "microsoft.identitymodel.jsonwebtokens.6.35.0.nupkg.sha512"}, "Microsoft.IdentityModel.Logging/6.35.0": {"type": "package", "serviceable": true, "sha512": "sha512-jePrSfGAmqT81JDCNSY+fxVWoGuJKt9e6eJ+vT7+quVS55nWl//jGjUQn4eFtVKt4rt5dXaleZdHRB9J9AJZ7Q==", "path": "microsoft.identitymodel.logging/6.35.0", "hashPath": "microsoft.identitymodel.logging.6.35.0.nupkg.sha512"}, "Microsoft.IdentityModel.Protocols/6.35.0": {"type": "package", "serviceable": true, "sha512": "sha512-BPQhlDzdFvv1PzaUxNSk+VEPwezlDEVADIKmyxubw7IiELK18uJ06RQ9QKKkds30XI+gDu9n8j24XQ8w7fjWcg==", "path": "microsoft.identitymodel.protocols/6.35.0", "hashPath": "microsoft.identitymodel.protocols.6.35.0.nupkg.sha512"}, "Microsoft.IdentityModel.Protocols.OpenIdConnect/6.35.0": {"type": "package", "serviceable": true, "sha512": "sha512-LMtVqnECCCdSmyFoCOxIE5tXQqkOLrvGrL7OxHg41DIm1bpWtaCdGyVcTAfOQpJXvzND9zUKIN/lhngPkYR8vg==", "path": "microsoft.identitymodel.protocols.openidconnect/6.35.0", "hashPath": "microsoft.identitymodel.protocols.openidconnect.6.35.0.nupkg.sha512"}, "Microsoft.IdentityModel.Tokens/6.35.0": {"type": "package", "serviceable": true, "sha512": "sha512-RN7lvp7s3Boucg1NaNAbqDbxtlLj5Qeb+4uSS1TeK5FSBVM40P4DKaTKChT43sHyKfh7V0zkrMph6DdHvyA4bg==", "path": "microsoft.identitymodel.tokens/6.35.0", "hashPath": "microsoft.identitymodel.tokens.6.35.0.nupkg.sha512"}, "Microsoft.NETCore.Platforms/5.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-VyPlqzH2wavqquTcYpkIIAQ6WdenuKoFN0BdYBbCWsclXacSOHNQn66Gt4z5NBqEYW0FAPm5rlvki9ZiCij5xQ==", "path": "microsoft.netcore.platforms/5.0.0", "hashPath": "microsoft.netcore.platforms.5.0.0.nupkg.sha512"}, "Microsoft.NETCore.Targets/1.1.0": {"type": "package", "serviceable": true, "sha512": "sha512-aOZA3BWfz9RXjpzt0sRJJMjAscAUm3Hoa4UWAfceV9UTYxgwZ1lZt5nO2myFf+/jetYQo4uTP7zS8sJY67BBxg==", "path": "microsoft.netcore.targets/1.1.0", "hashPath": "microsoft.netcore.targets.1.1.0.nupkg.sha512"}, "Microsoft.SqlServer.Server/1.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-N4KeF3cpcm1PUHym1RmakkzfkEv3GRMyofVv40uXsQhCQeglr2OHNcUk2WOG51AKpGO8ynGpo9M/kFXSzghwug==", "path": "microsoft.sqlserver.server/1.0.0", "hashPath": "microsoft.sqlserver.server.1.0.0.nupkg.sha512"}, "Microsoft.Win32.SystemEvents/7.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-2nXPrhdAyAzir0gLl8Yy8S5Mnm/uBSQQA7jEsILOS1MTyS7DbmV1NgViMtvV1sfCD1ebITpNwb1NIinKeJgUVQ==", "path": "microsoft.win32.systemevents/7.0.0", "hashPath": "microsoft.win32.systemevents.7.0.0.nupkg.sha512"}, "MongoDB.Bson/2.25.0": {"type": "package", "serviceable": true, "sha512": "sha512-xQx/qtC2nu9oGiyNqAwfiDpUMweLi0nID677cyKykpwmj5AVMrnd//UwmcmuX95178DeY6rf7cjmA613TQXPiA==", "path": "mongodb.bson/2.25.0", "hashPath": "mongodb.bson.2.25.0.nupkg.sha512"}, "Mono.TextTemplating/2.2.1": {"type": "package", "serviceable": true, "sha512": "sha512-KZYeKBET/2Z0gY1WlTAK7+RHTl7GSbtvTLDXEZZojUdAPqpQNDL6tHv7VUpqfX5VEOh+uRGKaZXkuD253nEOBQ==", "path": "mono.texttemplating/2.2.1", "hashPath": "mono.texttemplating.2.2.1.nupkg.sha512"}, "Newtonsoft.Json/13.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-ppPFpBcvxdsfUonNcvITKqLl3bqxWbDCZIzDWHzjpdAHRFfZe0Dw9HmA0+za13IdyrgJwpkDTDA9fHaxOrt20A==", "path": "newtonsoft.json/13.0.1", "hashPath": "newtonsoft.json.13.0.1.nupkg.sha512"}, "Npgsql/7.0.2": {"type": "package", "serviceable": true, "sha512": "sha512-/k12hfmg4PI0IU2UTLN6n0s/FKUfox8+RdWtzgADYZoyks7GH82WLyXm27eeqatsi/nmiK9lO3HyULlTD87szg==", "path": "npgsql/7.0.2", "hashPath": "npgsql.7.0.2.nupkg.sha512"}, "Npgsql.EntityFrameworkCore.PostgreSQL/7.0.3": {"type": "package", "serviceable": true, "sha512": "sha512-3iVz7vcPAZt6iBSjJ8XyjtIP19jilNOJqTiTCs2UHWhpTyP+FbNhnzCBE/oyD3n49McmmenjKpva+yTy7q1z/g==", "path": "npgsql.entityframeworkcore.postgresql/7.0.3", "hashPath": "npgsql.entityframeworkcore.postgresql.7.0.3.nupkg.sha512"}, "Polly/7.2.3": {"type": "package", "serviceable": true, "sha512": "sha512-DeCY0OFbNdNxsjntr1gTXHJ5pKUwYzp04Er2LLeN3g6pWhffsGuKVfMBLe1lw7x76HrPkLxKEFxBlpRxS2nDEQ==", "path": "polly/7.2.3", "hashPath": "polly.7.2.3.nupkg.sha512"}, "Polly.Extensions.Http/3.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-drrG+hB3pYFY7w1c3BD+lSGYvH2oIclH8GRSehgfyP5kjnFnHKQuuBhuHLv+PWyFuaTDyk/vfRpnxOzd11+J8g==", "path": "polly.extensions.http/3.0.0", "hashPath": "polly.extensions.http.3.0.0.nupkg.sha512"}, "Serilog/2.12.0": {"type": "package", "serviceable": true, "sha512": "sha512-xaiJLIdu6rYMKfQMYUZgTy8YK7SMZjB4Yk50C/u//Z4OsvxkUfSPJy4nknfvwAC34yr13q7kcyh4grbwhSxyZg==", "path": "serilog/2.12.0", "hashPath": "serilog.2.12.0.nupkg.sha512"}, "Serilog.AspNetCore/6.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-5XW90k62V7G9I0D/j9Iz+NyRBB6/SnoFpHUPeLnV40gONV2vs2A/ewWi91QVjQmyHBfzFeqIrkvE/DJMZ0alTg==", "path": "serilog.aspnetcore/6.0.1", "hashPath": "serilog.aspnetcore.6.0.1.nupkg.sha512"}, "Serilog.Enrichers.GlobalLogContext/2.1.0": {"type": "package", "serviceable": true, "sha512": "sha512-BtSFymdnkHWYpxJzLAQ7J03pdnkIlQ3kQLswBRz37j2XllEp5cnmCOOTqdw7Hyh8TrHQ1nJSZZ+HTjV4AqTwhA==", "path": "serilog.enrichers.globallogcontext/2.1.0", "hashPath": "serilog.enrichers.globallogcontext.2.1.0.nupkg.sha512"}, "Serilog.Extensions.Hosting/5.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-o0VUyt3npAqOJaZ6CiWLFeLYs3CYJwfcAqaUqprzsmj7qYIvorcn8cZLVR8AQX6vzX7gee2bD0sQeA17iO2/Aw==", "path": "serilog.extensions.hosting/5.0.1", "hashPath": "serilog.extensions.hosting.5.0.1.nupkg.sha512"}, "Serilog.Extensions.Logging/3.1.0": {"type": "package", "serviceable": true, "sha512": "sha512-IWfem7wfrFbB3iw1OikqPFNPEzfayvDuN4WP7Ue1AVFskalMByeWk3QbtUXQR34SBkv1EbZ3AySHda/ErDgpcg==", "path": "serilog.extensions.logging/3.1.0", "hashPath": "serilog.extensions.logging.3.1.0.nupkg.sha512"}, "Serilog.Formatting.Compact/1.1.0": {"type": "package", "serviceable": true, "sha512": "sha512-pNroKVjo+rDqlxNG5PXkRLpfSCuDOBY0ri6jp9PLe505ljqwhwZz8ospy2vWhQlFu5GkIesh3FcDs4n7sWZODA==", "path": "serilog.formatting.compact/1.1.0", "hashPath": "serilog.formatting.compact.1.1.0.nupkg.sha512"}, "Serilog.Settings.Configuration/3.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-7GNudISZwqaT902hqEL2OFGTZeUFWfnrNLupJkOqeF41AR3GjcxX+Hwb30xb8gG2/CDXsCMVfF8o0+8KY0fJNg==", "path": "serilog.settings.configuration/3.3.0", "hashPath": "serilog.settings.configuration.3.3.0.nupkg.sha512"}, "Serilog.Sinks.Console/4.1.0": {"type": "package", "serviceable": true, "sha512": "sha512-K6N5q+5fetjnJPvCmkWOpJ/V8IEIoMIB1s86OzBrbxwTyHxdx3pmz4H+8+O/Dc/ftUX12DM1aynx/dDowkwzqg==", "path": "serilog.sinks.console/4.1.0", "hashPath": "serilog.sinks.console.4.1.0.nupkg.sha512"}, "Serilog.Sinks.Debug/2.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-Y6g3OBJ4JzTyyw16fDqtFcQ41qQAydnEvEqmXjhwhgjsnG/FaJ8GUqF5ldsC/bVkK8KYmqrPhDO+tm4dF6xx4A==", "path": "serilog.sinks.debug/2.0.0", "hashPath": "serilog.sinks.debug.2.0.0.nupkg.sha512"}, "Serilog.Sinks.File/5.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-uwV5hdhWPwUH1szhO8PJpFiahqXmzPzJT/sOijH/kFgUx+cyoDTMM8MHD0adw9+Iem6itoibbUXHYslzXsLEAg==", "path": "serilog.sinks.file/5.0.0", "hashPath": "serilog.sinks.file.5.0.0.nupkg.sha512"}, "Serilog.Sinks.Http/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-eHyl2/93Roymf2eudPl/6Eeu2GQ93Ucy4GM1UPF0jyd7CIW8r+Bk5ohdbjjyjB9TQSpP2ovOuj6ltf9DjoWHtg==", "path": "serilog.sinks.http/8.0.0", "hashPath": "serilog.sinks.http.8.0.0.nupkg.sha512"}, "Serilog.Sinks.Logz.Io/7.1.0": {"type": "package", "serviceable": true, "sha512": "sha512-uZN5FWMvpk0m/6u7PiKShODbUvfxQZGzK+D91BaxV1ePVJSE0xaqJwCh7rp8/mhPp2C0UPmXOt1hAF306Fzn1Q==", "path": "serilog.sinks.logz.io/7.1.0", "hashPath": "serilog.sinks.logz.io.7.1.0.nupkg.sha512"}, "Serilog.Sinks.PeriodicBatching/3.1.0": {"type": "package", "serviceable": true, "sha512": "sha512-NDWR7m3PalVlGEq3rzoktrXikjFMLmpwF0HI4sowo8YDdU+gqPlTHlDQiOGxHfB0sTfjPA9JjA7ctKG9zqjGkw==", "path": "serilog.sinks.periodicbatching/3.1.0", "hashPath": "serilog.sinks.periodicbatching.3.1.0.nupkg.sha512"}, "System.CodeDom/4.4.0": {"type": "package", "serviceable": true, "sha512": "sha512-2sCCb7doXEwtYAbqzbF/8UAeDRMNmPaQbU2q50Psg1J9KzumyVVCgKQY8s53WIPTufNT0DpSe9QRvVjOzfDWBA==", "path": "system.codedom/4.4.0", "hashPath": "system.codedom.4.4.0.nupkg.sha512"}, "System.Collections/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-3Dcj85/TBdVpL5Zr+gEEBUuFe2icOnLalmEh9hfck1PTYbbyWuZgh4fmm2ysCLTrqLQw6t3TgTyJ+VLp+Qb+Lw==", "path": "system.collections/4.3.0", "hashPath": "system.collections.4.3.0.nupkg.sha512"}, "System.Collections.Concurrent/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-ztl69Xp0Y/UXCL+3v3tEU+lIy+bvjKNUmopn1wep/a291pVPK7dxBd6T7WnlQqRog+d1a/hSsgRsmFnIBKTPLQ==", "path": "system.collections.concurrent/4.3.0", "hashPath": "system.collections.concurrent.4.3.0.nupkg.sha512"}, "System.Collections.NonGeneric/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-prtjIEMhGUnQq6RnPEYLpFt8AtLbp9yq2zxOSrY7KJJZrw25Fi97IzBqY7iqssbM61Ek5b8f3MG/sG1N2sN5KA==", "path": "system.collections.nongeneric/4.3.0", "hashPath": "system.collections.nongeneric.4.3.0.nupkg.sha512"}, "System.Configuration.ConfigurationManager/7.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-WvRUdlL1lB0dTRZSs5XcQOd5q9MYNk90GkbmRmiCvRHThWiojkpGqWdmEDJdXyHbxG/BhE5hmVbMfRLXW9FJVA==", "path": "system.configuration.configurationmanager/7.0.0", "hashPath": "system.configuration.configurationmanager.7.0.0.nupkg.sha512"}, "System.Data.Common/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-lm6E3T5u7BOuEH0u18JpbJHxBfOJPuCyl4Kg1RH10ktYLp5uEEE1xKrHW56/We4SnZpGAuCc9N0MJpSDhTHZGQ==", "path": "system.data.common/4.3.0", "hashPath": "system.data.common.4.3.0.nupkg.sha512"}, "System.Diagnostics.Debug/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-ZUhUOdqmaG5Jk3Xdb8xi5kIyQYAA4PnTNlHx1mu9ZY3qv4ELIdKbnL/akbGaKi2RnNUWaZsAs31rvzFdewTj2g==", "path": "system.diagnostics.debug/4.3.0", "hashPath": "system.diagnostics.debug.4.3.0.nupkg.sha512"}, "System.Diagnostics.DiagnosticSource/6.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-KiLYDu2k2J82Q9BJpWiuQqCkFjRBWVq4jDzKKWawVi9KWzyD0XG3cmfX0vqTQlL14Wi9EufJrbL0+KCLTbqWiQ==", "path": "system.diagnostics.diagnosticsource/6.0.1", "hashPath": "system.diagnostics.diagnosticsource.6.0.1.nupkg.sha512"}, "System.Diagnostics.EventLog/7.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-eUDP47obqQm3SFJfP6z+Fx2nJ4KKTQbXB4Q9Uesnzw9SbYdhjyoGXuvDn/gEmFY6N5Z3bFFbpAQGA7m6hrYJCw==", "path": "system.diagnostics.eventlog/7.0.0", "hashPath": "system.diagnostics.eventlog.7.0.0.nupkg.sha512"}, "System.Diagnostics.Tracing/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-rswfv0f/Cqkh78rA5S8eN8Neocz234+emGCtTF3lxPY96F+mmmUen6tbn0glN6PMvlKQb9bPAY5e9u7fgPTkKw==", "path": "system.diagnostics.tracing/4.3.0", "hashPath": "system.diagnostics.tracing.4.3.0.nupkg.sha512"}, "System.Drawing.Common/7.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-K<PERSON>+oBU38pxkKPxvLcLfIkOV5Ien8ReN78wro7OF5/erwcmortzeFx+iBswlh2Vz6gVne0khocQudGwaO1Ey6A==", "path": "system.drawing.common/7.0.0", "hashPath": "system.drawing.common.7.0.0.nupkg.sha512"}, "System.Formats.Asn1/5.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-MTvUIktmemNB+El0Fgw9egyqT9AYSIk6DTJeoDSpc3GIHxHCMo8COqkWT1mptX5tZ1SlQ6HJZ0OsSvMth1c12w==", "path": "system.formats.asn1/5.0.0", "hashPath": "system.formats.asn1.5.0.0.nupkg.sha512"}, "System.Globalization/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-kYdVd2f2PAdFGblzFswE4hkNANJBKRmsfa2X5LG2AcWE1c7/4t0pYae1L8vfZ5xvE2nK/R9JprtToA61OSHWIg==", "path": "system.globalization/4.3.0", "hashPath": "system.globalization.4.3.0.nupkg.sha512"}, "System.IdentityModel.Tokens.Jwt/6.35.0": {"type": "package", "serviceable": true, "sha512": "sha512-yxGIQd3BFK7F6S62/7RdZk3C/mfwyVxvh6ngd1VYMBmbJ1YZZA9+Ku6suylVtso0FjI0wbElpJ0d27CdsyLpBQ==", "path": "system.identitymodel.tokens.jwt/6.35.0", "hashPath": "system.identitymodel.tokens.jwt.6.35.0.nupkg.sha512"}, "System.IO/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-3qjaHvxQPDpSOYICjUoTsmoq5u6QJAFRUITgeT/4gqkF1bajbSmb1kwSxEA8AHlofqgcKJcM8udgieRNhaJ5Cg==", "path": "system.io/4.3.0", "hashPath": "system.io.4.3.0.nupkg.sha512"}, "System.IO.FileSystem/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-3wEMARTnuio+ulnvi+hkRNROYwa1kylvYahhcLk4HSoVdl+xxTFVeVlYOfLwrDPImGls0mDqbMhrza8qnWPTdA==", "path": "system.io.filesystem/4.3.0", "hashPath": "system.io.filesystem.4.3.0.nupkg.sha512"}, "System.IO.FileSystem.AccessControl/5.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-SxHB3nuNrpptVk+vZ/F+7OHEpoHUIKKMl02bUmYHQr1r+glbZQxs7pRtsf4ENO29TVm2TH3AEeep2fJcy92oYw==", "path": "system.io.filesystem.accesscontrol/5.0.0", "hashPath": "system.io.filesystem.accesscontrol.5.0.0.nupkg.sha512"}, "System.IO.FileSystem.Primitives/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-6QOb2XFLch7bEc4lIcJH49nJN2HV+OC3fHDgsLVsBVBk3Y4hFAnOBGzJ2lUu7CyDDFo9IBWkSsnbkT6IBwwiMw==", "path": "system.io.filesystem.primitives/4.3.0", "hashPath": "system.io.filesystem.primitives.4.3.0.nupkg.sha512"}, "System.Linq/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-5DbqIUpsDp0dFftytzuMmc0oeMdQwjcP/EWxsksIz/w1TcFRkZ3yKKz0PqiYFMmEwPSWw+qNVqD7PJ889JzHbw==", "path": "system.linq/4.3.0", "hashPath": "system.linq.4.3.0.nupkg.sha512"}, "System.Linq.Expressions/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-PGKkrd2khG4CnlyJwxwwaWWiSiWFNBGlgXvJpeO0xCXrZ89ODrQ6tjEWS/kOqZ8GwEOUATtKtzp1eRgmYNfclg==", "path": "system.linq.expressions/4.3.0", "hashPath": "system.linq.expressions.4.3.0.nupkg.sha512"}, "System.Linq.Queryable/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-In1Bmmvl/j52yPu3xgakQSI0YIckPUr870w4K5+Lak3JCCa8hl+my65lABOuKfYs4ugmZy25ScFerC4nz8+b6g==", "path": "system.linq.queryable/4.3.0", "hashPath": "system.linq.queryable.4.3.0.nupkg.sha512"}, "System.Memory/4.5.5": {"type": "package", "serviceable": true, "sha512": "sha512-XIWiDvKPXaTveaB7HVganDlOCRoj03l+jrwNvcge/t8vhGYKvqV+dMv6G4SAX2NoNmN0wZfVPTAlFwZcZvVOUw==", "path": "system.memory/4.5.5", "hashPath": "system.memory.4.5.5.nupkg.sha512"}, "System.Memory.Data/1.0.2": {"type": "package", "serviceable": true, "sha512": "sha512-JGkzeqgBsiZwKJZ1IxPNsDFZDhUvuEdX8L8BDC8N3KOj+6zMcNU28CNN59TpZE/VJYy9cP+5M+sbxtWJx3/xtw==", "path": "system.memory.data/1.0.2", "hashPath": "system.memory.data.1.0.2.nupkg.sha512"}, "System.Numerics.Vectors/4.5.0": {"type": "package", "serviceable": true, "sha512": "sha512-QQTlPTl06J/iiDbJCiepZ4H//BVraReU4O4EoRw1U02H5TLUIT7xn3GnDp9AXPSlJUDyFs4uWjWafNX6WrAojQ==", "path": "system.numerics.vectors/4.5.0", "hashPath": "system.numerics.vectors.4.5.0.nupkg.sha512"}, "System.ObjectModel/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-bdX+80eKv9bN6K4N+d77OankKHGn6CH711a6fcOpMQu2Fckp/Ft4L/kW9WznHpyR0NRAvJutzOMHNNlBGvxQzQ==", "path": "system.objectmodel/4.3.0", "hashPath": "system.objectmodel.4.3.0.nupkg.sha512"}, "System.Reflection/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-KMiAFoW7MfJGa9nDFNcfu+FpEdiHpWgTcS2HdMpDvt9saK3y/G4GwprPyzqjFH9NTaGPQeWNHU+iDlDILj96aQ==", "path": "system.reflection/4.3.0", "hashPath": "system.reflection.4.3.0.nupkg.sha512"}, "System.Reflection.Emit/4.7.0": {"type": "package", "serviceable": true, "sha512": "sha512-VR4kk8XLKebQ4MZuKuIni/7oh+QGFmZW3qORd1GvBq/8026OpW501SzT/oypwiQl4TvT8ErnReh/NzY9u+C6wQ==", "path": "system.reflection.emit/4.7.0", "hashPath": "system.reflection.emit.4.7.0.nupkg.sha512"}, "System.Reflection.Emit.ILGeneration/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-59tBslAk9733NXLrUJrwNZEzbMAcu8k344OYo+wfSVygcgZ9lgBdGIzH/nrg3LYhXceynyvTc8t5/GD4Ri0/ng==", "path": "system.reflection.emit.ilgeneration/4.3.0", "hashPath": "system.reflection.emit.ilgeneration.4.3.0.nupkg.sha512"}, "System.Reflection.Emit.Lightweight/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-oadVHGSMsTmZsAF864QYN1t1QzZjIcuKU3l2S9cZOwDdDueNTrqq1yRj7koFfIGEnKpt6NjpL3rOzRhs4ryOgA==", "path": "system.reflection.emit.lightweight/4.3.0", "hashPath": "system.reflection.emit.lightweight.4.3.0.nupkg.sha512"}, "System.Reflection.Extensions/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-rJkrJD3kBI5B712aRu4DpSIiHRtr6QlfZSQsb0hYHrDCZORXCFjQfoipo2LaMUHoT9i1B7j7MnfaEKWDFmFQNQ==", "path": "system.reflection.extensions/4.3.0", "hashPath": "system.reflection.extensions.4.3.0.nupkg.sha512"}, "System.Reflection.Primitives/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-5RXItQz5As4xN2/YUDxdpsEkMhvw3e6aNveFXUn4Hl/udNTCNhnKp8lT9fnc3MhvGKh1baak5CovpuQUXHAlIA==", "path": "system.reflection.primitives/4.3.0", "hashPath": "system.reflection.primitives.4.3.0.nupkg.sha512"}, "System.Reflection.TypeExtensions/4.7.0": {"type": "package", "serviceable": true, "sha512": "sha512-VybpaOQQhqE6siHppMktjfGBw1GCwvCqiufqmP8F1nj7fTUNtW35LOEt3UZTEsECfo+ELAl/9o9nJx3U91i7vA==", "path": "system.reflection.typeextensions/4.7.0", "hashPath": "system.reflection.typeextensions.4.7.0.nupkg.sha512"}, "System.Resources.ResourceManager/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-/zrcPkkWdZmI4F92gL/TPumP98AVDu/Wxr3CSJGQQ+XN6wbRZcyfSKVoPo17ilb3iOr0cCRqJInGwNMolqhS8A==", "path": "system.resources.resourcemanager/4.3.0", "hashPath": "system.resources.resourcemanager.4.3.0.nupkg.sha512"}, "System.Runtime/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-JufQi0vPQ0xGnAczR13AUFglDyVYt4Kqnz1AZaiKZ5+GICq0/1MH/mO/eAJHt/mHW1zjKBJd7kV26SrxddAhiw==", "path": "system.runtime/4.3.0", "hashPath": "system.runtime.4.3.0.nupkg.sha512"}, "System.Runtime.Caching/6.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-E0e03kUp5X2k+UAoVl6efmI7uU7JRBWi5EIdlQ7cr0NpBGjHG4fWII35PgsBY9T4fJQ8E4QPsL0rKksU9gcL5A==", "path": "system.runtime.caching/6.0.0", "hashPath": "system.runtime.caching.6.0.0.nupkg.sha512"}, "System.Runtime.CompilerServices.Unsafe/6.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-/iUeP3tq1S0XdNNoMz5C9twLSrM/TH+qElHkXWaPvuNOt+99G75NrV0OS2EqHx5wMN7popYjpc8oTjC1y16DLg==", "path": "system.runtime.compilerservices.unsafe/6.0.0", "hashPath": "system.runtime.compilerservices.unsafe.6.0.0.nupkg.sha512"}, "System.Runtime.Extensions/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-guW0uK0fn5fcJJ1tJVXYd7/1h5F+pea1r7FLSOz/f8vPEqbR2ZAknuRDvTQ8PzAilDveOxNjSfr0CHfIQfFk8g==", "path": "system.runtime.extensions/4.3.0", "hashPath": "system.runtime.extensions.4.3.0.nupkg.sha512"}, "System.Runtime.Handles/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-OKiSUN7DmTWeYb3l51A7EYaeNMnvxwE249YtZz7yooT4gOZhmTjIn48KgSsw2k2lYdLgTKNJw/ZIfSElwDRVgg==", "path": "system.runtime.handles/4.3.0", "hashPath": "system.runtime.handles.4.3.0.nupkg.sha512"}, "System.Runtime.InteropServices/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-uv1ynXqiMK8mp1GM3jDqPCFN66eJ5w5XNomaK2XD+TuCroNTLFGeZ+WCmBMcBDyTFKou3P6cR6J/QsaqDp7fGQ==", "path": "system.runtime.interopservices/4.3.0", "hashPath": "system.runtime.interopservices.4.3.0.nupkg.sha512"}, "System.Security.AccessControl/5.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-dagJ1mHZO3Ani8GH0PHpPEe/oYO+rVdbQjvjJkBRNQkX4t0r1iaeGn8+/ybkSLEan3/slM0t59SVdHzuHf2jmw==", "path": "system.security.accesscontrol/5.0.0", "hashPath": "system.security.accesscontrol.5.0.0.nupkg.sha512"}, "System.Security.Cryptography.Cng/5.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-jIMXsKn94T9JY7PvPq/tMfqa6GAaHpElRDpmG+SuL+D3+sTw2M8VhnibKnN8Tq+4JqbPJ/f+BwtLeDMEnzAvRg==", "path": "system.security.cryptography.cng/5.0.0", "hashPath": "system.security.cryptography.cng.5.0.0.nupkg.sha512"}, "System.Security.Cryptography.ProtectedData/7.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-xSPiLNlHT6wAHtugASbKAJwV5GVqQK351crnILAucUioFqqieDN79evO1rku1ckt/GfjIn+b17UaSskoY03JuA==", "path": "system.security.cryptography.protecteddata/7.0.0", "hashPath": "system.security.cryptography.protecteddata.7.0.0.nupkg.sha512"}, "System.Security.Permissions/7.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-Vmp0iRmCEno9BWiskOW5pxJ3d9n+jUqKxvX4GhLwFhnQaySZmBN2FuC0N5gjFHgyFMUjC5sfIJ8KZfoJwkcMmA==", "path": "system.security.permissions/7.0.0", "hashPath": "system.security.permissions.7.0.0.nupkg.sha512"}, "System.Security.Principal.Windows/5.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-t0MGLukB5WAVU9bO3MGzvlGnyJPgUlcwerXn1kzBRjwLKixT96XV0Uza41W49gVd8zEMFu9vQEFlv0IOrytICA==", "path": "system.security.principal.windows/5.0.0", "hashPath": "system.security.principal.windows.5.0.0.nupkg.sha512"}, "System.Text.Encoding/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-BiIg+KWaSDOITze6jGQynxg64naAPtqGHBwDrLaCtixsa5bKiR8dpPOHA7ge3C0JJQizJE+sfkz1wV+BAKAYZw==", "path": "system.text.encoding/4.3.0", "hashPath": "system.text.encoding.4.3.0.nupkg.sha512"}, "System.Text.Encoding.CodePages/6.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-ZFCILZuOvtKPauZ/j/swhvw68ZRi9ATCfvGbk1QfydmcXBkIWecWKn/250UH7rahZ5OoDBaiAudJtPvLwzw85A==", "path": "system.text.encoding.codepages/6.0.0", "hashPath": "system.text.encoding.codepages.6.0.0.nupkg.sha512"}, "System.Text.Encoding.Extensions/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-YVMK0Bt/A43RmwizJoZ22ei2nmrhobgeiYwFzC4YAN+nue8RF6djXDMog0UCn+brerQoYVyaS+ghy9P/MUVcmw==", "path": "system.text.encoding.extensions/4.3.0", "hashPath": "system.text.encoding.extensions.4.3.0.nupkg.sha512"}, "System.Text.Encodings.Web/7.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-OP6umVGxc0Z0MvZQBVigj4/U31Pw72ITihDWP9WiWDm+q5aoe0GaJivsfYGq53o6dxH7DcXWiCTl7+0o2CGdmg==", "path": "system.text.encodings.web/7.0.0", "hashPath": "system.text.encodings.web.7.0.0.nupkg.sha512"}, "System.Text.Json/7.0.2": {"type": "package", "serviceable": true, "sha512": "sha512-/LZf/JrGyilojqwpaywb+sSz8Tew7ij4K/Sk+UW8AKfAK7KRhR6mKpKtTm06cYA7bCpGTWfYksIW+mVsdxPegQ==", "path": "system.text.json/7.0.2", "hashPath": "system.text.json.7.0.2.nupkg.sha512"}, "System.Text.RegularExpressions/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-RpT2DA+L660cBt1FssIE9CAGpLFdFPuheB7pLpKpn6ZXNby7jDERe8Ua/Ne2xGiwLVG2JOqziiaVCGDon5sKFA==", "path": "system.text.regularexpressions/4.3.0", "hashPath": "system.text.regularexpressions.4.3.0.nupkg.sha512"}, "System.Threading/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-VkUS0kOBcUf3Wwm0TSbrevDDZ6BlM+b/HRiapRFWjM5O0NS0LviG0glKmFK+hhPDd1XFeSdU1GmlLhb2CoVpIw==", "path": "system.threading/4.3.0", "hashPath": "system.threading.4.3.0.nupkg.sha512"}, "System.Threading.Tasks/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-LbSxKEdOUhVe8BezB/9uOGGppt+nZf6e1VFyw6v3DN6lqitm0OSn2uXMOdtP0M3W4iMcqcivm2J6UgqiwwnXiA==", "path": "system.threading.tasks/4.3.0", "hashPath": "system.threading.tasks.4.3.0.nupkg.sha512"}, "System.Threading.Tasks.Extensions/4.5.4": {"type": "package", "serviceable": true, "sha512": "sha512-zteT+G8xuGu6mS+mzDzYXbzS7rd3K6Fjb9RiZlYlJPam2/hU7JCBZBVEcywNuR+oZ1ncTvc/cq0faRr3P01OVg==", "path": "system.threading.tasks.extensions/4.5.4", "hashPath": "system.threading.tasks.extensions.4.5.4.nupkg.sha512"}, "System.Windows.Extensions/7.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-bR4qdCmssMMbo9Fatci49An5B1UaVJZHKNq70PRgzoLYIlitb8Tj7ns/Xt5Pz1CkERiTjcVBDU2y1AVrPBYkaw==", "path": "system.windows.extensions/7.0.0", "hashPath": "system.windows.extensions.7.0.0.nupkg.sha512"}, "System.Xml.ReaderWriter/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-GrprA+Z0RUXaR4N7/eW71j1rgMnEnEVlgii49GZyAjTH7uliMnrOU3HNFBr6fEDBCJCIdlVNq9hHbaDR621XBA==", "path": "system.xml.readerwriter/4.3.0", "hashPath": "system.xml.readerwriter.4.3.0.nupkg.sha512"}, "System.Xml.XPath/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-v1JQ5SETnQusqmS3RwStF7vwQ3L02imIzl++sewmt23VGygix04pEH+FCj1yWb+z4GDzKiljr1W7Wfvrx0YwgA==", "path": "system.xml.xpath/4.3.0", "hashPath": "system.xml.xpath.4.3.0.nupkg.sha512"}, "TinyHelpers/2.0.28": {"type": "package", "serviceable": true, "sha512": "sha512-uEdYOAGo7ykfePH4RYw07Gm4toah03sMZnNpTDt3kxGGK1xHyOs08y7X19eJFKi2VxPlL/fquvROAUjDCgVX+w==", "path": "tinyhelpers/2.0.28", "hashPath": "tinyhelpers.2.0.28.nupkg.sha512"}, "TinyHelpers.EntityFrameworkCore/2.0.14": {"type": "package", "serviceable": true, "sha512": "sha512-W4/lHSUFeb4+k2xvHS3ES9vacIGxz4L568LB5ncbrW3cXYymxcep6KO3+4ifkzo54jxQvfP/BaTroj40rrg5bg==", "path": "tinyhelpers.entityframeworkcore/2.0.14", "hashPath": "tinyhelpers.entityframeworkcore.2.0.14.nupkg.sha512"}, "Z.EntityFramework.Extensions.EFCore/7.20.0": {"type": "package", "serviceable": true, "sha512": "sha512-GfEz8vR2u9e7wVI1LcajSDmXLf8bs7dBsfl6+kGHy0OWb+rrxWTReE14HeUsOJNEFX4xid6O57MVVXMqtfbnfg==", "path": "z.entityframework.extensions.efcore/7.20.0", "hashPath": "z.entityframework.extensions.efcore.7.20.0.nupkg.sha512"}, "Z.EntityFramework.Plus.EFCore/7.20.0": {"type": "package", "serviceable": true, "sha512": "sha512-dFcuLb7/L0EOqtJLN/UtXoGd0MNMoGInVRx0YYCcGKHPLiZO3pR1/z1P3pVkVnzqu5QhIAS40w23Zs1OBvPpww==", "path": "z.entityframework.plus.efcore/7.20.0", "hashPath": "z.entityframework.plus.efcore.7.20.0.nupkg.sha512"}, "Z.Expressions.Eval/5.0.2": {"type": "package", "serviceable": true, "sha512": "sha512-fe/qxvBaTW/+vfFj8mzEj/wgagOKIx0tUzYPtB3ao0Td3qedoIzhyqlOPmeZ50Cigk3MKPD5wGi11kyYDvtS1w==", "path": "z.expressions.eval/5.0.2", "hashPath": "z.expressions.eval.5.0.2.nupkg.sha512"}, "BlueTape.Common/1.0.0": {"type": "project", "serviceable": false, "sha512": ""}, "BlueTape.Domain/1.0.0": {"type": "project", "serviceable": false, "sha512": ""}}}