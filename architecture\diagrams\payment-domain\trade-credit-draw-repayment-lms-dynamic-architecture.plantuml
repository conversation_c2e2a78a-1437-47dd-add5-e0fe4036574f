@startuml

title Trade Credit Components Draw Repayment II.\n(not all components are shown)

participant "LMS" as lms #LightGray
queue "lmsPaymentsQueue" as lmspqueue #LightSalmon
participant "Payment\nService" as pserv #SkyBlue
participant "Aion" as aion #Orange

autonumber

== Draw repayment events ==

aion -> pserv
pserv -> lmspqueue : Send payment\n""PROCESSING""\n""SUCCESS""\n""FAIL""
lmspqueue -> lms : Read payment

@enduml