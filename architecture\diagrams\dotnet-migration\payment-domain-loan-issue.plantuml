@startuml Loan Issue
title Loan Issue static architecture (component diagram)
!include <awslib/AWSCommon>

' Uncomment the following line to create simplified view
!include <awslib/AWSSimplified>

!include <awslib/Compute/Lambda>

skinparam responseMessageBelowArrow true

component "deIssueLoan\n//Lambda//" as lambdaIssueLoan #Orange
component "loanCreation\n//Lambda//" as lambdaLoanCreation #Orange
component "internalTransfer\n//Lambda//" as lambdaInternalTransfer #Orange
component "merchantTransfer\n//Lambda//" as lambdaMerchantTransfer #Orange
component "LMS" as lms #PaleTurquoise
component "LMS" as lms2 #PaleTurquoise
queue "NetSuite" as sqsNetsuite #HotPink
component "loanActivation\n//Lambda//" as lambdaLoanActivation  #Orange
component "CBW" as cbw #LightGrey
component "CBW" as cbw2 #LightGrey

lambdaIssueLoan -r-> lambdaLoanCreation
lambdaIssueLoan -d-> cbw2 : ADD_CARD\nADD_CARD_HOLDER
lambdaLoanCreation -r-> lambdaInternalTransfer
lambdaLoanCreation -d-> lms2
lambdaInternalTransfer -r-> lambdaMerchantTransfer
lambdaInternalTransfer -d-> cbw : GL_TRANSFER
lambdaMerchantTransfer -r-> lambdaLoanActivation
lambdaMerchantTransfer -d-> sqsNetsuite
lambdaMerchantTransfer -d-> cbw : ACH_OUT
lambdaLoanActivation -d-> lms

@enduml