import sendUserRequest from '../common/send-user-request';

export async function getBankAccounts(session: string, challenge: string) {
    const endpoint = `v1/company/bank-accounts`;
    try {
        const response = await sendUserRequest('get', endpoint, session, challenge);
        const bodyString = response.toString();
        const bodyJSON = JSON.parse(bodyString);
        const bankAccountsList: any = [];
        for (let i = 0; i < bodyJSON.bankAccounts.length; i++) {
            bankAccountsList[i] = bodyJSON.bankAccounts[i]._id;
        }
        return bankAccountsList;
    } catch (error) {
        console.log(error);
        return error;
    }
}
