import type { TFunction } from 'i18next'
import { isNumber } from 'lodash'

import type { IDrawApplicationDetailedAutomatedApprovalDetails } from '@/lib/redux/api/draw-application/types'
import { DrawApplicationAutomatedApprovalFailureReason } from '@/globals/types'
import { currencyMask } from '@/globals/utils'

export const getDrawApplicationManualApprovalDescription = (
  automatedApprovalResult: IDrawApplicationDetailedAutomatedApprovalDetails | null,
  t: TFunction,
) => {
  if (!automatedApprovalResult) {
    return t('na')
  }

  if (
    automatedApprovalResult.reason ===
      DrawApplicationAutomatedApprovalFailureReason.DAILY_AMOUNT_OVERRUN &&
    isNumber(automatedApprovalResult.dailyAmountLimit)
  ) {
    return t(
      'drawApplication.page.detailed.alert.manualApproval.dailyAmountOverrun',
      { amount: currencyMask(automatedApprovalResult.dailyAmountLimit) },
    )
  }

  if (
    automatedApprovalResult.reason ===
      DrawApplicationAutomatedApprovalFailureReason.WEEKLY_AMOUNT_OVERRUN &&
    isNumber(automatedApprovalResult.weeklyAmountLimit)
  ) {
    return t(
      'drawApplication.page.detailed.alert.manualApproval.weeklyAmountOverrun',
      { amount: currencyMask(automatedApprovalResult.weeklyAmountLimit) },
    )
  }

  if (
    automatedApprovalResult.reason ===
      DrawApplicationAutomatedApprovalFailureReason.DRAW_LIMIT_OVERRUN &&
    isNumber(automatedApprovalResult.drawLimit)
  ) {
    return t(
      'drawApplication.page.detailed.alert.manualApproval.drawLimitOverrun',
      { amount: currencyMask(automatedApprovalResult.drawLimit) },
    )
  }

  return t('na')
}
