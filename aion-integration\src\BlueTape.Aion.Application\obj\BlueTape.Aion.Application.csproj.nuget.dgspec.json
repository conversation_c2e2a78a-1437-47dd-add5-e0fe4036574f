{"format": 1, "restore": {"C:\\Users\\<USER>\\source\\repos\\BlueTape\\aion-integration\\src\\BlueTape.Aion.Application\\BlueTape.Aion.Application.csproj": {}}, "projects": {"C:\\Users\\<USER>\\source\\repos\\BlueTape\\aion-integration\\src\\BlueTape.Aion.Application\\BlueTape.Aion.Application.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\aion-integration\\src\\BlueTape.Aion.Application\\BlueTape.Aion.Application.csproj", "projectName": "BlueTape.Aion.Application", "projectPath": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\aion-integration\\src\\BlueTape.Aion.Application\\BlueTape.Aion.Application.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\aion-integration\\src\\BlueTape.Aion.Application\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\source\\repos\\BlueTape\\aion-integration\\src\\NuGet.Config", "C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net8.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}, "https://nuget.pkg.github.com/bluetape-org/index.json": {}}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "projectReferences": {"C:\\Users\\<USER>\\source\\repos\\BlueTape\\aion-integration\\src\\BlueTape.Aion.DataAccess.External\\BlueTape.Aion.DataAccess.External.csproj": {"projectPath": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\aion-integration\\src\\BlueTape.Aion.DataAccess.External\\BlueTape.Aion.DataAccess.External.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "dependencies": {"AWSSDK.SimpleNotificationService": {"target": "Package", "version": "[3.7.300.25, )"}, "AutoMapper": {"target": "Package", "version": "[12.0.1, )"}, "AutoMapper.Extensions.Microsoft.DependencyInjection": {"target": "Package", "version": "[12.0.1, )"}, "BlueTape.AWSS3": {"target": "Package", "version": "[1.1.3, )"}, "BlueTape.AzureKeyVault": {"target": "Package", "version": "[1.0.3, )"}, "BlueTape.CompanyClient": {"target": "Package", "version": "[1.0.51, )"}, "BlueTape.Integrations.Aion": {"target": "Package", "version": "[1.0.20, )"}, "BlueTape.Integrations.Aion.AzureTableStorage": {"target": "Package", "version": "[1.1.3, )"}, "BlueTape.SNS": {"target": "Package", "version": "[1.0.2, )"}, "BlueTape.ServiceBusMessaging": {"target": "Package", "version": "[1.0.8, )"}, "BlueTape.Utilities": {"target": "Package", "version": "[1.4.6, )"}, "System.IdentityModel.Tokens.Jwt": {"target": "Package", "version": "[8.0.2, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.300/PortableRuntimeIdentifierGraph.json"}}}, "C:\\Users\\<USER>\\source\\repos\\BlueTape\\aion-integration\\src\\BlueTape.Aion.DataAccess.External\\BlueTape.Aion.DataAccess.External.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\aion-integration\\src\\BlueTape.Aion.DataAccess.External\\BlueTape.Aion.DataAccess.External.csproj", "projectName": "BlueTape.Aion.DataAccess.External", "projectPath": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\aion-integration\\src\\BlueTape.Aion.DataAccess.External\\BlueTape.Aion.DataAccess.External.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\aion-integration\\src\\BlueTape.Aion.DataAccess.External\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\source\\repos\\BlueTape\\aion-integration\\src\\NuGet.Config", "C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net8.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}, "https://nuget.pkg.github.com/bluetape-org/index.json": {}}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "projectReferences": {"C:\\Users\\<USER>\\source\\repos\\BlueTape\\aion-integration\\src\\BlueTape.Aion.DataAccess.MongoDB\\BlueTape.Aion.DataAccess.MongoDB.csproj": {"projectPath": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\aion-integration\\src\\BlueTape.Aion.DataAccess.MongoDB\\BlueTape.Aion.DataAccess.MongoDB.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "dependencies": {"Portable.BouncyCastle": {"target": "Package", "version": "[1.9.0, )"}, "System.IdentityModel.Tokens.Jwt": {"target": "Package", "version": "[8.0.2, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.300/PortableRuntimeIdentifierGraph.json"}}}, "C:\\Users\\<USER>\\source\\repos\\BlueTape\\aion-integration\\src\\BlueTape.Aion.DataAccess.MongoDB\\BlueTape.Aion.DataAccess.MongoDB.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\aion-integration\\src\\BlueTape.Aion.DataAccess.MongoDB\\BlueTape.Aion.DataAccess.MongoDB.csproj", "projectName": "BlueTape.Aion.DataAccess.MongoDB", "projectPath": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\aion-integration\\src\\BlueTape.Aion.DataAccess.MongoDB\\BlueTape.Aion.DataAccess.MongoDB.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\aion-integration\\src\\BlueTape.Aion.DataAccess.MongoDB\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\source\\repos\\BlueTape\\aion-integration\\src\\NuGet.Config", "C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net8.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}, "https://nuget.pkg.github.com/bluetape-org/index.json": {}}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "projectReferences": {"C:\\Users\\<USER>\\source\\repos\\BlueTape\\aion-integration\\src\\BlueTape.Aion.Domain\\BlueTape.Aion.Domain.csproj": {"projectPath": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\aion-integration\\src\\BlueTape.Aion.Domain\\BlueTape.Aion.Domain.csproj"}, "C:\\Users\\<USER>\\source\\repos\\BlueTape\\aion-integration\\src\\BueTape.Aion.Infrastructure\\BueTape.Aion.Infrastructure.csproj": {"projectPath": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\aion-integration\\src\\BueTape.Aion.Infrastructure\\BueTape.Aion.Infrastructure.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "dependencies": {"AutoMapper": {"target": "Package", "version": "[12.0.1, )"}, "AutoMapper.Extensions.Microsoft.DependencyInjection": {"target": "Package", "version": "[12.0.1, )"}, "BlueTape.MongoDB": {"target": "Package", "version": "[1.1.30, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.300/PortableRuntimeIdentifierGraph.json"}}}, "C:\\Users\\<USER>\\source\\repos\\BlueTape\\aion-integration\\src\\BlueTape.Aion.Domain\\BlueTape.Aion.Domain.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\aion-integration\\src\\BlueTape.Aion.Domain\\BlueTape.Aion.Domain.csproj", "projectName": "BlueTape.Aion.Domain", "projectPath": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\aion-integration\\src\\BlueTape.Aion.Domain\\BlueTape.Aion.Domain.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\aion-integration\\src\\BlueTape.Aion.Domain\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\source\\repos\\BlueTape\\aion-integration\\src\\NuGet.Config", "C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net8.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}, "https://nuget.pkg.github.com/bluetape-org/index.json": {}}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "dependencies": {"BlueTape.Integrations.Aion.Infrastructure": {"target": "Package", "version": "[1.0.20, )"}, "BlueTape.MongoDB": {"target": "Package", "version": "[1.1.30, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.300/PortableRuntimeIdentifierGraph.json"}}}, "C:\\Users\\<USER>\\source\\repos\\BlueTape\\aion-integration\\src\\BueTape.Aion.Infrastructure\\BueTape.Aion.Infrastructure.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\aion-integration\\src\\BueTape.Aion.Infrastructure\\BueTape.Aion.Infrastructure.csproj", "projectName": "BueTape.Aion.Infrastructure", "projectPath": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\aion-integration\\src\\BueTape.Aion.Infrastructure\\BueTape.Aion.Infrastructure.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\aion-integration\\src\\BueTape.Aion.Infrastructure\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\source\\repos\\BlueTape\\aion-integration\\src\\NuGet.Config", "C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net8.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}, "https://nuget.pkg.github.com/bluetape-org/index.json": {}}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "projectReferences": {"C:\\Users\\<USER>\\source\\repos\\BlueTape\\aion-integration\\src\\BlueTape.Aion.Domain\\BlueTape.Aion.Domain.csproj": {"projectPath": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\aion-integration\\src\\BlueTape.Aion.Domain\\BlueTape.Aion.Domain.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "dependencies": {"BlueTape.Common.ExceptionHandling": {"target": "Package", "version": "[1.0.6, )"}, "BlueTape.Integrations.Aion.Infrastructure": {"target": "Package", "version": "[1.0.20, )"}, "BlueTape.SNS": {"target": "Package", "version": "[1.0.0, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.300/PortableRuntimeIdentifierGraph.json"}}}}}