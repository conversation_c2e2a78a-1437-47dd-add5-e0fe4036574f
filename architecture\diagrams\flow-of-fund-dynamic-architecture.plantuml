@startuml Flow of Fund (Before SPV) via ACH method (OUTDATED)

title Flow of Fund (Before SPV) via ACH method (OUTDATED)

participant "External\nAccount" as ea #LightGray
participant "Merchant's\nAccount" as merch #LightGray
participant "Bo<PERSON><PERSON>'s\nAccount" as borrow #LightGray
participant "BlueTape\nOperating" as bto #SkyBlue
participant "BlueTape\nLoan FBO" as btfbo #SkyBlue
participant "BlueTape\nRevenue" as btrev #SkyBlue
participant "BlueTape\nServicing" as btsrv #SkyBlue
participant "GL Loan held\nfor sale" as gl #Orange
participant "CBW Loans Held\nfor Investment GL" as cwbinv #Orange
participant "GL CBW Fee" as cwbfee #Orange

autonumber

== Prior to issuing loans ==
ea -> bto : Manual transfer\n//Funding BT program//
== Day 0 issuing loan ==
gl -> btfbo : Internal transfer\n//Funding loan by CBW//
btfbo -> merch : ACH_OUT\n//Originating loan//
== Day 3 BT buys loan from CBW ==
bto -> gl : Internal transfer\n//Buying loan from CBW//
== Day 30 to 90 (due dates) ==
loop Loans
    borrow -> btsrv : ACH_PULL\n//Collection//
    btsrv -> btrev : Internal transfer\n//BT Revenue//
    btsrv -> bto : Internal transfer\n//Recycling funds//
end
opt Will be not implemented
    bto --> cwbinv : Internal transfer
end
bto -> merch : ACH_OUT\n//Pay the merchant final payment//
bto -> cwbfee : Internal transfer by CBW\n//CWB fees and interest//
== Returning investors' capital ==
bto -> ea : Manual transfer

@enduml