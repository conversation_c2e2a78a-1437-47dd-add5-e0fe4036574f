openapi: "3.0.0"
info:
  version: "0.0.1"
  title: "Credit & Draw API (as a part of LMS)"
  description: |
    API definition of Credit functions
servers:
  - url: TBD-Dev
    description: Development server
  - url: TBD-Prod
    description: Production server
paths:
  /credits:
    post:
      tags:
        - credits
      summary: Adds a new Line of Credit for a specified company
      description: Adds a new Line of Credit for a specified company
      operationId: addCredit
      requestBody:
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/CreateCredit"
      responses:
        201:
          description: The credit was created
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Credit'
        400:
          description: Bad request
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
    get:
      tags:
        - credits
      summary: Gets all credits by various filters
      description: Gets all credits by various filters
      operationId: getCRedits
      parameters:
        - name: companyId
          description: The company id
          in: query
          required: false
          schema:
            type: string
        - name: detailed
          description: Is the response should contain detailed data
          in: query
          required: false
          schema:
            type: boolean
      responses:
        200:
          description: The credits
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/Credit'
        400:
          description: Bad request
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
  /credits/{id}:
    get:
      tags:
        - credits
      summary: Gets a credit by id
      description: Gets a credit by id
      operationId: getCreditById
      parameters:
        - name: id
          description: The id of credit
          in: path
          required: true
          schema:
            type: string
        - name: detailed
          description: Is the response should contain detailed data
          in: query
          required: false
          schema:
            type: boolean
      responses:
        200:
          description: The credit
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Credit'
        400:
          description: Bad request
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
  /credits/{id}/status:
    patch: 
      tags:
      - credits
      summary: Updates credit status
      description: Updates credit status
      operationId: updateCreditStatusById
      parameters:
        - name: id
          description: The id of credit
          in: path
          required: true
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/PatchCredit"
      responses:
        200:
          description: The patched credit
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Credit'
        400:
          description: Bad request
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
  /credits/company:
    post:
      tags:
        - credits
      summary: Gets credits by company id array
      description: Gets credits by company id array
      operationId: getCreditsByCompanyIdArray
      requestBody:
        content:
          application/json:
            schema:
              type: array              
              items:
                type: string
      responses:
        200:
          description: The credits
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/Credit'
        404:
          description: Not found
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
  /credits/company/{id}:
    get:
      tags:
        - credits
      summary: Gets a credit by company id (only 1 exists now)
      description: Gets a credit by company id (only 1 exists now)
      operationId: getCreditByCompanyId
      parameters:
        - name: id
          description: The id of company
          in: path
          required: true
          schema:
            type: string
        - name: detailed
          description: Is the response should contain detailed data
          in: query
          required: false
          schema:
            type: boolean
            default: false
      responses:
        200:
          description: The credit
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Credit'
        400:
          description: Bad request
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
  /admin/credits/{id}/details:
    patch: 
      tags:
      - credits Admin
      summary: Updates credit details (admin function)
      description: Updates credit details (admin function) All fields are optional. Which field is sent, that field to update.
      operationId: updateCreditDetailsById
      parameters:
        - name: id
          description: The id of credit
          in: path
          required: true
          schema:
            type: string
        - name: userId
          description: Identifier of the user who updated the credit details
          example: 62c5e900cd65d31d3f25c34c
          in: header
          required: true
          schema:
            type: string
            nullable: false
      requestBody:
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/PatchCreditDetails"
      responses:
        200:
          description: The patched credit
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Credit'
        400:
          description: Bad request
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
  /draws/approved:
    post: 
      tags:
        - draw Events Queue (read by LFS)
      summary: Draw.Approved queue event payload
      description: Draw.Approved queue event payload
      operationId: drawApproved
      requestBody:
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/DrawApproved"
      responses:
        200:
          description: No response (queue event)
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/EmptyResponse'
        400:
          description: Bad request
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
  /loans/payment/disbursement:
    post: 
      tags:
        - payments Queue (read by LMS)
      summary: Draw.Disbursement payment event from Payment Domain
      description: Draw.Disbursement payment event from Payment Domain
      operationId: drawDisbursed
      requestBody:
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/DrawDisbursed"
      responses:
        200:
          description: No response (queue event)
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/EmptyResponse'
        400:
          description: Bad request
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
  /loanTemplates:
    get:
      tags:
        - loanTemplates
      summary: Gets available loan templates (payment plans)
      description: Gets available loan templates (payment plans)
      operationId: getLoanTemplates
      parameters: 
        - name: id
          description: The loan template id
          in: query
          required: false
          schema:
            type: string
        - name: isDefaultForAutoTradeCredit
          description: Is the template default for auto trade credit
          in: query
          required: false
          schema:
            type: boolean
      responses:
        200:
          description: The array of loan templates
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/LoanTemplate'
        400:
          description: Bad request
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
  /loans:
    get: 
      tags:
        - loan
      summary: Gets loans by different query params (existing function)
      description: Gets loans by different query params (existing function)
      operationId: getLoans
      parameters: 
        - name: id
          description: The loan id
          in: query
          required: false
          schema:
            type: string
        - name: fromDate
          description: The last payment from date
          in: query
          required: false
          schema:
            type: string
            format: date
        - name: toDate
          description: The last payment to date
          in: query
          required: false
          schema:
            type: string
            format: date
        - name: showLateOnly
          description: Show late loans only
          in: query
          required: false
          schema:
            type: boolean
        - name: einHash
          description: The company ein hash
          in: query
          required: false
          schema:
            type: string
        - name: loanStatus
          description: The loan status
          in: query
          required: false
          schema:
            type: string
            enum:
              - Created
              - Started
              - Pending
              - Canceled
              - Closed
              - Defaulted
              - Recovered
              - Refinanced
        - name: projectId
          description: The project Id
          in: query
          required: false
          schema:
            type: string
          example: 95a373a7d93b2602
        - name: merchantOrInvoiceNumber
          description: Search for merchant name or invoice number
          in: query
          required: false
          schema:
            type: string
          example: Best
      responses:
        200:
          description: The array of loans
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/Loan'
        404:
          description: Not found
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
    post: 
      tags:
        - loan
      summary: Creates a new loan (existing function, but needs to be updated)
      description: Creates a new loan (existing function, but needs to be updated)
      operationId: createLoan
      requestBody:
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/CreateLoan"
      responses:
        201:
          description: The created loan
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Loan'
        400:
          description: Bad request
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
  /loans/{id}:
    get:
      tags:
        - loan
      summary: Get a loan by id (for compatibility, existing function)
      description: Get a loan by id (for compatibility, existing function)
      operationId: getLoan
      parameters:
        - name: id
          description: The loan id
          in: path
          required: true
          schema:
            type: string
      responses:
        200:
          description: The loan
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Loan'
        404:
          description: Not found
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
    patch:
      tags:
        - loan
      summary: Change loan status (existing function)
      description: Change loan status (existing function)
      operationId: changeLoanStatus
      parameters:
        - name: id
          description: The loan id
          in: path
          required: true
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/PatchLoanStatus"
      responses:
        200:
          description: The updated loan
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Loan'
        400:
          description: Bad request
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
  /loans/{id}/history:
    get:
      tags:
        - loan History
      summary: Get loan history
      description: Get loan history
      operationId: getLoanHistory
      parameters:
        - name: id
          description: The loan id
          in: path
          required: true
          schema:
            type: string
      responses:
        200:
          description: The loan history array
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/LoanHistoryItem'
        400:
          description: Bad request
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
  /admin/loans/{id}/autoCollection:
    patch:
      tags:
        - draw (admin functions)
      summary: Turns off/on the loan repayment autocollection
      description: Turns off/on the loan repayment autocollection
      operationId: setRepaymentAutoCollection
      parameters:
        - name: id
          description: The loan id
          in: path
          required: true
          schema:
            type: string
        - name: userId
          description: Identifier of the user who canceled/set auto collection
          example: 62c5e900cd65d31d3f25c34c
          in: header
          required: true
          schema:
            type: string
            nullable: false
      requestBody:
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/PatchRepaymentAutoCollection"
      responses:
        200:
          description: The modified loan
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Loan'
        404:
          description: Not found
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
  /draws/{id}/cancelAutoTradeCredit:
    patch: 
      tags:
        - draw (borrower functions)
      summary: Cancels auto issued draw (one way update, no request body)
      description: Cancels auto issued draw (one way update, no request body)
      operationId: cancelAutoIssueDraw
      parameters: 
        - name: id
          description: The id of draw
          in: path
          required: true
          schema:
            type: string
        - name: userId
          description: Identifier of the user who canceled draw
          example: 62c5e900cd65d31d3f25c34c
          in: header
          required: true
          schema:
            type: string
            nullable: false
      requestBody:
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/EmptyBody"
      responses:
        200:
          description: The canceled loan
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Loan'
        404:
          description: Not found
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
components:
  schemas:
    ErrorResponse:
      type: object
      required:
        - statusCode
        - message
        - errorDescription
      properties:
        statusCode:
          description: HTTP status code corresponding to the error.
          type: integer
          example: 400
        message:
          description: A human readable error message.
          type: string
          example: Invalid model.
        errorDescription:
          description: Detailed information.
          type: string
    Credit:
      allOf:
        - type: object
          required:
            - id
          properties:
            id:
              type: string
              format: guid
              description: Id of credit
              example: d3a5d889-47e4-4321-b87b-f816ce5e7b6e
            createdAt:
              type: string
              format: date-time
            updatedAt:
              type: string
              format: date-time
        - $ref: '#/components/schemas/CreateCredit'
        - type: object
          properties:
            creditDetails:
              $ref: '#/components/schemas/CreditDetails'
    CreditWithoutDetails:
      allOf:
        - type: object
          required:
            - id
          properties:
            id:
              type: string
              format: guid
              description: Id of credit
              example: d3a5d889-47e4-4321-b87b-f816ce5e7b6e
            createdAt:
              type: string
              format: date-time
            updatedAt:
              type: string
              format: date-time
        - $ref: '#/components/schemas/CreateCredit'
    CreateCredit:
      type: object
      required: 
        - companyId
        - creditApplicationId
        - startDate
        - creditLimit
        - currency
        - purchaseType
        - revenueFallPercentage
      properties:
        companyId:
          type: string
        creditApplicationId:
          type: string
        startDate:
          type: string
          format: date
        closeDate:
          type: string
          format: date
          nullable: true
        creditLimit:
          type: number
          format: decimal
          example: 100000.00
          pattern: '[0-9]*\.[0-9]{2}'
          multipleOf: 0.01
          description: The allowed total credit amount for a customer.
        currency:
          type: string
          example: USD
        status:
          type: string
          enum:
            - active
            - pastDue
            - expired
            - closed
          default: active
        purchaseType:
          type: string
          enum:
            - inventory
            - project
            - both
        pastDueDays:
          type: number
          format: int64
        revenueFallPercentage:
          type: number
          description: When the customer revenue drops under this value, will be on hold
    PatchCredit:
      type: object
      properties:
        newStatus:
          type: string
          enum:
            - active
            - pastDue
            - expired
            - closed
        pastDueDays:
          type: number
          format: int32
    CreditDetails:
      type: object
      properties:
        numberOfDraws:
          type: number
          format: int32
          example: 5
          description: Calculated field. Count of all draws for this credit.
        availableCredit:
          type: number
          format: decimal
          example: 5500.00
          pattern: '[0-9]*\.[0-9]{2}'
          multipleOf: 0.01
          description: Calculated field. The total remaining amount available on the credit (creditLimit - active loans' principalBalance)
        usedCreditPercentage:
          type: number
          example: 66.6
          pattern: '[0-9]*\.[0-9]{2}'
          multipleOf: 0.01
          description: Calculated field. Percent ratio of available and used credit. 100 if all is used, zero if nothing.
        businessDaysLate:
          type: number
          format: int32
          example: 32
          description: Calculated field. The maximum of business days late for a draw in this credit.
        lateAmount:
          type: number
          example: 443.26
          pattern: '[0-9]*\.[0-9]{2}'
          multipleOf: 0.01
          description: Calculated field. The sum amount of all late draw amounts for this credit.
        principalBalance:
          type: number
          example: 4000.00
          pattern: '[0-9]*\.[0-9]{2}'
          multipleOf: 0.01
          description: Calculated field. The sum amount of all draws principal amount in this credit.
        drawOutstandingAmount:
          type: number
          example: 4500.00
          pattern: '[0-9]*\.[0-9]{2}'
          multipleOf: 0.01
          description: Calculated field. The sum amount of all draws outstanding amount in this credit.
        processingAmount:
          type: number
          example: 500.00
          pattern: '[0-9]*\.[0-9]{2}'
          multipleOf: 0.01
          description: Calculated field. The sum amount of all draws processing amount in this credit.
        totalPaid:
          type: number
          example: 3500.00
          pattern: '[0-9]*\.[0-9]{2}'
          multipleOf: 0.01
          description: Calculated field. The sum amount of all draws successfully paid amount in this credit.
        totalDrawAmount:
          type: number
          example: 3500.00
          pattern: '[0-9]*\.[0-9]{2}'
          multipleOf: 0.01
          description: Calculated field. The sum amount of all active draws installments and loan fees, but no penalties in this credit.
        isAnyReceivableLate:
          type: boolean
          example: false
          description: Calculated field. Is any active draw late with a receivable in this credit.
        isFullyPaid:
          type: boolean
          example: false
          description: Calculated field. Is this credit has 1 draw at least and all of them are paid.
        oldestDueOrPastDueDate:
          type: string
          format: date
          example: 2024-01-01
          description: Calculated field. The oldest due or past due date of active draws in this credit.
        totalDailyPenaltyInterest:
          type: number
          example: 112.88
          pattern: '[0-9]*\.[0-9]{2}'
          multipleOf: 0.01
          description: Calculated field. The sum amount of penalty interests of active draws in this credit.
        hadPenalty:
          type: boolean
          example: false
          description: Calculated field. Is any active draw has a penalty (paid or unpaid) in this credit.
    PatchCreditDetails:
      type: object
      properties:
        creditLimit:
          type: number
        purchaseType:
          type: string
          enum:
            - inventory
            - project
            - both
        revenueFallPercentage:
          type: number
        notes:
          type: string
    EmptyBody:
      type: object
      nullable: true
    EmptyResponse:
      type: object
      nullable: true
    DrawApproved:
      type: object
      properties:
        event:
          type: string
          example: Draw.Approved
        blueTapeCorrelationId:
          type: string
          example: 7fca978e-44bb-487a-a80a-a6bffa0b8ec6
        createdAt:
          type: string
          format: date-time
        createdBy:
          type: string
          example: BlueTape.OnBoardingService
        details:
          $ref: "#/components/schemas/DrawApprovedDetails"
    DrawApprovedDetails:
      type: object
      properties:
        drawApprovalId:
          type: string
          example: b326a2b811941b50
    PayableItem:
      type: object
      required: 
        - id
        - amount
      properties:
        id:
          type: string
          example: 705e37a2-053c-4dd5-ac32-71da27920cc8
        amount:
          type: number
          format: double
          example: 5000
    LoanTemplate:
      type: object
      properties:
        id:
          type: string
          example: bdafd605-fb56-4d32-9c9b-3b58c174dd79
        createdAt:
          type: string
          format: date-time
        updatedAt:
          type: string
          format: date-time
        loanFeePercentage:
          type: number
          example: 2
        installmentsNumber:
          type: number
          example: 13
        paymentDelayInDays:
          type: number
          example: 30
        paymentIntervalInDays:
          type: number
          example: 7
        minimumLateFeeAmount:
          type: number
          example: 35
        gracePeriodInDays:
          type: number
          example: 3
        lateFeePercentage:
          type: number
          example: 0.01
        earlyPayPeriod:
          type: number
          example: 0
        totalDurationInDays:
          type: number
          example: 120
        lateFeeCollectionDelayInDays:
          type: number
          example: 1
        penaltyInterestTriggerRule:
          type: string
          enum:
            - twoTimesThreeDaysOrOneTimeSevenDaysRule
        code:
          type: string
          example: 120vc
        name:
          type: string
          example: Virtual Card 120 days
        status:
          type: string
          enum:
            - active
            - inactive
          example: active
          default: active
        type:
          type: string
          enum:
            - draw
            - virtualcard
            - nosupplier
          example: virtualcard
          default: draw
        isDefaultForAutoTradeCredit:
          type: boolean
          default: false
          description: Only 1 could be true
    CreateLoan:
      type: object
      required: 
        - companyId
        - amount
        - loanTemplateId
      properties:
        companyId:
          type: string
          example: 8a837a7f855feed0
          description: The borrower company id
        amount:
          type: number
          format: double
          example: 5000.00
          pattern: '[0-9]*\.[0-9]{2}'
          multipleOf: 0.01
          description: The draw amount. All attached payables amount.
        loanTemplateId:
          type: string
          example: 0511fa00-94ca-4466-aefb-d7695d1fa28f
          description: The loan payment plan, the loan template id
        merchantId:
          type: string
          example: 5083c5e3-a220-47f1-8730-396ed0ccacee
          nullable: true
          description: The supplier, the merchant id
        loanPricingPackage:
          type: string
          example: 11a22af9-cc6f-4432-a379-ed01e61e6479
          nullable: true
          description: The merchant loan pricing package id. Loan or card package to fill, not both.
        cardPricingPackage:
          type: string
          example: c490e41c-3aa9-4d2c-91ae-b54f9e4725ac
          nullable: true
          description: The merchant card pricing package id. Loan or card package to fill, not both.
        projectId:
          type: string
          example: 95a373a7d93b2602
          nullable: true
          description: The attached project, can be empty.
        payables:
          type: array
          description: The draw attached payables, invoices. Cannot be empty.
          items:
            $ref: '#/components/schemas/PayableItem'
        authorizationDetails:
          allOf:
            - $ref: '#/components/schemas/AuthorizationDetails'
          nullable: true
          description: The authorization period and amount details.
    Loan:
      type: object
      properties:
        id:
          type: string
          format: uuid
        companyId:
          type: string
          nullable: true
          example: 8a837a7f855feed0
        einHash:
          type: string
          example: 55776abcdef12212
          nullable: true
        amount:
          type: number
          format: double
          example: 10000
        refundAmount:
          type: number
          format: double
          example: 0
        fee:
          type: number
          format: double
          example: 400
        activeLoanTemplateId:
          type: string
          format: uuid
          nullable: true
        status:
          type: string
          enum:
            - Created
            - Started
            - Pending
            - Canceled
            - Closed
            - Defaulted
            - Recovered
            - Refinanced
        isDeleted:
          type: boolean
          default: false
          example: false
        isOverdue:
          type: boolean
          default: false
          example: false
        startDate:
          type: string
          format: date
        closeDate:
          type: string
          format: date
          nullable: true
        defaultedDate:
          type: string
          format: date
          nullable: true
        lastPaymentDate:
          type: string
          format: date
          nullable: true
        lastSyncDate:
          type: string
          format: date-time
          nullable: true
        drawApprovalId:
          type: string
          example: a8c10e59460a3b41
        merchantId:
          type: string
          example: 6454d53d-08ac-4b2e-8308-4724b9e2228e
          nullable: true
        loanPricingPackage:
          type: string
          example: 4810b720-3a1f-4c0d-9018-3fb88c3c6304
          nullable: true
        cardPricingPackage:
          type: string
          example: 7975a0d0-0680-45a4-ba94-0179a6f9e144
          nullable: true
        projectId:
          type: string
          example: 95a373a7d93b2602
          nullable: true
          description: The attached project, can be empty.
        isIssuedByAutoTradeCredit:
          type: boolean
          default: false
          description: The draw was created automatically by express flow
        autoTradeCreditCanceledAt:
          type: string
          format: date-time
          nullable: true
          description: The draw was canceled during auth. period by borrower in this time
        autoTradeCreditCanceledBy:
          type: string
          nullable: true
          description: The draw was canceled during auth. period by this user id
        isAutoCollectionPaused:
          type: boolean
          default: false
          description: No automatic repayment is initiated if true
        autoCollectionPausedAt:
          type: string
          format: date-time
          nullable: true
          description: Filled then paused, else it is null
        autoCollectionPausedBy:
          type: string
          nullable: true
          description: Filled then paused, else it is null
        payablesAmount:
          type: number
          format: decimal
          example: 10000.00
          pattern: '[0-9]*\.[0-9]{2}'
          multipleOf: 0.01
          description: Calculated field. The sum of invoices amount.
        payablesCount:
          type: number
          format: int32
          example: 5
          description: Calculated field. The count of invoices.
        payablesNumber:
          type: string
          nullable: true
          example: 2004/00001
          description: The number of invoices. Null if multiple.
        activeLoanTemplate:
          $ref: "#/components/schemas/LoanTemplate"
        loanDetails:
          allOf:
            - $ref: "#/components/schemas/LoanDetails"
          nullable: true
          description: Filled only if detailed view was requested
        loanReceivables:
          type: array
          description: Array of loan receivables, installments, fees
          items:
            $ref: "#/components/schemas/LoanReceivable"
        payments:
          type: array
          description: Array of failed and successful payments
          items:
            $ref: "#/components/schemas/Payment"
        credit:
          $ref: "#/components/schemas/CreditWithoutDetails"
          description: Details of credit for this loan
        loanParameters:
          type: array
          items:
            $ref: "#/components/schemas/LoanParameter"
    AuthorizationDetails:
      type: object
      required: 
        - periodStarts
        - periodEnds
        - creditHoldAmount
      properties:
        periodStarts:
          type: string
          format: date
        periodEnds:
          type: string
          format: date
        creditHoldAmount:
          type: number
          format: double
          example: 4000
    PatchLoanStatus:
      type: object
      required: 
        - status
      properties: 
        status:
          type: string
          enum:
            - Started
            - Canceled
            - Closed
            - Defaulted
            - Recovered
            - Refinanced
    DrawDisbursed:
      type: object
      required: 
        - event
        - blueTapeCorrelationId
        - createdBy
        - details
      properties:
        event:
          type: string
          example: DRAW.DISBURSEMENT
        blueTapeCorrelationId:
          type: string
          example: c3c81cfa-a765-4f25-afa8-a109df055e76
        createdBy:
          type: string
          example: BlueTape.PaymentService
        details:
          $ref: "#/components/schemas/DrawDisbursementDetails"
    DrawDisbursementDetails:
      type: object
      required: 
        - drawId
        - amount
        - status
        - transactionId
        - transactionNumber
      properties:
        drawId:
          type: string
          example: 6aa31975-a7b9-49c2-8d77-830df1266c22
        amount:
          type: number
          format: double
          example: 5000
        status:
          type: string
          enum:
            - PROCESSING
            - SUCCESS
            - REJECTED
        transactionId:
          type: string
          example: f2322f1a-518c-4e89-9816-75826d3c3270
        transactionNumber:
          type: string
          example: abcdefghijkl0123456
    LoanHistoryItem:
      type: object
      properties:
        id:
          type: string
        createdAt:
          type: string
          format: date-time
        createdBy:
          type: string
        operation:
          type: string
          example: DRAW.DISBURSEMENT.SUCCESS
        displayText:
          type: string
          example: The draw has been successfully disbursed to merchant Best Lumber.
    PatchRepaymentAutoCollection:
      type: object
      required: 
        - isAutoCollectionPaused
      properties:
        isAutoCollectionPaused:
          type: boolean
    LoanDetails:
      type: object
      nullable: true
      description: Filled only if detailed view was requested
      properties:
        loanId:
          type : string
        businessDaysLate:
          type: number
          format: int32
        nextPaymentAmount:
          type: number
        nextPaymentDate:
          type: string
          format: date
        lateAmount:
          type: number
        totalProcessingPaymentsAmount:
          type: number
        principalBalance:
          type: number
        loanOutstandingAmount:
          type: number
        totalPaid:
          type: number
        totalLoanAmount:
          type: number
        isAnyReceivableLate:
          type: boolean
        isFullyPaid:
          type: boolean
        oldestDueOrPastDueDate:
          type: string
          format: date
        totalDailyPenaltyInterest:
          type: number
        hadPenalty:
          type: boolean
    LoanReceivable:
      type: object
      properties:
        id:
          type: string
        createdAt:
          type: string
          format: date-time
        createdBy:
          type: string
        updatedAt:
          type: string
          format: date-time
        updatedBy:
          type: string
        loanId:
          type: string
        expectedDate:
          type: string
          format: date
        paidDate:
          type: string
          format: date
          nullable: true
        actualDate:
          type: string
          format: date
          nullable: true
        processingDate:
          type: string
          format: date
          nullable: true
        expectedAmount:
          type: number
        paidAmount:
          type: number
          nullable: true
        actualAmount:
          type: number
          nullable: true
        adjustAmount:
          type: number
          nullable: true
        processingAmount:
          type: number
          nullable: true
        status:
          type: string
          enum:
            - paid
            - late
            - pending
            - canceled
        scheduleStatus:
          type: string
          enum:
            - Current
            - Rescheduled
            - Postponed
            - Replanned
        type:
          type: string
          enum:
            - Installment
            - LoanFee
            - LatePaymentFee
            - ManualLatePaymentFee
            - ExtensionFee
            - PenaltyInterestFee
        penaltyInterestDetails:
          allOf:
            - $ref: "#/components/schemas/PenaltyInterestDetails"
          nullable: true
          description: Filled if type PenaltyInterestFee
    Payment:
      type: object
      properties:
        id:
          type: string
        loanId:
          type: string
        date:
          type: string
          format: date
        amount:
          type: number
        status:
          type: string
          enum:
            - Success
            - Rejected
            - Processing
            - Canceled
        transactionNumber:
          type: string
        type:
          type: string
          enum:
            - AutoDebit
            - Manual
            - Custom
        subType:
          type: string
          enum:
            - Repayment
            - Refund
    LoanParameter:
      type: object
      properties:
        id:
          type: string
        loanId:
          type: string
        isActive:
          type: boolean
          default: true
        loanFeePercentage:
          type: number
        installmentsNumber:
          type: number
          format: int32
        paymentDelayInDays:
          type: number
          format: int32
        paymentIntervalInDays:
          type: number
          format: int32
        minimumLateFeeAmount:
          type: number
        lateFeePercentage:
          type: number
        gracePeriodInDays:
          type: number
          format: int32
        earlyPayPeriod:
          type: number
          format: int32
        totalDurationInDays:
          type: number
          format: int32
        changeType:
          type: string
          enum:
            - Initial
            - Reschedule
            - Replan
        loanTemplateId:
          type: string
        penaltyInterestTriggerRule:
          type: string
          enum:
            - twoTimesThreeDaysOrOneTimeSevenDaysRule
        code:
          type: string
        note:
          type: string
        createdBy:
          type: string
    PenaltyInterestDetails:
      type: object
      properties:
        id:
          type: string
        createdAt:
          type: string
          format: date-time
        createdBy:
          type: string
        updatedAt:
          type: string
          format: date-time
        updatedBy:
          type: string
        penaltyStartDate:
          type: string
          format: date
        penaltyEndDate:
          type: string
          format: date
        outstandingPrincipalAmount:
          type: number
        sofrDay:
          type: string
          format: date
        sofrRate:
          type: number
        basisPoint:
          type: number
  securitySchemes:
    ApiKey:
      type: apiKey
      in: header
      name: X-Api-Key
security:
  - ApiKey: []  
