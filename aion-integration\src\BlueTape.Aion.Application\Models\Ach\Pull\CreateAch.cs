﻿using BlueTape.Integrations.Aion.Infrastructure.Enums;
using System.Runtime.Serialization;
using System.Text.Json.Serialization;

namespace BlueTape.Aion.Application.Models.Ach.Pull;

[DataContract]
public class CreateAch
{
    [JsonPropertyName("amount")]
    public decimal Amount { get; set; }

    [JsonPropertyName("description")]
    public string Description { get; set; } = null!;

    [JsonPropertyName("transactionNumber")]
    public string TransactionNumber { get; set; } = null!;

    [JsonPropertyName("transactionId")]
    public string TransactionId { get; set; } = null!;

    [JsonPropertyName("addenda")]
    public string[] Addenda { get; set; } = null!;

    [JsonPropertyName("receiver")]
    public Receiver Receiver { get; set; } = null!;

    [JsonPropertyName("originatorAccountCode")]
    public AccountCodeType OriginatorAccountCode { get; set; }
    
    [Json<PERSON>ropertyName("sameDayAch")]
    public bool SameDayAch { get; set; }
}