using System.Text.Json.Serialization;

namespace BlueTape.Aion.DataAccess.External.Models.WireTransfer.Response;

public class GetWireTransferResponse : BaseAionResponseModel
{
    [JsonPropertyName("count")]
    public int Count { get; set; }

    [JsonPropertyName("numPages")]
    public int NumPages { get; set; }

    [JsonPropertyName("pageNumber")]
    public int PageNumber { get; set; }

    [JsonPropertyName("wireList")]
    public List<WireTransferObjItem> WireList { get; set; } = new();
}