using BlueTape.Aion.Application.Abstractions;
using BlueTape.Aion.Application.Abstractions.MessageSenders;
using BlueTape.Aion.Domain.Constants;
using BlueTape.Aion.Domain.Extensions;
using BlueTape.Common.ExceptionHandling.Exceptions.Base;
using BlueTape.Common.ExceptionHandling.Extensions;
using BlueTape.Common.Extensions.Abstractions;
using BlueTape.Integrations.Aion.Infrastructure.Enums;
using BlueTape.ServiceBusMessaging.Attributes;
using BueTape.Aion.Infrastructure.ServiceBusMessages;
using Microsoft.Azure.Functions.Worker;
using Microsoft.Extensions.Logging;
using Serilog.Context;
using System.Diagnostics.CodeAnalysis;

namespace BlueTape.Functions.Aion.TransactionStatusReport;

[ExcludeFromCodeCoverage]
public class ExternalTransactionStatusReportFunction(
    ITraceIdAccessor traceIdAccessor,
    IAionExternalTransactionMessageSender aionExternalTransactionMessageSender,
    IErrorNotificationService notificationService,
    ILogger<ExternalTransactionStatusReportFunction> logger)
{
    private const string BlueTapeCorrelationId = "BlueTapeCorrelationId";

    [Function("ExternalTransactionStatusReportFunction")]
    public async Task Run([TimerTrigger($"%{AionFunctionConstants.AionExternalTransactionJobPeriod}%")] TimerInfo myTimer, CancellationToken ct)
    {
        traceIdAccessor.TraceId = $"{Guid.NewGuid()}-{nameof(ExternalTransactionStatusReportFunction)}";

        using (GlobalLogContext.PushProperty("Method", "Scheduler"))
        using (GlobalLogContext.PushProperty(BlueTapeCorrelationId, traceIdAccessor.TraceId))
        {
            try
            {
                logger.LogInformation("Start ExternalTransactionStatusReportFunction status report");
                var environment = Environment.GetEnvironmentVariable(EnvironmentConstants.Environment);

                if (environment?.IsEnvironmentDevOrBeta() ?? false)
                {
                    logger.LogInformation("ExternalTransactionStatusReportFunction status report function execution is suppresed due to environment: {environment}", environment);
                    return;
                }

                var body = new AionExternalTransactionMessage();
                var paymentSubscriptions = Enum.GetValues<PaymentSubscriptionType>();
                var currentHour = DateTime.UtcNow.Hour;
                
                foreach (var paymentSubscription in paymentSubscriptions)
                {
                    if (Environment.GetEnvironmentVariable(EnvironmentConstants.Environment).IsEnvironmentNotProd())
                    {
                        body.ExternalAchTransactionReportRequest.ExternalAchReportRequests.Add(new()
                        {
                            AchReturnTransactionPage = 1,
                            AchTransactionPage = 1,
                            PaymentSubscriptionType = paymentSubscription,
                            InstantTransactionPage = 1,
                            WireTransactionPage = 1
                        });
                    }
                    else
                    {
                        if (currentHour % 4 == 0)
                        {
                            body.ExternalAchTransactionReportRequest.ExternalAchReportRequests.Add(new()
                            {
                                AchReturnTransactionPage = 1,
                                AchTransactionPage = 1,
                                PaymentSubscriptionType = paymentSubscription,
                                InstantTransactionPage = 1,
                                WireTransactionPage = 1
                            });
                        }
                        else
                        {
                            body.ExternalAchTransactionReportRequest.ExternalAchReportRequests.Add(new()
                            {
                                AchReturnTransactionPage = null,
                                AchTransactionPage = null,
                                PaymentSubscriptionType = paymentSubscription,
                                InstantTransactionPage = 1,
                                WireTransactionPage = 1
                            });
                        }   
                    }
                }
                
                await aionExternalTransactionMessageSender.SendMessage(new ServiceBusMessageBt<AionExternalTransactionMessage>(body, new ServiceBusMessageAttributes()
                {
                    CorrelationId = traceIdAccessor.TraceId,
                    MessageId = traceIdAccessor.TraceId
                }), ct);

            }
            catch (DomainException domainEx)
            {
                await notificationService.Notify(domainEx.GetSlackEventMessageBody(EnvironmentExtensions.GetExecutionEnvironment(), AionFunctionConstants.ProjectName), ct);
                logger.LogError(domainEx, "ExternalTransactionStatusReportFunction status scheduler function failed: {message}", domainEx.Message);
                throw;
            }
            catch (Exception ex)
            {
                await notificationService.Notify(ex.GetSlackEventMessageBody(EnvironmentExtensions.GetExecutionEnvironment(), AionFunctionConstants.ProjectName, maxMessageLength: 400), ct);
                logger.LogError(ex, "ExternalTransactionStatusReportFunction status scheduler function failed: {message}", ex.Message);
                throw;
            }
        }
    }
}
