import {expect, Page} from '@playwright/test';

export class BasePage {
    readonly page: Page;

    readonly pageLocator: string;

    constructor(page: Page, pageLocator = '') {
        this.page = page;
        this.pageLocator = pageLocator;
        if (pageLocator) {
            expect(this.page.isVisible(this.pageLocator)).toBeTruthy;
        }
    };

    async uploadFile(element) {
        const [uploadFiles] = await Promise.all([
            this.page.waitForEvent('filechooser'),
            element.click()
        ]);
        await uploadFiles.setFiles('test-data/files/testFile.png');
    };

    async clickFistOption(element) {
        await element.first().click();
    };

    async getFistOption(element) {
        await element.first();
    };
}
