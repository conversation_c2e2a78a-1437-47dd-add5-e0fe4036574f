openapi: '3.0.0'
info:
  version: '0.0.1'
  title: 'Payment Flow Service'
  description: API definition of Payment Flow Service (orchestator service of payment domain)
paths:
  /createInvoicePayment:
    post: 
      tags: 
        - queueEvents
      summary: Payload definition of create invoice payment request event from queue
      description: Payload definition of create invoice payment request event from queue
      operationId: createInvoicePaymentEvent
      requestBody: 
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/CreateInvoicePaymentEvent'
      responses:
        201:
          description: PaymentRequest created (just for compatibility)
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/CreateInvoicePaymentEventResponse'
        400:
          description: Invalid request (just for compatibility)
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
  /createDisburseDraw:
    post: 
      tags:
        - queueEvents
      summary: Payload definition of disburse draw, advance payment for merchant
      description: Payload definition of disburse draw, advance payment for merchant
      operationId: createDisburseDraw
      requestBody: 
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/CreateDisburseDrawEvent'
      responses:
        201:
          description: Disburse draw created (just for compatibility)
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/CreateDisburseDrawEventResponse'
        400:
          description: Invalid request (just for compatibility)
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
  /createDrawRepayment:
    post: 
      tags:
        - queueEvents
      summary: Payload definition of draw repayment
      description: Payload definition of draw repayment
      operationId: createDrawRepayment
      requestBody: 
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/CreateDrawRepaymentEvent'
      responses:
        201:
          description: Draw repayment created (just for compatibility)
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/CreateDrawRepaymentResponse'
        400:
          description: Invalid request (just for compatibility)
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
  /createFinalPayment:
    post: 
      tags:
        - queueEvents
      summary: Payload definition of final payment
      description: Payload definition of final payment
      operationId: createFinalPayment
      requestBody: 
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/CreateFinalPaymentEvent'
      responses:
        201:
          description: Final payment created (just for compatibility)
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/CreateFinalPaymentEventResponse'
        400:
          description: Invalid request (just for compatibility)
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
components:
  schemas:
    CreateInvoicePaymentEvent:
      type: object
      required:
        - flowTemplateCode
      properties:
        flowTemplateCode:
          type: string
          example: CREATE.PAYNOW.INVOICE_PAYMENT
        blueTapeCorrelationId:
          type: string
          example: 657056ddff868bcdb89bb8f1
        createdBy:
          type: string
          example: BlueTape
        details:
          $ref: "#/components/schemas/CreateInvoicePaymentEventDetails"
    CreateInvoicePaymentEventResponse:
      type: object
      nullable: true
    CreateDrawRepaymentResponse:
      type: object
      nullable: true
    CreateInvoicePaymentEventDetails:
      type: object
      required: 
        - date
        - currency
        - requestedAmount
      properties:
        date:
          type: string
          format: date-time
          example: 2023-12-12T00:00:00.000Z
        currency:
          description: Currency of the payment request
          type: string
          example: USD
        requestedAmount:
          type: number
          format: decimal
          example: 22000.00
          pattern: '[0-9]*\.[0-9]{2}'
          multipleOf: 0.01
        paymentMethod:
          type: string
          enum:
            - ach
            - card
            - tradeCredit
          example: ach
        payablesDetails:
          type: array
          items:
            $ref: '#/components/schemas/PayableDetail'
        customerDetails:
          $ref: '#/components/schemas/CustomerDetails'
        sellerDetails:
          $ref: '#/components/schemas/SellerDetails'
        feeDetails:
          type: array
          items:
            $ref: '#/components/schemas/FeeDetail'
        discountDetails:
          type: array
          items:
            $ref: '#/components/schemas/DiscountDetail'
        projectDetails:
          $ref: '#/components/schemas/ProjectDetail'
    PayableDetail:
      type: object
      required: 
        - id
        - payableType
        - payableAmount
        - requestedAmount
      properties:
        id:
          type: string
        payableType:
          type: string
          enum:
            - invoice
            - salesOrder
          example: invoice
        payableAmount:
          type: number
          format: decimal
          example: 55000.00
          pattern: '[0-9]*\.[0-9]{2}'
          multipleOf: 0.01
        requestedAmount:
          type: number
          format: decimal
          example: 22000.00
          pattern: '[0-9]*\.[0-9]{2}'
          multipleOf: 0.01
        discountAmount:
          type: number
          format: decimal
          example: 15.00
          pattern: '[0-9]*\.[0-9]{2}'
          multipleOf: 0.01
    CustomerDetails:
      type: object
      required: 
        - id
        - accountId
      properties:
        id:
          type: string
        name:
          type: string
        accountId:
          type: string
    SellerDetails:
      type: object
      required: 
        - companyId
      properties:
        companyId:
          type: string
          example: e845bb09-4e2a-48c5-bc4d-e44be50e7f11
        name:
          type: string
          example: Best Lumber Inc.
        paymentSettings:
          $ref: '#/components/schemas/SellerPaymentSettings'
    SellerPaymentSettings:
      type: object
      properties:
        merchantAchDelayDays:
          type: number
          format: int32
          example: 2
    FeeDetail:
      type: object
      properties:
        companyId:
          type: string
        amount:
          type: number
          format: decimal
          example: 3.82
          pattern: '[0-9]*\.[0-9]{2}'
          multipleOf: 0.01
        description:
          type: string
          example: Merchant fee
        type:
          type: string
          enum:
            - merchantFee
            - purchaserFee
    DiscountDetail:
      type: object
      properties:
        companyId:
          type: string
        amount:
          type: number
          format: decimal
          example: 3.82
          pattern: '[0-9]*\.[0-9]{2}'
          multipleOf: 0.01
        description:
          type: string
          example: Ach Early Payment Discount
        type:
          type: string
          enum:
            - achEarlyPaymentDiscount
    ProjectDetail:
      type: object
      required: 
        - id
      properties:
        id:
          type: string
    CreateDisburseDrawEvent:
      type: object
      required:
        - flowTemplateCode
      properties:
        flowTemplateCode:
          type: string
          example: CREATE.DRAW.DISBURSEMENT
        blueTapeCorrelationId:
          type: string
          example: 657056ddff868bcdb89bb8f1
        createdBy:
          type: string
          example: BlueTape
        details:
          $ref: "#/components/schemas/CreateDisburseDrawEventDetails"
    CreateDisburseDrawEventDetails:
      type: object
      properties:
        date:
          type: string
          format: date-time
          example: 2023-12-12T00:00:00.000Z
        currency:
          description: Currency of the advance payment
          type: string
          example: USD
        requestedAmount:
          type: number
          format: decimal
          example: 22000.00
          pattern: '[0-9]*\.[0-9]{2}'
          multipleOf: 0.01
          description: The amount of advance payment
        paymentMethod:
          type: string
          enum:
            - tradeCredit
          example: tradeCredit
        drawDetails:
          $ref: "#/components/schemas/DrawDetails"
        creditorDetails:
          $ref: "#/components/schemas/CreditorDetails"
        customerDetails:
          $ref: '#/components/schemas/CustomerDetailsDrawAdvancePayment'
        sellerDetails:
          $ref: '#/components/schemas/SellerDetails'
        feeDetails:
          type: array
          items:
            $ref: '#/components/schemas/FeeDetail'
    DrawDetails:
      type: object
      required: 
        - id
        - drawAmount
      properties:
        id:
          type: string
          example: 9b8b4b2d-7a82-4c90-b2d8-17e42da4c194
        drawAmount:
          type: number
          format: decimal
          example: 30000.00
          pattern: '[0-9]*\.[0-9]{2}'
          multipleOf: 0.01
          description: The amount of draw
    DrawDetailsRepayment:
      type: object
      required: 
        - id
        - amount
      properties:
        id:
          type: string
          example: 9b8b4b2d-7a82-4c90-b2d8-17e42da4c194
        amount:
          type: number
          format: decimal
          example: 340.00
          pattern: '[0-9]*\.[0-9]{2}'
          multipleOf: 0.01
    CustomerDetailsDrawAdvancePayment:
      type: object
      required:
        - id
      properties:
        id:
          type: string
          example: 50111f79-7390-472d-bc23-6d1f2b16d924
        name:
          type: string
          example: Tom Smith & Sons
    CreditorDetails:
      type: object
      required: 
        - drawBuybackDelayDays
      properties:
        drawBuybackDelayDays:
          type: number
          format: int32
          example: 3
    CreateDrawRepaymentEvent:
      type: object
      required:
        - flowTemplateCode
      properties:
        flowTemplateCode:
          type: string
          example: CREATE.DRAW.REPAYMENT
        blueTapeCorrelationId:
          type: string
          example: 657056ddff868bcdb89bb8f1
        createdBy:
          type: string
          example: BlueTape
        details:
          $ref: "#/components/schemas/CreateDrawRepaymentEventDetails"
    CreateDrawRepaymentEventDetails:
      type: object
      properties:
        date:
          type: string
          format: date-time
          example: 2023-12-12T00:00:00.000Z
        currency:
          description: Currency of repayment
          type: string
          example: USD
        requestedAmount:
          type: number
          format: decimal
          example: 340.00
          pattern: '[0-9]*\.[0-9]{2}'
          multipleOf: 0.01
          description: The amount of repayment
        paymentMethod:
          type: string
          enum:
            - ach
          example: ach
        drawDetails:
          $ref: "#/components/schemas/DrawDetailsRepayment"
        customerDetails:
          $ref: '#/components/schemas/CustomerDetailsDrawRepayment'
    CreateDisburseDrawEventResponse:
      type: object
    CreateFinalPaymentEvent:
      type: object
      required:
        - flowTemplateCode
      properties:
        flowTemplateCode:
          type: string
          example: CREATE.DRAW.FINALPAYMENT
        blueTapeCorrelationId:
          type: string
          example: 657056ddff868bcdb89bb8f1
        createdBy:
          type: string
          example: BlueTape
        details:
          $ref: "#/components/schemas/CreateFinalPaymentEventDetails"
    CreateFinalPaymentEventDetails:
      type: object
      properties:
        date:
          type: string
          format: date-time
          example: 2023-12-12T00:00:00.000Z
        currency:
          description: Currency of final payment
          type: string
          example: USD
        requestedAmount:
          type: number
          format: decimal
          example: 550.00
          pattern: '[0-9]*\.[0-9]{2}'
          multipleOf: 0.01
          description: The amount of final payment
        paymentMethod:
          type: string
          enum:
            - ach
          example: ach
        drawDetails:
          $ref: "#/components/schemas/DrawDetailsFinalPayment"
        sellerDetails:
          $ref: '#/components/schemas/SellerDetails'
        payablesDetails:
          type: array
          items:
            $ref: '#/components/schemas/FinalPaymentPayableDetail'
    CreateFinalPaymentEventResponse:
      type: object
    CustomerDetailsDrawRepayment:
      type: object
      required: 
        - id
        - accountId
      properties:
        id:
          type: string
        name:
          type: string
          example: Tom Smith & Sons
        accountId:
          type: string
    SellerDetailsDrawRepayment:
      type: object
      nullable: true
      required: 
        - id
        - accountId
      properties:
        id:
          type: string
        name:
          type: string
          example: Best Lumber Inc.
        accountId:
          type: string
    DrawDetailsFinalPayment:
      type: object
      properties:
        id:
          type: string
    FinalPaymentPayableDetail:
      type: object
      properties:
        id:
          type: string
        payableType:
          type: string
          enum:
            - invoice
            - salesOrder
          example: invoice
        payableAmount:
          type: number
          format: decimal
          example: 5000.00
          pattern: '[0-9]*\.[0-9]{2}'
          multipleOf: 0.01
        requestedAmount:
          type: number
          format: decimal
          example: 1000.00
          pattern: '[0-9]*\.[0-9]{2}'
          multipleOf: 0.01
        discountAmount:
          type: number
          format: decimal
          example: 25.00
          pattern: '[0-9]*\.[0-9]{2}'
          multipleOf: 0.01
    Error:
      type: object
      required:
        - message
      properties:
        message:
          description: A human readable error message
          type: string
    EmptyResponse:
      type: object
      nullable: true
  securitySchemes:
    ApiKey:
      type: apiKey
      in: header
      name: X-Api-Key
security:
  - ApiKey: []
