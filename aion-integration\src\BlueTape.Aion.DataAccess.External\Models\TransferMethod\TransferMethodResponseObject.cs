﻿using System.Runtime.Serialization;
using System.Text.Json.Serialization;

namespace BlueTape.Aion.DataAccess.External.Models.TransferMethod;

[DataContract]
public class TransferMethodResponseObject
{
    [JsonPropertyName("id")]
    public string Id { get; set; } = null!;
    
    [JsonPropertyName("type")]
    public string Type { get; set; } = null!;
    
    [JsonPropertyName("associatedObjectId")]
    public string AssociatedObjectId { get; set; } = null!;
    
    [JsonPropertyName("counterpartyId")]
    public string CounterpartyId { get; set; } = null!;
    
    [JsonPropertyName("accountId")]
    public string AccountId { get; set; } = null!;
    
    [JsonPropertyName("nickName")]
    public string NickName { get; set; } = null!;
    
    [JsonPropertyName("bankDetail")]
    public BankDetail BankDetail { get; set; } = null!;
    
    [JsonPropertyName("default")]
    public bool Default { get; set; }
}