name: Schedule run on beta env

on:
  workflow_dispatch:
  schedule:
    - cron: '0 3 * * *'
jobs:
  regress-beta:
    timeout-minutes: 120
    environment: BETA
    runs-on: ubuntu-latest
    container:
      image: mcr.microsoft.com/playwright:v1.37.1-jammy
    env:
      CI_ENVIRONMENT_URL: ${{ vars.CI_ENVIRONMENT_URL }}
      CI_ENVIRONMENT_BACKEND_URL: ${{ vars.CI_ENVIRONMENT_BACKEND_URL }}
      ADMIN_BACKEND_URL: ${{ vars.ADMIN_BACKEND_URL }}
      ADMIN_EMAIL: ${{ vars.ADMIN_EMAIL }}
      ADMIN_PASSWORD: ${{ vars.ADMIN_PASSWORD }}
      ADMIN_FIRSTNAME: ${{ vars.ADMIN_FIRSTNAME }}
      ADMIN_LASTNAME: ${{ vars.ADMIN_LASTNAME }}
      USER_DOMAIN: ${{ vars.USER_DOMAIN }}
      USER_BACKEND_URL: ${{ vars.USER_BACKEND_URL }}
      USER_EMAIL: ${{ vars.USER_EMAIL }}
      USER_PASSWORD: ${{ vars.USER_PASSWORD }}
      USER_FIRSTNAME: ${{ vars.USER_FIRSTNAME }}
      USER_LASTNAME: ${{ vars.USER_LASTNAME }}
      USER_COMPANYNAME: ${{ vars.USER_COMPANYNAME }}
      USER_CUSTOMEREMAIL: ${{ vars.USER_CUSTOMEREMAIL }}
      USER_CUSTOMERPASSWORD: ${{ vars.USER_CUSTOMERPASSWORD }}
      USER_CUSTOMERID: ${{ vars.USER_CUSTOMERID }}
      USER_CUSTOMER_COMPANYNAME: ${{ vars.USER_CUSTOMER_COMPANYNAME }}
      GENERIC_API_BASE_URL: ${{ vars.GENERIC_API_BASE_URL }}
      X_BLUETAPE_KEY: ${{ vars.X_BLUETAPE_KEY }}
      X_INTEGRATION_ACCOUNT_ID: ${{ vars.X_INTEGRATION_ACCOUNT_ID }}
      MONGODB_URI: ${{ vars.MONGODB_URI }}
      ACCESS_KEY_AWS: ${{ vars.ACCESS_KEY_AWS }}
      SECRET_ACCESS_KEY_AWS: ${{ vars.SECRET_ACCESS_KEY_AWS }}
    steps:
      - uses: actions/checkout@v3
      - uses: actions/setup-node@v3
        with:
          node-version: 18

      - name: install cross-env
        run: npm install --g cross-env

      - name: Install dependencies
        run: npm ci

      - name: Run Playwright tests
#        run: npx playwright test --grep "@none"
        run: npx playwright test --grep-invert "@(aion|generic)" --reporter=html
      - uses: actions/upload-artifact@v3
        if: always()  
        with:
          name: playwright-report
          path: playwright-report/
          retention-days: 7

  publishTestResults:
    name: "Publish Test Results"
    needs: [regress-beta]
    runs-on: ubuntu-latest
    if: always()
    steps:
      - name: Download artifacts
        uses: actions/download-artifact@v3
        id: download

      - name: Publish to GH Pages
        if: always()
        uses: peaceiris/actions-gh-pages@v3.7.3
        with:
          external_repository: bluetape-org/auto-tests-playwright-mocha
          publish_branch: gh-pages
          personal_token: ${{ secrets.PIPELINE }}
          publish_dir: playwright-report/
          keep_files: true
          user_name: "github-actions[bot]"
          user_email: "github-actions[bot]@users.noreply.github.com"