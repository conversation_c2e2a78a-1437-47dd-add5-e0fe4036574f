import {BaseTest, test} from "../../test-utils";
import {
    createPaymentRequest,
    findRequiredPaymentRequest,
    getRequiredPaymentRequestByDay,
    getRequiredPaymentRequestById, updatedTransactionsInfo
} from "../../../api/paynow/send-pay-now-request";
import {getCurrentDay} from "../../../api/base-api";
import {expect} from "@playwright/test";
import * as _ from 'lodash';
import {transactionStatusUpdateJob} from "../../../api/paynow/queue-ivents";

const payNowData = JSON.parse(JSON.stringify(require('../../../constants/PayNow-data.json')));

test.describe(`Pay now API negative full path. @paynow`, async () => {

    /*
    A lot of tests depends on the execution of the previous ones
    */

    const totalAmountString = Math.floor(Math.random() * 100).toString() + "." + Math.floor(Math.random() * 99).toString();
    const day = getCurrentDay();
    const sellerId = process.env.SELLER_ID;
    const payerId = process.env.PAYER_ID;
    const payeeId = process.env.PAYEE_ID;
    const customerAccountID = process.env.CUSTOMER_ACCOUNT_ID;
    const invoiceId = ``;

    /*
    Data that tests receive from each other
    */

    let requiredPaymentId: string;
    let requiredPaymentRequest;
    let allPaymentRequests;
    let response;
    let responseData;
    let paymentStatus;
    let transactions;

    /*
    Create invoice via UI
    Get all required data from getPayments response
    */

    test(`Precondition.`, async ({pageManager, supplierForPayNowPageManager}) => {
        const invoiceNumber = BaseTest.dateTimePrefix() + 'invoiceNumber';
        await pageManager.onBoardingPage.createInvoiceViaUI(invoiceNumber, totalAmountString);
        await supplierForPayNowPageManager.payModal.openInvoicesPage();
        await supplierForPayNowPageManager.invoicesList.clickOnFirstRequiredInvoice();
        await supplierForPayNowPageManager.invoicesDetails.payForLatestInvoice();

        allPaymentRequests = await getRequiredPaymentRequestByDay(day);
        requiredPaymentRequest = await findRequiredPaymentRequest(allPaymentRequests, +totalAmountString);
        requiredPaymentId = await requiredPaymentRequest.id;
        requiredPaymentRequest = await findRequiredPaymentRequest(allPaymentRequests, +totalAmountString);
        requiredPaymentId = await requiredPaymentRequest.id;
        response = await getRequiredPaymentRequestById(requiredPaymentId);
        responseData = await response.data;
        paymentStatus = await responseData.status;
        transactions = await responseData.transactions;
    });

    /*
    Each payment creates 4 transactions by default
    */

    test(`No transaction number greater than four for new payment request.`, async () => {
        expect(_.find(transactions, {sequenceNumber: 5})).toBe(undefined);
    });

    /*
    Each transaction have transaction number with which we trigger transactions
    */

    test(`Trigger transaction by non-existent number. Transactions requests should not change status.`, async() => {
        await transactionStatusUpdateJob("", payNowData.statuses.Sent);
        transactions = await updatedTransactionsInfo(requiredPaymentId);
        expect(await _.find(transactions, {sequenceNumber: 1}).status).toEqual(payNowData.statuses.Processing);
        expect(await _.find(transactions, {sequenceNumber: 2}).status).toEqual(payNowData.statuses.Placed);
        expect(await _.find(transactions, {sequenceNumber: 3}).status).toEqual(payNowData.statuses.Placed);
        expect(await _.find(transactions, {sequenceNumber: 4}).status).toEqual(payNowData.statuses.Placed);
    });

    /*
    Skipped until "create invoice" endpoint will be fixed
    */

    test.skip(`Create payment with invalid date.`, async () => {
        const response = await createPaymentRequest(null, payerId, payeeId, sellerId, customerAccountID, +totalAmountString, invoiceId);
        expect(await response.status).toEqual(500);
    });
});
