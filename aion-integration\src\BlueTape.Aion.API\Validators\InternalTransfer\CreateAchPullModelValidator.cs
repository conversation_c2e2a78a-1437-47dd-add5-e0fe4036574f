﻿using BlueTape.Integrations.Aion.Internal;
using FluentValidation;

namespace BlueTape.Aion.API.Validators.InternalTransfer;

public class CreateInternalModelValidator : AbstractValidator<CreateInternalModel>
{
    public CreateInternalModelValidator()
    {
        RuleSet(ValidationConstant.ManualValidation, () =>
        {
            RuleFor(x => x.Receiver)
                .NotNull()
                .SetValidator(new BaseInternalAccountDetailsModelValidator());
            
            RuleFor(x => x.Originator)
                .NotNull()
                .SetValidator(new BaseInternalAccountDetailsModelValidator());
            
            RuleFor(x => x.Amount)
                .NotNull();
            RuleFor(x => x.Description)
                .MaximumLength(30);
        });
    }
}