import {expect} from '@playwright/test';
import {test} from '../../test-utils';
import {sendLMSRequest, deleteLoan} from '../../../api/common/lms-send-request';
import {getCurrentDay} from "../../../api/base-api";


const constants = JSON.parse(JSON.stringify(require('../../../constants/LMStestData.json')));

test.describe(`Loan. CRUD API Tests @LMS @API`, async () => {

    let loanId: string;
    let templateId: string;
    let statusCode: number;
    const loanStates = ["Canceled", "Closed", "Started"];
    const fromDate = getCurrentDay();
    const toDate = getCurrentDay();

    test.beforeAll(async () => {
        /**
         * Create Template
         */

        const requestBody = await constants.loanTemplates.TemplateWithTwoInstallments;

        const response = await sendLMSRequest('post', 'LoanTemplates', requestBody);

        templateId = await response.data.id;

        /**
         * Create Loan
         */

        const loanBody: { companyId: string; amount: number; loanTemplateId: string; einHash: string } = {
            "companyId": constants.loans.ownerId,
            "amount": 100,
            "einHash": constants.loans.einHash,
            "loanTemplateId": templateId,

        };
        const loanResponse = await sendLMSRequest('post', 'Loans', loanBody);

        loanId = await loanResponse.data.id;

        statusCode = await response.status;
    });

    test.afterAll(async () => {
        const response = await deleteLoan(loanId);

        expect(response.status, `Status code 200`)
            .toEqual(200);
    });

    /**
     * Positive tests
     */

    test(`Create Loan.`, async () => {
        expect(statusCode, `Status code 200`).toEqual(200);
    });

    test(`Read Loan by ID. @lms`, async () => {
        const response = await sendLMSRequest('get', `Loans/${loanId}`);

        expect(response.status, `Status code 200`)
            .toEqual(200);

        expect(response.data)
            .toEqual(expect.any(Object));

        expect(response.data.id)
            .toBe(loanId);

        expect(response.data.companyId, `Loan created with correct Owner ID`)
            .toBe(constants.loans.ownerId);
    });

    for (const state of loanStates) {
        test(`Loan change state for ${state}`, async () => {
            const requestBody = {"status": `${state}`};

            const response = await sendLMSRequest('patch', `Loans/${loanId}`, requestBody);

            expect(response.status, `Loan state is changed to ${state}`)
                .toEqual(200);
        });
    }

    test(`Get Detailed Loan by Loan ID. @lms`, async () => {
        const response = await sendLMSRequest('get', `Loans/${loanId}?detailed=true`);

        expect(response.status, `Status code 200`)
            .toEqual(200);

        expect(response.data.loanDetails, `True detailed format of the loan is not empty`)
            .not.toEqual(null);

        expect(response.data.loanDetails.loanId, `Correct credit is returned by a true detailed credit format `)
            .toEqual(loanId);
    });

    test(`Get a non-detailed loan by loan ID. @lms`, async () => {
        const response = await sendLMSRequest('get', `Loans/${loanId}?detailed=false`);

        expect(response.status, `Status code 200`)
            .toEqual(200);

        expect(response.data.loanDetails, `False detailed format of the loan is empty`)
            .toEqual(null);
    });

    test(`Get Loan Parameters by loan id. @lms`, async () => {
        const response = await sendLMSRequest('get', `LoanParameters?LoanId=${loanId}`);

        expect(response.status, `Status code 200`)
            .toEqual(200);

        expect(response.data[0].loanId, `Correct Loan is returned by LoanID`)
            .toEqual(loanId);
    });

    test(`Loan change state for Started. @lms`, async () => {
        const requestUpdateLoanStateBody = {
            "status": "Started"
        };

        const response = await sendLMSRequest('patch', `Loans/${loanId}`, requestUpdateLoanStateBody);

        expect(response.status, `Status code 200`)
            .toEqual(200);
    });

    test(`Replain Loan by loan id. @lms`, async () => {
        const requestBody = {
            "newLoanTemplateId": constants.loanTemplateIDs.totalDurationInDays60,
            "replainDate": "2023-06-10"
        };

        const response = await sendLMSRequest('put', `Loans/${loanId}`, requestBody);

        expect(response.status, `Status code 200`)
            .toEqual(200);
    });

    //todo ask about endpoint with arrays, currently tests fail
    test(`Get an array of loans by query parameters with the status of the loan Started.`, async () => {
        const response = await sendLMSRequest('get', `Loans?FromDate=${fromDate}&ToDate=${toDate}&LoanStatus=Started`);

        expect(response.status, `Status code 200`)
            .toEqual(200);

        expect(response.data[0].loanReceivables, `Loans receivable not empty with correct query parameters`)
            .not.toEqual(null);
    });

    test(`Get an array of loans by query parameters with the status of the loan Created.`, async () => {
        const response = await sendLMSRequest('get', `Loans?FromDate=${fromDate}&ToDate=${toDate}&LoanStatus=Created`);

        expect(response.status, `Status code 200`)
            .toEqual(200);

        expect(response.data[0].loanReceivables, `Loans receivable not empty with correct query parameters`)
            .not.toEqual(null);
    });

    test(`Get an array of loans by query parameters with the status of the loan Closed. @lms`, async () => {
        const response = await sendLMSRequest('get', `Loans?FromDate=${fromDate}&ToDate=${toDate}&LoanStatus=Closed`);

        expect(response.status, `Status code 200`)
            .toEqual(200);

        expect(response.data[0].loanReceivables, `Loans receivable not empty with correct query parameters`)
            .not.toEqual(null);
    });

    // test(`Deleted Loan isn't returned. @lms`, async () => {
    //     const response = await sendLMSRequest('get', `Loans/${loanId}`);
    //     expect(response.status, `Status code 204`).toEqual(204);
    // });

    // Extended Tests
    test(`Server validation for GET request by Invalid Loan ID. @lms`, async () => {
        const response = await sendLMSRequest('get', `Loans/${constants.invalidLoanIDs.id}`);
        expect(response.response.status, `Status code 500`).toEqual(500);
        // expect(response.data).toEqual(expect.any(Object));
        // expect(response.data.Message).toBe('Internal Server Error');
    });

    /**
     * Negative Tests
     */
    test(`Getting empty Loan Parameters by invalid Loan ID. @lms`, async () => {
        const response = await sendLMSRequest('get', `LoanParameters?LoanId=${constants.invalidLoanIDs.id}`);

        expect(response.status, `Status code 200`)
            .toEqual(200);

        expect(response.data, `Received answer contains an empty array`)
            .toEqual([]);
    });

    test(`Get failed data by invalid status. @lms`, async () => {
        const requestUpdateLoanStateBody = {
            "status": "WrongStatus"
        };

        const response = await sendLMSRequest('patch', `Loans/${loanId}`, requestUpdateLoanStateBody);

        expect(response.response.status, `Status code 400`)
            .toEqual(400);

        expect(response.code, `Error message for invalid LoanId`)
            .toContain('ERR_BAD_REQUEST');
    });

    test(`Get failed loan details by invalid Loan ID. @lms`, async () => {
        const response = await sendLMSRequest('get', `Loans/${constants.invalidLoanIDs.id}?detailed=true`);

        expect(response.response.status, `Status code 500`)
            .toEqual(500);

        expect(response.response.statusText, `Error message for invalid LoanId`)
            .toContain('Internal Server Error');
    });

    test(`Get failed non-detailed loan by invalid Loan ID. @lms`, async () => {
        const response = await sendLMSRequest('get', `Loans/${constants.invalidLoanIDs.id}?detailed=false`);

        expect(response.response.status, `Status code 500`)
            .toEqual(500);

        expect(response.response.statusText, `Error message for invalid LoanId`)
            .toContain('Internal Server Error');
    });
});
