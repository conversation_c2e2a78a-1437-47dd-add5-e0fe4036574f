{"loanTemplates": {"TemplateWithThreeInstallments": {"LoanFeePercentage": 1, "InstallmentsNumber": 3, "PaymentDelayInDays": 10, "PaymentIntervalInDays": 10, "MinimumLateFeeAmount": 10, "LateFeePercentage": 2, "GracePeriodInDays": 3, "EarlyPayPeriod": 5, "TotalDurationInDays": 100, "penaltyInterestTriggerRule": "TwoTimesThreeDaysOrOneTimeSevenDaysRule", "Code": "120"}, "TemplateWithTwoInstallments": {"LoanFeePercentage": 1, "InstallmentsNumber": 6, "PaymentDelayInDays": 11, "PaymentIntervalInDays": 10, "MinimumLateFeeAmount": 10, "LateFeePercentage": 2, "GracePeriodInDays": 3, "EarlyPayPeriod": 5, "TotalDurationInDays": 300, "penaltyInterestTriggerRule": "TwoTimesThreeDaysOrOneTimeSevenDaysRule", "Code": "100"}, "TemplateWithZeroInstallments": {"loanFeePercentage": 0, "installmentsNumber": 0, "paymentDelayInDays": 0, "paymentIntervalInDays": 0, "minimumLateFeeAmount": 0, "lateFeePercentage": 0, "gracePeriodInDays": 3, "earlyPayPeriod": 0, "totalDurationInDays": 0, "penaltyInterestTriggerRule": "TwoTimesThreeDaysOrOneTimeSevenDaysRule", "code": "60"}, "TemplateWithNineInstallments": {"loanFeePercentage": 0.04, "installmentsNumber": 9, "paymentDelayInDays": 30, "paymentIntervalInDays": 7, "minimumLateFeeAmount": 35, "lateFeePercentage": 0.01, "gracePeriodInDays": 3, "earlyPayPeriod": 0, "totalDurationInDays": 90, "penaltyInterestTriggerRule": "TwoTimesThreeDaysOrOneTimeSevenDaysRule", "code": "90"}}, "basisPoint": {"value": 280000, "updatedValue": 270000}, "loanTemplateIDs": {"totalDurationInDays30": "3476b83d-f7c7-492d-a986-a6e3b3b87b43", "totalDurationInDays60": "3ee4cbd6-935a-436e-b2d1-041ac9a5dc11", "totalDurationInDays90": "7ce0034d-800e-4ce0-9585-e9dd17338878"}, "invalidLoanTemplateIDs": {"id": "859c115e-0775-4fe1-b299-ea4bd533ba45"}, "loans": {"ownerId": "6461fe2baa42f651a66fef6b", "einHash": "ad6adc807481a5ffd747c54bf5db0008", "id": "d4913795-a402-44bf-a897-b54c5eaeb9c4"}, "invalidLoanIDs": {"id": "ecb58b93-d5e1-4184-b1f8-d101f4a3acb3"}, "invalidLoanParameterIDs": {"id": "ecb58b93-d5e1-4184-b1f8-d101f4a3acb"}, "latePaymentFees": [{"id": "bfd72acc-1928-4774-897e-cc3cff634474"}], "loanResivables": [{"newExpectedDate": "2023-06-26", "newExpectedAmount": 100.4, "newScheduleStatus": "Current", "receivableType": "Installment"}, {"newExpectedDate": "2023-06-26", "newExpectedAmount": 100.4, "newScheduleStatus": "Current", "receivableType": "Installment"}], "InputLoanStatus": {"started": "Started", "canceled": "Canceled", "closed": "Closed", "defaulted": "Defaulted", "recovered": "Recovered"}, "loanStatus": {"none": "None", "created": "Created", "started": "Started", "pending": "Pending", "canceled": "Canceled", "closed": "Closed", "defaulted": "Defaulted", "recovered": "Recovered"}, "loanReceivable": {"amount": 1000, "fee": 60, "loanReceivablesNumber": 7, "loanReceivables": [{"expectedDate": "", "expectedAmount": 60}, {"expectedDate": "", "expectedAmount": 117}, {"expectedDate": "", "expectedAmount": 177}, {"expectedDate": "", "expectedAmount": 177}, {"expectedDate": "", "expectedAmount": 177}, {"expectedDate": "", "expectedAmount": 177}, {"expectedDate": "", "expectedAmount": 175}]}, "loanUsers": [{"email": "<EMAIL>", "password": "Ss@22222222"}, {"email": "<EMAIL>", "password": "Ss@22222222"}], "penaltyInterest": {"basisPoint": 0.028, "daysInYear": 366, "enoughAmountForCreatePenalty": 5000, "notEnoughAmountForCreatePenalty": 300}, "loanReceivablesSchema": {"type": "object", "properties": {"id": {"type": "string"}, "companyId": {"type": "string"}, "amount": {"type": "number"}, "refundAmount": {"type": "number"}, "fee": {"type": "number"}, "activeLoanTemplateId": {"type": "string"}, "activeLoanTemplate": {"type": "null"}, "loanDetails": {"type": "null"}, "loanReceivables": {"type": "array", "items": {"type": "object", "properties": {"id": {"type": "string"}, "expectedDate": {"type": "string", "format": "date"}, "actualDate": {"type": "string", "format": "date"}, "adjustDate": {"type": null}, "expectedAmount": {"type": "number"}, "actualAmount": {"type": "number"}, "adjustAmount": {"type": "number"}, "status": {"type": "string"}, "scheduleStatus": {"type": "string"}, "type": {"type": "string"}, "loanId": {"type": "string"}, "loan": {"type": null}, "createdBy": {"type": "string"}, "updatedBy": {"type": null}}, "required": ["id", "expectedDate", "actualDate", "adjustDate", "expectedAmount", "actualAmount", "adjustAmount", "status", "scheduleStatus", "type", "loanId", "loan", "created<PERSON>y", "updatedBy"]}}, "payments": {"type": "array", "items": {"type": "object", "properties": {"id": {"type": "string"}, "date": {"type": "string", "format": "date"}, "amount": {"type": "number"}, "status": {"type": "string"}, "transactionNumber": {"type": "string"}, "type": {"type": "string"}, "subType": {"type": "string"}, "loanId": {"type": "string"}, "loan": {"type": "null"}}, "required": ["id", "date", "amount", "status", "transactionNumber", "type", "subType", "loanId", "loan"]}}, "loanParameters": {"type": "array", "items": {"type": "object", "properties": {"id": {"type": "string"}, "isActive": {"type": "boolean"}, "loanId": {"type": "string"}, "loanFeePercentage": {"type": "number"}, "installmentsNumber": {"type": "number"}, "paymentDelayInDays": {"type": "number"}, "paymentIntervalInDays": {"type": "number"}, "minimumLateFeeAmount": {"type": "number"}, "lateFeePercentage": {"type": "number"}, "gracePeriodInDays": {"type": "number"}, "earlyPayPeriod": {"type": "number"}, "totalDurationInDays": {"type": "number"}, "changeType": {"type": "string"}, "loanTemplateId": {"type": "null"}, "loanTemplate": {"type": "null"}, "code": {"type": "string"}, "note": {"type": "null"}, "createdBy": {"type": "null"}}, "required": ["id", "isActive", "loanId", "loanFeePercentage", "installmentsNumber", "paymentDelayInDays", "paymentIntervalInDays", "minimumLateFeeAmount", "lateFeePercentage", "gracePeriodInDays", "earlyPayPeriod", "totalDurationInDays", "changeType", "loanTemplateId", "loanTemplate", "code", "note", "created<PERSON>y"]}}, "status": {"type": "string"}, "isDeleted": {"type": "boolean"}, "isOverdue": {"type": "boolean"}, "startDate": {"type": "string", "format": "date"}, "closeDate": {"type": "null"}, "lastPaymentDate": {"type": "string", "format": "date"}, "lastSyncDate": {"type": "string", "format": "date-time"}}, "required": ["id", "companyId", "amount", "refundAmount", "fee", "activeLoanTemplateId", "activeLoanTemplate", "loanDetails", "loanReceivables", "payments", "loanParameters", "status", "isDeleted", "isOverdue", "startDate", "closeDate", "lastPaymentDate", "lastSyncDate"]}, "loanTemplateSchema": {"type": "object", "properties": {"id": {"type": "string"}, "loanFeePercentage": {"type": "number"}, "installmentsNumber": {"type": "integer"}, "paymentDelayInDays": {"type": "integer"}, "paymentIntervalInDays": {"type": "integer"}, "minimumLateFeeAmount": {"type": "number"}, "lateFeePercentage": {"type": "number"}, "gracePeriodInDays": {"type": "integer"}, "earlyPayPeriod": {"type": "integer"}, "totalDurationInDays": {"type": "integer"}, "penaltyInterestTriggerRule": {"type": "string"}, "code": {"type": "string"}}, "required": ["id", "loanFeePercentage", "installmentsNumber", "paymentDelayInDays", "paymentIntervalInDays", "minimumLateFeeAmount", "lateFeePercentage", "gracePeriodInDays", "earlyPayPeriod", "totalDurationInDays", "penaltyInterestTriggerRule", "code"]}, "paymentSchema": {"type": "object", "properties": {"status": {"type": "string"}, "transactionNumber": {"type": "string"}}, "required": ["status", "transactionNumber"]}, "paymentsSchema": {"type": "array", "items": {"type": "object", "properties": {"id": {"type": "string"}, "date": {"type": "string"}, "amount": {"type": "string"}, "status": {"type": "string"}, "transactionNumber": {"type": ["string", "null"]}, "type": {"type": "string"}, "subType": {"type": "string"}, "loanId": {"type": "string"}}, "required": ["id", "date", "amount", "status", "transactionNumber", "type", "subType", "loanId"]}}, "loanParametersSchema": {"type": "array", "items": {"type": "object", "properties": {"id": {"type": "string"}, "isActive": {"type": "boolean"}, "loanId": {"type": "string"}, "loanFeePercentage": {"type": "number"}, "installmentsNumber": {"type": "integer"}, "paymentDelayInDays": {"type": "integer"}, "paymentIntervalInDays": {"type": "integer"}, "minimumLateFeeAmount": {"type": "integer"}, "lateFeePercentage": {"type": "integer"}, "gracePeriodInDays": {"type": "integer"}, "earlyPayPeriod": {"type": "integer"}, "totalDurationInDays": {"type": "integer"}, "changeType": {"type": "string"}, "loanTemplateId": {"type": "string"}, "loanTemplate": {"type": "object", "properties": {"id": {"type": "string"}, "loanFeePercentage": {"type": "number"}, "installmentsNumber": {"type": "integer"}, "paymentDelayInDays": {"type": "integer"}, "paymentIntervalInDays": {"type": "integer"}, "minimumLateFeeAmount": {"type": "integer"}, "lateFeePercentage": {"type": "integer"}, "gracePeriodInDays": {"type": "integer"}, "earlyPayPeriod": {"type": "integer"}, "totalDurationInDays": {"type": "integer"}, "penaltyInterestTriggerRule": {"type": "string"}, "code": {"type": "string"}}, "required": ["id", "loanFeePercentage", "installmentsNumber", "paymentDelayInDays", "paymentIntervalInDays", "minimumLateFeeAmount", "lateFeePercentage", "gracePeriodInDays", "earlyPayPeriod", "totalDurationInDays", "penaltyInterestTriggerRule", "code"]}, "penaltyInterestTriggerRule": {"type": "string"}, "code": {"type": "string"}, "note": {"type": "string"}, "createdBy": {"type": "string"}}, "required": ["id", "isActive", "loanId", "loanFeePercentage", "installmentsNumber", "paymentDelayInDays", "paymentIntervalInDays", "minimumLateFeeAmount", "lateFeePercentage", "gracePeriodInDays", "earlyPayPeriod", "totalDurationInDays", "changeType", "loanTemplateId", "loanTemplate", "penaltyInterestTriggerRule", "code", "note", "created<PERSON>y"]}}}