﻿using System.Runtime.Serialization;
using System.Text.Json.Serialization;

namespace BlueTape.Aion.DataAccess.External.Models.Transactions;

[DataContract]
public class GetTransactionsResponseModel : BaseAionPageAbleResponseModel
{
    [JsonPropertyName("pageNumber")]
    public int PageNumber { get; set; }

    [JsonPropertyName("count")]
    public int Count { get; set; }

    [JsonPropertyName("numPages")]
    public int NumPages { get; set; }

    [JsonPropertyName("transactionsList")]
    public List<TransactionListObj> TransactionsList { get; set; } = null!;
}