@startuml

title Trade Credit Components Final Payment\n(not all components are shown)

participant "GetFinalPayment Func" as final1 #LightGray
database "MongoDb" as mongo
queue "Business operations" as bsqs #LightSalmon
participant "ProcessFinalPayment Func" as final2 #LightGray
queue "Payment Operations" as opsqs #LightSalmon
participant "Payment\nService" as pserv #SkyBlue
participant "Aion" as aion #Orange

autonumber

== Final payment (with LoanApplications+Compatibility Service [not shown]) ==

final1 -> mongo : Read\nLoanApplications\nand Operations
mongo --> final1
final1 -> bsqs : Place final payment draws\n""DRAW.ISSUE.FINAL""
bsqs -> final2 : Read business\nevents
final2 -> opsqs : Convert to Payment requests\n""CREATE.DRAW.FINALPAYMENT""
opsqs -> pserv : Read payment\noperations
pserv -> aion : ACH

@enduml