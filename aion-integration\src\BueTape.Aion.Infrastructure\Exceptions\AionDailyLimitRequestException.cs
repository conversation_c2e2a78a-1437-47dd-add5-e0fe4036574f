using System.Globalization;
using System.Net;
using System.Runtime.Serialization;
using BlueTape.Common.ExceptionHandling.Exceptions.Base;
using BlueTape.SNS.SlackNotification.Models;

namespace BueTape.Aion.Infrastructure.Exceptions;

[Serializable]
public class AionDailyLimitRequestException : DomainException
{
    public HttpStatusCode SourceHttpStatusCode { get; set; }
    public string? RequestPath { get; set; }

    public AionDailyLimitRequestException(
        string errorMessage, string requestPath, HttpStatusCode httpStatusCode, object? errorModel, HttpStatusCode statusCode = HttpStatusCode.BadRequest)
        : base(BuildErrorMessage(errorMessage), statusCode)
    {
        ErrorData = errorModel;
        SourceHttpStatusCode = httpStatusCode;
        RequestPath = requestPath;
    }

    protected AionDailyLimitRequestException(SerializationInfo info, StreamingContext context) : base(info, context)
    {
    }

    public override EventMessageBody GetSlackEventMessageBody(string source = "dotnet", string serviceName = "undefined", EventLevel eventLevel = EventLevel.Warning, int maxMessageLength = 400)
    {
        return new EventMessageBody
        {
            Message = $"ExceptionType: {GetType().Name} \n StatusCode: {HttpStatusCode} \n Message: {Message.TruncateMessage(400)}",
            EventLevel = SourceHttpStatusCode is HttpStatusCode.OK ? EventLevel.Warning : EventLevel.Error,
            EventName = $"Aion exceeded daily limit. RequestPath: {RequestPath}",
            EventSource = source,
            ServiceName = serviceName,
            TimeStamp = DateTime.UtcNow.ToString(CultureInfo.InvariantCulture),
            AwsAccountId = Environment.GetEnvironmentVariable("LP_AWS_ACCOUNT") ??
                           "Not provided in service"
        };
    }
    
    private static string BuildErrorMessage(string aionErrorMessage)
    {
        return $"Aion service respond with error: {aionErrorMessage}";
    }

    public override string Code => ErrorCodes.AionDailyLimitRequestError;
}