const jsdom = require("jsdom");

/**
 * Extract link from base64 and find it in email Html
 * @param {String} data
 * @param {Number} linkNumber
 */

// const getInvitationLinkFromBase64 = (encodedLink, linkNumber = 1) => {
//     const {document} = getConvertedDocumentFromBase64(encodedLink);
  
//     const a = document.querySelectorAll("a")[1];
//     const link = a.href;
//     return link;
// };

// const getVerificationLinkFromBase64 = (encodedLink, linkNumber = 1) => {
//     const {document} = getConvertedDocumentFromBase64(encodedLink);
  
//     const a = document.evaluate('//text()[3]")', document, null, 0, null);
//     const link = a.iterateNext().textContent.trim();
//     return link;
// };

const getLinkFromBase64 = (linkType, encodedLink) => {
    const {document} = getConvertedDocumentFromBase64(encodedLink);
    let link;
    switch(linkType){
        case "Invitation":
            link = document.querySelectorAll("a")[1].href;
        break;
        case "Verification":
            const a = document.evaluate('//text()[3]")', document, null, 0, null);
            link = a.iterateNext().textContent.trim();
        break;
    }
    return link;
};
const getConvertedDocumentFromBase64 = (encodedLink) => {
    const buff = Buffer.from(encodedLink, "base64");
    const mydata = buff.toString("UTF-8");
    const { JSDOM } = jsdom;
    const dom = new JSDOM(mydata);
    return dom.window;
};

module.exports = {getLinkFromBase64};