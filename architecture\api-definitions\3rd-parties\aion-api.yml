openapi: '3.0.0'
info:
  version: '1.0.0'
  title: 'Aion API'
  description: | 
    API definition of Aion banking services, made manually by documentation.

    No version number nor release date provided by Aion.

    Reference: https://aionfi.readme.io/reference/getting-started-with-your-api
servers:
- url: https://uam-stage.aionfi.com
  description: Auth Sandbox
- url: https://uam.aionfi.com
  description: Auth Production
- url: https://bb-stage.aionfi.com
  description: API Sandbox
- url: https://bb.aionfi.com
  description: API Production
paths:
  /api/login:
    post:
      tags:
        - authentication
      summary: Gets auth token
      description: Gets auth token
      operationId: getAuthToken
      requestBody:
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/APILoginRequest"
      responses: 
        200:
          description: Login result.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/APILoginResponse'
  /partner/login:
    post:
      tags:
        - authentication
      summary: Gets auth token for partner
      description: Gets auth token for partner
      operationId: getAuthTokenPartner
      requestBody:
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/PartnerLoginRequest"
      responses:
        200:
          description: Login result.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/PartnerLoginResponse'
  /api/bb/getAccounts:
    post:
      tags:
        - accounts
      summary: Gets accounts
      description: Gets accounts
      operationId: getAccounts
      requestBody:
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/GetAccountsRequest"
      responses:
        200:
          description: Accounts result.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/GetAccountsResponse'
  /api/bb/getAccountInfo:
    post:
      tags:
        - accounts
      summary: Get account info
      description: Get account info
      operationId: getAccountInfo
      requestBody:
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/GetAccountInfoRequest"
      responses:
        200:
          description: Account info result.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/GetAccountInfoResponse'
  /api/bb/getTransactions:
    post:
      tags:
        - transactions
      summary: Get transactions
      description: Get transactions
      operationId: getTransactions
      requestBody:
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/GetTransactionsRequest"
      responses:
        200:
          description: Transactions result.
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/GetTransactionsResponse'
  /api/bb/createCounterparty:
    post:
      tags:
        - counterparty
      summary: Create counterparty
      description: Create counterparty
      operationId: createCounterparty
      requestBody:
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/CreateCounterpartyRequest"
      responses:
        200:
          description: Create counterparty result.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/CreateCounterpartyResponse'
  /api/bb/getCounterparties:
    post:
      tags:
        - counterparty
      summary: Get counterparties
      description: Get counterparties
      operationId: getCounterparties
      requestBody:
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/GetCounterpartiesRequest"
      responses:
        200:
          description: Get counterparties result.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/GetCounterpartiesResponse'
  /api/bb/addTransferMethod:
    post:
      tags:
        - counterparty
      summary: Add transfer method
      description: Add transfer method
      operationId: addTransferMethod
      requestBody:
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/AddTransferMethodRequest"
      responses:
        200:
          description: Add transfer method result.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/AddTransferMethodResponse'
  /api/bb/getTransferMethods:
    post:
      tags:
        - counterparty
      summary: Get transfer methods
      description: Get transfer methods
      operationId: getTransferMethods
      requestBody:
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/GetTransferMethodsRequest"
      responses:
        200:
          description: Get transfer methods result.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/GetTransferMethodsResponse'
  /api/bb/createACHTransfer:
    post:
      tags:
        - ach
      summary: Create ACH transfer
      description: Create ACH transfer
      operationId: createACHTransfer
      requestBody:
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/CreateACHTransferRequest"
      responses:
        200:
          description: Create ACH transfer result.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/CreateACHTransferResponse'
  /api/bb/getACHTransfers:
    post:
      tags:
        - ach
      summary: Get ACH transfers
      description: Get ACH transfers
      operationId: getACHTransfers
      requestBody:
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/GetACHTransfersRequest"
      responses:
        200:
          description: Get ACH transfer result.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/GetACHTransfersResponse'
components:
  schemas:
    APILoginRequest:
      type: object
      properties:
        userId:
          type: string
        password:
          type: string
        apiKey:
          type: string
    APILoginResponse:
      type: object
      properties:
        authToken:
          type: string
        result:
          type: boolean
        responseMessage:
          type: string
    PartnerLoginRequest:
      type: object
      properties:
        userId:
          type: string
        password:
          type: string
        partnerKey:
          type: string
    PartnerLoginResponse:
      type: object
      properties:
        authToken:
          type: string
        result:
          type: boolean
        responseMessage:
          type: string
    GetAccountsRequest:
      type: object
      nullable: true
    GetAccountsResponse:
      type: object
      properties:
        responseMessage:
          type: string
        result:
          type: boolean
        bankAccounts:
          type: array
          items:
            $ref: '#/components/schemas/BankAccount'
        transactionsList:
          type: array
          items:
            $ref: '#/components/schemas/Transaction'
        counterpartiesList:
          type: array
          items:
            $ref: '#/components/schemas/CounterParty'
        achList:
          type: array
          items:
            $ref: '#/components/schemas/Ach'
        transferMethodsList:
          type: array
          items:
            $ref: '#/components/schemas/TransferMethod'
    BankAccountInfo:
      type: object
      properties:
        id:
          type: string
        name:
          type: string
        accountNumber:
          type: string
        address:
          $ref: "#/components/schemas/Address"
        accountType:
          type: string
        status:
          type: string
        availableBalance:
          type: string
        currentBalance:
          type: string
        currency:
          type: string
        routingNumber:
          type: string
        locked:
          type: boolean
    BankAccount:
      type: object
      properties:
        accountId:
          type: string
        mask:
          type: string
        accountName:
          type: string
        nickName:
          type: string
        accountSubType:
          type: string
        accountNumber:
          type: string
        routingNumber:
          type: string
        ledgerType:
          type: string
        classification:
          type: string
        title:
          type: string
        status:
          type: string
        locked:
          type: boolean
        address:
          $ref: "#/components/schemas/Address"
        availableBalance:
          type: number
        currentBalance:
          type: number
        availableBalanceStr:
          type: string
        currentBalanceStr:
          type: string
        currency:
          type: string
        createdAt:
          type: string
          format: date
        updatedAt:
          type: string
          format: date
        openedAt:
          type: string
          format: date
    Address:
      type: object
      properties:
        line1:
          type: string
        line2:
          type: string
        countrySubDivisionCode:
          type: string
        postalCode:
          type: string
        city:
          type: string
        countryCode:
          type: string
    GetAccountInfoRequest:
      type: object
      properties:
        accountId:
          type: string
    GetAccountInfoResponse:
      type: object
      properties:
        responseMessage:
          type: string
        result:
          type: boolean
        count:
          type: integer
        numPages:
          type: integer
        pageNumber:
          type: integer
        pageable:
          type: boolean
        accountInfo:
          $ref: "#/components/schemas/BankAccountInfo"
        bankAccounts:
          type: array
          items:
            $ref: '#/components/schemas/BankAccount'
        transactionsList:
          type: array
          items:
            $ref: '#/components/schemas/Transaction'
        counterpartiesList:
          type: array
          items:
            $ref: '#/components/schemas/CounterParty'
        achList:
          type: array
          items:
            $ref: '#/components/schemas/Ach'
        transferMethodsList:
          type: array
          items:
            $ref: '#/components/schemas/TransferMethod'
    GetTransactionsRequest:
      type: object
      required:
        - accountId
      properties:
        accountId:
          type: string
        fromDate:
          type: string
          format: date
        toDate:      
          type: string
          format: date
        size:
          type: integer
        providerStatus:
          type: string
        page:
          type: integer          
    GetTransactionsResponse:
      type: object
      properties:
        result:
          type: boolean
        count:
          type: integer
        numPages:
          type: integer
        pageNumber:
          type: integer
        pageable:
          type: boolean
        bankAccounts:
          type: array
          items:
            $ref: '#/components/schemas/BankAccount'
        transactionsList:
          type: array
          items:
            $ref: '#/components/schemas/Transaction'
        counterpartiesList:
          type: array
          items:
            $ref: '#/components/schemas/CounterParty'
        achList:
          type: array
          items:
            $ref: '#/components/schemas/Ach'
        transferMethodsList:
          type: array
          items:
            $ref: '#/components/schemas/TransferMethod'
    Transaction:
      type: object
      properties:
        id:
          type: string
        accountId:
          type: string
        accountNumber:
          type: string
        achId:
          type: string
        amount:
          type: number
        amountStr:
          type: string
        balance:
          type: number
        balanceStr:
          type: string
        txnDate:
          type: string
          format: date
        providerStatus:
          type: string
        displayDescription:
          type: string
        transactionId:
          type: string
        traceNumber:
          type: string
        transactionType:
          type: string
        transactionCode:
          type: string
        rail:
          type: string
    CreateCounterpartyRequest:
      type: object
      properties:
        counterparty:
          $ref: "#/components/schemas/CreateCounterParty"
    CreateCounterParty:
      type: object
      properties:
        type: 
          type: string
          enum:
            - Individual
            - Business
        nameOnAccount:
          type: string
        email:
          type: string
    CreateCounterpartyResponse:
      type: object
      properties:
        responseMessage:
          type: string
        result:
          type: boolean
        counterpartiesObj:
          $ref: "#/components/schemas/CounterParty"
    CounterParty:
      type: object
      properties:
        objectId:
          type: string
        id:
          type: string
        email:
          type: string
        type:
          type: string
        nameOnAccount:
          type: string
        lastTransferDate:
          type: string
          format: date
        lastTransferMethod:
          type: string
        createdAt:
          type: string
          format: date
        updatedAt:
          type: string
          format: date
        transferMethodACH:
          type: boolean
        transferMethodWire:
          type: boolean
        active:
          type: boolean        
    GetCounterpartiesRequest:
      type: object
      properties:
        page:
          type: integer
        size:
          type: integer
    GetCounterpartiesResponse:
      type: object
      properties:
        responseMessage:
          type: string
        result:
          type: boolean
        count:
          type: integer
        numPages:
          type: integer
        pageNumber:
          type: integer
        pageable:
          type: boolean
        counterpartiesList:
          type: array
          items:
            $ref: '#/components/schemas/CounterParty'
    AddTransferMethodRequest:
      type: object
      properties:
        counterpartyId:
          type: string
        accountId:
          type: string
        nickName:
          type: string
        bankDetail:
          $ref: "#/components/schemas/BankDetail"
    BankDetail:
      type: object
      properties:
        accountNumber:
          type: string
        routingNumber:
          type: string
        accountType:
          type: string
          enum:
            - Checking
            - Savings
        type:
          type: string
          enum:
            - ACH
    AddTransferMethodResponse:
      type: object
      properties:
        responseMessage:
          type: string
        result:
          type: boolean
    GetTransferMethodsRequest:
      type: object
      properties:
        counterpartyId:
          type: string
    GetTransferMethodsResponse:
      type: object
      properties:
        responseMessage:
          type: string
        result:
          type: boolean
        count:
          type: integer
        numPages:
          type: integer
        pageNumber:
          type: integer
        pageable:
          type: boolean
        accountInfo:
          $ref: "#/components/schemas/BankAccountInfo"
        bankAccounts:
          type: array
          items:
            $ref: '#/components/schemas/BankAccount'
        transactionsList:
          type: array
          items:
            $ref: '#/components/schemas/Transaction'
        counterpartiesList:
          type: array
          items:
            $ref: '#/components/schemas/CounterParty'
        achList:
          type: array
          items:
            $ref: '#/components/schemas/Ach'
        transferMethodsList:
          type: array
          items:
            $ref: '#/components/schemas/TransferMethod'
    TransferMethod:
      type: object
      properties: 
        id:
          type: string
        type:
          type: string
        nickName:
          type: string
        accountId:
          type: string
        associatedObjectId:
          type: string
        default:
          type: boolean
        bankDetail:
          $ref: "#/components/schemas/BankDetail"
    CreateACHTransferRequest:
      type: object
      properties:
        achObj:
          $ref: "#/components/schemas/AchCreate"
    AchCreate:
      type: object
      properties:
        transferMethodId:
          type: string
          description: objectId of the transfer method
        accountId:
          type: string
        accountNumber:
          type: string
        amount:
          type: string
        counterpartyId:
          type: string
          description: id field of the counterparty
        counterpartyName:
          type: string
        transactionType:
          type: string
          enum:
            - Push
            - Pull
          description: Push for credit, Pull for debit
        sendEmail:
          type: boolean
        addenda:
          type: array
          items:
            type: string
          description: This will be added as addenda on the ACH
        description:
          type: string
        serviceType:
          type: string
          enum:
            - Standard
        secCode:
          type: string
          enum:
            - ppd
            - ccd
          description: ppd for individuals, ccd for business      
    CreateACHTransferResponse:
      type: object
      properties:
        responseMessage:
          type: string
        result:
          type: boolean
        achObj:
          $ref: "#/components/schemas/Ach"
    Ach:
      type: object
      properties:
        id:
          type: string
        accountId:
          type: string
        amount:
          type: string
        counterpartyId:
          type: string
        description:
          type: string
        direction:
          type: string
        error:
          type: string
        secCode:
          type: string
        status:
          type: string
          enum:
            - created
            - pending
            - processing
            - sent
            - error
            - canceled
        counterpartyName:
          type: string
        counterpartyType:
          type: string
        email:
          type: boolean
        userNote:
          type: string
        sendEmail:
          type: boolean
        effectiveDate:
          type: string
        transferMethodId:
          type: string
        initiatedBy:
          type: string
        contextIdentifier:
          type: string
        addenda:
          type: array
          items:
            type: string
        createdAt:
          type: string
          format: date
        updatedAt:
          type: string
          format: date
        originator:
          $ref: "#/components/schemas/ParticipantAccount"
        receiver:
          $ref: "#/components/schemas/ParticipantAccount"
        referenceId:
          type: string
          description: Aion's internal id.
        paymentType:
          type: string
        postingCode:
          type: string
        posting:
          type: string
        reasonCode:
          type: string
        reasonData:
          type: string
        traceNumber:
          type: string
          description: External reference, counterparty can see it.
        transactionType:
          type: string
        serviceType:
          type: string
        wasReturned:
          type: boolean
        wasCorrected:
          type: boolean
        canceledAt:
          type: string
          format: date
        processedAt:
          type: string
          format: date
        completedAt:
          type: string
          format: date
        postedAt:
          type: string
          format: date
        rejectedAt:
          type: string
          format: date
        original:
          $ref: "#/components/schemas/Payment"
        previous:
          $ref: "#/components/schemas/Payment"
        accountNumber:
          type: string
    Payment:
      type: object
      properties:
        paymentId:
          type: string
          description: Aion's internal id.
    ParticipantAccount:
      type: object
      properties:
          accountNumber:
            type: string
          routingNumber:
            type: string
          name:
            type: string
          accountType:
            type: string
          identification:
            type: string
    GetACHTransfersRequest:
      type: object
      properties:
        page:
          type: integer
        size:
          type: integer
    GetACHTransfersResponse:
      type: object
      properties:
        responseMessage:
          type: string
        result:
          type: boolean
        count:
          type: integer
        numPages:
          type: integer
        pageNumber:
          type: integer
        pageable:
          type: boolean
        accountInfo:
          $ref: "#/components/schemas/BankAccountInfo"
        bankAccounts:
          type: array
          items:
            $ref: '#/components/schemas/BankAccount'
        transactionsList:
          type: array
          items:
            $ref: '#/components/schemas/Transaction'
        counterpartiesList:
          type: array
          items:
            $ref: '#/components/schemas/CounterParty'
        achList:
          type: array
          items:
            $ref: '#/components/schemas/Ach'
        transferMethodsList:
          type: array
          items:
            $ref: '#/components/schemas/TransferMethod'
  securitySchemes:
    ApiKey:
      type: apiKey
      in: header
      name: AionAuth
security:
  - ApiKey: []