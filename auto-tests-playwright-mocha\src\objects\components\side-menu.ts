import { Locator, Page} from '@playwright/test';
import {BasePage} from '../base.page';

export class SideMenu extends BasePage {
    constructor(page: Page) {
        super(page);
    };

    sideMenuTabs = {
        home: this.page.locator('[data-testid="menu_link__self"]'),
        pay: this.page.locator('[data-testid="menu_link_pay"] >> nth=0'),
        tradeCredit: this.page.locator('[data-testid="menu_link_tradeCredit"]'),
        sales: this.page.locator('[data-testid="menu_link_sales"]'),
        projects: this.page.locator('[data-testid="menu_link_projects"]'),
        referFriends: this.page.locator('[data-testid="menu_link_refer"]'),

        //dev
        notifications: this.page.getByTestId('menu_link_notifications'),
        menuButton: this.page.getByTestId('menu_link_more'),
        invoices: this.page.getByTestId('menu_link_pay-invoices'),
    };

    sideMenuSubTabs = {
        pay: {
            invoices: this.page.locator('[data-testid="menu_link_pay-invoices"]'),
            payables: this.page.locator('[data-testid="menu_link_payables"]'),
            credit: this.page.locator('[data-testid="menu_link_credit"] >> nth=0'),
            notifications: this.page.locator('[data-testid="menu_link_notifications"]'),
            accounts: this.page.locator('[data-testid="menu_link_accounts"]'),
            profile: this.page.locator('[data-testid="menu_link_profile"]'),
        },
        tradeCredit: {
            draws: this.page.locator('[data-testid="menu_link_draws"]'),
        },
        sales: {
            receivables: this.page.locator('[data-testid="menu_link_receivables"]'),
            customers: this.page.locator('[data-testid="menu_link_customers"]'),
            transactions: this.page.locator('[data-testid="menu_link_transactions"]'),
            settlements: this.page.locator('[data-testid="menu_link_settlements"]'),
            setting: this.page.locator('[data-testid="menu_link_settings"]'),
        },
        menu: {
            accounts: this.page.getByTestId('menu_link_accounts'),
            profile: this.page.getByTestId('menu_link_profile')
        }
    };

    async clickProject() {
        await this.sideMenuTabs.projects.click();
    }

    async openMenuTab(tab: Locator) {
        await tab.click();
    };

    async openPaySubTab(subTab) {
        await this.openMenuTab(this.sideMenuTabs.pay);
        await subTab.click();
    };

    async openTradeCreditSubTab(subTab) {
        await this.openMenuTab(this.sideMenuTabs.tradeCredit);
        await subTab.click();
    };

    async openSalesSubTab(subTab) {
        await this.openMenuTab(this.sideMenuTabs.sales);
        await subTab.click();
    };

    async openAccountsFromLeftSideMenu() {
        await this.sideMenuTabs.menuButton.click();
        await this.sideMenuSubTabs.menu.accounts.click();
    }

    async openInvoicesTab() {
        await this.sideMenuTabs.invoices.click();
    }
}