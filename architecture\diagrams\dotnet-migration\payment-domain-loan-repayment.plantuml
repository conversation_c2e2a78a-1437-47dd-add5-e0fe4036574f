@startuml Loan Repayment
title Loan Repayment static architecture (component diagram)
!include <awslib/AWSCommon>

' Uncomment the following line to create simplified view
!include <awslib/AWSSimplified>

!include <awslib/Compute/Lambda>

skinparam responseMessageBelowArrow true

interface "EB Cron" as cron #Black
component "getDue\n//Lambda//" as lambdaGetDue #Orange
component "LMS" as lms #PaleTurquoise
queue "DueLoans" as sqsDue #HotPink
component "processDue\n//Lambda//" as lambdaProcessDue  #Orange
component "Finicity" as fin #LightGrey
database "Mongo" as mongo #LightSteelBlue
database "Mongo" as mongo2 #LightSteelBlue
component "CBW &\nTabapay" as providers #LightGrey

cron -r-> lambdaGetDue
lambdaGetDue -d-> lms
lambdaGetDue -d-> mongo2
lambdaGetDue -r-> sqsDue
sqsDue -r-> lambdaProcessDue
lambdaProcessDue -d-> lms
lambdaProcessDue -r-> providers
lambdaProcessDue -d-> fin
lambdaProcessDue -d-> mongo

@enduml