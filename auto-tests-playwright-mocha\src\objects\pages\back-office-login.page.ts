import {BasePage} from '../base.page';

const pageLocator = '"Login"';

export class BackOfficeLoginPage extends BasePage {
    constructor(page, locator = pageLocator){
        super(page, locator); 
    };
    
    inputFields = {
        email: this.page.locator('[placeholder="E-mail"]'),
        password: this.page.locator('[placeholder="Password"]'),
    };

    buttons = {
        logIn: this.page.locator('[type="submit"]'),
    };

    async login(email, password){
        await this.inputFields.email.fill(email);
        await this.inputFields.password.fill(password);
        await this.buttons.logIn.click();
    };
}