openapi: '3.0.0'
info:
  version: '0.0.1'
  title: Loan Application Domain Model
  description: | 
    Model definition of Loan Domain related business models.
paths:
  /GeneralApplication:
    get:
      tags:
        - loanapplication
      summary: Loan Application model
      description: Loan Application model
      operationId: getLoanApplication
      responses:
        200:
          description: Loan Application model
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/GeneralApplication'
components:
  schemas:
    GeneralApplication:
      type: object
      required:
        - id
      properties:
        id:
          type: string
          description: Identifier of loan application
          example: 637dff014189f7dd1fa510a1
        createdAt:
          type: string
          format: datetime
          description: Time of operation creating. Immutable after creation.
          example: 2023-01-04T09:47:57.477Z
        updatedAt:
          type: string
          format: datetime
          description: Time of last operation update. In the time of creation equals to createdAt.
          example: 2023-01-04T09:47:57.477Z
        sub:
          type: string
        companyId:
          type: string
        type:
          $ref: '#/components/schemas/ApplicationType'
          description: Maybe obsolete
        data:
          $ref: '#/components/schemas/ApplicationData'
        current:
          type: string
        filled:
          type: array
          items:
            type: string
        submitDate:
          type: string
          format: datetime
        approvalDate:
          type: string
          format: datetime
        disburseDate:
          type: string
          format: datetime
        invoiceId:
          type: string
        isSentBack:
          type: boolean
        loanId:
          type: string
        loanStatus:
          type: string
          description: Should be detailed as enum
        prequalificationDetails:
          type: object
        virtualCardDetails:
          type: object
    ApplicationType:
      type: string
      enum:
        - general
        - supplier
        - loan
    ApplicationData:
      type: object
      properties:
        businessInfo:
          $ref: '#/components/schemas/ApplicationDataGroupModel'
        businessOwner:
          $ref: '#/components/schemas/ApplicationDataGroupModel'
        finance:
          $ref: '#/components/schemas/ApplicationDataGroupModel'
        bank:
          $ref: '#/components/schemas/ApplicationDataGroupModel'
    ApplicationDataGroupModel:
      type: object
      properties:
        group:
          type: string
        title:
          type: string
        items:
          type: array
          items:
            $ref: '#/components/schemas/ApplicationDataGroupItemModel'
    ApplicationDataGroupItemModel:
      type: object
      properties:
        identifier:
          type: string
        title:
          type: string
        filled:
          type: boolean
        content:
          type: object
