﻿using BlueTape.Integrations.Aion.Infrastructure.Enums;
using System.Diagnostics.CodeAnalysis;

namespace BueTape.Aion.Infrastructure.ServiceBusMessages.Report;

[ExcludeFromCodeCoverage]
public class AionInternalTransactionReportRequest
{
    public List<InternalTransactionReportRequest> InternalTransactionReportRequests { get; set; } = new();
}

public class InternalTransactionReportRequest
{
    public PaymentSubscriptionType PaymentSubscriptionType { get; set; }
    public int? InternalTransactionPage { get; set; }
}