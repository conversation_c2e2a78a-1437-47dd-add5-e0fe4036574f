name: Verify payments beta

on:
  workflow_dispatch:

jobs:
  verify-payments-beta:
    timeout-minutes: 30
    environment: BETA
    runs-on: ubuntu-latest
    container:
      image: mcr.microsoft.com/playwright:v1.44.1-jammy
    env:
      SLACK_WEBHOOK_URL: ${{ vars.SLACK_WEBHOOK_URL }}
      CI_ENVIRONMENT_URL: ${{ vars.CI_ENVIRONMENT_URL }}
      CI_ENVIRONMENT_BACKEND_URL: ${{ vars.CI_ENVIRONMENT_BACKEND_URL }}
      ADMIN_BACKEND_URL: ${{ vars.ADMIN_BACKEND_URL }}
      ADMIN_EMAIL: ${{ vars.ADMIN_EMAIL }}
      ADMIN_PASSWORD: ${{ vars.ADMIN_PASSWORD }}
      ADMIN_FIRSTNAME: ${{ vars.ADMIN_FIRSTNAME }}
      ADMIN_LASTNAME: ${{ vars.ADMIN_LASTNAME }}
      USER_DOMAIN: ${{ vars.USER_DOMAIN }}
      USER_BACKEND_URL: ${{ vars.USER_BACKEND_URL }}
      USER_EMAIL: ${{ vars.USER_EMAIL }}
      USER_PASSWORD: ${{ vars.USER_PASSWORD }}
      USER_FIRSTNAME: ${{ vars.USER_FIRSTNAME }}
      USER_LASTNAME: ${{ vars.USER_LASTNAME }}
      USER_COMPANYNAME: ${{ vars.USER_COMPANYNAME }}
      USER_CUSTOMEREMAIL: ${{ vars.USER_CUSTOMEREMAIL }}
      USER_CUSTOMERPASSWORD: ${{ vars.USER_CUSTOMERPASSWORD }}
      USER_CUSTOMERID: ${{ vars.USER_CUSTOMERID }}
      USER_CUSTOMER_COMPANYNAME: ${{ vars.USER_CUSTOMER_COMPANYNAME }}
      GENERIC_API_BASE_URL: ${{ vars.GENERIC_API_BASE_URL }}
      X_BLUETAPE_KEY: ${{ vars.X_BLUETAPE_KEY }}
      X_INTEGRATION_ACCOUNT_ID: ${{ vars.X_INTEGRATION_ACCOUNT_ID }}
      MONGODB_URI: ${{ vars.MONGODB_URI }}
      ACCESS_KEY_AWS: ${{ vars.ACCESS_KEY_AWS }}
      SECRET_ACCESS_KEY_AWS: ${{ vars.SECRET_ACCESS_KEY_AWS }}
      TestGroupId: ${{ github.run_id }}
      test_env: beta
      PAY_NOW_BASE_URL: ${{ vars.PAY_NOW_BASE_URL }}
    steps:
      - uses: actions/checkout@v3
      - uses: actions/setup-node@v3
        with:
          node-version: 18
          
      - name: Install dependencies
        run: npm ci
            
      - name: Run Playwright tests
        run: npx playwright test --grep "@paymentsE2E" --reporter=./sqs-reporter.js
        
      - uses: actions/upload-artifact@v3
        if: always()  
        with:
          name: playwright-report
          path: playwright-report/
          retention-days: 7