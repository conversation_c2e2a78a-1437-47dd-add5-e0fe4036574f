﻿using BlueTape.Aion.Application.Abstractions;
using BlueTape.Aion.Application.Abstractions.MessageSenders;
using BlueTape.Aion.DataAccess.External.Models.CreateAchTransfer.Response;
using BlueTape.Aion.DataAccess.External.Models.InstantTransfer.Response;
using BlueTape.Aion.DataAccess.External.Models.InternalTransfer;
using BlueTape.ServiceBusMessaging.Attributes;
using BueTape.Aion.Infrastructure.Options;
using BueTape.Aion.Infrastructure.ServiceBusMessages;
using BueTape.Aion.Infrastructure.ServiceBusMessages.Report;
using Microsoft.Extensions.Options;

namespace BlueTape.Aion.Application.Service;

public class ReportService(
    IAchService achService,
    ITransactionService transactionService,
    IOptions<AionReportOptions> options,
    IAionExternalTransactionMessageSender aionExternalTransactionMessageSender,
    IAionInternalTransactionMessageSender aionInternalTransactionMessageSender)
    : IReportService
{
    private readonly AionReportOptions _reportOptions = options.Value;

    public async Task RunExternalTransactionStatusReportAsync(AionExternalAchTransactionReportRequest request, CancellationToken ctx)
    {
        var listToProcess = new List<AchResponseObj>();
        var listOfReturns = new List<AchResponseObj>();
        var listOfWire = new List<WireTransferObjItem>();
        var listOfInstant = new List<InstantTransferObjectItemResponse>();
        var nextExternalAchReportRequest = new AionExternalAchTransactionReportRequest();
        var externalAchReportPages = request.ExternalAchReportRequests.ToList();

        foreach (var externalAchReportPage in externalAchReportPages)
        {
            var achPage = externalAchReportPage.AchTransactionPage;
            var achReturnPage = externalAchReportPage.AchReturnTransactionPage;
            var instantPage = externalAchReportPage.InstantTransactionPage;
            var wirePage = externalAchReportPage.WireTransactionPage;
            var paymentSubscription = externalAchReportPage.PaymentSubscriptionType;

            int? nextAchPage = null;
            int? nextAchReturnPage = null;
            int? nextWirePage = null;
            int? nextInstantPage = null;

            if (achPage.HasValue)
            {
                var achListResult = await achService
                    .GetAchTransactionsByPageAsync((int)achPage, _reportOptions.DateRange, paymentSubscription.ToString(), ctx);
                listToProcess.AddRange(achListResult.Item1);
                nextAchPage = achListResult.Item2;
            }

            if (achReturnPage.HasValue)
            {
                var achListReturnResult = await achService
                    .GetAchReturnTransactionsByPageAsync((int)achReturnPage, _reportOptions.DateRange, paymentSubscription.ToString(), ctx);
                listOfReturns.AddRange(achListReturnResult.Item1);
                nextAchReturnPage = achListReturnResult.Item2;
            }

            if (instantPage.HasValue)
            {
                var instantTransactionsByPageAsync = await achService
                    .GetInstantTransactionsByPageAsync((int)instantPage, _reportOptions.DateRange, paymentSubscription.ToString(), ctx);
                listOfInstant.AddRange(instantTransactionsByPageAsync.Item1);
                nextInstantPage = instantTransactionsByPageAsync.Item2;
            }

            if (wirePage.HasValue)
            {
                var wireTransactionsByPageAsync = await achService
                    .GetWireTransactionsByPageAsync((int)wirePage, _reportOptions.DateRange, paymentSubscription.ToString(), ctx);
                listOfWire.AddRange(wireTransactionsByPageAsync.Item1);
                nextWirePage = wireTransactionsByPageAsync.Item2;
            }

            if (achPage.HasValue || achReturnPage.HasValue || instantPage.HasValue || wirePage.HasValue)
            {
                nextExternalAchReportRequest.ExternalAchReportRequests.Add(new ExternalAchReportRequest()
                {
                    AchReturnTransactionPage = nextAchReturnPage,
                    AchTransactionPage = nextAchPage,
                    InstantTransactionPage = nextInstantPage,
                    WireTransactionPage = nextWirePage,
                    PaymentSubscriptionType = paymentSubscription
                });
            }
        }

        await transactionService.SaveAionAchTransactionsAsync(listToProcess, ctx);
        await transactionService.SaveAionAchTransactionReturnsAsync(listOfReturns, ctx);
        await transactionService.SaveAionWireTransactionsAsync(listOfWire, ctx);
        await transactionService.SaveAionInstantTransactionsAsync(listOfInstant, ctx);

        if (nextExternalAchReportRequest.ExternalAchReportRequests.Count == 0) return;

        var body = new AionExternalTransactionMessage { ExternalAchTransactionReportRequest = nextExternalAchReportRequest };
        await aionExternalTransactionMessageSender.SendMessage(new ServiceBusMessageBt<AionExternalTransactionMessage>(body), ctx);
    }

    public async Task RunInternalTransactionStatusReportAsync(AionInternalTransactionReportRequest request, CancellationToken ctx)
    {
        var bookTransferObjList = new List<BookTransferObj>();

        var nextInternalTransactionReportRequest = new AionInternalTransactionReportRequest();

        var internalTransactionReportPages =
            request.InternalTransactionReportRequests.Where(x => x.InternalTransactionPage.HasValue).ToList();

        foreach (var internalTransactionReportPage in internalTransactionReportPages)
        {
            var internalTransfersResult = await achService
                .GetAchInternalTransactionsByPageAsync(internalTransactionReportPage.InternalTransactionPage ?? 0,
                    _reportOptions.DateRange,
                    internalTransactionReportPage.PaymentSubscriptionType.ToString(),
                    ctx);

            bookTransferObjList.AddRange(internalTransfersResult.Item1);

            if (internalTransfersResult.Item2.HasValue)
            {
                nextInternalTransactionReportRequest.InternalTransactionReportRequests.Add(new InternalTransactionReportRequest()
                {
                    InternalTransactionPage = internalTransfersResult.Item2,
                    PaymentSubscriptionType = internalTransactionReportPage.PaymentSubscriptionType,
                });
            }
        }

        await transactionService.SaveAionInternalTransactionsAsync(bookTransferObjList, ctx);

        if (nextInternalTransactionReportRequest.InternalTransactionReportRequests.Count == 0) return;

        var body = new AionInternalTransactionMessage
        {
            AionInternalTransactionReportRequest = nextInternalTransactionReportRequest
        };
        await aionInternalTransactionMessageSender.SendMessage(new ServiceBusMessageBt<AionInternalTransactionMessage>(body), ctx);
    }
}