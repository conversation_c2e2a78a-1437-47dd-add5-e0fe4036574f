﻿using AutoMapper;
using BlueTape.Aion.API.Extensions;
using BlueTape.Aion.API.Models.Errors;
using BlueTape.Aion.Application.Abstractions;
using BlueTape.Aion.Application.Models.Ach.Pull;
using BlueTape.Aion.Application.Models.InternalTransfer;
using BlueTape.Aion.Application.SandboxDataHelper;
using BlueTape.Aion.DataAccess.External.Constants;
using BlueTape.Aion.Domain.Constants;
using BlueTape.Aion.Domain.Extensions;
using BlueTape.Integrations.Aion;
using BlueTape.Integrations.Aion.Ach.CreateAchTransfer;
using BlueTape.Integrations.Aion.Infrastructure.Enums;
using BlueTape.Integrations.Aion.Internal;
using BlueTape.Integrations.Aion.Transactions;
using BlueTape.Utilities.Models;
using FluentValidation;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using System.ComponentModel.DataAnnotations;
using System.Diagnostics.CodeAnalysis;
using System.Text.Json;
using TransactionListObj = BlueTape.Aion.DataAccess.External.Models.Transactions.TransactionListObj;

namespace BlueTape.Aion.API.Controllers;

[ApiController]
[Route("/api/transactions")]
[ExcludeFromCodeCoverage]
[Authorize]
[ProducesResponseType(typeof(List<ErrorModel>), StatusCodes.Status400BadRequest)]
[ProducesResponseType(typeof(void), StatusCodes.Status401Unauthorized)]
[ProducesResponseType(typeof(List<ErrorModel>), StatusCodes.Status500InternalServerError)]
public class TransactionController(
    IValidator<CreateAchModel> validator,
    IMapper mapper,
    IAchService achService,
    IInternalTransferService internalTransferService,
    IValidator<CreateInternalModel> createInternalValidator)
    : ControllerBase
{
    [HttpGet]
    [ProducesResponseType(typeof(PaginatedResponse<TransactionListObj>), StatusCodes.Status200OK)]
    public async Task<IActionResult> GetTransactions([FromQuery] TransactionsQuery request, CancellationToken ctx)
    {
        var response = await achService.GetAllTransactionsAsync(request, ctx);
        return Ok(response);
    }

    [HttpPost("internal")]
    [ProducesResponseType(typeof(BlueTapeTransactionResponseModel), StatusCodes.Status200OK)]
    public async Task<IActionResult> CreateInternalTransfer(
        [FromHeader(Name = "PaymentRequestId")][Required] string paymentRequestId,
        [FromHeader(Name = ClientConstants.PaymentSubscriptionTypeHeader)][Required] string paymentSubscriptionType,
        [FromBody] CreateInternalModel createInternalModel,
        CancellationToken ctx)
    {
        if (Environment.GetEnvironmentVariable(EnvironmentConstants.Environment).IsEnvironmentDevOrBeta())
        {
            var fakeResult = SandboxTransferData.GetInternalTransferObj(createInternalModel);

            return Ok(new BlueTapeTransactionResponseModel
            {
                AionFee = 0,
                AionReferenceId = fakeResult.TraceNumber,
                AionResponse = JsonSerializer.Serialize(fakeResult)
            });
        }

        await createInternalValidator.ValidateAndThrowsManualAsync(createInternalModel, ctx);
        var model = mapper.Map<CreateInternal>(createInternalModel);
        var result = await internalTransferService.CreateInternalTransferAsyncV2(model, paymentSubscriptionType, ctx);
        return Ok(result);
    }

    [HttpPost("external/transactionType/{transactionType}/paymentMethodType/{paymentMethodType}")]
    [ProducesResponseType(typeof(BlueTapeTransactionResponseModel), StatusCodes.Status200OK)]
    public async Task<IActionResult> CreateExternalTransaction(
        [FromHeader(Name = "PaymentRequestId")][Required] string paymentRequestId,
        [FromHeader(Name = ClientConstants.PaymentSubscriptionTypeHeader)][Required] string paymentSubscriptionType,
        [FromBody] CreateAchModel achModel,
        [FromRoute][Required] TransactionType transactionType,
        [FromRoute][Required] AionPaymentMethodType paymentMethodType,
        CancellationToken ctx)
    {
        if (Environment.GetEnvironmentVariable(EnvironmentConstants.Environment).IsEnvironmentDevOrBeta())
        {
            var fakeResult = new BlueTapeTransactionResponseModel();

            switch (paymentMethodType)
            {
                case AionPaymentMethodType.ACH:
                    var achFake = SandboxTransferData.GetAchResponseModel(achModel);
                    fakeResult.AionFee = achFake.AchObj.FeeAmount;
                    fakeResult.AionReferenceId = achFake.AchObj.ReferenceId;
                    fakeResult.AionResponse = JsonSerializer.Serialize(fakeResult);
                    break;
                case AionPaymentMethodType.WIRE:
                    var wireFake = SandboxTransferData.GetWireResponseModel(achModel);
                    fakeResult.AionFee = wireFake.WireTransferObj.FeeAmount;
                    fakeResult.AionReferenceId = wireFake.WireTransferObj.ReferenceId;
                    fakeResult.AionResponse = JsonSerializer.Serialize(fakeResult);
                    break;
                case AionPaymentMethodType.INSTANT:
                    var instantFake = SandboxTransferData.GetInstantResponseModel(achModel);
                    fakeResult.AionFee = instantFake.InstantTransfer.FeeAmount;
                    fakeResult.AionReferenceId = instantFake.InstantTransfer.RtpPaymentInfo.ReferenceId;
                    fakeResult.AionResponse = JsonSerializer.Serialize(fakeResult);
                    break;
                default:
                    throw new ArgumentOutOfRangeException(nameof(paymentMethodType), paymentMethodType, null);
            }

            return Ok(fakeResult);
        }

        await validator.ValidateAndThrowsManualAsync(achModel, ctx);
        var data = mapper.Map<CreateAch>(achModel);
        var response = await achService.StartAionTransactionAsync(data, transactionType, paymentSubscriptionType, paymentMethodType, ctx);

        return Ok(response);
    }
}
