@startuml
title CBW Integration Automation Elements
!include <awslib/AWSCommon>

' Uncomment the following line to create simplified view
!include <awslib/AWSSimplified>

!include <awslib/Compute/Lambda>
!include <awslib/ApplicationIntegration/SQS>
!include <awslib/Storage/SimpleStorageServiceS3>
!include <awslib/Database/DocumentDBwithMongoDBcompatibility>

left to right direction
skinparam responseMessageBelowArrow true

package "Check status" {
    Lambda(cbwCheckStatus, "cbwCheckStatus", "")
    SimpleStorageServiceS3(s3Reports, "CBW reports", "")
    SQS(CBWStatusDeadLetterFifoQueue, "CBWStatusDLQ", "")
}

package "Loan buyback" {
    Lambda(cbwLoanBuyback, "cbwLoanBuyback\n(scheduled)", "")
    DocumentDBwithMongoDBcompatibility(dbMongoLoan, "MongoDb", "")
}

package "Early warning" {
    component "CBW" as cbw #LightGrey
    Lambda(cbwEW, "cbwEW", "")
    SQS(CBWEWFifoQueue, "CBWEWSQS.Fifo", "")
    DocumentDBwithMongoDBcompatibility(dbMongoEW, "MongoDb", "")
}

package "Virtual Cards" {
    Lambda(cbwVirtualCard, "cbwVirtualCard\n(scheduled)", "")
    DocumentDBwithMongoDBcompatibility(dbMongoVirtualCard, "MongoDb", "")
    component "CBW" as cbwVC #LightGrey
    component "LMS" as lms #LightBlue
}

CBWEWFifoQueue-r->cbwEW
cbwEW -r-> dbMongoEW: Company & user\ndata, write EW
cbwEW -d-> cbw: Early warning

cbwLoanBuyback -d-> dbMongoLoan: Write\nloan_buyback
cbwLoanBuyback -d-> dbMongoLoan: Read loan\noperations

cbwVirtualCard -r-> dbMongoVirtualCard: Card operations
cbwVirtualCard -d-> cbwVC: Handle cards
cbwVirtualCard -d-> lms: Manage loans

s3Reports -d-> cbwCheckStatus: S3 bucket\nevents
cbwCheckStatus -r-> CBWStatusDeadLetterFifoQueue: Unprocessable events

@enduml