import {BaseTest} from '../../tests/test-utils';
import {BaseAPI} from '../base-api';
import sendUserRequest from '../common/send-user-request';

export default async function createInvoice(session: string, challenge: string) {
    const endpoint = `v1/invoices/invoice`;
    const dueDate: string = BaseTest.getDueDate();
    const invoiceName = `Invoice${BaseTest.dateTimePrefix()}`;
    const body = {
        "id": "",
        "company_id": "",
        "type": "invoice",
        "invoice_number": `${invoiceName}`,
        "operation_id": "",
        "payer_id": "",
        "customer_account_id": "",
        "contacts": [],
        "customer": null,
        "supplierInvitationDetails": {
            "name": `${invoiceName}`,
            "firstName": "",
            "lastName": "",
            "email": "",
            "phone": "",
            "type": "Material",
            "paymentMethodId": "",
            "userId": ""
        },
        "invoice_date": `${dueDate}`,
        "material_description": "",
        "note": "",
        "notes": [],
        "material_subtotal": "60.00",
        "tax_amount": "0.0",
        "refunded_amount": "",
        "invoice_due_date": `${dueDate}`,
        "expiration_date": null,
        "address": "",
        "unitNumber": "",
        "city": "",
        "state": "",
        "zip": "",
        "lat": null,
        "lng": null,
        "addressType": "Pickup",
        "invoiceNotificationType": "Both",
        "invoice_document": "invoices/638716c58e9cd61222455d59/0.vnp4ekyvsm.png",
        "document_name": "testFile.png",
        "status": "PLACED",
        "isDeleted": false,
        "tax_exempted": false,
        "approved": true,
        "dismiss_reasons": null,
        "railz": null,
        "quoteId": null,
        "supplierInvitationDetails.phone": "",
        "total_amount": "60.00"
    };
    try {
        await sendUserRequest('post', endpoint, session, challenge, body);
    } catch (error) {
        console.log(error);
        return error;
    }
};

export async function createCustomerInvoice(session: string, challenge: string, customerAccountId = '', totalAmount = "60.00") {
    const endpoint = `v1/invoices/invoice`;
    const dueDate: string = BaseTest.getDueDate();
    const currentDate: string = BaseTest.getCurrentDate();
    const invoiceName = `Invoice${BaseTest.dateTimePrefix()}`;
    const body = {
        "id": "",
        "company_id": "",
        "type": "invoice",
        "invoice_number": `${invoiceName}`,
        "operation_id": "",
        "payer_id": "",
        "customer_account_id": `${customerAccountId}`,
        "contacts": [],
        "customer": null,
        "supplierInvitationDetails": null,
        "invoice_date": `${currentDate}`,
        "material_description": null,
        "note": "",
        "notes": [],
        "material_subtotal": Number(totalAmount) * 0.9,
        "tax_amount": Number(totalAmount) * 0.1,
        "refunded_amount": "",
        "invoice_due_date": `${dueDate}`,
        "expiration_date": null,
        "address": "",
        "unitNumber": "",
        "city": "",
        "state": "",
        "zip": "",
        "lat": null,
        "lng": null,
        "addressType": "Service",
        "invoiceNotificationType": "Both",
        "invoice_document": null,
        "document_name": null,
        "status": "PLACED",
        "isDeleted": false,
        "tax_exempted": false,
        "approved": true,
        "dismiss_reasons": null,
        "quoteId": null,
        "currentDate": `${currentDate}`,
        "total_amount": totalAmount
    };
    try {
        const response = await sendUserRequest('post', endpoint, session, challenge, body);
        const res = await BaseAPI.convertToJson(response);
        return res;
    } catch (error) {
        console.log(error);
        return error;
    }
}
