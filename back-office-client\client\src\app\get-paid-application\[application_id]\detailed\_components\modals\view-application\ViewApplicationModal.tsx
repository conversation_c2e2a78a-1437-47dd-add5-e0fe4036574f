import { useTranslation } from 'react-i18next'
import { useMemo } from 'react'

import { StyledModal } from '@/components/common/Modal'
import StyledTitle from '@/components/common/typography/StyledTitle'
import type { IModalProps } from '@/globals/types'
import { StyledButton } from '@/components/common/Button'
import { useCreditApplicationDraftQuery } from '@/lib/redux/api/credit-application-draft'
import CentredSpinner from '@/components/common/CentredSpinner'
import type { DraftSection } from '@/components/common/CreditApplicationDraft/types'
import {
  creditApplicationDraftBankDetailsFields,
  creditApplicationDraftBusinessInfoFields,
  creditApplicationDraftBusinessOwnerFields,
  creditApplicationDraftCoOwnerFields,
  creditApplicationDraftFinanceFields,
  mapDraftToObject,
} from '@/components/common/CreditApplicationDraft/utils'
import type { ICreditApplicationMappedDraft } from '@/lib/redux/api/credit-application-draft/types'
import CreditApplicationDraftViewer from '@/components/common/CreditApplicationDraft/CreditApplicationDraftViewer'

interface IProps extends IModalProps {
  applicationId: string
}

export const ViewApplicationModal = ({
  isOpened,
  onClose,
  applicationId,
}: Readonly<IProps>): JSX.Element => {
  const { t } = useTranslation()

  const { data: draft } = useCreditApplicationDraftQuery({
    applicationId,
  })

  const transformedDraft = draft ? mapDraftToObject(draft) : null

  const businessInfoSection = useMemo(
    () => ({
      title: t('viewCreditApplicationModal.businessDetails'),
      fields: [
        creditApplicationDraftBusinessInfoFields.businessName(t),
        creditApplicationDraftBusinessInfoFields.dba(t),
        creditApplicationDraftBusinessInfoFields.businessPhone(t),
        creditApplicationDraftBusinessInfoFields.businessAddress(t),
        creditApplicationDraftBusinessInfoFields.startDate(t),
        creditApplicationDraftBusinessInfoFields.type(t),
      ],
    }),
    [t],
  )

  const businessOwnerSection = useMemo(
    () => ({
      title: t('viewCreditApplicationModal.ownerDetails'),
      fields: [
        creditApplicationDraftBusinessOwnerFields.isOwner(t),
        creditApplicationDraftBusinessOwnerFields.isAuthorized(t),
        creditApplicationDraftBusinessOwnerFields.ownershipPercentage(t),
        creditApplicationDraftBusinessOwnerFields.address(t),
        creditApplicationDraftBusinessOwnerFields.birthdate(t),
        creditApplicationDraftBusinessOwnerFields.firstName(t),
        creditApplicationDraftBusinessOwnerFields.lastName(t),
        creditApplicationDraftBusinessOwnerFields.email(t),
        creditApplicationDraftBusinessOwnerFields.phone(t),
        creditApplicationDraftBusinessOwnerFields.id(t),
      ],
    }),
    [t],
  )

  const coOwnersSections = useMemo(
    () =>
      transformedDraft?.data?.coOwnerInfo?.map((_, index) => ({
        title: t('viewCreditApplicationModal.coOwnerDetails', {
          count: index,
        }),
        fields: [
          creditApplicationDraftCoOwnerFields.id(t, index),
          creditApplicationDraftCoOwnerFields.firstName(t, index),
          creditApplicationDraftCoOwnerFields.lastName(t, index),
          creditApplicationDraftCoOwnerFields.type(t, index),
          creditApplicationDraftCoOwnerFields.percentOwned(t, index),
          creditApplicationDraftCoOwnerFields.address(t, index),
          creditApplicationDraftCoOwnerFields.phone(t, index),
          creditApplicationDraftCoOwnerFields.email(t, index),
          creditApplicationDraftCoOwnerFields.birthday(t, index),
          creditApplicationDraftCoOwnerFields.entityName(t, index),
        ],
      })) ?? [],
    [t, transformedDraft],
  )

  const financeDetailsSection = useMemo<
    DraftSection<ICreditApplicationMappedDraft>
  >(
    () => ({
      title: t('viewCreditApplicationModal.financeDetails'),
      fields: [
        creditApplicationDraftFinanceFields.revenue(t),
        creditApplicationDraftFinanceFields.debt(t),
        creditApplicationDraftFinanceFields.howMuchCredit(t),
        creditApplicationDraftFinanceFields.arAdvanceRequestedLimit(t),
      ],
    }),
    [t],
  )

  const bankDetailsSection = useMemo<
    DraftSection<ICreditApplicationMappedDraft>
  >(
    () => ({
      title: t('viewCreditApplicationModal.financeDetails'),
      fields: [creditApplicationDraftBankDetailsFields.account(t)],
    }),
    [t],
  )

  const sections = useMemo(
    () => [
      businessInfoSection,
      businessOwnerSection,
      ...coOwnersSections,
      financeDetailsSection,
      bankDetailsSection,
    ],
    [
      businessInfoSection,
      businessOwnerSection,
      coOwnersSections,
      financeDetailsSection,
      bankDetailsSection,
    ],
  )

  return (
    <StyledModal
      open={isOpened}
      title={
        <StyledTitle level={4}>
          {t('viewCreditApplicationModal.viewGetPaidApplication')}
        </StyledTitle>
      }
      onCancel={onClose}
      destroyOnClose
      footer={
        <StyledButton type="text" onClick={onClose}>
          {t('viewCreditApplicationModal.close')}
        </StyledButton>
      }
    >
      {transformedDraft === null ? (
        <CentredSpinner />
      ) : (
        <CreditApplicationDraftViewer
          draft={transformedDraft}
          sections={sections}
        />
      )}
    </StyledModal>
  )
}
