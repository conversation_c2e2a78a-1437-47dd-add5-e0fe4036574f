@startuml NoSupplier Draw Process (.NET)

title NoSupplier Draw Process (.NET)

participant "OnBoarding Service" as obs #SkyBlue
queue "Draw Events Queue" as dq #PaleVioletRed
participant "Loan Flow Service" as lfs #SkyBlue
participant "VCard Service" as vs #SkyBlue
participant "CBW" as cbw #LightGray
participant "LMS" as lms #SkyBlue
queue "Payment Queue" as pq #PaleVioletRed
participant "Payment Domain Services" as payment #SkyBlue

autonumber

== Approve draw, create virtual card ==

obs --> obs : Draw approved
obs -> dq : Place event\n""Draw.Approved""\nwith type\n""virtualCard""
dq -> lfs : Consume draw events
lfs -> vs : Issue virtual card w/ short expiration
vs -> cbw : Issue virtual card w/ short expiration

== Payment plan switched to NoSupplier ==

obs --> obs : Switch to NoSupplier
obs -> dq : Place event\n""Draw.Approved""\nwith type\n""nosupplier""
dq -> lfs : Consume draw events
lfs -> vs : Deactivate card
vs -> cbw : Deactivate card
lfs -> lms : Create and start draw
lfs -> pq : Place payment request\n""CREATE.DRAW.DISBURSEMENT""
pq -> payment : Consume payment events
payment --> payment : Create necessary transactions\n(not detailed here)

@enduml