﻿import {BasePage} from '../base.page';
import {Action} from "../../database/autoTests/entities/AutoTestReport";

const pageLocator = '"Welcome back"';

export class IntegrationPaymentPage extends BasePage {
    constructor(page, locator = pageLocator) {
        super(page, locator);
    };

    inputFields = {
        email: this.page.locator('[data-testid="login"]'),
        password: this.page.locator('[data-testid="password"]'),
    };

    buttons = {
        loginToMakePayment: this.page.locator('"Log in to make a payment"'),
        logIn: this.page.locator('"Log In"'),
        continue: this.page.locator('"Continue"'),
        payWithBankOfAmerica: this.page.locator('"Bank of America"').first(),
        agreeAndPay: this.page.locator('"Agree & Pay"'),
        payWithBTC: this.page.locator('"No fees & payments until day 30"'),
        thirdOptionDays: this.page.locator('"90 days"'),
        btcContinue: this.page.locator('"Continue"')
    };

    async loginAndPayWithAch(email, password, actionsPerTest: Action[]) {
        actionsPerTest.push({description: "Click loginToMakePayment"})
        await this.buttons.loginToMakePayment.click();
        actionsPerTest.push({description: `Fill email input: ${email}`})
        await this.inputFields.email.fill(email);
        actionsPerTest.push({description: "Click continue"})
        await this.buttons.continue.click();
        actionsPerTest.push({description: `Fill password input: ${password}`})
        await this.inputFields.password.fill(password);
        actionsPerTest.push({description: `Click login`})
        await this.buttons.logIn.click({delay: 2000});
        actionsPerTest.push({description: `Click to pay with Bank Of America`})
        await this.buttons.payWithBankOfAmerica.click({delay: 2000});
        actionsPerTest.push({description: `Click agree and pay`})
        await this.buttons.agreeAndPay.click({delay: 2000});
        
        await this.page.waitForTimeout(10000)
    };

    async loginAndPayWithBTC(email, password, actionsPerTest: Action[]) {
        actionsPerTest.push({description: "Click loginToMakePayment"})
        await this.buttons.loginToMakePayment.click();
        actionsPerTest.push({description: `Fill email input: ${email}`})
        await this.inputFields.email.fill(email);
        actionsPerTest.push({description: "Click continue"})
        await this.buttons.continue.click();
        actionsPerTest.push({description: `Fill password input: ${password}`})
        await this.inputFields.password.fill(password);
        actionsPerTest.push({description: `Click login`})
        await this.buttons.logIn.click({delay: 5000});
        actionsPerTest.push({description: `Click pay with BTC`})
        await this.buttons.payWithBTC.click({delay: 7000})
        actionsPerTest.push({description: `Choose  90 days`})
        await this.buttons.thirdOptionDays.click({delay: 7000})
        actionsPerTest.push({description: `Click  first continue button`})
        await this.buttons.btcContinue.click({delay: 5000})
        actionsPerTest.push({description: `Click  second continue button`})
        await this.buttons.btcContinue.click({delay: 9000})
        
        await this.page.waitForTimeout(10000)
    };
}