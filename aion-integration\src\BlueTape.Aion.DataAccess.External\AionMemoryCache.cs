﻿using BlueTape.Aion.DataAccess.External.Abstractions;
using BlueTape.Aion.DataAccess.External.Models.Login;
using BlueTape.Integrations.Aion.Infrastructure.Enums;
using System.Collections.Concurrent;

namespace BlueTape.Aion.DataAccess.External;

public class AionMemoryCache : IAionMemoryCache
{
    private readonly ConcurrentDictionary<PaymentSubscriptionType, AionApiKeyCachingParameters> _authApiKeys = new();
    private const int CacheTimeToLiveInMinutes = 59;

    public string GetAuthApiKey(PaymentSubscriptionType paymentSubscription)
    {
        var isApiKeyExistInCache = _authApiKeys.TryGetValue(paymentSubscription, out var apiKeyCachingParameters);

        return IsValidApiKey(isApiKeyExistInCache, apiKeyCachingParameters) ? apiKeyCachingParameters!.ApiKey : string.Empty;
    }

    public void SetAuthApiKey(string value, PaymentSubscriptionType paymentSubscription)
    {
        var isApiKeyExistInCache = _authApiKeys.TryGetValue(paymentSubscription, out var apiKeyCachingParameters);

        if (IsValidApiKey(isApiKeyExistInCache, apiKeyCachingParameters)) return;

        _authApiKeys.AddOrUpdate(paymentSubscription,
            addValueFactory: _ => new AionApiKeyCachingParameters()
            {
                ApiKey = value,
                TimeStamp = DateTime.UtcNow
            },
            updateValueFactory: (_, existingApiKeyCachingParameters) =>
            {
                if (IsValidApiKey(true, existingApiKeyCachingParameters))
                {
                    return existingApiKeyCachingParameters;
                }

                existingApiKeyCachingParameters.ApiKey = value;
                existingApiKeyCachingParameters.TimeStamp = DateTime.UtcNow;
                return existingApiKeyCachingParameters;
            });
    }

    private static bool IsValidApiKey(bool isExistsInCache, AionApiKeyCachingParameters? apiKeyCachingParameters)
    {
        return isExistsInCache && !string.IsNullOrEmpty(apiKeyCachingParameters?.ApiKey) &&
               apiKeyCachingParameters.TimeStamp.HasValue &&
               (DateTime.UtcNow - apiKeyCachingParameters.TimeStamp).Value.TotalMinutes <= CacheTimeToLiveInMinutes;
    }
}