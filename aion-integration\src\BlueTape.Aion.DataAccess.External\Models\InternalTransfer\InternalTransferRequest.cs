﻿using System.Runtime.Serialization;
using System.Text.Json.Serialization;

namespace BlueTape.Aion.DataAccess.External.Models.InternalTransfer;

[DataContract]
public class InternalTransferRequest
{
    [JsonPropertyName("FromAccountNumber")]
    public string FromAccountNumber { get; set; } = null!;

    [JsonPropertyName("ToAccountNumber")]
    public string ToAccountNumber { get; set; } = null!;

    [JsonPropertyName("amount")]
    public string Amount { get; set; } = null!;

    [JsonPropertyName("SenderName")]
    public string SenderName { get; set; } = null!;

    [JsonPropertyName("ReceiverName")]
    public string ReceiverName { get; set; } = null!;

    [JsonPropertyName("senderDescription")]
    public string SenderDescription { get; set; } = null!;

    [JsonPropertyName("receiverDescription")]
    public string ReceiverDescription { get; set; } = null!;

    [JsonPropertyName("UserNote")]
    public string UserNote { get; set; } = null!;

    [JsonPropertyName("contextIdentifier")]
    public string ContextIdentifier { get; set; } = null!;
}