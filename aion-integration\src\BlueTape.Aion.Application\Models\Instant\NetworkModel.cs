using System.Runtime.Serialization;
using System.Text.Json.Serialization;

namespace BlueTape.Aion.Application.Models.Instant;

[DataContract]
public class NetworkModel
{
    [JsonPropertyName("messageId")]
    public string? MessageId { get; set; }

    [JsonPropertyName("createdAt")]
    public DateTime CreatedAt { get; set; }

    [JsonPropertyName("currency")]
    public string? Currency { get; set; }

    [JsonPropertyName("interbankSettlementDate")]
    public DateTime InterbankSettlementDate { get; set; }
    
    [JsonPropertyName("numberOfTransactions")]
    public decimal NumberOfTransactions { get; set; }
    
    [JsonPropertyName("interbankSettlementAmount")]
    public decimal InterbankSettlementAmount { get; set; }
    
    [JsonPropertyName("settlementMethod")]
    public string? SettlementMethod { get; set; }
    
    [JsonPropertyName("clearingSystemCode")]
    public string? ClearingSystemCode { get; set; }
}