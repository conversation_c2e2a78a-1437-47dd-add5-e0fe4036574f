import {BaseTest, test} from "../../test-utils";
import {expect} from "@playwright/test";
import {
    cancelPaymentRequest, cancelTransactionRequest,
    createTransactionForPaymentRequest, findRequiredPaymentRequest,
    getRequiredPaymentRequestByDay,
    getRequiredPaymentRequestById,
    updatedTransactionsInfo
} from "../../../api/paynow/send-pay-now-request";
import {getCurrentDay} from "../../../api/base-api";
import {triggerPaymentScheduledJobWithDelay} from "../../../api/paynow/queue-ivents";
import * as _ from "lodash";


const payNowData = JSON.parse(JSON.stringify(require('../../../constants/PayNow-data.json')));

const totalAmountString: string = Math.floor(Math.random() * 200).toString() + "." + Math.floor(Math.random() * 99).toString();
const day: string = getCurrentDay();
const sellerId: string = process.env.SELLER_ID;
const payerId: string = process.env.PAYER_ID;
const payeeId: string = process.env.PAYEE_ID;


test.describe(`Pay now API positive fractured. @paynow`, async () => {

    /**
    Data that tests receive from each other
    */

    let requiredPaymentId: string;
    let requiredPaymentRequest;
    let allPaymentRequests;
    let requiredPaymentRequestGettedById;
    let requiredPaymentRequestGettedByIdData;
    let paymentStatus: string;
    let transactions;
    let generatedTransactionId;

    /**
    Trigger payment job for transactions sync
    */

    test.afterEach(async () => {
        await triggerPaymentScheduledJobWithDelay();
    });

    /**
    Create invoice via UI
    Get all required data from response
    */

    test(`Invoice creating.`, async ({pageManager, supplierForPayNowPageManager}) => {
        const invoiceNumber = BaseTest.dateTimePrefix() + 'invoiceNumber';
        await pageManager.onBoardingPage.createInvoiceViaUI(invoiceNumber, totalAmountString);
        await supplierForPayNowPageManager.payModal.openInvoicesPage();
        await supplierForPayNowPageManager.invoicesList.clickOnFirstRequiredInvoice();
        await supplierForPayNowPageManager.invoicesDetails.payForLatestInvoice();

        allPaymentRequests = await getRequiredPaymentRequestByDay(day);
        requiredPaymentRequest = await findRequiredPaymentRequest(allPaymentRequests, +totalAmountString);
        requiredPaymentId = await requiredPaymentRequest.id;
        requiredPaymentRequestGettedById = await getRequiredPaymentRequestById(requiredPaymentId);
        requiredPaymentRequestGettedByIdData = await requiredPaymentRequestGettedById.data;
        paymentStatus = await requiredPaymentRequestGettedByIdData.status;
    });

    /**
    Cancel payment request
    */

    test(`Cancel payment request.`, async () => {
        const cancelResponse = await cancelPaymentRequest(requiredPaymentId, sellerId);
        expect(cancelResponse.status).toEqual(200);
        const updatedPaymentRequestResponse = await getRequiredPaymentRequestById(requiredPaymentId);
        expect(updatedPaymentRequestResponse.data.status).toEqual(payNowData.statuses.Canceled);
    });

    /**
    Add transaction to payment request
    Sequence number will be from 1 to 4
    */

    test(`Add transaction to payment request.`, async () => {
        transactions = await updatedTransactionsInfo(requiredPaymentId);
        generatedTransactionId = `generatedTransactionId${BaseTest.dateTimePrefix()}`;
        const response = await createTransactionForPaymentRequest(requiredPaymentId, generatedTransactionId, day, +totalAmountString);
        expect(response.status).toEqual(200);

        transactions = await updatedTransactionsInfo(requiredPaymentId);
        expect(_.find(transactions, {transactionNumber: generatedTransactionId}).status).toBe(payNowData.statuses.Placed);
    });

    /**
    Cancel added transaction
    No effect on other transaction
    */

    test(`Cancel added transaction.`, async () => {
        transactions = await updatedTransactionsInfo(requiredPaymentId);
        const transactionId = _.find(transactions, {transactionNumber: generatedTransactionId}).id;
        const response = await cancelTransactionRequest(transactionId, sellerId);
        expect(response.status).toEqual(200);

        transactions = await updatedTransactionsInfo(requiredPaymentId);
        expect(_.find(transactions, {transactionNumber: generatedTransactionId}).status).toEqual(payNowData.statuses.Canceled);
    });
});
