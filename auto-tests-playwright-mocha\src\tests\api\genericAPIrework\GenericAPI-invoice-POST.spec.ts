import {expect} from '@playwright/test';
import {BaseTest, test} from '../../test-utils';
import {
    createInvoiceR,
    sendGenericApiRequest
} from "../../../api/common/send-generic-api-request";
import {searchCustomerByObjectId} from "../../../database/customers/customer-searcher";
import {searchInvoiceByObjectId} from "../../../database/invoices/invoiceSearcherByObjectId";
import {reasonStringParser} from "../../../utils/genericAPI-utils";
import {wipeCustomerFields} from "../../../database/customers/customer-fields-wiper";
import {generateCompanyRequestBody} from "./genericAPI-request-body-functions-and-storage";

const constants = JSON.parse(JSON.stringify(require('../../../constants/constants.json')));
const genericErrors = JSON.parse(JSON.stringify(require('../../../constants/generic-api-errors.json')));

const badRequestError = genericErrors.common["badRequest"];
const customerAlreadyExist = genericErrors["customer"].reason["errorCustomerAlreadyExist"];

test.describe(`Invoice creation @genericR @API @POST @invoice`, async () => {
    let beforeAllResponse;
    let customerId: string;
    let customerEmail: string;
    let customerPhone: string;
    let uniqueBusinessPhone: string;
    let currentDate: string;
    let mongoIdOfCreatedCustomer: string;

    test.beforeAll(`Customer creation, getting required fields`, async () => {
        currentDate = new Date().toISOString();

        customerEmail = `sergei+genericapicustomer${BaseTest.dateTimePrefix()}@bluetape.com`;
        customerPhone = BaseTest.getCellPhoneNumber();
        customerId = `customerId_${BaseTest.getGUID()}`;

        uniqueBusinessPhone = BaseTest.getCellPhoneNumber();
        let companyId = BaseTest.getGUID();

        const customerFirstName = constants["generic"].firstName;
        const customerLastName = constants["generic"].lastName;


        const companyRequestBody = await generateCompanyRequestBody({
            companyId: companyId,
            businessPhoneNumber: uniqueBusinessPhone,
            customerCellPhoneNumber: customerPhone,
            customerEmailAddress: customerEmail,
            customerFirstName: customerFirstName,
            customerId: customerId,
            customerLastName: customerLastName,
            isCustomerUsed: true
        });

        beforeAllResponse = await sendGenericApiRequest('post', 'company', companyRequestBody);

        expect(beforeAllResponse.status).toEqual(201);

        mongoIdOfCreatedCustomer = await beforeAllResponse.data.customers[0].blueTapeCustomerId;
    });

    test(`Invoice creation. Deep check with DB. @positive @debug @createInvoice`, async () => {
        const invoiceNumber = `invoiceNumber_${BaseTest.getGUID()}`;

        /**
         * Connector object
         * @var connectorDocumentId equal to invoiceId in request
         */
        const connectorDocumentId = `connectorDocumentId${BaseTest.getGUID()}`;

        /**
         * Project object in connector object
         */
        const projectName = `projectName_${BaseTest.getGUID()}`;
        const projectAddress = `projectAddress_${BaseTest.getGUID()}`;
        const projectId = `projectId_${BaseTest.getGUID()}`;
        const jobId = `jobId_${BaseTest.getGUID()}`;

        const response = await createInvoiceR({
            customerId: customerId,
            subTotalAmount: 1,
            invoiceNumber: invoiceNumber,
            projectAddress: projectAddress,
            invoiceId: connectorDocumentId,
            projectName: projectName,
            projectId: projectId,
            jobId: jobId
        });

        const blueTapeCustomerId = await response.data.blueTapeId;

        expect(response.status)
            .toEqual(201);

        expect(response.statusText)
            .toEqual('Created');

        const bluetapeIdOfInvoice = await response.data.blueTapeId;

        const DBObjectInvoice = await searchInvoiceByObjectId(bluetapeIdOfInvoice);

        const DBObjectCustomer = await searchCustomerByObjectId(mongoIdOfCreatedCustomer);

        const objectForAssertion = {
            'invoice_number': invoiceNumber,
            'type': 'invoice',
            'status': 'PLACED',
            'company_id': DBObjectCustomer['company_id'],
            'payer_id': '',
            'operation_id': ''
        };

        const objectForConnectorAssertion = {
            'integration_id': DBObjectCustomer['connector']['integration_id'],
            'connector_type': 'Generic',
            'sync_type': 'ToBlueTape',
            'document_id': connectorDocumentId,
        };

        const objectForProjectAssertion = {
            'external_project_id': projectId,
            'name': projectName,
            'address': projectAddress,
            'jobId': jobId,
        }

        expect(DBObjectInvoice).toEqual(expect.objectContaining(objectForAssertion));

        expect(DBObjectInvoice['connector']).toEqual(expect.objectContaining(objectForConnectorAssertion));

        expect(DBObjectInvoice['connector']['project']).toEqual(expect.objectContaining(objectForProjectAssertion));

        await wipeCustomerFields(blueTapeCustomerId);
    });

    test(`Cannot create invoice with empty customer id @negative`, async () => {
        const response = await createInvoiceR({customerId: ''});

        expect(response.code).toEqual(badRequestError);

        expect(response.response.status).toEqual(400);

        expect(response.response.data[0]).toEqual(expect.objectContaining({
            code: 'invalid_data',
            reason: "'Customer Id' must not be empty."
        }));
    });

    test(`Cannot create invoice with non-existent customer id @negative`, async () => {
        const nonExistentCustomer = BaseTest.getGUID();

        const response = await createInvoiceR({customerId: nonExistentCustomer});

        expect(response.code).toEqual(badRequestError);

        expect(response.response.status).toEqual(400);

        expect(response.response.data[0]).toEqual(expect.objectContaining({
            code: 'customer_does_not_exist',
            reason: await reasonStringParser(customerAlreadyExist, nonExistentCustomer, 'id'),
        }));
    });

    test(`Cannot create invoice with null customer id @negative`, async () => {
        const response = await createInvoiceR({customerId: null});

        expect(response.code).toEqual(badRequestError);

        expect(response.response.status).toEqual(400);

        expect(response.response.data[0]).toEqual(expect.objectContaining({
            code: 'InvalidModelState',
            reason: 'The CustomerId field is required.',
        }));
    });

    test(`Cannot create invoice with negative total amount value @negative`, async () => {
        const response = await createInvoiceR({
            customerId: customerId,
            totalAmount: -111
        });

        expect(response.code).toEqual(badRequestError);

        expect(response.response.status).toEqual(400);

        expect(response.response.data[0]).toEqual(expect.objectContaining({
            code: 'invalid_data',
            reason: "'Total Amount' must be greater than '0'.",
        }));
    });

    test(`Cannot create invoice with negative tax amount value @negative`, async () => {
        const response = await createInvoiceR({
            customerId: customerId,
            taxAmount: -111
        });

        expect(response.code).toEqual(badRequestError);

        expect(response.response.status).toEqual(400);

        expect(response.response.data[0]).toEqual(expect.objectContaining({
            code: 'invalid_data',
            reason: "'Tax Amount' must be greater than or equal to '0'.",
        }));
    });

    test(`Cannot create invoice with negative sub total amount value @negative`, async () => {
        const response = await createInvoiceR({
            customerId: customerId,
            subTotalAmount: -111
        });

        expect(response.code).toEqual(badRequestError);

        expect(response.response.status).toEqual(400);

        expect(response.response.data[0]).toEqual(expect.objectContaining({
            code: 'invalid_data',
            reason: "'Sub Total' must be greater than '0'.",
        }));
    });
})
