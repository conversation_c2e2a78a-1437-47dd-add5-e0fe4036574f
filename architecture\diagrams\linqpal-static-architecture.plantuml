@startuml Linqpal Static Architecture

title Linqpal Static Architecture

left to right direction

component app as "App\n(React Native)" #LightGoldenRodYellow
component core as "Core Services\n(NodeJs)" #LightBlue
component svc1 as "Integration Service 1\n(NodeJs)" #LightBlue
component svc2 as "Integration Service 2\n(NodeJs)" #LightBlue
component svc3 as "Integration Service 3\n(dotNetCore)" #LightBlue
component third1 as "3rd party" #LightGrey
component third2 as "3rd party" #LightGrey
component third3 as "3rd party" #LightGrey
database mongo as "NoSQL Database\n(MongoDb)" #DarkTurquoise

app -d-> core
core -l-> mongo
core -d-> svc1
core -d-> svc2
core -d-> svc3
svc1 -d-> third1
svc2 -d-> third2
svc3 -d-> third3

legend bottom
| Color | Meaning |
|<#LightGoldenRodYellow>| Frontend |
|<#LightBlue>| Backend |
|<#LightGrey>| 3rd party application component|
|<#DarkTurquoise>| Persistence Layer |
endlegend

@enduml