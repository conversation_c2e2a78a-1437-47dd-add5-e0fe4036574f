{"openapi": "3.0.1", "info": {"title": "BlueTape.Aion.API", "version": "1.0"}, "paths": {"/api/account/account-code/{accountCodeType}": {"get": {"tags": ["Account"], "parameters": [{"name": "PaymentRequestId", "in": "header", "required": true, "schema": {"type": "string"}}, {"name": "accountCodeType", "in": "path", "required": true, "schema": {"$ref": "#/components/schemas/AccountCodeType"}}, {"name": "X-PAYMENT-SUBSCRIPTION-TYPE", "in": "header", "description": "Select payment subscription type", "required": true, "schema": {"enum": ["SUBSCRIPTION1", "SUBSCRIPTION2", "SUBSCRIPTION3"], "type": "string"}}], "responses": {"400": {"description": "Bad Request", "content": {"text/plain": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/ErrorModel"}}}, "application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/ErrorModel"}}}, "text/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/ErrorModel"}}}}}, "401": {"description": "Unauthorized"}, "500": {"description": "Server Error", "content": {"text/plain": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/ErrorModel"}}}, "application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/ErrorModel"}}}, "text/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/ErrorModel"}}}}}, "200": {"description": "Success", "content": {"text/plain": {"schema": {"type": "string"}}, "application/json": {"schema": {"type": "string"}}, "text/json": {"schema": {"type": "string"}}}}}}}, "/api/account": {"get": {"tags": ["Account"], "responses": {"400": {"description": "Bad Request", "content": {"text/plain": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/ErrorModel"}}}, "application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/ErrorModel"}}}, "text/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/ErrorModel"}}}}}, "401": {"description": "Unauthorized"}, "500": {"description": "Server Error", "content": {"text/plain": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/ErrorModel"}}}, "application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/ErrorModel"}}}, "text/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/ErrorModel"}}}}}}}}, "/api/ach/pull": {"post": {"tags": ["Ach"], "parameters": [{"name": "PaymentRequestId", "in": "header", "required": true, "schema": {"type": "string"}}, {"name": "X-PAYMENT-SUBSCRIPTION-TYPE", "in": "header", "description": "Select payment subscription type", "required": true, "schema": {"enum": ["SUBSCRIPTION1", "SUBSCRIPTION2", "SUBSCRIPTION3"], "type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreateAchModel"}}, "text/json": {"schema": {"$ref": "#/components/schemas/CreateAchModel"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/CreateAchModel"}}}}, "responses": {"400": {"description": "Bad Request", "content": {"text/plain": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/ErrorModel"}}}, "application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/ErrorModel"}}}, "text/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/ErrorModel"}}}}}, "401": {"description": "Unauthorized"}, "500": {"description": "Server Error", "content": {"text/plain": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/ErrorModel"}}}, "application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/ErrorModel"}}}, "text/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/ErrorModel"}}}}}, "200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/CreateAchResponseModel"}}, "application/json": {"schema": {"$ref": "#/components/schemas/CreateAchResponseModel"}}, "text/json": {"schema": {"$ref": "#/components/schemas/CreateAchResponseModel"}}}}}}}, "/api/ach/push": {"post": {"tags": ["Ach"], "parameters": [{"name": "PaymentRequestId", "in": "header", "required": true, "schema": {"type": "string"}}, {"name": "X-PAYMENT-SUBSCRIPTION-TYPE", "in": "header", "description": "Select payment subscription type", "required": true, "schema": {"enum": ["SUBSCRIPTION1", "SUBSCRIPTION2", "SUBSCRIPTION3"], "type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreateAchModel"}}, "text/json": {"schema": {"$ref": "#/components/schemas/CreateAchModel"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/CreateAchModel"}}}}, "responses": {"400": {"description": "Bad Request", "content": {"text/plain": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/ErrorModel"}}}, "application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/ErrorModel"}}}, "text/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/ErrorModel"}}}}}, "401": {"description": "Unauthorized"}, "500": {"description": "Server Error", "content": {"text/plain": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/ErrorModel"}}}, "application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/ErrorModel"}}}, "text/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/ErrorModel"}}}}}, "200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/CreateAchResponseModel"}}, "application/json": {"schema": {"$ref": "#/components/schemas/CreateAchResponseModel"}}, "text/json": {"schema": {"$ref": "#/components/schemas/CreateAchResponseModel"}}}}}}}, "/api/ach/push/wire": {"post": {"tags": ["Ach"], "parameters": [{"name": "PaymentRequestId", "in": "header", "required": true, "schema": {"type": "string"}}, {"name": "X-PAYMENT-SUBSCRIPTION-TYPE", "in": "header", "description": "Select payment subscription type", "required": true, "schema": {"enum": ["SUBSCRIPTION1", "SUBSCRIPTION2", "SUBSCRIPTION3"], "type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreateAchModel"}}, "text/json": {"schema": {"$ref": "#/components/schemas/CreateAchModel"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/CreateAchModel"}}}}, "responses": {"400": {"description": "Bad Request", "content": {"text/plain": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/ErrorModel"}}}, "application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/ErrorModel"}}}, "text/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/ErrorModel"}}}}}, "401": {"description": "Unauthorized"}, "500": {"description": "Server Error", "content": {"text/plain": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/ErrorModel"}}}, "application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/ErrorModel"}}}, "text/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/ErrorModel"}}}}}, "200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/CreateAchResponseModel"}}, "application/json": {"schema": {"$ref": "#/components/schemas/CreateAchResponseModel"}}, "text/json": {"schema": {"$ref": "#/components/schemas/CreateAchResponseModel"}}}}}}}, "/api/ach/push/instant": {"post": {"tags": ["Ach"], "parameters": [{"name": "PaymentRequestId", "in": "header", "required": true, "schema": {"type": "string"}}, {"name": "X-PAYMENT-SUBSCRIPTION-TYPE", "in": "header", "description": "Select payment subscription type", "required": true, "schema": {"enum": ["SUBSCRIPTION1", "SUBSCRIPTION2", "SUBSCRIPTION3"], "type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreateAchModel"}}, "text/json": {"schema": {"$ref": "#/components/schemas/CreateAchModel"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/CreateAchModel"}}}}, "responses": {"400": {"description": "Bad Request", "content": {"text/plain": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/ErrorModel"}}}, "application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/ErrorModel"}}}, "text/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/ErrorModel"}}}}}, "401": {"description": "Unauthorized"}, "500": {"description": "Server Error", "content": {"text/plain": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/ErrorModel"}}}, "application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/ErrorModel"}}}, "text/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/ErrorModel"}}}}}, "200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/CreateAchResponseModel"}}, "application/json": {"schema": {"$ref": "#/components/schemas/CreateAchResponseModel"}}, "text/json": {"schema": {"$ref": "#/components/schemas/CreateAchResponseModel"}}}}}}}, "/report/run": {"post": {"tags": ["Ach"], "responses": {"400": {"description": "Bad Request", "content": {"text/plain": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/ErrorModel"}}}, "application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/ErrorModel"}}}, "text/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/ErrorModel"}}}}}, "401": {"description": "Unauthorized"}, "500": {"description": "Server Error", "content": {"text/plain": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/ErrorModel"}}}, "application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/ErrorModel"}}}, "text/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/ErrorModel"}}}}}, "200": {"description": "Success"}}}}, "/dailyAchProcessing/run": {"post": {"tags": ["Ach"], "parameters": [{"name": "amount", "in": "query", "schema": {"type": "number", "format": "double"}}, {"name": "transactionsLimitPerCall", "in": "query", "schema": {"type": "integer", "format": "int32"}}], "responses": {"400": {"description": "Bad Request", "content": {"text/plain": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/ErrorModel"}}}, "application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/ErrorModel"}}}, "text/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/ErrorModel"}}}}}, "401": {"description": "Unauthorized"}, "500": {"description": "Server Error", "content": {"text/plain": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/ErrorModel"}}}, "application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/ErrorModel"}}}, "text/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/ErrorModel"}}}}}, "200": {"description": "Success"}}}}, "/ach-pull/account-code/{accountCodeType}": {"get": {"tags": ["AionLimit"], "parameters": [{"name": "accountCodeType", "in": "path", "required": true, "schema": {"$ref": "#/components/schemas/AccountCodeType"}}, {"name": "amount", "in": "query", "schema": {"type": "number", "format": "double", "default": 0}}, {"name": "X-PAYMENT-SUBSCRIPTION-TYPE", "in": "header", "description": "Select payment subscription type", "required": true, "schema": {"enum": ["SUBSCRIPTION1", "SUBSCRIPTION2", "SUBSCRIPTION3"], "type": "string"}}], "responses": {"400": {"description": "Bad Request", "content": {"text/plain": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/ErrorModel"}}}, "application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/ErrorModel"}}}, "text/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/ErrorModel"}}}}}, "401": {"description": "Unauthorized"}, "500": {"description": "Server Error", "content": {"text/plain": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/ErrorModel"}}}, "application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/ErrorModel"}}}, "text/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/ErrorModel"}}}}}, "200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/AionTransferLimitModel"}}, "application/json": {"schema": {"$ref": "#/components/schemas/AionTransferLimitModel"}}, "text/json": {"schema": {"$ref": "#/components/schemas/AionTransferLimitModel"}}}}}}}, "/ach-push/account-code/{accountCodeType}": {"get": {"tags": ["AionLimit"], "parameters": [{"name": "accountCodeType", "in": "path", "required": true, "schema": {"$ref": "#/components/schemas/AccountCodeType"}}, {"name": "amount", "in": "query", "schema": {"type": "number", "format": "double", "default": 0}}, {"name": "X-PAYMENT-SUBSCRIPTION-TYPE", "in": "header", "description": "Select payment subscription type", "required": true, "schema": {"enum": ["SUBSCRIPTION1", "SUBSCRIPTION2", "SUBSCRIPTION3"], "type": "string"}}], "responses": {"400": {"description": "Bad Request", "content": {"text/plain": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/ErrorModel"}}}, "application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/ErrorModel"}}}, "text/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/ErrorModel"}}}}}, "401": {"description": "Unauthorized"}, "500": {"description": "Server Error", "content": {"text/plain": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/ErrorModel"}}}, "application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/ErrorModel"}}}, "text/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/ErrorModel"}}}}}, "200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/AionTransferLimitModel"}}, "application/json": {"schema": {"$ref": "#/components/schemas/AionTransferLimitModel"}}, "text/json": {"schema": {"$ref": "#/components/schemas/AionTransferLimitModel"}}}}}}}, "/wire-push/account-code/{accountCodeType}": {"get": {"tags": ["AionLimit"], "parameters": [{"name": "accountCodeType", "in": "path", "required": true, "schema": {"$ref": "#/components/schemas/AccountCodeType"}}, {"name": "amount", "in": "query", "schema": {"type": "number", "format": "double", "default": 0}}, {"name": "X-PAYMENT-SUBSCRIPTION-TYPE", "in": "header", "description": "Select payment subscription type", "required": true, "schema": {"enum": ["SUBSCRIPTION1", "SUBSCRIPTION2", "SUBSCRIPTION3"], "type": "string"}}], "responses": {"400": {"description": "Bad Request", "content": {"text/plain": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/ErrorModel"}}}, "application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/ErrorModel"}}}, "text/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/ErrorModel"}}}}}, "401": {"description": "Unauthorized"}, "500": {"description": "Server Error", "content": {"text/plain": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/ErrorModel"}}}, "application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/ErrorModel"}}}, "text/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/ErrorModel"}}}}}, "200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/AionTransferLimitModel"}}, "application/json": {"schema": {"$ref": "#/components/schemas/AionTransferLimitModel"}}, "text/json": {"schema": {"$ref": "#/components/schemas/AionTransferLimitModel"}}}}}}}, "/instant-push/account-code/{accountCodeType}": {"get": {"tags": ["AionLimit"], "parameters": [{"name": "accountCodeType", "in": "path", "required": true, "schema": {"$ref": "#/components/schemas/AccountCodeType"}}, {"name": "amount", "in": "query", "schema": {"type": "number", "format": "double", "default": 0}}, {"name": "X-PAYMENT-SUBSCRIPTION-TYPE", "in": "header", "description": "Select payment subscription type", "required": true, "schema": {"enum": ["SUBSCRIPTION1", "SUBSCRIPTION2", "SUBSCRIPTION3"], "type": "string"}}], "responses": {"400": {"description": "Bad Request", "content": {"text/plain": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/ErrorModel"}}}, "application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/ErrorModel"}}}, "text/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/ErrorModel"}}}}}, "401": {"description": "Unauthorized"}, "500": {"description": "Server Error", "content": {"text/plain": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/ErrorModel"}}}, "application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/ErrorModel"}}}, "text/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/ErrorModel"}}}}}, "200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/AionTransferLimitModel"}}, "application/json": {"schema": {"$ref": "#/components/schemas/AionTransferLimitModel"}}, "text/json": {"schema": {"$ref": "#/components/schemas/AionTransferLimitModel"}}}}}}}, "/errorCodes": {"get": {"tags": ["Helper"], "responses": {"400": {"description": "Bad Request", "content": {"text/plain": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/ErrorModel"}}}, "application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/ErrorModel"}}}, "text/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/ErrorModel"}}}}}, "401": {"description": "Unauthorized"}, "500": {"description": "Server Error", "content": {"text/plain": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/ErrorModel"}}}, "application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/ErrorModel"}}}, "text/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/ErrorModel"}}}}}}}}, "/git": {"get": {"tags": ["Helper"], "responses": {"400": {"description": "Bad Request", "content": {"text/plain": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/ErrorModel"}}}, "application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/ErrorModel"}}}, "text/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/ErrorModel"}}}}}, "401": {"description": "Unauthorized"}, "500": {"description": "Server Error", "content": {"text/plain": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/ErrorModel"}}}, "application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/ErrorModel"}}}, "text/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/ErrorModel"}}}}}}}}, "/helper/azure-storage/transactions/get-all": {"get": {"tags": ["Helper"], "responses": {"400": {"description": "Bad Request", "content": {"text/plain": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/ErrorModel"}}}, "application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/ErrorModel"}}}, "text/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/ErrorModel"}}}}}, "401": {"description": "Unauthorized"}, "500": {"description": "Server Error", "content": {"text/plain": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/ErrorModel"}}}, "application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/ErrorModel"}}}, "text/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/ErrorModel"}}}}}}}}, "/health": {"get": {"tags": ["Helper"], "responses": {"400": {"description": "Bad Request", "content": {"text/plain": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/ErrorModel"}}}, "application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/ErrorModel"}}}, "text/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/ErrorModel"}}}}}, "401": {"description": "Unauthorized"}, "500": {"description": "Server Error", "content": {"text/plain": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/ErrorModel"}}}, "application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/ErrorModel"}}}, "text/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/ErrorModel"}}}}}}}}, "/api/internal": {"post": {"tags": ["Internal"], "parameters": [{"name": "PaymentRequestId", "in": "header", "required": true, "schema": {"type": "string"}}, {"name": "X-PAYMENT-SUBSCRIPTION-TYPE", "in": "header", "description": "Select payment subscription type", "required": true, "schema": {"enum": ["SUBSCRIPTION1", "SUBSCRIPTION2", "SUBSCRIPTION3"], "type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreateInternalModel"}}, "text/json": {"schema": {"$ref": "#/components/schemas/CreateInternalModel"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/CreateInternalModel"}}}}, "responses": {"400": {"description": "Bad Request", "content": {"text/plain": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/ErrorModel"}}}, "application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/ErrorModel"}}}, "text/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/ErrorModel"}}}}}, "401": {"description": "Unauthorized"}, "500": {"description": "Server Error", "content": {"text/plain": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/ErrorModel"}}}, "application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/ErrorModel"}}}, "text/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/ErrorModel"}}}}}, "200": {"description": "Success", "content": {"text/plain": {"schema": {}}, "application/json": {"schema": {}}, "text/json": {"schema": {}}}}}}}, "/api/transactions": {"get": {"tags": ["Transaction"], "parameters": [{"name": "AccountCodeType", "in": "query", "schema": {"$ref": "#/components/schemas/AccountCodeType"}}, {"name": "PageSize", "in": "query", "schema": {"type": "integer", "format": "int32"}}, {"name": "PageNumber", "in": "query", "schema": {"type": "integer", "format": "int32"}}], "responses": {"400": {"description": "Bad Request", "content": {"text/plain": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/ErrorModel"}}}, "application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/ErrorModel"}}}, "text/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/ErrorModel"}}}}}, "401": {"description": "Unauthorized"}, "500": {"description": "Server Error", "content": {"text/plain": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/ErrorModel"}}}, "application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/ErrorModel"}}}, "text/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/ErrorModel"}}}}}, "200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/TransactionListObjPaginatedResponse"}}, "application/json": {"schema": {"$ref": "#/components/schemas/TransactionListObjPaginatedResponse"}}, "text/json": {"schema": {"$ref": "#/components/schemas/TransactionListObjPaginatedResponse"}}}}}}}, "/api/transactions/internal": {"post": {"tags": ["Transaction"], "parameters": [{"name": "PaymentRequestId", "in": "header", "required": true, "schema": {"type": "string"}}, {"name": "X-PAYMENT-SUBSCRIPTION-TYPE", "in": "header", "description": "Select payment subscription type", "required": true, "schema": {"enum": ["SUBSCRIPTION1", "SUBSCRIPTION2", "SUBSCRIPTION3"], "type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreateInternalModel"}}, "text/json": {"schema": {"$ref": "#/components/schemas/CreateInternalModel"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/CreateInternalModel"}}}}, "responses": {"400": {"description": "Bad Request", "content": {"text/plain": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/ErrorModel"}}}, "application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/ErrorModel"}}}, "text/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/ErrorModel"}}}}}, "401": {"description": "Unauthorized"}, "500": {"description": "Server Error", "content": {"text/plain": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/ErrorModel"}}}, "application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/ErrorModel"}}}, "text/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/ErrorModel"}}}}}, "200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/BlueTapeTransactionResponseModel"}}, "application/json": {"schema": {"$ref": "#/components/schemas/BlueTapeTransactionResponseModel"}}, "text/json": {"schema": {"$ref": "#/components/schemas/BlueTapeTransactionResponseModel"}}}}}}}, "/api/transactions/external/transactionType/{transactionType}/paymentMethodType/{paymentMethodType}": {"post": {"tags": ["Transaction"], "parameters": [{"name": "PaymentRequestId", "in": "header", "required": true, "schema": {"type": "string"}}, {"name": "transactionType", "in": "path", "required": true, "schema": {"$ref": "#/components/schemas/TransactionType"}}, {"name": "paymentMethodType", "in": "path", "required": true, "schema": {"$ref": "#/components/schemas/AionPaymentMethodType"}}, {"name": "X-PAYMENT-SUBSCRIPTION-TYPE", "in": "header", "description": "Select payment subscription type", "required": true, "schema": {"enum": ["SUBSCRIPTION1", "SUBSCRIPTION2", "SUBSCRIPTION3"], "type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreateAchModel"}}, "text/json": {"schema": {"$ref": "#/components/schemas/CreateAchModel"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/CreateAchModel"}}}}, "responses": {"400": {"description": "Bad Request", "content": {"text/plain": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/ErrorModel"}}}, "application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/ErrorModel"}}}, "text/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/ErrorModel"}}}}}, "401": {"description": "Unauthorized"}, "500": {"description": "Server Error", "content": {"text/plain": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/ErrorModel"}}}, "application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/ErrorModel"}}}, "text/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/ErrorModel"}}}}}, "200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/BlueTapeTransactionResponseModel"}}, "application/json": {"schema": {"$ref": "#/components/schemas/BlueTapeTransactionResponseModel"}}, "text/json": {"schema": {"$ref": "#/components/schemas/BlueTapeTransactionResponseModel"}}}}}}}}, "components": {"schemas": {"AccountCodeType": {"enum": ["GL", "FBO", "SERVICE", "REVENUE", "OP", "FUNDING", "TABAPAY", "COLLECTION", "COLLECTIONREPAYMENT", "DISBURSEMENT", "CARDCOLLECTION", "LOCKBOXCOLLECTION", "DACACOLLECTION", "SPV_COLLECTION_ARCADIA", "SPV_COLLECTION_RAISTONE", "SPV_COLLECTION_AION", "SPV_FUNDING_ARCADIA", "SPV_FUNDING_RAISTONE", "SPV_FUNDING_AION"], "type": "string"}, "AchObjResponseModel": {"type": "object", "properties": {"id": {"type": "string", "nullable": true}, "accountId": {"type": "string", "nullable": true}, "amount": {"type": "string", "nullable": true}, "feeAmount": {"type": "number", "format": "double"}, "counterpartyId": {"type": "string", "nullable": true}, "description": {"type": "string", "nullable": true}, "direction": {"type": "string", "nullable": true}, "error": {"type": "string", "nullable": true}, "secCode": {"type": "string", "nullable": true}, "status": {"type": "string", "nullable": true}, "counterpartyName": {"type": "string", "nullable": true}, "counterpartyType": {"type": "string", "nullable": true}, "email": {"type": "boolean"}, "userNote": {"type": "string", "nullable": true}, "sendEmail": {"type": "boolean"}, "effectiveDate": {"type": "string", "nullable": true}, "transferMethodId": {"type": "string", "nullable": true}, "initiatedBy": {"type": "string", "nullable": true}, "contextIdentifier": {"type": "string", "nullable": true}, "addenda": {"type": "array", "items": {"type": "string"}, "nullable": true}, "createdAt": {"type": "string", "format": "date-time"}, "updatedAt": {"type": "string", "format": "date-time"}, "originator": {"$ref": "#/components/schemas/OriginatorResponseModel"}, "receiver": {"$ref": "#/components/schemas/ReceiverResponseModel"}, "referenceId": {"type": "string", "nullable": true}, "paymentType": {"type": "string", "nullable": true}, "postingCode": {"type": "string", "nullable": true}, "posting": {"type": "string", "nullable": true}, "reasonCode": {"type": "string", "nullable": true}, "reasonData": {"type": "string", "nullable": true}, "traceNumber": {"type": "string", "nullable": true}, "transactionType": {"type": "string", "nullable": true}, "serviceType": {"type": "string", "nullable": true}, "wasReturned": {"type": "boolean"}, "wasCorrected": {"type": "boolean"}, "canceledAt": {"type": "string", "format": "date-time"}, "processedAt": {"type": "string", "format": "date-time"}, "completedAt": {"type": "string", "format": "date-time"}, "postedAt": {"type": "string", "format": "date-time"}, "rejectedAt": {"type": "string", "format": "date-time"}, "original": {"$ref": "#/components/schemas/OriginalResponseModel"}, "previous": {"$ref": "#/components/schemas/PreviousResponseModel"}, "accountNumber": {"type": "string", "nullable": true}}, "additionalProperties": false}, "AionPaymentMethodType": {"enum": ["DEFAULT", "ACH", "WIRE", "INSTANT", "Internal"], "type": "string"}, "AionTransferLimitModel": {"type": "object", "properties": {"limit": {"type": "number", "format": "double", "nullable": true}}, "additionalProperties": false}, "BaseInternalAccountDetailsModel": {"type": "object", "properties": {"accountCode": {"$ref": "#/components/schemas/AccountCodeType"}, "name": {"type": "string", "nullable": true}, "description": {"type": "string", "nullable": true}}, "additionalProperties": false}, "BlueTapeTransactionResponseModel": {"type": "object", "properties": {"aionReferenceId": {"type": "string", "nullable": true}, "aionFee": {"type": "number", "format": "double"}, "aionResponse": {"type": "string", "nullable": true}}, "additionalProperties": false}, "CreateAchModel": {"type": "object", "properties": {"amount": {"type": "number", "format": "double"}, "description": {"type": "string", "nullable": true}, "transactionNumber": {"type": "string", "nullable": true}, "transactionId": {"type": "string", "nullable": true}, "addenda": {"type": "array", "items": {"type": "string"}, "nullable": true}, "receiver": {"$ref": "#/components/schemas/ReceiverModel"}, "originatorAccountCode": {"$ref": "#/components/schemas/AccountCodeType"}, "sameDayAch": {"type": "boolean"}}, "additionalProperties": false}, "CreateAchResponseModel": {"type": "object", "properties": {"responseMessage": {"type": "string", "nullable": true}, "result": {"type": "boolean"}, "achObj": {"$ref": "#/components/schemas/AchObjResponseModel"}}, "additionalProperties": false}, "CreateInternalModel": {"type": "object", "properties": {"originator": {"$ref": "#/components/schemas/BaseInternalAccountDetailsModel"}, "receiver": {"$ref": "#/components/schemas/BaseInternalAccountDetailsModel"}, "amount": {"type": "number", "format": "double"}, "description": {"type": "string", "nullable": true}, "transactionNumber": {"type": "string", "nullable": true}, "transactionId": {"type": "string", "nullable": true}}, "additionalProperties": false}, "ErrorModel": {"type": "object", "properties": {"errorType": {"$ref": "#/components/schemas/ErrorType"}, "code": {"type": "string", "nullable": true}, "reason": {"type": "string", "nullable": true}, "errorData": {"nullable": true}}, "additionalProperties": false}, "ErrorType": {"enum": ["<PERSON><PERSON><PERSON>", "ValidationError", "BusinessLogicError", "InternalError", "AionError"], "type": "string"}, "OriginalResponseModel": {"type": "object", "properties": {"paymentId": {"type": "string", "nullable": true}}, "additionalProperties": false}, "OriginatorResponseModel": {"type": "object", "properties": {"accountNumber": {"type": "string", "nullable": true}, "routingNumber": {"type": "string", "nullable": true}, "name": {"type": "string", "nullable": true}, "accountType": {"type": "string", "nullable": true}, "identification": {"type": "string", "nullable": true}}, "additionalProperties": false}, "PreviousResponseModel": {"type": "object", "properties": {"paymentId": {"type": "string", "nullable": true}}, "additionalProperties": false}, "ReceiverModel": {"type": "object", "properties": {"companyId": {"type": "string", "nullable": true}, "bankAccountId": {"type": "string", "nullable": true}}, "additionalProperties": false}, "ReceiverResponseModel": {"type": "object", "properties": {"accountNumber": {"type": "string", "nullable": true}, "routingNumber": {"type": "string", "nullable": true}, "name": {"type": "string", "nullable": true}, "accountType": {"type": "string", "nullable": true}, "identification": {"type": "string", "nullable": true}}, "additionalProperties": false}, "TransactionListObj": {"type": "object", "properties": {"id": {"type": "string", "nullable": true}, "accountId": {"type": "string", "nullable": true}, "achId": {"type": "string", "nullable": true}, "amount": {"type": "number", "format": "double", "nullable": true}, "amountStr": {"type": "string", "nullable": true}, "balance": {"type": "number", "format": "double", "nullable": true}, "balanceStr": {"type": "string", "nullable": true}, "txnDate": {"type": "string", "format": "date-time", "nullable": true}, "wireId": {"type": "string", "nullable": true}, "displayDescription": {"type": "string", "nullable": true}, "transactionId": {"type": "string", "format": "uuid"}, "traceNumber": {"type": "string", "nullable": true}, "transactionType": {"type": "string", "nullable": true}, "providerStatus": {"type": "string", "nullable": true}, "accountNumber": {"type": "string", "nullable": true}, "transactionCode": {"type": "string", "nullable": true}, "rail": {"type": "string", "nullable": true}, "flags": {"type": "array", "items": {"type": "string"}, "nullable": true}, "schedule": {"type": "array", "items": {"type": "integer", "format": "int32"}, "nullable": true}, "clearing": {"type": "boolean", "nullable": true}}, "additionalProperties": false}, "TransactionListObjPaginatedResponse": {"type": "object", "properties": {"limit": {"type": "integer", "format": "int32", "nullable": true}, "offset": {"type": "integer", "format": "int32", "nullable": true}, "count": {"type": "integer", "format": "int32", "nullable": true}, "total": {"type": "integer", "format": "int32", "nullable": true}, "result": {"type": "array", "items": {"$ref": "#/components/schemas/TransactionListObj"}, "nullable": true}}, "additionalProperties": false}, "TransactionType": {"enum": ["<PERSON><PERSON>", "<PERSON><PERSON>"], "type": "string"}}, "securitySchemes": {"ApiKeyAuthScheme": {"type": "<PERSON><PERSON><PERSON><PERSON>", "description": "API key used in the Authorization header.", "name": "X-API-KEY", "in": "header"}}}, "security": [{"ApiKeyAuthScheme": []}]}