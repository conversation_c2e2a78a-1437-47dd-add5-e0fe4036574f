@startuml

title Document Versioning Service\n(process diagram)

actor "User" as cl #LightGreen
participant "Linqpal" as lq #LightGray
database "MongoDb" as mongo #LightGray
participant "AWS\nS3" as s3 #Orange
participant "Document\nVersioning" as dv #SkyBlue
database "RDS" as rds #Orange

autonumber

== BNPL agreement ==
cl -> lq : User approves BNPL
lq -> dv : Gets actual version of template
dv -> rds : Read
rds --> dv
dv --> lq
lq -> s3 : Downloads actual template file
s3 --> lq
lq -> mongo : Read application\ndetails
mongo --> lq
lq -> lq : Fills information
lq -> dv : Gets new document version filename
dv --> lq
lq -> s3 : Uploads file
lq -> dv : Adds application document
dv -> rds: Write
lq -> cl : Shows filled\ndocument

== View Agreement ==

cl -> lq : User views\nagreement
lq -> dv : Gets agreed document details
dv -> rds : Read
rds --> dv
dv --> lq
lq -> s3 : Downloads file
s3 --> lq
lq --> cl : Shows document

@enduml