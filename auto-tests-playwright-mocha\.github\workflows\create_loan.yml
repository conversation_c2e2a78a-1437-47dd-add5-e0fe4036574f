name: Create Loan Workflow

on:
  workflow_call:
    inputs:
      supplierEmail:
        description: 'Supplier email'
        required: true
        type: string
      supplierPassword:
        description: 'Supplier password'
        required: true
        type: string
      customerEmail:
        description: 'Customer email'
        required: true
        type: string
      customerPassword:
        description: 'Customer password'
        required: true
        type: string
      invoiceQuantity:
        description: 'Number of invoices to create'
        required: true
        default: '1'
        type: string
      environment:
        description: 'Environment to run tests against'
        required: true
        default: 'beta'
        type: string      
      loanType:
        description: 'Type of loan to test run'
        required: false
        default: 'Regular'
        type: string
      invoiceAmount:
        description: 'Amount for the invoice (optional)'
        required: false
        type: string
    outputs:
      loan_ids:
        description: "IDs of created loans"
        value: ${{ jobs.test.outputs.loan_ids }}
      invoice_ids:
        description: "IDs of created invoices"
        value: ${{ jobs.test.outputs.invoice_ids }}
      status:
        description: "Workflow execution status"
        value: ${{ jobs.test.outputs.status }}
  workflow_dispatch:
    inputs:
      supplierEmail:
        description: 'Supplier email'
        required: true
        type: string
      supplierPassword:
        description: 'Supplier password'
        required: true
        type: string
      customerEmail:
        description: 'Customer email'
        required: true
        type: string
      customerPassword:
        description: 'Customer password'
        required: true
        type: string
      invoiceQuantity:
        description: 'Number of invoices to create'
        required: true
        default: '1'
        type: number
      environment:
        description: 'Environment to run tests against'
        required: true
        default: 'beta'
        type: choice
        options:
          - beta
          - qa
          - dev
      loanType:
        description: 'Type of loan to test run'
        required: true
        default: 'Regular'
        type: choice
        options:
          - Regular
          - DirectTerms
      invoiceAmount:
        description: 'Amount for the invoice (optional)'
        required: false
        type: string

jobs:
  test:
    runs-on: ubuntu-latest
    container:
      image: mcr.microsoft.com/playwright:v1.44.1-jammy
    
    # Define job outputs that will be available at the workflow level
    outputs:
      loan_ids: ${{ steps.extract_results.outputs.loan_ids }}
      invoice_ids: ${{ steps.extract_results.outputs.invoice_ids }}
      status: ${{ steps.extract_results.outputs.status }}
    
    env:
      # Environment configuration
      TEST_ENVIRONMENT: ${{ github.event.inputs.environment }}
      TEST_TIMEOUT: ${{ github.event.inputs.test_timeout }}
      PAYMENT_DELAY_MS: ${{ github.event.inputs.payment_delay_ms }}
      TestGroupId: ${{ github.run_id }}-${{ github.run_number }}
      
      # URLs for different environments - can be set per environment
      CI_ENVIRONMENT_URL: ${{ secrets.CI_ENVIRONMENT_URL }}
      LMS_BASE_URL: ${{ secrets.LMS_BASE_URL }}
      COMPANY_BASE_URL: ${{ secrets.COMPANY_BASE_URL }}
      GENERIC_API_BASE_URL: ${{ secrets.GENERIC_API_BASE_URL }}
      PAY_NOW_BASE_URL: ${{ secrets.PAY_NOW_BASE_URL }}
      
      # Supplier credentials
      TEST_SUPPLIER_EMAIL: ${{ secrets.TEST_SUPPLIER_EMAIL }}
      TEST_SUPPLIER_PASSWORD: ${{ secrets.TEST_SUPPLIER_PASSWORD }}
      SUPPLIER_EMAIL: ${{ secrets.SUPPLIER_EMAIL }}
      SUPPLIER_PASSPORD: ${{ secrets.PASSPORD }}
      
      # Customer credentials
      TEST_CUSTOMER_EMAIL: ${{ secrets.TEST_CUSTOMER_EMAIL }}
      TEST_CUSTOMER_PASSWORD: ${{ secrets.TEST_CUSTOMER_PASSWORD }}
      TEST_CUSTOMER_COMPANYNAME: ${{ secrets.TEST_CUSTOMER_COMPANYNAME }}
      USER_CUSTOMEREMAIL: ${{ secrets.USER_CUSTOMEREMAIL }}
      USER_CUSTOMERPASSWORD: ${{ secrets.USER_CUSTOMERPASSWORD }}
      
      # Other required environment variables
      MONGODB_URI: ${{ secrets.MONGODB_URI }}
      X_BLUETAPE_KEY: ${{ secrets.X_BLUETAPE_KEY }}
      X_API_KEY: ${{ secrets.X_API_KEY }}
      COMPANY_ID: ${{ secrets.COMPANY_ID }}
      USER_KEY: ${{ secrets.USER_KEY }}
        # Add any other environment variables needed for your tests
      CREATE_LOAN_INPUTS: >
        {
          "supplierEmail": "${{ inputs.supplierEmail }}",
          "supplierPassword": "${{ inputs.supplierPassword }}",
          "customerEmail": "${{ inputs.customerEmail }}",
          "customerPassword": "${{ inputs.customerPassword }}",
          "invoiceQuantity": "${{ inputs.invoiceQuantity }}",
          "environment": "${{ inputs.environment }}",
          "loanType": "${{ inputs.loanType }}",
          "invoiceAmount": "${{ inputs.invoiceAmount }}"
        }

    steps:
      - uses: actions/checkout@v4
      
      - name: Set up Node.js
        uses: actions/setup-node@v4
        with:
          node-version: 18
      
      - name: Install dependencies
        run: npm ci --verbose
      
      - name: Run Loan Flow tests
        id: run_tests
        run: npx playwright test --grep "@CreateLoanFlow" --reporter=./sqs-reporter.js
      
      - name: Extract test results
        id: extract_results
        run: |
          echo "Extracting results from test execution..."
          # Extract data from the metadata file generated by our reporter
          if [ -f "test-metadata.json" ]; then
            echo "Processing test metadata file..."
            
            # Extract loan IDs as a comma-separated list
            LOAN_IDS=$(jq -r '.metadata.loanIds | join(",")' test-metadata.json || echo "")
            echo "loan_ids=${LOAN_IDS}" >> $GITHUB_OUTPUT
            
            # Extract invoice IDs as a comma-separated list
            INVOICE_IDS=$(jq -r '.metadata.invoiceIds | join(",")' test-metadata.json || echo "")
            echo "invoice_ids=${INVOICE_IDS}" >> $GITHUB_OUTPUT
            
            # Set execution status
            if jq -e '.status == "passed"' test-metadata.json > /dev/null; then
              echo "status=success" >> $GITHUB_OUTPUT
            else
              echo "status=failed" >> $GITHUB_OUTPUT
            fi
              # Create a summary file with test results
            echo "### Test Execution Summary" >> $GITHUB_STEP_SUMMARY            echo "✅ Environment: ${{ inputs.environment }}" >> $GITHUB_STEP_SUMMARY
            echo "✅ Test Type: ${{ inputs.testType }}" >> $GITHUB_STEP_SUMMARY
            echo "✅ Loan Type: ${{ inputs.loanType }}" >> $GITHUB_STEP_SUMMARY
            echo "✅ Invoice Quantity: ${{ inputs.invoiceQuantity }}" >> $GITHUB_STEP_SUMMARY
            if [ -n "${{ inputs.invoiceAmount }}" ]; then
              echo "✅ Invoice Amount: ${{ inputs.invoiceAmount }}" >> $GITHUB_STEP_SUMMARY
            fi
            echo "✅ Generated Loan IDs: ${LOAN_IDS}" >> $GITHUB_STEP_SUMMARY
            echo "✅ Generated Invoice IDs: ${INVOICE_IDS}" >> $GITHUB_STEP_SUMMARY
            
            # Add statistics
            TOTAL_TESTS=$(jq '.metadata.totalTests' test-metadata.json)
            PASSED_TESTS=$(jq '.metadata.passedTests' test-metadata.json)
            FAILED_TESTS=$(jq '.metadata.failedTests' test-metadata.json)
            echo "📊 Test Statistics: ${PASSED_TESTS}/${TOTAL_TESTS} passed, ${FAILED_TESTS} failed" >> $GITHUB_STEP_SUMMARY
          else
            echo "No test metadata file found"
            echo "status=no_results" >> $GITHUB_OUTPUT
            echo "loan_ids=" >> $GITHUB_OUTPUT
            echo "invoice_ids=" >> $GITHUB_OUTPUT
          fi
      
      - name: Display results in console
        run: |          
          echo "============================================================="
          echo "⭐ WORKFLOW RESULTS ⭐"
          echo "============================================================="          echo "📋 TEST TYPE: ${{ inputs.testType }}"
          echo "📋 LOAN TYPE: ${{ inputs.loanType }}"
          if [ -n "${{ inputs.invoiceAmount }}" ]; then
            echo "📋 INVOICE AMOUNT: ${{ inputs.invoiceAmount }}"
          fi
          echo "📋 LOAN IDs: ${{ steps.extract_results.outputs.loan_ids }}"
          echo "📋 INVOICE IDs: ${{ steps.extract_results.outputs.invoice_ids }}"
          echo "📋 STATUS: ${{ steps.extract_results.outputs.status }}"
          echo "============================================================="
          
          # If we have loan or invoice IDs, print them in a structured JSON format
          if [ -n "${{ steps.extract_results.outputs.loan_ids }}" ] || [ -n "${{ steps.extract_results.outputs.invoice_ids }}" ]; then
            echo ""            echo "Results in JSON format (for easy copying):"
            echo "{" 
            echo "  \"loan_ids\": \"${{ steps.extract_results.outputs.loan_ids }}\"," 
            echo "  \"invoice_ids\": \"${{ steps.extract_results.outputs.invoice_ids }}\"," 
            echo "  \"status\": \"${{ steps.extract_results.outputs.status }}\"" 
            if [ -n "${{ inputs.invoiceAmount }}" ]; then
              echo "  ,\"invoice_amount\": \"${{ inputs.invoiceAmount }}\"" 
            fi
            echo "}"
            echo ""
          fi
          
          # Cat the metadata file for additional details
          if [ -f "test-metadata.json" ]; then
            echo "Full test metadata:"
            cat test-metadata.json
          fi
      
      - name: Upload test results
        if: always()
        uses: actions/upload-artifact@v4
        with:
          name: playwright-report
          path: playwright-report/
          retention-days: 30
      
      - name: Upload results metadata
        if: always()
        uses: actions/upload-artifact@v4
        with:
          name: test-metadata
          path: test-metadata.json
          retention-days: 30