import {expect} from '@playwright/test';
import {BaseTest, test} from '../../test-utils';
import {sendLMSRequest, postBasisRequest} from '../../../api/common/lms-send-request';
import {HttpStatusCode} from 'axios';

const constants = JSON.parse(JSON.stringify(require('../../../constants/LMStestData.json')));

test.describe(`Basis Point API Tests @LMS @API`, async () => {
    let basisId: string;
    const basisPointValue: number = constants.basisPoint.value;
    const updatedBasisPointValue: number = constants.basisPoint.updatedValue;
    const currentDate: string = BaseTest.getCurrentDate();
    const dateInTheFuture: string = BaseTest.getReceivablesDates(30);

    test.beforeEach(async () => {
        const response = await postBasisRequest(basisPointValue, currentDate);

        basisId = response.data.id;
    });

    test.afterEach(async () => {
        await sendLMSRequest('delete', `BasisPoint/${basisId}`);
    });

    test('Get list of basis points', async () => {
        const response = await sendLMSRequest('get', `BasisPoint`);

        const basisElementInArray = response.data.find(el => el.id === basisId);

        expect(response.status, `Status code ${HttpStatusCode.Ok}`)
            .toEqual(HttpStatusCode.Ok);

        expect(response.data)
            .toEqual(expect.any(Array));

        expect(basisElementInArray).toBeTruthy();

        expect(basisElementInArray.basisPointValue)
            .toEqual(basisPointValue);
    });

    test('Get basis points by ID ', async () => {
        const response = await sendLMSRequest('get', `BasisPoint?Id=${basisId}`);

        expect(response.status, `Status code ${HttpStatusCode.Ok}`)
            .toEqual(HttpStatusCode.Ok);

        expect(response.data)
            .toEqual(expect.any(Object));
    });

    test('Get basis points by Date ', async () => {
        const response = await sendLMSRequest('get', `BasisPoint?OnDate=${currentDate}`);

        expect(response.status, `Status code ${HttpStatusCode.Ok}`)
            .toEqual(HttpStatusCode.Ok);

        expect(response.data)
            .toEqual(expect.any(Object));
    });

    test('Update Basis Point Value in Basis Point', async () => {
        const requestBody = {
            "basisPointValue": updatedBasisPointValue,
            "validFrom": currentDate,
        };

        const response = await sendLMSRequest('put', `BasisPoint/${basisId}`, requestBody);

        expect(response.status, `Status code ${HttpStatusCode.Ok}`)
            .toEqual(HttpStatusCode.Ok);

        expect(response.data)
            .toEqual(expect.any(Object));

        expect(response.data.basisPointValue)
            .toEqual(updatedBasisPointValue);
    });

    test('Update Valid Form in Basis Point', async () => {
        const requestBody = {
            "basisPointValue": basisPointValue,
            "validFrom": dateInTheFuture,
        };

        const response = await sendLMSRequest('put', `BasisPoint/${basisId}`, requestBody);

        expect(response.status, `Status code ${HttpStatusCode.Ok}`)
            .toEqual(HttpStatusCode.Ok);

        expect(response.data, `Response contains Object`)
            .toEqual(expect.any(Object));

        expect(response.data.validFrom)
            .toEqual(BaseTest.getReceivablesDates(30));
    });

    /**
     * Negative Tests
     */

    test('Cannot update Basis Point with Basis Point Value = null', async () => {
        const requestBody = {
            "basisPointValue": null,
            "validFrom": currentDate,
        };

        const response = await sendLMSRequest('put', `BasisPoint/${basisId}`, requestBody);

        expect(response.response.status, `Status code ${HttpStatusCode.BadRequest}`)
            .toEqual(HttpStatusCode.BadRequest);

        expect(response.response.data, `Response contains Object`)
            .toEqual(expect.any(Object));
    });

    test('Cannot update Basis Point with negative Basis Point Value = -1', async () => {
        const requestBody = {
            "basisPointValue": -1,
            "validFrom": currentDate,
        };

        const response = await sendLMSRequest('put', `BasisPoint/${basisId}`, requestBody);

        expect(response.response.status, `Status code ${HttpStatusCode.BadRequest}`)
            .toEqual(HttpStatusCode.BadRequest);

        expect(response.response.data, `Response contains Object`)
            .toEqual(expect.any(Object));
    });

    test('Cannot update Basis Point with Valid Form = null', async () => {
        const requestBody = {
            "basisPointValue": basisPointValue,
            "validFrom": null,
        };

        const response = await sendLMSRequest('put', `BasisPoint/${basisId}`, requestBody);

        expect(response.response.status, `Status code ${HttpStatusCode.BadRequest}`)
            .toEqual(HttpStatusCode.BadRequest);

        expect(response.response.data, `Response contains Object`)
            .toEqual(expect.any(Object));
    });
});
