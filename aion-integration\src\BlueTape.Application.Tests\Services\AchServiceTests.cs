﻿using AutoFixture;
using BlueTape.Aion.Application.Abstractions;
using BlueTape.Aion.Application.Constants;
using BlueTape.Aion.Application.Models.Ach.Pull;
using BlueTape.Aion.Application.Service;
using BlueTape.Aion.DataAccess.External.Abstractions;
using BlueTape.Aion.DataAccess.External.Models.Accounts;
using BlueTape.Aion.DataAccess.External.Models.AchTransfer;
using BlueTape.Aion.DataAccess.External.Models.AchTransfer.Response;
using BlueTape.Aion.DataAccess.External.Models.CreateAchTransfer;
using BlueTape.Aion.DataAccess.External.Models.CreateAchTransfer.Response;
using BlueTape.Aion.DataAccess.External.Models.InternalTransfer;
using BlueTape.Aion.Domain.DTOs.BankAccount;
using BlueTape.Aion.Domain.DTOs.Company;
using BlueTape.Application.Tests.AutoFixture.Attributes;
using BlueTape.Integrations.Aion.Accounts;
using BlueTape.Integrations.Aion.Infrastructure.Enums;
using BlueTape.Utilities.Providers;
using BueTape.Aion.Infrastructure.Exceptions;
using BueTape.Aion.Infrastructure.Options;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Options;
using Moq;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Net;
using System.Threading;
using System.Threading.Tasks;
using BlueTape.Aion.DataAccess.MongoDB.Abstractions;

namespace BlueTape.Application.Tests.Services;

public class AchServiceTests
{
    private readonly Mock<ICompanyService> _companyServiceMock = new();
    private readonly Mock<IBankAccountService> _bankAccountServiceMock = new();
    private readonly Mock<IAionHttpClient> _aionHttpClientServiceMock = new();
    private readonly Mock<IMapper> _mapperMock = new();
    private readonly Mock<ITransactionService> _transactionServiceMock = new();
    private Mock<IOptions<AionInternalTransferOptions>> _reportOptionsMock = new();
    private readonly Mock<IConfiguration> _configurationMock = new();
    private readonly Mock<IDateProvider> _dateProvider = new();
    private readonly Mock<IAionLoggingService> _aionLoggingServiceMock = new();

    private AionInternalTransferOptions _reportOptions = new();
    private Fixture _fixture = new();

    private void VerifyNoOtherCalls()
    {
        _companyServiceMock.VerifyNoOtherCalls();
        _bankAccountServiceMock.VerifyNoOtherCalls();
        _aionHttpClientServiceMock.VerifyNoOtherCalls();
    }

    private AchService GetService(bool useDefaultReportOptionMock = true)
    {
        if (useDefaultReportOptionMock)
        {
            _reportOptions = new AionInternalTransferOptions
            {
                AccountNumberSecretNames = new Dictionary<AccountCodeType, string>
                {
                    {AccountCodeType.GL, _fixture.Create<string>()},
                    {AccountCodeType.OP, _fixture.Create<string>()},
                    {AccountCodeType.FUNDING, _fixture.Create<string>()},
                    {AccountCodeType.FBO, _fixture.Create<string>()},
                    {AccountCodeType.SERVICE, _fixture.Create<string>()},
                    {AccountCodeType.REVENUE, _fixture.Create<string>()},
                    {AccountCodeType.TABAPAY, _fixture.Create<string>()},
                    {AccountCodeType.COLLECTIONREPAYMENT, _fixture.Create<string>()},
                    {AccountCodeType.DISBURSEMENT, _fixture.Create<string>()},
                    {AccountCodeType.COLLECTION, _fixture.Create<string>()},
                },
                AccountIdSecretNames = new Dictionary<AccountCodeType, string>
                {
                    {AccountCodeType.GL, _fixture.Create<string>()},
                    {AccountCodeType.OP, _fixture.Create<string>()},
                    {AccountCodeType.FUNDING, _fixture.Create<string>()},
                    {AccountCodeType.FBO, _fixture.Create<string>()},
                    {AccountCodeType.SERVICE, _fixture.Create<string>()},
                    {AccountCodeType.REVENUE, _fixture.Create<string>()},
                    {AccountCodeType.TABAPAY, _fixture.Create<string>()},
                    {AccountCodeType.COLLECTIONREPAYMENT, _fixture.Create<string>()},
                    {AccountCodeType.DISBURSEMENT, _fixture.Create<string>()},
                    {AccountCodeType.COLLECTION, _fixture.Create<string>()},
                }
            };

            _reportOptionsMock
                .SetupGet(opt => opt.Value)
                .Returns(_reportOptions);
        }

        return new AchService(
            _companyServiceMock.Object,
            _bankAccountServiceMock.Object,
            _aionHttpClientServiceMock.Object,
            _transactionServiceMock.Object,
            _mapperMock.Object,
            _configurationMock.Object,
            _dateProvider.Object,
            _aionLoggingServiceMock.Object,
            _reportOptionsMock.Object
            );
    }

    [Fact]
    public Task GetAvailableBalance_Throws_AionAccountDoesNotExistException_Mapping_Issue()
    {
        var glSecret = _fixture.Create<string>();

        _reportOptions = new AionInternalTransferOptions
        {
            AccountNumberSecretNames = new Dictionary<AccountCodeType, string>()
            {
                {AccountCodeType.GL, glSecret},
            },
            AccountIdSecretNames = new Dictionary<AccountCodeType, string>
            {
                {AccountCodeType.GL, glSecret},
            }
        };

        _reportOptionsMock
            .SetupGet(opt => opt.Value)
            .Returns(_reportOptions);

        _configurationMock.Setup(x => x[It.IsAny<string>()])
            .Returns(glSecret);

        var service = GetService(false);

        return service.GetAvailableBalance(AccountCodeType.COLLECTION, PaymentSubscriptionType.SUBSCRIPTION1.ToString(), default)
            .ShouldThrowAsync<AionAccountDoesNotExistException>();
    }

    [Fact]
    public Task GetAvailableBalance_Throws_AionAccountDoesNotExistException_Config_Issue()
    {
        var glSecret = _fixture.Create<string>();

        _reportOptions = new AionInternalTransferOptions
        {
            AccountNumberSecretNames = new Dictionary<AccountCodeType, string>()
            {
                {AccountCodeType.GL, glSecret},
            },
            AccountIdSecretNames = new Dictionary<AccountCodeType, string>
            {
                {AccountCodeType.GL, glSecret},
            }
        };

        _reportOptionsMock
            .SetupGet(opt => opt.Value)
            .Returns(_reportOptions);

        var service = GetService(false);

        return service.GetAvailableBalance(AccountCodeType.GL, PaymentSubscriptionType.SUBSCRIPTION1.ToString(), default)
            .ShouldThrowAsync<AionAccountDoesNotExistException>();
    }

    [Fact]
    public Task GetAvailableBalance_Throws_BankAccountDoesNotExistException()
    {
        var glSecret = _fixture.Create<string>();

        _reportOptions = new AionInternalTransferOptions
        {
            AccountNumberSecretNames = new Dictionary<AccountCodeType, string>()
            {
                {AccountCodeType.GL, glSecret},
            },
            AccountIdSecretNames = new Dictionary<AccountCodeType, string>
            {
                {AccountCodeType.GL, glSecret},
            }
        };

        _reportOptionsMock
            .SetupGet(opt => opt.Value)
            .Returns(_reportOptions);

        _configurationMock.Setup(x => x[It.IsAny<string>()])
            .Returns(glSecret);

        var aionAccounts = new GetAccountsResponse()
        {
            BankAccounts =
            [
                new BankAccounts
                {
                    AccountId = _fixture.Create<string>()
                }
            ]
        };

        _aionHttpClientServiceMock.Setup(x => x.GetAccounts(PaymentSubscriptionType.SUBSCRIPTION1, default))
            .ReturnsAsync(aionAccounts);

        var service = GetService(false);

        return service.GetAvailableBalance(AccountCodeType.GL, PaymentSubscriptionType.SUBSCRIPTION1.ToString(), default)
            .ShouldThrowAsync<BankAccountDoesNotExistException>();
    }

    [Fact]
    public async Task GetAvailableBalance_Execute_Success()
    {
        var glSecret = _fixture.Create<string>();
        var availableBalance = _fixture.Create<decimal>();

        _reportOptions = new AionInternalTransferOptions
        {
            AccountNumberSecretNames = new Dictionary<AccountCodeType, string>()
            {
                {AccountCodeType.GL, glSecret},
            },
            AccountIdSecretNames = new Dictionary<AccountCodeType, string>
            {
                {AccountCodeType.GL, glSecret},
            }
        };

        _reportOptionsMock
            .SetupGet(opt => opt.Value)
            .Returns(_reportOptions);

        _configurationMock.Setup(x => x[It.IsAny<string>()])
            .Returns(glSecret);

        var aionAccounts = new GetAccountsResponse()
        {
            BankAccounts =
            [
                new BankAccounts
                {
                    AccountId = glSecret,
                    AvailableBalance = availableBalance
                }
            ]
        };

        _aionHttpClientServiceMock.Setup(x => x.GetAccounts(PaymentSubscriptionType.SUBSCRIPTION1, default))
            .ReturnsAsync(aionAccounts);

        var service = GetService(false);

        var result = await service.GetAvailableBalance(AccountCodeType.GL, PaymentSubscriptionType.SUBSCRIPTION1.ToString(), default);
        result.ShouldBeEquivalentTo(availableBalance);
    }

    [Fact]
    public async Task CreateAchPullAsync_Throws_CompanyDoesNotExistException()
    {
        var createAchPull = _fixture.Create<CreateAch>();
        var transactionType = _fixture.Create<TransactionType>();
        var accountId = _fixture.Create<string>();

        _configurationMock.Setup(x => x[It.IsAny<string>()])
            .Returns(accountId);

        _companyServiceMock.Setup(x =>
                x.SyncCompanyWithAionAsync(It.IsAny<string>(), PaymentSubscriptionType.SUBSCRIPTION1.ToString(), It.IsAny<CancellationToken>()))!
            .Throws(() => new CompanyDoesNotExistException(createAchPull.Receiver.CompanyId, HttpStatusCode.BadRequest));

        var service = GetService();
        await service.CreateAchAsync(createAchPull, transactionType, PaymentSubscriptionType.SUBSCRIPTION1.ToString(), CancellationToken.None)
            .ShouldThrowAsync<CompanyDoesNotExistException>();

        _companyServiceMock.Verify(x =>
            x.SyncCompanyWithAionAsync(createAchPull.Receiver.CompanyId, PaymentSubscriptionType.SUBSCRIPTION1.ToString(), It.IsAny<CancellationToken>()), Times.Once);

        VerifyNoOtherCalls();
    }

    [Theory]
    [InlineData(true)]
    [InlineData(false)]
    public async Task CreateAchPullAsync_Throws_BankAccountDoesNotExistException(bool bankAccountIsNUll)
    {
        var createAchPull = _fixture.Create<CreateAch>();
        var transactionType = _fixture.Create<TransactionType>();
        var accountId = _fixture.Create<string>();
        var companyDto = new CompanyDto();

        if (!bankAccountIsNUll)
        {
            companyDto.BankAccounts = new[]
            {
                Guid.NewGuid().ToString()
            };
        }

        _configurationMock.Setup(x => x[It.IsAny<string>()])
            .Returns(accountId);

        _companyServiceMock.Setup(x =>
                x.SyncCompanyWithAionAsync(It.IsAny<string>(), PaymentSubscriptionType.SUBSCRIPTION1.ToString(), It.IsAny<CancellationToken>()))!
            .ReturnsAsync(companyDto);

        var service = GetService();
        await service.CreateAchAsync(createAchPull, transactionType, PaymentSubscriptionType.SUBSCRIPTION1.ToString(), CancellationToken.None)
            .ShouldThrowAsync<BankAccountDoesNotExistException>();

        _companyServiceMock.Verify(x =>
            x.SyncCompanyWithAionAsync(createAchPull.Receiver.CompanyId, PaymentSubscriptionType.SUBSCRIPTION1.ToString(), It.IsAny<CancellationToken>()), Times.Once);

        VerifyNoOtherCalls();
    }

    [Theory]
    [InlineData(TransactionType.Pull, "Pull", true, AionConstants.AionBusinessSecCode, AionConstants.AchServiceTypeStandard, false)]
    [InlineData(TransactionType.Pull, "Pull", true, AionConstants.AionBusinessSecCode, AionConstants.AchServiceTypeStandard, false)]
    [InlineData(TransactionType.Push, "Push", true, AionConstants.AionBusinessSecCode, AionConstants.AchServiceTypeSameDay, true)]
    [InlineData(TransactionType.Push, "Push", true, AionConstants.AionBusinessSecCode, AionConstants.AchServiceTypeStandard, false)]
    public async Task CreateAchPullAsync_Execute_Success(TransactionType transactionType, string expectedType, bool companyBusiness, string secCode, string serviceType, bool sameDayAch)
    {
        var createAchPull = _fixture.Create<CreateAch>();
        createAchPull.SameDayAch = sameDayAch;
        var accountId = _fixture.Create<string>();
        var companyDto = _fixture.Create<CompanyDto>();
        CreateAchTransferRequest achTransferRequest = null;

        companyDto.IsBusiness = companyBusiness;
        companyDto.BankAccounts = new[]
        {
            Guid.NewGuid().ToString(),
            Guid.NewGuid().ToString(),
            createAchPull.Receiver.BankAccountId,
            Guid.NewGuid().ToString(),
            Guid.NewGuid().ToString()
        };

        var receiverBankAccountDto = _fixture.Create<BankAccountDto>();

        _configurationMock.Setup(x => x[It.IsAny<string>()])
            .Returns(accountId);

        _companyServiceMock.Setup(x =>
                x.SyncCompanyWithAionAsync(It.IsAny<string>(), PaymentSubscriptionType.SUBSCRIPTION1.ToString(), It.IsAny<CancellationToken>()))!
            .ReturnsAsync(companyDto);

        _bankAccountServiceMock.Setup(x =>
            x.SynBankAccountWithAionAsync(
                It.IsAny<string>(),
                It.IsAny<CompanyDto>(),
                PaymentSubscriptionType.SUBSCRIPTION1.ToString(),
                AionPaymentMethodType.ACH,
                It.IsAny<CancellationToken>()))
            .ReturnsAsync(receiverBankAccountDto);

        _aionHttpClientServiceMock.Setup(x =>
                x.CreateAchTransfer(It.IsAny<CreateAchTransferRequest>(), PaymentSubscriptionType.SUBSCRIPTION1, It.IsAny<CancellationToken>()))
            .Callback<CreateAchTransferRequest, PaymentSubscriptionType, CancellationToken>((request, _, _) => achTransferRequest = request)
            .ReturnsAsync(_fixture.Create<CreateAchResponse>());

        var service = GetService();
        await service.CreateAchAsync(createAchPull, transactionType, PaymentSubscriptionType.SUBSCRIPTION1.ToString(), CancellationToken.None)
            .ShouldNotThrowAsync();

        achTransferRequest.ShouldNotBeNull();
        achTransferRequest.AchObj.AccountId.ShouldBeEquivalentTo(accountId);
        achTransferRequest.AchObj.ServiceType.ShouldBeEquivalentTo(serviceType);
        achTransferRequest.AchObj.TransactionType.ShouldBeEquivalentTo(expectedType);
        achTransferRequest.AchObj.TransferMethodId.ShouldBeEquivalentTo(receiverBankAccountDto.AionSettings!.TransferMethodId);
        achTransferRequest.AchObj.CounterpartyId.ShouldBeEquivalentTo(companyDto.AionSettings!.CounterPartyId!);
        achTransferRequest.AchObj.SecCode.ShouldBeEquivalentTo(secCode);

        _companyServiceMock.Verify(x =>
            x.SyncCompanyWithAionAsync(createAchPull.Receiver.CompanyId, PaymentSubscriptionType.SUBSCRIPTION1.ToString(), It.IsAny<CancellationToken>()), Times.Once);

        _bankAccountServiceMock.Verify(x =>
            x.SynBankAccountWithAionAsync(
                It.IsAny<string>(),
                It.IsAny<CompanyDto>(),
                PaymentSubscriptionType.SUBSCRIPTION1.ToString(),
                AionPaymentMethodType.ACH,
                It.IsAny<CancellationToken>()), Times.Once);

        _aionHttpClientServiceMock.Verify(x =>
            x.CreateAchTransfer(It.IsAny<CreateAchTransferRequest>(), PaymentSubscriptionType.SUBSCRIPTION1, It.IsAny<CancellationToken>()), Times.Once);

        VerifyNoOtherCalls();
    }

    [Fact]
    public async Task GetAllAchTransactionsAsync_Execute_Success()
    {
        var response = _fixture.Create<GetACHTransferResponse>();
        response.NumPages = 1;
        response.PageNumber = 0;

        var forLastDaysNumber = 7;

        _aionHttpClientServiceMock.Setup(x =>
            x.GetACHTransfers(
                It.IsAny<GetACHTransfersRequest>(), PaymentSubscriptionType.SUBSCRIPTION1, It.IsAny<CancellationToken>()))
            .ReturnsAsync(response);

        var service = GetService();

        await service.GetAllAchTransactionsAsync(forLastDaysNumber, PaymentSubscriptionType.SUBSCRIPTION1.ToString(), CancellationToken.None);

        _aionHttpClientServiceMock.Verify(x =>
            x.GetACHTransfers(
                It.IsAny<GetACHTransfersRequest>(), PaymentSubscriptionType.SUBSCRIPTION1, It.IsAny<CancellationToken>()), Times.Once);

        VerifyNoOtherCalls();
    }

    [Fact]
    public async Task GetAllAchReturnTransactionsAsync_Execute_Success()
    {
        var response = _fixture.Create<GetACHTransferResponse>();
        response.NumPages = 1;
        response.PageNumber = 0;

        var forLastDaysNumber = 7;

        _aionHttpClientServiceMock.Setup(x =>
                x.GetACHTransferReturns(
                    It.IsAny<GetACHTransfersReturnsRequest>(), PaymentSubscriptionType.SUBSCRIPTION1, It.IsAny<CancellationToken>()))
            .ReturnsAsync(response);

        var service = GetService();

        await service.GetAllAchReturnTransactionsAsync(forLastDaysNumber, PaymentSubscriptionType.SUBSCRIPTION1.ToString(), CancellationToken.None);

        _aionHttpClientServiceMock.Verify(x =>
            x.GetACHTransferReturns(
                It.IsAny<GetACHTransfersReturnsRequest>(), PaymentSubscriptionType.SUBSCRIPTION1, It.IsAny<CancellationToken>()), Times.Once);

        VerifyNoOtherCalls();
    }

    [Theory]
    [InlineData(1, 2, 2)]
    [InlineData(1, 1, null)]
    public async Task GetAchTransactionsByPageAsync_Execute_Success(int initialPage, int numberOfPages, int? resultNextPage)
    {
        var response = _fixture.Create<GetACHTransferResponse>();
        response.NumPages = numberOfPages;
        response.PageNumber = initialPage;

        var forLastDaysNumber = 7;

        _aionHttpClientServiceMock.Setup(x =>
                x.GetACHTransfers(
                    It.IsAny<GetACHTransfersRequest>(), PaymentSubscriptionType.SUBSCRIPTION1, It.IsAny<CancellationToken>()))
            .ReturnsAsync(response);

        var service = GetService();

        var result = await service.GetAchTransactionsByPageAsync(initialPage, forLastDaysNumber, PaymentSubscriptionType.SUBSCRIPTION1.ToString(), CancellationToken.None);

        result.Item1.Count.ShouldBeEquivalentTo(response.AchList.Count);
        result.Item2.ShouldBeEquivalentTo(resultNextPage);

        _aionHttpClientServiceMock.Verify(x =>
            x.GetACHTransfers(
                It.IsAny<GetACHTransfersRequest>(), PaymentSubscriptionType.SUBSCRIPTION1, It.IsAny<CancellationToken>()), Times.Once);

        VerifyNoOtherCalls();
    }

    [Theory]
    [InlineData(1, 2, 2)]
    [InlineData(1, 1, null)]
    public async Task GetAchReturnTransactionsByPageAsync_Execute_Success(int initialPage, int numberOfPages, int? resultNextPage)
    {
        var response = _fixture.Create<GetACHTransferResponse>();
        response.NumPages = numberOfPages;
        response.PageNumber = initialPage;

        var forLastDaysNumber = 7;

        _aionHttpClientServiceMock.Setup(x =>
                x.GetACHTransferReturns(
                    It.IsAny<GetACHTransfersReturnsRequest>(), PaymentSubscriptionType.SUBSCRIPTION1, It.IsAny<CancellationToken>()))
            .ReturnsAsync(response);

        var service = GetService();

        var result = await service.GetAchReturnTransactionsByPageAsync(initialPage, forLastDaysNumber, PaymentSubscriptionType.SUBSCRIPTION1.ToString(), CancellationToken.None);

        result.Item1.Count.ShouldBeEquivalentTo(response.AchList.Count);
        result.Item2.ShouldBeEquivalentTo(resultNextPage);

        _aionHttpClientServiceMock.Verify(x =>
            x.GetACHTransferReturns(
                It.IsAny<GetACHTransfersReturnsRequest>(), PaymentSubscriptionType.SUBSCRIPTION1, It.IsAny<CancellationToken>()), Times.Once);

        VerifyNoOtherCalls();
    }

    [Theory]
    [InlineData(1, 2, 2)]
    [InlineData(1, 1, null)]
    public async Task GetAchInternalTransactionsByPageAsync_Execute_Success(int initialPage, int numberOfPages, int? resultNextPage)
    {
        var response = _fixture.Create<GetInternalTransferResponse>();
        response.NumPages = numberOfPages;
        response.PageNumber = initialPage;

        var forLastDaysNumber = 7;

        _aionHttpClientServiceMock.Setup(x =>
                x.GetInternalTransfers(
                    It.IsAny<GetACHTransfersRequest>(), PaymentSubscriptionType.SUBSCRIPTION1, It.IsAny<CancellationToken>()))
            .ReturnsAsync(response);

        var service = GetService();

        var result = await service.GetAchInternalTransactionsByPageAsync(initialPage, forLastDaysNumber, PaymentSubscriptionType.SUBSCRIPTION1.ToString(), CancellationToken.None);

        result.Item1.Count.ShouldBeEquivalentTo(response.AchList.Count);
        result.Item2.ShouldBeEquivalentTo(resultNextPage);

        _aionHttpClientServiceMock.Verify(x =>
            x.GetInternalTransfers(
                It.IsAny<GetACHTransfersRequest>(), PaymentSubscriptionType.SUBSCRIPTION1, It.IsAny<CancellationToken>()), Times.Once);

        VerifyNoOtherCalls();
    }

    [Fact]
    public async Task GetAllAccounts_ValidData_NoException()
    {
        _aionHttpClientServiceMock.Setup(x => x.GetAccounts(PaymentSubscriptionType.SUBSCRIPTION1, default))
            .ReturnsAsync(new GetAccountsResponse());

        _aionHttpClientServiceMock.Setup(x => x.GetAccounts(PaymentSubscriptionType.SUBSCRIPTION2, default))
            .ReturnsAsync(new GetAccountsResponse());

        _aionHttpClientServiceMock.Setup(x => x.GetAccounts(PaymentSubscriptionType.SUBSCRIPTION3, default))
            .ReturnsAsync(new GetAccountsResponse());

        var service = GetService(false);

        Exception exception = null;

        try
        {
            await service.GetAllAccounts(default);
        }
        catch (Exception ex)
        {
            exception = ex;
        }
    }

    [Theory, AutoFixtureCustom]
    public async Task GetAllAccounts_FullValidData_NoException(BankAccounts[] accountsSub1, BankAccounts[] accountsSub2, BankAccounts[] accountsSub3)
    {
        var accountsResSub1 = new GetAccountsResponse
        {
            BankAccounts = accountsSub1
        };

        var accountsResSub2 = new GetAccountsResponse
        {
            BankAccounts = accountsSub2
        };

        var accountsResSub3 = new GetAccountsResponse
        {
            BankAccounts = accountsSub3
        };

        var accounts = new List<BankAccounts>();
        accounts.AddRange(accountsResSub1.BankAccounts);
        accounts.AddRange(accountsResSub2.BankAccounts);
        accounts.AddRange(accountsResSub3.BankAccounts);

        var resultAccounts = new List<AccountResponseObj>();
        resultAccounts.AddRange(accounts.Select(x =>
        {
            var account = new AccountResponseObj();
            account.Name = x.AccountName;
            account.AccountNumber = x.AccountNumber;
            account.AvailableBalance = x.AvailableBalance;
            account.CurrentBalance = x.CurrentBalance;
            account.AmountOnHold = x.CurrentBalance - x.AvailableBalance;
            account.Id = x.AccountId;
            account.PaymentProvider = "aion";

            return account;
        }));

        _aionHttpClientServiceMock.Setup(x => x.GetAccounts(PaymentSubscriptionType.SUBSCRIPTION1, default))
            .ReturnsAsync(accountsResSub1);

        _aionHttpClientServiceMock.Setup(x => x.GetAccounts(PaymentSubscriptionType.SUBSCRIPTION2, default))
            .ReturnsAsync(accountsResSub2);

        _aionHttpClientServiceMock.Setup(x => x.GetAccounts(PaymentSubscriptionType.SUBSCRIPTION3, default))
            .ReturnsAsync(accountsResSub3);

        var service = GetService(false);

        var result = await service.GetAllAccounts(default);

        result.ShouldBeEquivalentTo(resultAccounts);
    }
}