﻿using AutoMapper;
using BlueTape.Aion.API.Extensions;
using BlueTape.Aion.API.Models.Errors;
using BlueTape.Aion.Application.Abstractions;
using BlueTape.Aion.Application.Models.Ach.Pull;
using BlueTape.Aion.Application.Models.Ach.Pull.Response;
using BlueTape.Aion.DataAccess.External.Constants;
using BlueTape.Aion.Domain.Constants;
using BlueTape.Aion.Domain.Extensions;
using BlueTape.Integrations.Aion.Ach.CreateAchTransfer;
using BlueTape.Integrations.Aion.Infrastructure.Enums;
using BueTape.Aion.Infrastructure.ServiceBusMessages.Report;
using FluentValidation;
using Microsoft.AspNetCore.Mvc;
using MongoDB.Bson;
using System.ComponentModel.DataAnnotations;
using System.Diagnostics.CodeAnalysis;
using BlueTape.Integrations.Aion.Ach.CreateWireTransfer.Response;
using Microsoft.AspNetCore.Authorization;

namespace BlueTape.Aion.API.Controllers;

[ApiController]
[Route("/api/ach")]
[ExcludeFromCodeCoverage]
[Authorize]
[ProducesResponseType(typeof(List<ErrorModel>), StatusCodes.Status400BadRequest)]
[ProducesResponseType(typeof(void), StatusCodes.Status401Unauthorized)]
[ProducesResponseType(typeof(List<ErrorModel>), StatusCodes.Status500InternalServerError)]
public class AchController : ControllerBase
{
    private readonly IValidator<CreateAchModel> _validator;
    private readonly IMapper _mapper;
    private readonly IAchService _achService;
    private readonly IReportService _reportService;
    private readonly IDailyAchProcessingService _dailyAchProcessingService;

    public AchController(
        IValidator<CreateAchModel> validator,
        IMapper mapper,
        IAchService achService,
        IReportService reportService,
        IDailyAchProcessingService dailyAchProcessingService)
    {
        _validator = validator;
        _mapper = mapper;
        _achService = achService;
        _reportService = reportService;
        _dailyAchProcessingService = dailyAchProcessingService;
    }

    [HttpPost("pull")]
    [ProducesResponseType(typeof(CreateAchResponseModel), StatusCodes.Status200OK)]
    public async Task<IActionResult> CreateAchPull(
        [FromHeader(Name = "PaymentRequestId")][Required] string paymentRequestId,
        [FromHeader(Name = ClientConstants.PaymentSubscriptionTypeHeader)][Required] string paymentSubscriptionType,
        [FromBody] CreateAchModel achModel,
        CancellationToken ctx)
    {
        if (Environment.GetEnvironmentVariable(EnvironmentConstants.Environment).IsEnvironmentDevOrBeta())
        {
            return Ok(GetAchResponseModel(achModel));
        }

        await _validator.ValidateAndThrowsManualAsync(achModel, ctx);
        var data = _mapper.Map<CreateAch>(achModel);
        var response = await _achService.CreateAchAsync(data, TransactionType.Pull, paymentSubscriptionType, ctx);
        return Ok(response);
    }

    [HttpPost("push")]
    [ProducesResponseType(typeof(CreateAchResponseModel), StatusCodes.Status200OK)]
    public async Task<IActionResult> CreateAchPush(
        [FromHeader(Name = "PaymentRequestId")][Required] string paymentRequestId,
        [FromHeader(Name = ClientConstants.PaymentSubscriptionTypeHeader)][Required] string paymentSubscriptionType,
        [FromBody] CreateAchModel achModel,
        CancellationToken ctx)
    {
        if (Environment.GetEnvironmentVariable(EnvironmentConstants.Environment)!.IsEnvironmentDevOrBeta())
        {
            return Ok(GetAchResponseModel(achModel));
        }

        await _validator.ValidateAndThrowsManualAsync(achModel, ctx);
        var data = _mapper.Map<CreateAch>(achModel);
        var response = await _achService.CreateAchAsync(data, TransactionType.Push, paymentSubscriptionType, ctx);
        return Ok(response);
    }
    
    [HttpPost("push/wire")]
    [ProducesResponseType(typeof(CreateAchResponseModel), StatusCodes.Status200OK)]
    public async Task<IActionResult> CreateWirePush(
        [FromHeader(Name = "PaymentRequestId")][Required] string paymentRequestId,
        [FromHeader(Name = ClientConstants.PaymentSubscriptionTypeHeader)][Required] string paymentSubscriptionType,
        [FromBody] CreateAchModel achModel,
        CancellationToken ctx)
    {
        if (Environment.GetEnvironmentVariable(EnvironmentConstants.Environment)!.IsEnvironmentDevOrBeta())
        {
            return Ok(GetWireResponseModel(achModel));
        }

        await _validator.ValidateAndThrowsManualAsync(achModel, ctx);
        var data = _mapper.Map<CreateAch>(achModel);
        var response = await _achService.CreateWireAsync(data, paymentSubscriptionType, ctx);
        return Ok(response);
    }
    
    [HttpPost("push/instant")]
    [ProducesResponseType(typeof(CreateAchResponseModel), StatusCodes.Status200OK)]
    public async Task<IActionResult> CreateInstantPush(
        [FromHeader(Name = "PaymentRequestId")][Required] string paymentRequestId,
        [FromHeader(Name = ClientConstants.PaymentSubscriptionTypeHeader)][Required] string paymentSubscriptionType,
        [FromBody] CreateAchModel achModel,
        CancellationToken ctx)
    {
        if (Environment.GetEnvironmentVariable(EnvironmentConstants.Environment)!.IsEnvironmentDevOrBeta())
        {
            return Ok(GetWireResponseModel(achModel));
        }

        await _validator.ValidateAndThrowsManualAsync(achModel, ctx);
        var data = _mapper.Map<CreateAch>(achModel);
        var response = await _achService.CreateInstantAsync(data, paymentSubscriptionType, ctx);
        return Ok(response);
    }

    [HttpPost("/report/run")]
    [ProducesResponseType(typeof(void), StatusCodes.Status200OK)]
    public async Task<IActionResult> GetReport(CancellationToken ctx)
    {
        var externalAchRequest = new AionExternalAchTransactionReportRequest();
        var internalAchRequest = new AionInternalTransactionReportRequest();
        var paymentSubscriptionTypes = Enum.GetValues<PaymentSubscriptionType>();

        foreach (var paymentSubscription in paymentSubscriptionTypes)
        {
            externalAchRequest.ExternalAchReportRequests.Add(new ExternalAchReportRequest()
            {
                AchReturnTransactionPage = 1,
                AchTransactionPage = 1,
                InstantTransactionPage = 1,
                WireTransactionPage = 1,
                PaymentSubscriptionType = paymentSubscription
            });

            internalAchRequest.InternalTransactionReportRequests.Add(new InternalTransactionReportRequest()
            {
                InternalTransactionPage = 1,
                PaymentSubscriptionType = paymentSubscription
            });
        }

        await _reportService.RunExternalTransactionStatusReportAsync(externalAchRequest, ctx);
        await _reportService.RunInternalTransactionStatusReportAsync(internalAchRequest, ctx);

        return Ok();
    }

    [HttpPost("/dailyAchProcessing/run")]
    [ProducesResponseType(typeof(void), StatusCodes.Status200OK)]
    public async Task<IActionResult> RunDailyAchProcessing([FromQuery] decimal? amount, int? transactionsLimitPerCall, CancellationToken ct)
    {
        return Ok(await _dailyAchProcessingService.ProcessAsync(amount ?? 1, transactionsLimitPerCall ?? 5, ct));
    }
    
    private static CreateWireTransferResponseModel GetWireResponseModel(CreateAchModel createModel) =>
        new()
        {
            ResponseMessage = string.Empty,
            Result = true,
            WireTransferObj = new WireTransferObjItemModel()
            {
                Id = ObjectId.GenerateNewId().ToString(),
                AccountId = ObjectId.GenerateNewId().ToString(),
                Amount = createModel.Amount.ToString(),
                CounterpartyId = ObjectId.GenerateNewId().ToString(),
                Direction = "Ach",
                Originator = new FinancialInstitutionModel()
                {
                    Address1 = "123 Main St",
                    Address2 = "Suite 100",
                    Address3 = "Suite 200",
                    Name = "John Doe",
                    IdCode = "*********",
                    Identifier = "*********"
                },
                ReferenceId = Guid.NewGuid().ToString(),
                EffectiveDate = DateTime.UtcNow.ToString("yyMMdd"),
                FeeAmount = 12.34m,
                Status = "Pending",
                ProviderStatus = "Pending",
                DebitStatus = false,
                StatusMessage = "Transaction is pending",
                SendEmail = false,
                InitiatedByUserName = "John Doe",
                InitiatedBy = "System",
                ContextIdentifier = "Context123",
                PaymentMethodId = ObjectId.GenerateNewId().ToString(),
                CreatedAt = DateTime.UtcNow,
                UpdatedAt = DateTime.UtcNow,
                AccountNumber = createModel.Receiver.BankAccountId,
            }
        };

    private static CreateAchResponseModel GetAchResponseModel(CreateAchModel createModel) =>
        new()
        {
            ResponseMessage = string.Empty,
            Result = true,
            AchObj = new AchObjResponseModel
            {
                Id = ObjectId.GenerateNewId().ToString(),
                AccountId = ObjectId.GenerateNewId().ToString(),
                Amount = createModel.Amount.ToString(),
                CounterpartyId = ObjectId.GenerateNewId().ToString(),
                Description = createModel.Description,
                Direction = "Ach",
                ReasonData = "Everything is fine",
                TraceNumber = "T2404506540616PP1",
                Originator = new OriginatorResponseModel
                {
                    AccountNumber = createModel.OriginatorAccountCode.ToString(),
                    AccountType = createModel.OriginatorAccountCode.ToString(),
                },
                Receiver = new ReceiverResponseModel
                {
                    AccountNumber = createModel.Receiver.BankAccountId
                },
                ReferenceId = Guid.NewGuid().ToString(),
                EffectiveDate = DateTime.UtcNow.ToString("yyMMdd")
            }
        };
}